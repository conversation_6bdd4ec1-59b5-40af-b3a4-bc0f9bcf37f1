<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip.corp.obt</groupId>
        <artifactId>service-parent</artifactId>
        <version>1.0.4-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.ctrip.corp.obt</groupId>
    <artifactId>hotel-booking</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>hotel-booking-server</module>
    </modules>
    <properties>
        <arch-security.version>*******</arch-security.version>
        <shardVersion>********</shardVersion>
        <api-version>1.1.47-SNAPSHOT</api-version>
        <java.version>1.8</java.version>
        <log4j2.version>2.15.0</log4j2.version>
        <start-class>com.corpgovernment.HotelBookingApplication</start-class>
        <common.version>2.2.50</common.version>
        <redis-handler.version>2.0.0</redis-handler.version>
        <basic-data-jar.version>2.1.1</basic-data-jar.version>
        <arch-logging.version>1.0.6</arch-logging.version>
        <arch-threadpool.version>********</arch-threadpool.version>
        <arch.version>1.0.8</arch.version>
        <booking-core-service-sdk.version>1.0.6-SNAPSHOT</booking-core-service-sdk.version>
        <arch-feign-gray-starter.version>1.0.1-SNAPSHOT</arch-feign-gray-starter.version>
        <discovery.version>1.0.6.6</discovery.version>
        <arch-platform-api.version>1.0.3</arch-platform-api.version>
        <arch-core.version>1.0.9.1</arch-core.version>
        <order-consolidation-sdk.version>1.0.4</order-consolidation-sdk.version>
        <arch-async-redis.version>1.0.5.11</arch-async-redis.version>
        <organization-manage-sdk.version>1.0.5</organization-manage-sdk.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>booking-core-service-dto</artifactId>
            <version>${booking-core-service-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>org.gavaghan</groupId>
            <artifactId>geodesy</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>order-consolidation-sdk</artifactId>
            <version>${order-consolidation-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-platform-api</artifactId>
            <version>${arch-platform-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>booking-core-service-sdk</artifactId>
            <version>${booking-core-service-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-threadpool-spring-boot-starter-apollo</artifactId>
            <version>${arch-threadpool.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-security</artifactId>
            <version>${arch-security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-core</artifactId>
            <version>${arch-core.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-shard-starter</artifactId>
            <version>${shardVersion}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>redis-handler</artifactId>
            <version>${redis-handler.version}</version>
        </dependency>


        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-async-redis</artifactId>
            <version>${arch-async-redis.version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>common</artifactId>
            <version>${common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-feign-gray-starter</artifactId>
            <version>${arch-feign-gray-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>apply-trip-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>basic-manage-api</artifactId>
            <version>${api-version}</version>
        </dependency>
        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>basic-data-jar</artifactId>
            <version>${basic-data-jar.version}</version>
        </dependency>
        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>organization-manage-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>travel-standard-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>permission-manage-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>approval-system-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>supplier-manage-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>supplier-system-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>order-center-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>cost-center-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>hotel-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>
        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>hotel-product-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>pay-platform-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>mice-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>car-product-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>car-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>flight-product-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>flight-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>flightintl-product-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>flightintl-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>hotelintl-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>train-product-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>train-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>car-transfer-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>message-center-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <artifactId>arch-logging</artifactId>
            <groupId>com.ctrip.corp.obt</groupId>
            <version>${arch-logging.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-event</artifactId>
            <version>${arch.version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>event-center-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>order-common</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>organization-manage-sdk</artifactId>
            <version>${organization-manage-sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
