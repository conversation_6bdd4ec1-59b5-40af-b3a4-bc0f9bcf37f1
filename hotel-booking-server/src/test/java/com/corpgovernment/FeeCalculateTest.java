package com.corpgovernment;

import com.corpgovernment.api.hotel.booking.core.FeeCalculateReq;
import com.corpgovernment.api.hotel.booking.core.FeeCalculateResp;
import com.corpgovernment.core.service.FeeCalculateService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class FeeCalculateTest {

    @Autowired
    private FeeCalculateService feeCalculateService;


    @Test
    public void testFeeCalculateService(){
        FeeCalculateReq req = JsonUtils.parse("{\"expenseType\":\"PUB\",\"hotelId\":\"8169049\",\"roomId\":\"70838320\",\"token\":\"309f13fed0e934592b40902316c430fe5HOTEL\"}",
                FeeCalculateReq.class);
        FeeCalculateResp ret = feeCalculateService.calculate(req);
        System.out.println("-----------------");
        System.out.println(JsonUtils.toJsonString(req));
        System.out.println("-----------------");
    }
}
