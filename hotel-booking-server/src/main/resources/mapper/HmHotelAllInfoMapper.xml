<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmHotelAllInfoMapper" >
  <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HmHotelAllInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="supplier_id" property="supplierId" jdbcType="INTEGER" />
    <result column="hotel_no" property="hotelNo" jdbcType="VARCHAR" />
    <result column="hotel_name" property="hotelName" jdbcType="VARCHAR" />
    <result column="hotel_address" property="hotelAddress" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="brand_code" property="brandCode" jdbcType="VARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, supplier_id, hotel_no, hotel_name, hotel_address, telephone, brand_code, brand_name,
    longitude, latitude ,city_id,city_name,star,supplier_code
  </sql>
  <insert id="batchInsert" parameterType="com.corpgovernment.mapping.bo.HmHotelAllInfo">
    insert into hm_hotel_all_info (supplier_id, hotel_no,
    hotel_name, hotel_address, telephone,
    brand_code, brand_name, longitude,
    latitude,city_id,city_name,star,supplier_code)
    values
    <foreach collection="lists" item="list" separator=",">
      ( #{list.supplierId,jdbcType=INTEGER}, #{list.hotelNo,jdbcType=VARCHAR},
      #{list.hotelName,jdbcType=VARCHAR},#{list.hotelAddress,jdbcType=VARCHAR},#{list.telephone,jdbcType=VARCHAR},
      #{list.brandCode,jdbcType=VARCHAR}, #{list.brandName,jdbcType=VARCHAR},#{list.longitude,jdbcType=VARCHAR},
      #{list.latitude,jdbcType=VARCHAR}, #{list.cityId,jdbcType=VARCHAR},#{list.cityName,jdbcType=VARCHAR},#{list.star,jdbcType=INTEGER},#{list.supplierCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="getHotelDataBySupplierId" resultType="com.corpgovernment.mapping.bo.HmHotelAllInfo">
    select
    <include refid="Base_Column_List" />
    from hm_hotel_all_info
    where supplier_id = #{supplierId}
    <if test="cityId != null"  >
      and city_id=#{cityId}
    </if>
    and latitude > 0 and longitude > 0
    <if test="start != null and limit !=null"  >
      limit #{start},#{limit}
    </if>
  </select>
  <select id="selectHotelIdBySupplierId" resultType="java.lang.String">
    select hotel_no from hm_hotel_all_info
    where supplier_id = #{supplierId}
    <if test="cityId != null"  >
      and city_id=#{cityId}
    </if>
  </select>

  <select id="selectSetHotelIdBySupplierId" resultType="java.lang.String">
    select hotel_no from hm_hotel_all_info
    where supplier_id = #{supplierId}
    <if test="cityId != null"  >
      and city_id=#{cityId}
    </if>
  </select>

  <select id="selectHotelListByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hm_hotel_all_info
    where supplier_id = #{supplierId} and  hotel_no in
    <foreach collection="lists" item="list" open="(" separator="," close=")">
      #{list}
    </foreach>
  </select>
  <select id="selectHotelByLatLng" resultType="com.corpgovernment.mapping.bo.HmHotelAllInfo">
    select hotel_no, hotel_name, hotel_address, telephone,city_id,city_name
    from hm_hotel_all_info
    where latitude &gt;= #{lat1} and latitude &lt;= #{lat2} AND longitude &gt;= #{lng1} AND longitude &lt;= #{lng2}
    and supplier_id = #{supplierId}
  </select>

  <select id="selectSupplierDetail" resultType="com.corpgovernment.mapping.bo.HmHotelDetail">
    select b.*,CONCAT_WS(',',b.longitude,b.latitude) as latitude_all_best_scope
    from hm_hotel_all_info a left join hm_hotel_detail b on a.detail_id=b.id where a.hotel_no=#{hotelNo} and a.supplier_id =#{supplierId}
  </select>
  <select id="getHotelNumBySupplierId" resultType="java.lang.Integer">
    select count(1) from hm_hotel_all_info
    where  supplier_id = #{supplierId}
    <if test="cityId != null"  >
      and city_id=#{cityId}
    </if>
  </select>

  <select id="getExistCityList" resultType="java.lang.String">
    SELECT city_id FROM hm_hotel_all_info WHERE supplier_id=#{supplierId} GROUP BY city_id
  </select>

  <update id="batchUpdateDetailId">
    UPDATE hm_hotel_all_info a
    SET detail_id  = (
    SELECT id
    FROM hm_hotel_detail
    WHERE hotel_no = a.hotel_no AND supplier_id=#{supplierId})
    WHERE a.supplier_id=#{supplierId} AND a.hotel_no IN
    <foreach collection="hotelNoList" open="(" separator="," close=")" item="item">
      #{item.hotelNo}
    </foreach>


  </update>

  <delete id="deleteByCityIdAndSupplierId">
    delete from hm_hotel_all_info where city_id = #{cityId} and supplier_id = #{supplierId}
  </delete>

</mapper>