<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.hotel.product.mapper.HoHotelMapper">

    <select id="listByOrderIds" parameterType="java.util.List" resultType="com.corpgovernment.hotel.product.entity.db.HoHotel">
        SELECT * FROM ho_hotel
        <foreach item="orderId" collection="orderIds" open="WHERE order_id IN (" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

</mapper>