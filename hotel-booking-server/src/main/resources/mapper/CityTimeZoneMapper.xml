<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.core.dao.mapper.CityTimeZoneMapper" >

  <insert id="insertOrUpdateCityTimeZone">
    INSERT INTO ho_city_time_zone
    (city_id, city_name, province_name, country_name, time_zone)
    values(#{cityId}, #{cityName}, #{provinceName}, #{countryName}, #{timeZone})
    ON DUPLICATE KEY UPDATE
    city_name = #{cityName}, province_name = #{provinceName}, country_name = #{countryName}, time_zone = #{timeZone}
  </insert>

</mapper>