<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmHotelConfirmMappingMapper" >
  <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HmHotelConfirmMapping" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="base_supplier" property="baseSupplier" jdbcType="INTEGER" />
    <result column="base_supplier_name" property="baseSupplierName" jdbcType="VARCHAR" />
    <result column="related_supplier" property="relatedSupplier" jdbcType="INTEGER" />
    <result column="related_supplier_name" property="relatedSupplierName" jdbcType="VARCHAR" />
    <result column="base_hotel" property="baseHotel" jdbcType="VARCHAR" />
    <result column="related_hotel" property="relatedHotel" jdbcType="VARCHAR" />
    <result column="handler_status" property="handlerStatus" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, base_supplier, base_supplier_name, related_supplier, related_supplier_name, base_hotel, 
    related_hotel, handler_status
  </sql>

  <insert id="batchInsert" parameterType="com.corpgovernment.mapping.bo.HmHotelConfirmMapping">
    insert IGNORE into hm_hotel_confirm_mapping (id, base_supplier, base_supplier_name,
    related_supplier, related_supplier_name, base_hotel,
    related_hotel, handler_status)
    values
    <foreach collection="lists" item="list" separator=",">
      (#{list.id,jdbcType=INTEGER}, #{list.baseSupplier,jdbcType=INTEGER}, #{list.baseSupplierName,jdbcType=VARCHAR},
      #{list.relatedSupplier,jdbcType=INTEGER}, #{list.relatedSupplierName,jdbcType=VARCHAR}, #{list.baseHotel,jdbcType=VARCHAR},
      #{list.relatedHotel,jdbcType=VARCHAR}, #{list.handlerStatus,jdbcType=INTEGER})
    </foreach>
  </insert>
  <!--  使用分区表来提升性能，使用供应商做分区字段-->
  <select id="selectRelatedHotels" resultType="com.corpgovernment.mapping.bo.RelatedHotelBo">
    select id,related_hotel as hotelId,related_supplier as supplierId from hm_hotel_confirm_mapping where
    base_hotel in
    <foreach collection="relatedHotelIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
       and related_supplier in
    <foreach collection="supplierList" item="item" index="index" open="(" close="" separator=",">
      #{item.supplierId}
    </foreach>
    ,-1)
    and handler_status=1
    union
    select id,base_hotel as hotelId,base_supplier as supplierId from hm_hotel_confirm_mapping where
    related_hotel in
    <foreach collection="relatedHotelIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and base_supplier in
    <foreach collection="supplierList" item="item" index="index" open="(" close="" separator=",">
      #{item.supplierId}
    </foreach>
    ,-1)
    and handler_status=1
  </select>

  <update id="updateHandlerStatus" >
    update hm_hotel_confirm_mapping
    set handler_status = #{handlerStatus}
    where id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="selectByRelation" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from hm_hotel_confirm_mapping
    where base_supplier=#{baseSupplierId} and related_supplier=#{relatedSupplierId} and handler_status=1
  </select>

  <select id="selectRelationGroup" resultMap="BaseResultMap" >
    select base_supplier, related_supplier from hm_hotel_confirm_mapping GROUP BY base_supplier, related_supplier
  </select>

  <select id="getMatchStatistics" resultType="com.corpgovernment.mapping.vo.HotelMatchStatisticsVo" >
    SELECT base_supplier,base_supplier_name,match_supplier,match_supplier_name,base_city_id,base_city_name,match_city_id,
    SUM(IF(total_percent>=#{matchPercent} ,1,0 )) AS relatedNum,
    SUM(IF(total_percent &lt;#{matchPercent} ,1,0 )) AS similarityNum
    FROM hm_hotel_confirm_mapping GROUP BY base_city_id
  </select>

  <select id="getSimilarityNum" resultType="java.lang.Integer" >
    SELECT COUNT(1) FROM hm_hotel_confirm_mapping WHERE base_city_id=#{baseCityId} AND total_percent &lt;#{matchPercent} AND ${fieldName} >=#{matchPercent}
  </select>
</mapper>