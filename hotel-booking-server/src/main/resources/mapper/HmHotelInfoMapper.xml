<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmHotelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="hotel" property="hotel" jdbcType="INTEGER" />
    <result column="hotel_name" property="hotelName" jdbcType="VARCHAR" />
    <result column="hotel_belong_to" property="hotelBelongTo" jdbcType="VARCHAR" />
    <result column="master_hotel_id" property="masterHotelId" jdbcType="INTEGER" />
    <result column="star" property="star" jdbcType="INTEGER" />
    <result column="is_star_licence" property="isStarLicence" jdbcType="VARCHAR" />
    <result column="r_star" property="rStar" jdbcType="INTEGER" />
    <result column="bookable" property="bookable" jdbcType="VARCHAR" />
    <result column="customer_eval" property="customerEval" jdbcType="REAL" />
    <result column="country" property="country" jdbcType="INTEGER" />
    <result column="province" property="province" jdbcType="INTEGER" />
    <result column="city" property="city" jdbcType="INTEGER" />
    <result column="district" property="district" jdbcType="INTEGER" />
    <result column="balance_period" property="balancePeriod" jdbcType="CHAR" />
    <result column="location" property="location" jdbcType="INTEGER" />
    <result column="glat" property="glat" jdbcType="DOUBLE" />
    <result column="glng" property="glng" jdbcType="DOUBLE" />
    <result column="hotel_address" property="hotelAddress" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="zip_code" property="zipCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, hotel, hotel_name, hotel_belong_to, master_hotel_id, star, is_star_licence, r_star, 
    bookable, customer_eval, country, province, city, district, balance_period, zone_id_list, 
    location, glat, glng, hotel_address, telephone, zip_code
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from hm_hotel_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from hm_hotel_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <select id="selectHotelListByIds" resultMap="BaseResultMap">
    select
    hotel, hotel_name,hotel_address, telephone, zip_code
    from hm_hotel_info
    where hotel in
    <foreach collection="lists" item="list" open="(" separator="," close=")">
      #{list}
    </foreach>
  </select>
  <select id="selectHotelId" resultType="java.lang.Integer">
    select hotel from hm_hotel_info
  </select>
  <select id="selectHotelByLatLng" resultMap="BaseResultMap">
    select hotel, hotel_name,hotel_address, telephone, zip_code
    from hm_hotel_info
    where glat &gt;= #{lat1} and glat &lt;= #{lat2} AND glng &gt;= #{lng1} AND glng &lt;= #{lng2}
  </select>
  <insert id="batchInsert" parameterType="com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo">
    insert into hm_hotel_info (id, hotel, hotel_name,
    hotel_belong_to, master_hotel_id, star,
    is_star_licence, r_star, bookable,
    customer_eval, country, province,
    city, district, balance_period,
    zone_id_list, location, glat,
    glng, hotel_address, telephone,
    zip_code)
    values
    <foreach collection="lists" item="list" separator=",">
      (#{list.id,jdbcType=BIGINT}, #{list.hotel,jdbcType=INTEGER}, #{list.hotelName,jdbcType=VARCHAR},
      #{list.hotelBelongTo,jdbcType=VARCHAR}, #{list.masterHotelId,jdbcType=INTEGER}, #{list.star,jdbcType=INTEGER},
      #{list.isStarLicence,jdbcType=VARCHAR}, #{list.rStar,jdbcType=INTEGER}, #{list.bookable,jdbcType=VARCHAR},
      #{list.customerEval,jdbcType=REAL}, #{list.country,jdbcType=INTEGER}, #{list.province,jdbcType=INTEGER},
      #{list.city,jdbcType=INTEGER}, #{list.district,jdbcType=INTEGER}, #{list.balancePeriod,jdbcType=CHAR},
      #{list.zoneIdList,jdbcType=VARCHAR}, #{list.location,jdbcType=INTEGER}, #{list.glat,jdbcType=DOUBLE},
      #{list.glng,jdbcType=DOUBLE}, #{list.hotelAddress,jdbcType=VARCHAR}, #{list.telephone,jdbcType=VARCHAR},
      #{list.zipCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insert" parameterType="com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo" >
    insert into hm_hotel_info (id, hotel, hotel_name, 
      hotel_belong_to, master_hotel_id, star, 
      is_star_licence, r_star, bookable, 
      customer_eval, country, province, 
      city, district, balance_period, 
      zone_id_list, location, glat, 
      glng, hotel_address, telephone, 
      zip_code)
    values (#{id,jdbcType=BIGINT}, #{hotel,jdbcType=INTEGER}, #{hotelName,jdbcType=VARCHAR}, 
      #{hotelBelongTo,jdbcType=VARCHAR}, #{masterHotelId,jdbcType=INTEGER}, #{star,jdbcType=INTEGER}, 
      #{isStarLicence,jdbcType=VARCHAR}, #{rStar,jdbcType=INTEGER}, #{bookable,jdbcType=VARCHAR}, 
      #{customerEval,jdbcType=REAL}, #{country,jdbcType=INTEGER}, #{province,jdbcType=INTEGER}, 
      #{city,jdbcType=INTEGER}, #{district,jdbcType=INTEGER}, #{balancePeriod,jdbcType=CHAR}, 
      #{zoneIdList,jdbcType=VARCHAR}, #{location,jdbcType=INTEGER}, #{glat,jdbcType=DOUBLE}, 
      #{glng,jdbcType=DOUBLE}, #{hotelAddress,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, 
      #{zipCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo" >
    insert into hm_hotel_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="hotel != null" >
        hotel,
      </if>
      <if test="hotelName != null" >
        hotel_name,
      </if>
      <if test="hotelBelongTo != null" >
        hotel_belong_to,
      </if>
      <if test="masterHotelId != null" >
        master_hotel_id,
      </if>
      <if test="star != null" >
        star,
      </if>
      <if test="isStarLicence != null" >
        is_star_licence,
      </if>
      <if test="rStar != null" >
        r_star,
      </if>
      <if test="bookable != null" >
        bookable,
      </if>
      <if test="customerEval != null" >
        customer_eval,
      </if>
      <if test="country != null" >
        country,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="district != null" >
        district,
      </if>
      <if test="balancePeriod != null" >
        balance_period,
      </if>
      <if test="zoneIdList != null" >
        zone_id_list,
      </if>
      <if test="location != null" >
        location,
      </if>
      <if test="glat != null" >
        glat,
      </if>
      <if test="glng != null" >
        glng,
      </if>
      <if test="hotelAddress != null" >
        hotel_address,
      </if>
      <if test="telephone != null" >
        telephone,
      </if>
      <if test="zipCode != null" >
        zip_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="hotel != null" >
        #{hotel,jdbcType=INTEGER},
      </if>
      <if test="hotelName != null" >
        #{hotelName,jdbcType=VARCHAR},
      </if>
      <if test="hotelBelongTo != null" >
        #{hotelBelongTo,jdbcType=VARCHAR},
      </if>
      <if test="masterHotelId != null" >
        #{masterHotelId,jdbcType=INTEGER},
      </if>
      <if test="star != null" >
        #{star,jdbcType=INTEGER},
      </if>
      <if test="isStarLicence != null" >
        #{isStarLicence,jdbcType=VARCHAR},
      </if>
      <if test="rStar != null" >
        #{rStar,jdbcType=INTEGER},
      </if>
      <if test="bookable != null" >
        #{bookable,jdbcType=VARCHAR},
      </if>
      <if test="customerEval != null" >
        #{customerEval,jdbcType=REAL},
      </if>
      <if test="country != null" >
        #{country,jdbcType=INTEGER},
      </if>
      <if test="province != null" >
        #{province,jdbcType=INTEGER},
      </if>
      <if test="city != null" >
        #{city,jdbcType=INTEGER},
      </if>
      <if test="district != null" >
        #{district,jdbcType=INTEGER},
      </if>
      <if test="balancePeriod != null" >
        #{balancePeriod,jdbcType=CHAR},
      </if>
      <if test="zoneIdList != null" >
        #{zoneIdList,jdbcType=VARCHAR},
      </if>
      <if test="location != null" >
        #{location,jdbcType=INTEGER},
      </if>
      <if test="glat != null" >
        #{glat,jdbcType=DOUBLE},
      </if>
      <if test="glng != null" >
        #{glng,jdbcType=DOUBLE},
      </if>
      <if test="hotelAddress != null" >
        #{hotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null" >
        #{zipCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo" >
    update hm_hotel_info
    <set >
      <if test="hotel != null" >
        hotel = #{hotel,jdbcType=INTEGER},
      </if>
      <if test="hotelName != null" >
        hotel_name = #{hotelName,jdbcType=VARCHAR},
      </if>
      <if test="hotelBelongTo != null" >
        hotel_belong_to = #{hotelBelongTo,jdbcType=VARCHAR},
      </if>
      <if test="masterHotelId != null" >
        master_hotel_id = #{masterHotelId,jdbcType=INTEGER},
      </if>
      <if test="star != null" >
        star = #{star,jdbcType=INTEGER},
      </if>
      <if test="isStarLicence != null" >
        is_star_licence = #{isStarLicence,jdbcType=VARCHAR},
      </if>
      <if test="rStar != null" >
        r_star = #{rStar,jdbcType=INTEGER},
      </if>
      <if test="bookable != null" >
        bookable = #{bookable,jdbcType=VARCHAR},
      </if>
      <if test="customerEval != null" >
        customer_eval = #{customerEval,jdbcType=REAL},
      </if>
      <if test="country != null" >
        country = #{country,jdbcType=INTEGER},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=INTEGER},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=INTEGER},
      </if>
      <if test="district != null" >
        district = #{district,jdbcType=INTEGER},
      </if>
      <if test="balancePeriod != null" >
        balance_period = #{balancePeriod,jdbcType=CHAR},
      </if>
      <if test="zoneIdList != null" >
        zone_id_list = #{zoneIdList,jdbcType=VARCHAR},
      </if>
      <if test="location != null" >
        location = #{location,jdbcType=INTEGER},
      </if>
      <if test="glat != null" >
        glat = #{glat,jdbcType=DOUBLE},
      </if>
      <if test="glng != null" >
        glng = #{glng,jdbcType=DOUBLE},
      </if>
      <if test="hotelAddress != null" >
        hotel_address = #{hotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null" >
        zip_code = #{zipCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo" >
    update hm_hotel_info
    set hotel = #{hotel,jdbcType=INTEGER},
      hotel_name = #{hotelName,jdbcType=VARCHAR},
      hotel_belong_to = #{hotelBelongTo,jdbcType=VARCHAR},
      master_hotel_id = #{masterHotelId,jdbcType=INTEGER},
      star = #{star,jdbcType=INTEGER},
      is_star_licence = #{isStarLicence,jdbcType=VARCHAR},
      r_star = #{rStar,jdbcType=INTEGER},
      bookable = #{bookable,jdbcType=VARCHAR},
      customer_eval = #{customerEval,jdbcType=REAL},
      country = #{country,jdbcType=INTEGER},
      province = #{province,jdbcType=INTEGER},
      city = #{city,jdbcType=INTEGER},
      district = #{district,jdbcType=INTEGER},
      balance_period = #{balancePeriod,jdbcType=CHAR},
      zone_id_list = #{zoneIdList,jdbcType=VARCHAR},
      location = #{location,jdbcType=INTEGER},
      glat = #{glat,jdbcType=DOUBLE},
      glng = #{glng,jdbcType=DOUBLE},
      hotel_address = #{hotelAddress,jdbcType=VARCHAR},
      telephone = #{telephone,jdbcType=VARCHAR},
      zip_code = #{zipCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>