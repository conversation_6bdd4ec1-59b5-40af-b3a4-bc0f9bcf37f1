<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.core.dao.mapper.HotelRoomMatchMonitorMapper">

    <insert id="batchInsertOrUpdateSql" parameterType="java.util.List">
        <foreach collection="list" item="item" separator="">
            INSERT INTO ms_hotel_room_match_monitor(match_key, supplier_code, hotel_id, ctrip_matched_count, self_matched_count, ctrip_self_matched_count, room_count)
            values(#{item.matchKey}, #{item.supplierCode}, #{item.hotelId}, #{item.ctripMatchedCount}, #{item.selfMatchedCount}, #{item.ctripSelfMatchedCount}, #{item.roomCount})
            ON DUPLICATE KEY UPDATE
            match_key = #{item.matchKey}, ctrip_matched_count = #{item.ctripMatchedCount}, self_matched_count = #{item.selfMatchedCount}, ctrip_self_matched_count = #{item.ctripSelfMatchedCount}, room_count = #{item.roomCount};
        </foreach>
    </insert>

</mapper>