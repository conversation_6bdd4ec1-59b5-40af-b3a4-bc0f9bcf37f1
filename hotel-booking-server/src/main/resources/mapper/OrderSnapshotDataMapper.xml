<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.hotel.product.mapper.OrderSnapshotDataMapper">

    <insert id="batchInsert" parameterType="com.corpgovernment.hotel.product.entity.db.OrderSnapshotData">
        INSERT INTO order_snapshot_data (
            product_type,
            data_scene,
            business_id,
            data_type,
            data_content
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
           #{item.productType},
            #{item.dataScene},
            #{item.businessId},
            #{item.dataType},
            #{item.dataContent}
            )
        </foreach>
    </insert>

</mapper>