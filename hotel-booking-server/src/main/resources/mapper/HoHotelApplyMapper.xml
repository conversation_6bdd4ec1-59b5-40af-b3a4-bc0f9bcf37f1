<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.hotel.product.mapper.HoHotelApplyMapper">

    <update id="updateRefundAmountBatch" parameterType="java.util.ArrayList">
        <foreach collection="list" item="item" separator=";">
            UPDATE ho_hotel_apply
            SET refund_amount = #{item.refundAmount},datachange_lasttime = NOW()
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="listSuccessByOrderIds" resultType="com.corpgovernment.hotel.product.entity.db.HoHotelApply">
        SELECT * FROM ho_hotel_apply
        WHERE status = 4 <!--4成功-->
        and order_id IN
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <select id="listNoFailByOrderIds"
            resultType="com.corpgovernment.hotel.product.entity.db.HoHotelApply">
        SELECT * FROM ho_hotel_apply
        WHERE status not in (5, 6)
        and order_id IN
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

</mapper>