<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.core.dao.mapper.ManualHotelRelationMapper">

    <select id="queryCtripManualHotelRelations"
            resultType="com.corpgovernment.core.dao.entity.db.ManualHotelRelationDo">
        select *
        from ms_manual_hotel_relation
        where master_supplier_code = 'ctrip'
        and master_hotel_id in
        <foreach collection="ctripHotelIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryNonCtripManualHotelRelations"
            resultType="com.corpgovernment.core.dao.entity.db.ManualHotelRelationDo">
        select *
        from ms_manual_hotel_relation
        where sub_supplier_code = #{supplierCode}
        and sub_hotel_id in
        <foreach collection="hotelIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>