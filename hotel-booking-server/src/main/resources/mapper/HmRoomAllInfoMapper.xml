<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmRoomAllInfoMapper" >


  <insert id="insert">
    INSERT INTO hm_room_all_info
    (supplier_id,hotel_no,basic_room_id,basic_room_name,area_range,floor_range,person)
    value
    (#{supplierId},#{hotelNo},#{basicRoomId},#{basicRoomName},#{areaRange},#{floorRange},#{person})
  </insert>


  <insert id="batchInsertRoomInfos">
    INSERT INTO hm_room_all_info
    (supplier_id,hotel_no,basic_room_id,basic_room_name,area_range,floor_range,person,addbed,windowtype,bedtype)
    VALUE
    <foreach collection="hmRoomAllInfos" item="hmRoomAllInfo" separator=",">
      (#{hmRoomAllInfo.supplierId},#{hmRoomAllInfo.hotelNo},#{hmRoomAllInfo.basicRoomId},#{hmRoomAllInfo.basicRoomName},#{hmRoomAllInfo.areaRange},#{hmRoomAllInfo.floorRange},#{hmRoomAllInfo.person},#{hmRoomAllInfo.addBed},#{hmRoomAllInfo.windowType},#{hmRoomAllInfo.bedType})
    </foreach>
  </insert>
  
  <select id="selectListBySupplierId"  resultType="com.corpgovernment.mapping.bo.HmRoomAllInfo">
  SELECT * FROM hm_room_all_info a WHERE (a.supplier_id = #{confirmMapping.baseSupplier} AND a.hotel_no = #{confirmMapping.baseHotel}) OR (a.supplier_id = #{confirmMapping.relatedSupplier} AND a.hotel_no = #{confirmMapping.relatedHotel})
  </select>
</mapper>