<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmWeightRuleMapper" >
  <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HmWeightRule" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="supplier_id" property="supplierId" jdbcType="INTEGER" />
    <result column="supplier_name" property="supplierName" jdbcType="VARCHAR" />
    <result column="hotel_name_weight" property="hotelNameWeight" jdbcType="DOUBLE" />
    <result column="hotel_address_weight" property="hotelAddressWeight" jdbcType="DOUBLE" />
    <result column="telephone_weight" property="telephoneWeight" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, supplier_id, supplier_name, hotel_name_weight, hotel_address_weight, telephone_weight
  </sql>

  <select id="selectBysupplierCode" resultMap="BaseResultMap" parameterType="com.corpgovernment.mapping.bo.HmWeightRule" >
    select
    <include refid="Base_Column_List" />
    from hm_weight_rule
    where supplier_id = #{supplierId,jdbcType=INTEGER}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from hm_weight_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from hm_weight_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.corpgovernment.mapping.bo.HmWeightRule" >
    insert into hm_weight_rule (id, supplier_id, supplier_name, 
      hotel_name_weight, hotel_address_weight, telephone_weight
      )
    values (#{id,jdbcType=INTEGER}, #{supplierId,jdbcType=INTEGER}, #{supplierName,jdbcType=VARCHAR}, 
      #{hotelNameWeight,jdbcType=DOUBLE}, #{hotelAddressWeight,jdbcType=DOUBLE}, #{telephoneWeight,jdbcType=DOUBLE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.corpgovernment.mapping.bo.HmWeightRule" >
    insert into hm_weight_rule
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="supplierId != null" >
        supplier_id,
      </if>
      <if test="supplierName != null" >
        supplier_name,
      </if>
      <if test="hotelNameWeight != null" >
        hotel_name_weight,
      </if>
      <if test="hotelAddressWeight != null" >
        hotel_address_weight,
      </if>
      <if test="telephoneWeight != null" >
        telephone_weight,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null" >
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null" >
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="hotelNameWeight != null" >
        #{hotelNameWeight,jdbcType=DOUBLE},
      </if>
      <if test="hotelAddressWeight != null" >
        #{hotelAddressWeight,jdbcType=DOUBLE},
      </if>
      <if test="telephoneWeight != null" >
        #{telephoneWeight,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.corpgovernment.mapping.bo.HmWeightRule" >
    update hm_weight_rule
    <set >
      <if test="supplierId != null" >
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null" >
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="hotelNameWeight != null" >
        hotel_name_weight = #{hotelNameWeight,jdbcType=DOUBLE},
      </if>
      <if test="hotelAddressWeight != null" >
        hotel_address_weight = #{hotelAddressWeight,jdbcType=DOUBLE},
      </if>
      <if test="telephoneWeight != null" >
        telephone_weight = #{telephoneWeight,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.corpgovernment.mapping.bo.HmWeightRule" >
    update hm_weight_rule
    set supplier_id = #{supplierId,jdbcType=INTEGER},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      hotel_name_weight = #{hotelNameWeight,jdbcType=DOUBLE},
      hotel_address_weight = #{hotelAddressWeight,jdbcType=DOUBLE},
      telephone_weight = #{telephoneWeight,jdbcType=DOUBLE}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>