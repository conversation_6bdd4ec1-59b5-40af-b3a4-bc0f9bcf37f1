<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.core.dao.mapper.HotelMatchMonitorMapper">

    <insert id="batchInsertOrUpdateSql" parameterType="java.util.List">
        <foreach collection="list" item="item" separator="">
            INSERT INTO ms_hotel_match_monitor(supplier_code, hotel_id, strategy, show_matched, error_record, static_matched)
            values(#{item.supplierCode}, #{item.hotelId}, #{item.strategy}, #{item.showMatched}, #{item.errorRecord}, #{item.staticMatched})
            ON DUPLICATE KEY UPDATE
            show_matched = #{item.showMatched}, error_record = #{item.errorRecord}, static_matched = #{item.staticMatched};
        </foreach>
    </insert>

</mapper>