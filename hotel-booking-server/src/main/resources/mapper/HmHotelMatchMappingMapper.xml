<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmHotelMatchMappingMapper" >
  <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HmHotelMatchMapping" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="base_supplier" property="baseSupplier" jdbcType="INTEGER" />
    <result column="base_supplier_name" property="baseSupplierName" jdbcType="VARCHAR" />
    <result column="match_supplier" property="matchSupplier" jdbcType="INTEGER" />
    <result column="match_supplier_name" property="matchSupplierName" jdbcType="VARCHAR" />
    <result column="base_hotel_name" property="baseHotelName" jdbcType="VARCHAR" />
    <result column="match_hotel_name" property="matchHotelName" jdbcType="VARCHAR" />
    <result column="base_hotel_address" property="baseHotelAddress" jdbcType="VARCHAR" />
    <result column="match_hotel_address" property="matchHotelAddress" jdbcType="VARCHAR" />
    <result column="base_hotel_telephone" property="baseHotelTelephone" jdbcType="VARCHAR" />
    <result column="match_hotel_telephone" property="matchHotelTelephone" jdbcType="VARCHAR" />
    <result column="base_hotel" property="baseHotel" jdbcType="VARCHAR" />
    <result column="match_hotel" property="matchHotel" jdbcType="VARCHAR" />
    <result column="hotel_name_percent" property="hotelNamePercent" jdbcType="DOUBLE" />
    <result column="hotel_address_percent" property="hotelAddressPercent" jdbcType="DOUBLE" />
    <result column="hotel_telephone_percent" property="hotelTelephonePercent" jdbcType="DOUBLE" />
    <result column="total_percent" property="totalPercent" jdbcType="DOUBLE" />
    <result column="handler_status" property="handlerStatus" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="MatchResultMap" type="com.corpgovernment.mapping.bo.HotelMatchDisplayVo" >
    <result column="base_supplier" property="supplierId" jdbcType="INTEGER" />
    <result column="base_supplier_name" property="supplierName" jdbcType="VARCHAR" />
    <result column="base_hotel" property="hotelId" jdbcType="VARCHAR" />
    <result column="base_hotel_name" property="hotelName" jdbcType="VARCHAR" />
    <result column="base_hotel_address" property="hotelAddress" jdbcType="VARCHAR" />
    <result column="total_percent" property="matchPercent" jdbcType="DOUBLE" />
    <result column="num" property="childrenCount" jdbcType="DOUBLE" />
  </resultMap>

  <resultMap id="mergeResultMap" type="com.corpgovernment.mapping.bo.HmHotelMatchMapping" >
    <result column="base_hotel" property="baseHotel" jdbcType="VARCHAR" />
    <result column="base_hotel_name" property="baseHotelName" jdbcType="VARCHAR" />
    <result column="base_hotel_address" property="baseHotelAddress" jdbcType="VARCHAR" />
    <result column="match_hotel" property="matchHotel" jdbcType="VARCHAR" />
    <result column="match_hotel_name" property="matchHotelName" jdbcType="VARCHAR" />
    <result column="match_hotel_address" property="matchHotelAddress" jdbcType="VARCHAR" />
    <result column="handler_status" property="handlerStatus" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="MatSupplierHotelsMap" type="com.corpgovernment.mapping.bo.HotelMatchDisplayVo" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="match_supplier" property="supplierId" jdbcType="INTEGER" />
    <result column="match_supplier_name" property="supplierName" jdbcType="VARCHAR" />
    <result column="match_hotel" property="hotelId" jdbcType="VARCHAR" />
    <result column="match_hotel_name" property="hotelName" jdbcType="VARCHAR" />
    <result column="match_hotel_address" property="hotelAddress" jdbcType="VARCHAR" />
    <result column="total_percent" property="matchPercent" jdbcType="DOUBLE" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, base_supplier, base_supplier_name, match_supplier, match_supplier_name, base_hotel_name, 
    match_hotel_name, base_hotel_address, match_hotel_address, base_hotel_telephone, 
    match_hotel_telephone, base_hotel, match_hotel, hotel_name_percent, hotel_address_percent, 
    hotel_telephone_percent, total_percent, handler_status
  </sql>

  <update id="updateOtherMatchRelation" parameterType="com.corpgovernment.mapping.bo.HmHotelMatchMapping" >
    update `hm_hotel_match_mapping` set handler_status=4  WHERE base_supplier=#{baseSupplier} and match_supplier=#{matchSupplier} and (
    (base_hotel =#{baseHotel} and match_hotel !=#{matchHotel} ) or (base_hotel !=#{baseHotel} and match_hotel =#{matchHotel}))
  </update>

  <select id="getDisplayHotelMatchRelation" resultMap="MatchResultMap" parameterType="com.corpgovernment.mapping.basic.bo.request.MergeHandlerRequest">
    SELECT id,base_supplier,base_supplier_name,base_hotel,base_hotel_address,base_hotel_name,
           count(1) as num,total_percent
           FROM hm_hotel_match_mapping
           where handler_status=1
    <if test="hotelName != null and hotelName != ''" >
      and (base_hotel_name like concat('%',#{hotelName}, '%') or  match_hotel_name like concat('%',#{hotelName}, '%') )
    </if>

    <if test="hotelAddress != null and hotelAddress != ''" >
      and (base_hotel_address like concat('%',#{hotelAddress}, '%') or  match_hotel_address like concat('%',#{hotelAddress}, '%') )
    </if>

    <if test="minPercent != null and minPercent >0" >
      and total_percent > #{minPercent}
    </if>

    <if test="maxPercent != null and maxPercent >0" >
      and total_percent &lt; #{maxPercent}
    </if>
    GROUP BY base_supplier,base_hotel LIMIT #{pageRecords},#{pageSize}
  </select>

  <select id="getDisplayHotelMatchRelationNum" resultType="java.lang.Integer" parameterType="com.corpgovernment.mapping.basic.bo.request.MergeHandlerRequest">
    SELECT count(1) FROM (
       SELECT base_supplier,base_hotel,count(1) as num,match_supplier,match_supplier_name,match_hotel,total_percent
    FROM hm_hotel_match_mapping
    where handler_status=1
    <if test="hotelName != null and hotelName != ''" >
      and (base_hotel_name like concat('%',#{hotelName}, '%') or  match_hotel_name like concat('%',#{hotelName}, '%') )
    </if>

    <if test="hotelAddress != null and hotelAddress != ''" >
      and (base_hotel_address like concat('%',#{hotelAddress}, '%') or  match_hotel_address like concat('%',#{hotelAddress}, '%') )
    </if>

    <if test="minPercent != null and minPercent >0" >
      and total_percent > #{minPercent}
    </if>

    <if test="maxPercent != null and maxPercent >0" >
      and total_percent &lt; #{maxPercent}
    </if>
    GROUP BY base_supplier,base_hotel) T
  </select>

  <select id="getHotelMatchSupplierHotels" resultMap="MatSupplierHotelsMap">
    SELECT id,match_supplier,match_supplier_name,match_hotel,
           match_hotel_name,match_hotel_address,total_percent
    FROM hm_hotel_match_mapping
    where handler_status=1 and base_supplier=#{baseSupplier} and base_hotel=#{baseHotelId} order by total_percent desc
  </select>

  <select id="getHigherMatching" resultType="com.corpgovernment.mapping.bo.HigherMatchingVo" >
    select a.id as id, a.base_hotel as hotelId,a.base_hotel_name as hotelName,a.base_supplier as supplierId,
         a.base_supplier_name as supplierName,a.total_percent as totalPercent
    FROM
    (select * from hm_hotel_match_mapping WHERE id=#{id}) t LEFT JOIN hm_hotel_match_mapping a
    on a.base_supplier=t.base_supplier AND a.match_hotel=t.match_hotel
    WHERE a.total_percent >t.total_percent ORDER BY a.total_percent DESC
  </select>

  <insert id="batchInsertByHotelId" parameterType="com.corpgovernment.mapping.bo.HmSupplierResultMapping" >
    insert into hp_result_hotel (hotel_name,hotel_address,hotel_telephone)
    select hotel_name,hotel_address,telephone FROM hm_hotel_info WHERE hotel in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item.baseHotel}
    </foreach>
  </insert>

  <update id="updateMatchStatus" >
    update hm_hotel_match_mapping set handler_status=#{status}
    where
    <foreach item="item" index="index" collection="list"
             open="" separator="or" close="">
      (base_hotel =#{item.baseHotel} and match_hotel=#{item.matchHotel}) or
      (base_hotel =#{item.matchHotel} and match_hotel=#{item.baseHotel})
    </foreach>
  </update>

  <select id="getHotelMergeResult" resultMap="mergeResultMap" >
    SELECT id,base_hotel,base_hotel_address,base_hotel_name,
              match_hotel,match_hotel_address,match_hotel_name,handler_status
    FROM hm_hotel_match_mapping
    where handler_status in
    <foreach item="item" index="index" collection="status"
             open="(" separator="," close=")">
              #{item}
    </foreach>
    <if test="hotelName != null and hotelName != ''" >
      and (base_hotel_name like concat('%',#{hotelName}, '%') or  match_hotel_name like concat('%',#{hotelName}, '%') )
    </if>

    <if test="hotelAddress != null and hotelAddress != ''" >
      and (base_hotel_address like concat('%',#{hotelAddress}, '%') or  match_hotel_address like concat('%',#{hotelAddress}, '%') )
    </if>
    ORDER BY OPERATOR_TIME DESC,id DESC
  </select>

  <select id="getSimilarHotel" resultType="com.corpgovernment.mapping.vo.SimilarHotelVo" >
    select b.id, b.match_hotel as hotelId,
    CONCAT_WS('-',b.base_hotel_name,b.match_hotel_name) as hotelName,
    b.id,b.total_percent as totalPercent  from
    hm_supplier_result_mapping a right join hm_hotel_match_mapping b on a.supplier_hotel_id=b.base_hotel
    where a.is_delete=0 and a.result_id=#{id} and b.handler_status=1
  </select>

  <select id="getMergerHistory" resultType="com.corpgovernment.mapping.vo.HotelMergeHistoryVo" >
    select b.id,b.match_hotel as hotelId,
    CONCAT_WS('-',b.base_hotel_name,b.match_hotel_name) as hotelName,
    b.handler_status as handlerStatus,b.operator_time as operatorTime,b.operator,
           b.total_percent as totalPercent
    from hm_supplier_result_mapping a right join hm_hotel_match_mapping b on a.supplier_hotel_id=b.base_hotel
    where a.is_delete=0 and a.result_id=#{id} and b.handler_status in(2,3)
  </select>

</mapper>