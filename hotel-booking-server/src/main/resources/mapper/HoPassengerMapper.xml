<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.hotel.product.mapper.HoPassengerMapper">

    <resultMap type="com.corpgovernment.hotel.product.entity.db.HoPassenger" id="BaseResultMap">
        <result property="orderId" column="order_id"/>
        <result property="passengerName" column="passenger_name" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="uid" column="uid"/>
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="noEmployeeId" column="no_employee_id"/>
        <result property="birthday" column="birthday"/>
        <result property="gender" column="gender"/>
        <result property="mobilePhone" column="mobile_phone" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="isSendSms" column="is_send_sms"/>
        <result property="countryCode" column="country_code"/>
        <result property="datachangeCreatetime" column="datachange_createtime"/>
        <result property="datachangeLasttime" column="datachange_lasttime"/>
        <result property="costCenterCode" column="cost_center_code"/>
        <result property="costCenterName" column="cost_center_name" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="departmentId" column="department_id"/>
        <result property="departmentName" column="department_name" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="vipLevel" column="vip_level"/>
        <result property="supplierAccountId" column="supplier_account_id"/>
    </resultMap>


    <select id="listByPassengerIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT * FROM ho_passenger
        <where>
            <if test="uids != null and uids.size() > 0">
                and uid in
                <foreach item="uid" collection="uids" open="(" separator="," close=")">
                    #{uid}
                </foreach>
            </if>
            <if test="nonEmployeeIds != null and nonEmployeeIds.size()>0">
                or no_employee_id in
                <foreach item="nonEmployeeId" collection="nonEmployeeIds" open="(" separator="," close=")">
                    #{nonEmployeeId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>