<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HpSupplierMapper" >

  <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HpSupplier" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="supplier_id" property="supplierId" jdbcType="INTEGER" />
    <result column="supplier_name" property="supplierName" jdbcType="VARCHAR" />
    <result column="supplier_table" property="supplierTable" jdbcType="VARCHAR" />
    <result column="hotel_num" property="hotelNum" jdbcType="INTEGER" />
    <result column="supplier_code" property="supplierCode" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, supplier_id, supplier_name, supplier_table, hotel_num, supplier_code
  </sql>

  <select id="selectSupplierBaseInfo" resultType="com.corpgovernment.mapping.vo.SupplierBaseInfoVo" >
    SELECT a.supplier_id,a.supplier_name,a.hotel_num,b.hotel_name_weight,b.hotel_address_weight,b.telephone_weight as telephoneWeight
    FROM hp_supplier a LEFT JOIN hm_weight_rule b ON a.supplier_id= b.supplier_id
    WHERE a.supplier_id=#{supplierId}
  </select>

  <select id="selectAllSupplier" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hp_supplier
  </select>

<!--  <select id="selectAllSupplier" resultType="com.corpgovernment.mapping.vo.SupplierBaseInfoVo">-->
<!--    SELECT a.supplier_id,a.supplier_name,a.hotel_num,b.hotel_name_weight,b.hotel_address_weight,b.telephone_weight-->
<!--    FROM hp_supplier a LEFT JOIN hm_weight_rule b ON a.supplier_id= b.supplier_id-->
<!--    <if test="id != null">-->
<!--      WHERE a.supplier_id=#{supplierId}-->
<!--    </if>-->
<!--  </select>-->


</mapper>