<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.hotel.product.mapper.HoOrderMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.corpgovernment.hotel.product.entity.db.HoOrder" id="BaseResultMap">
        <result property="orderId" column="order_id"/>
        <result property="supplierOrderId" column="supplier_order_id"/>
        <result property="supplierUid" column="supplier_uid"/>
        <result property="supplierCorpId" column="supplier_corp_id"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="supplierPhone" column="supplier_phone"/>
        <result property="uid" column="uid"/>
        <result property="uname" column="uname" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="orgId" column="org_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="corpId" column="corp_id"/>
        <result property="source" column="source"/>
        <result property="orderDate" column="order_date"/>
        <result property="orderStatus" column="order_status"/>
        <result property="amount" column="amount"/>
        <result property="deliveryType" column="delivery_type"/>
        <result property="deliveryPrice" column="delivery_price"/>
        <result property="corpPayType" column="corp_pay_type"/>
        <result property="paytype" column="paytype"/>
        <result property="payChannel" column="pay_channel"/>
        <result property="contactName" column="contact_name" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="contactMobilePhone" column="contact_mobile_phone" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="contactCountryCode" column="contact_country_code"/>
        <result property="contactEmail" column="contact_email"/>
        <result property="tripApplyNo" column="trip_apply_no"/>
        <result property="tripTrafficId" column="trip_traffic_id"/>
        <result property="settlementStatus" column="settlement_status"/>
        <result property="settlementConfirmTime" column="settlement_confirm_time"/>
        <result property="travelStandard" column="travel_standard"/>
        <result property="specialNeed" column="special_need"/>
        <result property="ticketIssuedTime" column="ticket_issued_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="ladderAmount" column="ladder_amount"/>
        <result property="agentUid" column="agent_uid"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="approvalId" column="approval_id"/>
        <result property="pPayAmount" column="p_pay_amount"/>
        <result property="aPayAmount" column="a_pay_amount"/>
        <result property="datachangeCreatetime" column="datachange_createtime"/>
        <result property="datachangeLasttime" column="datachange_lasttime"/>
    </resultMap>
    <resultMap type="com.corpgovernment.hotel.product.model.financial.HotelOrderInfo" id="hotelOrderInfo">
        <result property="orderId" column="order_id"/>
        <result property="supplierOrderId" column="supplier_order_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="uid" column="uid"/>
        <result property="uname" column="uname" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
        <result property="deptId" column="dept_id"/>
        <result property="corpId" column="corp_id"/>
        <result property="amount" column="amount"/>
        <result property="tripApplyNo" column="trip_apply_no"/>
        <result property="tripTrafficId" column="trip_traffic_id"/>
        <result property="hotelName" column="hotel_name"/>
        <result property="cityName" column="city_name"/>
        <result property="hotelPhone" column="hotel_phone"/>
        <result property="checkInDate" column="check_in_date"/>
        <result property="checkOutDate" column="check_out_date"/>
        <result property="nextDay" column="next_day"/>
        <result property="invoiceTitle" column="invoice_title"/>
        <collection property="roomDailyInfoList" ofType="com.corpgovernment.hotel.product.model.financial.HotelOrderInfo$RoomDailyInfo">
            <result property="orderId" column="order_id"/>
            <result property="effectDate" column="effect_date"/>
            <result property="roomPrice" column="room_price"/>
        </collection>
        <collection property="passengerInfoList" ofType="com.corpgovernment.hotel.product.model.financial.HotelOrderInfo$PassengerInfo">
            <result property="passengerName" column="passenger_name" typeHandler="com.corpgovernment.common.handler.Sm4TypeHandler"/>
            <result property="uid" column="employee_id"/>
            <result property="noEmployeeId" column="no_employee_id"/>
        </collection>
    </resultMap>
    <update id="rollbackSubmitTime">
        update ho_order set submit_time = null where order_id = #{orderId}
    </update>

    <select id="listHotelOrder" resultMap="hotelOrderInfo">
        SELECT
	        o.order_id,
            o.supplier_order_id,
            o.supplier_name,
            o.uid,
            o.uname,
            o.dept_id,
            o.corp_id,
            o.amount,
            o.trip_apply_no,
            o.trip_traffic_id,
            h.hotel_name,
            h.city_name,
            h.hotel_phone,
            r.check_in_date,
            r.check_out_date,
            r.next_day,
            i.invoice_title,
            d.order_id,
            d.effect_date,
            d.room_price,
            p.passenger_name,
            p.uid employee_id,
            p.no_employee_id,
            p.traveler_name
        FROM
            ho_order o
        LEFT JOIN ho_hotel h ON o.order_id = h.order_id
        LEFT JOIN ho_room r ON o.order_id = r.order_id
        LEFT JOIN ho_invoice i ON o.order_id = i.order_id
        LEFT JOIN ho_room_daily_info d ON o.order_id = d.order_id
        LEFT JOIN ho_passenger p ON o.order_id = p.order_id
        WHERE
	        o.order_status = 'ED'
        and o.datachange_lasttime &gt;= #{request.startLastUpdateDate}
        and o.datachange_lasttime &lt;= #{request.endLastUpdateDate}
        limit 0, #{request.totalRecord}
    </select>

    <sql id="baseSelect">
        o.order_id,
        o.supplier_code,
        o.supplier_name,
        o.supplier_corp_id,
        o.corp_id,
        o.source,
        o.dept_id,
        o.uid as bookUid,
        o.uname as bookName,
        GROUP_CONCAT(p.traveler_name) as travelerName,
        GROUP_CONCAT(p.passenger_name) as passengerName,
        GROUP_CONCAT(IFNULL(p.uid,p.no_employee_id)  , '_' ,p.passenger_name) as passengerArr,
        o.ticket_issued_time,
        r.check_in_date,
        r.check_out_date,
        h.city_name,
        h.hotel_name,
        h.hotel_type,
        GROUP_CONCAT( p.uid, '_', p.employee_type ) AS employeeType,
        GROUP_CONCAT( p.cost_center_code ) AS costCenterCode,
        GROUP_CONCAT( p.cost_center_name ) AS costCenterName,
        GROUP_CONCAT( p.cost_center_remark ) AS costCenterRemark,
        GROUP_CONCAT( p.project_code ) AS projectCode,
        GROUP_CONCAT( p.project_name ) AS projectName,
        GROUP_CONCAT( p.no_select_project_desc ) AS noProjectDesc,
        GROUP_CONCAT( p.wbs_remark ) AS wbsRemark,
        GROUP_CONCAT( IFNULL(p.uid,p.no_employee_id) ) AS uid,
        r.room_name,
        r.room_quantity,
        h.star,
        o.amount,
        ifnull(o.service_fee, 0) as service_price,
        o.delivery_price,
        o.ladder_amount,
        o.corp_pay_type,
        o.paytype as payType,
        o.order_status,
        o.order_date,
        o.trip_apply_no,
        o.supplier_order_id,
        hl.amount_high
    </sql>

    <sql id="linkSql">
        ho_order o
        LEFT JOIN
        ho_room r on o.order_id = r.order_id
        LEFT JOIN
        ho_passenger p	on o.order_id = p.order_id
        LEFT JOIN
        ho_hotel h on o.order_id = h.order_id
        LEFT JOIN
        ho_hotel_low hl on o.order_id = hl.order_id
    </sql>

    <sql id="conditionsSql">
        <if test="orderIds !=null and orderIds.size()>0">
            And o.order_id in
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        <if test="beginTime!=null and endTime!=null">
            and o.ticket_issued_time BETWEEN #{beginTime} and #{endTime}
        </if>
        GROUP BY order_id
    </sql>

    <!-- 查询列表-->
    <select id="getHotelOrderListByOrderStatus" resultType="com.corpgovernment.hotel.product.dto.OrderInfoDTO">
        SELECT
        *
        FROM(
        SELECT
        <include refid="baseSelect"/>
        FROM
        <include refid="linkSql"/>
        WHERE
        (o.is_deleted != 1 or o.is_deleted is null)
        and o.paytype in ('MIXPAY','ACCNT')
        and (o.order_status='ED'
        or o.order_status ='TA'
        or o.order_status='CA'
        or o.source = 'Supplement')
        <include refid="conditionsSql"/>
        ORDER BY order_date DESC) t
    </select>
    <select id="getCompleteStatusOrder" resultType="com.corpgovernment.hotel.product.entity.db.HoOrder">
        select o.* from ho_order o left join ho_room r on o.order_id = r.order_id where o.order_status = 'TA' and r.check_out_date = #{yesterdayYMD}
    </select>
    <select id="verifyEndOrder" resultType="com.corpgovernment.hotel.product.dto.HotelOrderBo">
        select
            r.check_out_date,
            o.order_status,
            o.order_id,
            o.amount,
            o.ladder_amount,
            o.is_deleted,
            o.source
        from ho_order o left join ho_room r on (o.order_id=r.order_id)
        where  o.trip_apply_no=#{applyNo}
    </select>
    <select id="getHotelOrderList" resultType="com.corpgovernment.hotel.product.dto.OrderInfoDTO">
        SELECT
        *
        FROM(
        SELECT
        <include refid="baseSelect"/>
        FROM
        <include refid="linkSql"/>
        WHERE
        (o.is_deleted != 1 or o.is_deleted is null)
        <include refid="conditionsSqlByOrderId"/>
        ORDER BY order_date DESC) t
    </select>

    <select id="listValidOrdersByOrderIds" resultType="com.corpgovernment.hotel.product.entity.db.HoOrder">
        SELECT * FROM ho_order
        WHERE (is_deleted != 1 or is_deleted is null)
        and order_status NOT IN ('CA', 'ED') <!--排除 已取消-CA， 已完成-ED-->
        <foreach item="item" collection="ids" open="and order_id in (" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <sql id="conditionsSqlByOrderId">
        <if test="orderIds !=null and orderIds.size()>0">
            And o.order_id in
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        GROUP BY order_id
    </sql>

</mapper>