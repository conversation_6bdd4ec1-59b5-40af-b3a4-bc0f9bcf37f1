<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.hotel.product.mapper.HoRoomMapper">

    <select id="selectNeedToCompleted" parameterType="java.util.Date" resultType="java.lang.Long">
        SELECT order_id FROM ho_room WHERE order_id IN (SELECT order_id FROM ho_order WHERE order_status IN ('TA','ED')) AND DATE(check_out_date) = CURDATE()
    </select>

    <select id="listByOrderIds" parameterType="java.util.List" resultType="com.corpgovernment.hotel.product.entity.db.HoRoom">
        SELECT * FROM ho_room
        <foreach item="orderId" collection="orderIds" open="WHERE order_id IN (" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

</mapper>