<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmSupplierResultMappingMapper">
    <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HmSupplierResultMapping">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="supplier_hotel_id" property="supplierHotelId" jdbcType="VARCHAR"/>
        <result column="result_id" property="resultId" jdbcType="INTEGER"/>
        <result column="is_online" property="isOnline" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, supplier_id, supplier_hotel_id, result_id,is_online
  </sql>

    <select id="selectHotels" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hm_supplier_result_mapping where result_id in (
        select result_id
        from hm_supplier_result_mapping
        where
        <foreach collection="list" item="item" index="index" open="" close="" separator="or">
            (supplier_id=#{item.supplierId} and supplier_hotel_id=#{item.hotelId})
        </foreach>
        )
    </select>
    <insert id="insertRelatedHotels">
        insert into hm_supplier_result_mapping (supplier_id, supplier_hotel_id,
        result_id)
        values
        <foreach collection="list" item="item" index="index" open="" close="" separator=",">
            (#{item.supplierId} ,#{item.hotelId},#{resultId})
        </foreach>
    </insert>
    <update id="mergeSupplierResult">
    update hm_supplier_result_mapping set result_id=#{beforeId} WHERE result_id=#{afterId}
  </update>

    <insert id="batchInsertResultMapping" parameterType="com.corpgovernment.mapping.bo.HmHotelMatchMapping">
        insert into hm_supplier_result_mapping
        (supplier_id,supplier_hotel_id,result_id,is_online)
        values
        <foreach item="item" index="index" collection="list"
                 open="" separator="," close="">
            (#{item.supplierId},#{item.supplierHotelId},#{item.resultId},1)
        </foreach>
    </insert>

    <update id="batchUpdateSupplierResult">
    update hm_supplier_result_mapping set is_online=#{onlineStatus} WHERE result_id =#{resultId}
  </update>

    <select id="selectSupplierWithParent" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hm_supplier_result_mapping where result_id in (
        select result_id from hm_supplier_result_mapping where id=#{supplierId})
    </select>

    <select id="selectHotelsForCheck" resultType="com.corpgovernment.mapping.bo.HmSupplierResultVo">
        <foreach collection="list" item="item" index="index" open="" close="" separator="union">
            select
            a.id,b.supplier_code ,a.supplier_hotel_id,a.result_id
            FROM hm_supplier_result_mapping a LEFT JOIN hp_supplier b ON a.supplier_id =b.supplier_id
            WHERE b.supplier_code =#{item.supplier} and supplier_hotel_id in
            <foreach collection="item.hotelIds" item="hotelId" index="index" open="(" close=")" separator=",">
                #{hotelId}
            </foreach>
        </foreach>
    </select>

    <insert id="batchInsertByResultHotelNo">
        INSERT INTO hm_supplier_result_mapping (supplier_id,supplier_hotel_id,result_id)
        SELECT original_supplier_id,original_hotel_no,id FROM hp_result_hotel where original_supplier_id=#{supplierId}
        and original_hotel_no in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </insert>

    <select id="selectAllRelatedHotels" resultMap="BaseResultMap">
        SELECT * FROM hm_supplier_result_mapping WHERE result_id IN (
        SELECT a.result_id FROM hm_supplier_result_mapping a
        LEFT JOIN hp_supplier b ON a.supplier_id=b.supplier_id
        WHERE b.supplier_code=#{supplierCode} AND a.supplier_hotel_id=#{hotelNo})
    </select>

    <select id="selectExistHotelNoList" resultType="java.lang.String">
        SELECT supplier_hotel_id FROM hm_supplier_result_mapping where supplier_id=#{supplierId}
    </select>

    <select id="selectByResultIds" resultType="com.corpgovernment.mapping.bo.HmSupplierResultVo">
        select
        a.id, a.supplier_id, a.supplier_hotel_id, a.result_id
        FROM hm_supplier_result_mapping a where result_id in
        <foreach collection="resultIds" item="resultId" index="index" open="(" close=")" separator=",">
            #{resultId}
        </foreach>
    </select>


    <select id="selectByHotelIds" resultType="com.corpgovernment.mapping.bo.HmSupplierResultVo">
        select rm.supplier_id,hs.supplier_name ,rm.result_id ,rm.supplier_hotel_id, hs.supplier_code from hm_supplier_result_mapping rm, hp_supplier hs where rm.supplier_id  = hs.supplier_id  and result_id in (
            select result_id from hm_supplier_result_mapping where supplier_hotel_id in
            <foreach collection="hotelIds" item="hotelId" index="index" open="(" close=")" separator=",">
                #{hotelId}
            </foreach>
            and supplier_id in (select supplier_id  from hp_supplier WHERE  supplier_code = #{supplierCode})
        )
    </select>
</mapper>