<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HpResultHotelMapper">
    <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HpResultHotel">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="hotel_name" property="hotelName" jdbcType="VARCHAR"/>
        <result column="hotel_address" property="hotelAddress" jdbcType="VARCHAR"/>
        <result column="hotel_telephone" property="hotelTelephone" jdbcType="VARCHAR"/>
        <result column="is_online" property="isOnline" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="HotelResultMap" type="com.corpgovernment.mapping.vo.HotelResultVo">
        <result column="id" property="id"/>
        <result column="is_online" property="status"/>
        <result column="hotel_name" property="hotelName"/>
        <result column="hotel_address" property="address"/>
    </resultMap>

    <resultMap id="HotelSupplierResultMap" type="com.corpgovernment.mapping.vo.HotelResultSupplierVo">
        <result column="id" property="id"/>
        <result column="is_online" property="status"/>
        <result column="supplier_name" property="service"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, hotel_name, hotel_address, hotel_telephone
    </sql>

    <select id="getHotelSupplierByResult" resultMap="HotelSupplierResultMap">
        select a.result_id as resultId, a.id as id,a.is_online as is_online,b.supplier_name as supplier_name
        from hm_supplier_result_mapping a left join hp_supplier b on a.supplier_id=b.supplier_id where result_id in
        <foreach collection="resultList" open="(" separator="," close=")" item="item">
            #{item.id}
        </foreach>
    </select>


    <select id="selectHotelResultPage" resultMap="HotelResultMap">
        select id,hotel_name,hotel_address,is_online from hp_result_hotel where 1=1
        <if test="hotelName != null and hotelName != ''">
            and hotel_name like concat('%',#{hotelName}, '%')
        </if>

        <if test="hotelAddress != null and hotelAddress != ''">
            and hotel_address like concat('%',#{hotelAddress}, '%')
        </if>

        <if test="status != null ">
            and is_online = #{status}
        </if>

        limit #{pageRecords},#{pageSize}
    </select>

    <select id="selectHotelResultPageNum" resultType="java.lang.Integer">
        select count(1) from hp_result_hotel where 1=1
        <if test="hotelName != null and hotelName != ''">
            and hotel_name like concat('%',#{hotelName}, '%')
        </if>

        <if test="hotelAddress != null and hotelAddress != ''">
            and hotel_address like concat('%',#{hotelAddress}, '%')
        </if>

        <if test="status != null ">
            and is_online = #{status}
        </if>
    </select>

    <select id="selectHotelMergeInfo" resultType="com.corpgovernment.mapping.vo.HotelMergeVo">
        select a.match_supplier   as supplierId,
               a.total_percent    as similarity,
               a.handler_status   as handlerStatus,
               a.operator         as operator,
               a.operator_time    as operatorTime,
               a.match_hotel_name as hotelName
        from hm_hotel_match_mapping a
                 left join hm_supplier_result_mapping b on
            a.base_supplier = b.supplier_id and a.base_hotel = b.supplier_hotel_id
        where b.result_id = #{id}
          and a.handler_status in (1, 2, 3)
    </select>

    <select id="selectHotelDetail" resultType="com.corpgovernment.mapping.bo.HmHotelDetail">
        select b.*, CONCAT_WS(',', b.longitude, b.latitude) as latitude_all_best_scope
        from hp_result_hotel a
                 left join hm_hotel_detail b on a.detail_id = b.id
        where a.id = #{id}
    </select>

    <select id="selectCompareHotelDetail" resultType="com.corpgovernment.mapping.bo.HmHotelDetail">
        select a.hotel_no,a.hotel_name,c.supplier_id,c.supplier_name, d.id hotel_id,
        b.*,d.id hotel_id ,CONCAT_WS(',',b.longitude,b.latitude) as
        latitude_all_best_scope
        from hm_hotel_all_info a
        left join hm_hotel_detail b on a.detail_id= b.id
        left join hp_supplier c on a.supplier_id=c.id
        left join hp_result_hotel d on a.hotel_no = d.original_hotel_no
        where a.hotel_no in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBySupplier" useGeneratedKeys="true" keyProperty="id">
        insert into hp_result_hotel(hotel_name, hotel_address, hotel_telephone, detail_id)
        select hotel_name, hotel_address, telephone, detail_id
        from hm_hotel_all_info
        where hotel_no = #{hotelId}
          and supplier_id = #{supplierId}
    </insert>


    <select id="selectOffLine" resultType="com.corpgovernment.api.hotel.product.model.response.OffLineHotel">
        select a.supplier_id as supplier, a.supplier_hotel_id as hotelId
        from hm_supplier_result_mapping a
                 left join hp_result_hotel b on a.result_id = b.id
                 join hp_supplier c on a.supplier_id = c.supplier_id
        where a.is_online = 0
          and c.supplier_code = #{supplierCode}
        union all
        select a.supplier_id as supplier, a.supplier_hotel_id as hotelId
        from hm_supplier_result_mapping a
                 left join hp_result_hotel b on a.result_id = b.id
                 join hp_supplier c on a.supplier_id = c.supplier_id
        where b.is_online = 0
          and c.supplier_code = #{supplierCode}
    </select>

    <update id="updateHotelBySupplierId">
        update hp_result_hotel
        set is_online=#{changeStatus}
        WHERE id in (
            select t.result_id
            from (
                     select result_id
                     from hm_supplier_result_mapping
                     where id = #{supplierId}) t
        )
    </update>

    <select id="selectResultBySupplier" resultType="com.corpgovernment.mapping.vo.HotelResultInfoVo">
        SELECT a.supplier_hotel_id AS hotelNo, b.id AS resultId,b.hotel_name AS hotelName,b.hotel_address AS
        hotelAddress,b.hotel_telephone AS hotelTelPhone
        FROM hm_supplier_result_mapping a LEFT JOIN hp_result_hotel b ON a.result_id=b.id
        WHERE a.supplier_id=#{supplierId} AND a.supplier_hotel_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.hotelNo}
        </foreach>
    </select>

    <insert id="BatchInsertResultHotel">
        INSERT INTO hp_result_hotel
        (hotel_name,hotel_address,hotel_telephone,detail_id,original_supplier_id,original_hotel_no)
        SELECT hotel_name,hotel_address,telephone,detail_id,supplier_id,hotel_no
        FROM hm_hotel_all_info WHERE supplier_id=#{supplierId} and hotel_no IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </insert>

    <insert id="BatchInsertResultHotelReturnId" useGeneratedKeys="true" keyProperty="list.id">
        INSERT INTO hp_result_hotel
        (hotel_name,hotel_address,hotel_telephone,detail_id,original_supplier_id,original_hotel_no)
        SELECT hotel_name,hotel_address,telephone,detail_id,supplier_id,hotel_no
        FROM hm_hotel_all_info WHERE supplier_id=#{supplierId} and hotel_no IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.relatedHotelNo}
        </foreach>
    </insert>

    <select id="selectHotelByIds" resultType="com.corpgovernment.mapping.bo.HpResultHotel">
        SELECT id,original_supplier_id,original_hotel_no from hp_result_hotel
        WHERE id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </select>
</mapper>