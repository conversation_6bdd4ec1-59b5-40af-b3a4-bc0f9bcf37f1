<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmScoreRuleMapper" >
  <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HmScoreRule" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="supplier_id" property="supplierId" jdbcType="INTEGER" />
    <result column="supplier_name" property="supplierName" jdbcType="VARCHAR" />
    <result column="hotel_name" property="hotelName" jdbcType="DOUBLE" />
    <result column="hotel_address" property="hotelAddress" jdbcType="DOUBLE" />
    <result column="telephone" property="telephone" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, supplier_id, supplier_name, hotel_name, hotel_address, telephone
  </sql>

  <select id="selectBysupplierCode" resultMap="BaseResultMap" parameterType="com.corpgovernment.mapping.bo.HmScoreRule" >
    select
    <include refid="Base_Column_List" />
    from hm_score_rule
    where supplier_id = #{supplierId,jdbcType=INTEGER}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from hm_score_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from hm_score_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.corpgovernment.mapping.bo.HmScoreRule" >
    insert into hm_score_rule (id, supplier_id, supplier_name, 
      hotel_name, hotel_address, telephone
      )
    values (#{id,jdbcType=INTEGER}, #{supplierId,jdbcType=INTEGER}, #{supplierName,jdbcType=VARCHAR}, 
      #{hotelName,jdbcType=DOUBLE}, #{hotelAddress,jdbcType=DOUBLE}, #{telephone,jdbcType=DOUBLE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.corpgovernment.mapping.bo.HmScoreRule" >
    insert into hm_score_rule
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="supplierId != null" >
        supplier_id,
      </if>
      <if test="supplierName != null" >
        supplier_name,
      </if>
      <if test="hotelName != null" >
        hotel_name,
      </if>
      <if test="hotelAddress != null" >
        hotel_address,
      </if>
      <if test="telephone != null" >
        telephone,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null" >
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null" >
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="hotelName != null" >
        #{hotelName,jdbcType=DOUBLE},
      </if>
      <if test="hotelAddress != null" >
        #{hotelAddress,jdbcType=DOUBLE},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.corpgovernment.mapping.bo.HmScoreRule" >
    update hm_score_rule
    <set >
      <if test="supplierId != null" >
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null" >
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="hotelName != null" >
        hotel_name = #{hotelName,jdbcType=DOUBLE},
      </if>
      <if test="hotelAddress != null" >
        hotel_address = #{hotelAddress,jdbcType=DOUBLE},
      </if>
      <if test="telephone != null" >
        telephone = #{telephone,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.corpgovernment.mapping.bo.HmScoreRule" >
    update hm_score_rule
    set supplier_id = #{supplierId,jdbcType=INTEGER},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      hotel_name = #{hotelName,jdbcType=DOUBLE},
      hotel_address = #{hotelAddress,jdbcType=DOUBLE},
      telephone = #{telephone,jdbcType=DOUBLE}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>