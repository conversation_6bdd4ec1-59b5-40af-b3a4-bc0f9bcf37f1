<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmRoomMatchMappingMapper" >


    <insert id="batchInsert">
            insert into hm_room_match_mapping
            (base_supplier,base_supplier_name,match_supplier,match_supplier_name,base_hotel_name,match_hotel_name,base_hotel,match_hotel,basic_room_name,match_room_name,basic_room_area,match_room_area
            ,basic_room_person,match_room_person,basic_room_floor,match_room_floor,basic_room_addbed,match_room_addbed,basic_room_windowtype,match_room_windowtype,basic_room_bedtype,match_room_bedtype
            ,room_name_percent,room_area_percent,room_person_percent,room_floor_percent,room_addbed_percent,room_windowtype_percent,room_bedtype_percent)
            value
            <foreach collection="roomMatches" item="roomMatch" separator=",">
                (#{roomMatch.baseSupplier},#{roomMatch.baseSupplierName},#{roomMatch.matchSupplier},#{roomMatch.matchSupplierName},#{roomMatch.baseHotelName},#{roomMatch.matchHotelName},#{roomMatch.baseHotel}
                ,#{roomMatch.matchHotel},#{roomMatch.basicRoomName},#{roomMatch.matchRoomName},#{roomMatch.basicRoomArea},#{roomMatch.matchRoomArea},#{roomMatch.basicRoomPerson},#{roomMatch.matchRoomPerson}
                ,#{roomMatch.basicRoomFloor},#{roomMatch.matchRoomFloor},#{roomMatch.basicRoomAddbed},#{roomMatch.matchRoomAddbed},#{roomMatch.basicRoomWindowtype},#{roomMatch.matchRoomWindowtype},#{roomMatch.basicRoomBedtype}
                ,#{roomMatch.matchRoomBedtype},#{roomMatch.roomNamePercent},#{roomMatch.roomAreaPercent},#{roomMatch.roomPersonPercent},#{roomMatch.roomFloorPercent},#{roomMatch.roomAddbedPercent},#{roomMatch.roomWindowtypePercent}
                ,#{roomMatch.roomBedtypePercent})
            </foreach>
    </insert>
</mapper>