<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HpHotelHzInfoMapper" >
  <resultMap id="BaseResultMap" type="com.corpgovernment.mapping.bo.HpHotelHzInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="hotelNo" property="hotelno" jdbcType="VARCHAR" />
    <result column="brandCode" property="brandcode" jdbcType="VARCHAR" />
    <result column="brandName" property="brandname" jdbcType="VARCHAR" />
    <result column="hotelName" property="hotelname" jdbcType="VARCHAR" />
    <result column="hotelAddress" property="hoteladdress" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, hotelNo, brandCode, brandName, hotelName, hotelAddress, telephone, longitude, 
    latitude
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from hm_hotel_hz_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from hm_hotel_hz_info
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <select id="selectHotelListByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hm_hotel_hz_info
    where hotelNo in
    <foreach collection="lists" item="list" open="(" separator="," close=")">
      #{list}
    </foreach>
  </select>
  <select id="getHotelData" resultType="com.corpgovernment.mapping.bo.HpHotelHzInfo">
    select
    <include refid="Base_Column_List" />
    from hm_hotel_hz_info
  </select>
  <select id="selectHotelId" resultType="java.lang.String">
    select hotelNo from hm_hotel_hz_info
  </select>
  <insert id="batchInsert" parameterType="com.corpgovernment.mapping.bo.HpHotelHzInfo">
    insert into hm_hotel_hz_info (id, hotelNo, brandCode,
      brandName, hotelName, hotelAddress,
      telephone, longitude, latitude
      )
      values
    <foreach collection="lists" item="list" separator=",">
      (#{list.id,jdbcType=BIGINT}, #{list.hotelno,jdbcType=VARCHAR}, #{list.brandcode,jdbcType=VARCHAR},
      #{list.brandname,jdbcType=VARCHAR}, #{list.hotelname,jdbcType=VARCHAR}, #{list.hoteladdress,jdbcType=VARCHAR},
      #{list.telephone,jdbcType=VARCHAR}, #{list.longitude,jdbcType=VARCHAR}, #{list.latitude,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <insert id="insert" parameterType="com.corpgovernment.mapping.bo.HpHotelHzInfo" >
    insert into hm_hotel_hz_info (id, hotelNo, brandCode, 
      brandName, hotelName, hotelAddress, 
      telephone, longitude, latitude
      )
    values (#{id,jdbcType=BIGINT}, #{hotelno,jdbcType=VARCHAR}, #{brandcode,jdbcType=VARCHAR}, 
      #{brandname,jdbcType=VARCHAR}, #{hotelname,jdbcType=VARCHAR}, #{hoteladdress,jdbcType=VARCHAR}, 
      #{telephone,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.corpgovernment.mapping.bo.HpHotelHzInfo" >
    insert into hm_hotel_hz_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="hotelno != null" >
        hotelNo,
      </if>
      <if test="brandcode != null" >
        brandCode,
      </if>
      <if test="brandname != null" >
        brandName,
      </if>
      <if test="hotelname != null" >
        hotelName,
      </if>
      <if test="hoteladdress != null" >
        hotelAddress,
      </if>
      <if test="telephone != null" >
        telephone,
      </if>
      <if test="longitude != null" >
        longitude,
      </if>
      <if test="latitude != null" >
        latitude,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="hotelno != null" >
        #{hotelno,jdbcType=VARCHAR},
      </if>
      <if test="brandcode != null" >
        #{brandcode,jdbcType=VARCHAR},
      </if>
      <if test="brandname != null" >
        #{brandname,jdbcType=VARCHAR},
      </if>
      <if test="hotelname != null" >
        #{hotelname,jdbcType=VARCHAR},
      </if>
      <if test="hoteladdress != null" >
        #{hoteladdress,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        #{latitude,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.corpgovernment.mapping.bo.HpHotelHzInfo" >
    update hm_hotel_hz_info
    <set >
      <if test="hotelno != null" >
        hotelNo = #{hotelno,jdbcType=VARCHAR},
      </if>
      <if test="brandcode != null" >
        brandCode = #{brandcode,jdbcType=VARCHAR},
      </if>
      <if test="brandname != null" >
        brandName = #{brandname,jdbcType=VARCHAR},
      </if>
      <if test="hotelname != null" >
        hotelName = #{hotelname,jdbcType=VARCHAR},
      </if>
      <if test="hoteladdress != null" >
        hotelAddress = #{hoteladdress,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.corpgovernment.mapping.bo.HpHotelHzInfo" >
    update hm_hotel_hz_info
    set hotelNo = #{hotelno,jdbcType=VARCHAR},
      brandCode = #{brandcode,jdbcType=VARCHAR},
      brandName = #{brandname,jdbcType=VARCHAR},
      hotelName = #{hotelname,jdbcType=VARCHAR},
      hotelAddress = #{hoteladdress,jdbcType=VARCHAR},
      telephone = #{telephone,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>