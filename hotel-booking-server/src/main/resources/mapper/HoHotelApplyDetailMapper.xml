<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.hotel.product.mapper.HoHotelApplyDetailMapper">


    <select id="listAfterRecordByApplyIds" parameterType="map" resultType="com.corpgovernment.hotel.product.entity.db.HoHotelApplyDetail">
        SELECT * FROM ho_hotel_apply_detail
        <foreach collection="applyIds" item="applyId" open="WHERE apply_id IN (" separator="," close=")">
            #{applyId}
        </foreach>
        and after_record  = 1
    </select>

</mapper>