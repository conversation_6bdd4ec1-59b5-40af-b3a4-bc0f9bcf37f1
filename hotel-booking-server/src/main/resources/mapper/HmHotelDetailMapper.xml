<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmHotelDetailMapper">


    <select id="selectAllHotelIds" resultType="java.lang.Integer">

    SELECT hotel_no FROM hm_hotel_detail_copy
    where supplier_id = #{supplierId}
    </select>


    <!--    <update id="batchUpdateHotelAllInfo" parameterType="java.util.List">-->
    <!--        update hm_hotel_all_info-->
    <!--        <trim prefix="set" suffixOverrides=",">-->
    <!--            <trim prefix="detail_id =case" suffix="end,">-->
    <!--                <foreach collection="list" item="i" index="index">-->
    <!--                    <if test="i.detail_id!=null">-->
    <!--                        when id=#{i.hotelNo} then #{i.detailId}-->
    <!--                    </if>-->
    <!--                </foreach>-->
    <!--            </trim>-->
    <!--        </trim>-->
    <!--        where supplier_id = 1;-->

    <!--    </update>-->
    <update id="batchUpdateHotelAllInfo" parameterType="java.util.List">
        update hm_hotel_all_info a inner join  (select id, hotel_no from hm_hotel_detail_copy id limit 1000) n on a.hotel_no=n.hotel_no
			set a.detail_id=n.id    </update>

    <select id="selectDetailNoKv" resultType="com.corpgovernment.mapping.basic.bo.subbo.CtripHotelNoDetailId">
        SELECT a.hotel_no,a.id FROM hm_hotel_detail_copy a ;
    </select>

    <select id="findShHotelIds" resultType="java.lang.Integer">
        select hotel_no from  hm_hotel_sh_hotel;
    </select>
</mapper>