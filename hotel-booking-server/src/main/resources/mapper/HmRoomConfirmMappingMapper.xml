<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmRoomConfirmMappingMapper">


    <insert id="batchInsert">
        insert into hm_room_confirm_mapping
        (base_supplier,base_supplier_name,related_supplier,related_supplier_name,base_hotel,related_hotel,base_room,base_room_name,related_room,related_room_name,handler_status)
        value
        <foreach collection="roomConfirms" item="roomConfirm" separator=",">
            (#{roomConfirm.baseSupplier},#{roomConfirm.baseSupplierName},#{roomConfirm.relatedSupplier},#{roomConfirm.relatedSupplierName},#{roomConfirm.baseHotel},#{roomConfirm.relatedHotel},#{roomConfirm.baseRoom}
            ,#{roomConfirm.baseRoomName},#{roomConfirm.relatedRoom},#{roomConfirm.relatedRoomName},,#{roomConfirm.handlerStatus})
        </foreach>
    </insert>
</mapper>