<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.mapping.mapper.HmHotelMappingSuccessMapper" >

    <delete id="deleteRepeatHotel" parameterType="com.corpgovernment.mapping.bo.MatchHotelBo">
        delete from hm_hotel_mapping_success
        where (base_hotel_id, match_hotel_id) in
        <foreach collection="matchHotelBoList" item="item" index="index" open="(" close=")" separator=",">
            (#{item.baseHotelId},#{item.matchHotelId})
        </foreach>
    </delete>

    <delete id="deleteByCityId">
        delete from hm_hotel_mapping_success where base_city_id = #{baseCityId} and match_city_id = #{matchCityId}
    </delete>
</mapper>