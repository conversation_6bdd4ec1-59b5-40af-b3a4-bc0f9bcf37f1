<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.corpgovernment.core.dao.mapper.HotelOrderMapper">

    <select id="listHotelOrderDoFromIn"
            resultType="com.corpgovernment.core.dao.entity.db.HotelOrderDo">
        select *
        from ho_order
        where order_id = #{orderId}
    </select>

    <select id="listHotelOrderDoFromOut"
            resultType="com.corpgovernment.core.dao.entity.db.HotelOrderDo">
        select *
        from hio_order
        where order_id = #{orderId}
    </select>

</mapper>