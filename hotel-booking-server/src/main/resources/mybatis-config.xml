<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
  <plugins>
    <plugin interceptor="com.ctrip.corp.obt.shard.sql.mybatis.interceptor.InnerPluginsInterceptor">
      <property name="@compatibility" value="com.ctrip.corp.obt.shard.sql.mybatis.interceptor.inner.CompatibilityInnerPlugin"/>
      <property name="@forbidden" value="com.ctrip.corp.obt.shard.sql.mybatis.interceptor.inner.UpdateForbiddenProcessPlugin"/>
      <property name="forbidden:DesensitizationForbiddenMatcher" value="com.ctrip.corp.obt.shard.sql.parser.forbidden.DesensitizationForbiddenMatcher"/>
      <property name="forbidden:ForcedMaskForbiddenMatcher" value="com.ctrip.corp.obt.shard.sql.parser.forbidden.ForcedMaskForbiddenMatcher"/>
      <property name="forbidden:MaskLoggerMatcher" value="com.ctrip.corp.obt.shard.sql.parser.forbidden.MaskLoggerMatcher"/>

      <property name="@table" value="com.ctrip.corp.obt.shard.sql.mybatis.interceptor.inner.TableRerouteInnerPlugin"/>
      <property name="table:tableRerouteHandler" value="com.ctrip.corp.obt.shard.sql.parser.table.DefaultTableRerouteHandler"/>
      <property name="@tenant" value="com.ctrip.corp.obt.shard.sql.mybatis.interceptor.inner.TenantInnerPlugin"/>
      <property name="tenant:tenantHandler" value="com.ctrip.corp.obt.shard.sql.parser.tenant.DefaultTenantHandler"/>
      <property name="@page" value="com.ctrip.corp.obt.shard.sql.mybatis.interceptor.inner.PaginationInnerPlugin"/>
      <property name="page:dbType" value="mysql"/>
      <property name="page:overflow" value="true"/>
      <property name="page:dialect" value="com.ctrip.corp.obt.shard.sql.mybatis.interceptor.dialect.MySqlDialect"/>
      <property name="page:maxLimit" value="100"/>
      <property name="page:optimizeJoin" value="true"/>
    </plugin>
    <plugin interceptor="com.ctrip.corp.obt.shard.sql.mybatis.interceptor.SqlExceptionInterceptor"/>
  </plugins>
</configuration>
