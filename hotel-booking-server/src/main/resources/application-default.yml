apollo:
  meta: @apollo.meta@
arch:
  auth:
    remote-url: @arch.auth.remoteUrl@
  gray:
    url:
      organization-manage: @remoteUrl@/corpApi/organization
      car-transfer-booking: @remoteUrl@/corpApi/cartransfer
      flight-booking: @remoteUrl@/corpApi/flight
      flight-product: @remoteUrl@/corpApi/flightproduct
      approval-system: @remoteUrl@/corpApi/approval
      basic-manage: @remoteUrl@/corpApi/basic
      corpgovernment-traffic: @remoteUrl@/corpApi/traffic
      hotel-booking: @remoteUrl@/corpApi/hotelbooking
      apply-trip: @remoteUrl@/corpApi/applytrip
      train-booking: @remoteUrl@/corpApi/trainBooking
      order-center: @remoteUrl@/corpApi/order
      pay-platform: @remoteUrl@/corpApi/payGateway
      car-booking: @remoteUrl@/corpApi/carBooking
      supplier-system: @remoteUrl@/corpApi/supplierSystem
      flightintl-booking: @remoteUrl@/corpApi/flightintl
      job-center: @remoteUrl@/corpApi/jobCenter
      booking-core-service: @remoteUrl@/corpApi/booking-core-service
