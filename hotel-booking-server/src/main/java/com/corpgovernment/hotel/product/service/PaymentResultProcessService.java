package com.corpgovernment.hotel.product.service;

import com.corpgovernment.api.approvalsystem.service.ApprovalSystemClient;
import com.corpgovernment.api.approvalsystem.service.request.StartApprovalRequest;
import com.corpgovernment.api.approvalsystem.service.response.StartApprovalResponse;
import com.corpgovernment.api.hotel.product.bo.HotelConfirmOrderRequestBO;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.ordercenter.dto.OcUtils;
import com.corpgovernment.api.ordercenter.dto.request.invoicing.SaveInvoicingDetailRequest;
import com.corpgovernment.api.ordercenter.soa.IIssueInvoiceClient;
import com.corpgovernment.api.platform.soa.handlerPaymentBill.PushPayResultModel;
import com.corpgovernment.api.platform.soa.paymentbill.PaymentBillDetailResponse;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.common.utils.LogSplicingUtils;
import com.corpgovernment.consolidation.sdk.enums.ApplicationEnum;
import com.corpgovernment.consolidation.sdk.enums.ApprovalStatusEnum;
import com.corpgovernment.consolidation.sdk.event.ApprovalStatusChangeExternalEvent;
import com.corpgovernment.hotel.booking.enums.PayStatusEnum;
import com.corpgovernment.hotel.product.dataloader.db.HoDeliveryInfoLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoInvoiceLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoRoomLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoDeliveryInfo;
import com.corpgovernment.hotel.product.entity.db.HoInvoice;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.entity.db.HoRoom;
import com.corpgovernment.hotel.product.mq.OrderStatusProducer;
import com.ctrip.corp.obt.generic.event.EventCenter;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.EnvironmentHolder;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PaymentResultProcessService {

    private static final String PAYMENT_SUCCESS_PROCESS_REDIS_KEY_PREFIX = "PAYMENT:SUCCESS:PROCESS";

    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private ConfirmOrderService confirmOrderService;
    @Autowired
    private IIssueInvoiceClient iIssueInvoiceService;
    @Autowired
    private HoInvoiceLoader hoInvoiceLoader;
    @Autowired
    private HoRoomLoader hoRoomLoader;
    @Autowired
    private HoDeliveryInfoLoader hoDeliveryInfoLoader;
    @Autowired
    private OrderStatusProducer orderStatusProducer;
    @Autowired
    private PayClientLoader payClientLoader;
    @Autowired
    private ApprovalSystemClient approvalSystemClient;
    @Autowired
    private HotelPushService hotelPushService;
    @Resource
    private RedissonClient redissonClient;

    public void processPaymentResult(PushPayResultModel pushPayResultModel, StringBuilder logContext) {
        String lockKey = getLockKey(pushPayResultModel.getOrderId());
        RLock lock = redissonClient.getLock(lockKey);

        try {
            boolean locked = lock.tryLock(0, 90, TimeUnit.SECONDS);
            log.info("获取支付成功处理锁【key】:{},【获取结果】:{}", lockKey, locked);
            if (!locked) {
                LogSplicingUtils.addLogContext(logContext, "获取支付成功处理锁失败");
                return;
            }
            String orderId = pushPayResultModel.getOrderId();
            boolean successFlag = pushPayResultModel.isSuccessFlag();

            if (StringUtils.isBlank(orderId) || !successFlag) {
                LogSplicingUtils.addLogContext(logContext, "缺少必要参数");
                return;
            }

            //查询订单信息
            HoOrder orderInfo = hoOrderLoader.selectByOrderId(Long.valueOf(orderId));
            LogSplicingUtils.addLogContext(logContext, "订单信息：%s", JsonUtils.toJsonString(orderInfo));
            if (orderInfo.getNewSystem()) {
                LogSplicingUtils.addLogContext(logContext, "新系统订单不处理");
                return;
            }

            //校验状态
            if (!Objects.equals(OrderStatusEnum.PW.getCode(), orderInfo.getOrderStatus())) {
                LogSplicingUtils.addLogContext(logContext, "订单状态不是待支付");
                return;
            }

            if (!PayTypeEnum.CASH.getType().equals(orderInfo.getPaytype())) {
                //查询支付单
                List<PaymentBillDetailResponse> paymentBillList = payClientLoader.getPaymentBillDetailList(orderInfo.getOrderId());
                LogSplicingUtils.addLogContext(logContext, "支付单信息：%s", JsonUtils.toJsonString(paymentBillList));
                if (CollectionUtils.isEmpty(paymentBillList)) {
                    LogSplicingUtils.addLogContext(logContext, "未找到支付单");
                    return;
                }
                //校验状态
                Optional<PaymentBillDetailResponse> first = paymentBillList.stream().filter(item -> PayStatusEnum.U.getType().equals(item.getStatus())).findFirst();
                if (first.isPresent()) {
                    LogSplicingUtils.addLogContext(logContext, "存在未支付的订单");
                    return;
                }
            }

            // 同步支付方式
            if (StringUtils.isNotBlank(pushPayResultModel.getPayChannel())) {
                hoOrderLoader.updateOrderPayChannel(Long.valueOf(orderId), pushPayResultModel.getPayChannel());
            }

            // 如果是个付/混付 那么看下有没有审批 假如有则启动审批
            if (Lists.newArrayList(PayTypeEnum.MIXPAY.getType(), PayTypeEnum.PPAY.getType()).contains(orderInfo.getPaytype())) {
                if (StringUtils.isNotBlank(orderInfo.getApprovalId())) {

                    LogSplicingUtils.addLogContext(logContext, "将订单状态变为待审批");
                    hoOrderLoader.updateOrderStatus(orderInfo.getOrderId(), null, OrderStatusEnum.AW.getCode());
                    orderStatusProducer.sendOrderStatusMsg(OcUtils.initOcReq(orderInfo.getOrderId(), OrderStatusEnum.AW.getCode(), null, OrderTypeEnum.HN));

                    LogSplicingUtils.addLogContext(logContext, "启动审批流");
                    boolean startFlag = startApproval(orderInfo.getApprovalId());
                    LogSplicingUtils.addLogContext(logContext, "启动审批流状态：%s", startFlag);

                    if (startFlag) {
                        // 发布ocs审批启动事件
                        log.info("审批启动，发送ocs审批启动事件，orderId:{}", orderId);
                        EnvironmentHolder.getBean(EventCenter.class).post(
                                new ApprovalStatusChangeExternalEvent(ApplicationEnum.HOTEL_DOMESTIC.getCode(),
                                        Long.parseLong(orderId),
                                        ApprovalStatusEnum.W.getCode()));
                    }
                    return;
                }
            }

            // 如果是混付，不需要审批，则创建公司支付部分支付单
            if (Objects.equals(PayTypeEnum.MIXPAY.getType(), orderInfo.getPaytype())
                    && StringUtils.isBlank(orderInfo.getApprovalId())) {
                hotelPushService.createPayBill(orderInfo);
            }

            LogSplicingUtils.addLogContext(logContext, "修改订单状态为待确认");
            hoOrderLoader.updateOrderStatus(orderInfo.getOrderId(), null, OrderStatusEnum.TW.getCode());
            confirmOrderService.confirmOrder(HotelConfirmOrderRequestBO.create(Long.valueOf(orderId)));
            orderStatusProducer.sendOrderStatusMsg(OcUtils.initOcReq(orderInfo.getOrderId(), OrderStatusEnum.TW.getCode(), null, OrderTypeEnum.HN));
        } catch (InterruptedException e) {
            log.error("redis获取锁异常：" + e.getMessage(), e);
        } finally {
            //如果当前线程保持锁定则解锁
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }


    }

    private String getLockKey(String orderId) {
        return PAYMENT_SUCCESS_PROCESS_REDIS_KEY_PREFIX.concat(orderId);
    }

    public boolean startApproval(String externalId){
        // 启动审批
        StartApprovalRequest startApprovalRequest = new StartApprovalRequest();
        startApprovalRequest.setExternalId(externalId);
        startApprovalRequest.setOperator("system");
        startApprovalRequest.setOperatorName("system");
        StartApprovalResponse startApprovalResponse = approvalSystemClient.startApproval(startApprovalRequest);
        if (startApprovalResponse == null || !startApprovalResponse.isResult()) {
            return false;
        }
        return true;
    }

    /**
     * 添加到开票配送列表
     */
    public void addIssueInvoice(HoOrder orderInfo) {
        //只有个人支付的订单进待开票列表
        if (Objects.equals(orderInfo.getPaytype().toUpperCase(), "ACCNT")) {
            return;
        }
        HoInvoice invoiceInfo = getCoInvoice(orderInfo.getOrderId());
        if (invoiceInfo != null) {
            SaveInvoicingDetailRequest request = new SaveInvoicingDetailRequest();
            request.setIssueInvoiceType(1);
            //从订单表中获取订单号
            request.setOrderNo(String.valueOf(orderInfo.getOrderId()));
            //从订单表中获取因公/因私
            request.setOrderType(orderInfo.getCorpPayType().toLowerCase());
            request.setHandlerStatus(0);
            //发票类型 1：专票(纸质) 2：普票（电子）
            if (Integer.parseInt(invoiceInfo.getInvoiceType()) == 1) {
                request.setInvoiceType(0);
            } else {
                request.setInvoiceType(1);
            }
            request.setCompany("航天集团");
            //co_order.dispatch_vendor_code
            request.setSupplierCode(orderInfo.getSupplierCode());
            HoRoom hoRoom = getRoom(orderInfo.getOrderId());
            if (hoRoom != null) {
                request.setDisplayDate(DateUtil.dateToYMD(hoRoom.getCheckOutDate()));
            }
            request.setInvoiceAmount(String.valueOf(orderInfo.getAmount().add(orderInfo.getDeliveryPrice())));
            request.setAccountBank(invoiceInfo.getAccountBank());
            request.setAccountCardNo(invoiceInfo.getAccountCardNo());
            request.setCorporationAddress(invoiceInfo.getCorporationAddress());
            request.setCorporationTel(invoiceInfo.getCorporationTel());
            request.setInvoiceTitle(invoiceInfo.getInvoiceTitle());
            request.setInvoiceTitleType(String.valueOf(invoiceInfo.getInvoiceTitleType()));
            request.setInvoiceContent(invoiceInfo.getInvoiceContent());
            request.setTaxpayerNumber(invoiceInfo.getTaxpayerNumber());
            if (Objects.equals(invoiceInfo.getInvoiceType(), "1")) {
                HoDeliveryInfo deliveryInfo = getDeliveryInfo(orderInfo.getOrderId());
                if (deliveryInfo == null) {
                    request.setDeliveryAddress("未知");
                    request.setRecipientName("未知");
                    request.setRecipientMobilePhone("未知");
                } else {
                    request.setDeliveryAddress(deliveryInfo.getProvinceName() +
                            deliveryInfo.getCityName() + deliveryInfo.getDistrictName() + deliveryInfo.getAddress());
                    request.setRecipientName(deliveryInfo.getRecipientName());
                    request.setRecipientMobilePhone(deliveryInfo.getRecipientMobilePhone());
                }
            } else {
                request.setEmail(orderInfo.getContactEmail());
            }
            iIssueInvoiceService.addIssuingInvoice(request);
        } else {

        }
    }

    private HoInvoice getCoInvoice(Long orderId) {
        return hoInvoiceLoader.selectByOrderId(orderId);
    }

    private HoDeliveryInfo getDeliveryInfo(Long orderId) {
        return hoDeliveryInfoLoader.selectByOrderId(orderId);
    }

    private HoRoom getRoom(Long orderId) {
        return hoRoomLoader.selectByOrderId(orderId);
    }

}
