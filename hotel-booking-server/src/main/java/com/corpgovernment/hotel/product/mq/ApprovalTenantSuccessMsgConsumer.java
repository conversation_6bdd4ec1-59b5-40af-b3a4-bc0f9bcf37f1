package com.corpgovernment.hotel.product.mq;

import com.corpgovernment.api.approvalsystem.mq.CallbackPojo;
import com.corpgovernment.common.mq.consumer.AbstractMultiTenantRocketMQConsumer;
import com.corpgovernment.common.mq.enums.MessageBizTypeEnum;
import com.corpgovernment.hotel.product.service.ApprovalResultProcessService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 审批完成推送consumer
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ApprovalTenantSuccessMsgConsumer extends AbstractMultiTenantRocketMQConsumer {

    @Resource
    private ApprovalResultProcessService approvalResultProcessService;

    @Override
    protected void consume(MessageExt messageExt, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        String msg = new String(messageExt.getBody(), StandardCharsets.UTF_8);
        log.info(String.format("接受到的消息为：[%s]，MsgId：[%s]", msg, messageExt.getMsgId()));
        CallbackPojo callback = JsonUtils.parse(msg, CallbackPojo.class);
        if (callback == null) {
            return;
        }
        log.info("审批消费:{}", JsonUtils.toJsonString(callback));
        approvalResultProcessService.processApprovalResult(callback);

    }

    @Override
    protected MessageBizTypeEnum getMessageBizType() {
        return MessageBizTypeEnum.APPROVE_CALLBACK_HOTEL;
    }

}