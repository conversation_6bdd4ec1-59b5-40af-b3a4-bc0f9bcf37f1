package com.corpgovernment.hotel.product.external.dto.order.confirm;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 携程契约-确认订单响应类
 */
@Data
public class CtripConfirmOrderResponse {

    @ApiModelProperty("错误码")
    private CtripResponseStatus status;

    @ApiModelProperty("供应商订单号")
    private String orderId;

    @ApiModelProperty("授权类型：T：同意授权 F：不同意授权")
    private String auditType;

    @Data
    public static class CtripResponseStatus {

        @ApiModelProperty("是否成功")
        private Boolean success;

        @ApiModelProperty("错误码")
        @JsonAlias({"errorCode", "code"})
        private Integer errorCode;

        @ApiModelProperty("错误提示信息")
        @JsonAlias({"message", "errorMessage", "msg"})
        private String message;
    }
}
