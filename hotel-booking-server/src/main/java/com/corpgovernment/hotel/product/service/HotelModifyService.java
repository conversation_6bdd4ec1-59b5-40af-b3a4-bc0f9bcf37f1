package com.corpgovernment.hotel.product.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.corpgovernment.api.hotel.product.dto.UpdateHotelApplyRequest;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.constant.LogTagConstants;
import com.corpgovernment.common.apollo.CommonApollo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.hotel.booking.bo.CheckInOutDateInfoBo;
import com.corpgovernment.hotel.booking.enums.HotelModifyStatusEnum;
import com.corpgovernment.hotel.booking.metric.HotelModifyMetricBo;
import com.corpgovernment.hotel.booking.metric.MetricService;
import com.corpgovernment.hotel.booking.vo.hotelmodify.*;
import com.corpgovernment.hotel.product.dataloader.db.*;
import com.corpgovernment.hotel.product.entity.db.*;
import com.corpgovernment.hotel.product.external.client.SupplierSoaClient;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModifiableRoomNightQueryRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModifiableRoomNightQueryResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModificationRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModificationResponse;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.DateUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lilayzzz
 * @since: 2023/12/14
 * @description:
 */
@Service
@Slf4j
public class HotelModifyService {

    @Autowired
    private CommonService commonService;
    @Autowired
    private HoHotelApplyLoader hoHotelApplyLoader;
    @Autowired
    private HoHotelApplyDetailLoader hoHotelApplyDetailLoader;
    @Autowired
    private HoHotelApplyStatusRecordLoader hoHotelApplyStatusRecordLoader;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HoHotelLoader hoHotelLoader;
    @Autowired
    private HoRoomLoader hoRoomLoader;
    @Autowired
    private HoPassengerLoader hoPassengerLoader;
    @Autowired
    private HotelModifyService hotelModifyService;
    @Autowired
    private MetricService metricService;
    
    @Autowired
    private SupplierSoaClient supplierSoaClient;

    @Autowired
    private CommonApollo commonApollo;

    /**
     * 提前离店状态排序首位编码
     */
    private static final Integer FIRST_VIEW_SORT = 1;

    /**
     * 酒店修改（提前离店）
     *
     * @param request
     * @return
     */
    public HotelModifyResponse hotelModify(HotelModifyRequest request) {

        try {
            HoOrder hoOrder = hoOrderLoader.selectByOrderId(request.getOrderId());
            StandardOrderModificationRequest standardOrderModificationRequest =
                    packageHotelModifySupplierRequest(request, hoOrder);
            
            standardOrderModificationRequest.setSupplierCode(hoOrder.getSupplierCode());
            standardOrderModificationRequest.setCompanyCode(hoOrder.getCorpId());
            standardOrderModificationRequest.setCorpPayType(hoOrder.getCorpPayType());

            log.info("酒店修改请求参数：{}", JsonUtils.toJsonString(standardOrderModificationRequest));

            // 强制取消 不与供应商交互
            if (request.getForceModify()) {
                String applyId = IdUtil.fastSimpleUUID();
                hotelModifyService.saveHotelModifyInfo(hoOrder.getOrderId(), applyId, standardOrderModificationRequest, request.getModifyInfo().getCheckInOutDetailList(), Boolean.TRUE);
                HotelModifyResponse hotelModifyResponse = new HotelModifyResponse();
                hotelModifyResponse.setSuccess(true);
                hotelModifyResponse.setApplyId(applyId);
                return hotelModifyResponse;
            }

            StandardOrderModificationResponse modificationResponse =
                supplierSoaClient.createOrderModification(standardOrderModificationRequest);
            if (Objects.nonNull(modificationResponse) && StringUtils.isNotBlank(modificationResponse.getApplyFormID())) {
                hotelModifyService.saveHotelModifyInfo(hoOrder.getOrderId(),modificationResponse.getApplyFormID(),
                        standardOrderModificationRequest, request.getModifyInfo().getCheckInOutDetailList(), Boolean.FALSE);
                HotelModifyResponse hotelModifyResponse = new HotelModifyResponse();
                hotelModifyResponse.setSuccess(true);
                hotelModifyResponse.setApplyId(modificationResponse.getApplyFormID());
                return hotelModifyResponse;
            }
        } catch (Exception e) {
            log.error("酒店修改异常", e);
        }
        return HotelModifyResponse.failed();
    }

    private void metricHotelModify(HoOrder hoOrder, HotelModifySupplierResponse response) {
        try {
            HotelModifyMetricBo hotelModifyMetricBo = new HotelModifyMetricBo();
            hotelModifyMetricBo.setSupplierCode(hoOrder.getSupplierCode());
            if (null == response || null == response.getStatus()) {
                hotelModifyMetricBo.setErrorCode(LogTagConstants.UNKNOWN);
                hotelModifyMetricBo.setErrorMessage(LogTagConstants.UNKNOWN);
                metricService.metricHotelModify(hotelModifyMetricBo);
            } else {
                hotelModifyMetricBo.setErrorCode(response.getStatus().getErrorCode());
                hotelModifyMetricBo.setErrorMessage(response.getStatus().getErrorCode());
                metricService.metricHotelModify(hotelModifyMetricBo);
            }
        } catch (Exception e) {
            log.error("埋点酒店修改监控失败", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveHotelModifyInfo(Long orderId, String applyId, StandardOrderModificationRequest hotelModifySupplierRequest, List<HotelModifyRequest.CheckInOutDetailInfo> checkInOutDetailList, Boolean forceModify) {
        // 保存修改单记录
        hoHotelApplyLoader.insert(packageHotelApply(orderId, applyId, hotelModifySupplierRequest.getReasonCode(),
            hotelModifySupplierRequest.getReasonContent(), forceModify));
        // 保存修改单详情
        hoHotelApplyDetailLoader.insertList(packageHotelApplyDetailList(orderId, applyId, hotelModifySupplierRequest, checkInOutDetailList));
        // 保存修改单状态记录
        hoHotelApplyStatusRecordLoader.insert(packageHotelApplyStatusRecord(applyId, forceModify));
    }

    private HoHotelApply packageHotelApply(Long orderId, String applyId, String reasonCode, String reasonDesc, Boolean forceModify) {
        HoHotelApply hoHotelApply = new HoHotelApply();
        hoHotelApply.setApplyId(applyId);
        hoHotelApply.setOrderId(orderId);
        hoHotelApply.setReasonCode(reasonCode);
        hoHotelApply.setReasonDesc(reasonDesc);
        hoHotelApply.setStatus(forceModify ? HotelModifyStatusEnum.PLATFORM_SUBMITTED.getCode() : HotelModifyStatusEnum.SUBMITTED.getCode());
        return hoHotelApply;
    }

    private List<HoHotelApplyDetail> packageHotelApplyDetailList(Long orderId, String applyId,
                                                                 StandardOrderModificationRequest hotelModifySupplierRequest, List<HotelModifyRequest.CheckInOutDetailInfo> checkInOutDetailList) {

        List<HoHotelApplyDetail> resultList = new ArrayList<>(2);
        // 组装申请前详情
        HoHotelApplyDetail beforeHotelApplyDetail = new HoHotelApplyDetail();
        beforeHotelApplyDetail.setApplyId(applyId);
        beforeHotelApplyDetail.setAfterRecord(Boolean.FALSE);
        beforeHotelApplyDetail.setDatachangeCreatetime(new Date());
        beforeHotelApplyDetail.setDatachangeLasttime(new Date());

        List<HoHotelApply> hoHotelApplyList = hoHotelApplyLoader.select(orderId);
        List<HoHotelApply> sucApplyList = Optional.ofNullable(hoHotelApplyList).orElse(new ArrayList<>()).stream()
            .filter(item -> HotelModifyStatusEnum.SUCCESS.getCode().equals(item.getStatus()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sucApplyList)) {
            sucApplyList.sort(Comparator.comparing(HoHotelApply::getDatachangeCreatetime).reversed());
            sucApplyList.stream().findFirst().ifPresent(apply -> {
                List<HoHotelApplyDetail> hotelApplyDetailList = hoHotelApplyDetailLoader.select(apply.getApplyId());
                hotelApplyDetailList.stream().filter(detail -> BooleanUtils.isTrue(detail.getAfterRecord())).findFirst()
                    .ifPresent(after -> {
                        beforeHotelApplyDetail.setCheckInDate(after.getCheckInDate());
                        beforeHotelApplyDetail.setCheckOutDate(after.getCheckOutDate());
                        beforeHotelApplyDetail.setRoomNight(after.getRoomNight());
                        beforeHotelApplyDetail.setCheckInOutDateDetail(after.getCheckInOutDateDetail());
                    });
            });
        } else {
            HoRoom hoRoom = hoRoomLoader.selectByOrderId(orderId);
            beforeHotelApplyDetail.setCheckInDate(hoRoom.getCheckInDate());
            beforeHotelApplyDetail.setCheckOutDate(hoRoom.getCheckOutDate());
            //间夜数=房间数*晚数
            Integer roomNight = hoRoom.getNextDay() * hoRoom.getRoomQuantity();
            beforeHotelApplyDetail.setRoomNight(roomNight);

            HotelModifyRequest.CheckInOutDetailInfo checkInOutDetailInfo = new HotelModifyRequest.CheckInOutDetailInfo();
            checkInOutDetailInfo.setCheckInDate(DateUtils.formatDate(hoRoom.getCheckInDate(), DateUtils.DATE_FORMAT));
            checkInOutDetailInfo.setCheckOutDate(DateUtils.formatDate(hoRoom.getCheckOutDate(), DateUtils.DATE_FORMAT));
            beforeHotelApplyDetail.setCheckInOutDateDetail(JsonUtils.toJsonString(CollectionUtils.newArrayList(checkInOutDetailInfo)));
        }
        resultList.add(beforeHotelApplyDetail);
        // 组装申请后详情
        StandardOrderModificationRequest.ModifyInfo modifyInfo = hotelModifySupplierRequest.getModifyInfo();
        HoHotelApplyDetail afterHotelApplyDetail = new HoHotelApplyDetail();
        afterHotelApplyDetail.setApplyId(applyId);
        afterHotelApplyDetail.setAfterRecord(Boolean.TRUE);
        // 入住时间
        Date checkInDate = DateUtil.stringToDate(modifyInfo.getCheckInTime(), DateUtil.DF_YMD_HMS);
        // 最后入住时间
        Date checkOutDate = DateUtil.stringToDate(modifyInfo.getCheckOutTime(), DateUtil.DF_YMD_HMS);
        // 离店时间需要+1天
        // checkOutDate = DateUtil.addDays(checkOutDate, 1);
        afterHotelApplyDetail.setCheckInDate(checkInDate);
        afterHotelApplyDetail.setCheckOutDate(checkOutDate);
        //间夜数=房间数*晚数 列表中存在每一天的房间数 累加即可
        Integer roomNight = modifyInfo.getReservedRoomNightList().stream()
            .map(StandardOrderModificationRequest.ReservedRoomNight::getQuantity).filter(Objects::nonNull)
            .reduce(0, Integer::sum);
        afterHotelApplyDetail.setRoomNight(roomNight);
        afterHotelApplyDetail.setDatachangeCreatetime(new Date());
        afterHotelApplyDetail.setDatachangeLasttime(new Date());
        afterHotelApplyDetail.setCheckInOutDateDetail(JsonUtils.toJsonString(checkInOutDetailList));
        resultList.add(afterHotelApplyDetail);
        return resultList;
    }

    private HoHotelApplyStatusRecord packageHotelApplyStatusRecord(String applyId, Boolean forceModify) {
        HoHotelApplyStatusRecord record = new HoHotelApplyStatusRecord();
        record.setApplyId(applyId);
        record.setHandleDate(new Date());
        record.setStatus(forceModify ? HotelModifyStatusEnum.PLATFORM_SUBMITTED.getCode() : HotelModifyStatusEnum.SUBMITTED.getCode());
        record.setViewSort(FIRST_VIEW_SORT);
        record.setDatachangeCreatetime(new Date());
        record.setDatachangeLasttime(new Date());
        return record;
    }

    private StandardOrderModificationRequest packageHotelModifySupplierRequest(HotelModifyRequest request, HoOrder hoOrder) {
        HoRoom hoRoom = hoRoomLoader.selectByOrderId(hoOrder.getOrderId());
        List<HoPassenger> passengerList = hoPassengerLoader.selectByOrderId(hoOrder.getOrderId());

        StandardOrderModificationRequest hotelModifySupplierRequest = new StandardOrderModificationRequest();
        hotelModifySupplierRequest.setOrderID(hoOrder.getSupplierOrderId());
        hotelModifySupplierRequest.setReasonCode(request.getReasonCode());
        hotelModifySupplierRequest.setReasonContent(request.getReasonContent());

        List<StandardOrderModificationRequest.ReservedRoomNight> roomNightInfoList = new ArrayList<>();
        if (request.getModifyInfo() != null
            && CollectionUtils.isNotEmpty(request.getModifyInfo().getReservedRoomNightList())) {
            for (HotelModifyRequest.RoomNightInfo roomNightInfo : request.getModifyInfo().getReservedRoomNightList()) {
                StandardOrderModificationRequest.ReservedRoomNight supplierRoomNightInfo =
                    new StandardOrderModificationRequest.ReservedRoomNight();
                supplierRoomNightInfo.setQuantity(hoRoom.getRoomQuantity());
                supplierRoomNightInfo.setDate(roomNightInfo.getDate());
                List<StandardOrderModificationRequest.ClientInfo> clientInfoList = new ArrayList<>();
                for (HoPassenger passenger : passengerList) {
                    StandardOrderModificationRequest.ClientInfo client = new StandardOrderModificationRequest.ClientInfo();
                    client.setRoomIndex(passenger.getRoomIndex());
                    client.setName(passenger.getPassengerName());
                    String uid = StringUtils.isNotBlank(passenger.getUid()) ? passenger.getUid()
                        : passenger.getNoEmployeeId().toString();
                    client.setUID(uid);
                    clientInfoList.add(client);
                }
                supplierRoomNightInfo.setClientInfoList(clientInfoList);
                roomNightInfoList.add(supplierRoomNightInfo);
            }
            StandardOrderModificationRequest.ModifyInfo modifyInfo = new StandardOrderModificationRequest.ModifyInfo();
            Date nextCheckOutTime = DateUtil
                .addDays(DateUtil.stringToDate(request.getModifyInfo().getCheckOutTime(), DateUtil.DF_YMD_HMS), 1);
            modifyInfo.setCheckOutTime(DateUtil.dateToString(nextCheckOutTime, DateUtil.DF_YMD_HMS));
            modifyInfo.setCheckInTime(request.getModifyInfo().getCheckInTime());
            modifyInfo.setReservedRoomNightList(roomNightInfoList);
            hotelModifySupplierRequest.setModifyInfo(modifyInfo);
        } else {
            throw new CorpBusinessException(HotelResponseCodeEnum.NEED_TO_BE_RESERVED_ROOM_CAN_NOT_BE_EMPTY);
        }
        return hotelModifySupplierRequest;
    }

//    /**
//     * 获取必传的酒店信息
//     */
//    private List<OrderDetailResponse.Itinerary> getOrderDetail(HoOrder hoOrder) {
//        OrderDetailResponse response = hotelPushService.getHotelOrderInfoList(hoOrder.getSupplierOrderId(),
//            hoOrder.getSupplierCode(), hoOrder.getSupplierCorpId());
//        if (response != null && CollectionUtils.isNotEmpty(response.getItineraryList())) {
//            return response.getItineraryList();
//        }
//        return Collections.emptyList();
//    }

    public HotelModifyQueryResponse hotelModifyDetail(HotelModifyQueryRequest request) {
        List<HoHotelApply> hoHotelApplyList = hoHotelApplyLoader.select(request.getOrderId());
        if (CollectionUtils.isEmpty(hoHotelApplyList)) {
            return new HotelModifyQueryResponse();
        }

        List<HoPassenger> passengerList = hoPassengerLoader.selectByOrderId(request.getOrderId());
        List<HotelModifyQueryResponse.HotelModifyDetail> detailList = hoHotelApplyList.stream().map(hotelApply -> {

            List<HoHotelApplyDetail> hotelApplyDetailList = hoHotelApplyDetailLoader.select(hotelApply.getApplyId());
            if (CollectionUtils.isEmpty(hotelApplyDetailList)) {
                return null;
            }
            List<HoHotelApplyStatusRecord> applyStatusRecordList =
                hoHotelApplyStatusRecordLoader.select(hotelApply.getApplyId());
            if (CollectionUtils.isEmpty(applyStatusRecordList)) {
                return null;
            }
            Optional<HoHotelApplyDetail> beforeApplyDetail = hotelApplyDetailList.stream()
                .filter(applyDetail -> BooleanUtils.isFalse(applyDetail.getAfterRecord())).findFirst();
            Optional<HoHotelApplyDetail> afterApplyDetail = hotelApplyDetailList.stream()
                .filter(applyDetail -> BooleanUtils.isTrue(applyDetail.getAfterRecord())).findFirst();
            if (!beforeApplyDetail.isPresent() || !afterApplyDetail.isPresent()) {
                return null;
            }

            HotelModifyQueryResponse.HotelModifyDetail orderDetail = new HotelModifyQueryResponse.HotelModifyDetail();
            orderDetail.setApplyId(
                Optional.ofNullable(hotelApply.getApplyId()).map(String::valueOf).orElse(StringUtils.EMPTY));
            orderDetail.setApplyTime(DateUtil.dateToString(hotelApply.getDatachangeCreatetime(), DateUtil.DF_YMD_HMS));
            orderDetail.setCancelReason(hotelApply.getReasonDesc());
            orderDetail.setApplyStatus(hotelApply.getStatus());
            orderDetail.setOriginOrderInfo(buildModifyInfo(beforeApplyDetail.get(), passengerList));
            orderDetail.setNowOrderInfo(buildModifyInfo(afterApplyDetail.get(), passengerList));
            orderDetail.setOriginOrderInfoList(buildModifyInfoList(beforeApplyDetail.get(), passengerList));
            orderDetail.setNowOrderInfoList(buildModifyInfoList(afterApplyDetail.get(), passengerList));
            orderDetail.setStatusItemList(applyStatusRecordList.stream()
                .map(this::buildStatusItem).collect(Collectors.toList()));
            orderDetail.setRefundAmount(hotelApply.getRefundAmount());
            return orderDetail;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return buildInAdvanceCancelOrderListQueryResponse(detailList);
    }

    private HotelModifyQueryResponse.OrderInfo buildModifyInfo(HoHotelApplyDetail hoHotelApplyDetail,
        List<HoPassenger> passengerList) {
        HotelModifyQueryResponse.OrderInfo afterModifyInfo = new HotelModifyQueryResponse.OrderInfo();
        afterModifyInfo.setRoomNight(hoHotelApplyDetail.getRoomNight());
        afterModifyInfo.setCheckInTime(DateUtil.dateToString(hoHotelApplyDetail.getCheckInDate(), DateUtil.DF_YMD_HMS));
        afterModifyInfo
            .setCheckOutTime(DateUtil.dateToString(hoHotelApplyDetail.getCheckOutDate(), DateUtil.DF_YMD_HMS));
        List<HotelModifyQueryResponse.roomInfo> roomInfoList =
            this.getDatesBetween(hoHotelApplyDetail.getCheckInDate(),
                DateUtil.addDays(hoHotelApplyDetail.getCheckOutDate(), -1)).stream().map(date -> {
                    HotelModifyQueryResponse.roomInfo roomInfo = new HotelModifyQueryResponse.roomInfo();
                    roomInfo.setDate(date);
                    roomInfo.setQuantity(1);
                    roomInfo.setRoomInfoList(passengerList.stream().map(passenger -> {
                        HotelModifyQueryResponse.SingleRoomInfo singleRoomInfo =
                            new HotelModifyQueryResponse.SingleRoomInfo();
                        singleRoomInfo.setName(passenger.getPassengerName());
                        singleRoomInfo.setRoomIndex(passenger.getRoomIndex());
                        return singleRoomInfo;
                    }).collect(Collectors.toList()));
                    return roomInfo;
                }).collect(Collectors.toList());
        afterModifyInfo.setRoomInfoList(roomInfoList);
        return afterModifyInfo;
    }

    private List<HotelModifyQueryResponse.OrderInfo> buildModifyInfoList(HoHotelApplyDetail hoHotelApplyDetail,
        List<HoPassenger> passengerList) {
        String checkInOutDateDetail = hoHotelApplyDetail.getCheckInOutDateDetail();
        if (StringUtils.isBlank(checkInOutDateDetail)) {
            return Collections.emptyList();
        }

        List<CheckInOutDateInfoBo> checkInOutDateInfoBoList =
            JsonUtils.parseArray(checkInOutDateDetail, CheckInOutDateInfoBo.class);

        return checkInOutDateInfoBoList.stream().map(checkInOutDateInfoBo -> {
            HotelModifyQueryResponse.OrderInfo orderInfo = new HotelModifyQueryResponse.OrderInfo();
            orderInfo.setCheckInTime(DateUtil.dateToString(checkInOutDateInfoBo.getCheckInDate(), DateUtil.DF_YMD_HMS));
            orderInfo.setCheckOutTime(DateUtil.dateToString(checkInOutDateInfoBo.getCheckOutDate(), DateUtil.DF_YMD_HMS));
            orderInfo.setRoomNight(hoHotelApplyDetail.getRoomNight());

            List<HotelModifyQueryResponse.roomInfo> roomInfoList =
                this.getDatesBetween(checkInOutDateInfoBo.getCheckInDate(),
                    DateUtil.addDays(checkInOutDateInfoBo.getCheckOutDate(), -1)).stream().map(date -> {
                        HotelModifyQueryResponse.roomInfo roomInfo = new HotelModifyQueryResponse.roomInfo();
                        roomInfo.setDate(date);
                        roomInfo.setQuantity(1);
                        roomInfo.setRoomInfoList(passengerList.stream().map(passenger -> {
                            HotelModifyQueryResponse.SingleRoomInfo singleRoomInfo =
                                new HotelModifyQueryResponse.SingleRoomInfo();
                            singleRoomInfo.setName(passenger.getPassengerName());
                            singleRoomInfo.setRoomIndex(passenger.getRoomIndex());
                            return singleRoomInfo;
                        }).collect(Collectors.toList()));
                        return roomInfo;
                    }).collect(Collectors.toList());
            orderInfo.setRoomInfoList(roomInfoList);
            return orderInfo;
        }).collect(Collectors.toList());
    }

    private HotelModifyQueryResponse.StatusItem buildStatusItem(HoHotelApplyStatusRecord statusRecord) {
        HotelModifyQueryResponse.StatusItem item = new HotelModifyQueryResponse.StatusItem();
        item.setStatus(statusRecord.getStatus());
        item.setStatusTime(DateUtil.dateToString(statusRecord.getHandleDate(), DateUtil.DF_YMD_HMS));
        item.setStatusSortIndex(statusRecord.getViewSort());
        return item;
    }

    private HotelModifyQueryResponse
    buildInAdvanceCancelOrderListQueryResponse(List<HotelModifyQueryResponse.HotelModifyDetail> detailList) {
        List<HotelModifyQueryResponse.HotelModifyDetail> sortedList = detailList.stream()
                .sorted(Comparator.comparing(HotelModifyQueryResponse.HotelModifyDetail::getApplyTime).reversed())
                .collect(Collectors.toList());
        HotelModifyQueryResponse hotelModifyQueryResponse = new HotelModifyQueryResponse();
        hotelModifyQueryResponse.setApplyList(sortedList);
        HotelModifyQueryResponse.CanApplyDate canApplyDate = new HotelModifyQueryResponse.CanApplyDate();
        List<HotelModifyQueryResponse.HotelModifyDetail> successedList = sortedList.stream()
                .filter(sortedItem -> !Lists.newArrayList(HotelModifyStatusEnum.FAILED.getCode(), HotelModifyStatusEnum.CANCELED.getCode()).contains(sortedItem.getApplyStatus()))
                .sorted(Comparator.comparing(HotelModifyQueryResponse.HotelModifyDetail::getApplyTime).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(successedList)) {
            canApplyDate.setCanModifyDate(successedList.get(0).getNowOrderInfo().getRoomInfoList().stream()
                    .map(HotelModifyQueryResponse.roomInfo::getDate).collect(Collectors.toList()));
        }
        hotelModifyQueryResponse.setCanApplyDate(canApplyDate);
        return hotelModifyQueryResponse;
    }


    public List<String> getDatesBetween(Date startDate, Date endDate) {
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DF_YMD, Locale.getDefault());
        List<String> dates = new ArrayList<>();
        for (int i = 0; i <= java.time.temporal.ChronoUnit.DAYS.between(startLocalDate, endLocalDate); i++) {
            LocalDate date = startLocalDate.plusDays(i);
            String dateString = date.format(formatter);
            dates.add(dateString);
        }
        return dates;
    }

    public Boolean updateHotelApplyRefundAmountByApplyId(UpdateHotelApplyRequest request) {
        Long orderId = request.getOrderId();
        List<HoHotelApply> hoHotelApplyList = hoHotelApplyLoader.select(orderId);

        Map<String, BigDecimal> applyRefundAmountMap = request.getApplyRefundAmountMap();
        hoHotelApplyList.forEach(hoHotelApply -> {
            BigDecimal refundAmount = applyRefundAmountMap.get(hoHotelApply.getApplyId());
            if (Objects.nonNull(refundAmount)) {
                hoHotelApply.setRefundAmount(refundAmount);
            }
        });
        Integer updateRow = hoHotelApplyLoader.updateRefundAmountBatch(hoHotelApplyList);
        return updateRow > 0;
    }

    public HotelModifiableRoomNightQueryResponse modifiableRoomNightQuery(HotelModifiableRoomNightQueryRequest request) {

        HoOrder hoOrder = hoOrderLoader.selectByOrderId(request.getOrderId());
        Optional.ofNullable(hoOrder).orElseThrow(() -> new CorpBusinessException(HotelResponseCodeEnum.ORDER_IS_NULL));

        HoHotel hoHotel = hoHotelLoader.selectByOrderId(request.getOrderId());
        LocalTime earlyArrivalTime = Optional.ofNullable(hoHotel.getEarlyArrivalTime()).map(Date::toInstant).map(instant -> instant.atZone(ZoneId.systemDefault()))
                .map(ZonedDateTime::toLocalTime).orElse(LocalTime.of(14, 0,0));

        HoRoom hoRoom = hoRoomLoader.selectByOrderId(request.getOrderId());

        HotelModifiableRoomNightQueryResponse response = new HotelModifiableRoomNightQueryResponse();
        List<HotelModifiableRoomNightQueryResponse.ModificationDateInfo> modificationDateInfoList = convert2ModificationDateList(hoOrder);
        response.setModificationDateInfoList(modificationDateInfoList);
        response.setMinConsecutiveDays(hoRoom.getMinConsecutiveDays());

        //可修改间夜 携程已接入供应商可修改间夜查询 其他供应商需根据申请单状态判断

        //携程
        if (commonApollo.isCtrip(hoOrder.getSupplierCode())) {
            response.setOnlyCheckOutEarly(Boolean.FALSE);
            return getCtripHotelModifiableRoomNightQueryResponse(hoOrder, response, earlyArrivalTime);
        }

        //其他供应商
        response.setOnlyCheckOutEarly(Boolean.TRUE);
        return getHotelModifiableRoomNightQueryResponse(request, response, earlyArrivalTime);
    }



    /**
     * 返回原单所有间夜日期 最后一天不算日期不算间夜
     *
     * @param hoOrder 订单
     */
    private List<HotelModifiableRoomNightQueryResponse.ModificationDateInfo> convert2ModificationDateList(HoOrder hoOrder) {
        List<String> orderAllRoomNightDateList = getDatesBetween(hoOrder.getCheckInTime(), DateUtil.addDays(hoOrder.getCheckOutTime(), -1));
        return orderAllRoomNightDateList.stream().map(roomNightDate -> {
            HotelModifiableRoomNightQueryResponse.ModificationDateInfo modificationDateInfo = new HotelModifiableRoomNightQueryResponse.ModificationDateInfo();
            modificationDateInfo.setDate(roomNightDate);
            modificationDateInfo.setModifiable(Boolean.FALSE);
            return modificationDateInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取酒店可修改房晚 - 携程
     *
     * @param hoOrder     订单
     * @param response    响应
     * @param earlyArrivalTime 最早入住时间
     * @return {@link HotelModifiableRoomNightQueryResponse }
     */
    private HotelModifiableRoomNightQueryResponse getCtripHotelModifiableRoomNightQueryResponse(HoOrder hoOrder, HotelModifiableRoomNightQueryResponse response, LocalTime earlyArrivalTime) {
        StandardOrderModifiableRoomNightQueryRequest standardOrderModifiableRoomNightQueryRequest = new StandardOrderModifiableRoomNightQueryRequest();
        standardOrderModifiableRoomNightQueryRequest.setOrderID(hoOrder.getSupplierOrderId());
        standardOrderModifiableRoomNightQueryRequest.setSupplierCode(hoOrder.getSupplierCode());
        StandardOrderModifiableRoomNightQueryResponse standardOrderModifiableRoomNightQueryResponse = supplierSoaClient.queryOrderModifiableRoomNight(standardOrderModifiableRoomNightQueryRequest);
        if (Objects.isNull(standardOrderModifiableRoomNightQueryResponse) || CollectionUtils.isEmpty(standardOrderModifiableRoomNightQueryResponse.getRoomNightInfoList())) {
            return response;
        }
        Date nowDate = DateUtils.getDayBegin();
        List<StandardOrderModifiableRoomNightQueryResponse.RoomNightInfo> roomNightInfoList = standardOrderModifiableRoomNightQueryResponse.getRoomNightInfoList();
        List<HotelModifiableRoomNightQueryResponse.ModificationDateInfo> modificationDateInfoList = response.getModificationDateInfoList();
        modificationDateInfoList.forEach(modificationDateInfo -> {
            modificationDateInfo.setModifiable(roomNightInfoList.stream().anyMatch(roomNightInfo -> roomNightInfo.getDate().equals(modificationDateInfo.getDate())));
            Date modificationDate = DateUtils.parse(modificationDateInfo.getDate(), DateUtils.DATE_FORMAT);
            modificationDateInfo.setExpire(nowDate.equals(modificationDate) ? checkInTimePassed(earlyArrivalTime) : nowDate.after(modificationDate));
            List<Date> newestDateList = getNewestDateList(hoOrder.getNewestCheckInOutDate());
            modificationDateInfo.setAlreadyCancel(!newestDateList.contains(modificationDate));
        });
        return response;
    }

    /**
     * 获取酒店可修改房晚 - 其他供应商
     *
     * @param request          请求
     * @param response         响应
     * @param earlyArrivalTime 最早入住时间
     * @return {@link HotelModifiableRoomNightQueryResponse }
     */
    private HotelModifiableRoomNightQueryResponse getHotelModifiableRoomNightQueryResponse(HotelModifiableRoomNightQueryRequest request, HotelModifiableRoomNightQueryResponse response, LocalTime earlyArrivalTime) {
        List<HotelModifiableRoomNightQueryResponse.ModificationDateInfo> modificationDateInfoList = response.getModificationDateInfoList();

        List<HoHotelApply> hoHotelApplyList = hoHotelApplyLoader.select(request.getOrderId());
        boolean notExistApply = CollectionUtils.isEmpty(hoHotelApplyList);
        boolean notExistSuccessApply = hoHotelApplyList.stream().noneMatch(apply -> HotelModifyStatusEnum.SUCCESS.getCode().equals(apply.getStatus()));
        if (notExistApply || notExistSuccessApply) {
            LocalDate nowDate = LocalDate.now();
            modificationDateInfoList.forEach(modificationDateInfo -> {
                LocalDate modificationDate = LocalDate.parse(modificationDateInfo.getDate());
                modificationDateInfo.setModifiable(nowDate.equals(modificationDate) ? !checkInTimePassed(earlyArrivalTime) : nowDate.isBefore(modificationDate));
                modificationDateInfo.setExpire(nowDate.equals(modificationDate) ? checkInTimePassed(earlyArrivalTime) : nowDate.isAfter(modificationDate));
                modificationDateInfo.setAlreadyCancel(Boolean.FALSE);
            });
            return response;
        }


        Optional<HoHotelApply> newestSuccessApplyOpt = hoHotelApplyList.stream()
                .filter(apply -> HotelModifyStatusEnum.SUCCESS.getCode().equals(apply.getStatus())).max(Comparator.comparing(HoHotelApply::getDatachangeCreatetime));
        if (newestSuccessApplyOpt.isPresent()) {
            LocalDate nowDate = LocalDate.now();
            List<HoHotelApplyDetail> hotelApplyDetailList = hoHotelApplyDetailLoader.select(newestSuccessApplyOpt.get().getApplyId());
            hotelApplyDetailList.stream().filter(hoHotelApplyDetail -> BooleanUtils.isTrue(hoHotelApplyDetail.getAfterRecord())).findFirst().ifPresent(
                    hoHotelApplyDetail -> {
                        //最新成功修改单的离店日期 - 1 才是可修改的最后一天
                        LocalDate newestCheckOutDate = hoHotelApplyDetail.getCheckOutDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        LocalDate modifiableLastDate = newestCheckOutDate.plusDays(-1);
                        modificationDateInfoList.forEach(modificationDateInfo -> {
                            LocalDate modificationDate = LocalDate.parse(modificationDateInfo.getDate());
                            modificationDateInfo.setModifiable(nowDate.equals(modificationDate) ? !checkInTimePassed(earlyArrivalTime) : nowDate.isBefore(modificationDate));
                            modificationDateInfo.setExpire(nowDate.equals(modificationDate) ? checkInTimePassed(earlyArrivalTime) : nowDate.isAfter(modificationDate));
                            modificationDateInfo.setAlreadyCancel(modificationDate.isAfter(modifiableLastDate));
                        });
                    }
            );
        }
        return response;
    }

    private Boolean checkInTimePassed(LocalTime earlyArrivalTime) {
        LocalTime now = LocalTime.now();
        return now.isAfter(earlyArrivalTime);
    }

    private List<Date> getNewestDateList(String newestCheckInOutDateStr) {
        if (StringUtils.isBlank(newestCheckInOutDateStr)) {
            return Collections.emptyList();
        }
        List<CheckInOutDateInfoBo> checkInOutDateInfoBoList =
            JsonUtils.parseArray(newestCheckInOutDateStr, CheckInOutDateInfoBo.class);

        return checkInOutDateInfoBoList.stream()
            .filter(item -> Objects.nonNull(item.getCheckInDate()) && Objects.nonNull(item.getCheckOutDate()))
            .flatMap(item -> getRoomNightBetween(item.getCheckInDate(), item.getCheckOutDate()).stream())
            .map(item -> DateUtils.parse(item, DateUtils.DATE_FORMAT)).collect(Collectors.toList());
    }

    private List<String> getRoomNightBetween(Date startDate, Date endDate) {
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DF_YMD, Locale.getDefault());
        List<String> dates = new ArrayList<>();
        for (int i = 0; i < java.time.temporal.ChronoUnit.DAYS.between(startLocalDate, endLocalDate); i++) {
            LocalDate date = startLocalDate.plusDays(i);
            String dateString = date.format(formatter);
            dates.add(dateString);
        }
        return dates;
    }

}
