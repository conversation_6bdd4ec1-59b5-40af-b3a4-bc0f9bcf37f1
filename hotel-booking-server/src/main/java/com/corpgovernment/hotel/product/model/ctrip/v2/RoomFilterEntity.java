package com.corpgovernment.hotel.product.model.ctrip.v2;

import lombok.Data;

import java.io.Serializable;

/**
 * 房型过滤条件
 *
 * <AUTHOR>
 */
@Data
public class RoomFilterEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 是否立即确认
	 */
	private Boolean justifyConfirm;

	/**
	 * 含早餐
	 */
	private Boolean hasbreakfast;

	/**
	 * 是否免费取消房型
	 */
	private Boolean freeCancel;
	/**
	 * 出有可积分房型的酒店
	 */
	private Boolean onlyBonusPoint;
}
