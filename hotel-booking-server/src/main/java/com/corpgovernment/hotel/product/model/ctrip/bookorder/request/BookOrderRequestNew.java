package com.corpgovernment.hotel.product.model.ctrip.bookorder.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BookOrderRequestNew {
    private BaseInfo baseInfo;
    private RoomInfo roomInfo;
    private List<Client> clientList;
    private ContactorInfo contactorInfo;
    private PriceInfo priceInfo;
    private CorpOrderInfo corpOrderInfo;
    private List<InvoiceInfo> invoiceInfoList;
    private DeliveryInfo deliveryInfo;

    @Data
    public static class BaseInfo {
        private String corpID;
        private String uID;
        private String wsID;
        private String platformOrderID;
        /**
         * 订单来源（Agent代表线下预订）
         */
        private String platformSource;
    }

    @Data
    public static class RoomInfo {
        private String hotelID;
        private String cityID;
        private String checkInDate;
        private String checkOutDate;
        private String roomID;
        private Integer roomQuantity;
        private Integer guestPerson;
        private String lastCancelTime;
        private String lastArrivalTime;
        private String earlyArrivalTime;
    }

    @Data
    public static class Client {
        private String name;
    }

    @Data
    public static class ContactorInfo {
        private String name;
        private String email;
        private String mobilePhone;
        private String mobilePhoneCountryCode;

    }

    @Data
    public static class PriceInfo {
        private BigDecimal amountCny;
        private List<ServiceFeeInfo> serviceFeeList;
    }

    @Data
    public static class ServiceFeeInfo {
        /**
         * 类型（F前收-体现在预订过程中，用户支付费用、B后收-不体现在预订过程中仅落库，后续结算使用）
         */
        private String type;
        /**
         * 价格
         */
        private BigDecimal price;
        /**
         * 日期（yyyy-MM-dd）
         */
        private String effectDate;
    }

    @Data
    public static class CorpOrderInfo {
        private String prepayType;
    }

    @Data
    public static class InvoiceInfo {
        private String accountBank;
        private String accountCardNo;
        private String corporationAddress;
        private String corporationTel;
        private String invoiceTitle;
        private String invoiceTitleType;
        private String invoiceContent;
        private String taxpayerNumber;
        private String invoiceType;
        private String invoiceEmail;
    }

    @Data
    public static class DeliveryInfo {
        private String deliveryType;
        private DeliveryAddress deliveryAddress;
    }

    @Data
    public static class DeliveryAddress {
        private String address;
        private String city;
        private String deliveryFee;
        private String district;
        private String mobilePhone;
        private String postCode;
        private String province;
        private String receiverName;
    }
}
