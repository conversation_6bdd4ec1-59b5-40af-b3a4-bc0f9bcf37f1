package com.corpgovernment.hotel.product.mapper;


import com.corpgovernment.hotel.product.common.mybatis.TkMapper;
import com.corpgovernment.hotel.product.entity.db.HoHotelApply;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 
 * HoHotelApplyMapper数据库操作接口类
 * 
 **/
public interface HoHotelApplyMapper extends TkMapper<HoHotelApply> {

    Integer updateRefundAmountBatch(List<HoHotelApply> list);

    List<HoHotelApply> listSuccessByOrderIds(@Param("orderIds") Collection<Long> orderIds);

    List<HoHotelApply> listNoFailByOrderIds(@Param("orderIds") Collection<Long> orderIds);

}
