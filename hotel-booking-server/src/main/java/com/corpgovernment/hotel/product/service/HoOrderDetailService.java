package com.corpgovernment.hotel.product.service;

import cn.hutool.core.bean.BeanUtil;
import com.corpgovernment.api.hotel.product.model.BusinessFileInfoVo;
import com.corpgovernment.api.hotel.product.model.enums.InvoiceTypeEnum;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.hotel.product.model.request.QueryOrderDetailRequestBo;
import com.corpgovernment.api.hotel.product.model.request.ReCreateInvoiceRequestBo;
import com.corpgovernment.api.hotel.product.model.response.*;
import com.corpgovernment.api.hotel.product.model.response.HoHotelApplyBo;
import com.corpgovernment.api.hotel.product.model.response.HoHotelMemberBo;
import com.corpgovernment.api.hotel.product.model.response.HoOrderBo;
import com.corpgovernment.api.hotel.product.model.response.HoOrderCancelRuleBo;
import com.corpgovernment.api.hotel.product.model.response.HoPassengerBo;
import com.corpgovernment.api.hotel.product.model.response.HoRoomDailyBo;
import com.corpgovernment.api.hotel.product.model.response.OrderDetailResponseBo;
import com.corpgovernment.api.order.common.enums.DataStorageTypeEnum;
import com.corpgovernment.api.ordercenter.dto.OcUtils;
import com.corpgovernment.api.ordercenter.dto.request.file.GetBusinessFileRequest;
import com.corpgovernment.api.ordercenter.dto.request.invoicing.SaveInvoicingDetailRequest;
import com.corpgovernment.api.ordercenter.dto.response.file.GetBusinessFileResponse;
import com.corpgovernment.api.ordercenter.enums.BusinessTypeEnum;
import com.corpgovernment.api.ordercenter.enums.ProductEnum;
import com.corpgovernment.api.ordercenter.soa.IIssueInvoiceClient;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.hotel.product.dataloader.db.*;
import com.corpgovernment.hotel.product.dataloader.soa.BusinessFileClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.OssServiceClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.ServiceSatisfactionCommentClientLoader;
import com.corpgovernment.hotel.product.entity.db.*;
import com.corpgovernment.hotel.product.mq.OrderStatusProducer;
import com.corpgovernment.hotel.product.producer.SendMessageMQProducer;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HoOrderDetailService {
	@Autowired
	private HoOrderLoader hoOrderLoader;
	@Autowired
	private HoHotelLoader hoHotelLoader;
	@Autowired
	private HoRoomLoader hoRoomLoader;
	@Autowired
	private HoRoomDailyInfoLoader hoRoomDailyInfoLoader;
	@Autowired
	private HoDeliveryInfoLoader hoDeliveryInfoLoader;
	@Autowired
	private HoInvoiceLoader hoInvoiceLoader;
	@Autowired
	private HoPassengerLoader hoPassengerLoader;
	@Autowired
	private HoHotelLowLoader hoHotelLowLoader;
	@Autowired
	private HoOrderCancelRuleLoader hoOrderCancelRuleLoader;
	@Autowired
	private IIssueInvoiceClient issueInvoiceService;
	@Autowired
	private ServiceSatisfactionCommentClientLoader serviceSatisfactionCommentClientLoader;
	@Autowired
	private SendMessageMQProducer sendMessageMQProducer;
    @Autowired
    private HoHotelApplyLoader hoHotelApplyLoader;
    @Autowired
    private HoHotelApplyDetailLoader hoHotelApplyDetailLoader;
    @Autowired
    private MessageService messageService;
    @Autowired
    private BusinessFileClientLoader businessFileClientLoader;
    @Autowired
    private OssServiceClientLoader ossServiceClientLoader;
    @Autowired
    private HoHotelMemberLoader hoHotelMemberLoader;

    @Autowired
    private OrderStatusProducer orderStatusProducer;

    @Autowired
    @Qualifier(value = "basicThreadPoolExecutor")
    private ThreadPoolExecutor basicThreadPoolExecutor;

    @Autowired
    private HoDataStorageLoader hoDataStorageLoader;

    @Autowired
    private HoChummageLoader chummageLoader;

    @Autowired
    private OrderSnapshotDataLoader orderSnapshotDataLoader;

	public OrderDetailResponseBo queryOrderDetail(QueryOrderDetailRequestBo queryOrderDetailRequestBo) {
		Long orderId = queryOrderDetailRequestBo.getOrderId();
		OrderDetailResponseBo orderDetailResponseBo = new OrderDetailResponseBo();
		//查询订单详情
		HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        log.info("查询 ho_order 表数据:{}", JsonUtils.toJsonString(hoOrder));
		if (hoOrder == null) {
            orderDetailResponseBo.setErrorCode("-1");
            orderDetailResponseBo.setMessage("订单不存在");
            return orderDetailResponseBo;
        }
        //查询酒店详情
		HoHotel hoHotelInfo = hoHotelLoader.selectByOrderId(orderId);
        //查询房间信息
        HoRoom hoRoomInfo = hoRoomLoader.selectByOrderId(orderId);
        //查询入住人信息
        List<HoPassenger> hoPassengerInfoList = hoPassengerLoader.selectByOrderId(orderId);
        if (CollectionUtils.isEmpty(hoPassengerInfoList)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.GUEST_INFORMATION_IS_EMPTY);
        }

        // 查询多成本
        Set<String> multiCostCenterDataIds =
                hoPassengerInfoList.stream().map(HoPassenger::getMultiCostCenterDataId).filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
        List<HoDataStorage> multiCostCenterList = hoDataStorageLoader
                .getDataStorageByDataIdList(multiCostCenterDataIds, DataStorageTypeEnum.MULTI_COST_CENTER);

        // 查询多成本
        Set<String> accountingUnitDataIds =
                hoPassengerInfoList.stream().map(HoPassenger::getAccountingUnitDataId).filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
        List<HoDataStorage> accountingUnitList = hoDataStorageLoader
                .getDataStorageByDataIdList(accountingUnitDataIds, DataStorageTypeEnum.ACCOUNTING_UNIT);

        //查询房间每日价格信息
        List<HoRoomDailyInfo> hoRoomDailyInfoList = hoRoomDailyInfoLoader.selectByOrderId(orderId);
        if (CollectionUtils.isEmpty(hoRoomDailyInfoList)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.DAILY_ROOM_RATE_IS_EMPTY);
        }
        //查询配送信息
		HoDeliveryInfo hoDeliveryInfo = hoDeliveryInfoLoader.selectByOrderId(orderId);
        //查询rc
		HoHotelLow hoHotelLow = hoHotelLowLoader.selectByOrderId(orderId);

        //查询发票信息
		HoInvoice hoInvoiceInfo = hoInvoiceLoader.selectByOrderId(orderId);

        //查询取消规则
		List<HoOrderCancelRule> orderCancelRuleList = hoOrderCancelRuleLoader.selectByOrderId(orderId);

        //查询快照数据
        List<OrderSnapshotData> orderSnapshotDataList = orderSnapshotDataLoader.listByBusinessId(String.valueOf(orderId));

        orderDetailResponseBo.setOrderInfo(hoOrder.convertHoOrderBo());
        orderDetailResponseBo.setHoHotelInfo(hoHotelInfo.convertHoHotelBo());
        orderDetailResponseBo.setHoRoomInfo(hoRoomInfo.convertHoRoomBo());
        if (hoDeliveryInfo != null) {
            orderDetailResponseBo.setHoDeliveryInfoBo(hoDeliveryInfo.converHoDeliveryInfoBo());
        }
        if (hoInvoiceInfo != null) {
            orderDetailResponseBo.setHoInvoiceInfo(hoInvoiceInfo.convertHoInvoiceBo());
        }
        List<HoPassengerBo> hoPassengerBoList = new ArrayList<>();
        List<HoRoomDailyBo> hoRoomDailyBoList = new ArrayList<>();
        Map<String, String> multiCostCenterMap = multiCostCenterList.stream()
                .collect(Collectors.toMap(HoDataStorage::getDataId, HoDataStorage::getDataContent, (v1, v2) -> v1));
        Map<String, String> accountingUnitMap = accountingUnitList.stream()
                .collect(Collectors.toMap(HoDataStorage::getDataId, HoDataStorage::getDataContent, (v1, v2) -> v1));
        for (HoPassenger hoPassenger : hoPassengerInfoList) {
            HoPassengerBo hoPassengerBo = hoPassenger.convertHoPassengerBo();
            hoPassengerBo.setMultiCostCenterDataJSON(multiCostCenterMap.get(hoPassenger.getMultiCostCenterDataId()));
            hoPassengerBo.setAccountingUnitJSON(accountingUnitMap.get(hoPassenger.getAccountingUnitDataId()));
            hoPassengerBoList.add(hoPassengerBo);
        }

        for (HoRoomDailyInfo hoRoomDailyInfo : hoRoomDailyInfoList) {
            if(StringUtils.isBlank(hoRoomDailyInfo.getBreakfastName())){
                hoRoomDailyInfo.setBreakfastName("无餐食");
            }
            hoRoomDailyBoList.add(hoRoomDailyInfo.convertHoRoomDailyBo());
        }
        orderDetailResponseBo.setHoPassengerList(hoPassengerBoList);
        orderDetailResponseBo.setHoRoomDailyInfoList(hoRoomDailyBoList);
        if (hoHotelLow != null) {
            orderDetailResponseBo.setHoHotelLow(hoHotelLow.converHoHotelBo());
        }
        if (CollectionUtils.isNotEmpty(orderCancelRuleList)) {
            List<HoOrderCancelRuleBo> hoOrderCancelRuleBoList = orderCancelRuleList.stream().map(item -> {
                HoOrderCancelRuleBo hoOrderCancelRuleBo = new HoOrderCancelRuleBo();
                BeanUtils.copyProperties(item, hoOrderCancelRuleBo);
                return hoOrderCancelRuleBo;
            }).collect(Collectors.toList());
            orderDetailResponseBo.setHoOrderCancelRuleBoList(hoOrderCancelRuleBoList);
        }

        if (CollectionUtils.isNotEmpty(orderSnapshotDataList)) {
            List<OrderSnapshotDataBo> orderSnapshotDataBoList = orderSnapshotDataList.stream().map(item -> {
                OrderSnapshotDataBo orderSnapshotDataBo = new OrderSnapshotDataBo();
                orderSnapshotDataBo.setDataType(item.getDataType());
                orderSnapshotDataBo.setDataContent(item.getDataContent());
                return orderSnapshotDataBo;
            }).collect(Collectors.toList());
            orderDetailResponseBo.setOrderSnapshotDataBoList(orderSnapshotDataBoList);
        }

        List<HoHotelApply> hoHotelApplyList = hoHotelApplyLoader.select(hoOrder.getOrderId());
        if (CollectionUtils.isNotEmpty(hoHotelApplyList)) {
            orderDetailResponseBo.setHoHotelApplyBoList(hoHotelApplyList.stream().map(apply -> {
                HoHotelApplyBo hoHotelApplyBo = new HoHotelApplyBo();
                hoHotelApplyBo.setApplyId(apply.getApplyId());
                hoHotelApplyBo.setStatus(apply.getStatus());
                hoHotelApplyBo.setOrderId(apply.getOrderId());
                hoHotelApplyBo.setReasonCode(apply.getReasonCode());
                hoHotelApplyBo.setReasonDesc(apply.getReasonDesc());
                hoHotelApplyBo.setApplyTime(DateUtil.dateToString(apply.getDatachangeCreatetime(), DateUtil.DF_YMD_HMS));
                List<HoHotelApplyDetail> applyDetailList = hoHotelApplyDetailLoader.select(apply.getApplyId());
                if (CollectionUtils.isNotEmpty(applyDetailList)){
                    hoHotelApplyBo.setHoHotelApplyDetailBoList(applyDetailList.stream().map(applyDetail ->{
                        HoHotelApplyDetailBo hoHotelApplyDetailBo = new HoHotelApplyDetailBo();
                        hoHotelApplyDetailBo.setApplyId(applyDetail.getApplyId());
                        hoHotelApplyDetailBo.setCheckInDate(DateUtil.dateToString(applyDetail.getCheckInDate(), DateUtil.DF_YMD));
                        hoHotelApplyDetailBo.setCheckOutDate(DateUtil.dateToString(applyDetail.getCheckOutDate(), DateUtil.DF_YMD));
                        hoHotelApplyDetailBo.setAfterRecord(applyDetail.getAfterRecord());
                        return hoHotelApplyDetailBo;
                    }).collect(Collectors.toList()));
                }
                return hoHotelApplyBo;
            }).collect(Collectors.toList()));
        }

        // 文件上传
        orderDetailResponseBo.setFileList(toFileList(orderId));

        //酒店会员信息
        HoHotelMember hoHotelMember = hoHotelMemberLoader.selectByOrderId(orderId);
        orderDetailResponseBo.setHoHotelMemberBo(toHoHotelMemberBo(hoHotelMember));

        //查询酒店合住信息
        HoChummage hoChummage = chummageLoader.selectByOrderId(orderId);
        orderDetailResponseBo.setChummageInfoBo(toHoChummageInfoBo(hoChummage));
        return orderDetailResponseBo;
    }

    private HoChummageInfoBo toHoChummageInfoBo(HoChummage hoChummage) {
        if(Objects.isNull(hoChummage)){
            return null;
        }
        return BeanUtil.toBean(hoChummage, HoChummageInfoBo.class);
    }

    /**
     * 批量查询订单详情 (异步)
     *
     * @param requests
     * @return
     */
    public List<OrderDetailResponseBo> queryOrderDetailList(List<QueryOrderDetailRequestBo> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return Collections.emptyList();
        }
        List<CompletableFuture<OrderDetailResponseBo>> futures = requests.stream()
            .map(request -> CompletableFuture.supplyAsync(() -> {
                try {
                    return this.queryOrderDetail(request);
                } catch (Exception e) {
                    log.error("批量查询订单详情，出现异常，request: " + JsonUtils.toJsonString(e), e);
                    // 异常时返回null
                    return null;
                }
            }, basicThreadPoolExecutor))
            .collect(Collectors.toList());

        // 等待所有异步操作完成，并收集非null的结果
        return futures.stream()
            .map(CompletableFuture::join)
            .filter(
                Objects::nonNull)
            .collect(Collectors.toList());
    }

    private HoHotelMemberBo toHoHotelMemberBo(HoHotelMember hoHotelMember){
        if (null == hoHotelMember){
            return null;
        }
        HoHotelMemberBo hoHotelMemberBo = new HoHotelMemberBo();
        hoHotelMemberBo.setOrderId(hoHotelMember.getOrderId());
        hoHotelMemberBo.setMemberCardholder(hoHotelMember.getMemberCardholder());
        hoHotelMemberBo.setMemberCardNo(hoHotelMember.getMemberCardNo());
        hoHotelMemberBo.setMemberRuleDesc(hoHotelMember.getMemberRuleDesc());
        hoHotelMemberBo.setGroupId(hoHotelMember.getGroupId());
        hoHotelMemberBo.setGroupName(hoHotelMember.getGroupName());
        hoHotelMemberBo.setEnabledBonusPoint(hoHotelMember.getEnabledBonusPoint());
        hoHotelMemberBo.setBonusPointCode(hoHotelMember.getBonusPointCode());
        hoHotelMemberBo.setBonusPointType(hoHotelMember.getBonusPointType());
        return hoHotelMemberBo;
    }

    @Transactional(rollbackFor = Exception.class)
    public JSONResult reCreateInvoice(ReCreateInvoiceRequestBo reCreateInvoiceRequestBo) {
		//发票状态 1无效 0 有效
		HoOrder orderInfo = hoOrderLoader.selectByOrderId(reCreateInvoiceRequestBo.getOrderId());
		hoInvoiceLoader.updateStatus(reCreateInvoiceRequestBo.getOrderId(), 1);
		//创建订单
		HoInvoice hoInvoice = new HoInvoice();
		BeanUtils.copyProperties(reCreateInvoiceRequestBo, hoInvoice);
		hoInvoice.setStatus(0);
		hoInvoice.setInvoiceType(String.valueOf(reCreateInvoiceRequestBo.getInvoiceType()));
		hoInvoice.setInvoiceContent(reCreateInvoiceRequestBo.getInvoiceContent());
		hoInvoice.setLadderCancel(0);
		hoInvoice.setInvoiceAmount(orderInfo.getAmount().subtract(orderInfo.getLadderAmount() == null ? BigDecimal.ZERO : orderInfo.getLadderAmount()));
		if (OrderStatusEnum.CA.getCode().equals(orderInfo.getOrderStatus())) {
			hoInvoice.setLadderCancel(1);
		}

		hoInvoiceLoader.insertSelective(hoInvoice);
		hoOrderLoader.updateContactEmail(reCreateInvoiceRequestBo.getOrderId(), reCreateInvoiceRequestBo.getEmail());
		SaveInvoicingDetailRequest saveInvoicingDetailRequest = new SaveInvoicingDetailRequest();
		saveInvoicingDetailRequest.setAccountBank(reCreateInvoiceRequestBo.getAccountBank());
		saveInvoicingDetailRequest.setAccountCardNo(reCreateInvoiceRequestBo.getAccountCardNo());
		saveInvoicingDetailRequest.setCompany("航天集团");
		saveInvoicingDetailRequest.setInvoiceAmount((orderInfo.getAmount() == null ? orderInfo.getLadderAmount() : orderInfo.getAmount()).toString());
		saveInvoicingDetailRequest.setEmail(reCreateInvoiceRequestBo.getEmail());
		saveInvoicingDetailRequest.setInvoiceTitle(reCreateInvoiceRequestBo.getInvoiceTitle());
		saveInvoicingDetailRequest.setInvoiceContent(reCreateInvoiceRequestBo.getInvoiceContent());
		saveInvoicingDetailRequest.setInvoiceType(reCreateInvoiceRequestBo.getInvoiceType());
		saveInvoicingDetailRequest.setIssueInvoiceType(0);//酒店
        saveInvoicingDetailRequest.setOrderNo(String.valueOf(orderInfo.getOrderId()));
        saveInvoicingDetailRequest.setOrderType(orderInfo.getCorpPayType());
        //调用创建发票
        JSONResult  jsonResult = issueInvoiceService.addIssuingInvoice(saveInvoicingDetailRequest);
        if(!jsonResult.isSUCCESS()){
          throw new CorpBusinessException(HotelResponseCodeEnum.EXCEPTION_INSERT_INVOICE);
        }
        return JSONResult.ok();
    }
    @Transactional(rollbackFor = Exception.class)
    public JSONResult autoReCreateInvoice(ReCreateInvoiceRequestBo reCreateInvoiceRequestBo) {
		//发票状态 1无效 0 有效
		HoOrder orderInfo = hoOrderLoader.selectByOrderId(reCreateInvoiceRequestBo.getOrderId());
		HoInvoice hoInvoiceInfo = hoInvoiceLoader.selectByOrderId(reCreateInvoiceRequestBo.getOrderId());
		if (hoInvoiceInfo != null) {
			hoInvoiceLoader.updateStatus(reCreateInvoiceRequestBo.getOrderId(), 1);
			//创建订单
			HoInvoice hoInvoice = new HoInvoice();
			hoInvoice.setOrderId(hoInvoiceInfo.getOrderId());
			hoInvoice.setStatus(0);
			hoInvoice.setLadderCancel(1);
			hoInvoice.setInvoiceType(InvoiceTypeEnum.S.getType());
			hoInvoice.setInvoiceTitle(hoInvoiceInfo.getInvoiceTitle());
			hoInvoice.setInvoiceType(hoInvoiceInfo.getInvoiceTitleType());
			hoInvoice.setTaxpayerNumber(hoInvoiceInfo.getTaxpayerNumber());
			hoInvoiceLoader.insertSelective(hoInvoice);
			hoOrderLoader.updateContactEmail(reCreateInvoiceRequestBo.getOrderId(), reCreateInvoiceRequestBo.getEmail());
			SaveInvoicingDetailRequest saveInvoicingDetailRequest = new SaveInvoicingDetailRequest();
			saveInvoicingDetailRequest.setCompany("航天集团");
			saveInvoicingDetailRequest.setInvoiceAmount(reCreateInvoiceRequestBo.getInvoiceAmount().toString());
			saveInvoicingDetailRequest.setEmail(reCreateInvoiceRequestBo.getEmail());
			saveInvoicingDetailRequest.setInvoiceTitle(hoInvoice.getInvoiceTitle());
			saveInvoicingDetailRequest.setInvoiceContent(hoInvoice.getInvoiceContent());
			saveInvoicingDetailRequest.setOrderNo(orderInfo.getOrderId().toString());
			saveInvoicingDetailRequest.setInvoiceType(hoInvoiceInfo.getInvoiceType() == null ? 1 : Integer.valueOf("2".equals(hoInvoiceInfo.getInvoiceType()) ? 1 : 0));
			saveInvoicingDetailRequest.setOrderType(orderInfo.getCorpPayType());
			saveInvoicingDetailRequest.setIssueInvoiceType(0);//酒店
           //调用创建发票
           JSONResult  jsonResult = issueInvoiceService.addIssuingInvoice(saveInvoicingDetailRequest);
           if(!jsonResult.isSUCCESS()){
               throw new CorpBusinessException(HotelResponseCodeEnum.EXCEPTION_INSERT_INVOICE);
           }
       }
        return JSONResult.ok();
    }

    public JSONResult saveSpecialNeed(HoOrderBo hoOrderBo) {
		hoOrderLoader.updateSpecialNeed(hoOrderBo.getOrderId(), hoOrderBo.getSpecialNeed());
        return JSONResult.ok();
    }


    private static Date getStartTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        todayStart.set(Calendar.MILLISECOND, 0);
        return todayStart.getTime();
    }

    private static Date getEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTime();
    }

    public JSONResult updateOrderCompleted() {
        //查询符合条件的订单
        List<Long> orderIds = hoRoomLoader.selectNeedToCompleted(new Date());
        log.info("需要会自动完成的订单id列表:{}", JsonUtils.toJsonString(orderIds));
        //更新
        if(CollectionUtils.isEmpty(orderIds)) {
            return JSONResult.ok();
        }

			hoOrderLoader.updateOrderCompleted(orderIds);

        orderIds.forEach(orderId -> orderStatusProducer
                .sendOrderStatusMsg(OcUtils.initOcReq(orderId, OrderStatusEnum.ED.getCode(), null, OrderTypeEnum.HN)));

            //发送订单评价邀请
            //查询订单信息
			/*List<HoOrder> orderList = hoOrderLoader.selectByOrderIds(orderIds);
			Map<Long, HoOrder> ordermap = orderList.stream().collect(Collectors.toMap(x -> x.getOrderId(), x -> x, (k1, k2) -> k1));
            //"开始校验是否已经有过评论"
            List<HoPassenger> hoPassengerList = hoPassengerLoader.selectByOrderIds(orderIds);
            log.info("订单评价邀请,出行人信息:{}",JsonUtils.toJsonString(hoPassengerList));

            Map<String,OrderInfoRequest>  requestMap = new HashMap<>();
            String key = null;
            for(HoOrder y:orderList) {
                OrderInfoRequest request = new OrderInfoRequest();
                request.setOrderId(String.valueOf(y.getOrderId()));
                request.setUid(y.getUid());
                request.setOrgId(y.getOrgId());
                request.setCountryCode(y.getContactCountryCode());
                request.setMobilePhone(y.getContactMobilePhone());
                key = String.format("%s%s%s", request.getOrderId(), request.getUid(), request.getOrgId());
                //酒店
                request.setOrderType("4");

                if(!requestMap.containsKey(key)){
                    requestMap.put(key,request);
                }
            }

            // 出行人集合不为空
            if (CollectionUtils.isNotEmpty(hoPassengerList)) {
              for(HoPassenger e:hoPassengerList) {
                    OrderInfoRequest request = new OrderInfoRequest();
                    request.setOrderId(String.valueOf(e.getOrderId()));
                    request.setUid(e.getUid());
                    //酒店
                    request.setOrderType("4");
                    request.setOrgId(e.getOrgId());
                    request.setCountryCode(e.getCountryCode());
                    request.setMobilePhone(e.getMobilePhone());
                    key = String.format("%s%s%s", request.getOrderId(), request.getUid(), request.getOrgId());

                  if(!requestMap.containsKey(key)){
                      requestMap.put(key,request);
                  }
                }
			}

            if(requestMap.isEmpty()){
                return JSONResult.ok();
            }

        List<OrderInfoRequest> requests = requestMap.values().stream()
                .map(orderInfoRequest -> {orderInfoRequest.setBizType(MsgBizType.HOTEL_COMMENT.getTemplateKey());return orderInfoRequest;})
                .collect(Collectors.toList());

        log.info("查询评价信息 req:{}",JsonUtils.toJsonString(requests));
            List<OrderInfoRequest> pushPass = serviceSatisfactionCommentClientLoader.batchComment(requests);
        log.info("查询评价信息 res:{}",JsonUtils.toJsonString(pushPass));
            // 效验成功的人员
            if (CollectionUtils.isEmpty(pushPass)){
                return JSONResult.ok();
            }

            MessageVo messageVo = null;
            MessageVo.AppPush appPush = null;
            MessageVo.Sms sms = null;
            HoOrder hoOrder = null;
            List<MessageVo> messageVoList =  new ArrayList<>();
            for(OrderInfoRequest orderInfoRequest :pushPass){
                if(orderInfoRequest.getHasSend()){
                    log.info("该订单邀请评价已经推送过，不再次推送:{}",JsonUtils.toJsonString(orderInfoRequest));
                    continue;
                }
                hoOrder = ordermap.get(Long.valueOf(orderInfoRequest.getOrderId()));
                messageVo = new MessageVo();
                appPush = new MessageVo.AppPush();
                sms = new MessageVo.Sms();
                Map<String,Object> param = new HashMap<>();
                param.put("orderId",orderInfoRequest.getOrderId());
                appPush.setVars(new HashMap<>(param));
                appPush.setBizId(orderInfoRequest.getOrderId());
                appPush.setBizType(MsgBizType.HOTEL_COMMENT);
                appPush.setOrgId(orderInfoRequest.getOrgId());
                appPush.setUid(orderInfoRequest.getUid());
                messageVo.setAppPush(appPush);
                sms.setVars(new HashMap<>(param));
                sms.setBizType(MsgBizType.HOTEL_COMMENT);
                sms.setCorpId(hoOrder.getCorpId());
                sms.setPhone(orderInfoRequest.getMobilePhone());
                sms.setCountryCode(orderInfoRequest.getCountryCode());
                messageVo.setSms(sms);
                MessageVo.StationMessage stationMessage = new MessageVo.StationMessage();
                BeanUtils.copyProperties(appPush,stationMessage);
                stationMessage.setVars(new HashMap<>(param));
                messageVo.setStationMessage(stationMessage);
                messageVoList.add(messageVo);
            }
            log.info("发送邀请评价通知 messageVoList:{}",JsonUtils.toJsonString(messageVoList));*/
			//messageVoList.forEach(sendMessageMQProducer::sendMsg);

            //新版短信
            //messageService.sendMessageForComment(pushPass, ordermap);

        return JSONResult.ok();
    }

    /**
     * 文件获取
     *
     * @param orderId
     * @return
     */
    private List<BusinessFileInfoVo> toFileList(Long orderId) {
        GetBusinessFileRequest request = new GetBusinessFileRequest();
        request.setBusinessId(String.valueOf(orderId));
        request.setBusinessType(BusinessTypeEnum.SUBMIT_ORDER.getCode());
        request.setProductType(ProductEnum.HOTEL.getCode());
        List<GetBusinessFileResponse> businessFileInfo = businessFileClientLoader.getBusinessFileInfo(request);
        if (CollectionUtils.isEmpty(businessFileInfo)) {
            return Collections.emptyList();
        }

        return businessFileInfo.stream().map(file -> BusinessFileInfoVo.builder()
                .fileName(file.getFileName())
                .filePath(file.getFilePath())
                .fileSize(file.getFileSize())
                .fileType(file.getFileType())
                .fileUrl(ossServiceClientLoader.getUploadSafeUrl(file.getFilePath()))
                .build())
                .collect(Collectors.toList());
    }
}
