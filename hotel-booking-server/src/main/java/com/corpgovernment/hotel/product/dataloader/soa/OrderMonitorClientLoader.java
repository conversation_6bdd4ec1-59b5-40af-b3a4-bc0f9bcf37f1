package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.ordercenter.soa.IOrderMonitorClient;
import com.corpgovernment.api.ordercenter.vo.OrderMonitorVo;
import com.corpgovernment.common.base.JSONResult;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderMonitorClientLoader {

    @Autowired
    private IOrderMonitorClient orderMonitorClient;

    /**
     * 查询酒店超时、异常订单
     *
     * @param orderId
     * @return
     */
    public OrderMonitorVo getMonitorInfo(Long orderId) {
        if (Objects.isNull(orderId)) {
            return null;
        }
        JSONResult<List<OrderMonitorVo>> result = orderMonitorClient.getHotelMonitorInfo(Lists.newArrayList(orderId));
        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            log.error("查询酒店超时、异常订单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
            return null;
        }
        return result.getData().get(0);
    }


    /**
     * 新增告警
     *
     * @param request
     * @return
     */
    public boolean save(OrderMonitorVo request) {
        if (request == null) {
            return false;
        }
        if (Objects.isNull(orderMonitorClient.save(request))) {
            log.error("新增告警异常:接口无响应");
            return false;
        }
        return true;
    }

}
