package com.corpgovernment.hotel.product.dataloader.db;

import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.hotel.booking.enums.HotelCancelReasonEnum;
import com.corpgovernment.hotel.product.dto.OrderInfoDTO;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.mapper.HoOrderMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.math.BigDecimal;
import java.util.*;

@Component
public class HoOrderLoader {

	@Autowired
	private HoOrderMapper orderMapper;

	public HoOrder selectByOrderId(Long orderId) {
		if (orderId == null) {
			return null;
		}
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		return orderMapper.selectOne(record);
	}

	public int deleteByOrderId(Long orderId) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setIsDeleted(true);
		return this.updateByOrderId(record);
	}

	public int updateByOrderId(HoOrder record) {
		Example example = new Example(HoOrder.class);
		example.createCriteria().andEqualTo("orderId", record.getOrderId());
		return orderMapper.updateByExampleSelective(record, example);
	}

	/**
	 * 更新订单状态（根据当前订单状态）
	 *
	 * @param orderId
	 * @param oldStatus
	 * @param newStatus
	 * @return
	 */
	public int updateOrderStatus(Long orderId, String oldStatus, String newStatus) {
		Example example = new Example(HoOrder.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("orderId", orderId);
		if (StringUtils.isNotBlank(oldStatus)) {
			criteria.andEqualTo("orderStatus", oldStatus);
		}
		HoOrder record = new HoOrder();
		record.setOrderStatus(newStatus);
		if (OrderStatusEnum.CA.getCode().equals(newStatus)){
			record.setCancelTime(new Date());
		}
		return orderMapper.updateByExampleSelective(record, example);
	}

	public int updateContactEmail(Long orderId, String contactEmail) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setContactEmail(contactEmail);
		return this.updateByOrderId(record);
	}

	public int updateSpecialNeed(Long orderId, String specialNeed) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setSpecialNeed(specialNeed);
		return this.updateByOrderId(record);
	}

	public List<HoOrder> selectByOrderIds(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return new ArrayList<>();
		}
		Example example = new Example(HoOrder.class);
		example.createCriteria().andIn("orderId", orderIds);
		return orderMapper.selectByExample(example);
	}

	public int updateOrderCompleted(List<Long> orderIds) {
		Example example = new Example(HoOrder.class);
		example.createCriteria()
			   .andIn("orderId", orderIds)
			   .andEqualTo("orderStatus", OrderStatusEnum.TA.getCode());
		HoOrder record = new HoOrder();
		record.setOrderStatus(OrderStatusEnum.ED.getCode());
		return orderMapper.updateByExampleSelective(record, example);

	}

	public int updateSupplierOrderId(Long orderId, String supplierOrderId) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setSupplierOrderId(supplierOrderId);
		return this.updateByOrderId(record);
	}

	public int updateLadderAmount(Long orderId, BigDecimal ladderAmount) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setLadderAmount(ladderAmount);
		return this.updateByOrderId(record);
	}

	public int updateOrderPayChannel(Long orderId, String payChannel) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setPayChannel(payChannel);
		return this.updateByOrderId(record);
	}

	public int updatePayNo(Long orderId, String payNo) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setPayNo(payNo);
		return this.updateByOrderId(record);
	}

	public int updateCancelTime(Long orderId) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setCancelTime(new Date());
		return this.updateByOrderId(record);
	}

	public List<HoOrder> selectByExample(Example example) {
		return orderMapper.selectByExample(example);
	}

	public int updateSubmitTime(Long orderId) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setSubmitTime(new Date());
		return this.updateByOrderId(record);
	}

	public int updateApprovingTime(Long orderId) {
		HoOrder record = new HoOrder();
		record.setOrderId(orderId);
		record.setApprovingTime(new Date());
		return this.updateByOrderId(record);
	}

	public int rollbackSubmitTime(Long orderId) {
		return orderMapper.rollbackSubmitTime(orderId);
	}

	public int insertSelective(HoOrder order) {
		return orderMapper.insertSelective(order);
	}

	public List<OrderInfoDTO> getHotelOrderListByOrderStatus(String startTime, String endTime, List<Long> orderIds) {
		return orderMapper.getHotelOrderListByOrderStatus(startTime, endTime, orderIds);
	}

	public List<HoOrder> getCompleteStatusOrder(String yesterdayYMD, String nowDayYMD) {
		return orderMapper.getCompleteStatusOrder(yesterdayYMD, nowDayYMD);
	}

	public List<HoOrder> selectByApplyNo(String applyNo) {
		Example example = new Example(HoOrder.class);
		example.createCriteria().andEqualTo("tripApplyNo", applyNo);
		return orderMapper.selectByExample(example);
	}

	public List<OrderInfoDTO> getHotelOrderList(List<Long> orderIdList) {
		return orderMapper.getHotelOrderList(orderIdList);
	}

	public boolean updateCancelReason(Long orderId, String orderStatus) {
		HoOrder orderRecord = new HoOrder();
		orderRecord.setOrderId(orderId);
		HotelCancelReasonEnum cancelReasonEnum = HotelCancelReasonEnum.getEnumByOrderStatus(orderStatus);
		if (null == cancelReasonEnum) {
			return false;
		}
		orderRecord.setCancelReasonCode(cancelReasonEnum.getCode());
		return this.updateByOrderId(orderRecord) > 0;
	}


	public boolean updateCancelSource(Long orderId,String cancelSource) {

		HoOrder hoOrder = this.selectByOrderId(orderId);
		if(Objects.isNull(hoOrder)|| StringUtils.isNotBlank(hoOrder.getCancelSource())||StringUtils.isEmpty(cancelSource)){
			return false;
		}

		HoOrder orderRecord = new HoOrder();
		orderRecord.setOrderId(orderId);
		orderRecord.setCancelSource(cancelSource);
		return this.updateByOrderId(orderRecord) > 0;
	}

    public boolean updateCancelReason(Long orderId, String reasonCode, String reasonDesc) {
        HoOrder orderRecord = new HoOrder();
        orderRecord.setOrderId(orderId);
        orderRecord.setCancelReasonCode(reasonCode);
        orderRecord.setCancelReasonDesc(reasonDesc);
        return this.updateByOrderId(orderRecord) > 0;
    }

	public boolean updateAsyncCancel(Long orderId, Boolean asyncCancel) {
		HoOrder orderRecord = new HoOrder();
		orderRecord.setOrderId(orderId);
		orderRecord.setAsyncCancel(asyncCancel);
		return this.updateByOrderId(orderRecord) > 0;
	}

    public HoOrder selectBySupplierOrderId(String supplierOrderId) {
		if (StringUtils.isBlank(supplierOrderId)){
			return null;
		}
		HoOrder hoOrder = new HoOrder();
		hoOrder.setSupplierOrderId(supplierOrderId);
        return orderMapper.selectOne(hoOrder);
    }

    public List<HoOrder> listValidOrdersByOrderIds(Collection<Long> ids) {
		if(CollectionUtils.isEmpty(ids)){
			return Collections.emptyList();
		}
		return orderMapper.listValidOrdersByOrderIds(ids);
    }
}
