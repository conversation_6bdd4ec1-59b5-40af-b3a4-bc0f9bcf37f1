package com.corpgovernment.hotel.product.entity.db;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "ho_room_daily_info_new")
public class HoRoomDailyInfoNew implements Serializable {

    /**
     * 订单号
     **/
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 订单号
     **/
    @Column(name = "order_id")
    private Long orderId;

    /**
     * ho_room表的id字段
     **/
    @Column(name = "ho_room_id")
    private Long hoRoomId;

    /**
     * 日期（输出格式化为String）
     **/
    @Column(name = "effect_date")
    private Date effectDate;

    /**
     * 原币价格(海外酒店含税)
     **/
    @Column(name = "room_price")
    private BigDecimal roomPrice;

    /**
     * 早餐类型
     */
    @Column(name = "breakfast_type")
    private Integer breakfastType;

    /**
     * 早餐（无早、单早、双早）
     **/
    @Column(name = "breakfast_name")
    private String breakfastName;

    /**
     * 餐食份数
     */
    @Column(name = "meals")
    private Integer meals;

    /**
     * 创建时间
     **/
    @Column(name = "datachange_createtime")
    private Date datachangeCreatetime;

    /**
     * 最后修改时间
     **/
    @Column(name = "datachange_lasttime")
    private Date datachangeLasttime;

}
