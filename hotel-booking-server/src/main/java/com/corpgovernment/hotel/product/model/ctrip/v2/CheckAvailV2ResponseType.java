package com.corpgovernment.hotel.product.model.ctrip.v2;

import com.corpgovernment.core.dao.dto.QueryHotelDetailCommonRespDto;
import com.corpgovernment.core.domain.model.SupplierCheckAvailResultModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CheckAvailV2ResponseType implements Serializable {
	private static final long serialVersionUID = 1L;


	private String errorMessage;

	private Long errorCode;


	private Boolean success;

	/**
	 * 接口返回状态
	 */
	private ResponseStatus status;

	/**
	 * 不可用原因
	 */
	private MultipleLanguageText failedReason;

	/**
	 * 房型信息
	 */
	private RoomItemV2 roomInfo;

	/**
	 * 每日房型信息
	 */
	private List<RoomDailyInfoV2> roomDailyInfoList;

	/**
	 * 房间是否变价
	 */
	private Boolean changePrice;
	
	/**
	 * 取消政策是否变化
	 */
	private Boolean changeCancelPolicy;

	/**
	 * 价格变化信息
	 */
	private List<ChangePriceDetailInfoV2> changePriceDetailList;

	private BookingRules bookingRules;

	@Data
	public static class BookingRules {
		/**
		 * 确认规则
		 */
		private ConfirmRules confirmRules;

		/**
		 * 可订客人信息
		 */
		private BillingGuestInfo billingGuestInfo;

		/**
		 * 证件信息(1:需要; 0:不需要)
		 */
		private CertificateInfo certificateInfo;
		
		/**
		 * 最少入住天数
		 */
		private Integer minLOS;
	}
	@Data
	@NoArgsConstructor
	public static class ConfirmRules{
		private Boolean justifyConfirm;
	}

	/**
	 * 可订客人信息
	 */
	@Data
	public static class BillingGuestInfo{
		private List<String> guestsNameLanguages;
		/**
		 * 是否需要邮箱
		 */
		private Boolean needEmail;
	}

	@Data
	public static class CertificateInfo{
		/**
		 * 是否需要证件(1:需要; 0:不需要)
		 */
		private Boolean needCertificate;
		/**
		 * 证件类型（1:身份证; 2:护照; 3:学生证; 4:军人证; 6:驾驶证; 7:回乡证; 8:台胞证; 10:港澳通行证; 11:国际海员证; 21:旅行证; 22:台湾通行证; 25:户口簿; 27:出生证明; 28:外国人永久居留身份证; 32:港澳台居民居住证; 99:其他; 0:未知）
		 */
		private String supportCertificateType;
	}
}
