package com.corpgovernment.hotel.product.model.ctrip.v2;


import lombok.Data;

import java.io.Serializable;

/**
 * 房型预定规则信息
 */
@Data
public class BookingRulesInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 是否可订
	 */
	public Boolean canReserve;

	/**
	 * 是否立即确认
	 */
	public Boolean justifyConfirm;

	/**
	 * 取消规则信息
	 */
	public CancelRuleInfoEntity cancelRuleInfo;

	/**
	 * 最晚入住时间
	 */
	public LastArriveTimeEntity lastArriveTimeInfo;

	/**
	 * 房型剩余房量
	 */
	public Integer roomQuantity;

	/**
	 * 房型适用人群信息
	 */
	public ApplicativeAreaEntity applicativeAreaInfo;
}
