package com.corpgovernment.hotel.product.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.corpgovernment.api.applytrip.apply.CalculateTravelFee;
import com.corpgovernment.api.approvalsystem.mq.CallbackPojo;
import com.corpgovernment.api.hotel.booking.enums.HotelOrderQueryEnum;
import com.corpgovernment.api.hotel.booking.orderstatus.request.CompletedOrderRequest;
import com.corpgovernment.api.hotel.booking.orderstatus.response.CompletedOrderResponse;
import com.corpgovernment.api.hotel.product.bo.*;
import com.corpgovernment.api.hotel.product.dto.*;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.hotel.product.model.orderdetail.request.SupplierOrderDetailRequest;
import com.corpgovernment.api.hotel.product.model.orderdetail.response.ClientInfo;
import com.corpgovernment.api.hotel.product.model.orderdetail.response.HotelOrderInfoResponse;
import com.corpgovernment.api.hotel.product.model.orderdetail.response.RefundPaymentInfoBO;
import com.corpgovernment.api.hotel.product.model.orderdetail.response.RoomInfo;
import com.corpgovernment.api.hotel.product.model.request.*;
import com.corpgovernment.api.hotel.product.model.response.*;
import com.corpgovernment.api.platform.soa.handlerPaymentBill.PushPayResultModel;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.hotel.booking.service.OrderDetailService;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse.HotelOrderInfo;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse.RefundInfo;
import com.corpgovernment.hotel.product.service.*;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * @ClassName: HotelController
 * @description: 酒店控制器
 * @author: zdwang
 * @date: Created in 11:06 2019/8/23
 * @Version: 1.0
 **/
@RestController
@RequestMapping("/hotelSoa")
@Slf4j
public class HotelSoaController {
	@Autowired
	private HoHotelService hoHotelService;
	@Autowired
	private HoOrderDetailService orderDetailService;
	@Autowired
	private CancelOrderProductService cancelOrderProductService;
	@Autowired
	private HotelPushService hotelPushService;
	@Autowired
	private HoOrderService orderService;
	@Autowired
	private HoOrderLoader hoOrderLoader;
	@Autowired
	private ConfirmOrderService confirmOrderService;
	@Autowired
	private OrderDetailService orderDetailServiceNew;
	@Autowired
	private HotelModifyService hotelModifyService;
	@Autowired
	private PaymentResultProcessService paymentResultProcessService;
	@Autowired
	private ApprovalResultProcessService approvalResultProcessService;
	@Autowired
	private CompletedOrderService completedOrderService;

	@GetMapping()
	public String hotel() {
		return "this is hotel controller";
	}

	/**
	 * 酒店列表
	 *
	 * @param localHotelListRequestBo
	 * @return
	 */
	@PostMapping("/page")
	public JSONResult<SearchHotelListResponseVO> hotelPage(@RequestBody LocalHotelListRequestBo localHotelListRequestBo) {
		SearchHotelListResponseVO page = hoHotelService.page(localHotelListRequestBo);
		return JSONResult.success(page);
	}

	/**
	 * 酒店详情
	 *
	 * @param localHotelDetailRequestBo
	 * @return
	 */
	@PostMapping("/detail")
	public JSONResult<HotelDetailResponseVO> detail(@RequestBody LocalHotelDetailRequestBo localHotelDetailRequestBo,BaseUserInfo baseUserInfo) {
		localHotelDetailRequestBo.setBaseUserInfo(baseUserInfo);
		HotelDetailResponseVO detail = hoHotelService.detail(localHotelDetailRequestBo,baseUserInfo);
		return JSONResult.success(detail);
	}

	/**
	 * 订单详情
	 */
	@RequestMapping("/orderDetail")
	public JSONResult<OrderDetailResponseBo> orderDetail(@RequestBody QueryOrderDetailRequestBo queryOrderDetailRequestBo) {
		return JSONResult.success(orderDetailService.queryOrderDetail(queryOrderDetailRequestBo));
	}

	/**
	 * 订单详情List
	 */
    @RequestMapping("/orderDetailList")
    public JSONResult<List<OrderDetailResponseBo>>
        orderDetailList(@RequestBody List<QueryOrderDetailRequestBo> queryOrderDetailRequestBoList) {
        return JSONResult.success(orderDetailService.queryOrderDetailList(queryOrderDetailRequestBoList));
    }

	/**
	 * 补开发票
	 */
	@RequestMapping("/reCreateInvoice")
	public JSONResult reCreateInvoice(@RequestBody ReCreateInvoiceRequestBo reCreateInvoiceRequestBo) {
		return orderDetailService.reCreateInvoice(reCreateInvoiceRequestBo);
	}

	/**
	 * 订单特殊信息
	 */
	@RequestMapping("/saveSpecialNeed")
	public JSONResult saveSpecialNeed(@RequestBody HoOrderBo hoOrderBo) {
		return JSONResult.success(orderDetailService.saveSpecialNeed(hoOrderBo));
	}

	/**
	 * 订单取消
	 */
	@RequestMapping("/cancelOrder")
	public JSONResult<CancelOrderResponseBo> cancelOrder(@RequestBody CancelOrderRequestBo cancelOrderRequestBo) {
		return JSONResult.success(cancelOrderProductService.cancelOrder(cancelOrderRequestBo));
	}

	/**
	 * 订单取消
	 */
	@RequestMapping("/cancelOrderInit")
	public JSONResult<CancelOrderInitResponse> cancelOrderInit(@RequestBody CancelOrderRequestBo cancelOrderRequestBo) {
		return JSONResult.success(cancelOrderProductService.cancelOrderInit(cancelOrderRequestBo));
	}

	/**
	 * 订单完成
	 */
	@RequestMapping("/updateOrderCompleted")
	public JSONResult updateOrderCompleted() {
		return JSONResult.success(orderDetailService.updateOrderCompleted());
	}

	/**
	 * 酒店差旅费用计算
	 */
	@RequestMapping("/feeCalc")
	public JSONResult<List<CalculateTravelFee>> feeCalc(@RequestBody CalcFeeRequest calcFeeRequestList) {
		List<CalculateTravelFee> result = hoHotelService.calcFee(calcFeeRequestList);
		return JSONResult.success(result);
	}


	/**
	 * 服务商订单详情
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping("/supplierOrderDetail")
    public JSONResult<HotelOrderInfoResponse> supplierOrderDetail(@RequestBody SupplierOrderDetailRequest request) {
        OrderDetailResponse.HotelOrderInfo hotelOrderInfo;
		// 有companyCode时为补录 或 酒店直连订单job触发（还未存在供应商订单号的场景 下单失败的场景）
        if (StringUtils.isNotBlank(request.getCompanyCode())) {
            hotelOrderInfo = hotelPushService.getHotelOrderInfo(request);
        } else {
            hotelOrderInfo = hotelPushService.getHotelOrderInfo(request.getSupplierOrderId(), request.getSupplierCode(),
                request.getSupplierCorpId(), HotelOrderQueryEnum.grepQueryEnumByCode(request.getOrderQueryEnum()));
        }
        if (hotelOrderInfo == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.SUPPLIER_ORDER_DETAILS_ARE_EMPTY);
        }
        HotelOrderInfoResponse hotelOrderInfoResponse = this.convert(hotelOrderInfo);
        return JSONResult.success(hotelOrderInfoResponse);
    }

	private HotelOrderInfoResponse convert(HotelOrderInfo hotelOrderInfo) {
		HotelOrderInfoResponse response = new HotelOrderInfoResponse();
		response.setOrderId(hotelOrderInfo.getOrderId());
		response.setSupplierUid(hotelOrderInfo.getSupplierUid());
		response.setPlatformOrderId(hotelOrderInfo.getPlatformOrderId());
		response.setOrderDetailStatus(hotelOrderInfo.getOrderDetailStatus());
		response.setOrderDetailStatusName(hotelOrderInfo.getOrderDetailStatusName());
		response.setCustomPayAmount(hotelOrderInfo.getCustomPayAmount());
		response.setLadderDeductAmount(hotelOrderInfo.getLadderDeductAmount());
		response.setSettlementACCNTAmt(hotelOrderInfo.getSettlementACCNTAmt());
		response.setSettlementPersonAmt(hotelOrderInfo.getSettlementPersonAmt());
		response.setCancelReasonCode(hotelOrderInfo.getCancelReasonCode());
		response.setCancelReasonDesc(hotelOrderInfo.getCancelReasonDesc());
		response.setHotelName(hotelOrderInfo.getHotelName());
		response.setCityId(hotelOrderInfo.getCityId());
		response.setCityName(hotelOrderInfo.getCityName());
		response.setHotelAddress(hotelOrderInfo.getHotelAddress());
		response.setHotelPhone(hotelOrderInfo.getHotelPhone());
		response.setContactName(hotelOrderInfo.getContactName());
		response.setContactPhone(hotelOrderInfo.getContactPhone());
		response.setCheckInDate(hotelOrderInfo.getCheckInDate());
		response.setCheckOutDate(hotelOrderInfo.getCheckOutDate());
		response.setVatFlag(hotelOrderInfo.getVatFlag());
		response.setHotelType(hotelOrderInfo.getHotelType());
		response.setServiceFee(hotelOrderInfo.getServiceFee());
		response.setPostServiceFee(hotelOrderInfo.getPostServiceFee());
		response.setDeliveryFee(hotelOrderInfo.getDeliveryFee());
		response.setRoomQuantity(hotelOrderInfo.getRoomQuantity());
		response.setAddedFees(hotelOrderInfo.getAddedFees());
		response.setCancelFee(hotelOrderInfo.getCancelFee());
		List<RefundInfo> refundInfoList = Optional.ofNullable(hotelOrderInfo.getRefundInfo()).orElse(new ArrayList<>());

		List<RefundPaymentInfoBO> refundPaymentInfoBOList = new ArrayList<>();
		for (RefundInfo refundInfo:refundInfoList){
			List<OrderDetailResponse.RefundPaymentInfo> refundPaymentList = refundInfo.getRefundPaymentList();
			for (OrderDetailResponse.RefundPaymentInfo refundPaymentInfo:refundPaymentList){
				RefundPaymentInfoBO bo = new RefundPaymentInfoBO();
				bo.setRefundId(refundPaymentInfo.getRefundId());
				bo.setRefundChannel(refundPaymentInfo.getRefundChannel());
				bo.setAmount(refundPaymentInfo.getAmount());
				bo.setRefundInfoId(refundInfo.getRefundInfoID());
				refundPaymentInfoBOList.add(bo);
			}
		}
		response.setRefundPaymentList(refundPaymentInfoBOList);

		response.setPaymentList(hotelOrderInfo.getPaymentList());
		response.setRefundInfoList(hotelOrderInfo.getRefundInfoList());

		List<OrderDetailResponse.ClientInfoEntity> clientInfoEntityList = hotelOrderInfo.getClientInfo();
        if (CollectionUtils.isNotEmpty(clientInfoEntityList)) {
            response.setClientInfoList(clientInfoEntityList.stream().map(clientInfoEntity -> {
                ClientInfo clientInfo = new ClientInfo();
                clientInfo.setClientName(clientInfoEntity.getClientName());
                clientInfo.setUID(clientInfoEntity.getUID());
                clientInfo.setRoomIndex(clientInfoEntity.getRoomIndex());
                clientInfo.setActualDepartureTime(clientInfoEntity.getActualDepartureTime());
                clientInfo.setActualCheckInTime(clientInfoEntity.getActualCheckInTime());
                clientInfo.setMobilePhone(clientInfoEntity.getMobilePhone());
                return clientInfo;

            }).collect(Collectors.toList()));
        }

		List<OrderDetailResponse.RoomInfo> roomInfoList = hotelOrderInfo.getRoomInfoList();
        if (CollectionUtils.isNotEmpty(roomInfoList)) {
            response.setRoomInfoList(roomInfoList.stream().map(roomInfo -> {
                RoomInfo room = new RoomInfo();
                room.setRoomName(roomInfo.getRoomName());
                room.setBedType(roomInfo.getBedType());
                room.setPrice(roomInfo.getPrice());
                room.setBreakfast(roomInfo.getBreakfast());
                return room;
            }).collect(Collectors.toList()));
        }

		return response;
	}

	/**
	 * 更新订单状态，并发送状态变化MQ消息 如果oldOrderStatus不传，则无脑更新状态；如果oldOrderStatus传，则校验原始订单状态必须与传值一致，才更新状态
	 *
	 * @param orderId
	 * @param oldOrderStatus
	 * @param newOrderStatus
	 * @return
	 */
	@RequestMapping(value = "/updateOrderStatus")
	public JSONResult<Integer> updateOrderStatus(@RequestParam("orderId") Long orderId, @RequestParam(value = "oldOrderStatus", required = false) String oldOrderStatus, @RequestParam("newOrderStatus") String newOrderStatus) {
		return JSONResult.success(hotelPushService.updateOrderStatus(orderId, oldOrderStatus, newOrderStatus));
	}

	/**
	 * 更新订单状态，并发送状态变化MQ消息 如果oldOrderStatus不传，则无脑更新状态；如果oldOrderStatus传，则校验原始订单状态必须与传值一致，才更新状态
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/updateOrderStatusNew")
	public JSONResult<Integer> updateOrderStatusNew(@RequestBody HotelUpdateOrderStatusRequestBo request) {
		return JSONResult.success(hotelPushService.updateOrderStatus(request));
	}

	/**
	 * 更新确认时间
	 *
	 * @param orderId
	 * @param supplierOrderId
	 * @return
	 */
	@RequestMapping(value = "/updateConfirmTime")
	public JSONResult<Integer> updateConfirmTime(@RequestParam("orderId") Long orderId, @RequestParam("supplierOrderId") String supplierOrderId) {
		//更新订单状态为已确认
		return JSONResult.success(orderService.updateOrderStatusAndTicketTime(orderId, supplierOrderId, OrderStatusEnum.TA.getCode(), Calendar.getInstance().getTime()));
	}

	/**
	 * 取消阶梯价格
	 *
	 * @param orderId
	 * @param ladderAmount
	 * @return
	 */
	@RequestMapping(value = "/updateOrderStatusAndLadderAmount")
	public JSONResult<Integer> updateOrderStatusAndLadderAmount(@RequestParam("orderId") Long orderId, @RequestParam("orderStatus") String orderStatus, @RequestParam("ladderAmount") BigDecimal ladderAmount) {
		return hotelPushService.updateOrderStatusAndLadderAmount(orderId, orderStatus, ladderAmount);
	}

	/**
	 * 创建发票
	 *
	 * @param reCreateInvoiceRequestBo
	 * @return
	 */
	@RequestMapping(value = "/autoReCreateInvoice")
	public JSONResult autoReCreateInvoice(@RequestBody ReCreateInvoiceRequestBo reCreateInvoiceRequestBo) {
		return orderDetailService.autoReCreateInvoice(reCreateInvoiceRequestBo);
	}

	/**
	 * 更新供应商订单Submit时间
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/updateSubmitTime")
	public JSONResult<Integer> updateSubmitTime(@RequestBody HotelUpdateSubmitTimeRequestBO request) {
		return JSONResult.success(hoOrderLoader.updateSubmitTime(request.getOrderId()));
	}

	/**
	 * 更新供应商订单Approving时间
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/updateApprovingTime")
	public JSONResult<Integer> updateApprovingTime(@RequestBody HotelUpdateApprovingTimeRequestBO request) {
		return JSONResult.success(hoOrderLoader.updateApprovingTime(request.getOrderId()));
	}

	/**
	 * 确认酒店订单
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/confirmOrder")
	public JSONResult<HotelConfirmOrderResponseBO> confirmOrder(@RequestBody HotelConfirmOrderRequestBO request) {
		return JSONResult.success(confirmOrderService.confirmOrder(request));
	}


	/**
	 * 查询超时未确认酒店
	 *
	 * @return
	 */
	@RequestMapping(value = "/searchTimeOutNotConfirmOrder")
	public JSONResult<SearchTimeOutNotConfirmOrderResponseBO> searchTimeOutNotConfirmOrder(){
		return JSONResult.success(orderService.searchTimeOutNotConfirmOrder());
	}

	/**
	 * 回滚酒店订单提交时间
	 *
	 * @return
	 */
	@RequestMapping(value = "/rollbackSubmitTime")
	public JSONResult<Integer> rollbackSubmitTime(@RequestBody HotelRollbackSubmitTimeRequestBO request){
		return JSONResult.success(hoOrderLoader.rollbackSubmitTime(request.getOrderId()));
	}

	/**
	 * 酒店详情查询-批量
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/queryHotelOrderDetail")
	public JSONResult queryHotelOrderDetail(@RequestBody Long orderId) {
		return JSONResult.success(orderDetailServiceNew.queryHotelOrderDetail(orderId));
	}

    /**
     * 保存取消原因
     * 
     * @param request
     * @return
     */
    @PostMapping(value = "/saveCancelReason")
    @ApiOperation("保存取消原因")
    public JSONResult<CancelReasonResponse> saveCancelReason(@RequestBody CancelReasonRequest request) {
        return JSONResult.success(cancelOrderProductService.saveCancelReason(request));
    }

	/**
	 * 保存取消原因
	 *
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/saveCancelSource")
	@ApiOperation("保存取消原因")
	public JSONResult<CancelReasonResponse> saveCancelSource(@RequestBody CancelReasonRequest request) {
		return JSONResult.success(cancelOrderProductService.saveCancelSource(request));
	}

	/**
	 * 更新是否异步取消
	 */
	@PostMapping(value = "/updateAsyncCancel")
	public JSONResult<AsyncCancelResponse> updateAsyncCancel(@RequestBody AsyncCancelRequest request){
		return JSONResult.success(cancelOrderProductService.updateAsyncCancel(request));
	}

	/**
	 * 根据订单号更新订单
	 */
	@PostMapping(value = "/updateByOrderId")
	public JSONResult<Boolean> updateByOrderId(@RequestBody UpdateOrderRequest request){
		return JSONResult.success(orderService.updateByOrderId(request));
	}

	@PostMapping(value = "/updateHotelApplyRefundAmountByApplyId")
	public JSONResult<Boolean> updateHotelApplyRefundAmountByApplyId(@RequestBody UpdateHotelApplyRequest request){
		return JSONResult.success(hotelModifyService.updateHotelApplyRefundAmountByApplyId(request));
	}

	@PostMapping("/processApprovalResult")
	public JSONResult<Boolean> processApprovalResult(@RequestParam String businessId,
													 @RequestParam String approvalResult) {
		try {
			log.info("[SOA请求]处理审批结果 businessId: {}, approvalResult: {}", businessId, approvalResult);
			CallbackPojo callbackPojo = new CallbackPojo();
			callbackPojo.setBusinessId(businessId);
			callbackPojo.setApproveResult(approvalResult);
			approvalResultProcessService.processApprovalResult(callbackPojo);
		} catch (Exception e) {
			log.error("处理审批结果异常：" + e.getMessage(), e);
			return JSONResult.success(false);
		}
		return JSONResult.success(true);
	}

	@PostMapping("/processPaymentResult")
	public JSONResult<Boolean> processPaymentResult(@RequestParam String orderId, @RequestParam Boolean successFlag, @RequestParam(required = false) String payChannel) {
		StringBuilder logContext = new StringBuilder();
		PushPayResultModel pushPayResultModel = new PushPayResultModel();
		pushPayResultModel.setOrderId(orderId);
		pushPayResultModel.setSuccessFlag(successFlag);
		pushPayResultModel.setPayChannel(payChannel);
		paymentResultProcessService.processPaymentResult(pushPayResultModel, logContext);
		return JSONResult.success(true);
	}

	/**
	 * 将订单状态改为"已完成"
	 */
	@PostMapping("/updateOrderCompletedByOrderId")
	public JSONResult<CompletedOrderResponse> updateOrderCompletedByOrderId(@RequestBody CompletedOrderRequest request) {
		return JSONResult.success(completedOrderService.updateOrderCompletedByOrderId(request.getOrderId()));
	}

	@PostMapping("/syncCancelFormDetail")
	public JSONResult<Boolean> syncCancelFormDetail(@RequestParam Long orderId) {
		return JSONResult.success(cancelOrderProductService.syncCancelFormDetail(orderId));
	}

	@PostMapping("/confirmOrderWithoutCheck")
	public JSONResult<HotelConfirmOrderResponseBO> confirmOrderWithoutCheck(@RequestBody HotelConfirmOrderRequestBO request) {
		return JSONResult.success(confirmOrderService.confirmOrderWithoutCheck(request));
	}

}