package com.corpgovernment.hotel.product.service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.hotel.product.bo.HotelOrderInfoBO;
import com.corpgovernment.api.hotel.product.bo.SearchTimeOutNotConfirmOrderResponseBO;
import com.corpgovernment.api.hotel.product.dto.UpdateOrderRequest;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.hotel.product.model.saveorder.request.SaveOrderRequestBo;
import com.corpgovernment.api.ordercenter.dto.OcUtils;
import com.corpgovernment.api.platform.soa.paymentbill.PpBillDto;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.common.enums.DeliveryTypeEnum;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.hotel.booking.bo.CheckInOutDateInfoBo;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayBillClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.mq.OrderStatusProducer;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Slf4j
public class HoOrderService extends BaseService {

    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private PayBillClientLoader payBillClientLoader;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private OrderStatusProducer orderStatusProducer;

    public Integer save(SaveOrderRequestBo request) {
        SaveOrderRequestBo.OrderInfo orderInfo = request.getOrderInfo();
        HoOrder order = new HoOrder();
        BeanUtils.copyProperties(orderInfo, order);
        order.setPaytype(orderInfo.getPayType());
        String corpId = order.getCorpId();
        String depId = order.getDeptId();
        order.setOrgId(depId);
        order.setCompensationFee(orderInfo.getCompensationFee());
        if (corpId.equals(depId)) {
            order.setDeptId(null);
        }
        if (order.getOrderStatus() == null) {
            order.setOrderStatus(OrderStatusEnum.SI.getCode());
        }
        if (StringUtils.isBlank(order.getDeliveryType())) {
            order.setDeliveryType(DeliveryTypeEnum.PJN.getCode());
        }

        SaveOrderRequestBo.RoomInfo roomInfo = request.getRoomInfo();
        order.setCheckInTime(roomInfo.getCheckInDate());
        order.setCheckOutTime(roomInfo.getCheckOutDate());
        //默认为订单初始入离时间
        order.setActualCheckInTime(roomInfo.getCheckInDate());
        order.setActualCheckOutTime(roomInfo.getCheckOutDate());

        CheckInOutDateInfoBo checkInOutDateInfoBo = new CheckInOutDateInfoBo();
        checkInOutDateInfoBo.setCheckInDate(roomInfo.getCheckInDate());
        checkInOutDateInfoBo.setCheckOutDate(roomInfo.getCheckOutDate());
        List<CheckInOutDateInfoBo> checkInOutDateInfoBoList = new ArrayList<>();
        checkInOutDateInfoBoList.add(checkInOutDateInfoBo);
        order.setNewestCheckInOutDate(JsonUtils.toJsonString(checkInOutDateInfoBoList));
        return hoOrderLoader.insertSelective(order);
    }

    public Integer updateOrderStatusAndTicketTime(Long orderId, String supplierOrderId, String orderStatus,
        Date ticketTime) {
        HoOrder record = new HoOrder();
        record.setOrderId(orderId);
        record.setTicketIssuedTime(ticketTime);
        record.setSupplierOrderId(supplierOrderId);
        record.setOrderStatus(orderStatus);
        Integer result = hoOrderLoader.updateByOrderId(record);
        if (result > 0) {
            orderStatusProducer.sendOrderStatusMsg(OcUtils.initOcReq(orderId, orderStatus, null, OrderTypeEnum.HN));
        }
        return result;
    }

    /**
     * 查询已支付支付未确认的订单
     *
     * @return
     */
    public SearchTimeOutNotConfirmOrderResponseBO searchTimeOutNotConfirmOrder() {
        try {
            initElkLog();
            addElkInfoLog("进入酒店查询未确认订单流程");
            Example example = new Example(HoOrder.class);
            Criteria criteria = example.createCriteria();
            criteria.andEqualTo("orderStatus", OrderStatusEnum.TW.getCode())
                .andEqualTo("isDeleted", false);
            List<HoOrder> orderList = hoOrderLoader.selectByExample(example);
            if (CollectionUtils.isEmpty(orderList)) {
                addElkInfoLog("没有查询到未确认的订单，流程结束");
                return new SearchTimeOutNotConfirmOrderResponseBO();
            }
            Integer timeoutNotConfirm = hotelApollo.getTimeoutNotConfirm();
            addElkInfoLog("超时未确认时间为：%s分钟", timeoutNotConfirm);
            List<Long> orderIds = orderList.stream().map(HoOrder::getOrderId).collect(Collectors.toList());
            List<PpBillDto> billList = payBillClientLoader.searchBillInfo(orderIds);
            Map<Long, List<PpBillDto>> billMap =
                billList.stream().collect(Collectors.groupingBy(PpBillDto::getOrderId));
            List<HotelOrderInfoBO> hotelOrderList = new ArrayList<>();
            orderIds.forEach(orderId -> {
                List<PpBillDto> subBillList = billMap.get(orderId);
                if (CollectionUtils.isEmpty(subBillList)) {
                    addElkInfoLog("订单：%s未查询到支付单", orderId);
                    return;
                }
                if (subBillList.stream().anyMatch(e -> Objects.equals(e.getStatus(), "U"))) {
                    addElkInfoLog("订单：%s存在未支付的支付单", orderId);
                    return;
                }
                Date paySuccessDate =
                    subBillList.stream().map(PpBillDto::getDatachangeLasttime).max(Date::compareTo).orElse(new Date());
                Date now = new Date();
                if (now.compareTo(DateUtil.addMinutes(paySuccessDate, timeoutNotConfirm)) > 0) {
                    hotelOrderList.add(HotelOrderInfoBO.create(orderId));
                }
            });
            addElkInfoLog("查询未确认订单流程结束");
            return SearchTimeOutNotConfirmOrderResponseBO.create(hotelOrderList);
        } finally {
            log.info("HoOrderService.searchTimeOutNotConfirmOrder查询酒店未确认订单{} request：{}{}{}{}", System.lineSeparator(),
                null, System.lineSeparator(), System.lineSeparator(), this.getElkInfoLog());
            clearElkLog();
        }
    }

    public Boolean updateByOrderId(UpdateOrderRequest request) {
        HoOrder record = new HoOrder();
        record.setOrderId(request.getOrderId());
        if (null != request.getTotalRefundAmount() && BigDecimal.ZERO.compareTo(request.getTotalRefundAmount()) != 0) {
            record.setRefundAmount(request.getTotalRefundAmount());
        }
        if (null != request.getCancelFee() && BigDecimal.ZERO.compareTo(request.getCancelFee()) != 0) {
            record.setCancelFee(request.getCancelFee());
        }
        int updateRow = hoOrderLoader.updateByOrderId(record);
        return updateRow > 0;
    }
}
