package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.organization.soa.IPassengerClient;
import com.corpgovernment.api.organization.soa.switchbo.GetPassengerNameRequest;
import com.corpgovernment.api.organization.soa.switchbo.GetPassengerNameResponse;
import com.corpgovernment.common.base.JSONResult;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lilayzzz
 * @since: 2023/11/13
 * @description:
 */
@Component
@Slf4j
public class OrganizationPassengerClientLoader {

    @Autowired
    private IPassengerClient passengerSoaService;

    /**
     * 中文
     */
    private static final String PASSENGER_NAME_TYPE_ZH = "zh";

    /**
     * 非中文
     */
    private static final String PASSENGER_NAME_TYPE_NO_ZH = "noZh";

    /**
     * 英文
     */
    private static final String PASSENGER_NAME_TYPE_EN = "en";

    /**
     * 获取乘客名称
     * 
     * @param passengerNameRequestList
     * @return
     */
    public List<GetPassengerNameResponse> getPassengerName(List<GetPassengerNameRequest> passengerNameRequestList) {
        try {
            if (CollectionUtils.isEmpty(passengerNameRequestList)) {
                return Collections.emptyList();
            }
            passengerNameRequestList.forEach(passenger -> {
                if (StringUtils.isBlank(passenger.getLanguage())) {
                    return;
                }
                // 校准接口入参 酒店产线语言枚举与接口提供方不一致
                if (!PASSENGER_NAME_TYPE_EN.equalsIgnoreCase(passenger.getLanguage())) {
                    passenger.setLanguage(PASSENGER_NAME_TYPE_ZH);
                }
            });
            // 传入"OrganizationPassengerClientLoader.PASSENGER_NAME_TYPE_ZH"查询中文名称、非"OrganizationPassengerClientLoader.PASSENGER_NAME_TYPE_ZH"则返回英文名称
            JSONResult<List<GetPassengerNameResponse>> result =
                passengerSoaService.getPassengerName(passengerNameRequestList);
            if (null == result || !result.isSUCCESS()) {
                log.error("获取乘客名称失败:{}", result);
                return Collections.emptyList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("获取乘客名称异常", e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取乘客名称，返回Map：key-uid value：passengerName
     * 
     * @param function 处理convert
     * @param obj 入参类型
     * @return
     */
    public Map<String, String> getTravelerNameMap(Function<Object, List<GetPassengerNameRequest>> function,
        Object obj) {
        try {
            List<GetPassengerNameRequest> passengerNameRequestList = function.apply(obj);
            List<GetPassengerNameResponse> getPassengerNameResponseList =
                this.getPassengerName(passengerNameRequestList);
            if (CollectionUtils.isEmpty(getPassengerNameResponseList)) {
                return Collections.emptyMap();
            }
            return getPassengerNameResponseList.stream().filter(res -> Objects.nonNull(res.getId()))
                .collect(Collectors.toMap(
                    GetPassengerNameResponse::getId, GetPassengerNameResponse::getPassengerName));
        } catch (Exception e) {
            log.error("获取乘客名称异常", e);
        }
        return Collections.emptyMap();
    }
}
