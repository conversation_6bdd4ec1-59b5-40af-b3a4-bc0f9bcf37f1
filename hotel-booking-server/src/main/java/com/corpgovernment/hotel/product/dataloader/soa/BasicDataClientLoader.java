package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.basic.bo.SearchBasicDataRequestBo;
import com.corpgovernment.api.basic.bo.StaticMapListRequestBo;
import com.corpgovernment.api.basic.bo.StaticMapRequestBo;
import com.corpgovernment.api.basic.dto.BasicCityInfoDto;
import com.corpgovernment.api.basic.dto.CityCountyResponse;
import com.corpgovernment.api.basic.dto.CityLocationResponse;
import com.corpgovernment.api.basic.enums.SiteEnum;
import com.corpgovernment.api.basic.request.*;
import com.corpgovernment.api.basic.response.BasicGeographyInfoResponse;
import com.corpgovernment.api.basic.response.BasicIntegratedCityResponse;
import com.corpgovernment.api.basic.response.HotelCityInfoFuzzySearchResponse;
import com.corpgovernment.api.basic.response.StaticMapResponse;
import com.corpgovernment.api.basic.soa.BasicDataClient;
import com.corpgovernment.api.basic.vo.OrderIdProducerRequestVo;
import com.corpgovernment.api.basic.vo.OrderIdProducerResponseVo;
import com.corpgovernment.api.basic.sdk.IdGeneratorClient;
import com.corpgovernment.basic.convert.BasicDataConvert;
import com.corpgovernment.basicdata.bo.HotelAreaBo;
import com.corpgovernment.basicdata.bo.HotelBrandBo;
import com.corpgovernment.basicdata.bo.HotelCityBo;
import com.corpgovernment.basicdata.bo.HotelCitySupplierBo;
import com.corpgovernment.basicdata.bo.HotelMetroBo;
import com.corpgovernment.basicdata.bo.HotelZoneBo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.hotel.product.dto.GeographyInfoDTO;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BasicDataClientLoader {
	@Autowired
	private BasicDataClient basicDataClient;

	@Autowired
	private IdGeneratorClient idGeneratorClient;

    // Type: 1: 酒店品牌 2：商业区 3：地铁线 4：行政区 5：机场+航站楼 6：火车站 7：汽车站 8：城市地标：
    private static final List<String> SEARCH_TYPE = Lists.newArrayList("1", "2", "3", "4");

	/**
	 * 获取appsecuriy
	 *
	 * @param corpId
	 * @param supplierCode
	 * @return
	 */
	public String getAppSecurity(String corpId, String supplierCode) {
		if (StringUtils.isBlank(corpId) || StringUtils.isBlank(supplierCode)) {
			return null;
		}
		JSONResult<String> result = basicDataClient.getAppSecurity(corpId, supplierCode);
		if (result == null || result.getData() == null) {
			log.error("获取AppSecurity异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 获取携程ticket
	 *
	 * @param corpId
	 * @param supplierCode
	 * @return
	 */
	public String ctripTokenByCorpID(String corpId, String supplierCode) {
		if (StringUtils.isBlank(corpId) || StringUtils.isBlank(supplierCode)) {
			return null;
		}
		JSONResult<String> result = basicDataClient.ctripTokenByCorpID(corpId, supplierCode);
		if (result == null || result.getData() == null) {
			log.error("获取携程ticket异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 获取订单号
	 *
	 * @param uid
	 * @return
	 */
	public Long productionOrderId(String uid) {
		if (StringUtils.isBlank(uid)) {
			return null;
		}
		OrderIdProducerRequestVo request = new OrderIdProducerRequestVo();
		request.setUid(uid);
		request.setSiteEnum(SiteEnum.Hotel);
		JSONResult<OrderIdProducerResponseVo> result = idGeneratorClient.productionOrderId(request);
		if (result == null || result.getData() == null) {
			log.error("获取订单号异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData().getOrderId();
	}

	/**
	 * 获取订单号
	 *
	 * @param uid
	 * @param productType 产线类型
	 * @return
	 */
	public Long productionOrderId(String uid,String productType) {
		if (StringUtils.isBlank(uid)) {
			return null;
		}
		OrderIdProducerRequestVo request = new OrderIdProducerRequestVo();
		request.setUid(uid);
		if(productType.equalsIgnoreCase("HOTEL_INTL")){ // 海外酒店
			request.setSiteEnum(SiteEnum.HotelIntl);
		}else {
			request.setSiteEnum(SiteEnum.Hotel);
		}
		JSONResult<OrderIdProducerResponseVo> result = idGeneratorClient.productionOrderId(request);
		log.info("获取订单号,request:{},result:{}", JsonUtils.toJsonString(request),JsonUtils.toJsonString(result));
		if (result == null || result.getData() == null) {
			log.error("获取订单号异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData().getOrderId();
	}

	/**
	 * 获取全局支付时间
	 *
	 * @return
	 */
	public int getPayTimeLimit() {
		JSONResult<Integer> result = basicDataClient.getPayTimeLimit();
		if (result == null || result.getData() == null) {
			log.error("获取支付时间异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return 0;
		}
		return result.getData();
	}

	/**
	 * 获取基础数据mapping
	 *
	 * @param request
	 * @return
	 */
	public Map<String, HotelCitySupplierBo> searchHotelCitySupplier(SearchBasicDataRequestBo request) {
		if (request == null) {
			return new HashMap<>();
		}
		JSONResult<Map<String, HotelCitySupplierBo>> result = basicDataClient.searchHotelCitySupplier(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础数据mapping异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new HashMap<>();
		}
		return result.getData();
	}

	public void searchHotelCityByCountryId(String countryId) {
		basicDataClient.searchHotelCityByCountryId(countryId);
	}

	/**
	 * 获取基础城市信息
	 *
	 * @param request
	 * @return
	 */
	public List<HotelCityBo> fuzzySearchHotelCity(FuzzySearchHotelCityRequest request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelCityBo>> result = basicDataClient.fuzzySearchHotelCity(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础城市信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 获取基础城市信息
	 *
	 * @param request
	 * @return
	 */
	public List<HotelCityBo> searchHotelCity(SearchBasicDataRequestBo request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelCityBo>> result = basicDataClient.searchHotelCity(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础城市信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 获取基础品牌信息
	 *
	 * @param request
	 * @return
	 */
	public List<HotelBrandBo> searchHotelBrand(SearchBasicDataRequestBo request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelBrandBo>> result = basicDataClient.searchHotelBrand(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础品牌信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 获取基础地铁信息
	 *
	 * @param request
	 * @return
	 */
	public List<HotelMetroBo> searchHotelMetro(SearchBasicDataRequestBo request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelMetroBo>> result = basicDataClient.searchHotelMetro(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础地铁信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 获取基础商业区信息
	 *
	 * @param request
	 * @return
	 */
	public List<HotelZoneBo> searchHotelZone(SearchBasicDataRequestBo request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelZoneBo>> result = basicDataClient.searchHotelZone(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础商业区信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 获取基础区信息
	 *
	 * @param request
	 * @return
	 */
	public List<HotelAreaBo> searchHotelArea(SearchBasicDataRequestBo request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelAreaBo>> result = basicDataClient.searchHotelArea(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础区信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

    /**
     * 获取基础数据
     * 
     * <AUTHOR> 2024/12/20
     */
    public GeographyInfoDTO listGeographyInfoByCityIds(List<String> cityIds) {
        if (CollectionUtils.isEmpty(cityIds)) {
            return new GeographyInfoDTO();
        }
        BasicGeographyInfoRequest request = new BasicGeographyInfoRequest();
        request.setCityIds(cityIds);
        request.setGeographyTypes(SEARCH_TYPE);
        JSONResult<BasicGeographyInfoResponse> result = basicDataClient.listGeographyInfoByTypeAndCityIds(request);
        if (result == null || result.getData() == null) {
            log.error("获取酒店推荐信息异常:{}", Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
            return new GeographyInfoDTO();
        }
        GeographyInfoDTO infoDTO = new GeographyInfoDTO();
        BasicGeographyInfoResponse response = result.getData();
        if (response == null) {
            return infoDTO;
        }
        infoDTO.setAreaBoList(BasicDataConvert.convertAreaList(response.getLocationInfoList()));
        infoDTO.setBrandBoList(BasicDataConvert.convertBrandList(response.getCorpBrandList()));
        infoDTO.setMetroBoList(BasicDataConvert.convertMetroList(response.getMetroInfoList()));
        infoDTO.setZoneBoList(BasicDataConvert.convertZoneList(response.getDomesticZoneList()));
        return infoDTO;
    }

	/**
	 * 获取基础数据mapping
	 *
	 * @param request
	 * @return
	 */
	public StaticMapResponse mapResponse(StaticMapRequestBo request) {
		if (request == null) {
			return null;
		}
		JSONResult<StaticMapResponse> result = basicDataClient.mapResponse(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础数据mapping异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 获取基础数据mapping
	 *
	 * @param request
	 * @return
	 */
	public List<StaticMapResponse> mapResponseList(StaticMapListRequestBo request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<StaticMapResponse>> result = basicDataClient.mapResponseList(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础数据mapping异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	@BusinessBehaviorMonitor
	public List<CityLocationResponse> searchLocation(String cityName, String locationName) {
		if (StringUtils.isBlank(cityName) || StringUtils.isBlank(locationName)) {
			return new ArrayList<>(0);
		}
		CityLocationRequest cityLocationRequest = new CityLocationRequest();
		CityLocation cityLocation = new CityLocation();
		cityLocation.setCityName(cityName);
		cityLocation.setLocationName(locationName);
		cityLocationRequest.setCityLocations(Collections.singleton(cityLocation));
		JSONResult<List<CityLocationResponse>> result = basicDataClient.searchCityByCityName(cityLocationRequest);
		if (result == null || result.getData() == null) {
			log.error("根据名称查询行政区失败");
			return null;
		}
		return result.getData();
	}

	@BusinessBehaviorMonitor
	public List<CityCountyResponse> searchCounty(String cityName, String countyName) {
		if (StringUtils.isBlank(cityName) || StringUtils.isBlank(countyName)) {
			return new ArrayList<>(0);
		}
		CityCountyRequest cityCountyRequest = new CityCountyRequest();
		CityCounty cityCounty = new CityCounty();
		cityCounty.setCityName(cityName);
		cityCounty.setCountyName(countyName);
		cityCountyRequest.setCityCounties(Collections.singleton(cityCounty));
		JSONResult<List<CityCountyResponse>> result = basicDataClient.searchCountyByCityName(cityCountyRequest);
		if (result == null || result.getData() == null) {
			log.error("根据名称查询行政区失败");
			return null;
		}
		return result.getData();
	}

	public BasicCityInfoDto getBasicCityInfo(String cityId) {
		if (StringUtils.isBlank(cityId)) {
			return null;
		}
		BasicCityListRequest basicCityListRequest = new BasicCityListRequest();
		basicCityListRequest.setCityIdList(Collections.singletonList(cityId));
		JSONResult<BasicIntegratedCityResponse> result = basicDataClient.listBasicCityInfoByIds(basicCityListRequest);
		if (result == null || result.getData() == null || CollectionUtils.isEmpty(result.getData().getCityInfoList())) {
			log.error("根据城市id查询城市信息失败");
			return null;
		}
		return result.getData().getCityInfoList().get(0);
	}
	/**
	 * 获取基础城市信息
	 *
	 * @param request
	 * @return
	 */
	public List<HotelCityBo> fuzzySearchHotelCityWithoutCountryId(FuzzySearchHotelCityRequest request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelCityBo>> result = basicDataClient.fuzzySearchHotelCityWithoutCountryId(request);
		if (result == null || result.getData() == null) {
			log.error("获取基础城市信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 获取分销基础城市信息
	 *
	 * @param response
	 * @return
	 */
	public List<HotelCityBo> fuzzySearchHotelCityWithoutCountryId(HotelCityInfoFuzzySearchResponse response) {
		if (response == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelCityBo>> result = basicDataClient.hotelCityInfoFuzzySearchWithoutCountryId(response);
		if (result == null || result.getData() == null) {
			log.error("获取基础城市信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}
}
