package com.corpgovernment.hotel.product.entity.db;

import java.util.Date;

import javax.persistence.*;

import lombok.Data;

@Data
@Table(name = "ho_order_cancel_form")
public class HoOrderCancelForm {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 供应商取消单据号
     */
    @Column(name = "supplier_form_id")
    private String supplierFormId;

    /**
     * 取消单据类型 direct-直接取消 apply - 申请取消
     */
    @Column(name = "form_type")
    private String formType;

    /**
     * 取消单据状态 0-差旅平台已提交 1-已提交 2-协调中 3-处理成功 4-处理失败 5-处理撤销
     */
     @Column(name = "status")
    private Integer status;

    /**
     * 订单号
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 供应商订单号
     */
    @Column(name = "supplier_order_id")
    private String supplierOrderId;

    /**
     * 取消原因 code
     */
    @Column(name = "reason_code")
    private String reasonCode;

    /**
     * 取消原因 desc
     */
    @Column(name = "reason_desc")
    private String reasonDesc;

    /**
     * 处理结果 code
     */
    @Column(name = "result_code")
    private String resultCode;

    /**
     * 处理结果 desc
     */
    @Column(name = "result_desc")
    private String resultDesc;

    /**
     * 是否删除 0-否 1-是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    private Date datachangeCreatetime;

    /**
     * 修改时间
     */
    @Column(name = "datachange_updatetime")
    private Date datachangeUpdatetime;

}