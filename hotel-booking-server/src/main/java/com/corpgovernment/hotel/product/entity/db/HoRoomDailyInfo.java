package com.corpgovernment.hotel.product.entity.db;

import com.corpgovernment.api.hotel.product.model.response.HoRoomDailyBo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 *
 **/
@Data
@Table(name = "ho_room_daily_info")
public class HoRoomDailyInfo implements Serializable {

    /**
     * 订单号
     **/
    private Long orderId;

    /**
     * 日期（输出格式化为String）
     **/
    private java.util.Date effectDate;

    /**
     * 原币价格(海外酒店含税)
     **/
    private java.math.BigDecimal roomPrice;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;
    /**
     * 早餐类型
     */
    private Integer breakfast;
    /**
     * 早餐（无早、单早、双早）
     **/
    private String breakfastName;

    /**
     * 餐食份数
     */
    @Column(name = "meals")
    private Integer meals;

    /**
     * 最后更改时间
     **/
    private java.util.Date datachangeLasttime;

    /**
     * 创建时间
     **/
    private java.util.Date datachangeCreatetime;

    public HoRoomDailyBo convertHoRoomDailyBo() {
        HoRoomDailyBo hoRoomDailyBo = new HoRoomDailyBo();
        BeanUtils.copyProperties(this, hoRoomDailyBo);
        return hoRoomDailyBo;
    }

}
