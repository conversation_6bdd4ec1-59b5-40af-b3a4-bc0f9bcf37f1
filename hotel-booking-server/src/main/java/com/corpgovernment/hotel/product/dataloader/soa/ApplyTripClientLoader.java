package com.corpgovernment.hotel.product.dataloader.soa;

import cn.hutool.core.util.ObjectUtil;
import com.corpgovernment.api.applytrip.enums.ApplyTripTempCostTypeEnum;
import com.corpgovernment.api.applytrip.request.applytrip.TripTempCostRequest;
import com.corpgovernment.api.applytrip.soa.ApplyTripClient;
import com.corpgovernment.api.applytrip.soa.request.*;
import com.corpgovernment.api.applytrip.soa.response.ApplyTripPersonResponse;
import com.corpgovernment.api.applytrip.soa.response.ApplyTripTrafficVerifyResponse;
import com.corpgovernment.api.applytrip.soa.response.GetProjectInfoByTravelerResponse;
import com.corpgovernment.api.applytrip.soa.response.QueryApplyTripStandardResponse;
import com.corpgovernment.api.applytrip.traffic.request.TripTrafficRequest;
import com.corpgovernment.api.applytrip.traffic.response.HotelTripResponse;
import com.corpgovernment.api.applytrip.vo.AoApplyTripPersonVo;
import com.corpgovernment.api.applytrip.vo.AoApplyTripStockVo;
import com.corpgovernment.api.applytrip.vo.AoApplyTripTempCostVo;
import com.corpgovernment.api.applytrip.vo.AoApplyTripTrafficVo;
import com.corpgovernment.api.order.common.dos.OcApplyTripControlRecord;
import com.corpgovernment.api.order.common.mapper.OcApplyTripControlRecordMapper;
import com.corpgovernment.basic.bo.response.TravelStandardResponseBO;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.convert.TravelStandardConvert;
import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.client.ManagementClientUtil;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ApplyTripClientLoader {

	@Autowired
	private ApplyTripClient applyTripClient;
	@Autowired
	private ManagementClientUtil managementClientUtil;
	@Autowired
	private CoreServiceClient coreServiceClient;
	@Autowired
	private OcApplyTripControlRecordMapper ocApplyTripControlRecordMapper;

	public List<ResourcesVerifyResponse> verifyTravelStandard(VerifyTravelStandardRequest request){
		log.info("校验酒店是否超标, verifyTravelStandard,request:{}", JsonUtils.toJsonString(request));
		JSONResult<List<ResourcesVerifyResponse>> result = coreServiceClient.verifyTravelStandard(request);
		log.info("校验酒店是否超标, verifyTravelStandard,response:{}", JsonUtils.toJsonString(result));
		if (result == null || result.getData() == null) {
			throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_CHECK_HOTEL_ROOM_FEGIN);
		}
		return result.getData();
	}

	public List<TravelStandardResponseBO> getTravelStandardByToken(GetTravelStandardByTokenRequest request) {
		log.info("获取酒店同住差旅标准by token, getTravelStandardByToken,request:{}", JsonUtils.toJsonString(request));
		List<TravelStandardResponse> travelStandardResponseList= managementClientUtil.getTravelStandardByToken(request);
		log.info("获取酒店同住差旅标准by token, getTravelStandardByToken,reponse:{}", JsonUtils.toJsonString(travelStandardResponseList));
		if (ObjectUtil.isNull(travelStandardResponseList)) {
			throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_CHECK_HOTEL_ROOM_FEGIN);
		}

		List<TravelStandardResponseBO> travelStandardResponseBOList = TravelStandardConvert.convert(travelStandardResponseList);
		log.info("获取酒店同住差旅标准 getTravelStandardToken,convert:{}", JsonUtils.toJsonString(travelStandardResponseBOList));
		return travelStandardResponseBOList;
	}

	/**
	 * 消耗出差申请单
	 *
	 * @param request
	 * @return
	 */
	public boolean updateApplyTripUseStatus(UseApplyTripTrafficRequest request) {
		if (request == null) {
			return false;
		}
		JSONResult<Boolean> result = applyTripClient.updateApplyTripUseStatus(request);
		if (result == null || result.getData() == null) {
			log.error("消耗出差申请单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return false;
		}
		return result.getData();
	}

	/**
	 * 消耗出差申请单并记录
	 */
	public boolean updateApplyTripUseStatusAndRecord(UseApplyTripTrafficRequest request, OcApplyTripControlRecord controlRecord) {
		log.info("消耗出差申请单请求参数为：{}", JsonUtils.toJsonString(request));
		if (request == null) {
			return false;
		}
		JSONResult<Boolean> result = null;
		try {
			result = applyTripClient.updateApplyTripUseStatus(request);
			if (result == null || result.getData() == null) {
				log.error("消耗出差申请单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
				return false;
			}
		} catch (Exception e) {
			log.error("消耗出差申请单异常", e);
			result = JSONResult.errorMsg(e.getMessage());
		} finally {
			saveControlRecord(request, controlRecord, result);
			log.info("申请单管控记录插入完成");
		}
		return result.getData();
	}

	/**
	 * 回滚出差申请单
	 *
	 * @param request
	 * @return
	 */
	public boolean rollbackApplyTripUseStatus(UseApplyTripTrafficRequest request) {
		if (request == null) {
			return false;
		}
		JSONResult<Boolean> result = applyTripClient.rollbackApplyTripUseStatus(request);
		if (result == null || result.getData() == null) {
			log.error("回滚出差申请单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return false;
		}
		return result.getData();
	}

	/**
	 * 回滚出差申请单并记录调用
	 *
	 * @param request 回滚出差申请单参数
	 * @param controlRecord 管控记录
	 */
	public boolean rollbackApplyTripUseStatusAndRecord(UseApplyTripTrafficRequest request, OcApplyTripControlRecord controlRecord) {
		log.info("回滚出差申请单请求参数为：{}", JsonUtils.toJsonString(request));
		if (request == null) {
			return false;
		}
		JSONResult<Boolean> result = null;
		try {
			result = applyTripClient.rollbackApplyTripUseStatus(request);
			if (result == null || result.getData() == null) {
				log.error("回滚出差申请行程的使用情况异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
				return false;
			}
		} catch (Exception e) {
			log.error("回滚出差申请单异常", e);
			result = JSONResult.errorMsg(e.getMessage());
		} finally {
			saveControlRecord(request, controlRecord, result);
			log.info("申请单管控记录插入完成");
		}
		return result.getData();
	}


	private void saveControlRecord(UseApplyTripTrafficRequest request, OcApplyTripControlRecord controlRecord, JSONResult<Boolean> result) {
		try {
			controlRecord.setAmount(request.getAmount());
			controlRecord.setApplyNo(request.getApplyNo());
			controlRecord.setTrafficNo(request.getTrafficId());
			controlRecord.setOrderId(Long.parseLong(request.getOrderId()));
			controlRecord.setRequestParam(JsonUtils.toJsonString(request));
			controlRecord.setResponse(JsonUtils.toJsonString(result));
			ocApplyTripControlRecordMapper.insertSelective(controlRecord);
		} catch (Exception e) {
			log.error("申请单管控记录插入异常：" + e.getMessage(), e);
		}
	}

	/**
	 * 酒店产线出差申请列表
	 *
	 * @param request
	 * @return
	 */
	public List<HotelTripResponse> listInitHotelTripTraffics(TripTrafficRequest request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<HotelTripResponse>> result = applyTripClient.listInitHotelTripTraffics(request);
		if (result == null || result.getData() == null) {
			log.error("酒店产线出差申请列表异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 根据出差单和入住人 查询出当前入住人所选择得项目信息
	 *
	 * @param request
	 * @return
	 */
	public List<GetProjectInfoByTravelerResponse> getProjectInfoByTraveler(GetProjectInfoByTravelerRequest request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<GetProjectInfoByTravelerResponse>> result = applyTripClient.getProjectInfoByTraveler(request);
		if (result == null || result.getData() == null) {
			log.error("消耗出差申请单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		log.info("根据出差单号和入住人uid查询项目数据:{}", result.getData());
		return result.getData();
	}

	public boolean updateApplyTripWbsAndCostRemake(ApplyTripUpdateRemakeRequest request) {
		if (request == null) {
			return false;
		}
		JSONResult<Boolean> result = applyTripClient.updateApplyTripWbsAndCostRemake(request);
		if (result == null || result.getData() == null) {
			log.error("修改WBS和成本中心备注:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return false;
		}
		return result.getData();
	}

	public List<ApplyTripPersonResponse> getApplyTripPerson(ApplyTripPersonRequest request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<ApplyTripPersonResponse>> result = applyTripClient.getApplyTripPerson(request);
		if (result == null || result.getData() == null) {
			log.error("查询出差申请单中出行人:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 根据行程号查询差标
	 *
	 * @param trafficId 行程号
	 */
	public QueryApplyTripStandardResponse getHotelApplyTripStandard(Long trafficId) {
		if (trafficId == null) {
			return new QueryApplyTripStandardResponse();
		}

		QueryApplyTripStandardRequest request = new QueryApplyTripStandardRequest();
		request.setTrafficId(trafficId);

		JSONResult<QueryApplyTripStandardResponse> response = applyTripClient.queryStandardInfo(request);
		if (response == null || response.getData() == null) {
			log.error("查询出差申请单中差标失败:" + Optional.ofNullable(response).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}

		return response.getData();
	}

	public AoApplyTripTrafficVo getApplyTripTrafficById(Long trafficId){
		if (null == trafficId){
			return null;
		}
		try {
			return applyTripClient.getApplyTripTrafficById(trafficId);
		} catch (Exception e){
			log.error("根据行程ID查询行程信息", e);
		}
		return null;
	}

	public ApplyTripTrafficVerifyResponse getApplyTrafficVerify(Long trafficId){
		if(trafficId==null){
			return null;
		}
		JSONResult<ApplyTripTrafficVerifyResponse> result = applyTripClient.getApplyTrafficVerify(trafficId);
		if(result==null || result.getData()==null){
			log.error("查询行程计划校验信息:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
		}
		return result.getData();
	}

	public AoApplyTripStockVo getApplyTrafficStock(Long trafficId){
		if(trafficId==null){
			return null;
		}
		JSONResult<AoApplyTripStockVo> result = applyTripClient.getApplyTrafficStock(trafficId);
		if(result==null || result.getData()==null){
			log.error("查询行程计划校验信息:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
		}
		return result.getData();
	}

	/**
	 * 查询
	 * @param urgentApply
	 * @param corpId
	 * @return
	 */
	public List<AoApplyTripTempCostVo> getTripTempCostSoa(Boolean urgentApply, String corpId) {
		if(corpId==null){
			return null;
		}
		// 查询多成本中心配置
		Integer costType = ApplyTripTempCostTypeEnum.TRAVEL.getCode();
		if(Boolean.TRUE.equals(urgentApply)) {
			costType = ApplyTripTempCostTypeEnum.URGENT.getCode();
		}
		TripTempCostRequest costRequest = new TripTempCostRequest();
		costRequest.setTempType(costType);
		costRequest.setOrgId(corpId);
		JSONResult<List<AoApplyTripTempCostVo>> result = applyTripClient.getTripTempCostSoa(costRequest);
		if (result == null || result.getData() == null) {
			log.error("查询出行人多成本中心配置信息:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 根据申请单信息查询出行人信息
	 * @param request
	 * @return
	 */
	public List<AoApplyTripPersonVo> personListByApplyInfo(QueryApplyTripPersonRequest request) {
		if (request == null) {
			return new ArrayList<>();
		}
		log.info("查询出行人拓展信息请求参数为：{}", JsonUtils.toJsonString(request));
		JSONResult<List<AoApplyTripPersonVo>> result = applyTripClient.personListByApplyInfo(request);
		log.info("查询出行人拓展信息结果为：{}", JsonUtils.toJsonString(result));
		if (result == null || result.getData() == null) {
			log.error("查询出行人拓展信息异常:{}", Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

}
