package com.corpgovernment.hotel.product.external.dto.order.detail;

import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
/**
 * 美亚契约 -订单详情请求类
 */
@Data
public class MeiyaOrderDetailRequest extends BaseExternalRequest {

    @JsonProperty(value = "OrderID")
    private String supplierOrderId;
    /**
     * 供应商公司ID
     */
    @JsonIgnore
    private String supplierCorpId;
}
