package com.corpgovernment.hotel.product.model.ctrip.response.detail;

import lombok.Data;

/**
 * @ClassName: CancelationPolicy
 * @description: TODO
 * @author: zdwang
 * @date: Created in 19:05 2019/8/23
 * @Version: 1.0
 **/
@Data
public class CancelationPolicy {
    /**
     * CancelationPolicy
     * 字段说明
     * 0: None 无效值,未知
     * 1: FreeCancelation 免费取消
     * 2: LimitedCancelation 限时取消, 需要从       RoomDataEntity.LastCancelTime获取最晚时间信息
     * 4:OverTimeGuaranteeLimitedCancelation 超时担保,有限制取消, 需要从       RoomDataEntity.LastCancelTime获取最晚时间信息
     * 8: CanNotCancelation 不能取消
     * 1和2表示免费取消
     */
    private Integer policyType;
}