package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.costcenter.model.CostCenter;
import com.corpgovernment.api.costcenter.soa.ICostCenterClient;
import com.corpgovernment.api.organization.dto.response.business.unit.BusinessUnitSimpleDto;
import com.corpgovernment.common.base.JSONResult;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CostCenterClientLoader {

	@Autowired
	private ICostCenterClient costCenterClient;

	/**
	 * 获取成本中心信息
	 *
	 * @param orgId
	 * @return
	 */
	public List<CostCenter> getCostCenter(String orgId) {
		if (StringUtils.isBlank(orgId)) {
			return null;
		}
		JSONResult<List<CostCenter>> result = costCenterClient.getCostCenter(orgId);
		if (result == null || result.getData() == null) {
			log.error("获取成本中心异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 根据成本中心code查询成本中心信息
	 * @param costCenterCodeList
	 * @return
	 */
	public Map<String, BusinessUnitSimpleDto> queryCostCenterByCodes(List<String> costCenterCodeList) {
		if (CollectionUtils.isEmpty(costCenterCodeList)) {
			return Collections.emptyMap();
		}

		JSONResult<List<CostCenter>> costCenterByCodeList = costCenterClient.getCostCenterByCodeList(costCenterCodeList);
		if (costCenterByCodeList == null || costCenterByCodeList.getData() == null) {
			log.error("根据成本中心code查询成本中心信息异常:" + Optional.ofNullable(costCenterByCodeList).map(JSONResult::getMsg).orElse("接口无响应"));
			return Collections.emptyMap();
		}

		return Optional.ofNullable(costCenterByCodeList.getData())
				.orElse(Collections.emptyList())
				.stream()
				.filter(t-> StringUtils.isNotBlank(t.getCostCenterCode()))
				.map(k -> {
					BusinessUnitSimpleDto businessUnitSimpleDto = new BusinessUnitSimpleDto();
					businessUnitSimpleDto.setCode(k.getCorporationCode());
					businessUnitSimpleDto.setName(k.getCorporationName());
					businessUnitSimpleDto.setSupplierAccountId(k.getCorporationSupplierAccountId());
					return new AbstractMap.SimpleEntry<>(k.getCostCenterCode(), businessUnitSimpleDto);
				})
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1));
	}
}
