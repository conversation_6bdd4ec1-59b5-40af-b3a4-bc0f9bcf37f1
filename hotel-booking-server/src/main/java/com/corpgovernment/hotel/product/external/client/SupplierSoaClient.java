package com.corpgovernment.hotel.product.external.client;

import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.soa.response.SupplierProductGetRespVo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.common.ValidGroup;
import com.corpgovernment.hotel.booking.metric.MetricService;
import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.StandardReserveOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.StandardReserveOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.*;
import com.corpgovernment.hotel.product.external.dto.order.confirm.StandardConfirmOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.confirm.StandardConfirmOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.*;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 外部供应商调用客户端
 */
@Slf4j
@Component
public class SupplierSoaClient {

    @Resource
    private CommonService commonService;
    @Resource
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    @Resource
    private Map<String, HotelSupplierClient> hotelSupplierContractMap;

    @Autowired
    private MetricService metricService;

    /**
     * 预定下单
     *
     * @param request 预定下单请求类
     * @return 预定下单响应类
     */
    public StandardReserveOrderResponse reserveOrder(StandardReserveOrderRequest request) {
        log.info("供应商下单流程 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductBo supplierProduct = getSupplierProduct(request, hotelOperatorTypeConfig.getBookOrder());

        // 赋供应商公司ID
        StandardReserveOrderRequest.OrderBasicInfo orderBasicInfo = request.getOrderBasicInfo();
        orderBasicInfo.setCorpID(supplierProduct.getSupplierCorpId());
        request.setSupplierUid(supplierProduct.getSupplierUid());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());
        StandardReserveOrderResponse response = hotelSupplierClient.reserveOrder(request);
        log.info("供应商下单流程 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        return response;
    }

    /**
     * 授权-确认酒店
     *
     * @param request 确认订单请求类
     * @return 确认订单响应类
     */
    public StandardConfirmOrderResponse confirmOrder(StandardConfirmOrderRequest request) {
        log.info("供应商授权-确认酒店 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductBo supplierProduct = getSupplierProduct(request, hotelOperatorTypeConfig.getConfirmOrder());

        // 赋供应商公司ID
        request.setCorpID(supplierProduct.getSupplierCorpId());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());
        StandardConfirmOrderResponse response = hotelSupplierClient.confirmOrder(request);
        log.info("供应商授权-确认酒店 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        return response;
    }

    /**
     * 订单详情
     *
     * @param request 订单详情请求
     * @return 订单详情
     *
     */
    public StandardOrderDetailResponse orderDetail(StandardOrderDetailRequest request,Class<? extends ValidGroup>... groups) {
        log.info("供应商订单详情 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductGetRespVo supplierProductV2 = getSupplierProductV2(request, hotelOperatorTypeConfig.getSearchOrder());

        // 通过企业订购 赋值供应商公司ID
        request.setCorpID(supplierProductV2.getSupplierCorpId());
        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());
        StandardOrderDetailResponse response = hotelSupplierClient.orderDetail(request);
        log.info("供应商订单详情 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        metricService.metricSupplierReturnInfoValid(request.getOrderID(), request.getSupplierCode(), response, "订单详情", groups);
        return response;
    }

    /**
     * 订单修改问询
     *
     * @param request 订单修改问询请求
     * @return 订单修改问询返回
     */
    public StandardOrderModificationInquiryResponse
        queryOrderModification(StandardOrderModificationInquiryRequest request) {
        log.info("供应商订单修改问询 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductGetRespVo supplierProductV2 = getSupplierProductV2(request, hotelOperatorTypeConfig.getModifyOrderQuery());

        // 通过企业订购 赋值供应商公司ID
        request.setCorpID(supplierProductV2.getSupplierCorpId());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());
        StandardOrderModificationInquiryResponse response = hotelSupplierClient.queryOrderModification(request);
        log.info("供应商订单修改问询 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        return response;
    }

    /**
     * 订单修改间夜查询
     *
     * @param request 订单修改问询请求
     * @return 订单修改问询返回
     */
    public StandardOrderModifiableRoomNightQueryResponse
    queryOrderModifiableRoomNight(StandardOrderModifiableRoomNightQueryRequest request) {
        // 获取供应商产品
        SupplierProductGetRespVo supplierProduct = getSupplierProductV2(request, hotelOperatorTypeConfig.getModifiableRoomNightQuery());

        // 通过企业订购 赋值供应商公司ID
        request.setCorpID(supplierProduct.getSupplierCorpId());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());

        log.info("供应商订单修改间夜查询 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        StandardOrderModifiableRoomNightQueryResponse response = hotelSupplierClient.queryOrderModifiableRoomNight(request);
        log.info("供应商订单修改间夜查询 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        return response;
    }

    /**
     * 订单修改申请
     *
     * @param request 订单修改申请请求
     * @return 订单修改申请返回
     */
    public StandardOrderModificationResponse createOrderModification(StandardOrderModificationRequest request) {
        log.info("供应商订单修改申请 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductGetRespVo supplierProductV2 = getSupplierProductV2(request, hotelOperatorTypeConfig.getModifyOrder());

        // 通过企业订购 赋值供应商公司ID
        request.setCorpID(supplierProductV2.getSupplierCorpId());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());

        StandardOrderModificationResponse response = hotelSupplierClient.createOrderModification(request);
        log.info("供应商订单修改申请 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        return response;
    }

    /**
     * 订单修改详情
     *
     * @param request 订单修改详情请求
     * @return 订单修改详情返回
     */
    public StandardOrderModificationDetailResponse
        orderModificationDetails(StandardOrderModificationDetailRequest request, Class<? extends ValidGroup>... groups) {
        log.info("供应商订单修改详情 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductGetRespVo supplierProductV2 = getSupplierProductV2(request, hotelOperatorTypeConfig.getModifyOrderDetail());

        // 通过企业订购 赋值供应商公司ID
        request.setCorpID(supplierProductV2.getSupplierCorpId());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());
        StandardOrderModificationDetailResponse response = hotelSupplierClient.orderModificationDetails(request);
        log.info("供应商订单修改详情 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        metricService.metricSupplierReturnInfoValid(request.getOrderID(), request.getSupplierCode(), response, "订单修改详情", groups);
        return response;
    }

    /**
     * 取消订单问询
     *
     * @param request 取消订单问询请求类
     * @return 取消订单问询响应类
     */
    public StandardCancelOrderInquiryResponse cancelOrderInquiry(StandardCancelOrderInquiryRequest request) {
        log.info("供应商取消订单问询 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductGetRespVo supplierProductV2 = getSupplierProductV2(request, hotelOperatorTypeConfig.getCancelOrderQuery());

        // 赋供应商公司ID
        request.setCorpID(supplierProductV2.getSupplierCorpId());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());
        StandardCancelOrderInquiryResponse response = hotelSupplierClient.cancelOrderInquiry(request);
        log.info("供应商取消订单问询 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        return response;
    }

    /**
     * 取消订单
     *
     * @param request 取消订单请求类
     * @return 取消订单响应类
     */
    public StandardCancelOrderResponse cancelOrder(StandardCancelOrderRequest request) {
        log.info("供应商取消订单 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductGetRespVo supplierProductV2 = getSupplierProductV2(request, hotelOperatorTypeConfig.getCancelOrder());

        // 赋供应商公司ID
        request.setCorpID(supplierProductV2.getSupplierCorpId());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());
        StandardCancelOrderResponse response = hotelSupplierClient.cancelOrder(request);
        log.info("供应商取消订单 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        return response;
    }

    /**
     * 订单取消详情
     *
     * @param request 订单取消详情请求
     * @return 订单取消详情返回
     */
    public StandardCancelOrderDetailResponse cancelOrderDetail(StandardCancelOrderDetailRequest request) {
        log.info("供应商订单取消详情 标准契约请求参数:{}", JsonUtils.toJsonString(request));
        // 获取供应商产品
        SupplierProductGetRespVo supplierProductV2 = getSupplierProductV2(request, hotelOperatorTypeConfig.getCancelOrderDetail());

        // 赋供应商公司ID
        request.setCorpID(supplierProductV2.getSupplierCorpId());

        // 获取相应实现类并调用
        HotelSupplierClient hotelSupplierClient = getHotelSupplierContact(request.getSupplierCode());
        StandardCancelOrderDetailResponse response = hotelSupplierClient.cancelOrderDetail(request);
        log.info("供应商订单取消详情 标准契约响应参数:{}", JsonUtils.toJsonString(response));
        return response;
    }

    /**
     * 获取供应商产品
     *
     * @param request 基础对外请求类
     * @param operatorType 操作类型
     * @return 供应商产品
     */
    private SupplierProductBo getSupplierProduct(BaseExternalRequest request, String operatorType) {
        SupplierProductBo supplierProduct = commonService.getSupplierProduct(request.getCompanyCode(),
            request.getSupplierCode(), operatorType, request.getCorpPayType());

        Optional.ofNullable(supplierProduct).orElseThrow(() -> {
            log.error(String.format("未匹配到相应供应商:[%s],", request.getSupplierCode()));
            return new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUPPLIER_INFORMATION);
        });
        request.setProductUrl(supplierProduct.getProductUrl());
        request.setUserKey(supplierProduct.getUserKey());
        return supplierProduct;
    }

    /**
     * 获取供应商产品 V2
     *
     * @param request 基础对外请求类
     * @param operatorType 操作类型
     * @return 供应商产品
     */
    private SupplierProductGetRespVo getSupplierProductV2(BaseExternalRequest request, String operatorType) {
        SupplierProductGetRespVo supplierProductV2 = commonService.getSupplierProductV2(request.getCompanyCode(),
            request.getSupplierCode(), operatorType);

        Optional.ofNullable(supplierProductV2).orElseThrow(() -> {
            log.error(String.format("未匹配到相应供应商:[%s],", request.getSupplierCode()));
            return new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUPPLIER_INFORMATION);
        });

        request.setProductUrl(supplierProductV2.getProductUrl());
        request.setUserKey(supplierProductV2.getUserKey());
        return supplierProductV2;
    }

    /**
     * 获取供应商适配
     *
     * @param supplierCode 供应商编码
     * @return 酒店供应商契约实现类
     */
    private HotelSupplierClient getHotelSupplierContact(String supplierCode) {
        // 拼接：供应商编码 + SupplierClientAdapter
        String strategyName =
            String.format("%s%s", supplierCode.toLowerCase(), SupplierConstant.SUPPLIER_CLIENT_ADAPTER);
        // 未获取到返回标准处理器
        return hotelSupplierContractMap.getOrDefault(strategyName,
            hotelSupplierContractMap.get(SupplierConstant.STANDARD_SUPPLIER_CLIENT));
    }
}
