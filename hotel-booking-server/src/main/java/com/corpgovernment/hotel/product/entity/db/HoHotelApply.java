package com.corpgovernment.hotel.product.entity.db;

import com.corpgovernment.common.entity.db.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 *
 **/
@Data
@Table(name = "ho_hotel_apply")
public class HoHotelApply extends BaseEntity implements Serializable {

    /**
     * 订单号
     **/
    private Long orderId;
    /**
     * 申请单号
     **/
    private String applyId;
    /**
     * 修改单状态
     */
    private Integer status;
    /**
     * 修改原因
     */
    private String reasonCode;
    /**
     * 原因说明
     */
    private String reasonDesc;

    /**
     * 退款金额
     */
    @Column(name = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 场景
     */
    @Column(name = "scene")
    private String scene;
}
