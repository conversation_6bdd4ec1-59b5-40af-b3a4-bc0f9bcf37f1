package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.organization.model.switchinfo.GetAllSwitchResponse;
import com.corpgovernment.api.organization.model.switchinfo.GetSwitchListRequest;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoRequest;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoResponse;
import com.corpgovernment.api.organization.soa.SwitchClient;
import com.corpgovernment.common.base.JSONResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SwitchClientLoader {

	@Autowired
	private SwitchClient switchClient;

	/**
	 * 获取所有开关信息
	 *
	 * @param request
	 * @return
	 */
	public GetAllSwitchResponse allSwitch(GetSwitchListRequest request) {
		if (request == null) {
			return null;
		}
		JSONResult<GetAllSwitchResponse> result = switchClient.allSwitch(request);
		if (result == null || result.getData() == null) {
			log.error("获取所有开关信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 获取用户的支付配置信息
	 *
	 * @param request
	 * @return
	 */
	public List<PayInfoResponse> getUserPayInfo(PayInfoRequest request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<PayInfoResponse>> result = switchClient.getUserPayInfo(request);
		if (result == null || CollectionUtils.isEmpty(result.getData())) {
			log.error("获取支付配置信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 获取所有开关信息
	 *
	 * @param request
	 * @return
	 */
	public Map<String, Object> getSwitchValueMap(GetSwitchListRequest request) {
		if (request == null) {
			return null;
		}
		JSONResult<Map<String, Object>> result = switchClient.getSwitchValueMapSoa(request);
		if (result == null || result.getData() == null) {
			log.error("获取开关Map信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}
}
