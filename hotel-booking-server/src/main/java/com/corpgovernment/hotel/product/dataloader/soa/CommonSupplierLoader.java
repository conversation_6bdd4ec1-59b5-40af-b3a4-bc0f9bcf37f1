package com.corpgovernment.hotel.product.dataloader.soa;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.corpgovernment.api.car.vo.subvo.SubAddressVo;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo.ExtInfo;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo.LocalOptionalRemark;
import com.corpgovernment.api.hotel.product.model.bookorder.response.LocalBookOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.*;
import com.corpgovernment.api.hotel.product.model.enums.HotelTypeEnum;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelDetailRequestBo;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelListRequestBo;
import com.corpgovernment.api.hotel.product.model.request.Tree;
import com.corpgovernment.api.hotel.product.model.request.Tree.Kv;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO.ReasonInfo;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO.*;
import com.corpgovernment.api.hotel.product.model.response.LadderDeductionEntity;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoResponse;
import com.corpgovernment.api.organization.utils.UuidUtil;
import com.corpgovernment.api.supplier.bo.suppliercompany.InitResponseBo;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.soa.constant.ProductTypeEnum;
import com.corpgovernment.api.supplier.soa.response.MbSupplierProductResponse;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.vo.*;
import com.corpgovernment.api.travelstandard.vo.response.AverageBrandSet;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.constant.HotelStarLicenceEnum;
import com.corpgovernment.basic.impl.HotelBasicDataService;
import com.corpgovernment.basicdata.bo.HotelCityBo;
import com.corpgovernment.basicdata.entity.db.BdHpHotelAreaSupplierEntity;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.MonitorLevelEnums;
import com.corpgovernment.common.enums.MonitorTypeEnums;
import com.corpgovernment.common.shunt.ShuntConfigDao;
import com.corpgovernment.common.utils.*;
import com.corpgovernment.common.shunt.ShuntConfigDao;
import com.corpgovernment.common.utils.BaseUtils;
import com.corpgovernment.common.utils.BigDecimalUtil;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.common.utils.HttpUtils;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.dao.apollo.impl.HotelCoreApolloDao;
import com.corpgovernment.core.dao.dataobject.HotelBonusPointInfoDo;
import com.corpgovernment.core.service.IHotelBonusPointService;
import com.corpgovernment.hotel.booking.bo.ApplyTripControlBo;
import com.corpgovernment.hotel.booking.bo.ApplyTripItemBo;
import com.corpgovernment.core.dao.dataobject.HotelBonusPointInfoDo;
import com.corpgovernment.core.service.IHotelBonusPointService;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.StarRuleVO;
import com.corpgovernment.hotel.booking.bo.ApplyTripControlBo;
import com.corpgovernment.hotel.booking.bo.ApplyTripItemBo;
import com.corpgovernment.hotel.booking.cache.HotelInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.model.HotelInfoModel;
import com.corpgovernment.hotel.booking.enums.AvailableVatInvoiceTypeEnum;
import com.corpgovernment.hotel.booking.enums.BalanceTypeEnum;
import com.corpgovernment.hotel.booking.enums.CancelRuleEnum;
import com.corpgovernment.hotel.booking.enums.HotelStarTypeEnum;
import com.corpgovernment.hotel.booking.enums.LadderDeductionTypeEnum;
import com.corpgovernment.hotel.booking.enums.LanguageEnum;
import com.corpgovernment.hotel.booking.request.SearchAddressRequest;
import com.corpgovernment.hotel.booking.request.SearchAddressSupplierRequest;
import com.corpgovernment.hotel.booking.service.ApplyTripService;
import com.corpgovernment.hotel.booking.service.TravelStandardService;
import com.corpgovernment.hotel.booking.enums.*;
import com.corpgovernment.hotel.booking.request.SearchAddressRequest;
import com.corpgovernment.hotel.booking.request.SearchAddressSupplierRequest;
import com.corpgovernment.hotel.booking.service.ApplyTripService;
import com.corpgovernment.hotel.booking.service.TravelStandardService;
import com.corpgovernment.hotel.booking.util.MetricsUtil;
import com.corpgovernment.hotel.booking.vo.SearchAddressResponse;
import com.corpgovernment.hotel.booking.vo.SearchAddressSupplierResponse;
import com.corpgovernment.hotel.product.constant.DetailRoomModalStatusEnum;
import com.corpgovernment.hotel.product.external.dto.order.synccostcenter.CtripSyncOrderCostCenterRequest;
import com.corpgovernment.hotel.product.external.dto.order.synccostcenter.CtripSyncOrderCostCenterResponse;
import com.corpgovernment.hotel.booking.vo.SearchAddressResponse;
import com.corpgovernment.hotel.booking.vo.SearchAddressSupplierResponse;
import com.corpgovernment.hotel.product.external.dto.order.synccostcenter.CtripSyncOrderCostCenterRequest;
import com.corpgovernment.hotel.product.external.dto.order.synccostcenter.CtripSyncOrderCostCenterResponse;
import com.corpgovernment.hotel.product.model.ctrip.request.CityRequest;
import com.corpgovernment.hotel.product.model.ctrip.response.staticinfo.CityResponse;
import com.corpgovernment.hotel.product.model.ctrip.v2.*;
import com.corpgovernment.hotel.product.model.ctrip.wrap.WrapCheckAvailV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.wrap.WrapGetHotelDataV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.wrap.WrapGetHotelDetailV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.v2.AddBedEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.BaseEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.BasicRoomInfo;
import com.corpgovernment.hotel.product.model.ctrip.v2.BedInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.BookOrderRemark;
import com.corpgovernment.hotel.product.model.ctrip.v2.BookOrderV2RequestType;
import com.corpgovernment.hotel.product.model.ctrip.v2.BookOrderV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.v2.BookingRulesInfo;
import com.corpgovernment.hotel.product.model.ctrip.v2.CancelRuleInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.ChargingPointEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.CheckAvailV2RequestType;
import com.corpgovernment.hotel.product.model.ctrip.v2.CheckAvailV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.v2.CheckRoomEntityV2;
import com.corpgovernment.hotel.product.model.ctrip.v2.ChildBedInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.ChildPolicyEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.Client;
import com.corpgovernment.hotel.product.model.ctrip.v2.CommonInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.Contactor;
import com.corpgovernment.hotel.product.model.ctrip.v2.CorpOrderInfo;
import com.corpgovernment.hotel.product.model.ctrip.v2.CreateOrderExtInfo;
import com.corpgovernment.hotel.product.model.ctrip.v2.CreateRoom;
import com.corpgovernment.hotel.product.model.ctrip.v2.DailyRatesEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.DetailBaseInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.FacilityDetailEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.FacilityEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.FacilityInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.FacilityInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.FacilityItemEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.FacilityListEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.GeoCommonEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.GetHotelDataV2RequestType;
import com.corpgovernment.hotel.product.model.ctrip.v2.GetHotelDataV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.v2.GetHotelDetailV2RequestType;
import com.corpgovernment.hotel.product.model.ctrip.v2.GetHotelDetailV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelBaseInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelBrandEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelBrandGroupFilterType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelCommentInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelContactInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelDetailInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelFacilitiesFilterType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelFacilitiesInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelFilterType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelGeoInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelInfoFilterType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelPicEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelPositionFilterType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelRatePlanEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelReviewInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelStarEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelStaticInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelTagInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.HotelTagType;
import com.corpgovernment.hotel.product.model.ctrip.v2.IdNameType;
import com.corpgovernment.hotel.product.model.ctrip.v2.ImportantNotifyEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.IntroductionEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.Invoice;
import com.corpgovernment.hotel.product.model.ctrip.v2.LadderDeductionDetailEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.LadderDeductionDetailEntityV2;
import com.corpgovernment.hotel.product.model.ctrip.v2.LadderDeductionInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.LastCancelTimeEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.MapSearchInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.MealPolicyEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.MinPriceRoomInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.MixPaymentWay;
import com.corpgovernment.hotel.product.model.ctrip.v2.MultipleLanguageText;
import com.corpgovernment.hotel.product.model.ctrip.v2.NearbyFacilityGroupEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.OptionalRemark;
import com.corpgovernment.hotel.product.model.ctrip.v2.PagingInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.ParkingServiceEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.PaymentInfo;
import com.corpgovernment.hotel.product.model.ctrip.v2.PictureInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.PolicyInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.PositionEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.PriceEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.PriceInfo;
import com.corpgovernment.hotel.product.model.ctrip.v2.ResponseStatus;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomFilterEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomFilterType;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomItemV2;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomMealEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomPolicyFilterType;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomPriceRangeType;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomStaticInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.SearchBaseInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.SearchResultType;
import com.corpgovernment.hotel.product.model.ctrip.v2.SortInfoType;
import com.corpgovernment.hotel.product.model.ctrip.v2.TrafficInfoGroupEntity;
import com.corpgovernment.hotel.product.model.ctrip.wrap.WrapCheckAvailV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.wrap.WrapGetHotelDataV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.wrap.WrapGetHotelDetailV2ResponseType;
import com.corpgovernment.hotel.product.model.gaode.AroundPlaceRespDto;
import com.corpgovernment.hotel.product.service.OrderMonitorService;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.corpgovernment.hotel.product.supplier.enums.HotelServiceEnum;
import com.corpgovernment.hotel.product.supplier.enums.InvoiceEnum;
import com.corpgovernment.hotel.product.supplier.enums.SupplierEnum;
import com.corpgovernment.hotel.product.supplier.enums.WindowTypeEnum;
import com.corpgovernment.mapping.bo.FacilityInfoBo;
import com.corpgovernment.mapping.bo.HmHotelDetailImport;
import com.corpgovernment.mapping.bo.HpSupplier;
import com.corpgovernment.mapping.mapper.HmHotelDetailImportMapper;
import com.corpgovernment.mapping.mapper.HpSupplierMapper;
import com.corpgovernment.orgsdk.client.ContentDicitionaryClient;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.corpgovernment.basic.constant.DestinationTypeEnum.AIRPORT;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.CORP_PLACE;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.HOTEL;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.HOTEL_BRAND;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.HOTEL_GROUP;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.INTL_AIRPORT;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.LANDMARK;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.METRO_STATION;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.RAILWAY_STATION;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.SCENIC_AREA;
import static com.corpgovernment.basic.constant.DestinationTypeEnum.ZONE;
import static com.corpgovernment.hotel.booking.enums.CancelRuleEnum.FREE;
import static com.corpgovernment.hotel.booking.enums.CancelRuleEnum.NOT_ALLOWED;
import static com.corpgovernment.hotel.booking.enums.CancelRuleEnum.TIME_LIMIT;
import static com.corpgovernment.hotel.booking.enums.FacilityTypeEnum.Airport_Pick_up;
import static com.corpgovernment.hotel.booking.enums.FacilityTypeEnum.FITNESS_CENTER;
import static com.corpgovernment.hotel.booking.enums.FacilityTypeEnum.PARKING;
import static com.corpgovernment.hotel.booking.enums.FacilityTypeEnum.RESTAURANT;
import static com.corpgovernment.hotel.booking.enums.FacilityTypeEnum.SWIMMING_POOL;
import static com.corpgovernment.hotel.booking.enums.FacilityTypeEnum.WIFI;
import static com.corpgovernment.hotel.booking.enums.MayTypeEnum.GAO_DE;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.corpgovernment.basic.constant.DestinationTypeEnum.*;
import static com.corpgovernment.hotel.booking.enums.CancelRuleEnum.*;
import static com.corpgovernment.hotel.booking.enums.FacilityTypeEnum.*;
import static com.corpgovernment.hotel.booking.enums.MayTypeEnum.GAO_DE;
/**
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class CommonSupplierLoader extends BaseService {
    // 共享变量
    public static ConcurrentHashMap<String, Map<String, List<BasicRoomInfo>>> SUPPLIER_REQUEST_INFO_MAP = new ConcurrentHashMap<>();
    // 详情页查询埋点
    Id queryDetailMetricId = Metrics.REGISTRY.createId("hotel.booking.queryDetail");
    // 高德周边搜索埋点
    private Id gaodeAroundPlaceMetricId = Metrics.REGISTRY.createId("gaode_around_place");

    private final Id NO_PRODUCT_METRIC_ID = Metrics.REGISTRY.createId("hotel.booking.noproduct");
    private final Id PRODUCT_METRIC_ID = Metrics.REGISTRY.createId("hotel.booking.exsitproduct");

    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private CommonService commonService;
    @Autowired
    private HpSupplierMapper supplierMapper;
    @Autowired
    private HotelInfoCacheManager hotelInfoCacheManager;
    @Autowired
    private HmHotelDetailImportMapper hotelDetailImportMapper;
    @Autowired
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    @Autowired
    private HotelBasicDataService hotelBasicDataService;
    @Autowired
    private OrderMonitorService orderMonitorService;
    @Autowired
    private ShuntConfigDao shuntConfigDao;
    @Autowired
    private IHotelBonusPointService hotelBonusPointService;

    private final static String FREE_CANCEL = "{0}之前可以免费取消；";
    private final static String LADDER_CANCEL = "{0}~{1}收取{2}元取消费；";
    private final static String CANNOT_CANCEL = "{0}之后不可取消";
    private final static String BREAKFAST_DESC = "{0}份早餐";
    /**
     * 因缺少关键传参而无结果
     */
    public static final String NO_RESULT_BY_PARAM = "noResultByParam";
    /**
     * 正常分销无结果
     */
    public static final String NO_RESULT_NORMAL = "noResultNormal";

    private final static String ROOM = "ROOM";
    // 因公
    private final static String PUB = "PUB";
    // 因私
    private final static String OWN = "OWN";
    // 公司支付
    private final static String ACCNT = "ACCNT";
    // 个人支付
    private final static String PPAY = "PPAY";
    private final static String EXCEED = "1";

    @Autowired
    private SupplierCompanyClientLoader supplierCompanyClientLoader;
    @Autowired
    private HotelCoreApolloDao hotelCoreApolloDao;

    @Autowired
    private TravelStandardService travelStandardService;
    @Autowired
    private ApplyTripService applyTripService;

    /**
     * ctrip同步成本中心
     *
     * @param request
     * @return
     */
    public CtripSyncOrderCostCenterResponse syncOrderCostCenter(@NotNull SupplierProductBo supplierProduct, @NotNull CtripSyncOrderCostCenterRequest request) {
        try {
            String productUrl = supplierProduct.getProductUrl();
            String supplierCode = supplierProduct.getSupplierCode();
            String userKey = supplierProduct.getUserKey();
            log.info("[REQUEST] 下单同步成本中心 - syncOrderCostCenter: {}", JsonUtils.toJsonString(request));
            String json = commonService.doPostJSON(supplierCode, "酒店详情查询", productUrl, userKey, JsonUtils.toJsonString(request));
            log.info("[RESPONSE] 下单同步成本中心 - syncOrderCostCenter: {}", json);
            return JsonUtils.parse(json, CtripSyncOrderCostCenterResponse.class);
        } catch (IOException e) {
            log.error("下单同步成本中心 - syncOrderCostCenter error: ", e);
        }
        return null;
    }

    /**
     * 酒店列表查询
     *
     * @param requestBo 酒店列表查询入参
     * @param supplierProduct 供应商产品实体
     * @param hotelCity 酒店城市信息
     * @return 酒店列表返回
     */
    public LocalHotelListResponseBo page(LocalHotelListRequestBo requestBo, SupplierProductBo supplierProduct,
        HotelCityBo hotelCity, Boolean ignoreFilter) {
        try {
            if (requestBo == null || supplierProduct == null) {
                return null;
            }
            // 查询酒店列表url
            String productUrl = supplierProduct.getProductUrl();
            String supplierCode = supplierProduct.getSupplierCode();
            String userKey = supplierProduct.getUserKey();
            log.info("酒店列表请求参数,requestBo:{},supplierProduct:{},hotelCity:{}", JsonUtils.toJsonString(requestBo),
                JsonUtils.toJsonString(supplierProduct), JsonUtils.toJsonString(hotelCity));
            GetHotelDataV2RequestType request = this.convertDataRequest(requestBo, supplierProduct, hotelCity);
            if (Boolean.TRUE.equals(ignoreFilter)) {
                if (request != null) {
                    request.setRoomFilterInfo(null);
                    request.setHotelFilterInfo(null);
                }
            }
            //无结果率入参校验
            boolean requestJudgement = this.judgeRequestParams(request);
            String paramSituation = requestJudgement ? NO_RESULT_NORMAL : NO_RESULT_BY_PARAM;
            String corpId = Optional.ofNullable(request).map(GetHotelDataV2RequestType::getBaseInfo).map(BaseEntity::getCorpId).orElse(null);

            //计时
            long hotelStartTime = System.currentTimeMillis();
            // 请求供应商
            String json = commonService.doPostJSON(supplierCode, "酒店列表查询", productUrl, userKey, JsonUtils.toJsonString(request));
            long hotelEndTime = System.currentTimeMillis();
            MetricsUtil.recordHotelProductTimeMetric(supplierCode, hotelEndTime - hotelStartTime);

            WrapGetHotelDataV2ResponseType response = JsonUtils.parse(json, WrapGetHotelDataV2ResponseType.class);
            if (response != null && (response.getData() != null || response.getCode() != null || response.getMsg() != null)) {
                log.info("走新契约");
                LocalHotelListResponseBo localHotelListResponseBo = this.convertDataResponse(response.getData(), requestBo, supplierProduct);
                //无结果率埋点
                List<LocalHotelListResponseBo.HotelListBean> hotelList = Optional.ofNullable(localHotelListResponseBo).map(LocalHotelListResponseBo::getHotelList).orElse(null);
                MetricsUtil.recordHotelProductResults(CollectionUtils.isEmpty(hotelList) ? NO_PRODUCT_METRIC_ID : PRODUCT_METRIC_ID, corpId, supplierCode, paramSituation);
                return localHotelListResponseBo;
            }
            else {
                log.info("走旧契约");
                LocalHotelListResponseBo localHotelListResponseBo = this.convertDataResponse(response, requestBo, supplierProduct);
                //无结果率埋点
                List<LocalHotelListResponseBo.HotelListBean> hotelList = Optional.ofNullable(localHotelListResponseBo).map(LocalHotelListResponseBo::getHotelList).orElse(null);
                MetricsUtil.recordHotelProductResults(CollectionUtils.isEmpty(hotelList) ? NO_PRODUCT_METRIC_ID : PRODUCT_METRIC_ID, corpId, supplierCode, paramSituation);
                return localHotelListResponseBo;
            }
        } catch (Exception e) {
            //无结果率埋点
            String corpId = Optional.ofNullable(requestBo).map(LocalHotelListRequestBo::getBaseCorpId).orElse(null);
            String supplierCode = Optional.ofNullable(supplierProduct).map(SupplierProductBo::getSupplierCode).orElse(null);
            MetricsUtil.recordHotelProductResults(NO_PRODUCT_METRIC_ID, corpId, supplierCode, null);
            log.error("获取供应商酒店数据失败：{}", e);
        }
        return null;
    }

    /**
     * 酒店详情
     *
     * @param requestBo 酒店详情入参
     * @param travelStandard 差标
     * @param reasonInfos rc原因
     * @param supplierProduct 供应商产品实体
     * @param payInfo 支付信息
     * @return 酒店详情返回
     */
    public HotelDetailResponseVO detail(LocalHotelDetailRequestBo requestBo, HotelControlVo travelStandard,
        List<HotelDetailResponseVO.ReasonInfo> reasonInfos,
        SupplierProductBo supplierProduct, List<PayInfoResponse> payInfo, List<ApplyTripItemBo> applyTripItemList) {
        GetHotelDetailV2ResponseType result = null;
        try {
            if (requestBo == null || supplierProduct == null) {
                Metrics.REGISTRY.counter(queryDetailMetricId.withTags("status", "failed")).increment();
                return null;
            }
            String productUrl = supplierProduct.getProductUrl();
            String supplierCode = supplierProduct.getSupplierCode();
            String userKey = supplierProduct.getUserKey();
            GetHotelDetailV2RequestType request = this.convertDetailRequest(requestBo, supplierProduct);
            String json = commonService.doPostJSON(supplierCode, "酒店详情查询", productUrl, userKey, JsonUtils.toJsonString(request));

            WrapGetHotelDetailV2ResponseType response = JsonUtils.parse(json, WrapGetHotelDetailV2ResponseType.class);
            if (response != null && (response.getData() != null || response.getCode() != null || response.getMsg() != null)) {
                log.info("走新契约");
                result = response.getData();
                return this.convertDetailResponse(response.getData(), requestBo, supplierProduct, travelStandard, reasonInfos, payInfo, applyTripItemList);
            }
            else {
                log.info("走旧契约");
                result = response;
                return this.convertDetailResponse(response, requestBo, supplierProduct, travelStandard, reasonInfos, payInfo, applyTripItemList);
            }
        } catch (Exception e) {
            log.error("获取供应商酒店详情数据失败,请求供应商:{}", supplierProduct.getSupplierCode());
            log.error("异常信息", e);
        } finally {
            reportData(supplierProduct, requestBo, result);
        }
        Metrics.REGISTRY.counter(queryDetailMetricId.withTags("status", "failed")).increment();
        return null;
    }

    private void reportData(SupplierProductBo supplierProduct, LocalHotelDetailRequestBo requestBo, GetHotelDetailV2ResponseType response) {
        try {
            if (requestBo == null || Boolean.TRUE.equals(requestBo.getBreakfast())
                    || Boolean.TRUE.equals(requestBo.getCheckImmediate())
                    || Boolean.TRUE.equals(requestBo.getProtatal())
                    || Boolean.TRUE.equals(requestBo.getFreeCancel())
                    || Boolean.TRUE.equals(requestBo.getOnlyBonusPoint())) {
                log.info("存在标签过滤，无需计算房型匹配率 request={}", requestBo);
                return;
            }
            if (response == null || response.getHotelRatePlan() == null || CollectionUtils.isEmpty(response.getHotelRatePlan().getBasicRoomList())) {
                log.info("酒店物理房型为空，无需计算房型匹配率 request={} response={}", requestBo, response);
                return;
            }
            String supplierCode = Optional.ofNullable(supplierProduct).map(SupplierProductBo::getSupplierCode).orElse("");
            String hotelId = Optional.ofNullable(response.getHotelDetailInfo()).map(HotelDetailInfoEntity::getHotelBaseInfo).map(DetailBaseInfoEntity::getHotelId).orElse("");
            if (StringUtils.isBlank(supplierCode) || StringUtils.isBlank(hotelId)) {
                log.info("supplierCode和hotelId不能为空");
                return;
            }
            synchronized (this) {
                String requestId = TraceContext.getRequestId();
                Map<String, List<BasicRoomInfo>> supplierRequestInfoMap = CommonSupplierLoader.SUPPLIER_REQUEST_INFO_MAP.get(requestId);
                List<BasicRoomInfo> basicRoomList = response.getHotelRatePlan().getBasicRoomList();
                if (supplierRequestInfoMap == null) {
                    supplierRequestInfoMap = new HashMap<>();
                }
                supplierRequestInfoMap.put(supplierCode + "_" + hotelId, basicRoomList);
                CommonSupplierLoader.SUPPLIER_REQUEST_INFO_MAP.put(requestId, supplierRequestInfoMap);
                log.info("supplierRequestInfoMap存储成功 CommonSupplierLoader.supplierRequestInfoMap={}", CommonSupplierLoader.SUPPLIER_REQUEST_INFO_MAP);
            }
        } catch (Exception e) {
            log.error("数据上报异常", e);
        }
    }

    /**
     * 可定查询
     *
     * @param requestBo 可定查询入参
     * @param supplierProduct 供应商产品实体
     * @return 可订查询返回
     */
    public LocalCheckAvailResponseBo checkAvail(LocalCheckAvailRequestBo requestBo, SupplierProductBo supplierProduct) {
        try {
            if (requestBo == null || supplierProduct == null) {
                return null;
            }
            CheckAvailV2RequestType request = this.convertCheckAvailRequest(requestBo, supplierProduct);
            String productUrl = supplierProduct.getProductUrl();
            String supplierCode = supplierProduct.getSupplierCode();
            String userKey = supplierProduct.getUserKey();
            String json = commonService.doPostJSON(supplierCode, "可订查询", productUrl, userKey, JsonUtils.toJsonString(request));

            WrapCheckAvailV2ResponseType response = JsonUtils.parse(json, WrapCheckAvailV2ResponseType.class);
            if (response != null && (response.getData() != null || response.getCode() != null || response.getMsg() != null)) {
                log.info("走新契约");
                return convertCheckAvailResponseNew(response, requestBo);
            }
            else {
                log.info("走旧契约");
                return this.convertCheckAvailResponse(response, requestBo);
            }
        } catch (Exception e) {
            log.warn("获取供应商可定查询数据失败", e);
        }
        return null;
    }

    /**
     * 创建订单
     *
     * @param requestBo 创建订单入参
     * @param supplierProduct 供应商产品实体
     * @return 创建订单返回
     */
    public LocalBookOrderResponseBo bookOrder(LocalBookOrderRequestBo requestBo, SupplierProductBo supplierProduct) {
        try {
            if (requestBo == null || supplierProduct == null) {
                return null;
            }
            BookOrderV2RequestType request = this.convertBookOrderRequest(requestBo, supplierProduct);
            String productUrl = supplierProduct.getProductUrl();
            String supplierCode = supplierProduct.getSupplierCode();
            String userKey = supplierProduct.getUserKey();
            String json = commonService.doPostJSON(supplierCode, OrderMonitorService.CREATE_ORDER, productUrl, userKey,
                JsonUtils.toJsonString(request));
            BookOrderV2ResponseType response = JsonUtils.parse(json, BookOrderV2ResponseType.class);
            return this.convertBookOrderResponse(request, response);
        } catch (IOException e) {
            log.warn("获取供应商可定查询数据失败", e);
            if (e instanceof SocketTimeoutException) {
                LocalBookOrderResponseBo responseBo = new LocalBookOrderResponseBo();
                responseBo.setTimeout(true);
                return responseBo;
            }
        }
        return null;
    }

    /**
     * 高德周边搜索
     */
    public AroundPlaceRespDto aroundPlaceByGaoDe(Double lat, Double lon) {
        if (lat == null || lon == null || lat == -1 || lon == -1 || lat == 0 || lon == 0) {
            return null;
        }
        String gaoDeAroundPlaceUrl = hotelApollo.getGaoDeAroundPlaceUrl();
        String gaoDeKey = hotelApollo.getGaoDeKey();
        if (StringUtils.isBlank(gaoDeAroundPlaceUrl) || StringUtils.isBlank(gaoDeKey)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("key=").append(gaoDeKey).append("&").append("location=").append(lon).append(",").append(lat);
        AroundPlaceRespDto aroundPlaceRespDto = null;
        long startTime = System.currentTimeMillis();
        try {
            aroundPlaceRespDto = JsonUtils.parse(HttpUtils.doGet("高德", "周边搜索", gaoDeAroundPlaceUrl, sb.toString()), AroundPlaceRespDto.class);
        } catch (Exception e) {
            log.error("周边搜索异常", e);
        } finally {
            log.info("高德周边搜索，request：{}，response：{}", sb, JsonUtils.toJsonString(aroundPlaceRespDto));
            Metrics.REGISTRY.timer(gaodeAroundPlaceMetricId).record(System.currentTimeMillis() - startTime, TimeUnit.MILLISECONDS);
        }
        return aroundPlaceRespDto;
    }

    /**
     * 获取本地下单接口返回
     *
     * @param response 供应商下单接口返回
     * @return 下单接口返回
     */
    private LocalBookOrderResponseBo convertBookOrderResponse(BookOrderV2RequestType request,
        BookOrderV2ResponseType response) {
        LocalBookOrderResponseBo responseBo = new LocalBookOrderResponseBo();
        if (response == null || StringUtils.isBlank(response.getOrderID())) {
            if (response != null && StringUtils.isNotBlank(response.getMessage())) {
                // 美亚错误信息
                orderMonitorService.saveOrderMonitor(request.getBaseInfo().getPlatformOrderId(),
                    MonitorTypeEnums.IE, MonitorLevelEnums.IMP,
                    JsonUtils.toJsonString(response),
                    response.getErrorCode().toString(), response.getMessage(),
                    OrderMonitorService.CREATE_ORDER);
                throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_MEIYA_CREATE_ORDER, response.getMessage());
            }
            // 携程错误信息
            Integer errorCode =
                Optional.ofNullable(response).map(BookOrderV2ResponseType::getStatus).map(ResponseStatus::getErrorCode)
                    .orElse(null);
            String errorMessage = Optional.ofNullable(response).map(BookOrderV2ResponseType::getStatus)
                .map(ResponseStatus::getErrorMessage).orElse(StringUtils.EMPTY);
            orderMonitorService.saveOrderMonitor(request.getBaseInfo().getPlatformOrderId(), MonitorTypeEnums.IE,
                MonitorLevelEnums.IMP, JsonUtils.toJsonString(response), String.valueOf(errorCode), errorMessage,
                OrderMonitorService.CREATE_ORDER);
            throw new CorpBusinessException(errorCode, errorMessage);
        }
        responseBo.setSupplierOrderId(response.getOrderID());
        responseBo.setPaymentNo(response.getPaymentTransactionId());
        return responseBo;
    }

    /**
     * 获取供应商下单接口入参
     *
     * @param requestBo 本地下单接口入参
     * @param supplierProduct 供应商产品实体
     * @return 供应商下单接口入参
     */
    private BookOrderV2RequestType convertBookOrderRequest(LocalBookOrderRequestBo requestBo,
        SupplierProductBo supplierProduct) {
        BookOrderV2RequestType request = new BookOrderV2RequestType();
        String platformOrderId =
            Optional.ofNullable(requestBo.getExtInfo()).map(ExtInfo::getPlatformOrderId).orElse(null);
        // 本次查询用户相关信息
        BaseEntity baseInfo = new BaseEntity();
        baseInfo.setUid(supplierProduct.getSupplierUid());
        baseInfo.setCorpId(supplierProduct.getSupplierCorpId());
        baseInfo.setLanguage(LanguageEnum.ZH_CN.getCode());
        baseInfo.setPlatformOrderId(platformOrderId);
        request.setBaseInfo(baseInfo);
        request.setProductId(requestBo.getProductId());
        // 创建订单房型信息
        Optional.ofNullable(requestBo.getRoomInfo()).ifPresent(e -> {
            CreateRoom roomInfo = new CreateRoom();
            roomInfo.setRoomQuantity(e.getRoomQuantity());
            roomInfo.setGuestQuantity(e.getGuestQuantity());
            roomInfo.setRoomId(e.getRoomId());
            roomInfo.setHotelId(e.getHotelId());
            roomInfo.setCityId(e.getCityId());
            roomInfo.setCheckInDate(e.getCheckInDate());
            roomInfo.setCheckOutDate(e.getCheckOutDate());
            request.setRoomInfo(roomInfo);
        });
        // 入住人列表
        List<Client> clientList =
            Optional.ofNullable(requestBo.getClientList()).orElse(new ArrayList<>()).stream().map(e -> {
                if (e == null) {
                    return null;
                }
                Client client = new Client();
                client.setName(e.getName());
                client.setUid(e.getUid());
                client.setCorpId(e.getCorpId());
                client.setCorpName(e.getCorpName());
                client.setDeptId(e.getDeptId());
                client.setDeptName(e.getDeptName());
                client.setMobilePhone(e.getMobilePhone());
                client.setCountryCode(e.getCountryCode());
                client.setEarnPoints(BooleanUtils.isTrue(e.getEarnPoints()));
                client.setRoomIndex(e.getRoomIndex());
                return client;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        request.setClientList(clientList);
        // 联系人
        Optional.ofNullable(requestBo.getContactInfo()).ifPresent(e -> {
            Contactor contactor = new Contactor();
            contactor.setName(e.getName());
            contactor.setEmail(e.getEmail());
            contactor.setMobilePhone(e.getMobilePhone());
            contactor.setMobilePhoneCountryCode(e.getMobilePhoneCountryCode());
            request.setContactorInfo(contactor);
        });
        // 备注信息
        Optional.ofNullable(requestBo.getRemarkInfo()).ifPresent(e -> {
            BookOrderRemark remarkInfo = new BookOrderRemark();
            remarkInfo.setCustomRemark(e.getCustomRemark());
            List<LocalOptionalRemark> localOptionalRemarks =
                Optional.ofNullable(e.getOptionalRemarkList()).orElse(new ArrayList<>());
            List<OptionalRemark> optionalRemarkList = localOptionalRemarks.stream().map(o -> {
                OptionalRemark optionalRemark = new OptionalRemark();
                optionalRemark.setId(o.getId());
                optionalRemark.setKey(o.getKey());
                optionalRemark.setTitle(o.getTitle());
                optionalRemark.setValue(o.getValue());
                return optionalRemark;
            }).collect(Collectors.toList());
            remarkInfo.setOptionalRemarkList(optionalRemarkList);
            request.setRemarkInfo(remarkInfo);
        });
        // 支付方式
        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setPrepayType(requestBo.getCorpOrderInfo().getPrepayType());

        paymentInfo.setMixPayWayInfo(Lists.newArrayList());
        Optional.ofNullable(requestBo.getCorpOrderInfo().getMixPayWayInfo()).orElse(Lists.newArrayList()).forEach(x -> {
            MixPaymentWay mixPaymentWay = new MixPaymentWay();
            mixPaymentWay.setMixPayWay(x.getMixPayWay());
            mixPaymentWay.setPayAmount(x.getPayAmount());
            paymentInfo.getMixPayWayInfo().add(mixPaymentWay);
        });

        request.setPaymentInfo(paymentInfo);
        Optional.ofNullable(requestBo.getInvoiceInfoList()).orElse(new ArrayList<>()).stream().findFirst()
            .ifPresent(e -> {
                Invoice invoice = new Invoice();
                invoice.setOrderInvoiceTargetType(e.getInvoiceType());
                invoice.setInvoiceTitleType(e.getInvoiceTitleType());
                invoice.setInvoiceTitle(e.getInvoiceTitle());
                invoice.setTaxpayerNumber(e.getTaxpayerNumber());
                invoice.setCompanyName(e.getInvoiceTitle());
                invoice.setCompanyAddress(e.getCorporationAddress());
                invoice.setCompanyPhone(e.getCorporationTel());
                invoice.setCompanyBankName(e.getAccountBank());
                invoice.setCompanyBankAccount(e.getAccountCardNo());
                invoice.setEmail(e.getInvoiceEmail());
                request.setInvoiceInfo(invoice);
            });
        CorpOrderInfo corpOrderInfo = new CorpOrderInfo();
        corpOrderInfo.setSendMsg(requestBo.getCorpOrderInfo().getSendMsg());
        request.setCorpOrderInfo(corpOrderInfo);
        CreateOrderExtInfo extInfo = new CreateOrderExtInfo();
        extInfo.setExternalOrderId(platformOrderId);
        extInfo.setMembershipCardNum(
            Optional.ofNullable(requestBo.getExtInfo()).map(ExtInfo::getMembershipCardNum).orElse(null));
        request.setExtInfo(extInfo);
        PriceInfo priceInfo = new PriceInfo();
        // 如果有快递费 添加快递费
        priceInfo.setAmount(requestBo.getPriceInfo().getCalculateAmount());
        priceInfo.setSellPrice(requestBo.getPriceInfo().getCalculateAmount());
        request.setPriceInfo(priceInfo);
        return request;
    }

    private LocalCheckAvailResponseBo convertCheckAvailResponseNew(WrapCheckAvailV2ResponseType response, LocalCheckAvailRequestBo requestBo) {
        if (response == null) {
            return null;
        }
        log.info("CommonSupplierLoader.convertCheckAvailResponse, CheckAvailV2ResponseType:{}", JsonUtils.toJsonString(response));
        LocalCheckAvailResponseBo responseBo = new LocalCheckAvailResponseBo();

        // 状态码校验
        // 可订检查不通过
        String code = response.getCode();
        CheckAvailV2ResponseType data = response.getData();
        if (!"0".equals(code) || data == null) {
            responseBo.setSuccess(false);
            responseBo.setFailedReason(response.getMsg());
            if (Arrays.asList("10812023", "10812030", "10812032", "10812035", "10812036").contains(code)) {
                responseBo.setCheckCode("2");
            }
            log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情:{}", JsonUtils.toJsonString(responseBo));
            return responseBo;
        }

        responseBo.setSuccess(true);
        responseBo.setFailedReason(Optional.ofNullable(data.getFailedReason()).map(MultipleLanguageText::getTextGB).orElse(null));
        responseBo.setRoomInfo(this.roomInfo(data, requestBo));
        responseBo.setRoomDailyInfoList(this.roomDailyInfo(data));
        responseBo.setChangePrice(data.getChangePrice());
        responseBo.setChangePriceDetailList(this.changePriceDetail(data));
        log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情:{}", JsonUtils.toJsonString(responseBo));
        return responseBo;
    }

    /**
     * 获取本地可订查询返回
     *
     * @param response 供应商可订查询返回
     * @param requestBo
     * @return
     */
    private LocalCheckAvailResponseBo convertCheckAvailResponse(CheckAvailV2ResponseType response,
        LocalCheckAvailRequestBo requestBo) {
        if (response == null) {
            return null;
        }
        log.info("CommonSupplierLoader.convertCheckAvailResponse, CheckAvailV2ResponseType:{}", JsonUtils.toJsonString(response));
        LocalCheckAvailResponseBo responseBo = new LocalCheckAvailResponseBo();

        if (Objects.nonNull(response.getSuccess()) && !response.getSuccess()) {
            responseBo.setSuccess(response.getSuccess());
            responseBo.setFailedReason(response.getErrorMessage());
            if(Objects.isNull(response.getStatus()) || Objects.isNull(response.getStatus().getErrorCode())) {
                log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情1:{}", JsonUtils.toJsonString(responseBo));
                return responseBo;
            }
            // 返回的错误码为10812023、10812030、10812032、10812035、10812036，用户点击【确定】按钮则返回酒店详情页并刷新
            // 返回其他错误码，用户点击【确定】按钮则返回酒店填单页无需刷新
            if(response.getStatus().getErrorCode() == 10812023 || response.getStatus().getErrorCode() == 10812030 ||
                    response.getStatus().getErrorCode() == 10812032 || response.getStatus().getErrorCode() == 10812035 ||
                    response.getStatus().getErrorCode() == 10812036 ) {
                responseBo.setCheckCode("2");
                responseBo.setFailedReason(Optional.ofNullable(response.getStatus()).map(ResponseStatus::getErrorMessage).orElse(null));
            }
            log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情2:{}", JsonUtils.toJsonString(responseBo));
            return responseBo;
        }

        responseBo.setSuccess(Optional.ofNullable(response.getStatus()).map(ResponseStatus::getSuccess).orElse(false));
        responseBo.setFailedReason(
                Optional.ofNullable(response.getFailedReason()).map(MultipleLanguageText::getTextGB).orElse(null));
        responseBo.setRoomInfo(this.roomInfo(response, requestBo));
        responseBo.setRoomDailyInfoList(this.roomDailyInfo(response));
        responseBo.setChangePrice(response.getChangePrice());
        responseBo.setChangePriceDetailList(this.changePriceDetail(response));

        if(responseBo.getSuccess() || Objects.isNull(response.getStatus()) || Objects.isNull(response.getStatus().getErrorCode())) {
            log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情3:{}", JsonUtils.toJsonString(responseBo));
            return responseBo;
        }
        // 返回的错误码为10812023、10812030、10812032、10812035、10812036，用户点击【确定】按钮则返回酒店详情页并刷新
        // 返回其他错误码，用户点击【确定】按钮则返回酒店填单页无需刷新
        if(response.getStatus().getErrorCode() == 10812023 || response.getStatus().getErrorCode() == 10812030 ||
                response.getStatus().getErrorCode() == 10812032 || response.getStatus().getErrorCode() == 10812035 ||
                response.getStatus().getErrorCode() == 10812036 ) {
            responseBo.setCheckCode("2");
            responseBo.setFailedReason(Optional.ofNullable(response.getStatus()).map(ResponseStatus::getErrorMessage).orElse(null));
        }
        log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情4:{}", JsonUtils.toJsonString(responseBo));
        return responseBo;
    }

    /**
     * 变价详情
     *
     * @param response
     * @return
     */
    private List<ChangePriceDetailInfo> changePriceDetail(CheckAvailV2ResponseType response) {
        if (CollectionUtils.isEmpty(response.getChangePriceDetailList())) {
            return new ArrayList<>();
        }
        return response.getChangePriceDetailList().stream().map(e -> {
            if (e == null) {
                return null;
            }
            ChangePriceDetailInfo priceDetailInfo = new ChangePriceDetailInfo();
            priceDetailInfo.setDate(e.getDate());
            priceDetailInfo.setPrice(e.getPrice());
            return priceDetailInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 每日房价信息
     *
     * @param response
     * @return
     */
    private List<RoomDailyInfo> roomDailyInfo(CheckAvailV2ResponseType response) {
        if (CollectionUtils.isEmpty(response.getRoomDailyInfoList())) {
            return new ArrayList<>();
        }
        return response.getRoomDailyInfoList().stream().map(e -> {
            if (e == null) {
                return null;
            }
            RoomDailyInfo roomDailyInfo = new RoomDailyInfo();
            roomDailyInfo.setEffectDate(e.getEffectDate());
            roomDailyInfo.setSellPrice(e.getSellPrice());
            return roomDailyInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取可订房间信息
     *
     * @param responseBo 可定接口返回
     * @return 可订接口房间信息
     */
    private RoomItem roomInfo(CheckAvailV2ResponseType responseBo, LocalCheckAvailRequestBo requestBo) {
        RoomItemV2 roomInfo = responseBo.getRoomInfo();
        if (roomInfo == null) {
            return null;
        }
        RoomItem roomItem = new RoomItem();
        Optional.ofNullable(roomInfo.getTimeInformation()).ifPresent(e -> {
            roomItem.setLastCancelTime(e.getLastCancelTime());
            roomItem.setEarlyArrivalTime(e.getEarlyArrivalTime());
            roomItem.setLastArrivalTime(e.getLastArrivalTime());
        });
        Optional.ofNullable(roomInfo.getPriceInformation()).ifPresent(e -> {
            roomItem.setOriginAmount(e.getSellPrice());
            roomItem.setCnyAmount(e.getSellPrice());
            roomItem.setCustomAmount(e.getSellPrice());
        });
        Optional.ofNullable(roomInfo.getLimitInformation()).ifPresent(e -> {
            roomItem.setGuestPerson(e.getGuestPerson());
            roomItem.setMinBookingRoomNum(e.getMinBookingRoomNum());
            roomItem.setMaxBookingRoomNum(e.getMaxBookingRoomNum());
        });
        List<Remark> remarkList =
            Optional.ofNullable(roomInfo.getRemarkList()).orElse(new ArrayList<>()).stream().map(e -> {
                if (e == null) {
                    return null;
                }
                Remark remark = new Remark();
                remark.setKey(e.getKey());
                remark.setId(e.getId());
                remark.setTitle(e.getTitle());
                remark.setDesc(e.getDesc());
                remark.setUnique(e.getUnique());
                return remark;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        roomItem.setRemarkList(remarkList);
        roomItem.setReceiveTextRemark(roomInfo.getReceiveTextRemark());
        roomItem.setSpecialTipList(roomInfo.getSpecialTipList());
        List<LadderDeductionInfo> ladderDeductionInfoList =
            Optional.ofNullable(roomInfo.getLadderDeductionInfoList()).orElse(new ArrayList<>()).stream().map(e -> {
                if (e == null) {
                    return null;
                }
                LadderDeductionInfo ladder = new LadderDeductionInfo();
                ladder.setDeductionType(e.getDeductionType());
                LadderDeductionDetailEntityV2 ladderDeductionInfo = e.getLadderDeductionInfo();
                LadderDeductionDetail detail = new LadderDeductionDetail();
                if (ladderDeductionInfo != null) {
                    detail.setStartDeductTime(
                        DateUtil.stringToDate(ladderDeductionInfo.getStartDeductTime(), DateUtil.DF_YMD_HM));
                    detail.setEndDeductTime(
                        DateUtil.stringToDate(ladderDeductionInfo.getEndDeductTime(), DateUtil.DF_YMD_HM));
                    detail.setDeductionRatio(ladderDeductionInfo.getDeductionRatio());
                    detail.setAmount(ladderDeductionInfo.getAmount());
                    ladder.setLadderDeductionInfo(detail);
                }
                return ladder;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        roomItem.setLadderDeductionInfoList(ladderDeductionInfoList);
        roomItem.setAvailableVatInvoiceType(
            AvailableVatInvoiceTypeEnum.getByType(roomInfo.getAvailableVATInvoiceType()).getType());
        // 支持的发票类型列表 需要兼容老字段
        Set<String> supportInvoiceTypeSet = new HashSet<>();
        String availableVATInvoiceType = roomInfo.getAvailableVATInvoiceType();
        if (StringUtils.equalsIgnoreCase(availableVATInvoiceType, "Special")) {
            supportInvoiceTypeSet.add(InvoiceEnum.DVatInvoice.getCode());
            supportInvoiceTypeSet.add(InvoiceEnum.DInvoice.getCode());
        } else if (StringUtils.equalsIgnoreCase(availableVATInvoiceType, "Ordinary")) {
            supportInvoiceTypeSet.add(InvoiceEnum.DInvoice.getCode());
        }
        List<String> supportInvoiceTypeList = roomInfo.getSupportInvoiceTypeList();
        if (CollectionUtils.isNotEmpty(supportInvoiceTypeList)) {
            supportInvoiceTypeSet.addAll(supportInvoiceTypeList);
        }
        roomItem.setSupportInvoiceTypeList(new ArrayList<>(supportInvoiceTypeSet));
        // 酒店积分
        String supplierCode = Optional.ofNullable(requestBo).map(LocalCheckAvailRequestBo::getBaseInfo).map(LocalCheckAvailRequestBo.CheckBaseInfo::getSupplierCode).orElse("");
        String groupId = Optional.ofNullable(requestBo).map(LocalCheckAvailRequestBo::getGroupId).orElse("");
        String bonusPointCode = Optional.ofNullable(roomInfo.getBonusPointInfo()).map(RoomItemV2.BonusPointInfo::getBonusPointCode).orElse("");
        HotelBonusPointInfoDo hotelBonusPointInfoDo = hotelBonusPointService.getHotelBonusPointInfo(supplierCode, groupId, bonusPointCode);
        log.info("获取酒店积分信息 supplierCode={} groupId={} bonusPointCode={} hotelBonusPointInfo={}", supplierCode, groupId, bonusPointCode, hotelBonusPointInfoDo);
        // 填充积分
        if (hotelBonusPointInfoDo != null) {
            LocalCheckAvailResponseBo.BonusPointInfo bonusPointInfo = convert(hotelBonusPointInfoDo, bonusPointCode, groupId, supplierCode);
            roomItem.setBonusPointInfo(bonusPointInfo);
            log.info("酒店积分填充 bonusPointInfo={}", bonusPointInfo);
        }
        // 供应商下单透传额外信息
        roomItem.setAdditionalSupplierInfo(roomInfo.getAdditionalSupplierInfo());
        return roomItem;
    }

    private LocalCheckAvailResponseBo.BonusPointInfo convert(HotelBonusPointInfoDo hotelBonusPointInfoDo, String bonusPointCode, String groupId, String supplierCode) {
        LocalCheckAvailResponseBo.BonusPointInfo tmp = new LocalCheckAvailResponseBo.BonusPointInfo();
        tmp.setSupplierCode(supplierCode);
        tmp.setGroupId(groupId);
        tmp.setGroupName(hotelBonusPointInfoDo.getGroupName());
        tmp.setBonusPointCode(bonusPointCode);
        tmp.setBonusPointType(hotelBonusPointInfoDo.getBonusPointType());
        tmp.setFillPageRuleDescList(hotelBonusPointInfoDo.getFillPageRuleDescList());
        tmp.setOrderDetailPageRuleDescList(hotelBonusPointInfoDo.getOrderDetailPageRuleDescList());
        return tmp;
    }

    /**
     * 获取供应商可订查询request
     *
     * @param requestBo 本地可订查询入参
     * @return 供应商可订查询request
     */
    private CheckAvailV2RequestType convertCheckAvailRequest(LocalCheckAvailRequestBo requestBo,
        SupplierProductBo supplierProduct) {
        if (requestBo == null) {
            return null;
        }
        CheckAvailV2RequestType request = new CheckAvailV2RequestType();
        BaseEntity baseInfo = new BaseEntity();
        if (StringUtils.equalsIgnoreCase(supplierProduct.getSupplierCode(), "ctrip")) {
            baseInfo.setUid(supplierProduct.getSupplierUid());
        } else {
            baseInfo.setUid(StringUtils.isBlank(supplierProduct.getSupplierUid()) ? null : supplierProduct.getSupplierUid());
        }
        baseInfo.setCorpId(supplierProduct.getSupplierCorpId());
        baseInfo.setLanguage(LanguageEnum.ZH_CN.getCode());
        request.setBaseInfo(baseInfo);
        CheckRoomEntityV2 roomInfo = new CheckRoomEntityV2();
        Optional.ofNullable(requestBo.getRoomInfo()).ifPresent(e -> {
            roomInfo.setProductId(e.getProductId());
            roomInfo.setCheckInDate(e.getCheckInDate());
            roomInfo.setCheckOutDate(e.getCheckOutDate());
            roomInfo.setQuantity(e.getQuantity());
            roomInfo.setGuestPerson(e.getGuestPerson());
            roomInfo.setRoomId(e.getRoomId());
            // 因公因私标识
            roomInfo.setFeeType(e.getFeeType());
        });
        request.setRoomInfo(roomInfo);
        return request;
    }

    /**
     * 获取酒店详情response
     *
     * @param response 供应商酒店详情
     * @param requestBo 酒店详情入参
     * @param supplierProductBo 供应商产品实体
     * @param travelStandard 差标
     * @param reasonInfos rc原因
     * @param payInfo 支付信息
     * @return
     */
    public HotelDetailResponseVO convertDetailResponse(GetHotelDetailV2ResponseType response,
        LocalHotelDetailRequestBo requestBo,
        SupplierProductBo supplierProductBo, HotelControlVo travelStandard,
        List<ReasonInfo> reasonInfos, List<PayInfoResponse> payInfo, List<ApplyTripItemBo> applyTripItemBoList) {
        if (response == null || response.getHotelDetailInfo() == null || response.getHotelRatePlan() == null) {
            return null;
        }
        HotelDetailResponseVO responseVo = new HotelDetailResponseVO();
        HotelDetailInfoEntity hotelDetailInfo = response.getHotelDetailInfo();
        // 酒店fullName
        DetailBaseInfoEntity hotelBaseInfo = hotelDetailInfo.getHotelBaseInfo();
        responseVo.setFullName(hotelBaseInfo.getHotelName());
        // 评论信息
        responseVo.setComment(this.getComment(response));
        // 图片
        responseVo.setPicBeans(this.getPicBeans(response, supplierProductBo));
        // 酒店介绍
        responseVo.setIntro(this.getIntro(response));
        // 酒店政策
        responseVo.setHotelPolicy(this.getHotelPolicy(response));
        // PC交通信息
        responseVo.setPcTransports(this.getPcTransports(response));
        // APP交通信息
        responseVo.setAppTransports(this.getAppTransports(response));
        // 周边设施
        responseVo.setAroundFacilities(this.getAroundFacilityList(response));
        // 酒店设施
        responseVo.setHotelFacilities(this.getHotelFacilities(response));
        // 酒店设施详情
        setHotelFacilityDetail(response, responseVo);
        // 酒店提示
        responseVo.setNotices(this.getNotices(response));
        // 房型列表
        responseVo.setRooms(this.getRoomInfo(response, requestBo, supplierProductBo, travelStandard, payInfo, applyTripItemBoList));
        // 供应商信息
        responseVo.setSupplierList(Lists.newArrayList(this.getSupplierInfo(responseVo, supplierProductBo)));
        // 地址
        responseVo.setAddressInfo(this.getAddressInfo(response));
        // rc reason
        responseVo.setReasonInfo(reasonInfos);
        // 供应商图片列表 + 补充图片
        responseVo.setHotelPicList(this.getHotelPicList(response, responseVo));
        Kv kv = Optional.ofNullable(requestBo.getTree()).map(Tree::getKvs).flatMap(e -> e.stream().findFirst())
            .orElse(new Kv());
        // 本地静态数据覆盖
        this.overwriteByLocalStaticInfo(kv.getHotelId(), kv.getSupplier(), responseVo);
        this.cacheRoomInfo(response, requestBo, supplierProductBo, travelStandard, responseVo);
        log.info("CommonSupplierLoader.convertDetailResponse, 封装后的酒店详情:{}", JsonUtils.toJsonString(responseVo));
        Metrics.REGISTRY.counter(queryDetailMetricId.withTags("status", "success")).increment();
        return responseVo;
    }

    /**
     * 异步缓存房间信息
     *
     * @param response 供应商酒店详情返回
     * @param requestBo 酒店详情入参
     * @param supplierProductBo 供应商产品实体
     * @param travelStandard 差标
     * @param responseVo 酒店详情返回
     */
    private void cacheRoomInfo(GetHotelDetailV2ResponseType response, LocalHotelDetailRequestBo requestBo,
        SupplierProductBo supplierProductBo,
        HotelControlVo travelStandard, HotelDetailResponseVO responseVo) {
        Optional<DetailBaseInfoEntity> baseInfoOptional =
            Optional.ofNullable(response.getHotelDetailInfo()).map(HotelDetailInfoEntity::getHotelBaseInfo);
        int star =
            baseInfoOptional.map(DetailBaseInfoEntity::getHotelStarInfo).map(HotelStarEntity::getStarNum).orElse(0);
        Boolean starLicence = response.getHotelRatePlan().getStarLicence();
        List<HotelInfoModel> cacheList = Lists.newArrayList();
        // 获取超标校验结果
        Map<String, String> responsesMap = travelStandardService.getTravelStandardMap(requestBo, responseVo);
        for (HotelDetailResponseVO.Room room : responseVo.getRooms()) {
            // 获取母房型信息
            HotelInfoModel.ParentRoomInfo parentRoomInfo = new HotelInfoModel.ParentRoomInfo();
            for (int i = 0; i < room.getChildRooms().size(); i++) {
                HotelDetailResponseVO.ChildRoom childRoom = room.getChildRooms().get(i);
                if (childRoom == null || !Boolean.TRUE.equals(childRoom.getButtonFlag()) || childRoom.getPrice() == null) {
                    continue;
                }
                String protocolTag = childRoom.getProtocolTag();
                BigDecimal price = childRoom.getPrice();
                // 协议房型
                if (StringUtils.isNotBlank(protocolTag)) {
                    // 协议最低价
                    BigDecimal protocolMinAvgPrice = parentRoomInfo.getProtocolMinAvgPrice();
                    if (protocolMinAvgPrice == null || price.compareTo(protocolMinAvgPrice) < 0) {
                        parentRoomInfo.setProtocolMinAvgPrice(price);
                        parentRoomInfo.setProtocolMinAvgPriceSupplierCode(supplierProductBo.getSupplierCode());
                    }
                }
                // 非协议房型
                else {
                    // 非协议最低价
                    BigDecimal nonProtocolMinAvgPrice = parentRoomInfo.getNonProtocolMinAvgPrice();
                    if (nonProtocolMinAvgPrice == null || price.compareTo(nonProtocolMinAvgPrice) < 0) {
                        parentRoomInfo.setNonProtocolMinAvgPrice(price);
                    }
                    // 非协议最高价
                    BigDecimal nonProtocolMaxAvgPrice = parentRoomInfo.getNonProtocolMaxAvgPrice();
                    if (nonProtocolMaxAvgPrice == null || price.compareTo(nonProtocolMaxAvgPrice) > 0) {
                        parentRoomInfo.setNonProtocolMaxAvgPrice(price);
                    }
                }
            }
            for (int i = 0; i < room.getChildRooms().size(); i++) {
                HotelDetailResponseVO.ChildRoom childRoom = room.getChildRooms().get(i);
                HotelInfoModel product = new HotelInfoModel();
                product.setPolicyId(requestBo.getPolicyId());
                product.setPolicyOrgId(requestBo.getPolicyOrgId());
                product.setIndex(childRoom.getIndex());
                product.setStar(star);
                product.setIsStarLicence(starLicence);
                product.setBreakfast(childRoom.getBreakFastCount());
                product.setBreakfastDesc(childRoom.getBreakFast());
                product.setHotelType(childRoom.getRoomType());
                product.setCheckInDate(requestBo.getCheckInDate());
                product.setCheckOutDate(requestBo.getCheckOutDate());
                product.setRoomId(childRoom.getRoomID());
                product.setBasicRoomId(room.getRoomID());
                product.setOriginRoomId(childRoom.getRoomID());
                product.setQuantity(childRoom.getRoomQuantity());
                product.setBedType(childRoom.getBedType());
                product.setGuestPerson(childRoom.getGuestPerson());
                product.setLastArrivalTime(childRoom.getLastArrivalTime());// 最晚到店时间
                product.setLastCancelTime(childRoom.getLastCancelTime());
                product.setAmadeus(childRoom.getAmadeus());
                product.setSmoke(childRoom.getSmoke());
                product.setOtherDescription(childRoom.getOtherDescription());
                product.setAveragePrice(childRoom.getAveragePrice());
                product.setBalanceType(childRoom.getPayType());
                // product.setHotelId(kv.getHotelId());
                product.setHotelId(room.getHotelID());
                product.setHotelName(responseVo.getFullName());
                product.setApplicativeAreaTitle(childRoom.getApplicativeAreaTitle());
                product.setApplicativeAreaDesc(childRoom.getApplicativeAreaDesc());
                product.setTelephone(baseInfoOptional.map(DetailBaseInfoEntity::getHotelContactInfo)
                    .map(HotelContactInfoType::getTelephone).orElse(null));
                Optional<PositionEntity> positionOp = baseInfoOptional.map(DetailBaseInfoEntity::getHotelPositionInfo);
                product.setLocationId(positionOp.map(PositionEntity::getLocationInfo).map(GeoCommonEntity::getId)
                    .map(String::valueOf).orElse(null));
                product.setLocationName(positionOp.map(PositionEntity::getLocationInfo).map(GeoCommonEntity::getName)
                        .map(String::valueOf).orElse(null));
                product.setCityId(requestBo.getCityID());
                product.setSupplierCityId(requestBo.getSupplierCityId());
                product.setCityName(
                    positionOp.map(PositionEntity::getCityInfo).map(GeoCommonEntity::getName).orElse(null));
                product.setAddress(positionOp.map(PositionEntity::getHotelAddress).orElse(null));
                product.setBrandId(baseInfoOptional.map(DetailBaseInfoEntity::getHotelBrandInfo)
                    .map(HotelBrandEntity::getBrandId).orElse(null));
                product.setGroupId(baseInfoOptional.map(DetailBaseInfoEntity::getHotelBrandInfo)
                    .map(HotelBrandEntity::getGroupId).orElse(null));
                positionOp.flatMap(e -> e.getCoordinateInfoList().stream()
                    .filter(c -> Objects.equals(c.getMapType(), GAO_DE.getCode())).findFirst()).ifPresent(e -> {
                        product.setLongitude(String.valueOf(e.getLon()));
                        product.setLatitude(String.valueOf(e.getLat()));
                    });
                HotelInfoModel.CancelInfo cancelInfo = new HotelInfoModel.CancelInfo();
                cancelInfo.setPolicyType(childRoom.getCancelationPolicy());
                product.setCancelInfo(cancelInfo);
                product.setPic(room.getRoomDetail().getLogoUrl());
                // todo 切图
                baseInfoOptional.map(DetailBaseInfoEntity::getHotelPictureInfo)
                    .map(PictureInfoEntity::getHotelLogoURL)
                    .ifPresent(e -> {
                        if (e.startsWith("http:") || e.startsWith("https:")) {
                            // 地址中已经有前缀了 无需在拼接前缀
                        } else {
                            product.setPic(getFilePrefix() + e);
                        }
                    });
                product.setSupplierCode(supplierProductBo.getSupplierCode());
                product.setSupplierName(supplierProductBo.getSupplierName());
                product.setSupplierUid(supplierProductBo.getSupplierUid());
                product.setSupplierCorpId(supplierProductBo.getSupplierCorpId());
                product.setSupplierAccountId(supplierProductBo.getSupplierAccountId());
                product.setProductId(childRoom.getProductId());
                product.setFloatControl(childRoom.getFloatControl());
                // todo
                product.setSupplierPhone("");
                List<LadderDeductionEntity> ladderDeductionList =
                    Optional.ofNullable(childRoom.getLadderDeductionEntitys()).orElse(new ArrayList<>());
                List<HotelInfoModel.LadderDeduction> list = ladderDeductionList.stream().map(x -> {
                    HotelInfoModel.LadderDeduction ladderDeduction = new HotelInfoModel.LadderDeduction();
                    ladderDeduction.setAdvanceHour(x.getAdvanceHour());
                    ladderDeduction.setAmount(x.getAmount());
                    ladderDeduction.setAntiCut(x.getAntiCut());
                    ladderDeduction.setDeductionRatio(x.getDeductionRatio());
                    ladderDeduction.setEndTime(x.getEndTime());
                    ladderDeduction.setStartTime(x.getStartTime());
                    ladderDeduction.setDeductionType(x.getDeductionType());
                    return ladderDeduction;
                }).collect(Collectors.toList());
                product.setRoomName(childRoom.getName());
                product.setLadderDeductionList(list);
                setTravelStandard(travelStandard, responsesMap, childRoom, product);
                log.info("setTravelStandard,setTravelStandard:{}",JsonUtils.toJsonString(travelStandard));
                if(ObjectUtil.isNotEmpty(travelStandard) && ObjectUtil.isNotEmpty(travelStandard.getAveragePriceSet())
                        && ObjectUtil.isNotEmpty(travelStandard.getAveragePriceSet().getPriceCeiling())){
                    product.setAmountHigh(new BigDecimal(travelStandard.getAveragePriceSet().getPriceCeiling()));
                } else {
                    product.setAmountHigh(BigDecimal.ZERO);
                }
                product.setDailyMealInfoList(childRoom.getDailyMealInfoList());
                product.setRoomDailyInfoList(childRoom.getDailyRateList().stream().map(e -> {
                    HotelInfoModel.RoomDailyInfo roomDailyInfo = new HotelInfoModel.RoomDailyInfo();
                    roomDailyInfo.setEffectDate(e.getEffectDate());
                    roomDailyInfo.setRoomPrice(e.getSalePrice());
                    return roomDailyInfo;
                }).collect(Collectors.toList()));
                product.setRoomKey(childRoom.getRoomKey());
                product.setPackageId(childRoom.getPackageId());
                product.setPackageRoom(childRoom.getPackageRoom());
                product.setPrice(childRoom.getPrice());
                product.setPicUrls(room.getRoomDetail().getPicUrls());
                product.setBasicInfo(room.getRoomDetail().getBasicInfo());
                product.setName(room.getRoomDetail().getName());
                product.setProtocolTag(childRoom.getProtocolTag());
                product.setParentRoomInfo(parentRoomInfo);
                cacheList.add(product);
            }
        }
        hotelInfoCacheManager.saveHotelInfoList(requestBo.getToken(), cacheList);
    }

    /**
     * 设置旅行标准
     *
     * @param travelStandard 旅行标准对象
     * @param responsesMap 是否超标的Map
     * @param childRoom 子房间对象
     * @param product 酒店信息模型
     */
    private static void setTravelStandard(HotelControlVo travelStandard, Map<String, String> responsesMap, ChildRoom childRoom, HotelInfoModel product) {
        try {
            Optional<HotelControlVo> optionalTravelStandard = Optional.ofNullable(travelStandard);
            optionalTravelStandard.ifPresent(ts -> {
                if (CollectionUtil.isNotEmpty(responsesMap)) {
                    String exceed = responsesMap.getOrDefault(childRoom.getRoomKey(),null);
                    // 是否超过差标 0:否 1:是
                    if(!"0".equals(exceed)){
                        exceed = "1";
                    }
                    ts.setExceed(exceed);
                }
            });
            product.setTravelStandard(
                    optionalTravelStandard
                            .map(JsonUtils::toJsonString)
                            .orElse("")
            );
        } catch (Exception e) {
            log.info("设置旅行标准,e:",e);
        }
    }

    /**
     * 获取供应商信息
     *
     * @param responseVo 酒店详情返回
     * @param supplierProductBo 供应商产品实体
     * @return 供应商信息
     */
    private SupplierInfo getSupplierInfo(HotelDetailResponseVO responseVo, SupplierProductBo supplierProductBo) {
        if (responseVo == null || supplierProductBo == null) {
            return null;
        }
        SupplierInfo supplierInfo = new SupplierInfo();
        supplierInfo.setSupplierCode(supplierProductBo.getSupplierCode());
        supplierInfo.setSupplierName(supplierProductBo.getSupplierName());
        String supplierLowPrice = "0";
        if(CollectionUtil.isNotEmpty(responseVo.getRooms())){
            supplierLowPrice =
                    responseVo.getRooms().stream().map(Room::getRoomDetail).map(RoomDetail::getPrice).min(Comparator.naturalOrder())
                            .map(String::valueOf).orElse("0");
        }
        supplierInfo.setSupplierLowPrice(supplierLowPrice);
        return supplierInfo;
    }

    /**
     * 使用本地静态数据进行覆盖
     *
     * @param hotelId 酒店id
     * @param supplierCode 供应商code
     * @param responseVo 酒店详情返回
     */
    private void overwriteByLocalStaticInfo(String hotelId, String supplierCode, HotelDetailResponseVO responseVo) {
        HmHotelDetailImport localInfo = this.getLocalStaticInfo(hotelId, supplierCode);
        if (localInfo == null) {
            return;
        }
        // 本地酒店名称
        responseVo.setFullName(Optional.ofNullable(localInfo.getHotelName()).orElse(responseVo.getFullName()));
        // 本地地址信息
        HotelDetailResponseVO.AddressInfo addressInfo = responseVo.getAddressInfo();
        String lon = localInfo.getLongitude();
        String lat = localInfo.getLatitude();
        addressInfo.setLatlon(Lists.newArrayList(Optional.ofNullable(lon).orElse(addressInfo.getLatlon().get(0)),
            Optional.ofNullable(lat).orElse(addressInfo.getLatlon().get(1))));
        String address = Optional.ofNullable(localInfo.getAddress()).orElse(addressInfo.getAddressDetail());
        if (StringUtils.isNotBlank(address)) {
            addressInfo.setAddressDetail(address);
        }
        Optional.ofNullable(localInfo.getZoneName()).ifPresent(addressInfo::setDistrict);
        responseVo.setAddressInfo(addressInfo);
        // 酒店介绍
        Date openYear = localInfo.getOpenYear();
        Date fitmentYear = localInfo.getFitmentYear();
        Intro intro = responseVo.getIntro();
        intro.setOpenYear(
            Optional.ofNullable(DateUtil.dateToString(openYear, DateUtil.DF_YMD)).orElse(intro.getOpenYear()));
        intro.setFitmentYear(
            Optional.ofNullable(DateUtil.dateToString(fitmentYear, DateUtil.DF_YMD)).orElse(intro.getFitmentYear()));
        Integer star = localInfo.getStar();
        intro.setLevel(star != null ? star.toString() : intro.getLevel());
        intro.setPhone(Optional.ofNullable(localInfo.getPhone()).orElse(intro.getPhone()));
        intro.setFax(Optional.ofNullable(localInfo.getFax()).orElse(intro.getFax()));
        intro.setDesc(Optional.ofNullable(localInfo.getHotelDesc()).orElse(intro.getDesc()));
        responseVo.setIntro(intro);
        // 酒店设施
        String hotelFacility = localInfo.getHotelFacility();
        if (StringUtils.isNotBlank(hotelFacility)) {
            List<FacilityInfoBo> facilities = new ArrayList<>();
            try {
                facilities = JsonUtils.parseArray(hotelFacility, FacilityInfoBo.class);
            } catch (Exception e) {
                log.error("本地设施转换异常", e);
            }
            if (CollectionUtils.isEmpty(facilities)) {
                return;
            }
            Map<String, List<FacilityInfoBo>> facilitiesMap =
                facilities.stream().filter(f -> f != null && StringUtils.isNotBlank(f.getFTypeName()))
                    .collect(Collectors.groupingBy(FacilityInfoBo::getFTypeName));
            List<HotelDetailResponseVO.HotelFacility> localHotelFacilities = Lists.newArrayList();
            for (Map.Entry<String, List<FacilityInfoBo>> entry : facilitiesMap.entrySet()) {
                HotelDetailResponseVO.HotelFacility localHotelFacility = new HotelDetailResponseVO.HotelFacility();
                localHotelFacility.setTitle(entry.getKey());
                localHotelFacility.setContent(
                    entry.getValue().stream().map(FacilityInfoBo::getFacilityName).collect(Collectors.toList()));
                localHotelFacilities.add(localHotelFacility);
            }
            responseVo.setHotelFacilities(localHotelFacilities);
        }
    }

    /**
     * 查询本地酒店静态数据
     *
     * @param hotelId 酒店id
     * @param supplierCode 供应商code
     * @return 本地酒店静态数据
     */
    private HmHotelDetailImport getLocalStaticInfo(String hotelId, String supplierCode) {
        HpSupplier supplier = new HpSupplier();
        supplier.setSupplierCode(supplierCode);
        supplier = supplierMapper.selectOne(supplier);
        if (supplier == null) {
            return null;
        }

        HmHotelDetailImport query = new HmHotelDetailImport();
        query.setSupplierId(supplier.getSupplierId());
        query.setHotelNo(hotelId);
        return hotelDetailImportMapper.selectOne(query);
    }

    /**
     * @param response
     * @param requestBo
     * @param supplierProductBo
     * @param travelStandard
     * @param payInfo
     * @return
     */
    private List<Room> getRoomInfo(GetHotelDetailV2ResponseType response, LocalHotelDetailRequestBo requestBo,
        SupplierProductBo supplierProductBo,
        HotelControlVo travelStandard, List<PayInfoResponse> payInfo, List<ApplyTripItemBo> applyTripItemBoList) {
        if (response == null || response.getHotelRatePlan() == null) {
            return new ArrayList<>();
        }
        HotelRatePlanEntity hotelRatePlan = response.getHotelRatePlan();
        List<BasicRoomInfo> basicRoomList = hotelRatePlan.getBasicRoomList();
        if (CollectionUtils.isEmpty(basicRoomList)) {
            return new ArrayList<>();
        }
        Boolean rcpolicy = requestBo.getRcpolicy();
        // List<HotelPicEntity> hotelPicList = Optional.ofNullable(response.getHotelPicList()).orElse(new
        // ArrayList<>());
        // Map<String, List<HotelPicEntity>> picMap = hotelPicList.stream().filter(e ->
        // StringUtils.isNotBlank(e.getBasicRoomTypeId())).collect(Collectors.groupingBy(HotelPicEntity::getBasicRoomTypeId));

        // 获取美亚服务费
        InitResponseBo initResponseBo = supplierCompanyClientLoader.searchSupplierConfig(requestBo.getBaseUserInfo().getCorpId(), "meiya");
        log.info("获取美亚服务费:{}",JsonUtils.toJsonString(initResponseBo));

        return basicRoomList.stream().map(basicRoomInfo -> {
            if (basicRoomInfo == null) {
                return null;
            }
            List<RoomInfoEntity> roomInfoList =
                Optional.ofNullable(basicRoomInfo.getRoomInfoList()).orElse(new ArrayList<>()).stream().filter(e -> {
                    // 根据差标过滤
                    if (rcpolicy && travelStandard != null) {
                        if (ObjectUtil.isEmpty(travelStandard.getAveragePriceSet())
                                || ObjectUtil.isEmpty(travelStandard.getAveragePriceSet().getPriceCeiling())
                                || Double.valueOf(travelStandard.getAveragePriceSet().getPriceCeiling()) == 0D) {
                            return true;
                        }
                        BigDecimal avgPrice =
                            Optional.ofNullable(e.getAvgSalePriceIncludeTax()).orElse(BigDecimal.ZERO);
                        return avgPrice
                            .compareTo(new BigDecimal(travelStandard.getAveragePriceSet().getPriceCeiling())) <= 0;
                    }
                    return true;
                }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(roomInfoList)) {
                return null;
            }
            RoomInfoEntity roomInfoEntity = roomInfoList.get(0);
            Room room = new Room();
            room.setHotelID(roomInfoEntity.getHotelId());
            room.setRoomID(roomInfoEntity.getRoomId());
            // 物理房型ID修正
            if (Boolean.TRUE.equals(shuntConfigDao.openFeature("updateBasicRoomId"))) {
                if (SupplierEnum.MEIYA.getCode().equals(supplierProductBo.getSupplierCode())) {
                    room.setRoomID(roomInfoEntity.getRoomId().split("\\|")[1]);
                } else {
                    room.setRoomID(basicRoomInfo.getMasterBasicRoomID());
                }
            }
            room.setRoomQuantity(roomInfoList.size());
            HotelDetailResponseVO.RoomDetail roomDetail = new HotelDetailResponseVO.RoomDetail();
            Optional.ofNullable(basicRoomInfo.getBasicRoomStaticInfo()).ifPresent(basicRoom -> {
                String supplierCode = Optional.ofNullable(supplierProductBo).map(SupplierProductBo::getSupplierCode).orElse("");
                if ("ctrip".equals(supplierCode) || "meiya".equals(supplierCode)) {
                    roomDetail.setPicUrls(Optional.ofNullable(basicRoom.getBasicRoomImageUrl()).orElse(Lists.newArrayList())
                            .stream().filter(StringUtils::isNotBlank).map(this::convertPicUrl).collect(Collectors.toList()));
                } else {
                    roomDetail.setPicUrls(Optional.ofNullable(basicRoom.getBasicRoomImageUrl()).orElse(Lists.newArrayList())
                            .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                }
                roomDetail.setLogoUrl(roomDetail.getPicUrls().stream().findFirst().orElse(null));
            });

            // 母房型的床型填充
            fillBedType(basicRoomInfo, roomInfoEntity, roomDetail);

            roomDetail.setName(basicRoomInfo.getBaseRoomName());
            Integer person = roomInfoEntity.getMaxGuestNumber();
            String addBedFeeDesc =
                Optional.ofNullable(roomInfoEntity.getAddBedInfo()).map(AddBedEntity::getAddBedPriceDesc).orElse(null);
            List<String> basicInfo = Lists.newArrayList();
            Optional<ChildBedInfoEntity> childBedOptional =
                Optional.ofNullable(roomInfoEntity.getRoomStaticInfo()).map(RoomStaticInfoEntity::getBedInfoList)
                    .flatMap(e -> e.stream().findFirst())
                    .map(BedInfoEntity::getChildBedInfoList)
                    .flatMap(e -> e.stream().findFirst());
            Optional<BedInfoEntity> bedOptional =
                Optional.ofNullable(roomInfoEntity.getRoomStaticInfo()).map(RoomStaticInfoEntity::getBedInfoList)
                    .flatMap(e -> e.stream().findFirst());
            String bedType = "";
            if (childBedOptional.isPresent() && StringUtils.isNotBlank(childBedOptional.get().getChildBedTypeName())) {
                bedType = childBedOptional.get().getChildBedTypeName();
            } else if (bedOptional.isPresent() && StringUtils.isNotBlank(bedOptional.get().getParentBedTypeName())) {
                bedType = bedOptional.get().getParentBedTypeName();
            }
            String bedWidth = "";
            if (childBedOptional.isPresent()) {
                bedWidth = childBedOptional.get().getBedWidth() == null ? ""
                    : String.valueOf(childBedOptional.get().getBedWidth());
            }
            room.setMaxGuestNumber(person);
            basicInfo.add(basicRoomInfo.getRoomArea());
            basicInfo.add(StringUtils.isBlank(basicRoomInfo.getFloor()) ? null : basicRoomInfo.getFloor() + "F");
            basicInfo.add(person == null ? null : person.toString());
            basicInfo.add(bedType);
            basicInfo.add(addBedFeeDesc);
            basicInfo.add(StringUtils.isBlank(bedWidth) ? null : bedType + bedWidth + "米");
            basicInfo.add(basicRoomInfo.getHasWindowDesc());
            basicInfo.add(roomInfoEntity.getBroadBand());
            roomDetail.setBasicInfo(basicInfo);
            roomDetail.setOtherInfo(Lists.newArrayList());
            roomDetail.setRoomType(roomInfoEntity.getRoomType());
            int index = 0;
            List<HotelDetailResponseVO.ChildRoom> childRooms = Lists.newArrayList();
            // 子房型
            for (RoomInfoEntity roomInfo : roomInfoList) {
                roomDetail.setShowTMCLabel(HotelTypeEnum.C.getType().equalsIgnoreCase(roomInfo.getRoomType()));
                List<ChildRoomDesc> childRoomDescList = Lists.newArrayList();
                HotelDetailResponseVO.ChildRoom childRoom = new HotelDetailResponseVO.ChildRoom();
                // 取消策略
                CancelRuleInfoEntity cancelRuleInfo = Optional.ofNullable(roomInfo.getBookingRules())
                    .map(BookingRulesInfo::getCancelRuleInfo).orElse(null);
                // 阶梯取消策略
                List<LadderDeductionInfoEntity> ladderDeductionInfo =
                    Optional.ofNullable(roomInfo.getBookingRules()).map(BookingRulesInfo::getCancelRuleInfo)
                        .map(CancelRuleInfoEntity::getLadderDeductionInfo).orElse(new ArrayList<>());
                ChildRoomDesc cancelDesc = this.getCancelDesc(cancelRuleInfo);
                Optional.ofNullable(cancelDesc).ifPresent(childRoomDescList::add);
                // otherInfo 内宾专享
                Optional.ofNullable(roomInfo.getBookingRules()).map(BookingRulesInfo::getApplicativeAreaInfo)
                    .ifPresent(e -> {
                        // 内宾专享标签
                        childRoom.setGuest("内宾专享");
                    });
                childRoom.setHotelId(roomInfoEntity.getHotelId());
                childRoom.setLadderDeductionEntitys(this.getLadderDeduction(ladderDeductionInfo));
                childRoom.setRoomID(roomInfo.getRoomId());
                childRoom.setRoomQuantity(roomInfo.getBookingRules().getRoomQuantity());
                childRoom.setName(roomInfo.getRoomName());
                childRoom.setShowTMCLabel(HotelTypeEnum.C.getType().equalsIgnoreCase(roomInfo.getRoomType()));
                String productType = supplierProductBo.getProductType();
                childRoom.setProtocolTag(getProtocolTag(supplierProductBo.getSupplierCode(), roomInfo,productType));
                childRoom.setIndex(index);

                // 餐食
                if (CollectionUtils.isNotEmpty(roomInfo.getDailyRates())) {
                    Integer mealCount = Optional.ofNullable(roomInfo.getDailyRates())
                        .flatMap(e -> e.stream().findFirst()).map(DailyRatesEntity::getMeals).orElse(0);
                    childRoom.setBreakFast(mealCount == 0 ? "无早" : MessageFormat.format(BREAKFAST_DESC, mealCount));
                    childRoom.setBreakFastCount(roomInfo.getDailyRates().get(0).getMeals());
                }
                // 加床
                childRoom.setAddBedDesc(Optional.ofNullable(roomInfo).map(RoomInfoEntity::getAddBedInfo).map(AddBedEntity::getAddBedPriceDesc).orElse(""));
                // 床型
                fillBedType(roomInfo, childRoom);
                // 窗型
                childRoom.setHasWindow(getHasWindow(roomInfo.getRoomStaticInfo()));

                CancelRuleEnum cancelRuleEnum = CancelRuleEnum.getByCode(Optional.ofNullable(roomInfo.getBookingRules())
                    .map(BookingRulesInfo::getCancelRuleInfo).map(CancelRuleInfoEntity::getCancelRule).orElse(null));
                childRoom.setCancelationPolicy(
                    Optional.ofNullable(cancelRuleEnum).map(CancelRuleEnum::getType).orElse(null));
                // 确认政策
                childRoom.setJustifyConfirm(roomInfo.getBookingRules().getJustifyConfirm());
                childRoom.setSupplierName(supplierProductBo.getSupplierName());
                childRoom.setSupplierCode(supplierProductBo.getSupplierCode());
                childRoom.setOtherInfo(childRoomDescList);

                // 根据支付类型获取金额
                childRoom.setPrice(
                        Optional.ofNullable(roomInfo.getAvgSalePriceIncludeTax()).orElse(BigDecimal.ZERO));

                // 宽带
                childRoom.setWifi(Optional.ofNullable(roomInfo.getBroadBand()).orElse("无"));

                // 缓存部分字段
                // 人数
                childRoom.setGuestPerson(roomInfo.getMaxGuestNumber());
                // 最晚到店时间取入住当天23:59:59
                childRoom.setLastArrivalTime(this.getLastArrivalTime(requestBo.getCheckInDate()));
                childRoom.setLastCancelTime(Optional.ofNullable(roomInfo.getBookingRules())
                    .map(BookingRulesInfo::getCancelRuleInfo).map(CancelRuleInfoEntity::getLastCancelTimeInfo)
                    .map(LastCancelTimeEntity::getLastCancelTime).orElse(null));
                childRoom.setAmadeus(false);
                childRoom.setSmoke(roomInfo.getRoomStaticInfo().getNonSmokeDesc());
                childRoom
                    .setAveragePrice(Optional.ofNullable(roomInfo.getAvgSalePriceIncludeTax()).orElse(BigDecimal.ZERO));
                String roomType = roomInfo.getRoomType();
                childRoom.setRoomType(roomType);
                childRoom.setProductId(roomInfo.getProductId());
                List<DailyMealInfo> dailyMealInfos = Optional.ofNullable(roomInfo.getRoomMealInfo())
                    .map(RoomMealEntity::getDailyMealInfo).orElse(new ArrayList<>()).stream().map(e -> {
                        String breakfastDesc = e.getDailyMealInfo().stream().findFirst().orElse("无早");
                        if (StringUtils.isBlank(breakfastDesc)) {
                            return null;
                        }
                        DailyMealInfo dailyMealInfo = new DailyMealInfo();
                        dailyMealInfo.setEffectDate(e.getEffectDate());
                        dailyMealInfo.setBreakfastDesc(breakfastDesc);
                        return dailyMealInfo;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                childRoom.setDailyMealInfoList(dailyMealInfos);
                List<DailyRate> dailyRateList =
                    Optional.ofNullable(roomInfo.getDailyRates()).orElse(new ArrayList<>()).stream().map(e -> {
                        DailyRate dailyRate = new DailyRate();
                        dailyRate.setEffectDate(e.getEffectDate());
                        dailyRate.setSalePrice(e.getSalePriceIncludeTax());
                        return dailyRate;
                    }).collect(Collectors.toList());
                childRoom.setDailyRateList(dailyRateList);
                Optional.ofNullable(roomInfo.getBookingRules()).map(BookingRulesInfo::getApplicativeAreaInfo)
                    .ifPresent(e -> {
                        childRoom.setApplicativeAreaTitle(e.getApplicativeAreaTitle());
                        childRoom.setApplicativeAreaDesc(e.getApplicativeAreaDesc());
                    });
                childRoom.setRoomKey(UuidUtil.getUUid());
                childRoom.setPackageRoom(Optional.ofNullable(roomInfo.getPackageRoomInfo()).map(RoomInfoEntity.PackageRoomInfoType::getPackageRoom).orElse(false));
                childRoom.setPackageId(Optional.ofNullable(roomInfo.getPackageRoomInfo()).map(RoomInfoEntity.PackageRoomInfoType::getPackageId).orElse(0));
                if(Objects.equals("PUB", requestBo.getCorpPayType()) && CollectionUtils.isNotEmpty(applyTripItemBoList)) {
                    // 校验
                    List<ApplyTripItemBo> checkApplyTripItemList = applyTripService.checkApplyTripItemList(
                            applyTripItemBoList,
                            ApplyTripControlBo.builder()
                                    .star(Optional.ofNullable(response).map(GetHotelDetailV2ResponseType::getHotelDetailInfo).map(HotelDetailInfoEntity::getHotelBaseInfo).map(DetailBaseInfoEntity::getHotelStarInfo).map(HotelStarEntity::getStarNum).orElse(null))
                                    .avgAmount(childRoom.getPrice())
                                    .wantCheckItemCodeList(Arrays.asList("hotelProductType", "hotelAveragePrice")).build());
                    List<HotelDetailResponseVO.FieldObject> exceedApplyFieldList = checkApplyTripItemList.stream().filter(item -> item != null && Boolean.TRUE.equals(item.getOverLimit())).map(item -> {
                        HotelDetailResponseVO.FieldObject fieldObject = new HotelDetailResponseVO.FieldObject();
                        fieldObject.setKey(item.getCode());
                        fieldObject.setLabel(item.getName());
                        fieldObject.setValue(item.getOverLimitDesc());
                        return fieldObject;
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(exceedApplyFieldList)) {
                        childRoom.setOperateType(ControlTypeEnum.F.getValue());
                        childRoom.setExceedApplyFieldList(exceedApplyFieldList);
                    }
                }
                // 酒店积分标签
                String groupId = Optional.ofNullable(response.getHotelDetailInfo()).map(HotelDetailInfoEntity::getHotelBaseInfo)
                        .map(DetailBaseInfoEntity::getHotelBrandInfo).map(HotelBrandEntity::getGroupId).orElse("");
                HotelBonusPointInfoDo hotelBonusPointInfoDo = hotelBonusPointService.getHotelBonusPointInfo(supplierProductBo.getSupplierCode(), groupId);
                log.info("获取酒店积分信息 groupId={} supplierCode={} hotelBonusPointInfo={}", groupId, supplierProductBo.getSupplierCode(), hotelBonusPointInfoDo);
                if (hotelBonusPointInfoDo != null) {
                    List<RoomInfoEntity.SaleRoomTag> saleRoomTags = roomInfo.getSaleRoomTags();
                    log.info("子房型标签信息 saleRoomTags={}", saleRoomTags);
                    if (CollectionUtils.isNotEmpty(saleRoomTags)) {
                        saleRoomTags.forEach(tag -> {
                            // 酒店积分标签填充
                            if (tag != null && ("JDJF".equals(tag.getTagCode()) || "JYJF".equals(tag.getTagCode()))) {
                                childRoom.setHotelBonusPoint(Boolean.TRUE);
                                List<String> hotelDetailPageRuleDescList = hotelBonusPointInfoDo.getHotelDetailPageRuleDescList();
                                if (CollectionUtils.isNotEmpty(hotelDetailPageRuleDescList)) {
                                    childRoom.setBonusPointDesc(hotelDetailPageRuleDescList.get(0));
                                }
                                log.info("酒店积分标签填充 childRoom={}", JsonUtils.toJsonString(childRoom));
                            }
                        });
                    }
                }


                // 赋值超标信息
                List<String> exceedMsgList = fillOverLimitInfo(requestBo,
                        travelStandard,
                        childRoom,
                        supplierProductBo,
                        initResponseBo,
                        response);
                childRoom.setExceedStandard(exceedMsgList);
                // 支付类型
                BalanceTypeEnum balanceTypeEnum = convertPayType(roomInfo, payInfo);
                if (balanceTypeEnum != null) {
                    childRoom.setPayType(balanceTypeEnum.getCode());
                    childRoom.setPayTypeDesc(balanceTypeEnum.getDesc());
                }

                // 是否可以预定
                Boolean canReserve = roomInfo.getBookingRules().getCanReserve();
                childRoom.setButtonFlag(canReserve);

                // 房型弹窗文案
                childRoom.setModalInfo(convertModalInfo(canReserve, payInfo, requestBo, travelStandard, exceedMsgList));

                childRooms.add(childRoom);
                index++;
            }
            log.info("转换的房间列表为{}", JsonUtils.toJsonString(childRooms));
            boolean enabled = childRooms.stream().anyMatch(i -> Objects.equals(i.getButtonFlag(), Boolean.TRUE));
            room.setBasicRoomEnabled(enabled);
            childRooms = childRooms.stream()
                .sorted(Comparator.comparing(HotelDetailResponseVO.ChildRoom::getButtonFlag).reversed())
                .collect(Collectors.toList());
            room.setChildRooms(childRooms);
            BigDecimal price = childRooms.stream().map(ChildRoom::getPrice).min(Comparator.naturalOrder()).orElse(BigDecimal.ZERO);
            roomDetail.setPrice(price);
            room.setRoomDetail(roomDetail);
            return room;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<Integer> buildStarTravelStandard(List<TravelStandardRuleVO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }

        StarRuleVO starRuleVO = ruleList.stream()
                .filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), "StarRule"))
                .map(StarRuleVO.class::cast)
                .findFirst()
                .orElse(null);
        if (starRuleVO == null) {
            return null;
        }

        return buildStarList(starRuleVO.getStarList());
    }

    private List<Integer> buildStarList(List<Integer> starList) {
        if (CollectionUtils.isEmpty(starList)) {
            return null;
        }

        // 如果只有0就是不限
        if (starList.size() == 1 && starList.get(0) != null && starList.get(0) == 0) {
            return null;
        }

        Set<Integer> starSet = new HashSet<>(starList);
        if (starSet.contains(2)) {
            starSet.add(1);
        }
        return new ArrayList<>(starSet);
    }
    private BigDecimal obtainServiceFee(SupplierProductBo supplierProductBo,
            InitResponseBo initResponseBo, String roomType) {
        if ("meiya".equalsIgnoreCase(supplierProductBo.getSupplierCode())
                && ObjectUtil.isAllNotEmpty(initResponseBo, initResponseBo.getSupplierCompany())) { // 获取美亚服务费
            InitResponseBo.SupplierCompanyBo supplierCompany = initResponseBo.getSupplierCompany();
            if ("C".equalsIgnoreCase(roomType)
                    && ObjectUtil.isNotEmpty(supplierCompany.getPersonalPubServiceFee())) { // C 为协议酒店,取协议酒店服务费
                return supplierCompany.getPersonalPubServiceFee();
            } else if (ObjectUtil.isNotEmpty(supplierCompany.getAccountPubOtherServiceFee())) {
                return supplierCompany.getAccountPubOtherServiceFee();
            }
        }
        return new BigDecimal(0);
    }
    private List<String> fillOverLimitInfo(LocalHotelDetailRequestBo requestBo,
            HotelControlVo travelStandard,
            HotelDetailResponseVO.ChildRoom childRoom,
            SupplierProductBo supplierProductBo,
            InitResponseBo initResponseBo,
            GetHotelDetailV2ResponseType response) {

        log.info("填充超标信息：{}", JsonUtils.toJsonString(travelStandard));
        BigDecimal serviceFee = obtainServiceFee(supplierProductBo, initResponseBo, childRoom.getRoomType());

        // 非因公支付/差标为空
        if (!Objects.equals(PUB, requestBo.getCorpPayType()) || travelStandard == null) {
            return Collections.emptyList();
        }

        List<String> exceedStandard = Lists.newArrayListWithExpectedSize(3);
        // 均价差标不为空
        if (travelStandard.getAveragePriceSet() != null) {
            BigDecimal averagePrice = new BigDecimal(travelStandard.getAveragePriceSet().getPriceCeiling());
            if (averagePrice.compareTo(BigDecimal.ZERO) > 0
                    && childRoom.getPrice().add(serviceFee).compareTo(averagePrice) > 0) {
                String msg = String.format("根据公司差旅政策规定，你可预订￥%s以下酒店",
                        travelStandard.getAveragePriceSet().getPriceCeiling());
                exceedStandard.add(msg);
                childRoom.setOperateType(travelStandard.getControl());
                // 判断是否用浮动价格
                if (Objects.nonNull(travelStandard.getFloatAveragePriceSet())) {
                    // 如果房价大于浮动差表 走浮动的RC
                    if (childRoom.getPrice().compareTo(
                            new BigDecimal(travelStandard.getFloatAveragePriceSet().getPriceCeiling())) > 0) {
                        childRoom.setOperateType(travelStandard.getFloatControl());
                        childRoom.setFloatControl(travelStandard.getFloatControl());
                    }
                }
            }
        }
        // 星级差标不为空
        if (travelStandard.getAverageStarSet() != null) {
            AverageStarSet starRule = travelStandard.getAverageStarSet();

            Integer cellingStar =
                    Optional.of(starRule.getStarCeiling()).map(StarCeiling::getValue).map(Integer::parseInt).orElse(0);
            Integer floorStar =
                    Optional.of(starRule.getStarFloor()).map(StarFloor::getValue).map(Integer::parseInt).orElse(0);

            Integer hotelStar = response.getHotelRatePlan().getStar();

            // 存在酒店星级 并且 该酒店星级不在差标范围内
            if (null != hotelStar && (cellingStar < hotelStar || floorStar > hotelStar)) {
                exceedStandard.add(convertStarMsg(cellingStar, floorStar));
            }
        }

        // 品牌差标不为空
        AverageBrandSet brandSet = travelStandard.getBrandSet();
        if (brandSet != null && CollectionUtils.isNotEmpty(brandSet.getBrandInfoList())) {
            String hotelBrandId = Optional.ofNullable(response.getHotelDetailInfo())
                    .map(HotelDetailInfoEntity::getHotelBaseInfo)
                    .map(DetailBaseInfoEntity::getHotelBrandInfo)
                    .map(HotelBrandEntity::getBrandId)
                    .orElse(null);
            Set<String> standBrandIdSet =
                    brandSet.getBrandInfoList().stream().map(BrandInfo::getBrandId).collect(Collectors.toSet());
            if (null != hotelBrandId && !standBrandIdSet.contains(hotelBrandId)) {
                exceedStandard.add(convertBrandMsg(brandSet.getBrandInfoList()));
            }
        }

        log.info("填充超标信息 结果：{}", JsonUtils.toJsonString(exceedStandard));

        if (exceedStandard.size() <= 1) {
            return exceedStandard;
        }
        List<String> resList = new ArrayList<>();
        for (int i = 0; i < exceedStandard.size(); i++) {
            String msg = exceedStandard.get(i);
            resList.add(i + "、" + msg);
        }
        return resList;
    }
    private String buildStarMsg(List<Integer> starList) {
        if (CollectionUtils.isEmpty(starList)) {
            return null;
        }
        starList.sort((o1, o2) -> o2 - o1);

        int loopSize = 0;
        String muchString = "";
        if (starList.size() <= 2) {
            loopSize = starList.size();
        } else {
            loopSize = 2;
            muchString = "等";
        }

        StringBuilder startRuleMsg = new StringBuilder("你可预订");
        for (int i = 0; i < loopSize; i++) {
            Integer star = starList.get(i);
            startRuleMsg.append(star).append("星级、");
        }
        startRuleMsg.deleteCharAt(startRuleMsg.length() - 1);
        startRuleMsg.append(muchString).append("的酒店");
        return startRuleMsg.toString();
    }

    private String convertBrandMsg(List<BrandInfo> brandInfoList) {
        StringBuilder res = new StringBuilder("你可预定");
        String muchString = brandInfoList.size() > 2 ? "等" : "";
        brandInfoList.forEach(brandInfo -> res.append(brandInfo.getBrandName()).append("、"));
        res.deleteCharAt(res.length() - 1);
        res.append(muchString).append("的酒店");
        return res.toString();
    }

    private String convertStarMsg(Integer cellingStar, Integer floorStar) {

        int maxStar = 5;
        int minStar = 1;

        if (cellingStar != 0 && floorStar != 0) {
            minStar = floorStar;
            maxStar = cellingStar;
        } else if (cellingStar != 0) {
            // 有上限，无下限
            minStar = 1;
            maxStar = cellingStar;
        } else if (floorStar != 0) {
            // 无上限，有下限
            minStar = floorStar;
            maxStar = 5;
        }

        return buildStarMsg(IntStream.rangeClosed(Math.max(minStar, 1), Math.min(maxStar, 5))
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll));



    }

    private ModalInfo convertModalInfo(Boolean canReserve,
            List<PayInfoResponse> payInfo,
            LocalHotelDetailRequestBo request,
            HotelControlVo travelStandard,
            List<String> exceedMsgList) {

        String reqPayType = request.getCorpPayType().toUpperCase();

        // 资源售罄
        if (!Boolean.TRUE.equals(canReserve)) {
            return convertModalStatus2Info(DetailRoomModalStatusEnum.SALE_OUT);
        }

        // 无可用支付方式
        if (CollectionUtils.isEmpty(payInfo)) {
            return convertModalStatus2Info(DetailRoomModalStatusEnum.NOT_SUIT_PAY_TYPE);
        }

        // 支付方式Map；key：支付方式（ACCNT 公司支付 / PPAY 个人支付)
        Map<String, List<PayInfoResponse>> corpPayMethodMap =
                payInfo.stream().collect(Collectors.groupingBy(payType -> payType.getCode().toUpperCase()));
        Set<String> corpPayMethodSet = corpPayMethodMap.keySet();

        // 请求为因私
        if (OWN.equalsIgnoreCase(reqPayType)) {
            return OWNModalCheck(corpPayMethodSet);
        }

        // 请求为因公
        return PUBModalCheck(travelStandard, corpPayMethodSet, exceedMsgList);

    }

    private ModalInfo OWNModalCheck(Set<String> corpPayMethodSet) {
        // 公司不支持个人支付
        if (!corpPayMethodSet.contains(PPAY)) {
            return convertModalStatus2Info(DetailRoomModalStatusEnum.NOT_SUIT_PAY_TYPE);
        }
        return null;
    }

    private ModalInfo PUBModalCheck(HotelControlVo travelStandard,
            Set<String> corpPayMethodSet,
            List<String> exceedMsgList) {

        if (travelStandard == null) {
            return null;
        }
        // 未超标
        if (!EXCEED.equals(travelStandard.getExceed())) {
            return null;
        }
        // 超标禁止预定
        if (ControlTypeEnum.F.getValue().equals(travelStandard.getControl())) {
            return convertModalStatus2Info(String.join("\n", exceedMsgList),
                    DetailRoomModalStatusEnum.OVER_LIMIT.getButtonDefaultDesc());
        }
        // 超标允许预定，允许混付，但公司不支持公帐支付
        if (ControlTypeEnum.M.getValue().equals(travelStandard.getControl()) && !corpPayMethodSet.contains(ACCNT)) {
            return convertModalStatus2Info(DetailRoomModalStatusEnum.NOT_SUIT_PAY_TYPE);

        }
        return null;
    }

    private ModalInfo convertModalStatus2Info(DetailRoomModalStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        return convertModalStatus2Info(statusEnum.getDefaultDesc(), statusEnum.getButtonDefaultDesc());
    }

    private ModalInfo convertModalStatus2Info(String text, String buttonText) {
        ModalInfo modalInfo = new ModalInfo();
        modalInfo.setText(text);
        modalInfo.setButtonText(buttonText);
        return modalInfo;
    }



    private BalanceTypeEnum convertPayType(RoomInfoEntity roomInfo, List<PayInfoResponse> payInfo) {
        if (Objects.equals(BalanceTypeEnum.FG.getCode(), roomInfo.getBalanceType())) {
            return BalanceTypeEnum.FG;
        } else if (Objects.equals(BalanceTypeEnum.PP.getCode(), roomInfo.getBalanceType()) && payInfo != null) {
            return BalanceTypeEnum.PP;
        } else if (Objects.equals(BalanceTypeEnum.USE_FG.getCode(), roomInfo.getBalanceType())) {
            return BalanceTypeEnum.USE_FG;
        }
        return null;
    }

    private String getHasWindow(RoomStaticInfoEntity roomStaticInfo) {
        if (roomStaticInfo == null || roomStaticInfo.getWindowInfo() == null) {
            return null;
        }

        String hasWindow = null;

        Integer windowType = roomStaticInfo.getWindowInfo().getWindowType();
        if (windowType != null) {
            hasWindow = WindowTypeEnum.getWindowDesc(String.valueOf(windowType));
        }

        String windowTypeName = roomStaticInfo.getWindowInfo().getWindowTypeName();
        if (hasWindow == null && StringUtils.isNotBlank(windowTypeName)) {
            hasWindow = WindowTypeEnum.getWindowDesc(windowTypeName);
        }

        return hasWindow;
    }

    private String getProtocolTag(String supplierCode, RoomInfoEntity roomInfo,String productType) {
        if (roomInfo == null && ObjectUtil.isNull(productType)) {
            return null;
        }
        if (!HotelTypeEnum.C.getType().equalsIgnoreCase(roomInfo.getRoomType())) {
            return null;
        }
        if (SupplierEnum.MEIYA.getCode().equals(supplierCode)) {
            return "平台协议价";
        }
        return Boolean.TRUE.equals(roomInfo.getTMCPrice()) ? "平台协议价" : hotelCoreApolloDao.getCompanyShortName() + "协议价";
    }

    private void fillBedType(RoomInfoEntity roomInfo, ChildRoom childRoom) {
        String bedType = null;
        if (roomInfo != null && roomInfo.getRoomStaticInfo() != null) {
            bedType = getBedType(roomInfo.getRoomStaticInfo().getBedInfoList());
        }
        if (StringUtils.isBlank(bedType)) {
            bedType = "其他床型";
        }
        childRoom.setBedType(bedType);
    }

    private void fillBedType(BasicRoomInfo basicRoomInfo, RoomInfoEntity roomInfoEntity, RoomDetail roomDetail) {
        String bedType = null;
        if (basicRoomInfo != null && basicRoomInfo.getBasicRoomStaticInfo() != null) {
            bedType = getBedType( basicRoomInfo.getBasicRoomStaticInfo().getBedInfoList());
        }
        if (StringUtils.isBlank(bedType) && roomInfoEntity != null && roomInfoEntity.getRoomStaticInfo() != null) {
            bedType = getBedType(roomInfoEntity.getRoomStaticInfo().getBedInfoList());
        }
        if (StringUtils.isBlank(bedType)) {
            bedType = "其他床型";
        }
        roomDetail.setBedType(bedType);
    }

    private String getBedType(List<BedInfoEntity> bedInfoList) {
        if (CollectionUtils.isEmpty(bedInfoList)) {
            return null;
        }
        StringBuilder bedType = new StringBuilder();
        for (BedInfoEntity bedInfoEntity : bedInfoList) {
            if (bedInfoEntity == null) {
                continue;
            }
            List<ChildBedInfoEntity> childBedInfoList = bedInfoEntity.getChildBedInfoList();
            if (CollectionUtils.isEmpty(childBedInfoList)) {
                continue;
            }
            for (ChildBedInfoEntity childBedInfoEntity : childBedInfoList) {
                if (childBedInfoEntity == null) {
                    continue;
                }
                Integer bedCount = childBedInfoEntity.getBedCount();
                Float bedWidth = childBedInfoEntity.getBedWidth();
                String childBedTypeName = Null.or(childBedInfoEntity.getChildBedTypeName(), "");
                if (StringUtils.isNotBlank(childBedTypeName)) {
                    if (bedCount != null && !childBedTypeName.contains("张")) {
                        bedType.append(bedCount).append("张");
                    }
                    if (bedWidth != null && Float.compare(bedWidth, 0) > 0 && !childBedTypeName.contains("米") && !childBedTypeName.contains("m") && !childBedTypeName.contains("cm") && !childBedTypeName.contains("厘米")) {
                        bedType.append(String.format("%.1f", bedWidth)).append("米");
                    }
                    bedType.append(childBedTypeName);
                } else {
                    if (bedWidth != null && Float.compare(bedWidth, 0) > 0) {
                        if (bedCount != null && !childBedTypeName.contains("张")) {
                            bedType.append(bedCount).append("张");
                        }
                        bedType.append(String.format("%.1f", bedWidth)).append("米").append("床");
                    } else {
                        bedType.append("其他床型");
                    }
                }
                bedType.append("、");
            }
        }
        bedType.deleteCharAt(bedType.length() - 1);
        return bedType.toString();
    }

    /**
     * 获取取消策略描述
     *
     * @param cancelRuleInfo 取消策略实体
     * @return 取消策略描述
     */
    private ChildRoomDesc getCancelDesc(CancelRuleInfoEntity cancelRuleInfo) {
        if (cancelRuleInfo == null) {
            return null;
        }
        ChildRoomDesc childRoomDesc = new ChildRoomDesc();
        String cancelRule = cancelRuleInfo.getCancelRule();
        String lastCancelTime = Optional.ofNullable(cancelRuleInfo.getLastCancelTimeInfo())
            .map(LastCancelTimeEntity::getLastCancelTime).orElse(null);
        CancelRuleEnum cancelRuleEnum = CancelRuleEnum.getByCode(cancelRule);
        if (cancelRuleEnum == null) {
            return null;
        }
        switch (cancelRuleEnum) {
            case FREE:
                childRoomDesc.setTitle(FREE.getDesc());
                childRoomDesc.setContent("预订后可随时免费取消，请放心预订");
                break;
            case TIME_LIMIT:
                childRoomDesc.setTitle(TIME_LIMIT.getDesc());
                childRoomDesc.setContent(lastCancelTime + "之前可以免费取消，请放心预订");
                break;
            case NOT_ALLOWED:
                childRoomDesc.setTitle(NOT_ALLOWED.getDesc());
                childRoomDesc.setContent("订单确认后不可取消或修改");
                break;
            default:
                childRoomDesc = null;
                break;
        }
        List<LadderDeductionInfoEntity> ladderDeductionInfo = cancelRuleInfo.getLadderDeductionInfo();
        if (CollectionUtils.isEmpty(ladderDeductionInfo)) {
            return childRoomDesc;
        }
        if (StringUtils.isBlank(childRoomDesc.getTitle())) {
            childRoomDesc.setTitle(TIME_LIMIT.getDesc());
        }
        StringBuilder sb = new StringBuilder();
        for (LadderDeductionInfoEntity cur : ladderDeductionInfo) {
            if (cur == null) {
                continue;
            }
            LadderDeductionDetailEntity detail = cur.getLadderDeductionDetailInfo();
            if (detail == null) {
                continue;
            }
            LadderDeductionTypeEnum ladderDeductionTypeEnum = LadderDeductionTypeEnum.getByCode(cur.getDeductionType());
            if (ladderDeductionTypeEnum == null) {
                continue;
            }
            switch (ladderDeductionTypeEnum) {
                case FREE:
                    sb.append(MessageFormat.format(FREE_CANCEL, detail.getEndDeductTime()));
                    break;
                case LADDER:
                    sb.append(MessageFormat.format(LADDER_CANCEL, detail.getStartDeductTime(),
                        detail.getEndDeductTime(), detail.getOriginPrice().getPrice()));
                    break;
                case CANNOT_CANCEL:
                    sb.append(MessageFormat.format(CANNOT_CANCEL, detail.getStartDeductTime()));
                    break;
                default:
                    break;
            }
        }
        childRoomDesc.setContent(sb.toString());
        return childRoomDesc;
    }

    /**
     * 获取最晚到店时间
     */
    private String getLastArrivalTime(String time) {
        Date date = DateUtil.stringToDate(time, DateUtil.DF_YMD);
        if (date == null) {
            return null;
        }
        String result = DateUtil.dateToYMD(date) + " 23:59:59";
        log.info("LastArrivalTime取入住当天最后一秒时间：" + result);
        return result;
    }

    /**
     * 阶梯取消规则
     *
     * @param ladderDeductionInfo 供应商阶梯规则
     * @return 阶梯取消规则
     */
    private List<LadderDeductionEntity> getLadderDeduction(List<LadderDeductionInfoEntity> ladderDeductionInfo) {
        if (CollectionUtils.isEmpty(ladderDeductionInfo)) {
            return new ArrayList<>();
        }
        return ladderDeductionInfo.stream().map(e -> {
            LadderDeductionDetailEntity detail = e.getLadderDeductionDetailInfo();
            if (detail == null) {
                return null;
            }
            LadderDeductionEntity target = new LadderDeductionEntity();
            target.setStartTime(detail.getStartDeductTime());
            target.setEndTime(detail.getEndDeductTime());
            target.setDeductionRatio(detail.getDeductionRatio());
            target.setDeductionType(e.getDeductionType());
            target.setAmount(
                Optional.ofNullable(detail.getOriginPrice()).map(PriceEntity::getPrice).orElse(BigDecimal.ZERO));
            return target;
        }).collect(Collectors.toList());
    }

    private List<HotelDetailResponseVO.HotelPic> getHotelPicList(GetHotelDetailV2ResponseType response,
        HotelDetailResponseVO responseVo) {
        if (response == null || CollectionUtils.isEmpty(response.getHotelPicList())) {
            return Collections.emptyList();
        }
        List<HotelDetailResponseVO.HotelPic> hotelPicList = new ArrayList<>(response.getHotelPicList().size());
        Map<String, List<HotelDetailResponseVO.HotelPic>> hotelPicMap = new HashMap<>();
        List<HotelPicEntity> hotelPicEntityList = response.getHotelPicList();
        for (HotelPicEntity hotelPic : hotelPicEntityList) {
            HotelDetailResponseVO.HotelPic convert = JsonUtils.convert(hotelPic, HotelDetailResponseVO.HotelPic.class);
            hotelPicList.add(convert);
            if (StringUtils.isBlank(convert.getBasicRoomTypeId())) {
                continue;
            }
            if (hotelPicMap.get(convert.getBasicRoomTypeId()) != null) {
                List<HotelDetailResponseVO.HotelPic> hotelPics = hotelPicMap.get(convert.getBasicRoomTypeId());
                hotelPics.add(convert);
                hotelPicMap.put(convert.getBasicRoomTypeId(), hotelPics);
            } else {
                hotelPicMap.put(convert.getBasicRoomTypeId(), Lists.newArrayList(convert));
            }
        }

        List<Room> rooms = responseVo.getRooms();
        rooms.forEach(item -> {
            String roomId = item.getRoomID();
            RoomDetail roomDetail = item.getRoomDetail();
            if (CollectionUtils.isEmpty(roomDetail.getPicUrls())) {
                List<HotelDetailResponseVO.HotelPic> hotelPics = hotelPicMap.get(roomId);
                if (CollectionUtils.isEmpty(hotelPics) && hotelPicMap.size() > 0) {
                    Optional<List<HotelDetailResponseVO.HotelPic>> first = hotelPicMap.values().stream().findFirst();
                    if (first.isPresent()) {
                        hotelPics = first.get();
                    }
                }
                hotelPics = Optional.ofNullable(hotelPics).orElse(Collections.emptyList());
                roomDetail.setPicUrls(
                    hotelPics.stream().map(HotelDetailResponseVO.HotelPic::getPictureUrl).collect(Collectors.toList()));
                Optional<HotelDetailResponseVO.HotelPic> first = hotelPics.stream().findFirst();
                if (first.isPresent()) {
                    roomDetail.setLogoUrl(first.get().getPictureUrl());
                }
            }
        });

        return hotelPicList;
    }

    /**
     * 获取酒店地址
     *
     * @param response 酒店详情
     * @return 酒店地址
     */
    private AddressInfo getAddressInfo(GetHotelDetailV2ResponseType response) {
        if (response == null || response.getHotelDetailInfo() == null
            || response.getHotelDetailInfo().getHotelBaseInfo() == null) {
            return null;
        }
        DetailBaseInfoEntity hotelBaseInfo = response.getHotelDetailInfo().getHotelBaseInfo();
        HotelDetailResponseVO.AddressInfo addressInfo = new HotelDetailResponseVO.AddressInfo();
        addressInfo.setAddressDetail(
            Optional.ofNullable(hotelBaseInfo.getHotelPositionInfo()).map(PositionEntity::getHotelAddress).orElse(""));
        addressInfo.setDistrict(Optional.ofNullable(hotelBaseInfo.getHotelPositionInfo())
            .map(PositionEntity::getLocationInfo).map(GeoCommonEntity::getName).orElse(""));
        Optional.ofNullable(hotelBaseInfo.getHotelPositionInfo())
            .flatMap(e -> e.getCoordinateInfoList().stream()
                .filter(c -> Objects.equals(c.getMapType(), GAO_DE.getCode())).findFirst())
            .ifPresent(
                e -> addressInfo.setLatlon(Lists.newArrayList(String.valueOf(e.getLon()), String.valueOf(e.getLat()))));
        return addressInfo;
    }

    /**
     * 获取酒店提示
     *
     * @param response 酒店详情
     * @return 酒店提示
     */
    private List<String> getNotices(GetHotelDetailV2ResponseType response) {
        if (response == null || response.getHotelDetailInfo() == null) {
            return new ArrayList<>();
        }
        return Optional.ofNullable(response.getHotelDetailInfo().getImportantNotifyInfo()).orElse(new ArrayList<>())
            .stream().map(ImportantNotifyEntity::getNotifyText).collect(Collectors.toList());
    }

    /**
     * 获取设施数据
     *
     * @param response 酒店详情
     * @return 设施数据
     */
    private List<HotelFacility> getHotelFacilities(GetHotelDetailV2ResponseType response) {
        if (response == null || response.getHotelDetailInfo() == null) {
            return new ArrayList<>();
        }
        Map<String,
            List<String>> facilityMap = Optional.ofNullable(response.getHotelDetailInfo().getHotelFacilityInfo())
                .map(FacilityInfoEntity::getFacilityList)
                .map(FacilityListEntity::getFacilityList).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(FacilityEntity::getFacilityTypeName,
                    Collectors.mapping(FacilityEntity::getFacilityName, Collectors.toList())));
        List<HotelDetailResponseVO.Tip> tips = this.getServiceTips(facilityMap);
        return facilityMap.entrySet().stream().map(entity -> {
            HotelFacility hotelFacility = new HotelFacility();
            hotelFacility.setTitle(entity.getKey());
            hotelFacility.setContent(entity.getValue());
            return hotelFacility;
        }).collect(Collectors.toList());
    }

    /**
     * 获取酒店设施详情
     * 
     * @param response 供应商返回的酒店设施详情
     * @return 酒店设施详情
     */
    public void setHotelFacilityDetail(GetHotelDetailV2ResponseType response, HotelDetailResponseVO responseVo) {
        // 获取供应商设施数据并判断是否为空
        Optional<FacilityDetailEntity> facilityDetailEntityOptional =
            Optional.ofNullable(response)
                .map(GetHotelDetailV2ResponseType::getHotelDetailInfo)
                .map(HotelDetailInfoEntity::getHotelFacilityInfo)
                .map(FacilityInfoEntity::getFacilityDetail);
        if (!facilityDetailEntityOptional.isPresent()) {
            return;
        }
        FacilityDetailEntity facilityDetail = facilityDetailEntityOptional.get();

        log.info("酒店设施详情json:" + JsonUtils.toJsonString(facilityDetail));

        // 设置酒店设施组
        responseVo.setFacilityGroupList(getFacilityGroupList(facilityDetail));
        // 设置停车信息
        if (facilityDetail.getParkingPolicyInfo() == null) {
            return;
        }
        // 停车场信息
        if (CollectionUtils.isNotEmpty(facilityDetail.getParkingPolicyInfo().getParkingServiceInfoList())) {
            List<ParkingDetailInfo> parkingDetailInfos = facilityDetail.getParkingPolicyInfo()
                .getParkingServiceInfoList()
                .stream()
                .map(CommonSupplierLoader::getParkingDetailInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            responseVo.setParkingServiceInfoList(parkingDetailInfos);
        }

        // 充电桩信息
        if (CollectionUtils.isNotEmpty(facilityDetail.getParkingPolicyInfo().getChargingPointList())) {
            List<ChargingPointInfo> chargingPointInfos = facilityDetail.getParkingPolicyInfo()
                .getChargingPointList()
                .stream()
                .map(CommonSupplierLoader::getChargingPointInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            responseVo.setChargingPointList(chargingPointInfos);
        }
    }

    /**
     * 调服务商接口查询地址信息
     * @param request
     * @return
     */
    public List<SearchAddressResponse> searchAddressFromSupplier(SearchAddressRequest request, SupplierProductBo supplierProductBo) {
        List<SearchAddressResponse> result = new ArrayList<>();
        SearchAddressSupplierRequest queryAddressListRequest = new SearchAddressSupplierRequest();
        queryAddressListRequest.setCorpID(supplierProductBo.getSupplierCorpId());
        queryAddressListRequest.setCityID(request.getCityId());
        queryAddressListRequest.setCityName(request.getCityName());
        queryAddressListRequest.setCityLimit(true);
        queryAddressListRequest.setKeyWord(request.getKeywords());
        queryAddressListRequest.setMapType(1);
        try {
            String response = HttpUtils.doPostJSONUseSignReplaceSupplierCardNo(supplierProductBo.getSupplierCode(), "酒店模糊搜索地址", supplierProductBo.getProductUrl(), JsonUtils.toJsonString(queryAddressListRequest), supplierProductBo.getUserKey());
            SearchAddressSupplierResponse supplierResponse = JSON.parseObject(response, SearchAddressSupplierResponse.class);
            if(supplierResponse==null || CollectionUtils.isEmpty(supplierResponse.getAddressList())){
                return result;
            }
            for(SearchAddressSupplierResponse.AddressList addressList:supplierResponse.getAddressList()){
                SearchAddressResponse address = new SearchAddressResponse();
                result.add(address);
                address.setCityId(request.getCityId());
                address.setCityName(request.getCityName());
                address.setAddress(addressList.getAddress());
                address.setAddressDetail(addressList.getAddressDetail());
                address.setLatitude(addressList.getLatitude());
                address.setLongitude(addressList.getLongitude());
                if(CollectionUtils.isEmpty(addressList.getSubAddressList())){
                    continue;
                }
                List<SubAddressVo> subAddressList = new ArrayList<>();
                address.setSubAddressList(subAddressList);
                for(SearchAddressSupplierResponse.AddressList.SubAddressList subAddress:addressList.getSubAddressList()){
                    SubAddressVo sub = new SubAddressVo();
                    subAddressList.add(sub);
                    sub.setAddress(subAddress.getAddress());
                    sub.setLongitude(subAddress.getLongitude());
                    sub.setLatitude(subAddress.getLatitude());
                    sub.setShortAddress(subAddress.getShortName());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("酒店模糊搜索地址异常", e);
            return result;
        }

    }

    /**
     * 获取酒店设施组
     * 
     * @param facilityDetail 供应商返回的酒店设施详情
     * @return 酒店设施组
     */
    private List<FacilityGroup> getFacilityGroupList(FacilityDetailEntity facilityDetail) {
        if (CollectionUtils.isEmpty(facilityDetail.getFacilityGroupList())) {
            return null;
        }
        List<FacilityGroup> facilityGroupList = new ArrayList<>();
        facilityDetail.getFacilityGroupList().forEach(group -> {
            if (CollectionUtils.isEmpty(group.getFacilityItemList())) {
                return;
            }
            FacilityGroup facilityGroup = new FacilityGroup();
            facilityGroup.setFacilityGroupName(group.getFacilityGroupName());
            facilityGroup.setFacilityGroupId(group.getFacilityGroupID());
            // 不返回ROOM类型设施
            List<FacilityItem> facilityList = group.getFacilityItemList().stream()
                .filter(item -> !ROOM.equalsIgnoreCase(item.getFacilityLimit()))
                .map(CommonSupplierLoader::getFacilityItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            // 不为空才添加
            if (CollectionUtils.isNotEmpty(facilityList)) {
                facilityGroup.setFacilityItemList(facilityList);
                facilityGroupList.add(facilityGroup);
            }
        });
        return facilityGroupList;
    }

    /**
     * 酒店设施实体转换
     * 
     * @param item 供应商返回的酒店设施实体
     * @return 酒店设施实体
     */
    private static FacilityItem getFacilityItem(FacilityItemEntity item) {
        return Optional.ofNullable(item).map(detail -> {
            FacilityItem facilityItem = new FacilityItem();
            facilityItem.setFacilityItemName(item.getFacilityItemName());
            Optional.ofNullable(item.getChargeInfo()).ifPresent(
                chargeEntity -> facilityItem.setChargeableDesc(chargeEntity.getChargeableDesc()));
            return facilityItem;
        }).orElse(null);
    }

    /**
     * 充电桩实体转换
     * 
     * @param chargingPointEntity 供应商返回的充电桩实体
     * @return 充电桩实体
     */
    private static ChargingPointInfo getChargingPointInfo(ChargingPointEntity chargingPointEntity) {
        return Optional.ofNullable(chargingPointEntity)
            .map(detail -> {
                ChargingPointInfo chargingPointInfo = new ChargingPointInfo();
                chargingPointInfo.setLocationDesc(detail.getLocationDesc());
                chargingPointInfo.setTypeDesc(detail.getTypeDesc());
                return chargingPointInfo;
            }).orElse(null);
    }

    /**
     * 停车场实体转换
     * 
     * @param parkingServiceInfoEntity 供应商返回的停车场实体
     * @return 停车场实体
     */
    private static ParkingDetailInfo getParkingDetailInfo(ParkingServiceEntity parkingServiceInfoEntity) {
        return Optional.ofNullable(parkingServiceInfoEntity)
            .map(ParkingServiceEntity::getParkingServiceDetail)
            .map(detail -> {
                ParkingDetailInfo parkingDetailInfo = new ParkingDetailInfo();
                parkingDetailInfo.setLocationDesc(detail.getLocationDesc());
                parkingDetailInfo.setReservedDesc(detail.getReservedDesc());
                parkingDetailInfo.setTypeDesc(detail.getTypeDesc());
                parkingDetailInfo.setChargeableDesc(detail.getChargeableDesc());
                return parkingDetailInfo;
            }).orElse(null);
    }

    /**
     * 获取周边设施数据
     *
     * @param response 酒店详情
     * @return 周边设施数据
     */
    private List<AroundFacility> getAroundFacilityList(GetHotelDetailV2ResponseType response) {
        if (response == null || response.getHotelDetailInfo() == null) {
            return new ArrayList<>();
        }
        // 获取供应商周边设施数据
        List<NearbyFacilityGroupEntity> hotelNearbyFacilityList =
            Optional.ofNullable(response.getHotelDetailInfo().getHotelFacilityInfo())
                .map(FacilityInfoEntity::getNearbyFacilityGroupList).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(hotelNearbyFacilityList)) {
            return new ArrayList<>();
        }
        // 按照周边设施分类分组
        return hotelNearbyFacilityList.stream().map((nearbyFacility) -> {
            HotelDetailResponseVO.AroundFacility aroundFacility = new HotelDetailResponseVO.AroundFacility();
            aroundFacility.setTitle(nearbyFacility.getNearbyFacilityGroupName());
            aroundFacility.setContent(nearbyFacility.getNearbyFacilityNameList());
            return aroundFacility;
        }).collect(Collectors.toList());
    }

    /**
     * 获取APP交通信息
     *
     * @param response 酒店详情
     * @return APP交通信息
     */
    private List<AppTransport> getAppTransports(GetHotelDetailV2ResponseType response) {
        if (response == null || response.getHotelDetailInfo() == null
            || CollectionUtils.isEmpty(response.getHotelDetailInfo().getHotelTrafficInfoGroupList())) {
            return new ArrayList<>();
        }
        List<TrafficInfoGroupEntity> hotelTrafficInfoGroupList =
            response.getHotelDetailInfo().getHotelTrafficInfoGroupList();
        return hotelTrafficInfoGroupList.stream().map(
            entity -> Optional.ofNullable(entity.getTrafficInfoList()).orElse(new ArrayList<>()).stream().map(e -> {
                AppTransport transport = new AppTransport();
                transport.setDetail(e.getLandMarkName() + e.getTrafficInfoDes());
                transport.setDistance("");
                return transport;
            }).collect(Collectors.toList())).reduce(BaseUtils::combine).orElse(new ArrayList<>());
    }

    /**
     * 获取PC交通信息
     *
     * @param response 酒店详情
     * @return PC交通信息
     */
    private List<PcTransport> getPcTransports(GetHotelDetailV2ResponseType response) {
        if (response == null || response.getHotelDetailInfo() == null
            || CollectionUtils.isEmpty(response.getHotelDetailInfo().getHotelTrafficInfoGroupList())) {
            return new ArrayList<>();
        }
        List<TrafficInfoGroupEntity> hotelTrafficInfoGroupList =
            response.getHotelDetailInfo().getHotelTrafficInfoGroupList();
        return hotelTrafficInfoGroupList.stream().map(entity -> {
            HotelDetailResponseVO.PcTransport transport = new HotelDetailResponseVO.PcTransport();
            transport.setTitle(entity.getTrafficInfoGroupName());
            List<HotelDetailResponseVO.Content> contents = Optional.ofNullable(entity.getTrafficInfoList())
                .orElse(new ArrayList<>()).stream().map(e -> {
                    HotelDetailResponseVO.Content content = new HotelDetailResponseVO.Content();
                    content.setName(e.getLandMarkName());
                    content.setDetail(e.getTrafficInfoDes());
                    return content;
                }).collect(Collectors.toList());
            transport.setContent(contents);
            return transport;
        }).collect(Collectors.toList());
    }

    /**
     * 获取酒店政策
     *
     * @param response 酒店详情
     * @return 酒店政策
     */
    private HotelPolicy getHotelPolicy(GetHotelDetailV2ResponseType response) {
        if (response == null || response.getHotelDetailInfo() == null
            || response.getHotelDetailInfo().getHotelPolicyInfo() == null) {
            return null;
        }
        PolicyInfoEntity hotelPolicyInfo = response.getHotelDetailInfo().getHotelPolicyInfo();
        Optional<PolicyInfoEntity> policyInfoOptional = Optional.of(hotelPolicyInfo);
        HotelDetailResponseVO.HotelPolicy hotelPolicy = new HotelDetailResponseVO.HotelPolicy();
        hotelPolicy.setArrivalanddeparture(policyInfoOptional.map(PolicyInfoEntity::getArrivalAndDeparture)
            .map(e -> e.getArrivalDesc() + "" + e.getDepartureDesc()).orElse(""));
        hotelPolicy.setBreakfast(
            policyInfoOptional.map(PolicyInfoEntity::getMealPolicy).map(MealPolicyEntity::getBreakfastDesc).orElse(""));
        hotelPolicy.setCancel("");
        hotelPolicy.setChild(policyInfoOptional.map(PolicyInfoEntity::getChildAndAddBed)
            .map(ChildPolicyEntity::getChildLimitRule).orElse(""));
        hotelPolicy.setRequirements(policyInfoOptional.map(PolicyInfoEntity::getChildAndAddBed)
            .map(ChildPolicyEntity::getSpecialRemarks).orElse(""));
        hotelPolicy.setPet(hotelPolicyInfo.getPetPolicy());
        hotelPolicy.setPolicy("");
        return hotelPolicy;
    }

    /**
     * 获取酒店介绍
     *
     * @param response 酒店详情
     * @return 酒店介绍
     */
    private Intro getIntro(GetHotelDetailV2ResponseType response) {
        if (response == null || response.getHotelDetailInfo() == null
            || response.getHotelDetailInfo().getHotelIntroductionInfo() == null) {
            return null;
        }
        HotelDetailInfoEntity hotelDetailInfo = response.getHotelDetailInfo();
        HotelRatePlanEntity hotelRatePlan = response.getHotelRatePlan();
        HotelDetailResponseVO.Intro intro = new HotelDetailResponseVO.Intro();
        Optional<IntroductionEntity> introductionOptional =
            Optional.ofNullable(hotelDetailInfo.getHotelIntroductionInfo());
        intro.setFax("");
        Optional<DetailBaseInfoEntity> hotelBaseInfoOptional = Optional.ofNullable(hotelDetailInfo.getHotelBaseInfo());
        intro.setPhone(hotelBaseInfoOptional.map(DetailBaseInfoEntity::getHotelContactInfo)
            .map(HotelContactInfoType::getTelephone).orElse(""));
        intro
            .setOpenRenovationDesc(introductionOptional.map(IntroductionEntity::getHotelOpenRenovationDesc).orElse(""));
        intro.setLevel(hotelBaseInfoOptional.map(DetailBaseInfoEntity::getHotelStarInfo)
            .map(HotelStarEntity::getStarNum).map(String::valueOf).orElse(""));
        intro.setStarLicence(hotelBaseInfoOptional.map(DetailBaseInfoEntity::getHotelStarInfo).map(HotelStarEntity::getIconType).map(item -> StringUtils.equalsIgnoreCase(item, "STAR")).orElse(false));
        intro.setRoomQuantity(0);
        intro.setHotelOpenRenovationDesc(
            introductionOptional.map(IntroductionEntity::getHotelOpenRenovationDesc).orElse(""));
        intro.setDesc(introductionOptional.map(IntroductionEntity::getHotelIntroductionInfo)
            .map(CommonInfoEntity::getContent).orElse(""));
        String type = hotelBaseInfoOptional.map(DetailBaseInfoEntity::getHotelStarInfo).map(HotelStarEntity::getStarNum)
            .map(HotelStarTypeEnum::getDescByCode).orElse("");
        intro.setHotelType(type);
        Map<String, List<String>> facilityMap =
            Optional.ofNullable(hotelDetailInfo.getHotelFacilityInfo()).map(FacilityInfoEntity::getFacilityList)
                .map(FacilityListEntity::getFacilityList).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(FacilityEntity::getFacilityTypeName,
                    Collectors.mapping(FacilityEntity::getFacilityName, Collectors.toList())));
        List<HotelDetailResponseVO.Tip> tips = this.getServiceTips(facilityMap);
        intro.setServiceTips(tips);
        intro.setBaseInfoDesc(introductionOptional.map(IntroductionEntity::getHotelBaseInfoDesc)
            .map(CommonInfoEntity::getContent).orElse(null));
        return intro;
    }

    /**
     * 获取酒店图片列表
     *
     * @param response 酒店详情
     * @return 图片列表
     */
    private List<PicBean> getPicBeans(GetHotelDetailV2ResponseType response, SupplierProductBo supplierProductBo) {
        List<HotelDetailResponseVO.PicBean> list = new ArrayList<>();
        if (Objects.equals(supplierProductBo.getSupplierCode(), "ctrip")) {
            if (response.getHotelDetailInfo().getHotelBaseInfo().getHotelPictureInfo() == null) {
                return new ArrayList<>();
            }
            PictureInfoEntity hotelPictureInfo = response.getHotelDetailInfo().getHotelBaseInfo().getHotelPictureInfo();
            if (StringUtils.isNotBlank(hotelPictureInfo.getHotelLogoURL())) {
                HotelDetailResponseVO.PicBean picBean = new HotelDetailResponseVO.PicBean();
                picBean.setPicUrl(hotelPictureInfo.getHotelLogoURL());
                picBean.setThumbnailUrl(convertPicUrl(hotelPictureInfo.getHotelLogoURL()));
                list.add(picBean);
            }
            Optional.ofNullable(hotelPictureInfo.getCommonPictureList()).ifPresent(x -> x.stream().forEach(y -> {
                HotelDetailResponseVO.PicBean picBean = new HotelDetailResponseVO.PicBean();
                picBean.setDesc(y.getCommonPictureTypeName());
                picBean.setPicUrl(y.getHotelLogoURL());
                picBean.setThumbnailUrl(convertPicUrl(y.getHotelLogoURL()));
                list.add(picBean);
            }));
        } else {
            if (response.getHotelDetailInfo().getHotelBaseInfo().getHotelPictureInfo() == null) {
                return new ArrayList<>();
            }
            PictureInfoEntity hotelPictureInfo = response.getHotelDetailInfo().getHotelBaseInfo().getHotelPictureInfo();
            if (StringUtils.isNotBlank(hotelPictureInfo.getHotelLogoURL())) {
                HotelDetailResponseVO.PicBean picBean = new HotelDetailResponseVO.PicBean();
                picBean.setPicUrl(hotelPictureInfo.getHotelLogoURL());
                picBean.setThumbnailUrl(hotelPictureInfo.getHotelLogoURL());
                list.add(picBean);
            }
            Optional.ofNullable(hotelPictureInfo.getCommonPictureList()).ifPresent(x -> x.stream().forEach(y -> {
                HotelDetailResponseVO.PicBean picBean = new HotelDetailResponseVO.PicBean();
                picBean.setDesc(y.getCommonPictureTypeName());
                picBean.setPicUrl(y.getHotelLogoURL());
                picBean.setThumbnailUrl(y.getHotelLogoURL());
                list.add(picBean);
            }));
        }

        return list;
    }

    /**
     * 获取评论信息
     *
     * @param response 酒店详情
     * @return 评论信息
     */
    private Comment getComment(GetHotelDetailV2ResponseType response) {
        if (response == null) {
            return null;
        }
        HotelDetailInfoEntity hotelDetailInfo = response.getHotelDetailInfo();
        HotelCommentInfoEntity hotelCommentInfo = hotelDetailInfo.getHotelCommentInfo();
        if (hotelCommentInfo == null) {
            return null;
        }
        HotelDetailResponseVO.Comment comment = new HotelDetailResponseVO.Comment();
        comment.setCount(hotelCommentInfo.getCommenterCount());
        List<String> ports = Lists.newArrayList();
        Optional.ofNullable(hotelCommentInfo.getScoreInfo()).ifPresent(e -> {
            Optional.ofNullable(e.getCleanliness()).ifPresent(s -> {
                ports.add(BigDecimalUtil.getScaleString(s, 2));
            });
            Optional.ofNullable(e.getService()).ifPresent(s -> {
                ports.add(BigDecimalUtil.getScaleString(s, 2));
            });
            Optional.ofNullable(e.getFacility()).ifPresent(s -> {
                ports.add(BigDecimalUtil.getScaleString(s, 2));
            });
            Optional.ofNullable(e.getLocation()).ifPresent(s -> {
                ports.add(BigDecimalUtil.getScaleString(s, 2));
            });
            Optional.ofNullable(e.getTotal()).ifPresent(s -> {
                comment.setAverage(BigDecimalUtil.getScaleString(s, 2));
            });
        });
        comment.setPorts(ports);
        return comment;
    }

    /**
     * 获取供应商酒店详情request
     *
     * @param requestBo 本地酒店详情入参
     * @param supplierProductBo 供应商产品信息
     * @return 供应商酒店详情request
     */
    private GetHotelDetailV2RequestType convertDetailRequest(LocalHotelDetailRequestBo requestBo,
        SupplierProductBo supplierProductBo) {
        GetHotelDetailV2RequestType request = new GetHotelDetailV2RequestType();
        BaseEntity baseInfo = new BaseEntity();
        if (StringUtils.equalsIgnoreCase(supplierProductBo.getSupplierCode(), "ctrip")) {
            baseInfo.setUid(supplierProductBo.getSupplierUid());
        } else {
            baseInfo.setUid(StringUtils.isBlank(supplierProductBo.getSupplierUid()) ? null : supplierProductBo.getSupplierUid());
        }
        baseInfo.setCorpId(supplierProductBo.getSupplierCorpId());
        baseInfo.setLanguage(LanguageEnum.ZH_CN.getCode());
        request.setBaseInfo(baseInfo);
        String hotelId = Optional.ofNullable(requestBo.getTree()).map(Tree::getKvs).map(e -> e.get(0))
            .map(Kv::getHotelId).orElse(null);
        request.setHotelId(hotelId);
        request.setCheckInDate(requestBo.getCheckInDate());
        request.setCheckOutDate(requestBo.getCheckOutDate());
        if (CollectionUtils.isNotEmpty(requestBo.getRooms())) {
            List<com.corpgovernment.api.hotel.booking.hotel.request.Room> roomList = JsonUtils.convert(requestBo.getRooms(), new TypeReference<List<com.corpgovernment.api.hotel.booking.hotel.request.Room>>() {}).stream().filter(Objects::nonNull).collect(Collectors.toList());
            request.setGuestQuantity(roomList.stream().mapToInt(a -> Optional.of(a).map(com.corpgovernment.api.hotel.booking.hotel.request.Room::getResidentList).map(List::size).orElse(0)).sum());
            request.setRoomQuantity(roomList.size());
        } else {
            request.setRoomQuantity(1);
            request.setGuestQuantity(1);
        }
        // 优惠券变价处理
        Integer roomQuantity = requestBo.getRoomQuantity();
        Integer guestQuantity = Null.or(requestBo.getGuestQuantity(), roomQuantity);
        if (roomQuantity != null) {
            log.info("优惠券变价case特殊处理");
            request.setRoomQuantity(roomQuantity);
            request.setGuestQuantity(guestQuantity);
        }
        request.setRoomfilter(this.getRoomFilter(requestBo));
        return request;
    }

    /**
     * 获取房型过滤条件
     *
     * @param requestBo 本地酒店详情入参
     * @return 房型过滤条件
     */
    private RoomFilterEntity getRoomFilter(LocalHotelDetailRequestBo requestBo) {
        RoomFilterEntity roomFilter = new RoomFilterEntity();
        // todo 协议酒店
        // 含早餐
        roomFilter.setHasbreakfast(requestBo.getBreakfast());
        // 立即确认
        roomFilter.setJustifyConfirm(requestBo.getCheckImmediate());
        // 免费取消
        roomFilter.setFreeCancel(requestBo.getFreeCancel());
        // 酒店积分
        roomFilter.setOnlyBonusPoint(requestBo.getOnlyBonusPoint());
        return roomFilter;
    }

    /**
     * 获取酒店列表返回
     *
     * @param response
     * @param requestBo
     * @param supplierProductBo
     * @return
     */
    private LocalHotelListResponseBo convertDataResponse(GetHotelDataV2ResponseType response,
        LocalHotelListRequestBo requestBo, SupplierProductBo supplierProductBo) {
        if (response == null || response.getHotelInfo() == null) {
            return null;
        }
        LocalHotelListResponseBo responseBo = new LocalHotelListResponseBo();
        List<HotelInfoType> hotelInfoList = response.getHotelInfo();
        SearchResultType searchResult = response.getSearchResult();
        // 供应商标识
        responseBo.setSupplier(supplierProductBo.getSupplierCode());
        responseBo.setSupplierName(supplierProductBo.getSupplierName());
        responseBo.setTotalCount(searchResult.getHotelCount());
        responseBo.setCurrentPage(requestBo.getPageNum());
        responseBo.setLastPage(searchResult.getLastPage());
        // 美亚只返回了酒店总数用于判断
        if (SupplierEnum.MEIYA.getCode().equals(supplierProductBo.getSupplierCode())
            && searchResult.getHotelCount() != null) {
            responseBo.setLastPage(requestBo.getPageNum() * requestBo.getPageSize() >= searchResult.getHotelCount());
        }
        responseBo.setPageSize(requestBo.getPageSize());
        responseBo.setTotalPage((searchResult.getHotelCount() + requestBo.getPageSize() - 1) / requestBo.getPageSize());
        List<LocalHotelListResponseBo.HotelListBean> hotelListBeans = hotelInfoList.stream().map(entity -> {
            LocalHotelListResponseBo.HotelListBean hotelListBean = new LocalHotelListResponseBo.HotelListBean();
            HotelBaseInfoType hotelInfo = entity.getHotelBaseInfo();
            if (hotelInfo == null) {
                log.warn("酒店基础信息异常");
                return null;
            }
            HotelGeoInfoType hotelGeoInfo = Optional.ofNullable(entity.getHotelStaticInfo())
                .map(HotelStaticInfoType::getHotelGeoInfo).orElse(new HotelGeoInfoType());
            HotelReviewInfoType hotelReviewInfo = Optional.ofNullable(entity.getHotelStaticInfo())
                .map(HotelStaticInfoType::getHotelReviewInfo).orElse(new HotelReviewInfoType());
            MinPriceRoomInfoType minPriceRoomInfo =
                Optional.ofNullable(entity.getMinPriceRoomInfo()).orElse(new MinPriceRoomInfoType());
            hotelListBean.setSupplier(supplierProductBo.getSupplierCode());
            hotelListBean.setSupplierName(supplierProductBo.getSupplierName());
            hotelListBean.setKey(String.valueOf(hotelInfo.getHotelId()));
            hotelListBean.setHotelId(String.valueOf(hotelInfo.getHotelId()));
            hotelListBean.setShowTMCLabel(entity.getHasContractRoom());
            // 协议标签
            hotelListBean.setProtocolTag(getProtocolTag(supplierProductBo.getSupplierCode(), entity));
            hotelListBean.setHotelName(hotelInfo.getHotelName());
            hotelListBean.setPic(hotelInfo.getHotelLogoUrl());
            hotelListBean.setScore(Objects.isNull(hotelReviewInfo.getHotelReviewScore()) ? "0.0"
                : String.valueOf(hotelReviewInfo.getHotelReviewScore()));
            LocalHotelListResponseBo.HotelListBean.StarLevel starLevel =
                new LocalHotelListResponseBo.HotelListBean.StarLevel();
            Integer hotelStar = hotelInfo.getHotelStar();
            starLevel.setKey(hotelStar != null && hotelStar > 0 ? String.valueOf(hotelStar) : "0");
            starLevel.setValue(HotelStarTypeEnum.getDescByCode(hotelStar));

            // 美亚的直接返回星级
            if (SupplierEnum.MEIYA.getCode().equals(supplierProductBo.getSupplierCode())) {
                starLevel.setLicence(HotelStarLicenceEnum.YES.getCode());;
            } else {
                // 是星级还是钻级
                starLevel.setLicence(HotelStarLicenceEnum.getByLicence(hotelInfo.getStarLicence()).getCode());
            }

            hotelListBean.setStarLevel(starLevel);
            hotelListBean.setDistance(getDistance(hotelGeoInfo.getLandMarkDistance()));
            IdNameType zoneInfo = Optional.ofNullable(hotelGeoInfo.getZoneInfoList()).orElse(new ArrayList<>()).stream()
                .findFirst().orElse(new IdNameType());
            String zoneName = StringUtils.isBlank(zoneInfo.getName()) ? "" : String.format("(%s)", zoneInfo.getName());
            hotelListBean
                .setAddress(Optional.ofNullable(hotelGeoInfo.getDistrictInfo()).map(IdNameType::getName).orElse("")
                    + hotelInfo.getHotelAddress() + zoneName);
            // 列表酒店距离话术
            hotelListBean.setAppPlace(zoneName);
            translateDistance(requestBo, hotelGeoInfo, hotelListBean);
            LocalHotelListResponseBo.HotelListBean.PriceBean priceBean =
                new LocalHotelListResponseBo.HotelListBean.PriceBean();
            // 目前只有一家供应商
            List<LocalHotelListResponseBo.HotelListBean.PriceBean> priceBeans = Lists.newArrayList();
            priceBean.setName(supplierProductBo.getSupplierName());
            priceBean.setPrice(BigDecimal.valueOf(
                Optional.ofNullable(minPriceRoomInfo.getMinSalePriceIncludeTax()).map(BigDecimal::intValue).orElse(0)));
            priceBeans.add(priceBean);
            hotelListBean.setPrice(priceBeans);
            // hotelListBean.setProtatal(hotelInfo.getShowTMCLabel());
            List<FacilityInfoType> facilityInfoList =
                Optional.ofNullable(entity.getHotelStaticInfo()).map(HotelStaticInfoType::getHotelFacilitiesInfo)
                    .map(HotelFacilitiesInfoType::getFacilityInfoList).orElse(new ArrayList<>());
            hotelListBean
                .setWifi(facilityInfoList.stream().anyMatch(e -> Objects.equals(e.getFacilityType(), WIFI.getCode())));
            hotelListBean.setParking(
                facilityInfoList.stream().anyMatch(e -> Objects.equals(e.getFacilityType(), PARKING.getCode())));
            Map<String, List<String>> facilityMap =
                facilityInfoList.stream().collect(Collectors.groupingBy(FacilityInfoType::getFacilityType,
                    Collectors.mapping(FacilityInfoType::getFacilityName, Collectors.toList())));
            List<HotelDetailResponseVO.Tip> serviceTips = getServiceTips(facilityMap);
            hotelListBean.setServiceTips(serviceTips);
            Optional.ofNullable(hotelGeoInfo.getHotelMapInfo()).orElse(new ArrayList<>())
                .stream().filter(e -> Objects.equals(GAO_DE.getCode(), e.getMapType())).findFirst().ifPresent(e -> {
                    hotelListBean.setLongitude(String.valueOf(e.getLon()));
                    hotelListBean.setLatitude(String.valueOf(e.getLat()));
                });
            hotelListBean.setPic(hotelInfo.getHotelLogoUrl());
            return hotelListBean;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        responseBo.setHotelList(hotelListBeans);
        return responseBo;
    }

    private String getProtocolTag(String supplierCode, HotelInfoType hotelInfoType) {
        if (SupplierEnum.MEIYA.getCode().equals(supplierCode)) {
            if (Boolean.TRUE.equals(hotelInfoType.getHasContractRoom())) {
                return "平台协议";
            }
            return null;
        }
        List<HotelTagType> hotelTagInfo = hotelInfoType.getHotelTagInfo();
        if (CollectionUtils.isEmpty(hotelTagInfo)) {
            return null;
        }
        String protocolTag = null;
        for (HotelTagType hotelTagType : hotelTagInfo) {
            if (hotelTagType == null || CollectionUtils.isEmpty(hotelTagType.getHotelTagList())) {
                continue;
            }
            boolean jumpLoop = false;
            for (HotelTagInfoType hotelTagInfoType : hotelTagType.getHotelTagList()) {
                if (hotelTagInfoType == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase("XYJ", hotelTagInfoType.getTagCode())) {
                    protocolTag = hotelCoreApolloDao.getCompanyShortName() + "协议";
                    jumpLoop = true;
                    break;
                } else if (StringUtils.equalsIgnoreCase("ZXJ", hotelTagInfoType.getTagCode())) {
                    protocolTag = "平台协议";
                }
            }
            if (jumpLoop) {
                break;
            }
        }
        return protocolTag;
    }

    protected String getDistance(Double distance) {
        if (distance == null || distance.equals(0.0)) {
            return null;
        }
        return String.valueOf(distance);
    }

    public String convertPicUrl(String url) {
        if (url == null) {
            return null;
        }
        // 设置酒店logo
        StringBuilder sb = new StringBuilder(url);
        try {
            if (url.contains("_R")) {
                sb.insert(url.indexOf("_R") + 2, "_480_480");
            } else if (StringUtils.isNotBlank(url)) {
                // 兼容上游图片后缀名不规范
                int index = url.lastIndexOf(".");
                if (index != -1) {
                    sb.insert(index, "_R_480_480");
                }
            }
        } catch (StringIndexOutOfBoundsException e) {
            log.info("错误图片url:{}", url);
        }
        return sb.toString();
    }

    public String getFilePrefix() {
        return hotelApollo.getCtripHotelPicUrlPrefix();
    }

    /**
     * 服务解析
     */
    private List<HotelDetailResponseVO.Tip> getServiceTips(Map<String, List<String>> facilityMap) {
        List<HotelDetailResponseVO.Tip> result = Lists.newArrayList();
        if (MapUtils.isEmpty(facilityMap)) {
            return result;
        }
        Set<String> facilityTypeSet = facilityMap.keySet();
        // 是否有wifi
        if (facilityTypeSet.contains(WIFI.getCode())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.WIFI.getCode())
                .desc(HotelServiceEnum.WIFI.getDesc()).build());
        }
        // 是否含有餐饮
        if (facilityTypeSet.contains(RESTAURANT.getCode())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.RESTAURANT.getCode())
                .desc(HotelServiceEnum.RESTAURANT.getDesc()).build());
        }
        // 是否有接送机服务
        if (facilityTypeSet.contains(Airport_Pick_up.getCode())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.RAIRPORT.getCode())
                .desc(HotelServiceEnum.RAIRPORT.getDesc()).build());
        }
        // 是否有娱乐设施
        if (Boolean.TRUE) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.FUNNY.getCode())
                .desc(HotelServiceEnum.FUNNY.getDesc()).build());
        }
        // 是否有健身房
        if (facilityTypeSet.contains(FITNESS_CENTER.getCode())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.FITNESS.getCode())
                .desc(HotelServiceEnum.FITNESS.getDesc()).build());
        }
        // 是否有游泳池
        if (facilityTypeSet.contains(SWIMMING_POOL.getCode())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.SWIMMING.getCode())
                .desc(HotelServiceEnum.SWIMMING.getDesc()).build());
        }
        return result;
    }

    /**
     * 解析距离
     */
    private void translateDistance(LocalHotelListRequestBo requestBo, HotelGeoInfoType hotelGeoInfo,
        LocalHotelListResponseBo.HotelListBean hotelListBean) {
        if (getDistance(hotelGeoInfo.getLandMarkDistance()) == null) {
            log.info("hotelGeoInfo.getLandMarkDistance() 为空");
            return;
        }
        String key = requestBo.getKey();
        Destination destination = this.getDestination(key);
        List<String> destinationTypeList =
            Lists.newArrayList(ZONE.getCode(), METRO_STATION.getCode(), SCENIC_AREA.getCode(), LANDMARK.getCode(),
                AIRPORT.getCode(), INTL_AIRPORT.getCode(), RAILWAY_STATION.getCode(), CORP_PLACE.getCode());
        boolean defaultChannel = destination != null && destinationTypeList.contains(destination.getDestinationType());
        BigDecimal landMarkDistance = BigDecimal.valueOf(hotelGeoInfo.getLandMarkDistance());
        String mulDistance = landMarkDistance.multiply(BigDecimal.valueOf(1000)).setScale(0, BigDecimal.ROUND_HALF_UP)
            .stripTrailingZeros().toPlainString();
        String distance = landMarkDistance.setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
        if (Boolean.TRUE.equals(requestBo.getTag())) {
            if (landMarkDistance.compareTo(BigDecimal.ONE) < 0) {
                hotelListBean.setAppDistance("距离您" + mulDistance + "米");
            } else {
                hotelListBean.setAppDistance("距离您" + distance + "公里");
            }
        } else if (StringUtils.isNotBlank(requestBo.getTitle()) && StringUtils.isNotBlank(requestBo.getKey())
            && defaultChannel) {
            if (landMarkDistance.compareTo(BigDecimal.ONE) < 0) {
                hotelListBean.setAppDistance("距离" + requestBo.getTitle() + mulDistance + "米");
            } else {
                hotelListBean.setAppDistance("距离" + requestBo.getTitle() + distance + "公里");
            }
        } else {
            String cityName = Optional.ofNullable(hotelGeoInfo.getCityInfo()).map(IdNameType::getName).orElse("");
            if (landMarkDistance.compareTo(BigDecimal.ONE) < 0) {
                hotelListBean.setAppDistance("距离" + cityName + "市中心" + mulDistance + "米");
            } else {
                hotelListBean.setAppDistance("距离" + cityName + "市中心" + distance + "公里");
            }
        }
    }

    /**
     * 获取酒店列表请求request
     *
     * @param requestBo
     * @param supplierProductBo
     * @param hotelCity
     * @return
     */
    private GetHotelDataV2RequestType convertDataRequest(LocalHotelListRequestBo requestBo,
        SupplierProductBo supplierProductBo, HotelCityBo hotelCity) {
        if (requestBo == null || supplierProductBo == null) {
            return null;
        }
        GetHotelDataV2RequestType request = new GetHotelDataV2RequestType();
        // 本次查询用户相关信息
        BaseEntity baseInfo = new BaseEntity();
        if (StringUtils.equalsIgnoreCase(supplierProductBo.getSupplierCode(), "ctrip")) {
            baseInfo.setUid(supplierProductBo.getSupplierUid());
        } else {
            baseInfo.setUid(StringUtils.isBlank(supplierProductBo.getSupplierUid()) ? null : supplierProductBo.getSupplierUid());
        }
        baseInfo.setCorpId(supplierProductBo.getSupplierCorpId());
        baseInfo.setLanguage(LanguageEnum.ZH_CN.getCode());
        request.setBaseInfo(baseInfo);
        // 房型层面的过滤条件
        RoomFilterType roomFilterInfo = new RoomFilterType();
        roomFilterInfo.setRoomPolicyFilter(this.getRoomPolicyFilter(requestBo));
        roomFilterInfo.setRoomPriceRange(this.getRoomPriceRange(requestBo));
        request.setRoomFilterInfo(roomFilterInfo);
        // 酒店层面的过滤条件
        HotelFilterType hotelFilterInfo = new HotelFilterType();
        hotelFilterInfo.setHotelInfoFilter(this.getHotelInfoFilter(requestBo));
        hotelFilterInfo.setHotelPositionFilter(this.getHotelPositionFilter(requestBo, hotelCity, supplierProductBo));
        hotelFilterInfo.setHotelFacilitiesFilter(this.getHotelFacilitiesFilter(requestBo));
        request.setHotelFilterInfo(hotelFilterInfo);
        // 酒店查询基础信息
        SearchBaseInfoType searchBaseInfo = new SearchBaseInfoType();
//        String key = requestBo.getKey();
//        log.info("酒店列表查询key：{}", key);
//        Destination destination = this.getDestination(key);
//        addElkInfoLog("酒店列表查询destination：%s", JsonUtils.toJsonString(destination));
//        if (destination != null && Objects.equals(HOTEL.getCode(), destination.getDestinationType())) {
//            searchBaseInfo.setHotelIdList(Lists.newArrayList(destination.getDestinationId()));
//        }
        if (CollectionUtils.isNotEmpty(requestBo.getHotelIdList())) {
            searchBaseInfo.setHotelIdList(requestBo.getHotelIdList());
        }
        searchBaseInfo.setCityId(requestBo.getSupplierCityId());
        searchBaseInfo.setCheckInDate(requestBo.getSDate());
        searchBaseInfo.setCheckOutDate(requestBo.getEDate());
        searchBaseInfo.setPagingInfo(this.getPagingInfo(requestBo));
        searchBaseInfo.setSortInfo(this.getSortInfo(requestBo.getRecommendSort()));
        if (!Objects.isNull(requestBo.getRoomQuantity()) && !Objects.isNull(requestBo.getGuestQuantity())) {
            searchBaseInfo.setRoomQuantity(requestBo.getRoomQuantity());
            searchBaseInfo.setGuestQuantity(requestBo.getGuestQuantity());
        }
        request.setSearchBaseInfo(searchBaseInfo);

        // 非携程 地址处理（商业区|地铁线|行政区） 这些传空，传经纬度过去
        if (!Objects.equals(SupplierEnum.CTRIP.getCode(), supplierProductBo.getSupplierCode())) {
            HotelPositionFilterType hotelPositionFilter = request.getHotelFilterInfo().getHotelPositionFilter();
            if (StringUtils.isNotBlank(hotelPositionFilter.getDistrictId())) {
                BdHpHotelAreaSupplierEntity areaSupplierEntity = hotelBasicDataService
                    .selectSupplierArea(hotelPositionFilter.getDistrictId(), supplierProductBo.getSupplierCode());
                hotelPositionFilter
                    .setDistrictId(Objects.nonNull(areaSupplierEntity) ? areaSupplierEntity.getSupplierAreaId() : "");
            } else {
                hotelPositionFilter.setDistrictId("");
            }
            if (Objects.nonNull(hotelPositionFilter.getMetroId()) || Objects.nonNull(hotelPositionFilter.getZoneId())
                || Objects.nonNull(hotelPositionFilter.getMetroStationId())) {

            } else {
                hotelPositionFilter.setMetroDistance(null);
            }
            hotelPositionFilter.setMetroId(null);
            hotelPositionFilter.setZoneId(null);
            hotelPositionFilter.setMetroStationId(null);
        }

        return request;
    }

    /**
     * 房型价格的区间筛选
     *
     * @param requestBo
     * @return
     */
    private RoomPriceRangeType getRoomPriceRange(LocalHotelListRequestBo requestBo) {
        RoomPriceRangeType roomPriceRange = new RoomPriceRangeType();
        roomPriceRange.setLowPrice(requestBo.getMinPrice());
        roomPriceRange.setHighPrice(requestBo.getMaxPrice());
        return roomPriceRange;
    }

    /**
     * 房型政策筛选条件
     *
     * @param requestBo
     * @return
     */
    private RoomPolicyFilterType getRoomPolicyFilter(LocalHotelListRequestBo requestBo) {
        RoomPolicyFilterType roomPolicyFilter = new RoomPolicyFilterType();
        roomPolicyFilter.setJustifyConfirm(requestBo.getCheckImmediate());
        roomPolicyFilter.setHasBreakfast(requestBo.getBreakfast());
        roomPolicyFilter.setFreeCancel(requestBo.getFreeCancel());
        return roomPolicyFilter;
    }

    /**
     * 根据酒店设施查询
     *
     * @param requestBo
     * @return
     */
    private HotelFacilitiesFilterType getHotelFacilitiesFilter(LocalHotelListRequestBo requestBo) {
        return new HotelFacilitiesFilterType();
    }

    /**
     * 根据酒店位置查询
     *
     * @param requestBo
     * @return
     */
    private HotelPositionFilterType getHotelPositionFilter(LocalHotelListRequestBo requestBo, HotelCityBo hotelCity,
        SupplierProductBo supplierProductBo) {
        log.info("【convertDataRequest】requestBo={} supplierProductBo={} hotelCity={}", requestBo, supplierProductBo,
            hotelCity);
        log.info("地图经纬范围查询入参: {}", JsonUtils.toJsonString(requestBo));
        HotelPositionFilterType hotelPositionFilter = new HotelPositionFilterType();
        MapSearchInfoType mapSearchInfo = null;
        if (StringUtils.isNotBlank(requestBo.getLatitude())) {
            mapSearchInfo = new MapSearchInfoType();
            mapSearchInfo.setLat(
                StringUtils.isNotBlank(requestBo.getLatitude()) ? Double.valueOf(requestBo.getLatitude()) : null);
            mapSearchInfo.setLon(
                StringUtils.isNotBlank(requestBo.getLongitude()) ? Double.valueOf(requestBo.getLongitude()) : null);
            mapSearchInfo.setMapType(GAO_DE.getCode());
            mapSearchInfo.setRadius(Objects.nonNull(requestBo.getRadius()) ? requestBo.getRadius() : 0.0);
            hotelPositionFilter.setMapSearchInfo(mapSearchInfo);
            log.info("地图经纬范围查询出参0：{}", JsonUtils.toJsonString(hotelPositionFilter));
        }
        String key = requestBo.getKey();

        if (StringUtils.isNotBlank(requestBo.getDistrict())) {
            if (StringUtils.isBlank(key)) {
                key = requestBo.getDistrict();
            }
            Destination districtDestination = this.getDestination(requestBo.getDistrict());
            if (districtDestination != null) {
                mapSearchInfo = new MapSearchInfoType();
                hotelPositionFilter.setDistrictId(districtDestination.getDestinationId());
                setLatitudeAndLongitude(requestBo, mapSearchInfo, districtDestination);
            }
        }

        if (StringUtils.isNotBlank(requestBo.getBusiness())) {
            if (StringUtils.isBlank(key)) {
                key = requestBo.getBusiness();
            }
            Destination businessDestination = this.getDestination(requestBo.getBusiness());
            if (businessDestination != null) {
                mapSearchInfo = new MapSearchInfoType();
                hotelPositionFilter.setZoneId(Lists.newArrayList(businessDestination.getDestinationId()));
                setLatitudeAndLongitude(requestBo, mapSearchInfo, businessDestination);
            }
        }

        if (StringUtils.isNotBlank(requestBo.getSubway())) {
            if (StringUtils.isBlank(key)) {
                key = requestBo.getSubway();
            }
            Destination subwayDestination = this.getDestination(requestBo.getSubway());
            if (subwayDestination != null) {
                mapSearchInfo = new MapSearchInfoType();
                setLatitudeAndLongitude(requestBo, mapSearchInfo, subwayDestination);
            }

        }
        Destination destination = this.getDestination(key);
        if (destination == null) {
            if (Objects.nonNull(mapSearchInfo) && mapSearchInfo.getLon() != null && mapSearchInfo.getLat() != null) {
                log.info("地图经纬范围查询出参1：{}", JsonUtils.toJsonString(hotelPositionFilter));
                hotelPositionFilter.setMapSearchInfo(mapSearchInfo);
                return hotelPositionFilter;
            }
            if (StringUtils.isEmpty(requestBo.getTitle()) && StringUtils.isEmpty(key)
                && StringUtils.isEmpty(requestBo.getLatitude())) {
                mapSearchInfo = this.getCitySearchInfo(hotelCity);
                hotelPositionFilter.setMapSearchInfo(mapSearchInfo);
            }
            log.info("地图经纬范围查询出参1：{}", JsonUtils.toJsonString(hotelPositionFilter));
            return hotelPositionFilter;
        }
        if (Objects.isNull(mapSearchInfo) || (mapSearchInfo.getLat() == null && mapSearchInfo.getLon() == null)) {
            mapSearchInfo = new MapSearchInfoType();
            setLatitudeAndLongitude(requestBo, mapSearchInfo, destination);
        }

        switch (destination.getDestinationType()) {
            case "ZONE":
                if (StringUtils.isBlank(requestBo.getBusiness())) {
                    hotelPositionFilter.setZoneId(Lists.newArrayList(destination.getDestinationId()));
                }
                break;
            case "LOCATION":
            case "SCENIC_AREA":
                if (StringUtils.isBlank(requestBo.getDistrict())) {
                    hotelPositionFilter.setDistrictId(destination.getDestinationId());
                }
                if (mapSearchInfo.getLat() != null && mapSearchInfo.getLon() != null) {
                    break;
                }
                if (StringUtils.isEmpty(requestBo.getLatitude())) {
                    mapSearchInfo = this.getCitySearchInfo(hotelCity);
                    hotelPositionFilter.setMapSearchInfo(mapSearchInfo);
                }
                log.info("地图经纬范围查询出参2：{}", JsonUtils.toJsonString(hotelPositionFilter));
                break;
            case "METRO":
                hotelPositionFilter.setMetroId(destination.getDestinationId());
                hotelPositionFilter.setMetroDistance(5);
                break;
            case "METRO_STATION":
            case "LANDMARK":
            case "AIRPORT":
            case "RAILWAY_STATION":
                mapSearchInfo.setRadius(Objects.nonNull(requestBo.getRadius()) ? requestBo.getRadius() : 0.0);
                break;
            case "PERSON":
                // TODO 个人订位需求
                requestBo.setTag(true);
                break;
            case "INTL_AIRPORT":
            case "CORP_PLACE":
            default:
                break;
        }
        if (mapSearchInfo.getLat() != null && mapSearchInfo.getLon() != null) {
            // 经确认地图查询去除范围限制，传入0不限距离
            if (Objects.isNull(mapSearchInfo.getRadius())) {
                mapSearchInfo.setRadius(0.0);
            }
            hotelPositionFilter.setMapSearchInfo(mapSearchInfo);
            log.info("地图经纬范围查询出参3：{}", JsonUtils.toJsonString(hotelPositionFilter));
        }

        if (mapSearchInfo.getLat() != null && mapSearchInfo.getLat() <= 0.1
            || mapSearchInfo.getLon() != null && mapSearchInfo.getLon() <= 0.1) {
            log.info("地图经纬范围查询出参4：{}", JsonUtils.toJsonString(hotelPositionFilter));
            hotelPositionFilter.setMapSearchInfo(null);
        }
        log.info("地图经纬范围查询出参5：{}", JsonUtils.toJsonString(hotelPositionFilter));
        return hotelPositionFilter;
    }

    private void setLatitudeAndLongitude(LocalHotelListRequestBo requestBo, MapSearchInfoType mapSearchInfo,
        Destination destination) {
        if (destination == null) {
            return;
        }
        if (StringUtils.isNotBlank(destination.getLat()) && StringUtils.isBlank(requestBo.getLatitude())) {
            mapSearchInfo.setLat(Double.valueOf(destination.getLat()));
            mapSearchInfo.setMapType(GAO_DE.getCode());
            mapSearchInfo.setRadius(Objects.nonNull(requestBo.getRadius()) ? requestBo.getRadius() : 0.0);
        }
        if (StringUtils.isNotBlank(destination.getLon()) && StringUtils.isBlank(requestBo.getLongitude())) {
            mapSearchInfo.setLon(Double.valueOf(destination.getLon()));
        }
    }

    private MapSearchInfoType getCitySearchInfo(HotelCityBo hotelCity) {
        MapSearchInfoType mapSearchInfo = new MapSearchInfoType();
        // 经纬度为空情况下设置为：-1（不指定范围）, 经纬度分销接口为必填项
        mapSearchInfo
            .setLat(Double.valueOf(StringUtils.isNotBlank(hotelCity.getCenterLat()) ? hotelCity.getCenterLat() : "-1"));
        mapSearchInfo
            .setLon(Double.valueOf(StringUtils.isNotBlank(hotelCity.getCenterLon()) ? hotelCity.getCenterLon() : "-1"));
        mapSearchInfo.setMapType(GAO_DE.getCode());
        mapSearchInfo.setRadius(0.0);
        return mapSearchInfo;
    }

    private MapSearchInfoType handleCityLocation(LocalHotelListRequestBo requestBo, String supplierCode,
        Double radius) {
        MapSearchInfoType mapSearchInfo;// 查询城市url
        MbSupplierProductResponse supplierInfo = commonService.getSupplierInfo(supplierCode,
            ProductTypeEnum.hotel.name(), hotelOperatorTypeConfig.getGetCityList());
        String cityProductUrl = supplierInfo.getProductUrl();
        CityRequest cityRequest = new CityRequest();
        CityRequest.CountryCityExtendParameterListBean countryCityExtendParameterListBean =
            new CityRequest.CountryCityExtendParameterListBean();
        countryCityExtendParameterListBean.setCityID(Integer.parseInt(requestBo.getCityCode()));
        countryCityExtendParameterListBean.setCountryCityExtendType("cityDetail");
        cityRequest.setCountryCityExtendParameterList(Lists.newArrayList(countryCityExtendParameterListBean));
        CityResponse cityResult = commonService.doPostJSON(supplierCode, "城市列表查询", cityProductUrl,
            supplierInfo.getUserKey(), JsonUtils.toJsonString(cityRequest), CityResponse.class);
        // 获取经纬度
        if (cityResult != null && CollectionUtils.isNotEmpty(cityResult.getCityAndCityList())) {
            String centerLat = cityResult.getCityAndCityList().get(0).getCenterLat();
            String centerLon = cityResult.getCityAndCityList().get(0).getCenterLon();
            mapSearchInfo = new MapSearchInfoType();
            mapSearchInfo.setLat(Double.valueOf(centerLat));
            mapSearchInfo.setLon(Double.valueOf(centerLon));
            mapSearchInfo.setMapType(GAO_DE.getCode());
            mapSearchInfo.setRadius(radius);
            return mapSearchInfo;
        }
        return null;
    }

    /**
     * 酒店信息筛选条件
     *
     * @param requestBo
     * @return
     */
    private HotelInfoFilterType getHotelInfoFilter(LocalHotelListRequestBo requestBo) {
        HotelInfoFilterType hotelInfoFilter = new HotelInfoFilterType();
        HotelBrandGroupFilterType hotelBrandGroupFilter = new HotelBrandGroupFilterType();
        List<String> category = Optional.ofNullable(requestBo.getCatagory()).orElse(new ArrayList<>());
        List<String> hotelBrand = category.stream().map(e -> {
            String[] array = StringUtils.split(e, "&");
            return ArrayUtils.isEmpty(array) ? null : array[0];
        }).filter(Objects::nonNull).collect(Collectors.toList());
        hotelBrandGroupFilter.setHotelBrand(hotelBrand);
        String key = requestBo.getKey();
        Destination destination = this.getDestination(key);
        log.info("key:{} destination:{}", key, destination);
        if (destination != null && Objects.equals(HOTEL_GROUP.getCode(), destination.getDestinationType())) {
            hotelBrandGroupFilter.setHotelGroup(Lists.newArrayList(destination.getDestinationId()));
        }
        if (destination != null && Objects.equals(HOTEL_BRAND.getCode(), destination.getDestinationType())) {
            hotelBrandGroupFilter.setHotelBrand(Lists.newArrayList(destination.getDestinationId()));
        }
        if (destination != null && Objects.equals(HOTEL.getCode(), destination.getDestinationType())) {
            hotelInfoFilter.setKeyword(requestBo.getTitle());
        }
        hotelInfoFilter.setHotelBrandGroupInfo(hotelBrandGroupFilter);
        if (StringUtils.isNotBlank(requestBo.getStarList())) {
            hotelInfoFilter.setHotelStar(
                Arrays.stream(requestBo.getStarList().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
        }
        hotelInfoFilter.setOnlyViewAgreementHotel(requestBo.getProtatal());
        // if (StringUtil.isBlank(key)) {
        // hotelInfoFilter.setKeyword(requestBo.getTitle());
        // }
        hotelInfoFilter.setKeyword(requestBo.getTitle());
        return hotelInfoFilter;
    }

    /**
     * 获取酒店查询条件
     *
     * @param key
     * @return
     */
    private Destination getDestination(String key) {
        log.info("【getDestination】key={}", key);
        addElkInfoLog("解析前的酒店查询条件：%s", key);
        if (StringUtils.isBlank(key)) {
            return null;
        }
        String[] array = StringUtils.split(key, "&");
        if (array == null) {
            return null;
        }
        Destination destination = new Destination();
        destination.setDestinationId(array[0]);
        destination.setDestinationType(array[1]);
        String lon = "*".equals(array[2]) || "null".equalsIgnoreCase(array[2]) ? null : array[2];
        String lat = "*".equals(array[3]) || "null".equalsIgnoreCase(array[3]) ? null : array[3];
        destination.setLat(lat);
        destination.setLon(lon);
        addElkInfoLog("解析后的酒店查询条件：%s", JsonUtils.toJsonString(destination));
        log.info("【getDestination】destination={}", JsonUtils.toJsonString(destination));
        return destination;
    }

    /**
     * 酒店查询
     */
    @Data
    static class Destination {
        /**
         * 查询id
         */
        private String destinationId;
        /**
         * 查询类型
         */
        private String destinationType;
        /**
         * 纬度
         */
        private String lat;
        /**
         * 经度
         */
        private String lon;
    }

    /**
     * 获取分页参数
     *
     * @param requestBo
     * @return
     */
    private PagingInfoType getPagingInfo(LocalHotelListRequestBo requestBo) {
        PagingInfoType pagingInfo = new PagingInfoType();
        pagingInfo.setPageIndex(requestBo.getPageNum());
        pagingInfo.setPageSize(requestBo.getPageSize());
        return pagingInfo;
    }

    /**
     * 获取排序规则
     *
     * @param recommendSort
     * @return
     */
    private SortInfoType getSortInfo(Integer recommendSort) {
        SortInfoType sortInfo = new SortInfoType();
        switch (recommendSort) {
            case 1:
                sortInfo.setSortDirection("ASC");
                sortInfo.setSortType("MIN_PRICE");
                break;
            case 2:
                sortInfo.setSortDirection("DESC");
                sortInfo.setSortType("MIN_PRICE");
                break;
            case 3:
                sortInfo.setSortDirection("ASC");
                sortInfo.setSortType("DISTANCE");
                break;
            case 4:
                sortInfo.setSortDirection("DESC");
                sortInfo.setSortType("DISTANCE");
                break;
            case 5:
                sortInfo.setSortDirection("ASC");
                sortInfo.setSortType("STAR");
                break;
            case 6:
                sortInfo.setSortDirection("DESC");
                sortInfo.setSortType("STAR");
                break;
            case 7:
                sortInfo.setSortDirection("ASC");
                sortInfo.setSortType("CUSTOMER_RATINGS");
                break;
            case 8:
                sortInfo.setSortDirection("DESC");
                sortInfo.setSortType("CUSTOMER_RATINGS");
                break;
            default:
                sortInfo.setSortDirection("DEFAULT");
                break;
        }
        return sortInfo;
    }
    /**
     * 酒店查询关键入参判空
     * @param request 分销入参
     * @return true 关键入参非空  false 关键入参为空
     */
    private boolean judgeRequestParams(GetHotelDataV2RequestType request){
        if (Objects.isNull(request) || Objects.isNull(request.getSearchBaseInfo())){
            return false;
        }
        SearchBaseInfoType searchBaseInfo = request.getSearchBaseInfo();
        if (StringUtils.isBlank(searchBaseInfo.getCheckInDate()) || StringUtils.isBlank(searchBaseInfo.getCheckOutDate())
                || StringUtils.isBlank(searchBaseInfo.getCityId())) {
            return false;
        }

        return true;
    }

}
