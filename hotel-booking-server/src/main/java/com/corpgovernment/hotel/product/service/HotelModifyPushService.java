package com.corpgovernment.hotel.product.service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.corpgovernment.hotel.booking.vo.hotelmodify.HotelModifyRequest;
import com.ctrip.corp.obt.generic.utils.DateUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.applytrip.enums.LineVerifyTypeEnum;
import com.corpgovernment.api.applytrip.metadata.ApplyTripStockOrderStatus;
import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficReturnType;
import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficType;
import com.corpgovernment.api.applytrip.soa.request.UseApplyTripTrafficRequest;
import com.corpgovernment.api.hotel.booking.enums.HotelOrderQueryEnum;
import com.corpgovernment.api.hotel.product.dto.HotelModifyPushRequest;
import com.corpgovernment.api.hotel.product.dto.HotelModifyPushResponse;
import com.corpgovernment.api.order.common.dos.OcApplyTripControlRecord;
import com.corpgovernment.api.order.common.enums.ApplyTripControlOperationTypeEnum;
import com.corpgovernment.api.order.common.enums.ApplyTripControlTypeEnum;
import com.corpgovernment.api.ordercenter.dto.request.HotelOrderTravelUpdateRequest;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.ValidGroup;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.common.utils.LogSplicingUtils;
import com.corpgovernment.hotel.booking.bo.CheckInOutDateInfoBo;
import com.corpgovernment.hotel.booking.enums.HotelModifyReasonEnum;
import com.corpgovernment.hotel.booking.enums.HotelModifyStatusEnum;
import com.corpgovernment.hotel.booking.valid.HotelOrderQueryGroup;
import com.corpgovernment.hotel.booking.valid.OrderDetailValidGroup;
import com.corpgovernment.hotel.booking.vo.hotelmodify.HotelModifyDetailSupplierRequest;
import com.corpgovernment.hotel.booking.vo.hotelmodify.HotelModifyDetailSupplierResponse;
import com.corpgovernment.hotel.product.dataloader.db.HoHotelApplyDetailLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoHotelApplyLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoHotelApplyStatusRecordLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoHotelLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoPassengerLoader;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CommonOrderClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoHotel;
import com.corpgovernment.hotel.product.entity.db.HoHotelApply;
import com.corpgovernment.hotel.product.entity.db.HoHotelApplyDetail;
import com.corpgovernment.hotel.product.entity.db.HoHotelApplyStatusRecord;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.entity.db.HoPassenger;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModificationDetailResponse;
import com.corpgovernment.hotel.product.supplier.CtripSlSupplier;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lilayzzz
 * @since: 2023/12/11
 * @description:
 */
@Service
@Slf4j
public class HotelModifyPushService {
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    @Autowired
    private HoHotelApplyStatusRecordLoader hoHotelApplyStatusRecordLoader;
    @Autowired
    private HoHotelApplyDetailLoader hoHotelApplyDetailLoader;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private HoHotelApplyLoader hoHotelApplyLoader;
    @Autowired
    private CtripSlSupplier ctripSlSupplier;
    @Autowired
    private HoPassengerLoader hoPassengerLoader;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HoHotelLoader hoHotelLoader;

    @Autowired
    private CommonOrderClientLoader commonOrderClientLoader;

    public HotelModifyPushResponse process(HotelModifyPushRequest request) {
        StringBuilder logContext = new StringBuilder();
        try {
            LogSplicingUtils.addLogContext(logContext, "开始处理修改单推送: %s", JsonUtils.toJsonString(request));

            // 根据供应商订单号查询订单
            HoOrder hoOrder = hoOrderLoader.selectBySupplierOrderId(request.getSupplierOrderId());
            LogSplicingUtils.addLogContext(logContext, "订单信息: %s", JsonUtils.toJsonString(hoOrder));
            if (Objects.isNull(hoOrder)) {
                LogSplicingUtils.addLogContext(logContext, "订单不存在");
                return getFailedByEnum(HotelResponseCodeEnum.ORDER_IS_NULL);
            }

            HotelOrderQueryEnum hotelOrderQueryEnum = HotelOrderQueryEnum.grepQueryEnumByCode(request.getHotelQueryEnum());
            Class<? extends ValidGroup> groups = null;
            if (hotelOrderQueryEnum != null){
                groups = HotelOrderQueryGroup.grepValidGroupByCode(hotelOrderQueryEnum);
            }

            // 供应商修改详情
            StandardOrderModificationDetailResponse supplierModifyDetail =
                ctripSlSupplier.getStandardSupplierModifyDetail(request.getModifyApplyID(), hoOrder, groups);
            LogSplicingUtils.addLogContext(logContext, "供应商修改详情: %s", JsonUtils.toJsonString(supplierModifyDetail));
            if (Objects.isNull(supplierModifyDetail)
                || CollectionUtils.isEmpty(supplierModifyDetail.getApplyFormDetailList())) {
                return getFailedByEnum(HotelResponseCodeEnum.FAILED_OBTAIN_REQUEST_MODIFICATION_ORDER_DETAILS);
            }

            // 比对申请单号
            Optional<StandardOrderModificationDetailResponse.ApplyFormDetail> applyFormDetailFirst =
                supplierModifyDetail.getApplyFormDetailList().stream()
                    .filter(item -> Objects.equals(item.getApplyFormID(), request.getModifyApplyID())).findFirst();
            if (!applyFormDetailFirst.isPresent()) {
                return getFailedByEnum(HotelResponseCodeEnum.FAILED_OBTAIN_REQUEST_MODIFICATION_ORDER_DETAILS);
            }

            StandardOrderModificationDetailResponse.ApplyFormDetail applyFormDetail = applyFormDetailFirst.get();
            LogSplicingUtils.addLogContext(logContext, "applyFormDetail: %s", JsonUtils.toJsonString(applyFormDetail));

            // 查看修改单ID是否存在
            HoHotelApply hoHotelApply = hoHotelApplyLoader.selectByApplyId(request.getModifyApplyID());
            LogSplicingUtils.addLogContext(logContext, "修改单申请: %s", JsonUtils.toJsonString(hoHotelApply));

            // 修改申请单不存在则补偿入库
            if (Objects.isNull(hoHotelApply)
                && (saveHotelApplyIfDontExists(hoOrder.getOrderId(), applyFormDetail, logContext))) {
                hoHotelApply = hoHotelApplyLoader.selectByApplyId(request.getModifyApplyID());
                LogSplicingUtils.addLogContext(logContext, "修改单申请: %s", JsonUtils.toJsonString(hoHotelApply));
            }

            // 修改单ID
            String applyId = hoHotelApply.getApplyId();
            // 平台订单号
            Long orderId = hoHotelApply.getOrderId();
            // 查询酒店信息
            HoHotel hoHotel = hoHotelLoader.selectByOrderId(orderId);
            // 查询入住人集合
            List<HoPassenger> passengerList = hoPassengerLoader.selectByOrderId(orderId);
            // 修改申请单信息
            saveHotelApply(hoHotelApply, applyFormDetail, logContext);
            // 修改单详情不存在则补偿入库
            saveHotelApplyDetailIfDontExists(applyId, applyFormDetail.getModifyRoomNight(), logContext);
            // 修改单状态记录
            saveHotelApplyStatusRecord(applyId, applyFormDetail.getStatusItemList(), logContext);
            List<HoHotelApplyDetail> hotelApplyDetailList = hoHotelApplyDetailLoader.select(applyId);
            // 回滚出差申请单
            rollbackApplyTrip(hoOrder, hoHotel, applyId, applyFormDetail.getStatus(), passengerList,
                hotelApplyDetailList, logContext);
            // 修改成功则更新实际离店时间
            boolean modifySuccess = HotelModifyStatusEnum.SUCCESS.getCode().equals(applyFormDetail.getStatus());
            if (modifySuccess) {
                updateOrderActualCheckOutTime(orderId, applyId);
                // 更新行程
                HoHotelApplyDetail modifyInfo = hotelApplyDetailList.stream()
                    .filter(item -> BooleanUtils.isTrue(item.getAfterRecord())).findFirst().orElse(null);
                updateOrderTravel(modifyInfo, orderId, hoOrder);
            }
        } catch (Exception e) {
            log.error("供应商修改单推送处理异常", e);
            return this.getFailedByEnum(HotelResponseCodeEnum.APPLY_MODIFY_STATUS_PUSH_FAILED);
        } finally {
            log.error("{}", logContext);
        }
        return HotelModifyPushResponse.success();
    }

    /**
     * 更新订单行程
     *
     * @param modifyInfo 修改信息
     * @param orderId 订单ID
     * @param hoOrder 酒店订单
     */
    private void updateOrderTravel(HoHotelApplyDetail modifyInfo, Long orderId, HoOrder hoOrder) {
        if (Objects.nonNull(modifyInfo) && StringUtils.isNotBlank(modifyInfo.getCheckInOutDateDetail())) {
            List<CheckInOutDateInfoBo> checkInOutDateInfoDetailList =
                JsonUtils.parseArray(modifyInfo.getCheckInOutDateDetail(), CheckInOutDateInfoBo.class);
            HotelOrderTravelUpdateRequest hotelTravelDTO = new HotelOrderTravelUpdateRequest();
            hotelTravelDTO.setOrderId(orderId);
            hotelTravelDTO.setOrderStatus(hoOrder.getOrderStatus());
            List<HotelOrderTravelUpdateRequest.Travel> travelList =
                new ArrayList<>(checkInOutDateInfoDetailList.size());
            for (CheckInOutDateInfoBo checkInOutDateInfo : checkInOutDateInfoDetailList) {
                HotelOrderTravelUpdateRequest.Travel travel = new HotelOrderTravelUpdateRequest.Travel();
                travel.setCheckInDate(checkInOutDateInfo.getCheckInDate());
                travel.setCheckOutDate(checkInOutDateInfo.getCheckOutDate());
                // 入住天数 = 离店日期 - 入住日期
                travel.setStayDays(
                    DateUtil.betweenDay(checkInOutDateInfo.getCheckInDate(),
                        checkInOutDateInfo.getCheckOutDate()));
                travelList.add(travel);
            }
            hotelTravelDTO.setTravelList(travelList);
            commonOrderClientLoader.updateHotelOrder(hotelTravelDTO);
        }
    }

    /**
     * 更新订单实际离店时间
     *
     * @param orderId 订单
     * @param applyId 申请ID
     */
    private void updateOrderActualCheckOutTime(Long orderId, String applyId) {
        List<HoHotelApplyDetail> hotelApplyDetailList = hoHotelApplyDetailLoader.select(applyId);
        Optional<HoHotelApplyDetail> hoHotelApplyDetailOpt =
            hotelApplyDetailList.stream().filter(item -> Boolean.TRUE.equals(item.getAfterRecord())).findFirst();
        hoHotelApplyDetailOpt.ifPresent(hoHotelApplyDetail -> {
            HoOrder updateRecord = new HoOrder();
            updateRecord.setOrderId(orderId);
            updateRecord.setActualCheckInTime(hoHotelApplyDetail.getCheckInDate());
            updateRecord.setActualCheckOutTime(hoHotelApplyDetail.getCheckOutDate());
            if (Objects.nonNull(hoHotelApplyDetail.getCheckInOutDateDetail())) {
                updateRecord
                    .setNewestCheckInOutDate(hoHotelApplyDetail.getCheckInOutDateDetail());
            }
            hoOrderLoader.updateByOrderId(updateRecord);
        });
    }

    /**
     * 回滚出差申请单
     *
     * @param hoOrder
     * @param hoHotel
     * @param applyId
     * @param passengerList
     * @param hotelApplyDetailList
     * @param logContext
     */
    private void rollbackApplyTrip(HoOrder hoOrder, HoHotel hoHotel, String applyId, Integer status,
        List<HoPassenger> passengerList, List<HoHotelApplyDetail> hotelApplyDetailList, StringBuilder logContext) {

        if (!HotelModifyStatusEnum.SUCCESS.getCode().equals(status)) {
            return;
        }

        if (StringUtils.isBlank(hoOrder.getTripApplyNo()) || Objects.isNull(hoOrder.getTripTrafficId())) {
            return;
        }
        
        if (CollectionUtils.isEmpty(hotelApplyDetailList)) {
            return;
        }

        LogSplicingUtils.addLogContext(logContext, "开始回滚出差申请单");

        UseApplyTripTrafficRequest hotelRequest = new UseApplyTripTrafficRequest();
        hotelRequest.setOrderId(String.valueOf(hoOrder.getOrderId()));
        hotelRequest.setApplyNo(hoOrder.getTripApplyNo());
        hotelRequest.setTrafficId(hoOrder.getTripTrafficId());
        hotelRequest.setAmount(BigDecimal.ZERO); // 提前离店不先归还金额
        List<String> uidList = passengerList
            .stream()
            .map(x -> StringUtils.isBlank(x.getUid()) ? String.valueOf(x.getNoEmployeeId()) : x.getUid())
            .collect(Collectors.toList());
        hotelRequest.setUid(uidList);

        hotelApplyDetailList.stream().filter(item -> BooleanUtils.isFalse(item.getAfterRecord())).findFirst()
            .ifPresent(item -> hotelRequest.setEndDate(DateUtil.dateToString(item.getCheckOutDate(), DateUtil.DF_YMD)));
        hotelApplyDetailList.stream().filter(item -> BooleanUtils.isTrue(item.getAfterRecord())).findFirst()
            .ifPresent(
                item -> hotelRequest.setStartDate(DateUtil.dateToString(item.getCheckOutDate(), DateUtil.DF_YMD)));

        int afterRecordRoomNight = hotelApplyDetailList.stream()
                .filter(item -> BooleanUtils.isTrue(item.getAfterRecord()))
                .mapToInt(HoHotelApplyDetail::getRoomNight)
                .sum();

        int beforeRecordRoomNight = hotelApplyDetailList.stream()
                .filter(item -> BooleanUtils.isFalse(item.getAfterRecord()))
                .mapToInt(HoHotelApplyDetail::getRoomNight)
                .sum();

        hotelRequest.setHotelCount(beforeRecordRoomNight - afterRecordRoomNight);
        hotelRequest.setEndCityCode(hoHotel.getCityId());

        hotelRequest.setTrafficType(EApplyTripTrafficType.HOTEL.getValue());
        hotelRequest.setReturnType(EApplyTripTrafficReturnType.HOTEL.getValue());
        hotelRequest.setBizOperation(ApplyTripStockOrderStatus.CHECK_OUT_EARLY.getValue());
        hotelRequest.setVerifyType(LineVerifyTypeEnum.ALL_TYPE.getCode());
        // 订单号:场景:申请单号:管控范围
        String bizKey =
            hotelRequest.getOrderId().concat(StrUtil.COLON).concat(hotelRequest.getBizOperation()).concat(StrUtil.COLON)
                .concat(applyId).concat(StrUtil.COLON).concat(String.valueOf(hotelRequest.getVerifyType()));
        hotelRequest.setBizKey(bizKey);

        HoHotelApplyDetail originalInfo = hotelApplyDetailList.stream()
            .filter(item -> BooleanUtils.isFalse(item.getAfterRecord())).findFirst().orElse(null);
        HoHotelApplyDetail modifyInfo = hotelApplyDetailList.stream()
            .filter(item -> BooleanUtils.isTrue(item.getAfterRecord())).findFirst().orElse(null);
        if (Objects.nonNull(originalInfo) && Objects.nonNull(modifyInfo)) {
            List<CheckInOutDateInfoBo> originalCheckInOutDateInfoDetailList =
                JsonUtils.parseArray(originalInfo.getCheckInOutDateDetail(), CheckInOutDateInfoBo.class);
            List<CheckInOutDateInfoBo> currentCheckInOutDateInfoDetailList =
                JsonUtils.parseArray(modifyInfo.getCheckInOutDateDetail(), CheckInOutDateInfoBo.class);
            // 计算originalCheckInOutDateInfoDetailList里面入住日期
            Set<String> originalDates = calculateDates(originalCheckInOutDateInfoDetailList);
            Set<String> currentDates = calculateDates(currentCheckInOutDateInfoDetailList);
            originalDates.removeAll(currentDates);
            // 回滚日期
            hotelRequest.setRollbackDates(originalDates);
        }
        OcApplyTripControlRecord controlRecord = new OcApplyTripControlRecord();
        controlRecord.setScene(hotelRequest.getBizOperation());
        controlRecord.setControlType(ApplyTripControlOperationTypeEnum.RELEASE.getCode());
        controlRecord.setOperationType(ApplyTripControlTypeEnum.CONTROL_ALL.getCode());
        controlRecord.setApplyId(applyId);

        LogSplicingUtils.addLogContext(logContext, "回滚出差申请单入参：%s", JsonUtils.toJsonString(hotelRequest));
        boolean result = applyTripClientLoader.rollbackApplyTripUseStatusAndRecord(hotelRequest, controlRecord);
        LogSplicingUtils.addLogContext(logContext, "回滚出差申请单结果：%s", result);
    }

    /**
     * 保存修改申请单信息
     * 
     * @param hoHotelApply
     * @param applyFormDetail
     */
    public void saveHotelApply(HoHotelApply hoHotelApply,
        StandardOrderModificationDetailResponse.ApplyFormDetail applyFormDetail,
        StringBuilder logContext) {
        if (!Objects.equals(applyFormDetail.getStatus(), hoHotelApply.getStatus())) {
            HoHotelApply updateHotelApply = new HoHotelApply();
            updateHotelApply.setId(hoHotelApply.getId());
            updateHotelApply.setApplyId(hoHotelApply.getApplyId());
            updateHotelApply.setStatus(applyFormDetail.getStatus());
            updateHotelApply.setReasonCode(applyFormDetail.getReasonCode());
            updateHotelApply
                .setReasonDesc(StringUtils.isNotBlank(applyFormDetail.getReasonDesc()) ? applyFormDetail.getReasonDesc()
                    : Optional.ofNullable(HotelModifyReasonEnum.getByCode(applyFormDetail.getReasonCode()))
                        .map(HotelModifyReasonEnum::getDesc).orElse(StringUtils.EMPTY));
            // 如供应商未返回此字段，默认落值提前离店和减少间数
            String scene = "6,9";
            List<StandardOrderModificationDetailResponse.ModifyContent> modifyContentList = applyFormDetail.getModifyContentList();
            if (CollectionUtils.isNotEmpty(modifyContentList)) {
                scene = modifyContentList.stream().map(StandardOrderModificationDetailResponse.ModifyContent::getScene)
                        .filter(Objects::nonNull).distinct().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA));
            }
            updateHotelApply.setScene(scene);
            LogSplicingUtils.addLogContext(logContext, "更新修改单状态参数: %s", JsonUtils.toJsonString(updateHotelApply));
            int updateHotelApplyResult = hoHotelApplyLoader.updateByPrimaryKey(updateHotelApply);
            LogSplicingUtils.addLogContext(logContext, "更新修改单状态结果: %s", updateHotelApplyResult);
        }
    }

    /**
     * 新增修改单申请信息
     * 
     * @param orderId
     * @param applyFormDetail
     * @param logContext
     */
    private boolean saveHotelApplyIfDontExists(Long orderId,
        StandardOrderModificationDetailResponse.ApplyFormDetail applyFormDetail, StringBuilder logContext) {
        HoHotelApply hotelApply = new HoHotelApply();
        hotelApply.setOrderId(orderId);
        hotelApply.setApplyId(applyFormDetail.getApplyFormID());
        hotelApply.setStatus(applyFormDetail.getStatus());
        hotelApply.setReasonCode(applyFormDetail.getReasonCode());
        hotelApply
            .setReasonDesc(StringUtils.isNotBlank(applyFormDetail.getReasonDesc()) ? applyFormDetail.getReasonDesc()
                : Optional.ofNullable(HotelModifyReasonEnum.getByCode(applyFormDetail.getReasonCode()))
                    .map(HotelModifyReasonEnum::getDesc).orElse(StringUtils.EMPTY));
        // 如供应商未返回此字段，默认落值提前离店和减少间数
        String scene = "6,9";
        List<StandardOrderModificationDetailResponse.ModifyContent> modifyContentList = applyFormDetail.getModifyContentList();
        if (CollectionUtils.isNotEmpty(modifyContentList)) {
            scene = modifyContentList.stream().map(StandardOrderModificationDetailResponse.ModifyContent::getScene)
                .filter(Objects::nonNull).distinct().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA));
        }
        hotelApply.setScene(scene);
        LogSplicingUtils.addLogContext(logContext, "新增修改单状态参数: %s", JsonUtils.toJsonString(hotelApply));
        int result = hoHotelApplyLoader.insert(hotelApply);
        LogSplicingUtils.addLogContext(logContext, "新增修改单状态结果: %s", result);
        return result > 0;
    }

    /**
     * 保存修改单详情
     * 
     * @param applyId
     * @param modifyRoomNight
     * @param logContext
     */
    public void saveHotelApplyDetailIfDontExists(String applyId,
        StandardOrderModificationDetailResponse.ModifyRoomNight modifyRoomNight, StringBuilder logContext) {

        List<HoHotelApplyDetail> hotelApplyDetailList = hoHotelApplyDetailLoader.select(applyId);
        if (CollectionUtils.isNotEmpty(hotelApplyDetailList)) {
            return;
        }
        StandardOrderModificationDetailResponse.OriginalRoomNight originalRoomNight =
            modifyRoomNight.getOriginalRoomNight();
        StandardOrderModificationDetailResponse.CurrentRoomNight currentRoomNight =
            modifyRoomNight.getCurrentRoomNight();
        if (Objects.isNull(originalRoomNight)
            || CollectionUtils.isEmpty(originalRoomNight.getOriginalRoomNightDetailInfoList())
            || Objects.isNull(currentRoomNight)
            || CollectionUtils.isEmpty(currentRoomNight.getCurrentRoomNightDetailInfoList())) {
            return;
        }

        HoHotelApplyDetail beforeHotelApplyDetail = getBeforeHotelApplyDetail(applyId, originalRoomNight);
        HoHotelApplyDetail afterHotelApplyDetail = getAfterHotelApplyDetail(applyId, currentRoomNight);
        if (Objects.isNull(beforeHotelApplyDetail) || Objects.isNull(afterHotelApplyDetail)) {
            return;
        }
        ArrayList<HoHotelApplyDetail> hoHotelApplyDetails =
            Lists.newArrayList(beforeHotelApplyDetail, afterHotelApplyDetail);
        LogSplicingUtils.addLogContext(logContext, "修改单详情入库参数: %s", JsonUtils.toJsonString(hoHotelApplyDetails));
        int result =
            hoHotelApplyDetailLoader.insertList(hoHotelApplyDetails);
        LogSplicingUtils.addLogContext(logContext, "修改单详情入库结果: %s", result);
    }

    /**
     * 组装修改前酒店信息
     * 
     * @param applyId
     * @param originalRoomNight
     * @return
     */
    public HoHotelApplyDetail getBeforeHotelApplyDetail(String applyId,
        StandardOrderModificationDetailResponse.OriginalRoomNight originalRoomNight) {
        HoHotelApplyDetail beforeHotelApplyDetail = new HoHotelApplyDetail();
        beforeHotelApplyDetail.setApplyId(applyId);
        beforeHotelApplyDetail.setAfterRecord(Boolean.FALSE);
        beforeHotelApplyDetail.setDatachangeCreatetime(new Date());
        beforeHotelApplyDetail.setDatachangeLasttime(new Date());
        beforeHotelApplyDetail.setRoomNight(originalRoomNight.getRestTotalRoomNight());
        // 获取checkInDate
        originalRoomNight.getOriginalRoomNightDetailInfoList().stream()
            .min(Comparator.comparing(StandardOrderModificationDetailResponse.OriginalRoomNightDetailInfo::getRoomDate))
            .map(StandardOrderModificationDetailResponse.OriginalRoomNightDetailInfo::getRoomDate)
            .map(date -> DateUtil.stringToDate(date, DateUtil.DF_YMD))
            .ifPresent(beforeHotelApplyDetail::setCheckInDate);
        // 获取checkOutDate
        originalRoomNight.getOriginalRoomNightDetailInfoList().stream()
            .max(Comparator.comparing(StandardOrderModificationDetailResponse.OriginalRoomNightDetailInfo::getRoomDate))
            .map(StandardOrderModificationDetailResponse.OriginalRoomNightDetailInfo::getRoomDate)
            .map(date -> DateUtil.stringToDate(date, DateUtil.DF_YMD))
            .ifPresent(date -> beforeHotelApplyDetail.setCheckOutDate(DateUtil.addDays(date, 1)));

        HotelModifyRequest.CheckInOutDetailInfo checkInOutDetailInfo = new HotelModifyRequest.CheckInOutDetailInfo();
        checkInOutDetailInfo.setCheckInDate(DateUtils.formatDate(beforeHotelApplyDetail.getCheckInDate(), DateUtils.DATE_FORMAT));
        checkInOutDetailInfo.setCheckOutDate(DateUtils.formatDate(beforeHotelApplyDetail.getCheckOutDate(), DateUtils.DATE_FORMAT));
        beforeHotelApplyDetail.setCheckInOutDateDetail(JsonUtils.toJsonString(CollectionUtils.newArrayList(checkInOutDetailInfo)));
        return beforeHotelApplyDetail;
    }

    /**
     * 组装修改后酒店信息
     * 
     * @param applyId
     * @param currentRoomNight
     * @return
     */
    public HoHotelApplyDetail getAfterHotelApplyDetail(String applyId,
        StandardOrderModificationDetailResponse.CurrentRoomNight currentRoomNight) {
        HoHotelApplyDetail afterHotelApplyDetail = new HoHotelApplyDetail();
        afterHotelApplyDetail.setApplyId(applyId);
        afterHotelApplyDetail.setAfterRecord(Boolean.TRUE);
        afterHotelApplyDetail.setDatachangeCreatetime(new Date());
        afterHotelApplyDetail.setDatachangeLasttime(new Date());
        afterHotelApplyDetail.setRoomNight(currentRoomNight.getRestTotalRoomNight());
        // 获取checkInDate
        currentRoomNight.getCurrentRoomNightDetailInfoList().stream()
            .min(Comparator.comparing(StandardOrderModificationDetailResponse.CurrentRoomNightDetailInfo::getRoomDate))
            .map(StandardOrderModificationDetailResponse.CurrentRoomNightDetailInfo::getRoomDate)
            .map(date -> DateUtil.stringToDate(date, DateUtil.DF_YMD))
            .ifPresent(afterHotelApplyDetail::setCheckInDate);
        // 获取checkOutDate
        currentRoomNight.getCurrentRoomNightDetailInfoList().stream()
            .max(Comparator.comparing(StandardOrderModificationDetailResponse.CurrentRoomNightDetailInfo::getRoomDate))
            .map(StandardOrderModificationDetailResponse.CurrentRoomNightDetailInfo::getRoomDate)
            .map(date -> DateUtil.stringToDate(date, DateUtil.DF_YMD))
            .ifPresent(date -> afterHotelApplyDetail.setCheckOutDate(DateUtil.addDays(date, 1)));

        HotelModifyRequest.CheckInOutDetailInfo checkInOutDetailInfo = new HotelModifyRequest.CheckInOutDetailInfo();
        checkInOutDetailInfo.setCheckInDate(DateUtils.formatDate(afterHotelApplyDetail.getCheckInDate(), DateUtils.DATE_FORMAT));
        checkInOutDetailInfo.setCheckOutDate(DateUtils.formatDate(afterHotelApplyDetail.getCheckOutDate(), DateUtils.DATE_FORMAT));
        afterHotelApplyDetail.setCheckInOutDateDetail(JsonUtils.toJsonString(CollectionUtils.newArrayList(checkInOutDetailInfo)));
        return afterHotelApplyDetail;
    }

    /**
     * 保存状态变更记录（即申请单处理记录）
     * 
     * @param applyId
     * @param statusItemList
     * @param logContext
     */
    public void saveHotelApplyStatusRecord(String applyId,
        List<StandardOrderModificationDetailResponse.StatusItem> statusItemList,
        StringBuilder logContext) {
        if (CollectionUtils.isEmpty(statusItemList)) {
            return;
        }
        List<HoHotelApplyStatusRecord> hotelApplyStatusRecordList = hoHotelApplyStatusRecordLoader.select(applyId);
        Map<Integer, HoHotelApplyStatusRecord> statusRecordMap =
            Optional.ofNullable(hotelApplyStatusRecordList).orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(HoHotelApplyStatusRecord::getStatus, Function.identity(), (k1, k2) -> k1));
        List<HoHotelApplyStatusRecord> hoHotelApplyStatusRecordList = new ArrayList<>(statusItemList.size());
        for (StandardOrderModificationDetailResponse.StatusItem statusItem : statusItemList) {
            HoHotelApplyStatusRecord applyStatusRecord = statusRecordMap.get(statusItem.getStatus());
            if (Objects.nonNull(applyStatusRecord)) {
                continue;
            }
            HoHotelApplyStatusRecord statusRecord = new HoHotelApplyStatusRecord();
            statusRecord.setApplyId(applyId);
            statusRecord.setStatus(statusItem.getStatus());
            statusRecord.setViewSort(statusItem.getStatusSortIndex());
            statusRecord.setHandleDate(StringUtils.isNotBlank(statusItem.getStatusTime())
                ? DateUtil.stringToDate(statusItem.getStatusTime(), DateUtil.DF_YMD_HMS) : null);
            statusRecord.setDatachangeCreatetime(new Date());
            statusRecord.setDatachangeLasttime(new Date());
            hoHotelApplyStatusRecordList.add(statusRecord);
        }
        if (CollectionUtils.isNotEmpty(hoHotelApplyStatusRecordList)) {
            LogSplicingUtils.addLogContext(logContext, "修改单状态记录入库参数: %s",
                JsonUtils.toJsonString(hoHotelApplyStatusRecordList));
            int result = hoHotelApplyStatusRecordLoader.insertList(hoHotelApplyStatusRecordList);
            LogSplicingUtils.addLogContext(logContext, "修改单状态记录入库结果: %s", result);
        }
    }

    /**
     * 获取酒店修改单详情
     *
     * @param hoOrder
     * @return
     */
    public HotelModifyDetailSupplierResponse getSupplierModifyDetail(HoOrder hoOrder) {
        HotelModifyDetailSupplierRequest detailSupplierRequest = new HotelModifyDetailSupplierRequest();
        detailSupplierRequest.setCorpId(hoOrder.getCorpId());
        detailSupplierRequest.setSupplierCorpId(hoOrder.getSupplierCorpId());
        detailSupplierRequest.setSupplierCode(hoOrder.getSupplierCode());
        detailSupplierRequest.setCorpPayType(hoOrder.getCorpPayType());
        detailSupplierRequest.setOrderId(hoOrder.getSupplierOrderId());
        return ctripSlSupplier.getSupplierModifyDetail(detailSupplierRequest);
    }

    /**
     * 组装失败返回
     * 
     * @param hotelResponseCodeEnum
     * @return
     */
    public HotelModifyPushResponse getFailedByEnum(HotelResponseCodeEnum hotelResponseCodeEnum) {
        return HotelModifyPushResponse.failed(hotelResponseCodeEnum.code(), hotelResponseCodeEnum.message());
    }


    public static Set<String> calculateDates(List<CheckInOutDateInfoBo> dateInfos) {
        Set<String> dateSet = new HashSet<>();
        for (CheckInOutDateInfoBo info : dateInfos) {
            Date start = info.getCheckInDate();
            Date end = info.getCheckOutDate();
            dateSet.addAll(getDatesBetween(start, end));
        }
        return dateSet;
    }

    private static List<String> getDatesBetween(Date start, Date end) {
        long between = cn.hutool.core.date.DateUtil.between(start, end, DateUnit.DAY);
        return Stream.iterate(start, date -> new Date(date.getTime() + TimeUnit.DAYS.toMillis(1)))
                .limit(between)
                .map(DATE_FORMAT::format)
                .collect(Collectors.toList());
    }
}
