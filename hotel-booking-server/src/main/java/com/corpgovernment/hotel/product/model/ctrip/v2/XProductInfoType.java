package com.corpgovernment.hotel.product.model.ctrip.v2;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 打包x产品信息
 * <AUTHOR>
 */
@Data
public class XProductInfoType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * x产品Id
     */
    private Integer xProductId;

    /**
     * x产品名称
     */
    private String xProductName;

    /**
     * x产品版本
     */
    private Integer xProductVersion;
    /**
     * x产品数量
     */
    private Integer quantity;

    /**
     * 类别id：常用：酒店套餐 102；餐饮 201；休闲娱乐 202
     */
    private List<Integer> categoryId;

    /**
     * 属性id：常用：单店单晚 10204 ; 单店多晚 10205; 酒店度假 10208 ; 早餐 20101 ;下午茶 20103 ; 正餐 20160; 餐饮抵扣券 20106; 其它餐饮 20108
     */
    private List<Integer> attributeId;

    /**
     * x产品单位 1:份，2:份/天
     */
    private String xProductUnit;

    /**
     * x产品介绍
     */
    private String xProductDesc;

    /**
     * 此x产品是否必选
     */
    private Boolean required;

    /**
     * 接待时间
     */
    private List<ReceptionTimeInfoType> receptionTimeInfo;

    /**
     * 预约信息
     */
    private ReservationInfoType reservationInfo;

    /**
     * 联系电话信息
     */
    private List<ContactNumberInfoType> contactNumberInfo;

    /**
     * 适用人数信息
     */
    private ApplicableNumberInfoType applicableNumberInfo;

    /**
     * 使用限制
     */
    private UseRuleInfoType useRuleInfo;

    /**
     * 证件信息
     */
    private CertificateInfoType certificateInfo;

    /**
     * 菜单信息
     */
    private MealInfoType mealInfo;




}
