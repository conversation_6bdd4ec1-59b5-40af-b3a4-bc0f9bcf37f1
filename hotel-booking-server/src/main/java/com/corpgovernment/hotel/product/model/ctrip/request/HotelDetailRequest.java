package com.corpgovernment.hotel.product.model.ctrip.request;

import lombok.Data;

/**
 * @ClassName: HotelDetailRequest
 * @description: 国内酒店详情请求
 * @author: zdwang
 * @date: Created in 13:25 2019/8/23
 * @Version: 1.0
 **/
@Data
public class HotelDetailRequest {
    /**
     * 酒店ID，必传
     */
    private String hotelID;
    /**
     * 入住时间（yyyy-MM-dd），必传
     */
    private String checkInDate;
    /**
     * 离店时间（yyyy-MM-dd），必传
     */
    private String checkOutDate;
    /**
     * 公司ID，必传
     */
    private String corpID;
    /**
     * 携程卡号，必传
     */
    private String uID;
    /**
     * 语言类型：ZH_CN（中文）,EN_US（英文）
     */
    private String language;
    /**
     * 城市ID，必传
     */
    private String cityID;
    /**
     * 每个酒店显示基础房型数量（默认-1，为0则只返回酒店信息，没有任何房型信息）
     */
    private Integer basicRoomTypeCountPerHotel;
    /**
     * 房型ID
     */
    private String roomId;
}