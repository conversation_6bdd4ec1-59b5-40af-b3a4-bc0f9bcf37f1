package com.corpgovernment.hotel.product.service;

import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.bookorder.response.LocalBookOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo.CheckBaseInfo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.hotel.booking.metric.MetricService;
import com.corpgovernment.hotel.product.dataloader.soa.CommonSupplierLoader;
import com.corpgovernment.hotel.product.external.dto.order.synccostcenter.CtripSyncOrderCostCenterRequest;
import com.corpgovernment.hotel.product.external.dto.order.synccostcenter.CtripSyncOrderCostCenterResponse;
import com.corpgovernment.hotel.product.supplier.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommonSupplierServiceNew {

	@Autowired
	private CommonSupplierLoader commonSupplierLoader;
	@Autowired
	private CommonService commonService;
	@Autowired
	private HotelOperatorTypeConfig hotelOperatorTypeConfig;
	@Autowired
	private MetricService metricService;

	/**
	 * 可定查询
	 *
	 * @param request 可订查询入参
	 * @return 可订查询返回
	 */
	public LocalCheckAvailResponseBo checkAvail(LocalCheckAvailRequestBo request) {
		if (request == null) {
			return null;
		}
		CheckBaseInfo baseInfo = request.getBaseInfo();
		SupplierProductBo supplierProduct = commonService.getSupplierProduct(request.getCorpId(), baseInfo.getSupplierCode(), hotelOperatorTypeConfig.getCheckAvail(), request.getCorpPayType());
		if (supplierProduct == null) {
			log.info("供应商不存在");
			return null;
		}
		return commonSupplierLoader.checkAvail(request, supplierProduct);
	}

	/**
	 * 创建订单
	 *
	 * @param request 创建订单入参
	 * @return 创建订单返回
	 */
	public LocalBookOrderResponseBo bookOrder(LocalBookOrderRequestBo request) {
		if (request == null) {
			return null;
		}
		SupplierProductBo supplierProduct = commonService.listSupplierProduct(request.getCorpId(), request.getSupplierCode(), hotelOperatorTypeConfig.getBookOrder(), request.getCorpPayType());
		if (supplierProduct == null) {
			log.info("供应商不存在");
			return null;
		}
        return metricService.execAndMetricSupplierSaveOrder(() -> commonSupplierLoader.bookOrder(request, supplierProduct),
            supplierProduct.getSupplierCode());
    }

	/**
	 * ctrip同步成本中心
	 *
	 * @param request
	 * @return
	 */
	public CtripSyncOrderCostCenterResponse syncOrderCostCenter(CtripSyncOrderCostCenterRequest request) {
		if (request == null) {
			return null;
		}
		SupplierProductBo supplierProduct = commonService.listSupplierProduct(request.getCorpID(),
				request.getSupplierCode(), hotelOperatorTypeConfig.getSyncOrderCostCenter(), request.getCorpPayType());
		if (supplierProduct == null) {
			if (log.isWarnEnabled()) {
				log.warn("服务商 [{}] 产品 [{}] 不存在", request.getSupplierCode(), hotelOperatorTypeConfig.getSyncOrderCostCenter());
			}
			return null;
		}
		return commonSupplierLoader.syncOrderCostCenter(supplierProduct, request);
	}
}