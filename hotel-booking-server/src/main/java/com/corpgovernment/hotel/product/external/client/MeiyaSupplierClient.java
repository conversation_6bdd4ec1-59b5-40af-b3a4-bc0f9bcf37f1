package com.corpgovernment.hotel.product.external.client;

import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.MonitorLevelEnums;
import com.corpgovernment.common.enums.MonitorTypeEnums;
import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import com.corpgovernment.hotel.product.external.dto.order.book.MeiyaReserveOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.MeiyaReserveOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderInquiryRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderInquiryResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.confirm.MeiyaConfirmOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.confirm.MeiyaConfirmOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.detail.MeiyaOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.detail.MeiyaOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.MeiyaOrderModificationDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.MeiyaOrderModificationDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.MeiyaOrderModificationInquiryRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.MeiyaOrderModificationInquiryResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.MeiyaOrderModificationRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.MeiyaOrderModificationResponse;
import com.corpgovernment.hotel.product.service.OrderMonitorService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.ws.rs.HEAD;
import java.util.Objects;

/**
 * @author: pwang27
 * @Date: 2024/4/9 21:29
 * @Description: 美亚接口实现
 */
@Component
public class MeiyaSupplierClient extends CommonSupplierClient {

    @Resource
    private OrderMonitorService orderMonitorService;

    public MeiyaReserveOrderResponse reserveOrder(MeiyaReserveOrderRequest request) {
        MeiyaReserveOrderResponse response = asyncHttpPostOriginResp(OrderMonitorService.BOOK_ORDER,
            request, MeiyaReserveOrderResponse.class, HotelResponseCodeEnum.SAVE_ORDER_SUPPLIER_ERROR);

        if (response == null || StringUtils.isBlank(response.getOrderID())) {
            if (response != null && StringUtils.isNotBlank(response.getMessage())) {
                // 美亚错误信息
                orderMonitorService.saveOrderMonitor(request.getBaseInfo().getPlatformOrderId(),
                    MonitorTypeEnums.IE, MonitorLevelEnums.IMP,
                    JsonUtils.toJsonString(response),
                    response.getErrorCode().toString(), response.getMessage(),
                    OrderMonitorService.CREATE_ORDER);
                throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_SUPPLIER_ERROR);
            }
        }
        return response;
    }

    /**
     * 订单确认接口
     * 
     * @param meiyaConfirmOrderRequest
     * @return
     */
    public MeiyaConfirmOrderResponse confirmOrder(MeiyaConfirmOrderRequest meiyaConfirmOrderRequest) {
        return asyncHttpPostOriginResp(OrderMonitorService.ORDER_CONFIRM, meiyaConfirmOrderRequest,
            MeiyaConfirmOrderResponse.class, HotelResponseCodeEnum.ORDER_CONFIRM_FAILED);
    }

    /**
     * 美亚订单详情
     *
     * @param request 订单详情
     * @return 美亚订单详情
     */
    public MeiyaOrderDetailResponse orderDetail(MeiyaOrderDetailRequest request) {
        return asyncHttpPostOriginResp(OrderMonitorService.ORDER_DETAILS,
            request, MeiyaOrderDetailResponse.class, HotelResponseCodeEnum.SUPPLIER_ORDER_DETAILS_ARE_EMPTY);
    }
    /**
     * 美亚订单修改问询
     *
     * @param request 美亚修改问询参数
     * @return 美亚修改问询返回参数
     */
    public MeiyaOrderModificationInquiryResponse queryOrderModification(MeiyaOrderModificationInquiryRequest request) {
        return asyncHttpPostOriginResp(OrderMonitorService.QUERY_ORDER_MODIFICATION,
                request, MeiyaOrderModificationInquiryResponse.class, HotelResponseCodeEnum.QUERY_ORDER_MODIFICATION_FAILED);
    }
    /**
     * 美亚订单修改申请
     *
     * @param request 订单修改申请
     * @return
     */
    public MeiyaOrderModificationResponse createOrderModification(MeiyaOrderModificationRequest request) {
        return asyncHttpPostOriginResp(OrderMonitorService.CREATE_ORDER_MODIFICATION,
                request, MeiyaOrderModificationResponse.class, HotelResponseCodeEnum.CREATE_ORDER_MODIFICATION_FAILED);
    }
    /**
     * 美亚订单修改详情
     *
     * @param request 订单修改详情请求
     * @return
     */
    public MeiyaOrderModificationDetailResponse orderModificationDetails(MeiyaOrderModificationDetailRequest request) {
        return asyncHttpPostOriginResp(OrderMonitorService.ORDER_MODIFICATION_DETAILS,
                request, MeiyaOrderModificationDetailResponse.class, HotelResponseCodeEnum.ORDER_MODIFICATION_DETAILS_FAILED);
    }

    public MeiyaCancelOrderInquiryResponse cancelOrderInquiry(MeiyaCancelOrderInquiryRequest request) {
        MeiyaCancelOrderInquiryResponse response = asyncHttpPostOriginResp(OrderMonitorService.CANCEL_ORDER_QUERY,
                request, MeiyaCancelOrderInquiryResponse.class, HotelResponseCodeEnum.CANCEL_QUERY_FAILED);
        if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.CANCEL_QUERY_FAILED);
        }
        if (BooleanUtils.isFalse(response.getStatus().getSuccess())) {
            throw new CorpBusinessException(response.getStatus().getErrorCode(), response.getStatus().getErrorMessage());
        }
        return response;
    }

    public MeiyaCancelOrderResponse cancelOrder(MeiyaCancelOrderRequest request) {
        MeiyaCancelOrderResponse meiyaCancelOrderResponse = asyncHttpPostOriginResp(OrderMonitorService.CANCEL_ORDER,
            request, MeiyaCancelOrderResponse.class, HotelResponseCodeEnum.HOTEL_CANCEL_FAILED);
        if (Objects.isNull(meiyaCancelOrderResponse)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.HOTEL_CANCEL_FAILED);
        }

        if (SupplierConstant.CancelStatus.CANCEL_STATUS_CANCELLING.toString().equals(meiyaCancelOrderResponse.getCancelStatus())) {
            return meiyaCancelOrderResponse;
        }
        // 取消失败抛出异常
        if (Objects.isNull(meiyaCancelOrderResponse.getIsSuccess()) || BooleanUtils.isFalse(meiyaCancelOrderResponse.getIsSuccess())) {
            if (StringUtils.isBlank(meiyaCancelOrderResponse.getErrorCode())
                || StringUtils.isBlank(meiyaCancelOrderResponse.getMessage())) {
                throw new CorpBusinessException(HotelResponseCodeEnum.HOTEL_CANCEL_FAILED);
            }
            throw new CorpBusinessException(Integer.parseInt(meiyaCancelOrderResponse.getErrorCode()),
                meiyaCancelOrderResponse.getMessage());
        }
        return meiyaCancelOrderResponse;
    }


    public MeiyaCancelOrderDetailResponse cancelOrderDetail(MeiyaCancelOrderDetailRequest request) {
        MeiyaCancelOrderDetailResponse meiyaCancelOrderDetailResponse = asyncHttpPostOriginResp(OrderMonitorService.CANCEL_ORDER_DETAIL,
                request, MeiyaCancelOrderDetailResponse.class, HotelResponseCodeEnum.CANCEL_DETAIL_FAILED);
        if (Objects.isNull(meiyaCancelOrderDetailResponse) || Objects.isNull(meiyaCancelOrderDetailResponse.getStatus())){
            throw new CorpBusinessException(HotelResponseCodeEnum.CANCEL_DETAIL_FAILED);
        }
        if (BooleanUtils.isFalse(meiyaCancelOrderDetailResponse.getStatus().getSuccess())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.CANCEL_DETAIL_FAILED);
        }
        return meiyaCancelOrderDetailResponse;
    }


}
