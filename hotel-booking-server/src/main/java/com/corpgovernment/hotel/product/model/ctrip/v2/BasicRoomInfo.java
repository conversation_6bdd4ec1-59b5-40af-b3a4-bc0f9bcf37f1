package com.corpgovernment.hotel.product.model.ctrip.v2;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 基础房型信息
 *
 * <AUTHOR>
 */
@Data
public class BasicRoomInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 基础房型名称
	 */
	private String baseRoomName;

	/**
	 * 楼层信息
	 */
	private String floor;

	/**
	 * 房型面积
	 */
	private String roomArea;

	/**
	 * 窗户描述
	 */
	private String hasWindowDesc;

	/**
	 * 基础房型ID
	 */
	private String basicRoomTypeID;

	private String masterBasicRoomID;

	/**
	 * 子房型列表
	 */
	private List<RoomInfoEntity> roomInfoList;

	private BasicRoomStaticInfoEntity basicRoomStaticInfo;
}
