package com.corpgovernment.hotel.product.model.ctrip.v2;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预定必读信息
 *
 * <AUTHOR>
 */
@Data
public class NoticeTipType implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 标题
	 */
	public String title;

	/**
	 * 必读信息类型 1：重要通知(信息级别为S、A） 5：入住人群限制 8：入住方式 14：酒店重要通知(信息级别为B、C）
	 */
	public Integer type;

	/**
	 * 预定必读信息详情列表
	 */
	public List<NoticeTipDetailType> noticeTipDetail;

}
