package com.corpgovernment.hotel.product.external.dto.order.modify;

import java.util.List;

import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;

import lombok.Data;

/**
 * 携程订单修改请求
 *
 * <AUTHOR> din<PERSON><PERSON><PERSON>
 * @date : 2024/4/11 14:20
 * @since : 1.0
 */
@Data
public class CtripOrderModificationRequest extends BaseExternalRequest {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * reasonCode 修改原因编码
     */
    private String reasonCode;
    /**
     * reasonCode 修改原因内容
     */
    private String reasonContent;
    private ModifyInfo modifyInfo;

    @Data
    public static class ModifyInfo {
        /**
         * 入住时间 format:yyyy-MM-dd HH:mm:ss
         */
        private String checkInTime;
        /**
         * 离店时间 format:yyyy-MM-dd HH:mm:ss
         */
        private String checkOutTime;
        /**
         * 保留的间夜信息列表
         */
        private List<RoomNightInfo> reservedRoomNightList;
    }

    @Data
    public static class RoomNightInfo {
        /**
         * 日期 format：yyyy-MM-dd
         */
        private String date;
        /**
         * 房间数
         */
        private Integer quantity;
        /**
         * 入住人信息列表
         */
        private List<ClientInfo> clientInfoList;
    }

    @Data
    public static class ClientInfo {
        /**
         * 入住人ID
         */
        private Long clientInfoId;
        /**
         * 房间索引
         */
        private Integer roomIndex;
        /**
         * 入住人姓名
         */
        private String name;
    }
}
