package com.corpgovernment.hotel.product.external.client;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Nullable;
import javax.annotation.Resource;

import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.google.common.base.Splitter;
import org.asynchttpclient.Request;
import org.springframework.stereotype.Component;

import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.constant.CommonConst;
import com.corpgovernment.common.supplier.ReplaceSupplierUidUtil;
import com.corpgovernment.common.supplier.SupplierSign;
import com.corpgovernment.common.utils.BaseUtils;
import com.corpgovernment.common.utils.HttpUtils;
import com.corpgovernment.hotel.booking.metric.MetricService;
import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;
import com.ctrip.corp.obt.async.http.AsyncHttpClient;
import com.ctrip.corp.obt.async.http.RequestBuilder;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.core.http.HttpResponse;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.framework.apollo.ConfigService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CommonSupplierClient {

    private static final Map<String, String> httpHeaderMap = new HashMap<>(2);
    private static final Integer SUCCESS_CODE = 0;
    private static final String ID = "id";
    private static final String TOKEN = "token";
    private static final String COMMA = ",";
    static {
        httpHeaderMap.put("Content-Type", "application/json");
    }

    @Resource
    protected MetricService metricService;

    /**
     * 标准版供应商接口调用
     *
     * @param operateName 操作名称
     *
     */
    @Nullable
    protected <T extends BaseExternalRequest, R> R standardContactHttpPost(String operateName, T request, Class<R> responseType, HotelResponseCodeEnum hotelResponseCodeEnum) {
        HttpResponse<R> httpResponse = null;
        long startTime = System.currentTimeMillis();
        Map<String, Object> requestMap = httpSign(request, request.getUserKey());
        try {
            httpResponse = AsyncHttpClient.post(request.getProductUrl(), requestMap, httpHeaderMap, responseType).block();
        } finally {
            long elapsed = System.currentTimeMillis() - startTime;
            HttpUtils.logHttp(request.getSupplierCode(), operateName, elapsed, request.getProductUrl(),
                    JsonUtils.toJsonString(requestMap), JsonUtils.toJsonString(httpResponse));
            metricService.metricSupplierApiResult(JsonUtils.toJsonString(httpResponse), request.getSupplierCode(), operateName, elapsed, hotelResponseCodeEnum);
        }

        if (Objects.isNull(httpResponse)) {
            throw new CorpBusinessException(hotelResponseCodeEnum);
        }

        if (SUCCESS_CODE != httpResponse.getResultCode()) {
            throw new CorpBusinessException(hotelResponseCodeEnum);
        }

        return httpResponse.getData();
    }

    /**
     * 非标准版供应商接口调用
     */
    @Nullable
    public <T extends BaseExternalRequest, R> R asyncHttpPostOriginResp(String operateName, T request,
        Class<R> responseType, HotelResponseCodeEnum hotelResponseCodeEnum) {
        String result = null;
        R response = null;
        long startTime = System.currentTimeMillis();
        Map<String, Object> requestMap;
        if (isMultiIdentitiesSwitchOn()) {
            requestMap = httpSign(request, request.getUserKey());
        } else {
            requestMap = httpSign(ReplaceSupplierUidUtil.replaceEmployeeId(request.getSupplierCode(),
                    JsonUtils.toJsonString(request)), request.getUserKey());
        }

        try {
            Request build = RequestBuilder.post(request.getProductUrl())
                .addBody(JsonUtils.toJsonString(requestMap)).addHeaders(httpHeaderMap).build();
            result = AsyncHttpClient.executeRequestReturnOriginResp(build).block();
            if (StringUtils.isBlank(result)) {
                return null;
            }
            response = JsonUtils.parse(result, responseType);
            return response;
        } finally {
            long elapsed = System.currentTimeMillis() - startTime;
            HttpUtils.logHttp(request.getSupplierCode(), operateName, elapsed,
                request.getProductUrl(), JsonUtils.toJsonString(requestMap), result);
            metricService.metricSupplierApiResult(JsonUtils.toJsonString(response), request.getSupplierCode(),
                operateName, elapsed,
                hotelResponseCodeEnum);
        }
    }


    /**
     * 接口验签
     */
    private <T> Map<String, Object> httpSign(T request, String userKey) {
        Map<String, Object> requestMap;
        if (request instanceof String) {
            requestMap = JsonUtils.parseMap((String) request);
        } else {
            requestMap = JsonUtils.parseMap(JsonUtils.toJsonString(request));
        }
        String guid = BaseUtils.getUUID();
        String time = BaseUtils.getTime().toString();
        requestMap.put(ID, String.format("%s%s", guid, time));
        requestMap.put(TOKEN, SupplierSign.createSign(userKey, guid, time));
        return requestMap;
    }

    protected boolean isMultiIdentitiesSwitchOn() {
        String tenantId = TenantContext.getTenantId();
        String openCardSwitchTenants = ConfigService.getConfig(CommonConst.NAMESPACE_COMMON)
                .getProperty("open-card.multi-identities.switch.on.tenants", "");
        if (StringUtils.isBlank(openCardSwitchTenants)) {
            return false;
        }

        List<String> tenantIdList = Splitter.on(COMMA).trimResults().splitToList(openCardSwitchTenants);
        return CollectionUtils.isNotEmpty(tenantIdList) && tenantIdList.contains(tenantId);
    }
}
