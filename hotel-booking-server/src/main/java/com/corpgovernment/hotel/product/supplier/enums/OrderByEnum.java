package com.corpgovernment.hotel.product.supplier.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: SupplierEnum
 * @description: 供应商枚举类
 * @author: zdwang
 * @date: Created in 13:37 2019/7/9
 * @Version: 1.0
 **/
public enum OrderByEnum {
    /**
     *推荐级别排序
     */
    RECOMMEND(0, "RECOMMEND"),
    MIN_PRICE(1, "价格由低到高"),
    MAX_PRICE(2, "价格由高到低"),
    DISTANCE(3, "距离由近到远"),
    H_RATING_OVERALL(4, "评价从高到低");


    private final Integer code;
    private final String desc;

    private static final Map<Integer, OrderByEnum> lookup = new HashMap<>();

    static {
        for (OrderByEnum s : EnumSet.allOf(OrderByEnum.class)) {
            lookup.put(s.getCode(), s);
        }
    }

    OrderByEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public String getDesc() {
        return this.desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public static OrderByEnum get(Integer code) {
        return lookup.get(code);
    }

    public static boolean exists(Integer code) {
        return lookup.containsKey(code);
    }
}