package com.corpgovernment.hotel.product.entity.db;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import org.springframework.beans.BeanUtils;

import com.corpgovernment.api.hotel.product.model.response.HoPassengerBo;
import com.corpgovernment.common.handler.Sm4TypeHandler;

import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 *
 **/
@Data
@Table(name = "ho_passenger")
public class HoPassenger implements Serializable {

    /**
     * 订单号
     **/
    private Long orderId;

    /**
     * 姓名
     **/
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String passengerName;

    /**
     * 员工ID
     **/
    private String uid;

    /**
     * 非员工ID
     **/
    private Long noEmployeeId;

    /**
     * 生日
     **/
    private java.util.Date birthday;

    /**
     * 性别
     **/
    private String gender;

    /**
     * 手机
     **/
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String mobilePhone;

    /**
     * 是否需要发短信
     **/
    private Boolean isSendSms;

    /**
     * 国家码
     **/
    private String countryCode;
    /**
     * 成本中心code
     **/
    private String costCenterCode;
    /**
     * 成本中心名称
     **/
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String costCenterName;
    /**
     * 部门id
     **/
    private String departmentId;
    /**
     * 部门名称
     **/
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String departmentName;
    /**
     * 组织id
     */
    private String orgId;
    /**
     * 组织名称
     */
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String orgName;
    /**
     * vip等级
     */
    private Integer vipLevel;
    /**
     * 创建时间
     **/
    private java.util.Date datachangeCreatetime;

    /**
     * 最后修改时间
     **/
    private java.util.Date datachangeLasttime;

    private String projectId;
    private String projectCode;
    private String projectName;
    private String costCenterId;
    private String noSelectProjectDesc;
    private String wbsRemark;
    private String costCenterRemark;

    /**
     * 员工类型
     */
    private Integer employeeType;

    /**
     * 入住人名称（新）
     */
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String travelerName;
    /**
     * 房间索引
     */
    private Integer roomIndex;

    /**
     * 多成本中心大对象的dataId，对应ho_data_storage中的data_id
     */
    @Column(name = "multi_cost_center_data_id")
    private String multiCostCenterDataId;

    /**
     * 证件类型
     */
    @Column(name = "card_type")
    private Integer cardType;

    /**
     * 证件号
     */
    @Column(name = "card_no")
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String cardNo;


    /**
     * 邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     * 核算单元大对象的dataId，对应ho_data_storage中的data_id
     */
    @Column(name = "accounting_unit_data_id")
    private String accountingUnitDataId;

    /**
     * 成本中心关联法人机构Code
     */
    @Column(name = "cost_center_legal_entity_code")
    private String costCenterLegalEntityCode;

    /**
     * 成本中心关联法人机构名字
     */
    @Column(name = "cost_center_legal_entity_name")
    private String costCenterLegalEntityName;

    /**
     * 项目关联法人机构Code
     */
    @Column(name = "project_legal_entity_code")
    private String projectLegalEntityCode;

    /**
     * 项目关联法人机构名字
     */
    @Column(name = "project_legal_entity_name")
    private String projectLegalEntityName;

    /**
     * 成本中心关联法人机构主账户
     */
    @Column(name = "supplier_account_id")
    private String supplierAccountId;

    /**
     * 携程uid
     */
    @Column(name = "ctrip_uid")
    private String ctripUid;

    public HoPassengerBo convertHoPassengerBo() {
        HoPassengerBo hoPassengerBo = new HoPassengerBo();
        BeanUtils.copyProperties(this, hoPassengerBo);
        return hoPassengerBo;
    }
}
