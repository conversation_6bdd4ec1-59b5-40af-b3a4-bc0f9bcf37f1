package com.corpgovernment.hotel.product.model.ctrip.v2;

import lombok.Data;

import java.io.Serializable;

/**
 * 详情基础信息实体
 *
 * <AUTHOR>
 */
@Data
public class DetailBaseInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 母酒店ID
	 */
	private String hotelId;

	/**
	 * 酒店名称
	 */
	private String hotelName;

	/**
	 * 酒店位置信息
	 */
	private PositionEntity hotelPositionInfo;

	/**
	 * 酒店图片信息
	 */
	private PictureInfoEntity hotelPictureInfo;

	/**
	 * 酒店星钻信息
	 */
	private HotelStarEntity hotelStarInfo;

	/**
	 * 酒店品牌信息
	 */
	private HotelBrandEntity hotelBrandInfo;

	/**
	 * 酒店联系方式
	 */
	private HotelContactInfoType hotelContactInfo;

}
