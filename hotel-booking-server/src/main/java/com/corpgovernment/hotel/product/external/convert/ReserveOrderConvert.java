package com.corpgovernment.hotel.product.external.convert;

import com.corpgovernment.api.hotel.product.enums.MixPayWayEnum;
import com.corpgovernment.api.hotel.product.enums.PrePayTypeWayEnum;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.costcenter.CostCenterHandler;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.core.domain.common.model.enums.SupportCertificateTypeEnum;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.booking.enums.LanguageEnum;
import com.corpgovernment.hotel.product.external.dto.order.book.CtripReserveOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.CtripReserveOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.book.MeiyaReserveOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.MeiyaReserveOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.book.StandardReserveOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.StandardReserveOrderResponse;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.EnvironmentHolder;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.mapstruct.Mapper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.corpgovernment.hotel.product.external.constant.SupplierConstant.ReserveOrder.*;

@Mapper(componentModel = "spring")
public interface ReserveOrderConvert {

    /**
     * 转换为携程供应商下单请求类
     *
     * @param request 标准契约下单请求类
     * @return 携程供应商下单请求类
     */
    default CtripReserveOrderRequest toCtripReserveOrderRequest(StandardReserveOrderRequest request) {
        Map<String, Object> additionalInformationMap = request.getAdditionalInformationMap();

        CtripReserveOrderRequest ctripReserveOrderRequest = new CtripReserveOrderRequest();
        ctripReserveOrderRequest.setSupplierCode(request.getSupplierCode());
        ctripReserveOrderRequest.setCorpPayType(request.getCorpPayType());
        ctripReserveOrderRequest.setCompanyCode(request.getCompanyCode());
        ctripReserveOrderRequest.setSupplierUid(request.getSupplierUid());
        ctripReserveOrderRequest.setProductUrl(request.getProductUrl());
        ctripReserveOrderRequest.setUserKey(request.getUserKey());

        //基础信息
        CtripReserveOrderRequest.BaseInfo baseInfo = new CtripReserveOrderRequest.BaseInfo();
        baseInfo.setUid(request.getSupplierUid());
        baseInfo.setCorpId(request.getOrderBasicInfo().getCorpID());
        baseInfo.setLanguage(LanguageEnum.ZH_CN.getCode());
        baseInfo.setPlatformOrderId(request.getOrderBasicInfo().getPlatformOrderID());
        ctripReserveOrderRequest.setBaseInfo(baseInfo);
        //产品编码信息
        ctripReserveOrderRequest.setProductId(request.getProductID());
        //房间信息
        if (Objects.nonNull(request.getRoomInfo())) {
            CtripReserveOrderRequest.RoomInfo roomInfo = new CtripReserveOrderRequest.RoomInfo();
            roomInfo.setRoomQuantity(request.getRoomInfo().getRoomQuantity());
            roomInfo.setGuestQuantity(request.getRoomInfo().getGuestQuantity());
            roomInfo.setCheckInDate(request.getRoomInfo().getCheckInDate());
            roomInfo.setCheckOutDate(request.getRoomInfo().getCheckOutDate());
            ctripReserveOrderRequest.setRoomInfo(roomInfo);
        }
        //入住人信息
        List<StandardReserveOrderRequest.GuestInfo> guestInfoList = request.getGuestInfoList();
        List<CtripReserveOrderRequest.Client> clientList = guestInfoList.stream().map(item -> {
            CtripReserveOrderRequest.Client client = new CtripReserveOrderRequest.Client();
            client.setUid(item.getUID());
            client.setName(item.getName());
            Boolean isSendSms = (Boolean) additionalInformationMap.getOrDefault(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_IS_SEND_SMS, item.getUID()), false);
            if (isSendSms) {
                client.setMobilePhone(item.getMobilePhone());
                client.setCountryCode(item.getCountryCode());
            }
            Boolean earnPoints = (Boolean) additionalInformationMap.getOrDefault(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_EARN_POINTS, item.getUID()), false);
            client.setEarnPoints(earnPoints);
            if (earnPoints) {
                client.setMobilePhone(item.getMobilePhone());
                client.setCountryCode(item.getCountryCode());
            }
            client.setRoomIndex(item.getRoomIndex());

            // 设置证件信息
            Optional.ofNullable(item.getCardType()).ifPresent(cardType -> {
                String cardTypeStr = String.valueOf(cardType);
                SupportCertificateTypeEnum supportCertificateTypeEnum = SupportCertificateTypeEnum.getEnum(cardTypeStr);
                if (Objects.nonNull(supportCertificateTypeEnum)) {
                    client.setCertificateType(supportCertificateTypeEnum.getCode());
                    client.setCertificateNumber(item.getCardNo());
                }
            });

            return client;
        }).collect(Collectors.toList());
        ctripReserveOrderRequest.setClientList(clientList);
        //联系人信息
        if (Objects.nonNull(request.getContactorInfo())) {
            CtripReserveOrderRequest.ContactorInfo contactorInfo = new CtripReserveOrderRequest.ContactorInfo();
            contactorInfo.setName(request.getContactorInfo().getName());
            contactorInfo.setEmail(request.getContactorInfo().getEmail());
            contactorInfo.setMobilePhone(request.getContactorInfo().getMobilePhone());
            contactorInfo.setMobilePhoneCountryCode(request.getContactorInfo().getCountryCode());
            ctripReserveOrderRequest.setContactorInfo(contactorInfo);
        }

        //备注信息
        Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_REMARK_INFO)).ifPresent(additionalInformationRemarkInfo -> {
            CtripReserveOrderRequest.RemarkInfo ctripRequestRemarkInfo = new CtripReserveOrderRequest.RemarkInfo();
            OrderInfoModel.RemarkInfo orderInfoRemarkInfo = (OrderInfoModel.RemarkInfo) additionalInformationRemarkInfo;
            ctripRequestRemarkInfo.setCustomRemark(orderInfoRemarkInfo.getCustomRemark());
            List<OrderInfoModel.OptionalRemark> orderInfoOptionalRemarkList = orderInfoRemarkInfo.getOptionalRemarkList();
            if (CollectionUtils.isNotEmpty(orderInfoOptionalRemarkList)) {
                ctripRequestRemarkInfo.setOptionalRemarkList(orderInfoOptionalRemarkList.stream().map(orderInfoOptionRemark -> {
                    CtripReserveOrderRequest.OptionalRemark ctripOptionalRemark = new CtripReserveOrderRequest.OptionalRemark();
                    ctripOptionalRemark.setId(orderInfoOptionRemark.getId());
                    ctripOptionalRemark.setTitle(orderInfoOptionRemark.getTitle());
                    ctripOptionalRemark.setKey(orderInfoOptionRemark.getKey());
                    ctripOptionalRemark.setValue(orderInfoOptionRemark.getValue());
                    return ctripOptionalRemark;
                }).collect(Collectors.toList()));
            }
            ctripReserveOrderRequest.setRemarkInfo(ctripRequestRemarkInfo);
        });

        //支付信息
        CtripReserveOrderRequest.PaymentInfo paymentInfo = new CtripReserveOrderRequest.PaymentInfo();

        paymentInfo.setPrepayType(PayTypeEnum.CASH.getType().equals(request.getPayInfo().getPayType())
                ? PayTypeEnum.CashNull.getType() : PrePayTypeWayEnum.getByPayTyp(request.getPayInfo().getPayType()));
        StandardReserveOrderRequest.PayInfo payInfo = request.getPayInfo();
        List<StandardReserveOrderRequest.MixPayWayInfo> mixPayWayInfoList = payInfo.getMixPayWayInfoList();
        if (CollectionUtils.isNotEmpty(mixPayWayInfoList)) {
            paymentInfo.setMixPayWayInfo(mixPayWayInfoList.stream().map(item -> {
                CtripReserveOrderRequest.MixPaymentWay mixPaymentWay = new CtripReserveOrderRequest.MixPaymentWay();
                mixPaymentWay.setMixPayWay(MixPayWayEnum.ACCNT.name().equals(item.getMixPayWay()) ? MixPayWayEnum.ACCNT : MixPayWayEnum.GUEST);
                mixPaymentWay.setPayAmount(item.getPayAmount());
                return mixPaymentWay;
            }).collect(Collectors.toList()));
        }

        CtripReserveOrderRequest.CorpPayInfo corpPayInfo = new CtripReserveOrderRequest.CorpPayInfo();
        CtripReserveOrderRequest.ServiceFeeInfo serviceFeeInfo = new CtripReserveOrderRequest.ServiceFeeInfo();
        serviceFeeInfo.setServiceFee(request.getPriceInfo().getServiceFee());
        corpPayInfo.setServiceFeeInfo(serviceFeeInfo);
        paymentInfo.setCorpPayInfo(corpPayInfo);
        ctripReserveOrderRequest.setPaymentInfo(paymentInfo);

        if (Objects.nonNull(request.getInvoiceInfo())) {
            CtripReserveOrderRequest.InvoiceInfo invoiceInfo = new CtripReserveOrderRequest.InvoiceInfo();
            invoiceInfo.setInvoiceTitleType(request.getInvoiceInfo().getInvoiceTitleType());
            invoiceInfo.setInvoiceTitle(request.getInvoiceInfo().getInvoiceTitle());
            invoiceInfo.setTaxpayerNumber(request.getInvoiceInfo().getTaxpayerNumber());
            invoiceInfo.setEmail(request.getInvoiceInfo().getEmail());
            invoiceInfo.setOrderInvoiceTargetType(request.getInvoiceInfo().getOrderInvoiceTargetType());
            invoiceInfo.setCompanyName(request.getInvoiceInfo().getCompanyName());
            invoiceInfo.setCompanyAddress(request.getInvoiceInfo().getCompanyAddress());
            invoiceInfo.setCompanyPhone(request.getInvoiceInfo().getCompanyPhone());
            invoiceInfo.setCompanyBankName(request.getInvoiceInfo().getCompanyBankName());
            invoiceInfo.setCompanyBankAccount(request.getInvoiceInfo().getCompanyBankAccount());
            ctripReserveOrderRequest.setInvoiceInfo(invoiceInfo);
        }


        CtripReserveOrderRequest.CreateOrderExtInfo extInfo = new CtripReserveOrderRequest.CreateOrderExtInfo();
        extInfo.setExternalOrderId(request.getOrderBasicInfo().getPlatformOrderID());
        Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_MEMBER_CARD_NO)).ifPresent(item -> extInfo.setMembershipCardNum(String.valueOf(item)));
        ctripReserveOrderRequest.setExtInfo(extInfo);

        CtripReserveOrderRequest.PriceInfo priceInfo = new CtripReserveOrderRequest.PriceInfo();
        BigDecimal serviceFee = request.getPriceInfo().getServiceFee();
        if (Objects.isNull(serviceFee)) {
            serviceFee = BigDecimal.ZERO;
        }
        priceInfo.setSellPrice(request.getPriceInfo().getTotalAmount().subtract(serviceFee));
        ctripReserveOrderRequest.setPriceInfo(priceInfo);

        CtripReserveOrderRequest.CorpOrderInfo corpOrderInfo = new CtripReserveOrderRequest.CorpOrderInfo();
        Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_SEND_MSG)).ifPresent(item -> corpOrderInfo.setSendMsg((Boolean) (item)));
        ctripReserveOrderRequest.setCorpOrderInfo(corpOrderInfo);
        return ctripReserveOrderRequest;
    }

    /**
     * 转换为标准供应商下单响应类
     *
     * @param response 携程供应商下单响应类
     * @return 标准契约下单响应类
     */
    default StandardReserveOrderResponse toReserveOrderResponse(CtripReserveOrderResponse response) {
        StandardReserveOrderResponse standardReserveOrderResponse = new StandardReserveOrderResponse();
        standardReserveOrderResponse.setOrderID(response.getOrderID());
        standardReserveOrderResponse.setPaymentNo(response.getPaymentTransactionId());
        return standardReserveOrderResponse;
    }

    /**
     * 转换为美亚供应商下单请求类
     *
     * @param request 标准契约下单请求类
     * @return 美亚供应商下单请求类
     */
    default MeiyaReserveOrderRequest toMeiyaReserveOrderRequest(StandardReserveOrderRequest request) {
        Map<String, Object> additionalInformationMap = request.getAdditionalInformationMap();

        MeiyaReserveOrderRequest meiyaReserveOrderRequest = new MeiyaReserveOrderRequest();
        meiyaReserveOrderRequest.setSupplierCode(request.getSupplierCode());
        meiyaReserveOrderRequest.setCorpPayType(request.getCorpPayType());
        meiyaReserveOrderRequest.setCompanyCode(request.getCompanyCode());
        meiyaReserveOrderRequest.setSupplierUid(request.getSupplierUid());
        meiyaReserveOrderRequest.setProductUrl(request.getProductUrl());
        meiyaReserveOrderRequest.setUserKey(request.getUserKey());

        //基础信息
        MeiyaReserveOrderRequest.BaseInfo baseInfo = new MeiyaReserveOrderRequest.BaseInfo();
        baseInfo.setUid(request.getSupplierUid());
        baseInfo.setCorpId(request.getOrderBasicInfo().getCorpID());
        baseInfo.setLanguage(LanguageEnum.ZH_CN.getCode());
        baseInfo.setPlatformOrderId(request.getOrderBasicInfo().getPlatformOrderID());
        meiyaReserveOrderRequest.setBaseInfo(baseInfo);
        //产品编码信息
        meiyaReserveOrderRequest.setProductId(request.getProductID());
        //房间信息
        if (Objects.nonNull(request.getRoomInfo())) {
            MeiyaReserveOrderRequest.RoomInfo roomInfo = new MeiyaReserveOrderRequest.RoomInfo();
            Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_ROOM_ID)).ifPresent(item -> roomInfo.setRoomId(String.valueOf(item)));
            Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_HOTEL_ID)).ifPresent(item -> roomInfo.setHotelId(String.valueOf(item)));
            Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_CITY_ID)).ifPresent(item -> roomInfo.setCityId(String.valueOf(item)));
            roomInfo.setRoomQuantity(request.getRoomInfo().getRoomQuantity());
            roomInfo.setGuestQuantity(request.getRoomInfo().getGuestQuantity());
            roomInfo.setCheckInDate(request.getRoomInfo().getCheckInDate());
            roomInfo.setCheckOutDate(request.getRoomInfo().getCheckOutDate());
            meiyaReserveOrderRequest.setRoomInfo(roomInfo);
        }


        List<StandardReserveOrderRequest.GuestInfo> guestInfoList = request.getGuestInfoList();
        List<MeiyaReserveOrderRequest.Client> clientList = guestInfoList.stream().map(item -> {
            MeiyaReserveOrderRequest.Client client = new MeiyaReserveOrderRequest.Client();
            client.setUid(item.getUID());
            client.setName(item.getName());
            client.setMobilePhone(item.getMobilePhone());
            client.setCountryCode(item.getCountryCode());
            client.setRoomIndex(item.getRoomIndex());
            Optional.ofNullable(additionalInformationMap.get(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_COMPANY_ID, item.getUID()))).ifPresent(companyId -> client.setCorpId(String.valueOf(companyId)));
            Optional.ofNullable(additionalInformationMap.get(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_COMPANY_NAME, item.getUID()))).ifPresent(companyName -> client.setCorpName(String.valueOf(companyName)));
            Optional.ofNullable(additionalInformationMap.get(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_ORG_ID, item.getUID()))).ifPresent(orgId -> client.setDeptId(String.valueOf(orgId)));
            Optional.ofNullable(additionalInformationMap.get(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_ORG_NAME, item.getUID()))).ifPresent(orgName -> client.setDeptName(String.valueOf(orgName)));
            return client;
        }).collect(Collectors.toList());
        meiyaReserveOrderRequest.setClientList(clientList);

        if (Objects.nonNull(request.getContactorInfo())){
            MeiyaReserveOrderRequest.ContactorInfo contactorInfo = new MeiyaReserveOrderRequest.ContactorInfo();
            contactorInfo.setName(request.getContactorInfo().getName());
            contactorInfo.setEmail(request.getContactorInfo().getEmail());
            contactorInfo.setMobilePhone(request.getContactorInfo().getMobilePhone());
            contactorInfo.setMobilePhoneCountryCode(request.getContactorInfo().getCountryCode());
            meiyaReserveOrderRequest.setContactorInfo(contactorInfo);
        }


        Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_REMARK_INFO)).ifPresent(additionalInformationRemarkInfo -> {
            MeiyaReserveOrderRequest.RemarkInfo meiyaRequestRemarkInfo = new MeiyaReserveOrderRequest.RemarkInfo();
            OrderInfoModel.RemarkInfo orderInfoRemarkInfo = (OrderInfoModel.RemarkInfo) additionalInformationRemarkInfo;
            meiyaRequestRemarkInfo.setCustomRemark(orderInfoRemarkInfo.getCustomRemark());
            List<OrderInfoModel.OptionalRemark> orderInfoOptionalRemarkList = orderInfoRemarkInfo.getOptionalRemarkList();
            meiyaRequestRemarkInfo.setOptionalRemarkList(orderInfoOptionalRemarkList.stream().map(item -> {
                MeiyaReserveOrderRequest.OptionalRemark meiyaOptionalRemark = new MeiyaReserveOrderRequest.OptionalRemark();
                meiyaOptionalRemark.setId(item.getId());
                meiyaOptionalRemark.setTitle(item.getTitle());
                meiyaOptionalRemark.setKey(item.getKey());
                meiyaOptionalRemark.setValue(item.getValue());
                return meiyaOptionalRemark;
            }).collect(Collectors.toList()));
            meiyaReserveOrderRequest.setRemarkInfo(meiyaRequestRemarkInfo);
        });

        MeiyaReserveOrderRequest.PaymentInfo meiyaRequestPaymentInfo = new MeiyaReserveOrderRequest.PaymentInfo();

        meiyaRequestPaymentInfo.setPrepayType(PayTypeEnum.CASH.getType().equals(request.getPayInfo().getPayType())
                ? PayTypeEnum.PPAY.getType() : PrePayTypeWayEnum.getByPayTyp(request.getPayInfo().getPayType()));
        List<StandardReserveOrderRequest.MixPayWayInfo> mixPayWayInfoList = request.getPayInfo().getMixPayWayInfoList();
        //美亚该字段传值不能为null
        meiyaRequestPaymentInfo.setMixPayWayInfo(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(mixPayWayInfoList)) {
            meiyaRequestPaymentInfo.setMixPayWayInfo(mixPayWayInfoList.stream().map(item -> {
                MeiyaReserveOrderRequest.MixPaymentWay mixPaymentWay = new MeiyaReserveOrderRequest.MixPaymentWay();
                mixPaymentWay.setMixPayWay(MixPayWayEnum.ACCNT.name().equals(item.getMixPayWay()) ? MixPayWayEnum.ACCNT : MixPayWayEnum.GUEST);
                mixPaymentWay.setPayAmount(item.getPayAmount());
                return mixPaymentWay;
            }).collect(Collectors.toList()));
        }
        meiyaReserveOrderRequest.setPaymentInfo(meiyaRequestPaymentInfo);

        if (Objects.nonNull(request.getInvoiceInfo())){
            MeiyaReserveOrderRequest.InvoiceInfo invoiceInfo = new MeiyaReserveOrderRequest.InvoiceInfo();
            invoiceInfo.setInvoiceTitleType(request.getInvoiceInfo().getInvoiceTitleType());
            invoiceInfo.setInvoiceTitle(request.getInvoiceInfo().getInvoiceTitle());
            invoiceInfo.setTaxpayerNumber(request.getInvoiceInfo().getTaxpayerNumber());
            invoiceInfo.setEmail(request.getInvoiceInfo().getEmail());
            invoiceInfo.setOrderInvoiceTargetType(request.getInvoiceInfo().getOrderInvoiceTargetType());
            invoiceInfo.setCompanyName(request.getInvoiceInfo().getCompanyName());
            invoiceInfo.setCompanyAddress(request.getInvoiceInfo().getCompanyAddress());
            invoiceInfo.setCompanyPhone(request.getInvoiceInfo().getCompanyPhone());
            invoiceInfo.setCompanyBankName(request.getInvoiceInfo().getCompanyBankName());
            invoiceInfo.setCompanyBankAccount(request.getInvoiceInfo().getCompanyBankAccount());
            meiyaReserveOrderRequest.setInvoiceInfo(invoiceInfo);
        }

        if (Objects.nonNull(request.getInvoiceDeliveryInfo())) {
            MeiyaReserveOrderRequest.InvoiceDeliveryInfo invoiceDeliveryInfo =
                    new MeiyaReserveOrderRequest.InvoiceDeliveryInfo();
            invoiceDeliveryInfo.setContactName(request.getInvoiceDeliveryInfo().getContactName());
            invoiceDeliveryInfo.setPostPhone(request.getInvoiceDeliveryInfo().getPostPhone());
            invoiceDeliveryInfo.setProvince(request.getInvoiceDeliveryInfo().getProvince());
            invoiceDeliveryInfo.setCity(request.getInvoiceDeliveryInfo().getCity());
            invoiceDeliveryInfo.setCanton(request.getInvoiceDeliveryInfo().getCanton());
            invoiceDeliveryInfo.setAddress(request.getInvoiceDeliveryInfo().getAddress());
            meiyaReserveOrderRequest.setInvoiceDeliveryInfo(invoiceDeliveryInfo);
        }

        MeiyaReserveOrderRequest.CreateOrderExtInfo extInfo = new MeiyaReserveOrderRequest.CreateOrderExtInfo();
        extInfo.setExternalOrderId(request.getOrderBasicInfo().getPlatformOrderID());
        Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_MEMBER_CARD_NO)).ifPresent(item -> extInfo.setMembershipCardNum(String.valueOf(item)));
        meiyaReserveOrderRequest.setExtInfo(extInfo);

        MeiyaReserveOrderRequest.PriceInfo priceInfo = new MeiyaReserveOrderRequest.PriceInfo();
        priceInfo.setAmount(request.getPriceInfo().getTotalAmount());
        priceInfo.setServiceFee(request.getPriceInfo().getServiceFee());
        meiyaReserveOrderRequest.setPriceInfo(priceInfo);

        MeiyaReserveOrderRequest.CorpOrderInfo corpOrderInfo = new MeiyaReserveOrderRequest.CorpOrderInfo();
        Optional.ofNullable(additionalInformationMap.get(REQUEST_ADDITIONAL_MAP_KEY_SEND_MSG)).ifPresent(item -> corpOrderInfo.setSendMsg((Boolean) (item)));
        meiyaReserveOrderRequest.setCorpOrderInfo(corpOrderInfo);
        return meiyaReserveOrderRequest;
    }

    /**
     * 转换为标准供应商下单响应类
     *
     * @param response 美亚供应商下单响应类
     * @return 标准契约下单响应类
     */
    default StandardReserveOrderResponse toReserveOrderResponse(MeiyaReserveOrderResponse response) {
        StandardReserveOrderResponse standardReserveOrderResponse = new StandardReserveOrderResponse();
        standardReserveOrderResponse.setOrderID(response.getOrderID());
        standardReserveOrderResponse.setPaymentNo(response.getPaymentTransactionId());
        return standardReserveOrderResponse;
    }
}
