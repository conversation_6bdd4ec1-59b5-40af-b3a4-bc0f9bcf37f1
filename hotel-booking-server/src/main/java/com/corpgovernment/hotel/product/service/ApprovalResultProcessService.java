package com.corpgovernment.hotel.product.service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.approvalsystem.enums.AccreditResultTypeEnum;
import com.corpgovernment.api.approvalsystem.mq.CallbackPojo;
import com.corpgovernment.api.hotel.product.bo.HotelConfirmOrderRequestBO;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.ordercenter.dto.OcUtils;
import com.corpgovernment.api.platform.enums.PaymentStatusType;
import com.corpgovernment.api.platform.soa.paymentbill.PpPaymentBillDto;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.consolidation.sdk.enums.ApplicationEnum;
import com.corpgovernment.consolidation.sdk.enums.ApprovalStatusEnum;
import com.corpgovernment.consolidation.sdk.event.ApprovalStatusChangeExternalEvent;
import com.corpgovernment.hotel.product.dataloader.db.HoHotelLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoRoomLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BasicDataClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayBillClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoHotel;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.entity.db.HoRoom;
import com.corpgovernment.hotel.product.mq.OrderStatusProducer;
import com.ctrip.corp.obt.generic.event.EventCenter;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ApprovalResultProcessService {

    private static final String APPROVAL_SUCCESS_PROCESS_REDIS_KEY_PREFIX = "APPROVAL:SUCCESS:PROCESS";

    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private PayBillClientLoader payBillClientLoader;
    @Autowired
    private BasicDataClientLoader basicDataClientLoader;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HoHotelLoader hoHotelLoader;
    @Autowired
    private HoRoomLoader hoRoomLoader;
    @Autowired
    private HotelPushService hotelPushService;
    @Autowired
    private CancelOrderProductService cancelOrderProductService;
    @Autowired
    private OrderStatusProducer orderStatusProducer;
    @Autowired
    private ConfirmOrderService confirmOrderService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private EventCenter eventCenter;

    public void processApprovalResult(CallbackPojo callback) {

        String lockKey = getLockKey(callback.getBusinessId());
        RLock lock = redissonClient.getLock(lockKey);

        try {
            boolean locked = lock.tryLock(0, 90, TimeUnit.SECONDS);
            log.info("获取审批成功处理锁【key】:{},【获取结果】:{}", lockKey, locked);
            if (!locked) {
                log.error("获取审批成功处理锁失败");
                throw new CorpBusinessException(HotelResponseCodeEnum.REDIS_LOCK_FAILED);
            }

            String approveResult = callback.getApproveResult();
            String businessId = callback.getBusinessId();
            HoOrder order = hoOrderLoader.selectByOrderId(Long.valueOf(businessId));
            //订单不存在或者订单状态不为AW->直接返回getCarReconciliationInfo
            if (order == null || !Objects.equals(order.getOrderStatus(), "AW")) {
                return;
            }
            if (order.getNewSystem()) {
                log.info("新系统订单不处理");
                return;
            }

            // 审批通过->修改订单状态->创建支付单
            if (Objects.equals(AccreditResultTypeEnum.APPROVAL_PASS.getCode(), approveResult)) {

                String targetStatus;
                if (PayTypeEnum.MIXPAY.getType().equals(order.getPaytype()) || PayTypeEnum.PPAY.getType().equals(order.getPaytype())) {
                    targetStatus = OrderStatusEnum.TW.getCode();
                } else {
                    targetStatus = OrderStatusEnum.PW.getCode();
                }

                hoOrderLoader.updateOrderStatus(order.getOrderId(), null, targetStatus);
                orderStatusProducer.sendOrderStatusMsg(OcUtils.initOcReq(order.getOrderId(), targetStatus, null, OrderTypeEnum.HN));

                // 非个付，创建支付单
                if (!PayTypeEnum.PPAY.getType().equals(order.getPaytype())) {
                    hotelPushService.createPayBill(order);
                }

                // 是个付或者混付 修改为待确认
                if (PayTypeEnum.MIXPAY.getType().equals(order.getPaytype()) || PayTypeEnum.PPAY.getType().equals(order.getPaytype())) {
                    //直接修改订单状态为待确认
                    confirmOrderService.confirmOrder(HotelConfirmOrderRequestBO.create(order.getOrderId()));
                    //查询酒店详情
                    HoHotel hoHotelInfo = hoHotelLoader.selectByOrderId(order.getOrderId());
                    //查询房间信息
                    HoRoom hoRoomInfo = hoRoomLoader.selectByOrderId(order.getOrderId());
                    //发送短信
                    messageService.sendMessageForApprovalPassRemindPay(order, hoHotelInfo, hoRoomInfo, getPayTimeout(order.getOrderId()));
                }
                // 发布ocs审批通过事件
                log.info("审批通过，发送ocs审批通过事件，orderId:{}", order.getOrderId());
                eventCenter.post(
                        new ApprovalStatusChangeExternalEvent(ApplicationEnum.HOTEL_DOMESTIC.getCode(),
                                Long.valueOf(businessId),
                                ApprovalStatusEnum.T.getCode()));
            }

            // 审批驳回->取消订单->通知供应商取消订单
            if (Objects.equals(AccreditResultTypeEnum.APPROVAL_REJECT.getCode(), approveResult)) {
                cancelOrderProductService.approvalRejectCancelOrder(order.getOrderId());

                // 发布ocs审批驳回事件
                log.info("审批驳回，发送ocs审批驳回事件，orderId:{}", order.getOrderId());
                eventCenter.post(
                        new ApprovalStatusChangeExternalEvent(ApplicationEnum.HOTEL_DOMESTIC.getCode(),
                                Long.valueOf(businessId),
                                ApprovalStatusEnum.F.getCode()));
            }

        } catch (InterruptedException e) {
            log.error("redis获取锁异常：" + e.getMessage(), e);
            throw new CorpBusinessException(HotelResponseCodeEnum.REDIS_LOCK_EXCEPTION);
        } finally {
            //如果当前线程保持锁定则解锁
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private String getLockKey(String externalId) {
        return APPROVAL_SUCCESS_PROCESS_REDIS_KEY_PREFIX.concat(externalId);
    }

    /**
     * 获取支付截止时间
     * 逻辑参考 hotel-booking OrderDetailService#queryOrderDetail
     */
    private Date getPayTimeout(Long orderId) {
        List<PpPaymentBillDto> paymentBillDtoList = payBillClientLoader.paymentBillInfo(orderId);
        // 预定时间
        Date ticketDate = paymentBillDtoList.stream().filter(e -> PaymentStatusType.UnHandler.getType().equalsIgnoreCase(e.getStatus())).findFirst().map(PpPaymentBillDto::getTxTime).orElse(new Date());
        // 系统设定支付时间[分钟]
        int payTimeLimit = basicDataClientLoader.getPayTimeLimit();
        return DateUtils.addMinutes(ticketDate, payTimeLimit);
    }
}
