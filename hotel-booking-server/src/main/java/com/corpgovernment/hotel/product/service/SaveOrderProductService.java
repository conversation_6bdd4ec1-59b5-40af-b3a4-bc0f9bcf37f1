package com.corpgovernment.hotel.product.service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import com.corpgovernment.api.order.common.enums.DataStorageTypeEnum;
import com.corpgovernment.hotel.product.dataloader.db.HoDataStorageLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoHotelMemberLoader;
import com.corpgovernment.hotel.product.dataloader.db.OrderSnapshotDataLoader;
import com.corpgovernment.hotel.product.entity.db.HoHotelMember;
import com.corpgovernment.hotel.product.entity.db.OrderSnapshotData;
import com.ctrip.corp.obt.generic.utils.ObjectUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.corpgovernment.api.hotel.product.model.saveorder.request.SaveOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.updateorder.request.UpdateOrderRequestBo;
import com.corpgovernment.api.ordercenter.dto.OcUtils;
import com.corpgovernment.api.ordercenter.dto.request.OrderAggregatesDto;
import com.corpgovernment.api.ordercenter.dto.request.OrderInfoRequest;
import com.corpgovernment.api.ordercenter.dto.request.file.SaveBusinessFileRequest;
import com.corpgovernment.api.ordercenter.enums.BusinessTypeEnum;
import com.corpgovernment.api.ordercenter.enums.ProductEnum;
import com.corpgovernment.api.supplier.soa.constant.ProductTypeEnum;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BusinessFileClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CommonOrderClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.mq.OrderStatusProducer;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SaveOrderProductService {

    @Autowired
    private HoOrderService hoOrderService;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HoPassengerService hoPassengerService;
    @Autowired
    private HoRoomService hoRoomService;
    @Autowired
    private HoHotelService hoHotelService;
    @Autowired
    private HoHotelLowService hoHotelLowService;
    @Autowired
    private HoInvoiceService hoInvoiceService;
    @Autowired
    private HoDeliveryInfoService hoDeliveryInfoService;
    @Autowired
    private HoRoomDailyInfoService hoRoomDailyInfoService;
    @Autowired
    private HoOrderCancelRuleService orderCancelRuleService;
    @Autowired
    private CommonOrderClientLoader commonOrderClientLoader;
    @Autowired
    private OrderStatusProducer orderStatusProducer;
    @Autowired
    @Qualifier(value = "basicThreadPoolExecutor")
    private ThreadPoolExecutor basicThreadPoolExecutor;
    @Autowired
    private BusinessFileClientLoader businessFileClientLoader;
    @Autowired
    private HoHotelMemberLoader hoHotelMemberLoader;
    @Autowired
    private HoDataStorageLoader hoDataStorageLoader;
    @Autowired
    private HoChummageService hoChummageService;
    @Autowired
    private OrderSnapshotDataLoader orderSnapshotDataLoader;

    @Transactional(rollbackFor = Exception.class)
    public void saveOrder(SaveOrderRequestBo request) {
        log.info("saveOrder request:{}", JsonUtils.toJsonString(request));
        List<SaveOrderRequestBo.PassengerInfo> passengerInfoList = request.getPassengerInfoList();
        hoOrderService.save(request);
        // 保存多成本中心，并将dataId填充至出行人中
        saveMultiCostCenterAndFillPassenger(passengerInfoList);
        // 保存核算单元，并将dataId填充至出行人中
        saveAccountingUnitAndFillPassenger(passengerInfoList);
        hoPassengerService.save(passengerInfoList);
        hoRoomService.save(request.getRoomInfo());
        hoHotelService.save(request.getHotelInfo());
        if(ObjectUtils.notNull(request.getChummageInfo())){
            hoChummageService.save(request.getChummageInfo(),request.getOrderInfo().getOrderId());
        }
        if (request.getHotelLowInfo() != null) {
            hoHotelLowService.save(request.getHotelLowInfo());
        }
        if (request.getDeliveryInfo() != null) {
            hoDeliveryInfoService.save(request.getDeliveryInfo());
        }
        if (request.getInvoiceInfo() != null) {
            hoInvoiceService.save(request.getInvoiceInfo());
        }
        if (request.getOrderCancelRule() != null) {
            request.getOrderCancelRule().forEach(orderCancelRuleService::save);
        }
        hoRoomDailyInfoService.save(request.getOrderInfo().getOrderId(), request.getRoomDailyInfoList());
        CompletableFuture.runAsync(() -> saveOrderAggregates(request), basicThreadPoolExecutor);
        this.saveBusinessFileInfo(request);
        this.saveHotelMember(request);
        this.saveOrderSnapshotData(request.getOrderSnapshotDataList());
    }

    /**
     * 保存出行人的核算单元，并将dataId填充至出行人中
     *
     * @param passengerInfos
     */
    private void saveAccountingUnitAndFillPassenger(List<SaveOrderRequestBo.PassengerInfo> passengerInfos) {
        if (CollectionUtils.isEmpty(passengerInfos)) {
            return;
        }
        // 保存出行人的核算单元
        passengerInfos.forEach(e -> {
            String accountingUnitJson = e.getAccountingUnitJson();
            if (StringUtils.isNotBlank(accountingUnitJson)) {
                // 保存多成本中心，并拿到dataId
                String accountingUnitDataId = hoDataStorageLoader.insertDataStorage(DataStorageTypeEnum.ACCOUNTING_UNIT,
                        accountingUnitJson);
                e.setAccountingUnitDataId(accountingUnitDataId);
                log.info("成功保存出行人[{}], 的核算单元[{}], dataId[{}]", e.getTravelerName(), accountingUnitJson,
                        accountingUnitDataId);
            }
        });
    }

    /**
     * 保存出行人的成本中心，并将dataId填充至出行人中
     *
     * @param passengerInfos
     */
    private void saveMultiCostCenterAndFillPassenger(List<SaveOrderRequestBo.PassengerInfo> passengerInfos) {
        if (CollectionUtils.isEmpty(passengerInfos)) {
            return;
        }
        // 保存出行人的多成本中心
        passengerInfos.forEach(e -> {
            String multiCostCenterDataJSON = e.getMultiCostCenterDataJSON();
            if (StringUtils.isNotBlank(multiCostCenterDataJSON)) {
                // 保存多成本中心，并拿到dataId
                String costCenterDataId = hoDataStorageLoader.insertDataStorage(DataStorageTypeEnum.MULTI_COST_CENTER,
                        multiCostCenterDataJSON);
                e.setMultiCostCenterDataId(costCenterDataId);
                log.info("成功保存出行人[{}], 的多成本中心[{}], dataId[{}]", e.getTravelerName(), multiCostCenterDataJSON,
                        costCenterDataId);
            }
        });
    }

    /**
     * 保存酒店会员信息
     * 
     * @param request
     */
    private void saveHotelMember(SaveOrderRequestBo request) {
        SaveOrderRequestBo.HotelMemberInfo hotelMemberInfo = request.getHotelMemberInfo();
        if (null == hotelMemberInfo) {
            return;
        }
        HoHotelMember hoHotelMember = new HoHotelMember();
        hoHotelMember.setOrderId(request.getOrderInfo().getOrderId());
        hoHotelMember.setGroupId(hotelMemberInfo.getGroupId());
        hoHotelMember.setGroupName(hotelMemberInfo.getGroupName());
        hoHotelMember.setEnabledBonusPoint(BooleanUtils.isTrue(hotelMemberInfo.getEnabledBonusPoint()));
        hoHotelMember.setMemberCardNo(hotelMemberInfo.getMemberCardNo());
        hoHotelMember.setMemberCardholder(hotelMemberInfo.getMemberCardholder());
        hoHotelMember.setMemberRuleDesc(hotelMemberInfo.getMemberRuleDesc());
        hoHotelMember.setBonusPointCode(hotelMemberInfo.getBonusPointCode());
        hoHotelMember.setBonusPointType(hotelMemberInfo.getBonusPointType());
        hoHotelMemberLoader.insert(hoHotelMember);
    }


    /**
     * 保存文件
     *
     * @param request
     */
    private void saveBusinessFileInfo(SaveOrderRequestBo request) {
        if (CollectionUtils.isEmpty(request.getFileList())) {
            return;
        }

        businessFileClientLoader.saveBusinessFileInfo(request.getFileList().stream()
            .map(file -> createSaveBusinessFileRequest(file, String.valueOf(request.getOrderInfo().getOrderId())))
            .collect(Collectors.toList()));
    }

    /**
     * 组装参数
     *
     * @param file
     * @param orderId
     * @return
     */
    private SaveBusinessFileRequest createSaveBusinessFileRequest(SaveOrderRequestBo.BusinessFileInfo file,
        String orderId) {
        SaveBusinessFileRequest saveBusinessFileRequest = new SaveBusinessFileRequest();
        saveBusinessFileRequest.setBusinessId(orderId);
        saveBusinessFileRequest.setBusinessType(BusinessTypeEnum.SUBMIT_ORDER.getCode());
        saveBusinessFileRequest.setProductType(ProductEnum.HOTEL.getCode());
        saveBusinessFileRequest.setFileName(file.getFileName());
        saveBusinessFileRequest.setFilePath(file.getFilePath());
        saveBusinessFileRequest.setFileType(file.getFileType());
        saveBusinessFileRequest.setFileSize(file.getFileSize());
        return saveBusinessFileRequest;
    }

    private void saveOrderAggregates(SaveOrderRequestBo request) {
        // 添加行程
        OrderInfoRequest orderInfoRequest = new OrderInfoRequest();
        orderInfoRequest.setOrderAggregates(getOrderAggregates(request));
        try {
            commonOrderClientLoader.addOrder(orderInfoRequest);
        } catch (Exception e) {
            log.error("添加到订单中心失败，request:{},错误信息e:{}", orderInfoRequest, e.getMessage());
        }
    }

    /**
     * 获取订单聚合行程信息
     */
    private List<OrderAggregatesDto> getOrderAggregates(SaveOrderRequestBo request) {
        SaveOrderRequestBo.OrderInfo orderInfo = request.getOrderInfo();
        SaveOrderRequestBo.HotelInfo hotelInfo = request.getHotelInfo();
        SaveOrderRequestBo.RoomInfo roomInfo = request.getRoomInfo();
        String departDate = DateUtil.dateToString(roomInfo.getCheckInDate());
        String arriveDate = DateUtil.dateToString(roomInfo.getCheckOutDate());
        List<SaveOrderRequestBo.PassengerInfo> passengerInfoList = request.getPassengerInfoList();
        List<OrderAggregatesDto> aggregatesDtos = Lists.newArrayList();
        Date date = new Date();
        passengerInfoList.forEach(p -> {
            OrderAggregatesDto dto = new OrderAggregatesDto();
            dto.setOrderId(orderInfo.getOrderId());
            dto.setAmount(orderInfo.getAmount());
            dto.setUid(p.getUid());
            dto.setOrgId(p.getOrgId());
            dto.setTravelerName(
                StringUtils.isNotBlank(p.getTravelerName()) ? p.getTravelerName() : p.getPassengerName());
            dto.setApproveNo(orderInfo.getTripApplyNo());
            dto.setTravelNo(orderInfo.getTripTrafficId());
            dto.setProductType(ProductTypeEnum.hotel.name());
            // 初始行程置为false，出票后会置为true
            dto.setIsEffective(false);
            dto.setOrderDate(orderInfo.getOrderDate());
            dto.setDepartDate(DateUtil.stringToDate(departDate, DateUtil.DF_YMD_HM));
            dto.setArriveDate(DateUtil.stringToDate(arriveDate, DateUtil.DF_YMD_HM));
            dto.setRoomName(roomInfo.getRoomName());
            dto.setHotelName(hotelInfo.getHotelName());
            dto.setDatachangeCreatetime(date);
            dto.setDatachangeLasttime(date);
            dto.setNextDay(roomInfo.getNextDay());
            dto.setCityName(hotelInfo.getCityName());
            dto.setOrderStatus(orderInfo.getOrderStatus());
            aggregatesDtos.add(dto);
        });
        return aggregatesDtos;
    }

    public boolean updateOrder(UpdateOrderRequestBo request) {
        Long orderId = request.getOrderId();
        HoOrder order = hoOrderLoader.selectByOrderId(orderId);
        if (order == null) {
            return false;
        }
        if (request.getIsDeleted() != null && request.getIsDeleted()) {
            Integer count = hoOrderLoader.deleteByOrderId(orderId);
            hoOrderLoader.updateCancelTime(orderId);
            return count > 0;
        }
        if (StringUtils.isNotBlank(request.getOrderStatus())) {
            Integer count = hoOrderLoader.updateOrderStatus(orderId, null, request.getOrderStatus());
            orderStatusProducer
                .sendOrderStatusMsg(OcUtils.initOcReq(orderId, request.getOrderStatus(), null, OrderTypeEnum.HN));
            return count > 0;
        }
        if (StringUtils.isNotBlank(request.getPayNo())) {
            int count = hoOrderLoader.updatePayNo(orderId, request.getPayNo());
            return count > 0;
        }
        return false;
    }

    public void saveOrderSnapshotData(List<SaveOrderRequestBo.OrderSnapshotDataInfo> orderSnapshotDataBoList) {
        if (CollectionUtils.isEmpty(orderSnapshotDataBoList)) {
            return;
        }
        List<OrderSnapshotData> orderSnapshotDataList = orderSnapshotDataBoList.stream().map(item -> {
            OrderSnapshotData orderSnapshotData = new OrderSnapshotData();
            orderSnapshotData.setProductType(item.getProductType());
            orderSnapshotData.setDataScene(item.getDataScene());
            orderSnapshotData.setDataType(item.getDataType());
            orderSnapshotData.setBusinessId(item.getBusinessId());
            orderSnapshotData.setDataContent(item.getDataContent());
            return orderSnapshotData;
        }).collect(Collectors.toList());
        orderSnapshotDataLoader.batchInsert(orderSnapshotDataList);
    }
    
}
