package com.corpgovernment.hotel.product.mapper;

import com.corpgovernment.hotel.product.common.mybatis.TkMapper;
import com.corpgovernment.hotel.product.entity.db.HoOrderCancelRule;
import com.corpgovernment.hotel.product.entity.db.MsBaseCtripCorpBrand;
import com.ctrip.corp.obt.shard.annotation.ShardModel;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.entity.Example;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/29
 * @description
 */
public interface MsBaseCtripCorpBrandMapper extends TkMapper<MsBaseCtripCorpBrand> {

    @ShardModel(targetDataSource = "apollo_common")
    default List<MsBaseCtripCorpBrand> queryListByBrandIdList(List<String> brandIdList, String brandType){
        Example example = new Example(MsBaseCtripCorpBrand.class);
        example.createCriteria().andIn("brandId", brandIdList)
                .andEqualTo("brandType", brandType)
                .andEqualTo("isDeleted", 0);
        return selectByExample(example);
    }
}

