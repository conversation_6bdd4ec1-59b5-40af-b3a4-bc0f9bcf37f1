package com.corpgovernment.hotel.product.model.ctrip.response;

import lombok.Data;

@Data
public class SupplierCancelOrderResponse {
    /**
     * 非携程返回
     */
    private String errorCode;
    /**
     * 非携程返回
     */
    private String message;
    private Boolean isSuccess;
    /**
     * 携程返回
     */
    private SupplierResponseStatus responseStatus;

    /**
     * 0：成功 1：失败 2：取消中
     */
    private String cancelStatus;
    /**
     * 携程返回 message
     */
    private String returnMessage;
}
