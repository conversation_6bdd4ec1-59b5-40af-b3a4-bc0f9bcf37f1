package com.corpgovernment.hotel.product.external.convert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderInquiryRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderInquiryResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderInquiryRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderInquiryResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.MeiyaCancelOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.StandardCancelOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.StandardCancelOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.StandardCancelOrderInquiryRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.StandardCancelOrderInquiryResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.StandardCancelOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.StandardCancelOrderResponse;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

/**
 * @author: pwang27
 * @Date: 2024/4/9 21:39
 * @Description:
 */

@Mapper(componentModel = "spring")
public interface CancelOrderConvert {

    /**
     * 转换为携程供应商取消订单问询请求类
     *
     * @param request 标准契约取消订单问询请求类
     * @return 携程供应商取消订单问询请求类
     */
    @Mapping(target = "orderId", source = "orderID")
    CtripCancelOrderInquiryRequest toCtripCancelOrderInquiryRequest(StandardCancelOrderInquiryRequest request);


    /**
     * 转换为标准供应商取消订单问询响应类
     *
     * @param cancelQueryInfo 携程供应商取消订单问询请求类
     * @return 标准供应商取消订单问询响应类
     */
    StandardCancelOrderInquiryResponse toCancelOrderInquiryResponse(CtripCancelOrderInquiryResponse.CancelQueryInfo cancelQueryInfo);


    /**
     * 转换为美亚供应商取消订单问询请求类
     *
     * @param request 标准契约取消订单问询请求类
     * @return 携程供应商取消订单问询请求类
     */
    @Mapping(target = "orderId", source = "orderID")
    @Mapping(target = "corpId", source = "corpID")
    @Mapping(target = "uid", source = "supplierUid")
    MeiyaCancelOrderInquiryRequest toMeiyaCancelOrderInquiryRequest(StandardCancelOrderInquiryRequest request);

    /**
     * 转换为标准供应商取消订单问询响应类
     *
     * @param cancelQueryInfo 携程供应商取消订单问询请求类
     * @return 标准供应商取消订单问询响应类
     */
    StandardCancelOrderInquiryResponse toCancelOrderInquiryResponse(MeiyaCancelOrderInquiryResponse.CancelQueryInfo cancelQueryInfo);

    /**
     * 转换为携程供应商取消订单请求类
     *
     * @param request 标准契约取消订单请求类
     * @return 携程供应商取消订单请求类
     */
    default CtripCancelOrderRequest toCtripCancelOrderRequest(StandardCancelOrderRequest request) {
        CtripCancelOrderRequest ctripCancelOrderRequest = new CtripCancelOrderRequest();
        ctripCancelOrderRequest.setProductUrl(request.getProductUrl());
        ctripCancelOrderRequest.setUserKey(request.getUserKey());
        ctripCancelOrderRequest.setSupplierCode(request.getSupplierCode());

        ctripCancelOrderRequest.setOrderId(request.getOrderID());
        Optional.ofNullable(request.getAdditionalInformationMap().get(SupplierConstant.CancelOrder.REQUEST_ADDITIONAL_MAP_KEY_UID)).ifPresent(item -> ctripCancelOrderRequest.setUid(String.valueOf(item)));
        Optional.ofNullable(request.getAdditionalInformationMap().get(SupplierConstant.CancelOrder.REQUEST_ADDITIONAL_MAP_KEY_SID)).ifPresent(item -> ctripCancelOrderRequest.setSid(String.valueOf(item)));
        ctripCancelOrderRequest.setCorpID(request.getCorpID());
        ctripCancelOrderRequest.setCancelReason(request.getCancelReason());
        ctripCancelOrderRequest.setApplyCancelFlag(request.getApplyCancelFlag());
        ctripCancelOrderRequest.setCancelType(request.getCancelType());
        ctripCancelOrderRequest.setCancelCategory(request.getCancelCategory());
        return ctripCancelOrderRequest;
    }

    /**
     * 转换为标准供应商取消订单响应类
     *
     * @param response 携程供应商取消订单请求类
     * @return 标准供应商取消订单响应类
     */
    default StandardCancelOrderResponse toCancelOrderResponse(CtripCancelOrderResponse response) {
        StandardCancelOrderResponse standardCancelOrderResponse = new StandardCancelOrderResponse();
        if (SupplierConstant.CTRIP_SUPPLIER_ASYNC_CANCEL_ERROR_CODE.equals(response.getErrorCode())) {
            standardCancelOrderResponse.setCancelStatus(SupplierConstant.CancelStatus.CANCEL_STATUS_CANCELLING);
            return standardCancelOrderResponse;
        }
        standardCancelOrderResponse.setCancelStatus(SupplierConstant.CancelStatus.CANCEL_STATUS_CANCEL_SUCCESS);
        return standardCancelOrderResponse;
    }

    /**
     * 转换为携程供应商取消订单请求类
     *
     * @param request 标准契约取消订单请求类
     * @return 携程供应商取消订单请求类
     */
    default MeiyaCancelOrderRequest toMeiyaCancelOrderRequest(StandardCancelOrderRequest request) {
        MeiyaCancelOrderRequest meiyaCancelOrderRequest = new MeiyaCancelOrderRequest();
        meiyaCancelOrderRequest.setProductUrl(request.getProductUrl());
        meiyaCancelOrderRequest.setUserKey(request.getUserKey());
        meiyaCancelOrderRequest.setSupplierCode(request.getSupplierCode());

        meiyaCancelOrderRequest.setOrderId(request.getOrderID());
        Optional.ofNullable(request.getAdditionalInformationMap().get(SupplierConstant.CancelOrder.REQUEST_ADDITIONAL_MAP_KEY_ORDER_TYPE)).ifPresent(item -> meiyaCancelOrderRequest.setOrderType(String.valueOf(item)));
        Optional.ofNullable(request.getAdditionalInformationMap().get(SupplierConstant.CancelOrder.REQUEST_ADDITIONAL_MAP_KEY_UID)).ifPresent(item -> meiyaCancelOrderRequest.setUid(String.valueOf(item)));
        Optional.ofNullable(request.getAdditionalInformationMap().get(SupplierConstant.CancelOrder.REQUEST_ADDITIONAL_MAP_KEY_SID)).ifPresent(item -> meiyaCancelOrderRequest.setSid(String.valueOf(item)));
        meiyaCancelOrderRequest.setCorpID(request.getCorpID());
        meiyaCancelOrderRequest.setCancelReason(request.getCancelReason());
        meiyaCancelOrderRequest.setApplyCancelFlag(request.getApplyCancelFlag());
        meiyaCancelOrderRequest.setCancelType(request.getCancelType());
        meiyaCancelOrderRequest.setCancelCategory(request.getCancelCategory());
        return meiyaCancelOrderRequest;
    }

    /**
     * 转换为标准供应商取消订单响应类
     *
     * @param response 美亚供应商取消订单请求类
     * @return 标准供应商取消订单响应类
     */
    default StandardCancelOrderResponse toCancelOrderResponse(MeiyaCancelOrderResponse response){
        StandardCancelOrderResponse standardCancelOrderResponse = new StandardCancelOrderResponse();
        standardCancelOrderResponse.setCancelStatus(Integer.valueOf(response.getCancelStatus()));
        Map<String, Object> additionalInformationMap = new HashMap<>();
        additionalInformationMap.put(SupplierConstant.CancelOrder.RESPONSE_ADDITIONAL_MAP_KEY_IS_SUCCESS, response.getIsSuccess());
        additionalInformationMap.put(SupplierConstant.CancelOrder.RESPONSE_ADDITIONAL_MAP_KEY_ERROR_CODE, response.getErrorCode());
        additionalInformationMap.put(SupplierConstant.CancelOrder.RESPONSE_ADDITIONAL_MAP_KEY_MESSAGE, response.getMessage());
        standardCancelOrderResponse.setAdditionalInformationMap(additionalInformationMap);
        return standardCancelOrderResponse;
    }

    @Mapping(source ="orderID", target = "orderId")
    CtripCancelOrderDetailRequest toCtripCancelOrderDetailRequest(StandardCancelOrderDetailRequest request);

    default StandardCancelOrderDetailResponse toCancelOrderDetailResponse(CtripCancelOrderDetailResponse response) {
        StandardCancelOrderDetailResponse standardCancelOrderDetailResponse = new StandardCancelOrderDetailResponse();
        List<CancelOrderDetailResponse.CancelOrderDetail> formDetailList = response.getFormDetailList();
        List<StandardCancelOrderDetailResponse.FormDetail> standardFormDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(formDetailList)) {
            standardFormDetailList = formDetailList.stream().map(item -> {
                StandardCancelOrderDetailResponse.FormDetail formDetail =
                        new StandardCancelOrderDetailResponse.FormDetail();
                formDetail.setOrderID(item.getOrderId());
                formDetail.setFormID(item.getFormId());
                formDetail.setFormType(item.getFormType());
                formDetail.setCreateTime(item.getCreateTime());
                formDetail.setStatus(item.getStatus());
                formDetail.setCancelType(item.getCancelType());
                formDetail.setReasonCode(item.getReasonCode());
                formDetail.setReasonDesc(item.getReasonDesc());
                formDetail.setStatusItemList(item.getStatusItemList().stream().map(statusItem -> {
                    StandardCancelOrderDetailResponse.StatusItem standardStatusItem =
                            new StandardCancelOrderDetailResponse.StatusItem();
                    standardStatusItem.setStatus(statusItem.getStatus());
                    standardStatusItem.setStatusTime(statusItem.getStatusTime());
                    standardStatusItem.setStatusSortIndex(statusItem.getStatusSortIndex());
                    return standardStatusItem;
                }).collect(Collectors.toList()));
                return formDetail;
            }).collect(Collectors.toList());
        }

        standardCancelOrderDetailResponse.setFormDetailList(standardFormDetailList);
        return standardCancelOrderDetailResponse;
    }

    @Mapping(source ="orderID", target = "orderId")
    MeiyaCancelOrderDetailRequest toMeiyaCancelOrderDetailRequest(StandardCancelOrderDetailRequest request);

    default StandardCancelOrderDetailResponse toCancelOrderDetailResponse(MeiyaCancelOrderDetailResponse response) {
        StandardCancelOrderDetailResponse standardCancelOrderDetailResponse = new StandardCancelOrderDetailResponse();
        List<CancelOrderDetailResponse.CancelOrderDetail> formDetailList = response.getFormDetailList();

        List<StandardCancelOrderDetailResponse.FormDetail> standardFormDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(formDetailList)) {
            standardFormDetailList =
                    formDetailList.stream().map(item -> {
                        StandardCancelOrderDetailResponse.FormDetail formDetail =
                                new StandardCancelOrderDetailResponse.FormDetail();
                        formDetail.setOrderID(item.getOrderId());
                        formDetail.setFormID(item.getFormId());
                        formDetail.setFormType(item.getFormType());
                        formDetail.setCreateTime(item.getCreateTime());
                        formDetail.setStatus(item.getStatus());
                        formDetail.setCancelType(item.getCancelType());
                        formDetail.setReasonCode(item.getReasonCode());
                        formDetail.setReasonDesc(item.getReasonDesc());
                        formDetail.setStatusItemList(item.getStatusItemList().stream().map(statusItem -> {
                            StandardCancelOrderDetailResponse.StatusItem standardStatusItem =
                                    new StandardCancelOrderDetailResponse.StatusItem();
                            standardStatusItem.setStatus(statusItem.getStatus());
                            standardStatusItem.setStatusTime(statusItem.getStatusTime());
                            standardStatusItem.setStatusSortIndex(statusItem.getStatusSortIndex());
                            return standardStatusItem;
                        }).collect(Collectors.toList()));
                        return formDetail;
                    }).collect(Collectors.toList());
        }
        standardCancelOrderDetailResponse.setFormDetailList(standardFormDetailList);
        return standardCancelOrderDetailResponse;
    }

}
