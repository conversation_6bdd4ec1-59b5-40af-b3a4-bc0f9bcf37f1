package com.corpgovernment.hotel.product.external.dto.order.cancel;

import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 美亚契约-取消订单问询请求类
 *
 * @see <a href="https://openapi.ctripbiz.com/#/serviceApi?apiId=1000397"></a>
 */
@Data
public class MeiyaCancelOrderInquiryRequest extends BaseExternalRequest {

    @ApiModelProperty("供应商订单号")
    private String orderId;

    @ApiModelProperty("供应商公司id")
    private String corpId;

    //不知道干啥用的
    @ApiModelProperty("sid")
    private String sid;

    //不知道干啥用的
    @ApiModelProperty("uid")
    private String uid;

}
