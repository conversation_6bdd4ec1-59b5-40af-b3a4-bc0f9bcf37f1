package com.corpgovernment.hotel.product.external.dto.order.book;

import java.math.BigDecimal;
import java.util.List;

import com.corpgovernment.api.hotel.product.enums.MixPayWayEnum;
import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 携程契约-预定下单请求类
 */
@Data
public class CtripReserveOrderRequest extends BaseExternalRequest {

    /**
     * 本次查询用户相关信息
     */
    private BaseInfo baseInfo;

    /**
     * 产品价格编码
     */
    private String productId;

    /**
     * 创建订单房型信息
     */
    private RoomInfo roomInfo;

    /**
     * 入住人列表
     */
    private List<Client> clientList;

    /**
     * 联系人
     */
    private ContactorInfo contactorInfo;

    /**
     * 备注信息
     */
    private RemarkInfo remarkInfo;

    /**
     * 支付信息
     */
    private PaymentInfo paymentInfo;

    /**
     * 发票信息
     */
    private InvoiceInfo invoiceInfo;

    /**
     * 发票配送信息 该字段后续移除 电票无需
     */
    private InvoiceDeliveryInfo invoiceDeliveryInfo;

    /**
     * 创建订单扩展信息
     */
    private CreateOrderExtInfo extInfo;

    /**
     * 价格信息
     */
    private PriceInfo priceInfo;

    /**
     * 商旅订单信息
     */
    private CorpOrderInfo corpOrderInfo;

    @Data
    public static class BaseInfo {
        /**
         * 商旅客户卡号，必填
         */
        private String uid;

        /**
         * 公司ID，必填
         */
        private String corpId;

        /**
         * 语言类别；枚举类型：ZH_CN EN_US；选填，默认ZH_CN
         */
        private String language;

        /**
         * 平台订单号
         */
        private String platformOrderId;
    }

    @Data
    public static class RoomInfo {
        /**
         * 预订房间数量
         */
        public Integer roomQuantity;

        /**
         * 入住人数
         */
        public Integer guestQuantity;

        /**
         * 到店时间
         */
        private String checkInDate;

        /**
         * 离店时间
         */
        private String checkOutDate;
    }

    @Data
    public static class Client {

        /**
         * uid
         */
        private String uid;
        /**
         * * 入住人名字
         */
        private String name;

        private String mobilePhone;

        private String countryCode;

        /**
         * 入住人是否获得积分
         */
        private Boolean earnPoints;

        /**
         * 房间索引
         */
        private Integer roomIndex;

        /**
         * 成本中心
         */
        private List<CostCenter> clientCostCenterList;

        /**
         * 证件类型
         */
        private String certificateType;

        /**
         * 证件号码
         */
        private String certificateNumber;
    }

    @Data
    public static class CostCenter{
        private Integer costCenterKey;
        private String costCenterValue;
        private String costCenterValueEn;
        private String costCenterTitle;
        private String costCenterTitleEn;
        private String costCenterName;
    }

    @Data
    public static class ContactorInfo {
        /**
         * * 名字
         */
        public String name;

        /**
         * * Email
         */
        public String email;

        /**
         * * 手机号
         */
        public String mobilePhone;

        /**
         * * 手机号前国家代号(e.g. China - 86)
         */
        public String mobilePhoneCountryCode;
    }

    @Data
    public static class RemarkInfo {
        /**
         * 自定义备注
         */
        private String customRemark;

        /**
         * 用户可选备注
         */
        private List<OptionalRemark> optionalRemarkList;
    }

    @Data
    public static class OptionalRemark {
        /**
         * 可选备注Id
         */
        private String id;
        /**
         * 可选备注Key
         */
        private String key;
        /**
         * 可选备注Title
         */
        private String title;
        /**
         * 可选备注Value
         */
        private String value;
    }

    @Data
    public static class PaymentInfo {

        /**
         * 预付房费支付方式(ACCNT：公司账户支付，SELF_PAY：个人支付，MIX_PAY：随心付)
         */
        @JsonProperty("prepayType")
        public String prepayType;

        @JsonProperty("mixPayWayInfo")
        public List<MixPaymentWay> mixPayWayInfo;

        public CorpPayInfo corpPayInfo;

    }

    @Data
    public static class MixPaymentWay {

        @JsonProperty("mixPayWay")
        public MixPayWayEnum mixPayWay;

        @JsonProperty("payAmount")
        public BigDecimal payAmount;
    }

    @Data
    public static class CorpPayInfo {

        private ServiceFeeInfo serviceFeeInfo;
    }
    
    @Data
    public static class ServiceFeeInfo {

        private BigDecimal serviceFee;

    }

    @Data
    public static class InvoiceInfo {
        /**
         * 创建订单时选定的增值税发票类型 VAT:增值税普票（纸质） EVAT:电子发票（普票） VATSpecial:增值税专票（纸质）
         */
        public String orderInvoiceTargetType;

        /**
         * 发票抬头类型 P:个人 E:企业 G:政府机关行政单位
         */
        public String invoiceTitleType;

        /**
         * 发票抬头
         */
        public String invoiceTitle;

        /**
         * 纳税人识别号
         */
        public String taxpayerNumber;

        /**
         * 公司名称
         */
        public String companyName;

        /**
         * 公司地址
         */
        public String companyAddress;

        /**
         * 公司电话
         */
        public String companyPhone;

        /**
         * 开户银行名称
         */
        public String companyBankName;

        /**
         * 开户银行账号
         */
        public String companyBankAccount;

        /**
         * 电子邮箱
         */
        public String email;
    }

    @Data
    public static class InvoiceDeliveryInfo {
        /**
         * 联系人名字
         */
        public String contactName;

        /**
         * 发票配送联系方式（手机号）
         */
        public String postPhone;

        /**
         * 发票地址省份
         */
        public String province;

        /**
         * 发票地址城市
         */
        public String city;

        /**
         * 发票地址行政区
         */
        public String canton;

        /**
         * 发票具体地址
         */
        public String address;
    }

    @Data
    public static class CreateOrderExtInfo {


        /**
         * 外部关联订单号（目前仅深圳分销渠道赋值）
         */
        public String externalOrderId;
        /**
         * 用户输入的会员卡号
         */
        private String membershipCardNum;

    }

    @Data
    public static class PriceInfo {

        /**
         * 单价，但是同分销侧确认该字段传入总金额可进行金额校验
         */
        private BigDecimal sellPrice;

    }

    @Data
    public static class CorpOrderInfo {

        /**
         * 是否给入住人发短信
         */
        private Boolean sendMsg;

    }
}
