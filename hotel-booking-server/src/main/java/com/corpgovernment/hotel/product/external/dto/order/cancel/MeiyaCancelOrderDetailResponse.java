package com.corpgovernment.hotel.product.external.dto.order.cancel;

import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderDetailResponse;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 美亚契约-取消下单详情响应类
 */
@Data
public class MeiyaCancelOrderDetailResponse {

    private CancelOrderStatus status;

    private List<CancelOrderDetailResponse.CancelOrderDetail> formDetailList;

    @Data
    public static class CancelOrderDetail {
        private String formId;
        private String orderId;
        private String createTimeUTC;
        private String createTime;
        private String formType;
        private Integer status;
        private String cancelType;
        private String reasonCode;
        private String reasonDesc;
        List<CancelOrderDetailResponse.StatusDetail> statusItemList;
    }

    @Data
    public static class StatusDetail {
        private Integer status;
        private String statusTime;
        private Integer statusSortIndex;
        private String statusTimeUTC;
    }
}
