package com.corpgovernment.hotel.product.dataloader.db;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.corpgovernment.hotel.product.entity.db.OrderSnapshotData;
import com.corpgovernment.hotel.product.mapper.OrderSnapshotDataMapper;
import tk.mybatis.mapper.entity.Example;

@Component
public class OrderSnapshotDataLoader {

    @Autowired
    private OrderSnapshotDataMapper orderSnapshotDataMapper;


    public int batchInsert(List<OrderSnapshotData> orderSnapshotDataList) {
        return orderSnapshotDataMapper.batchInsert(orderSnapshotDataList);
    }

    public List<OrderSnapshotData> listByBusinessId(String businessId) {
        Example example = new Example(OrderSnapshotData.class);
        example.createCriteria().andEqualTo("businessId", businessId).andEqualTo("isDeleted", false);
        return orderSnapshotDataMapper.selectByExample(example);
    }

}
