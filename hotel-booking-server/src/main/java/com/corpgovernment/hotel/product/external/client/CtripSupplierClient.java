package com.corpgovernment.hotel.product.external.client;

import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.corpgovernment.api.supplier.soa.constant.ProductTypeEnum;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.constant.CommonConst;
import com.corpgovernment.common.dataloader.CommonOrganizationDataloader;
import com.corpgovernment.common.dto.GetEmployeeOpenCardReq;
import com.corpgovernment.common.dto.GetEmployeeOpenCardRsp;
import com.corpgovernment.common.enums.MonitorLevelEnums;
import com.corpgovernment.common.enums.MonitorTypeEnums;
import com.corpgovernment.common.utils.Md5Util;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BasicDataClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.CtripReserveOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.CtripReserveOrderRequest.BaseInfo;
import com.corpgovernment.hotel.product.external.dto.order.book.CtripReserveOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderInquiryRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderInquiryResponse;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.cancel.CtripCancelOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.confirm.CtripConfirmOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.confirm.CtripConfirmOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.detail.CtripOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.detail.CtripOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.CtripOrderModifiableRoomNightQueryRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.CtripOrderModifiableRoomNightQueryResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.CtripOrderModificationDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.CtripOrderModificationDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.CtripOrderModificationInquiryRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.CtripOrderModificationInquiryResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.CtripOrderModificationRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.CtripOrderModificationResponse;
import com.corpgovernment.hotel.product.service.OrderMonitorService;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.framework.apollo.ConfigService;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: pwang27
 * @Date: 2024/4/9 21:29
 * @Description: 携程商旅接口实现
 */
@Component
@Slf4j
public class CtripSupplierClient extends CommonSupplierClient {

    private static final String OBK = "obk_";

    @Autowired
    private BasicDataClientLoader basicDataClientLoader;

    @Resource
    private OrderMonitorService orderMonitorService;
    @Resource
    private CommonOrganizationDataloader commonOrganizationDataloader;
    @Autowired
    private HoOrderLoader hoOrderLoader;

    /**
     * 预定下单
     */
    public CtripReserveOrderResponse reserveOrder(CtripReserveOrderRequest request) {
        BaseInfo baseInfo = request.getBaseInfo();
        String supplierUid = replaceSupplierUid(baseInfo.getPlatformOrderId());
        if (StringUtils.isNotBlank(supplierUid)) {
            request.setSupplierUid(supplierUid);
            request.getBaseInfo().setUid(supplierUid);
        }
        CtripReserveOrderResponse response = asyncHttpPostOriginResp(OrderMonitorService.BOOK_ORDER,
            request, CtripReserveOrderResponse.class, HotelResponseCodeEnum.SAVE_ORDER_SUPPLIER_ERROR);

        if (response == null || StringUtils.isBlank(response.getOrderID())) {
            // 携程错误信息
            Integer errorCode = Optional.ofNullable(response).map(CtripReserveOrderResponse::getStatus)
                .map(CtripReserveOrderResponse.ResponseStatus::getErrorCode)
                .orElse(null);
            String errorMessage = Optional.ofNullable(response).map(CtripReserveOrderResponse::getStatus)
                .map(CtripReserveOrderResponse.ResponseStatus::getErrorMessage).orElse(StringUtils.EMPTY);
            orderMonitorService.saveOrderMonitor(baseInfo.getPlatformOrderId(), MonitorTypeEnums.IE,
                MonitorLevelEnums.IMP, JsonUtils.toJsonString(response), String.valueOf(errorCode), errorMessage,
                OrderMonitorService.CREATE_ORDER);
            throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_SUPPLIER_ERROR);
        }

        return response;
    }

    /**
     * 携程订单详情
     *
     * @param request 订单详情
     * @return 携程订单详情
     */
    public CtripOrderDetailResponse orderDetail(CtripOrderDetailRequest request) {
        replaceSupplierUid(request.getSupplierOrderId(), request);

        CtripOrderDetailRequest.Auth auth = new CtripOrderDetailRequest.Auth();
        auth.setAppKey(OBK.concat(request.getSupplierCorpId()));
        auth.setTicket(
            basicDataClientLoader.ctripTokenByCorpID(request.getSupplierCorpId(), request.getSupplierCode()));
        request.setAuth(auth);
        return asyncHttpPostOriginResp(OrderMonitorService.ORDER_DETAILS,
            request, CtripOrderDetailResponse.class, HotelResponseCodeEnum.SUPPLIER_ORDER_DETAILS_ARE_EMPTY);
    }

    /**
     * 订单确认接口
     *
     * @param request 订单确认请求
     * @return 订单确认响应
     */
    public CtripConfirmOrderResponse confirmOrder(CtripConfirmOrderRequest request) {
        replaceSupplierUid(request.getOrderID(), request);

        String supplierCorpId = request.getSupplierCorpId();
        CtripConfirmOrderRequest.Auth auth = new CtripConfirmOrderRequest.Auth();
        auth.setAppKey(OBK.concat(supplierCorpId));
        String ticket = basicDataClientLoader.ctripTokenByCorpID(supplierCorpId, request.getSupplierCode());
        auth.setTicket(ticket);
        request.setAuth(auth);
        request.setOrderType(String.valueOf(ProductTypeEnum.hotel.getId()));
        String appSecurity = Md5Util
            .md5Hex(basicDataClientLoader.getAppSecurity(supplierCorpId, request.getSupplierCode()));
        request.setSignature(Md5Util.md5Hex(request.getOrderID()
            + request.getOrderType() + request.getAuditType() + appSecurity));
        CtripConfirmOrderResponse ctripConfirmOrderResponse = asyncHttpPostOriginResp(OrderMonitorService.ORDER_CONFIRM,
            request, CtripConfirmOrderResponse.class, HotelResponseCodeEnum.ORDER_CONFIRM_FAILED);
        if (!Optional.ofNullable(ctripConfirmOrderResponse).map(CtripConfirmOrderResponse::getStatus)
            .map(CtripConfirmOrderResponse.CtripResponseStatus::getSuccess).orElse(false)) {
            ctripConfirmOrderResponse = new CtripConfirmOrderResponse();
            ctripConfirmOrderResponse.setAuditType(SupplierConstant.AUDIT_TYPE_F);
        }
        return ctripConfirmOrderResponse;
    }

    /**
     * 携程订单修改问询
     *
     * @param request 携程修改问询参数
     * @return 携程修改问询返回参数
     */
    public CtripOrderModificationInquiryResponse queryOrderModification(CtripOrderModificationInquiryRequest request) {
        replaceSupplierUid(request.getOrderId(), request);

        return asyncHttpPostOriginResp(OrderMonitorService.QUERY_ORDER_MODIFICATION,
            request, CtripOrderModificationInquiryResponse.class,
            HotelResponseCodeEnum.QUERY_ORDER_MODIFICATION_FAILED);
    }

    /**
     * 携程订单可修改间夜问询
     *
     * @param request 要求
     * @return {@link CtripOrderModifiableRoomNightQueryResponse }
     */
    public CtripOrderModifiableRoomNightQueryResponse
        modifiableRoomNightQuery(CtripOrderModifiableRoomNightQueryRequest request) {
        replaceSupplierUid(request.getOrderId(), request);

        return asyncHttpPostOriginResp(OrderMonitorService.MODIFIABLE_ROOM_NIGHT_QUERY,
            request, CtripOrderModifiableRoomNightQueryResponse.class,
            HotelResponseCodeEnum.MODIFIABLE_ROOM_NIGHT_QUERY_FAILED);
    }

    /**
     * 携程订单修改申请
     *
     * @param request 订单修改申请
     * @return
     */
    public CtripOrderModificationResponse createOrderModification(CtripOrderModificationRequest request) {
        replaceSupplierUid(request.getOrderId(), request);

        return asyncHttpPostOriginResp(OrderMonitorService.CREATE_ORDER_MODIFICATION,
            request, CtripOrderModificationResponse.class, HotelResponseCodeEnum.CREATE_ORDER_MODIFICATION_FAILED);
    }

    /**
     * 携程订单修改详情
     *
     * @param request 订单修改详情请求
     * @return
     */
    public CtripOrderModificationDetailResponse orderModificationDetails(CtripOrderModificationDetailRequest request) {
        replaceSupplierUid(request.getOrderId(), request);

        return asyncHttpPostOriginResp(OrderMonitorService.ORDER_MODIFICATION_DETAILS,
            request, CtripOrderModificationDetailResponse.class,
            HotelResponseCodeEnum.ORDER_MODIFICATION_DETAILS_FAILED);
    }

    /**
     * 取消订单问询
     */
    public CtripCancelOrderInquiryResponse cancelOrderInquiry(CtripCancelOrderInquiryRequest request) {
        replaceSupplierUid(request.getOrderId(), request);

        CtripCancelOrderInquiryResponse response = asyncHttpPostOriginResp(OrderMonitorService.CANCEL_ORDER_QUERY,
            request, CtripCancelOrderInquiryResponse.class, HotelResponseCodeEnum.CANCEL_QUERY_FAILED);
        if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.CANCEL_QUERY_FAILED);
        }
        if (BooleanUtils.isFalse(response.getStatus().getSuccess())) {
            throw new CorpBusinessException(response.getStatus().getErrorCode(),
                response.getStatus().getErrorMessage());
        }
        return response;
    }

    /**
     * 取消订单
     */
    public CtripCancelOrderResponse cancelOrder(CtripCancelOrderRequest request) {
        replaceSupplierUid(request.getOrderId(), request);

        CtripCancelOrderResponse ctripCancelOrderResponse =
            asyncHttpPostOriginResp(OrderMonitorService.CANCEL_ORDER, request, CtripCancelOrderResponse.class,
                HotelResponseCodeEnum.HOTEL_CANCEL_FAILED);
        if (Objects.isNull(ctripCancelOrderResponse)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.HOTEL_CANCEL_FAILED);
        }
        // 异步取消直接返回, 异步取消isSuccess返回的false需要拿错误码判断
        if (SupplierConstant.CTRIP_SUPPLIER_ASYNC_CANCEL_ERROR_CODE.equals(ctripCancelOrderResponse.getErrorCode())) {
            return ctripCancelOrderResponse;
        }
        // 取消失败抛出异常
        if (Objects.isNull(ctripCancelOrderResponse.getIsSuccess()) || BooleanUtils.isFalse(ctripCancelOrderResponse.getIsSuccess())) {
            if (StringUtils.isBlank(ctripCancelOrderResponse.getErrorCode())
                || StringUtils.isBlank(ctripCancelOrderResponse.getReturnMessage())) {
                throw new CorpBusinessException(HotelResponseCodeEnum.HOTEL_CANCEL_FAILED);
            }
            throw new CorpBusinessException(Integer.parseInt(ctripCancelOrderResponse.getErrorCode()),
                ctripCancelOrderResponse.getReturnMessage());
        }
        return ctripCancelOrderResponse;
    }

    /**
     * 订单取消详情
     */
    public CtripCancelOrderDetailResponse cancelOrderDetail(CtripCancelOrderDetailRequest request) {
        replaceSupplierUid(request.getOrderId(), request);

        CtripCancelOrderDetailResponse ctripCancelOrderDetailResponse =
            asyncHttpPostOriginResp(OrderMonitorService.CANCEL_ORDER_DETAIL, request,
                CtripCancelOrderDetailResponse.class, HotelResponseCodeEnum.CANCEL_DETAIL_FAILED);
        if (Objects.isNull(ctripCancelOrderDetailResponse)
            || Objects.isNull(ctripCancelOrderDetailResponse.getStatus())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.CANCEL_DETAIL_FAILED);
        }
        if (BooleanUtils.isFalse(ctripCancelOrderDetailResponse.getStatus().getSuccess())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.CANCEL_DETAIL_FAILED);
        }
        return ctripCancelOrderDetailResponse;

    }

    private void replaceSupplierUid(String supplierOrderId, BaseExternalRequest req) {

        if (req == null || !isMultiIdentitiesSwitchOn()) {
            return;
        }

        HoOrder hoOrder = hoOrderLoader.selectBySupplierOrderId(supplierOrderId);
        if ( Objects.isNull(hoOrder) || StringUtils.isNotBlank(hoOrder.getSupplierUid())) {
            if (Objects.nonNull(hoOrder)) {
                req.setSupplierUid(hoOrder.getSupplierUid());
            }
            return;
        }

        String supplierUid = getSupplierUid(hoOrder.getUid(), hoOrder.getCorpId());
        if (StringUtils.isNotBlank(supplierUid)) {
            req.setSupplierUid(supplierUid);
        }
    }

    private String getSupplierUid(String uid, String corpId) {
        if (StringUtils.isBlank(uid) || StringUtils.isBlank(corpId)) {
            return null;
        }
        GetEmployeeOpenCardReq carReq = new GetEmployeeOpenCardReq();
        carReq.setUid(uid);
        carReq.setCorpId(corpId);
        GetEmployeeOpenCardRsp employeeOpenCardInfo = commonOrganizationDataloader.getEmployeeOpenCardInfo(carReq);
        if (employeeOpenCardInfo != null && StringUtils.isNotBlank(employeeOpenCardInfo.getSupplierUid())) {
            return  employeeOpenCardInfo.getSupplierUid();
        }
        return null;
    }

    private String replaceSupplierUid(String platformOrderId) {
        if (StringUtils.isBlank(platformOrderId) || !isMultiIdentitiesSwitchOn()) {
            return null;
        }

        log.info("replaceSupplierUid platformOrderId : {}", platformOrderId);

        HoOrder order = hoOrderLoader.selectByOrderId(Long.valueOf(platformOrderId));
        if (order == null) {
            log.warn("replaceSupplierUid order not found for orderId: {}", platformOrderId);
            return null;
        }
        // 如果 SupplierUid 已经存在，直接返回
        String existingSupplierUid = order.getSupplierUid();
        if (StringUtils.isNotBlank(existingSupplierUid)) {
            return existingSupplierUid;
        }

        return getSupplierUid(order.getUid(), order.getCorpId());
    }
}