package com.corpgovernment.hotel.product.external.constant;

/**
 * @author: pwang27
 * @Date: 2024/4/9 21:20
 * @Description:
 */
public class SupplierConstant {

    public static final String ADDITIONAL_SUPPLIER_CODE = "supplierCode";

    public static final String ADDITIONAL_AUTH_APP_KEY = "appKey";
    public static final String ADDITIONAL_AUTH_TICKET = "ticket";
    public static final String ADDITIONAL_SIGNATURE = "signature";
    public static final String ADDITIONAL_UID = "uid";
    public static final String STANDARD_SUPPLIER_CLIENT = "standardSupplierClient";
    public static final String SUPPLIER_CLIENT_ADAPTER = "SupplierClientAdapter";
    public static final String CTRIP_SUPPLIER_CLIENT_ADAPTER = "ctripSupplierClientAdapter";
    public static final String MEIYA_SUPPLIER_CLIENT_ADAPTER = "meiyaSupplierClientAdapter";

    public static final String SUCCESS_CODE_ZERO = "0";

    // 授权类型：T：同意授权
    public static final String AUDIT_TYPE_T = "T";

    // 授权类型 F：不同意授权
    public static final String AUDIT_TYPE_F = "F";

    // 携程商旅异步取消错误码
    public static final String CTRIP_SUPPLIER_ASYNC_CANCEL_ERROR_CODE = "22999900";

    public interface CancelStatus{
        /**
         * 取消成功
         */
        Integer CANCEL_STATUS_CANCEL_SUCCESS = 0;
        /**
         * 取消失败
         */
        Integer CANCEL_STATUS_CANCEL_FAILED = 1;
        /**
         * 取消中
         */
        Integer CANCEL_STATUS_CANCELLING = 2;
    }

    public interface Supplier {
        String CTRIP = "ctrip";
        String MEIYA = "meiya";
    }

    public interface ReserveOrder {

        String REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_COMPANY_ID = "%sCompanyId";
        String REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_COMPANY_NAME = "%sCompanyName";
        String REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_ORG_ID = "%sOrgId";
        String REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_ORG_NAME = "%sOrgName";
        String REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_IS_SEND_SMS = "%sisSendSms";
        String REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_EARN_POINTS = "%sEarnPoints";
        String REQUEST_ADDITIONAL_MAP_KEY_ROOM_ID = "roomId";
        String REQUEST_ADDITIONAL_MAP_KEY_HOTEL_ID = "hotelId";
        String REQUEST_ADDITIONAL_MAP_KEY_CITY_ID = "cityId";
        String REQUEST_ADDITIONAL_MAP_KEY_REMARK_INFO = "remarkInfo";
        String REQUEST_ADDITIONAL_MAP_KEY_MEMBER_CARD_NO = "memberCardNo";
        String REQUEST_ADDITIONAL_MAP_KEY_SEND_MSG = "sendMsg";
        String REQUEST_ADDITIONAL_MAP_KEY_CORP_NAME = "corpName";
    }

    public interface ConfirmOrder {
        String REQUEST_ADDITIONAL_MAP_KEY_UID = "uid";
    }

    public interface CancelOrderInquiry {
        String REQUEST_ADDITIONAL_MAP_KEY_SID = "sid";
        String REQUEST_ADDITIONAL_MAP_KEY_ORDER_ID = "orderId";
        String REQUEST_ADDITIONAL_MAP_KEY_ORDER_RESOURCE = "orderResource";
    }

    public interface  OrderModifyInquiry {
        String REQUEST_ADDITIONAL_MAP_KEY_ORDER_ID = "orderId";
        String REQUEST_ADDITIONAL_MAP_KEY_ORDER_RESOURCE = "orderResource";
    }

    public interface CancelOrder {
        String REQUEST_ADDITIONAL_MAP_KEY_UID = "uid";

        String REQUEST_ADDITIONAL_MAP_KEY_SID = "sid";

        String REQUEST_ADDITIONAL_MAP_KEY_ORDER_TYPE = "orderType";
        String RESPONSE_ADDITIONAL_MAP_KEY_IS_SUCCESS = "isSuccess";
        String RESPONSE_ADDITIONAL_MAP_KEY_RETURN_MESSAGE = "returnMessage";
        String RESPONSE_ADDITIONAL_MAP_KEY_RESPONSE_STATUS = "responseStatus";
        String RESPONSE_ADDITIONAL_MAP_KEY_ERROR_CODE = "errorCode";
        String RESPONSE_ADDITIONAL_MAP_KEY_MESSAGE = "message";

    }

    public interface OrderDetail{
        String REQUEST_ADDITIONAL_MAP_KEY_PLATFORM_ORDER_ID = "platformOrderId";
        String RESPONSE_ADDITIONAL_MAP_KEY_HOTEL_NAME = "hotelName";
        String RESPONSE_ADDITIONAL_MAP_KEY_CITY_ID = "cityId";
        String RESPONSE_ADDITIONAL_MAP_KEY_CITY_NAME = "cityName";
        String RESPONSE_ADDITIONAL_MAP_KEY_HOTEL_ADDRESS = "hotelAddress";
        String RESPONSE_ADDITIONAL_MAP_KEY_HOTEL_PHONE = "hotelPhone";
        String RESPONSE_ADDITIONAL_MAP_KEY_CONTACT_NAME = "contactName";
        String RESPONSE_ADDITIONAL_MAP_KEY_CONTACT_PHONE = "contactPhone";
        String RESPONSE_ADDITIONAL_MAP_KEY_CHECK_IN_DATE = "checkInDate";
        String RESPONSE_ADDITIONAL_MAP_KEY_CHECK_OUT_DATE = "checkOutDate";
        String RESPONSE_ADDITIONAL_MAP_KEY_VAT_FLAG = "vatFlag";
        String RESPONSE_ADDITIONAL_MAP_KEY_HOTEL_TYPE_CODE = "hotelTypeCode";
        String RESPONSE_ADDITIONAL_MAP_KEY_SERVICE_FEE = "serviceFee";
        String RESPONSE_ADDITIONAL_MAP_KEY_POST_SERVICE_FEE = "postServiceFee";
        String RESPONSE_ADDITIONAL_MAP_KEY_DELIVERY_FEE= "deliveryFee";
        String RESPONSE_ADDITIONAL_MAP_KEY_ADDED_FEES= "AddedFees";
        String RESPONSE_ADDITIONAL_MAP_KEY_SUBCRITION_FEE= "subcriptionFee";
        String RESPONSE_ADDITIONAL_MAP_KEY_CLIENT_MOBILE_PHONE = "clientMobilePhone_{0}";
        String RESPONSE_ADDITIONAL_MAP_KEY_CTRIP_ROOM_INFO_LIST = "roomInfoList";
    }
}
