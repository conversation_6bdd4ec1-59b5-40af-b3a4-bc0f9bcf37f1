package com.corpgovernment.hotel.product.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.annotation.Resource;

import com.corpgovernment.hotel.product.dto.SupplierStarBO;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import com.corpgovernment.api.applytrip.apply.CalculateTravelFee;
import com.corpgovernment.api.applytrip.apply.sync.StandardAmountSyncRequest;
import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficType;
import com.corpgovernment.api.applytrip.soa.response.ApplyTripTrafficVerifyResponse;
import com.corpgovernment.api.applytrip.soa.response.QueryApplyTripStandardResponse;
import com.corpgovernment.api.basic.bo.StaticMapListRequestBo;
import com.corpgovernment.api.basic.bo.StaticMapRequestBo;
import com.corpgovernment.api.basic.enums.HotelRequestTypeEnum;
import com.corpgovernment.api.basic.response.StaticMapResponse;
import com.corpgovernment.api.hotel.booking.hotel.request.Room;
import com.corpgovernment.api.hotel.product.model.request.CalcFeeRequest;
import com.corpgovernment.api.hotel.product.model.request.CalcFeeRequest.Trip;
import com.corpgovernment.api.hotel.product.model.request.HotelMappingRequest;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelDetailRequestBo;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelListRequestBo;
import com.corpgovernment.api.hotel.product.model.request.Tree;
import com.corpgovernment.api.hotel.product.model.response.ExceedApplyTemp;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO;
import com.corpgovernment.api.hotel.product.model.response.HotelMappingResponse;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo.HotelListBean;
import com.corpgovernment.api.hotel.product.model.response.LocalMappingHotelListResponseBo;
import com.corpgovernment.api.hotel.product.model.response.OffLineHotel;
import com.corpgovernment.api.hotel.product.model.response.SearchHotelListResponseVO;
import com.corpgovernment.api.hotel.product.model.saveorder.request.SaveOrderRequestBo;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoRequest;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoResponse;
import com.corpgovernment.api.organization.soa.resident.GetAllDifferentialPriceRequest;
import com.corpgovernment.api.supplier.bo.suppliercompany.InitResponseBo;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.vo.AveragePriceSet;
import com.corpgovernment.api.travelstandard.vo.AverageStarSet;
import com.corpgovernment.api.travelstandard.vo.ExceedReasonVO;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.api.travelstandard.vo.HotelRcValue;
import com.corpgovernment.api.travelstandard.vo.ReasonInfo;
import com.corpgovernment.api.travelstandard.vo.request.GetHotelDetailRequest;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.convert.HotelQueryConvert;
import com.corpgovernment.basic.impl.HotelBasicDataService;
import com.corpgovernment.basicdata.bo.HotelCityBo;
import com.corpgovernment.basicdata.entity.db.BdHpHotelCityEntity;
import com.corpgovernment.basicdata.mapper.HpHotelCityMapper;
import com.corpgovernment.basicdata.utils.CommonUtil;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.AbstractBaseService;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.enums.TransportEnum;
import com.corpgovernment.common.shunt.ShuntConfigDao;
import com.corpgovernment.common.utils.BaseUtils;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.common.utils.ListUtils;
import com.corpgovernment.common.utils.LogSplicingUtils;
import com.corpgovernment.common.utils.Md5Util;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.hotel.booking.bo.ApplyTripItemBo;
import com.corpgovernment.hotel.booking.cache.OrderInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.SupplierHotelModel;
import com.corpgovernment.hotel.booking.enums.RoomTypeEnum;
import com.corpgovernment.hotel.booking.enums.TravelUnLimitedTypeEnum;
import com.corpgovernment.hotel.booking.service.ApplyTripService;
import com.corpgovernment.hotel.booking.service.TokenAccessService;
import com.corpgovernment.hotel.booking.service.TravelStandardService;
import com.corpgovernment.hotel.booking.vo.CheckHotelCityRequestVO;
import com.corpgovernment.hotel.booking.vo.CheckHotelCityResponseVO;
import com.corpgovernment.hotel.product.cache.HotelListCacheManager;
import com.corpgovernment.hotel.product.dataloader.db.HoHotelLoader;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CommonSupplierLoader;
import com.corpgovernment.hotel.product.dataloader.soa.OrganizationClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierCompanyClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SwitchClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.TravelStandardPostClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoHotel;
import com.corpgovernment.hotel.product.entity.db.HotelRoomRelationDo;
import com.corpgovernment.hotel.product.mapper.HotelRoomRelationMapper;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.corpgovernment.hotel.product.supplier.enums.SupplierEnum;
import com.corpgovernment.mapping.mapper.HpResultHotelMapper;
import com.corpgovernment.mapping.processor.CheckHotelMappingProcessor;
import com.corpgovernment.mapping.service.StaticMapResultService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.DateUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.ObjectUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Service
@Slf4j
public class HoHotelService extends AbstractBaseService {
	private static final String HOTEL_REASONINFO = "根据贵公司差旅政策规定，继续预订您需要提供未预订差标内房型的原因:";

	@Autowired
	private HoHotelLoader hoHotelLoader;
	@Autowired
	private TravelStandardPostClientLoader travelStandardPostClientLoader;
	@Resource
	private HpResultHotelMapper hpResultHotelMapper;
	@Autowired
	private HotelListCacheManager hotelListCacheManager;
	@Autowired
	private CheckHotelMappingProcessor checkHotelMappingProcessor;
	@Autowired
	private HotelOperatorTypeConfig hotelOperatorTypeConfig;
	@Autowired
	private CommonService commonService;
	@Autowired
	private HotelBasicDataService hotelBasicDataService;
	@Autowired
	private StaticMapResultService mapResultService;
	@Autowired
	private SwitchClientLoader switchClientLoader;
	@Autowired
	@Qualifier("queryThreadPoolExecutor")
	private ThreadPoolExecutor queryThreadPoolExecutor;
	@Autowired
	private HotelApollo hotelApollo;
	@Autowired
	private CommonSupplierLoader commonSupplierLoader;
	@Autowired
	private OrganizationClientLoader organizationClientLoader;
	@Autowired
	private HotelQueryConvert hotelQueryConvert;
	@Autowired
	private OrderInfoCacheManager orderInfoCacheManager;
	@Autowired
	private ApplyTripClientLoader applyTripClientLoader;
	@Resource
	private HotelRoomRelationMapper hotelRoomRelationMapper;
	@Autowired
	private HpHotelCityMapper hotelCityMapper;
	@Autowired
	private TravelStandardService travelStandardService;
	@Autowired
	private SupplierCompanyClientLoader supplierCompanyClientLoader;
	@Autowired
	private ShuntConfigDao shuntConfigDao;
	@Autowired
	private ApplyTripService applyTripService;
    @Autowired
    private TokenAccessService tokenAccessService;
	private final static String LOCATION_DESC = "{0}&{1}&{2}&{3}";
	private final static Integer VERIFY_CODE = 2;

    /**
	 * 获取酒店列表
	 */
	public SearchHotelListResponseVO page(LocalHotelListRequestBo request) {
		initElkLog();
		addElkInfoLog("酒店列表查询");
		try {
			long start = System.currentTimeMillis();
			if (!request.getRcpolicy()) {
				addElkInfoLog("不走差标管控");
				return getDataFromCache(request);
			}
			HotelControlVo priceLevel = this.getPriceLevel(request);
			if (priceLevel == null) {
				addElkInfoLog("不走价格管控");
				return getDataFromCache(request);
			}
			AveragePriceSet averagePriceSet = priceLevel.getAveragePriceSet();
			addElkInfoLog("HoHotelService.page 价格管控：%s", JsonUtils.toJsonString(averagePriceSet));
			if (averagePriceSet != null) {
				BigDecimal minPrice = request.getMinPrice();
				BigDecimal maxPrice = request.getMaxPrice();
				BigDecimal priceCeiling = new BigDecimal(averagePriceSet.getPriceCeiling());
				BigDecimal priceFloor = new BigDecimal(averagePriceSet.getPriceFloor());
				if (minPrice == null) {
					request.setMinPrice(priceFloor);
				}
				if (maxPrice == null) {
					request.setMaxPrice(priceCeiling);
				}
				// 最低价大于均价上限，价格超标直接返回
				if (request.getMinPrice().compareTo(priceCeiling) > 0) {
					return new SearchHotelListResponseVO(request.getPageNum(), request.getPageSize(), true, Lists.newArrayList(), 0);
				}
				// 均价上限在最低价和最高价之间
				if (request.getMinPrice().compareTo(priceCeiling) < 0 && priceCeiling.compareTo(request.getMaxPrice()) < 0) {
					request.setMaxPrice(priceCeiling);
				}
			}
			AverageStarSet averageStarSet = priceLevel.getAverageStarSet();
			addElkInfoLog("HoHotelService.page 星级管控：%s", JsonUtils.toJsonString(averageStarSet));
			if (averageStarSet != null) {
				// 星级管控
				int maxStarCeiling = Integer.parseInt(averageStarSet.getStarCeiling().getValue());
				if (StringUtils.isEmpty(request.getStarList())) {
					String starList = IntStream.rangeClosed(1, maxStarCeiling).boxed().map(Objects::toString).collect(Collectors.joining(","));
					request.setStarList(starList);
				} else {
					String[] stars = StringUtils.split(request.getStarList(), ",");
					if (ArrayUtils.isNotEmpty(stars)) {
						int length = stars.length;
						int maxStar = Integer.parseInt(stars[length - 1]);
						int minStar = Integer.parseInt(stars[0]);
						if (maxStar > maxStarCeiling) {
							String starList = IntStream.rangeClosed(minStar, maxStarCeiling).boxed().map(Objects::toString).collect(Collectors.joining(","));
							request.setStarList(starList);
						}
						// 星级超标，直接返回
						if (minStar > maxStarCeiling) {
							return new SearchHotelListResponseVO(request.getPageNum(), request.getPageSize(), true, Lists.newArrayList(), 0);
						}
					}
				}
			}
			addElkInfoLog("page init 耗时(毫秒)：%s" + (System.currentTimeMillis() - start));
			return this.getDataFromCache(request);
		} finally {
			log.info("HoHotelService.page酒店列表查询{} request：{}{}{}{}", System.lineSeparator(), JsonUtils.toJsonString(request), System.lineSeparator(), System.lineSeparator(), this.getElkInfoLog());
			clearElkLog();
		}
	}

	@SneakyThrows
	private SearchHotelListResponseVO getDataFromCache(LocalHotelListRequestBo requestBo) {
		addElkInfoLog("列表查询requestBo：%s", JsonUtils.toJsonString(requestBo));
		long start = System.currentTimeMillis();
		String redisKey = this.getHotelListRequestMd5(requestBo);
		Map<String, SupplierHotelModel> cacheHotelMap = hotelListCacheManager.getHotelMap(redisKey);
		List<LocalMappingHotelListResponseBo> hotelMappingList = this.getStaticHotelInfo(cacheHotelMap);
		int minHotelSize = requestBo.getPageNum() * requestBo.getPageSize();
		// mapping后数据少于需要的酒店数量，加载数据
		if (hotelMappingList.size() <= minHotelSize) {
			addElkInfoLog("mapping后数据少于需要的酒店数量，加载数据");
			this.loadNewData(redisKey, requestBo, cacheHotelMap);
			hotelMappingList = this.getStaticHotelInfo(cacheHotelMap);
		}
		addElkInfoLog("hotelMappingList size: " + hotelMappingList.size());
		SearchHotelListResponseVO result = new SearchHotelListResponseVO();
		result.setPageSize(requestBo.getPageSize());
		result.setCurrentPage(requestBo.getPageNum());
		int totalCount = cacheHotelMap.values().stream().mapToInt(SupplierHotelModel::getTotalCount).max().orElse(0);
		result.setTotalCount(totalCount);
		//截取的开始位置
		int pageStart = (requestBo.getPageNum() - 1) * requestBo.getPageSize();
		if (hotelMappingList.size() <= pageStart) {
			addElkInfoLog("当前页无数据：" + requestBo.getPageNum());
			result.setLastPage(true);
			result.setHotelList(Lists.newArrayList());
			return result;
		}
		//排序
		hotelMappingList = this.sortHotelList(requestBo, hotelMappingList, cacheHotelMap);
		if (hotelMappingList.size() <= minHotelSize) {
			addElkInfoLog("当前为最后一页：" + requestBo.getPageNum());
			result.setLastPage(true);
		}
		//截取的结束位置
		int pageEnd = Math.min(hotelMappingList.size(), requestBo.getPageNum() * requestBo.getPageSize());
		List<LocalMappingHotelListResponseBo> subList = hotelMappingList.subList(pageStart, pageEnd);
		List<LocalHotelListResponseBo.HotelListBean> hotelListBeans = subList.stream().map(h -> {
			LocalHotelListResponseBo.HotelListBean data = h.getMultiHotelInfo().get(0);
			data.setTree(h.getTree());
			data.setPrice(h.getPrice());
			return data;
		}).collect(Collectors.toList());
		result.setHotelList(hotelListBeans);
		//异步加载下一页数据
		final List<LocalMappingHotelListResponseBo> list = hotelMappingList;
		int sum = cacheHotelMap.values().stream().mapToInt(s -> s.getHotelList().size()).sum();
		boolean needLoad = sum < 100 || list.size() <= (requestBo.getPageNum() + 1) * requestBo.getPageSize();
		if (needLoad) {
			CompletableFuture.runAsync(() -> {
				addElkInfoLog("提前异步加载数据");
				this.loadNewData(redisKey, requestBo, cacheHotelMap);
			}, queryThreadPoolExecutor);
		}
		addElkInfoLog("getDataFromCache 耗时(毫秒)：" + (System.currentTimeMillis() - start));
		return result;
	}

    /**
     * 通过md5判断是不是相同请求
     */
    public String getHotelListRequestMd5(LocalHotelListRequestBo request) {
		LocalHotelListRequestBo data = JsonUtils.convert(request, LocalHotelListRequestBo.class);
		data.setPageNum(null);
		data.setPageSize(null);
		String result = Md5Util.md5Hex(JsonUtils.toJsonString(data));
		addElkInfoLog("getHotelListRequestMd5:" + result);
		return result;
	}

    /**
     * 加载数据
     */
    @SneakyThrows
    private Map<String, SupplierHotelModel> loadNewData(String redisKey, LocalHotelListRequestBo requestBo, Map<String, SupplierHotelModel> cacheHotelMap) {
		long start = System.currentTimeMillis();
		boolean firstRequest = cacheHotelMap.isEmpty();
		if (firstRequest) {
			initCacheHotelMap(requestBo, cacheHotelMap);
		}
		List<Future<LocalHotelListResponseBo>> futures = Lists.newArrayList();
		cacheHotelMap.values().forEach(supplierData -> {
			if (!supplierData.hasData()) {
				addElkInfoLog(supplierData.getSupplierCode() + "全量数据已经都加载完毕，不再请求");
				return;
			}
			LocalHotelListRequestBo supplierRequest = supplierData.getRequest();
			supplierRequest.setPageNum(supplierData.getCurrentPage() + 1);
			supplierRequest.setPageSize(requestBo.getPageSize() + 1);
			CompletableFuture<LocalHotelListResponseBo> future = CompletableFuture.supplyAsync(() -> commonSupplierLoader.page(supplierRequest, supplierData.getSupplierProduct(), supplierData.getBasicCity(), false), queryThreadPoolExecutor)
					.handle((localHotelListResponseBo, throwable) -> {
						if (throwable != null) {
							addElkInfoLog(supplierData.getSupplierCode() + "请求供应商异常");
							return null;
						}
						return localHotelListResponseBo;
					});
			futures.add(future);
		});
		if (futures.size() == 0) {
			addElkInfoLog("全量数据已经都加载完毕，不再请求");
			return cacheHotelMap;
		}
		// wait all future done
		Long timeOut = hotelApollo.getSupplierTimeOut();
		Map<String, LocalHotelListResponseBo> responseBos = BaseUtils.getFuture("酒店列表查询", timeOut, futures).stream().filter(Objects::nonNull).
				collect(Collectors.toMap(LocalHotelListResponseBo::getSupplier, Function.identity(), (o, n) -> n));
		cacheHotelMap.values().forEach(h -> {
			LocalHotelListResponseBo newData = responseBos.get(h.getSupplierCode());
			if (newData == null || CollectionUtils.isEmpty(newData.getHotelList())) {
				addElkInfoLog("查询结果为空");
				if (firstRequest) {
					h.setTotalCount(0);
				}
				return;
			}
			addElkInfoLog(h.getSupplierCode() + "数据总条数：" + newData.getTotalCount());
			h.getHotelList().addAll(newData.getHotelList());
			h.setTotalCount(newData.getTotalCount());
			h.setPageSize(newData.getPageSize());
			h.setCurrentPage(newData.getCurrentPage());
		});
		//异步存缓存
		CompletableFuture.runAsync(() -> {
			hotelListCacheManager.cacheSupplierHotelMap(redisKey, cacheHotelMap);
		}, queryThreadPoolExecutor);
		addElkInfoLog("loadNewData 耗时(毫秒)：" + (System.currentTimeMillis() - start));
		return cacheHotelMap;
    }

    /**
     * 相同参数第一次请求，查询服务商等信息
     */
    private void initCacheHotelMap(LocalHotelListRequestBo requestBo, Map<String, SupplierHotelModel> cacheHotelMap) {
		addElkInfoLog("相同参数第一次请求，初始化服务商等信息");
		// 获取服务商列表
		List<SupplierProductBo> suppliers = commonService.listSupplierProduct(requestBo.getBaseCorpId(), hotelOperatorTypeConfig.getGetBusinessHotelList(), requestBo.getCorpPayType());
		if (CollectionUtils.isEmpty(suppliers)) {
			throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUPPLIER_INFORMATION);
		}
		addElkInfoLog("返回供应商数据为：%s", JsonUtils.toJsonString(suppliers));
		HotelCityBo hotelCityBo = hotelBasicDataService.getCityInfo(requestBo.getCityCode());
		List<String> supplierCodes = suppliers.stream().map(SupplierProductBo::getSupplierCode).collect(Collectors.toList());
		// 解析城市
		Map<String, String> supplierCities = this.getSupplierCityMap(supplierCodes, requestBo.getCityCode());
		// 解析key
		Map<String, String> analysisKey = this.analysisKey(supplierCodes, requestBo);
		addElkInfoLog("解析key结果:" + JsonUtils.toJsonString(analysisKey));
		Map<String, List<String>> analysisCategory = this.analysisCategory(requestBo.getCityCode(), supplierCodes, requestBo.getCatagory());
		addElkInfoLog("解析Category结果:" + JsonUtils.toJsonString(analysisCategory));
		Map<String, SupplierHotelModel> map = suppliers.stream().map(supplier -> {
			SupplierHotelModel data = new SupplierHotelModel();
			data.setSupplierCode(supplier.getSupplierCode());
			data.setSupplierProduct(supplier);
			data.setBasicCity(hotelCityBo);
			// 获取供应商对应的城市id
			String supplierCity = supplierCities.get(supplier.getSupplierCode());
			if (StringUtils.isBlank(supplierCity)) {
				addElkInfoLog(supplier.getSupplierCode() + "未匹配到供应商城市：" + requestBo.getCityCode());
				data.setRequest(requestBo);
				return data;
			}
			if (StringUtils.isNotBlank(requestBo.getKey()) && !analysisKey.containsKey(supplier.getSupplierCode())) {
				addElkInfoLog(supplier.getSupplierCode() + "静态数据map失败，不进行请求");
				data.setRequest(requestBo);
				return data;
			}
			// 解析请求中的key,适配不同供应商入参静态数据
			LocalHotelListRequestBo supplierListRequest = JsonUtils.convert(requestBo, LocalHotelListRequestBo.class);
			supplierListRequest.setSupplierCityId(supplierCity);
			supplierListRequest.setKey(requestBo.getKey());
			supplierListRequest.setCatagory(analysisCategory.get(supplier.getSupplierCode()));
			if (CollectionUtils.isEmpty(supplierListRequest.getCatagory()) && CollectionUtils.isNotEmpty(requestBo.getCatagory())) {
				addElkInfoLog(supplier.getSupplierCode() + "Category静态数据map失败，不进行请求");
				data.setRequest(requestBo);
				return data;
			}
			// 获取运营后台被屏蔽的酒店
			List<OffLineHotel> offLineHotels = hpResultHotelMapper.selectOffLine(supplier.getSupplierCode());
			List<String> offlines = offLineHotels.stream().map(OffLineHotel::getHotelId).distinct().collect(Collectors.toList());
			supplierListRequest.setHiddenHotelList(offlines);
			data.setRequest(supplierListRequest);
			return data;
		}).collect(Collectors.toMap(SupplierHotelModel::getSupplierCode, Function.identity(), (o, n) -> n));
		cacheHotelMap.putAll(map);
		addElkInfoLog("酒店searchList返回待查询数据为：%s", JsonUtils.toJsonString(cacheHotelMap));
	}

	/**
	 * 获取供应商城市id
	 */
	private Map<String, String> getSupplierCityMap(List<String> suppliers, String cityId) {
		StaticMapRequestBo supplierCityRequest = new StaticMapRequestBo();
		supplierCityRequest.setCityId(cityId);
		supplierCityRequest.setId(cityId);
		supplierCityRequest.setTypeEnum(HotelRequestTypeEnum.CITY);
		supplierCityRequest.setWantedSuppliers(suppliers);
		StaticMapResponse cityResp = mapResultService.mapResponse(supplierCityRequest);
		if (cityResp == null || cityResp.getResult() == null) {
			return Maps.newHashMap();
		}
		return cityResp.getResult();
	}

	/**
	 * 供应商基础数据mapping
	 */
	private Map<String, String> analysisKey(List<String> supplierCodeList, LocalHotelListRequestBo requestBo) {
		Map<String, String> result = Maps.newHashMap();
		if (StringUtils.isEmpty(requestBo.getKey())) {
			//没有需要解析的key
			return result;
		}
		//解析key
		String key = requestBo.getKey();
		String[] strs = StringUtils.split(key, "&");
		String destinationId = strs[0];
		String destinationType = strs[1];
		String lon = strs[2];
		String lat = strs[3];
		HotelRequestTypeEnum typeEnum = HotelRequestTypeEnum.get(destinationType);
		List<String> wantedSuppliers = Lists.newArrayList();
		StaticMapRequestBo request = new StaticMapRequestBo();

		supplierCodeList.forEach(supplierCode -> {
			if (Boolean.TRUE.equals(requestBo.getFromKeywordSearch())) {
				// 模糊查询，则取模糊查询接口的供应商code
				String srcSupplierCode = hotelApollo.getSearchLocationSupplierCode();
				request.setSupplier(srcSupplierCode);
				if (supplierCode.equals(srcSupplierCode)) {
					// 当前供应商code==模糊查询接口的供应商code 无需mapping
					addElkInfoLog("当前供应商code==模糊查询接口的供应商code，无需对key进行mapping处理");
					result.put(supplierCode, key);
					return;
				}
			}
			if (HotelRequestTypeEnum.PERSON.equals(typeEnum)) {
				addElkInfoLog("PERSON  不走mapping");
				result.put(supplierCode, key);
				return;
			}
			wantedSuppliers.add(supplierCode);
		});
		if (CollectionUtils.isEmpty(wantedSuppliers)) {
			addElkInfoLog("无需对Key进行mapping处理，key：%s", key);
			return result;
		}
		//解析key
		request.setTypeEnum(typeEnum);
		request.setCityId(requestBo.getCityCode());
		request.setWantedSuppliers(wantedSuppliers);
		request.setId(destinationId);
		request.setSupplier("ctrip");
		addElkInfoLog("StaticMapRequest：%s", JsonUtils.toJsonString(request));
		StaticMapResponse response = mapResultService.mapResponse(request);
		addElkInfoLog("StaticMapResponse：%s", JsonUtils.toJsonString(response));
		if (Optional.ofNullable(response).map(StaticMapResponse::getResult).isPresent()) {
			wantedSuppliers.forEach(supplier -> {
				String id = response.getResult().get(supplier);
				if (StringUtils.isBlank(id)) {
					addElkInfoLog(supplier + "供应商静态条件数据获取失败");
					return;
				}
				result.put(supplier, MessageFormat.format(LOCATION_DESC, id, destinationType, lon, lat));
			});
		} else {
			addElkInfoLog("供应商静态条件数据获取失败");
		}
		return result;
	}

	/**
	 * 供应商基础数据mapping
	 */
	private Map<String, List<String>> analysisCategory(String cityId, List<String> suppliers, List<String> category) {
		Map<String, List<String>> result = Maps.newHashMap();
		if (CollectionUtils.isEmpty(category)) {
			//没有需要解析的key
			return result;
		}
		suppliers.forEach(supplier -> {
			result.put(supplier, Lists.newArrayList());
		});
		StaticMapListRequestBo listRequest = new StaticMapListRequestBo();
		List<StaticMapRequestBo> list = category.stream().map(key -> {
			String[] strs = StringUtils.split(key, "&");
			String destinationId = strs[0];
			String destinationType = strs[1];
			HotelRequestTypeEnum typeEnum = HotelRequestTypeEnum.get(destinationType);
			StaticMapRequestBo request = new StaticMapRequestBo();
			//解析key
			request.setTypeEnum(typeEnum);
			request.setCityId(cityId);
			request.setWantedSuppliers(suppliers);
			request.setId(destinationId);
			return request;
		}).collect(Collectors.toList());
		listRequest.setList(list);
		addElkInfoLog("StaticMapListRequest：%s", JsonUtils.toJsonString(listRequest));
		List<StaticMapResponse> listResponse = mapResultService.mapResponseList(listRequest);
		addElkInfoLog("StaticMapResponse list：%s", JsonUtils.toJsonString(listResponse));
		if (CollectionUtils.isEmpty(listResponse)) {
			log.error("供应商静态条件数据获取失败");
			return result;
		}
		for (int i = 0; i < listResponse.size(); i++) {
			StaticMapResponse response = listResponse.get(i);
			String key = category.get(i);
			String[] strs = StringUtils.split(key, "&");
			String destinationType = strs[1];
			String lon = strs[2];
			String lat = strs[3];
			suppliers.forEach(supplier -> {
				String id = response.getResult().get(supplier);
				if (StringUtils.isBlank(id)) {
					addElkInfoLog(supplier + "供应商静态条件数据获取失败");
					return;
				}
				result.get(supplier).add(MessageFormat.format(LOCATION_DESC, id, destinationType, lon, lat));
			});
		}
		return result;
	}

	/**
	 * 多服务商排序合并
	 */
	private List<LocalMappingHotelListResponseBo> sortHotelList(LocalHotelListRequestBo requestBo, List<LocalMappingHotelListResponseBo> hotelMappingList,
																Map<String, SupplierHotelModel> cacheHotelMap) {
		long start = System.currentTimeMillis();
		if (CollectionUtils.isEmpty(hotelMappingList)) {
			return hotelMappingList;
		}
		//查服务商优先级配置
		List<String> supplierCodes = commonService.getSupplierSort(requestBo.getOrgId(), requestBo.getCorpPayType());
		if (CollectionUtils.isEmpty(supplierCodes)) {
			supplierCodes.addAll(cacheHotelMap.keySet());
		}
		int sort = Optional.ofNullable(requestBo.getRecommendSort()).orElse(0);
		if (sort == 1) {
			log.info("价格从低到高");
			hotelMappingList.sort((h1, h2) -> {
				Integer result = h1.getMultiHotelInfo().get(0).getPrice().get(0).getPrice().compareTo(h2.getMultiHotelInfo().get(0).getPrice().get(0).getPrice());
				if (result == 0) {
					return supplierCompare(h1.getMultiHotelInfo().get(0).getSupplier(), h2.getMultiHotelInfo().get(0).getSupplier(), supplierCodes);
				}
				return result;
			});
			return hotelMappingList;
		}
		if (sort == 2) {
			log.info("价格从高到低");
			hotelMappingList.sort((h1, h2) -> {
				Integer result = h2.getMultiHotelInfo().get(0).getPrice().get(0).getPrice().compareTo(h1.getMultiHotelInfo().get(0).getPrice().get(0).getPrice());
				if (result == 0) {
					return supplierCompare(h2.getMultiHotelInfo().get(0).getSupplier(), h1.getMultiHotelInfo().get(0).getSupplier(), supplierCodes);
				}
				return result;
			});
			return hotelMappingList;
		}
		if (sort == 3) {
			log.info("距离由近到远");
			hotelMappingList.sort((h1, h2) -> {
				if (h2.getMultiHotelInfo().get(0).getDistance() == null) {
					return -1;
				}
				if (h1.getMultiHotelInfo().get(0).getDistance() == null) {
					return 1;
				}
				Integer result = h1.getMultiHotelInfo().get(0).getDistance().compareTo(h2.getMultiHotelInfo().get(0).getDistance());
				if (result == 0) {
					return supplierCompare(h1.getMultiHotelInfo().get(0).getSupplier(), h2.getMultiHotelInfo().get(0).getSupplier(), supplierCodes);
				}
				return result;
			});
			return hotelMappingList;
		}
		if (sort == 4) {
			log.info("距离由远到近");
			hotelMappingList.sort((h1, h2) -> {
				if (h1.getMultiHotelInfo().get(0).getDistance() == null) {
					return -1;
				}
				if (h2.getMultiHotelInfo().get(0).getDistance() == null) {
					return 1;
				}
				Integer result = h2.getMultiHotelInfo().get(0).getDistance().compareTo(h1.getMultiHotelInfo().get(0).getDistance());
				if (result == 0) {
					return supplierCompare(h2.getMultiHotelInfo().get(0).getSupplier(), h1.getMultiHotelInfo().get(0).getSupplier(), supplierCodes);
				}
				return result;
			});
			return hotelMappingList;
		}
		log.info("推荐排序,按服务商分组");
		List<LocalMappingHotelListResponseBo> result = Lists.newArrayList();
		Map<String, LocalMappingHotelListResponseBo> map = hotelMappingList.stream().collect(Collectors.toMap(h -> {
			Tree.Kv kv = h.getTree().getKvs().get(0);
			return CommonUtil.getKey(kv.getSupplier(), kv.getHotelId());
		}, Function.identity(), (o, n) -> o));
		Set<String> hotelIds = Sets.newHashSet();
		Map<String, Integer> supplierIndex = supplierCodes.stream().collect(Collectors.toMap(x -> x, x -> 0, (o, n) -> o));
		for (int i = 0; i < hotelMappingList.size(); i++) {
			supplierCodes.forEach(s -> {
				SupplierHotelModel supplierHotelModel = cacheHotelMap.get(s);
				if (supplierHotelModel == null || CollectionUtils.isEmpty(supplierHotelModel.getHotelList())) {
					return;
				}
				Integer index = supplierIndex.get(s);
				LocalHotelListResponseBo.HotelListBean hotelListBean = null;
				String key = null;
				while (index < supplierHotelModel.getHotelList().size()) {
					hotelListBean = supplierHotelModel.getHotelList().get(index);
					index++;
					key = CommonUtil.getKey(s, hotelListBean.getKey());
					if (!hotelIds.contains(key)) {
						break;
					}
				}
				supplierIndex.put(s, index);
				if (hotelListBean == null) {
					return;
				}
				LocalMappingHotelListResponseBo mappingData = map.get(key);
				Optional.ofNullable(mappingData).ifPresent(d -> {
					d.getTree().getKvs().forEach(k -> {
						hotelIds.add(CommonUtil.getKey(k.getSupplier(), k.getHotelId()));
					});
					result.add(d);
				});
			});
		}
		return result;
	}

	public CheckHotelCityResponseVO checkHotelCity(CheckHotelCityRequestVO request) {
		Example example = new Example(BdHpHotelCityEntity.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("cityName", request.getCityName());
		example.orderBy("datachangeLasttime").desc();
		List<BdHpHotelCityEntity> cityEntities = hotelCityMapper.selectByExample(example);
		CheckHotelCityResponseVO responseVO = new CheckHotelCityResponseVO();
		if (CollectionUtils.isEmpty(cityEntities)) {
			// 前端约定查询不到城市id时返回-1
			responseVO.setCityId("-1");
			responseVO.setCitySupportSearch(Boolean.FALSE);
			return responseVO;
		}
		String searchCityId = cityEntities.get(0).getCityId();
		responseVO.setCityId(searchCityId);
        // 行程id为空，不关联申请单时
		if (Objects.isNull(request.getTrafficId())) {
			responseVO.setCitySupportSearch(Boolean.TRUE);
			return responseVO;
		}

		// 关联申请单时 默认不通过，包含城市才通过
		Boolean citySupportSearch = Boolean.FALSE;
		//获取模板
		ApplyTripTrafficVerifyResponse applyTemp = applyTripClientLoader.getApplyTrafficVerify(request.getTrafficId());
		//酒店入住城市是否校验
		if (applyTemp != null && applyTemp.getApplyTripTraffic() != null
				&& Objects.equals(applyTemp.getApplyTripTraffic().getTrafficType(), EApplyTripTrafficType.TRAFFIC.getValue())) {
			// 事由管控不判断模板
			citySupportSearch = Objects.equals(applyTemp.getApplyTripTraffic().getEndCityCode(), searchCityId) ? Boolean.TRUE :
					Boolean.FALSE;
		} else if(applyTemp != null && applyTemp.getApplyTripTemp() != null) {
			if (VERIFY_CODE.equals(applyTemp.getApplyTripTemp().getHotelCheckInCity())) {
				citySupportSearch = Objects.equals(applyTemp.getApplyTripTraffic().getEndCityCode(), searchCityId) ? Boolean.TRUE :
						Boolean.FALSE;
			} else {
				// 不校验时通过
				citySupportSearch = Boolean.TRUE;
			}
		}
		responseVO.setCitySupportSearch(citySupportSearch);
		return responseVO;
	}

    /**
     * 服务商优先级排序
     */
    private static Integer supplierCompare(String supplierCode1, String supplierCode2, List<String> supplierCodes) {
		if (CollectionUtils.isEmpty(supplierCodes) || supplierCode1.endsWith(supplierCode2)) {
			return 0;
		}
		int supplierCode1Index = supplierCodes.indexOf(supplierCode1);
		int supplierCode2Index = supplierCodes.indexOf(supplierCode2);
		return supplierCode1Index - supplierCode2Index;
    }

    /**
     * 酒店多供应商合并
     */
    private List<LocalMappingHotelListResponseBo> getStaticHotelInfo(Map<String, SupplierHotelModel> cacheHotelMap) {
        long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(cacheHotelMap)) {
            log.info("cacheHotelMap为空");
            return Lists.newArrayList();
        }
        List<HotelMappingRequest> mappingRequests = cacheHotelMap.values().stream().map(i -> {
            HotelMappingRequest mappingRequest = new HotelMappingRequest();
            mappingRequest.setSupplier(i.getSupplierCode());
			List<String> hotelIds = i.getHotelList().stream().map(HotelListBean::getKey).collect(Collectors.toList());
            mappingRequest.setHotelIds(hotelIds);
            return mappingRequest;
        }).filter(i -> CollectionUtils.isNotEmpty(i.getHotelIds())).collect(Collectors.toList());
        //做酒店mapping
        HotelMappingResponse hotelMappingResponse = checkHotelMappingProcessor.matchHotel(mappingRequests);
        if (hotelMappingResponse == null) {
            return Lists.newArrayList();
        }
        List<LocalHotelListResponseBo.HotelListBean> hotelList = Lists.newArrayList();
        cacheHotelMap.values().forEach(h -> hotelList.addAll(h.getHotelList()));
        Map<String, LocalHotelListResponseBo.HotelListBean> hotelMap = hotelList.stream().collect(Collectors.toMap
                (h -> CommonUtil.getKey(h.getSupplier(), h.getKey()), Function.identity(), (o, n) -> n));
        List<LocalMappingHotelListResponseBo> responseBos = Lists.newArrayList();
        Map<String, LocalHotelListResponseBo.HotelListBean.PriceBean> priceBeanMap = Maps.newHashMap();
        for (Tree localMapping : hotelMappingResponse.getMappingList()) {
            LocalMappingHotelListResponseBo responseBo = new LocalMappingHotelListResponseBo();
            Tree tree = new Tree();
            tree.setLocalHotelId(localMapping.getLocalHotelId());
            tree.setKvs(localMapping.getKvs());
            responseBo.setTree(tree);
            List<Tree.Kv> kvs = localMapping.getKvs();
            List<LocalHotelListResponseBo.HotelListBean> hotelListBeanArrayList = kvs.stream().map(kv -> hotelMap.get(CommonUtil.getKey(kv.getSupplier(), kv.getHotelId()))).filter(Objects::nonNull).collect(Collectors.toList());
            responseBo.setMultiHotelInfo(hotelListBeanArrayList);
            priceBeanMap.clear();
            hotelListBeanArrayList.forEach(h -> {
                Optional.ofNullable(h.getPrice()).ifPresent(p -> {
                    p.forEach(price -> {
                        priceBeanMap.put(CommonUtil.getKey(price.getPrice(), price.getName()), price);
                    });
                });
            });
            responseBo.setPrice(Lists.newArrayList(priceBeanMap.values()));
            responseBo.getPrice().sort(Comparator.comparing(p -> p.getPrice()));
            responseBos.add(responseBo);
        }
        log.info("getStaticHotelInfo 耗时(毫秒)：" + (System.currentTimeMillis() - start));
        return responseBos;
	}

	private void checkParams(LocalHotelDetailRequestBo requestBo){
		if (StringUtils.isBlank(requestBo.getCheckInDate())){
			throw new CorpBusinessException(HotelResponseCodeEnum.CHECK_IN_TIME_IS_EMPTY);
		}
		if (StringUtils.isBlank(requestBo.getCheckOutDate())){
			throw new CorpBusinessException(HotelResponseCodeEnum.CHECK_OUT_TIME_IS_EMPTY);
		}
		if (requestBo.getCheckInDate().equals(requestBo.getCheckOutDate())){
			throw new CorpBusinessException(HotelResponseCodeEnum.CHECK_IN_TIME_IS_SAME_AS_CHECK_OUT_TIME);
		}
		if (requestBo.getCheckInDate().compareTo(requestBo.getCheckOutDate()) >0){
			throw new CorpBusinessException(HotelResponseCodeEnum.CHECK_IN_TIME_CAN_NOT_BE_LONGER_THAN_CHECK_OUT_TIME);
		}
		try{
			//时间校验-》时间转换前端入参出现2022-1-12情况，正常情况是2022-01-12，转换一次则自动变为2022-01-12（出差申请单查询时候修改日期复现）
			//优化后删除该代码（临时处理）
			Date checkInDate = DateUtils.parseDate(requestBo.getCheckInDate(), "yyyy-MM-dd");
			Date checkOutDate = DateUtils.parseDate(requestBo.getCheckOutDate(), "yyyy-MM-dd");
			requestBo.setCheckInDate(DateUtils.formatDate(checkInDate, "yyyy-MM-dd"));
			requestBo.setCheckOutDate(DateUtils.formatDate(checkOutDate, "yyyy-MM-dd"));
		}catch (Exception e){
			log.error("时间格式异常");
		}
	}

	@SneakyThrows
	public HotelDetailResponseVO detail(LocalHotelDetailRequestBo requestBo,BaseUserInfo baseUserInfo) {
    	//校验参数正确性
    	this.checkParams(requestBo);
		StopWatch sw = new StopWatch("hotel detail");
		StringBuilder logContext = new StringBuilder();
		sw.start("getHotelControlVo");
        HotelControlVo travelStandard = null;
		List<HotelDetailResponseVO.ReasonInfo> reasonInfos = new ArrayList<>();
		//先获取出差申请单差标
		//查询出差申请单差标
		QueryApplyTripStandardResponse hotelApplyTripStandard = applyTripClientLoader.getHotelApplyTripStandard(requestBo.getTrafficId());
		if (Objects.isNull(hotelApplyTripStandard)) {
			throw new CorpBusinessException(HotelResponseCodeEnum.APPLY_CONTROL_IS_NULL);
		}
		// 查询出差申请单模板校验信息 中间页不校验申请单
//		ExceedApplyTemp exceedApplyTemp = getApplyTrafficVerifyByTemp(requestBo);
		List<ApplyTripItemBo> applyTripItemList = applyTripService.getApplyTripItemList(requestBo.getTrafficId(), BizTypeEnum.HOTEL);
		log.info("applyTripItemList={}", JsonUtils.toJsonString(applyTripItemList));

		if (BooleanUtils.isTrue(hotelApplyTripStandard.getEnable())) {
			travelStandard = getHotelControlVoToTrip(hotelApplyTripStandard);
		} else {
            // 获取差标
            travelStandard = this.getHotelControlVo(requestBo);
			//处理是否为同住差标
			if ((StrUtil.isNotBlank(requestBo.getTravelStandardToken()) || (CollectionUtils.isNotEmpty(requestBo.getRooms()) && travelStandard != null))
			 && CorpPayTypeEnum.PUB.getType().equals(requestBo.getCorpPayType())) {
				travelStandard = this.hotelSharingData(travelStandard, requestBo);
				log.info("查询酒店详情走同住差标:{}", JsonUtils.toJsonString(travelStandard));
			}
			sw.stop();
			sw.start("getHotelRcReason");
			// 获取rc reason
			if(ObjectUtil.isNotNull(travelStandard) && CollectionUtils.isNotEmpty(travelStandard.getExceedReasonList())){
				reasonInfos = converExceedReasonVOList(travelStandard.getExceedReasonList());
				log.info("获取rc reason:{}",JsonUtils.toJsonString(reasonInfos));
			}else{
				reasonInfos = this.getHotelRcReason(requestBo);
			}
		}

		// 接入token
		if (StrUtil.isNotBlank(requestBo.getTravelStandardToken()) && CorpPayTypeEnum.PUB.getType().equals(requestBo.getCorpPayType())) {
			HotelControlVo hotelControlVo = tokenAccessService.accessAndRefreshToken(requestBo.getTravelStandardToken(), requestBo.getCityID(), requestBo.getHotelLat(), requestBo.getHotelLon());
			log.info("接入token后的差标 hotelControlVo={}", hotelControlVo);
			if (hotelControlVo == null) {
				throw new CorpBusinessException(HotelResponseCodeEnum.TOKEN_ACCESS_ERROR);
			}
			travelStandard = hotelControlVo;
		}

		HotelControlVo travelStandardBo = travelStandard;
		List<HotelDetailResponseVO.ReasonInfo> reasonInfosBo = reasonInfos;
		sw.stop();
		sw.start("getPayWay");
		// 获取支付方式
		List<PayInfoResponse> payInfo = this.getPayWay(requestBo);
		sw.stop();
		sw.start("listSupplierProduct");
		// 获取供应商产品信息
		List<SupplierProductBo> supplierProducts = commonService.listSupplierProduct(requestBo.getBaseCorpId(), hotelOperatorTypeConfig.getGetDomesticHotelDetail(), requestBo.getCorpPayType());
		if (CollectionUtils.isEmpty(supplierProducts)) {
			throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUPPLIER_INFORMATION);
		}
		sw.stop();
		sw.start("prepare hotel detail request");
		Tree tree = requestBo.getTree();
		if (StringUtils.isNotBlank(requestBo.getSupplierCode())) {
			tree.setKvs(tree.getKvs().stream().filter(k -> Objects.equals(requestBo.getSupplierCode(), k.getSupplier())).collect(Collectors.toList()));
		}
		List<String> supplierCodes = tree.getKvs().stream().map(Tree.Kv::getSupplier).collect(Collectors.toList());
		if (supplierCodes.stream().noneMatch("ctrip"::equals)) {
			supplierCodes.add("ctrip");
		}
		Map<String, String> supplierCityMap = this.getSupplierCityMap(supplierCodes, requestBo.getCityID());
		log.info("supplierCodes={} supplierCityMap={}", supplierCodes, supplierCityMap);
		Map<String, SupplierProductBo> supplierMap = supplierProducts.stream().collect(Collectors.toMap(SupplierProductBo::getSupplierCode, Function.identity(), (o, n) -> n));
		sw.stop();
		sw.start("prepare hotel detail task");
		CompletionService<HotelDetailResponseVO> completionService = new ExecutorCompletionService<>(queryThreadPoolExecutor);
		List<Callable<HotelDetailResponseVO>> callableList = new ArrayList<>();
		LogSplicingUtils.addLogContext(logContext, "Tree kvs：%s", JsonUtils.toJsonString(tree.getKvs()));
		for (Tree.Kv kv : tree.getKvs()) {
			//获取供应商对应的城市id
			String supplierCity = "";
			if ("meiya".equals(kv.getSupplier())) {
				supplierCity = supplierCityMap.get(kv.getSupplier());
			} else {
				supplierCity = requestBo.getCityID();
			}
			if (StringUtils.isBlank(supplierCity)) {
				log.warn(kv.getSupplier() + "未匹配到供应商城市：" + requestBo.getCityID());
				continue;
			}
			//获取服务商列表
			SupplierProductBo supplierProduct = supplierMap.get(kv.getSupplier());
			if (supplierProduct == null) {
				log.warn("获取供应商url失败");
				continue;
			}
			LocalHotelDetailRequestBo supplierRequest = JsonUtils.convert(requestBo, LocalHotelDetailRequestBo.class);
			supplierRequest.setSupplierCityId(supplierCity);
			Tree supplierTree = new Tree();
			supplierTree.setLocalHotelId(tree.getLocalHotelId());
			supplierTree.setKvs(Lists.newArrayList(kv));
			supplierRequest.setTree(supplierTree);
			log.info("finalTravelStandardBo信息:{}", JsonUtils.toJsonString(travelStandardBo));
			log.info("finalReasonInfos信息:{}", JsonUtils.toJsonString(reasonInfos));
			log.info("payInfo信息:{}", JsonUtils.toJsonString(payInfo));
			callableList.add(() -> commonSupplierLoader.detail(supplierRequest, travelStandardBo, reasonInfosBo, supplierProduct, payInfo, applyTripItemList));
		}
		callableList.forEach(completionService::submit);
		sw.stop();
		sw.start("call supplier hotel detail");
		Long timeOut = hotelApollo.getSupplierTimeOut();
		List<HotelDetailResponseVO> responseVos = new ArrayList<>();
		LogSplicingUtils.addLogContext(logContext, "Callable Size：%s", JsonUtils.toJsonString(callableList.size()));
		for (int i = 0; i < callableList.size(); i++) {
			try {
          Future<HotelDetailResponseVO> detailFuture = completionService.poll(timeOut, TimeUnit.SECONDS);
          HotelDetailResponseVO responseVo = (null == detailFuture) ? null : detailFuture.get();
				Optional.ofNullable(responseVo).ifPresent(responseVos::add);
			} catch (InterruptedException | ExecutionException e) {
				log.info("查询酒店详情查询失败");
			}
		}
		if (CollectionUtils.isEmpty(responseVos)) {
			log.info("查询服务商详情接口超时");
			throw new CorpBusinessException(HotelResponseCodeEnum.THE_CURRENT_HOTEL_TEMPORARILY_NO_BOOKING_ROOM);
		}
		LogSplicingUtils.addLogContext(logContext, "responseVos Size：%s", JsonUtils.toJsonString(responseVos.size()));
		sw.stop();
		sw.start("merge hotel response");
		//合并酒店详情信息
		HotelDetailResponseVO hotelDetailResponse = this.surveyResponse(responseVos, reasonInfos, requestBo);
		// 关联出差申请单信息
		if (CollectionUtils.isNotEmpty(applyTripItemList)) {
			hotelDetailResponse.setApplyFieldList(applyTripItemList.stream().map(item -> {
				HotelDetailResponseVO.FieldObject fieldObject = new HotelDetailResponseVO.FieldObject();
				fieldObject.setKey(item.getCode());
				fieldObject.setLabel(item.getName());
				fieldObject.setValue(item.getDesc());
				return fieldObject;
			}).collect(Collectors.toList()));
		}
		sw.stop();
		log.info("detail cost time:{} {} {} {}", sw.prettyPrint(), System.lineSeparator(), System.lineSeparator(), logContext.toString());
		Map<String, SupplierStarBO> supplierStarMap = getSupplierStarMap(responseVos);
		//酒店筛选(是否协议价|含早餐|立即确认|免费取消)
		filterRoom(hotelDetailResponse, requestBo,baseUserInfo,travelStandardBo,supplierStarMap);


        // 如果产线开关关闭，则将所有房间设为不可订
        if ("PUB".equals(requestBo.getCorpPayType()) && Objects.nonNull(travelStandard) && "N".equals(travelStandard.getHotelProductSwitch())){
            productControlConvert(hotelDetailResponse);
        }

		if (travelStandard != null && StringUtils.isNotBlank(travelStandard.getChangeDesc())) {
			hotelDetailResponse.setChangeTravelStandardDesc(travelStandard.getChangeDesc());
		}

		return hotelDetailResponse;
	}

	/**
	 * 获取多供应商星级信息
	 * @param responseVos 供应商返回
	 * @return
	 */
	private Map<String, SupplierStarBO> getSupplierStarMap(List<HotelDetailResponseVO> responseVos) {
		Map<String, SupplierStarBO> supplierStarMap = new HashMap<>();
		if (CollectionUtils.isEmpty(responseVos)) {
			return supplierStarMap;
		}
		// 设置多供应商星级数据
		for (HotelDetailResponseVO responseVo : responseVos) {
			HotelDetailResponseVO.Intro intro = responseVo.getIntro();
			SupplierStarBO supplierStarBo = new SupplierStarBO();
			supplierStarBo.setStar(StringUtils.isBlank(intro.getLevel()) ? null : Integer.valueOf(intro.getLevel()));
			supplierStarBo.setStarLicence(intro.getStarLicence());
			supplierStarMap.put(responseVo.getSupplierList().get(0).getSupplierCode(), supplierStarBo);
		}
		return supplierStarMap;
	}

	public List<HotelDetailResponseVO.ReasonInfo> converExceedReasonVOList(List<ExceedReasonVO> exceedReasonVOList){
		List<HotelDetailResponseVO.ReasonInfo> reasonInfos  = new ArrayList<>();
		HotelDetailResponseVO.ReasonInfo reasonInfo = new HotelDetailResponseVO.ReasonInfo();
		reasonInfo.setName(HoHotelService.HOTEL_REASONINFO);
		reasonInfo.setSelectValue(converHotelRcValue(exceedReasonVOList));
		reasonInfos.add(reasonInfo);
		return reasonInfos;
	}

	public List<com.corpgovernment.api.hotel.product.model.response.HotelRcValue>  converHotelRcValue(List<ExceedReasonVO> exceedReasonVOList){
		List<com.corpgovernment.api.hotel.product.model.response.HotelRcValue> selectValueList = new ArrayList<>();
		for (ExceedReasonVO exceedReasonVO : exceedReasonVOList) {
			com.corpgovernment.api.hotel.product.model.response.HotelRcValue hotelRcValue = new com.corpgovernment.api.hotel.product.model.response.HotelRcValue();
			hotelRcValue.setName(exceedReasonVO.getName());
			hotelRcValue.setValue(exceedReasonVO.getId().toString());
			selectValueList.add(hotelRcValue);
		}
		return selectValueList;
	}

    private void productControlConvert(HotelDetailResponseVO responseVos) {
        if (CollectionUtils.isEmpty(responseVos.getRooms())){
            return;
        }
        responseVos.getRooms().forEach(e ->{
            if (CollectionUtils.isNotEmpty(e.getChildRooms())){
                e.getChildRooms().forEach(k ->{
                    k.setOperateType(0);
                    ArrayList<String> exceedStandardList = new ArrayList<>();
                    exceedStandardList.add("产线管控：禁止预订");
                    k.setExceedStandard(exceedStandardList);
                });
            }
        });
    }

	private void filterRoom(HotelDetailResponseVO hotelDetailResponse, LocalHotelDetailRequestBo requestBo,BaseUserInfo baseUserInfo,HotelControlVo travelStandardBo,Map<String, SupplierStarBO> supplierStarMap){
		log.info("filterRoom request,hotelDetailResponse:{},requestBo:{},baseUserInfo:{},travelStandardBo:{}",
				JsonUtils.toJsonString(hotelDetailResponse),JsonUtils.toJsonString(requestBo),JsonUtils.toJsonString(baseUserInfo),JsonUtils.toJsonString(travelStandardBo));
		List<HotelDetailResponseVO.Room> rooms = hotelDetailResponse.getRooms();
		if(StrUtil.isNotBlank(requestBo.getTravelStandardToken()) && CollUtil.isNotEmpty(rooms) && CorpPayTypeEnum.PUB.getType().equals(requestBo.getCorpPayType())){
        // 校验酒店房间是否超标
        this.verifyTravelStandard(hotelDetailResponse, requestBo, baseUserInfo, rooms,supplierStarMap);
    }

      if (CollectionUtils.isEmpty(rooms)) {
          hotelDetailResponse.setRooms(Collections.emptyList());
          log.info("rooms is empty,hotelDetailResponse:{}", JsonUtils.toJsonString(hotelDetailResponse));
      }

      List<HotelDetailResponseVO.Room> roomList = new ArrayList<>();
		for (HotelDetailResponseVO.Room room:rooms){
        List<HotelDetailResponseVO.ChildRoom> filterRestRooms = filterRoom(room.getChildRooms(), requestBo);
        if (CollectionUtils.isEmpty(filterRestRooms)) {
            continue;
        }

        // 起价重算
        reFillMinPrice(filterRestRooms, room);

        room.setChildRooms(filterRestRooms);
				roomList.add(room);

    }
		hotelDetailResponse.setRooms(roomList);
		log.info("filterRoom response,hotelDetailResponse:{}",JsonUtils.toJsonString(hotelDetailResponse));

	}

    private void reFillMinPrice(List<HotelDetailResponseVO.ChildRoom> filterRestRooms,
            HotelDetailResponseVO.Room room) {
        // 起价重算
        BigDecimal minPrice = filterRestRooms.stream()
                .map(HotelDetailResponseVO.ChildRoom::getPrice)
                .filter(Objects::nonNull)
                .min(BigDecimal::compareTo)
                .orElse(null);

        HotelDetailResponseVO.RoomDetail roomDetail = room.getRoomDetail();
        log.info("重算后的起价={} 原来的起价={}", minPrice, roomDetail);

        if (null == minPrice || null == roomDetail || minPrice.compareTo(roomDetail.getPrice()) >= 0) {
            return;
        }

        log.info("起价重置 原来的price={} 重算后的price={}", roomDetail.getPrice(), minPrice);
        roomDetail.setPrice(minPrice);
    }

    private List<HotelDetailResponseVO.ChildRoom> filterRoom(List<HotelDetailResponseVO.ChildRoom> childRooms,
            LocalHotelDetailRequestBo requestBo) {

        if (CollectionUtils.isEmpty(childRooms)) {
            return Collections.emptyList();
        }

        return childRooms.stream().filter(Objects::nonNull)
                .filter(item -> checkRoom(item, requestBo))
                .collect(Collectors.toList());

    }

    private boolean checkRoom(HotelDetailResponseVO.ChildRoom item, LocalHotelDetailRequestBo requestBo) {
        //是否协议价
        if (BooleanUtils.isTrue(requestBo.getProtatal())) {
            return RoomTypeEnum.C.getType().equals(item.getRoomType());
        }
        //含早餐
        if (Boolean.TRUE.equals(requestBo.getBreakfast())) {
            return null != item.getBreakFastCount() && item.getBreakFastCount() > 0;
        }
        //立即确认
        if (Boolean.TRUE.equals(requestBo.getCheckImmediate())) {
            return BooleanUtils.isTrue(item.getJustifyConfirm());
        }
        //免费取消
        if (Boolean.TRUE.equals(requestBo.getFreeCancel())) {
            return Lists.newArrayList(1, 2).contains(item.getCancelationPolicy());
        }
        // 酒店积分
        if (Boolean.TRUE.equals(requestBo.getOnlyBonusPoint())) {
            return Boolean.TRUE.equals(item.getHotelBonusPoint());
        }
        return true;
    }

    /**
	 * 校验酒店房间是否超标
	 * @param hotelDetailResponse
	 * @param requestBo
	 * @param baseUserInfo
	 * @param rooms
	 */
	private void verifyTravelStandard(HotelDetailResponseVO hotelDetailResponse, LocalHotelDetailRequestBo requestBo,
									  BaseUserInfo baseUserInfo, List<HotelDetailResponseVO.Room> rooms,Map<String, SupplierStarBO> supplierStarMap ) {
		try {
			InitResponseBo.SupplierCompanyBo  serviceFee = new InitResponseBo.SupplierCompanyBo();
			// 判断是否需要服务费,如果房间包括meiya的房间才查询服务费
			boolean serviceFeeFlag = hotelDetailResponse.getRooms().stream()
					.flatMap(room -> Optional.ofNullable(room.getChildRooms()).orElse(Collections.emptyList()).stream())
					.anyMatch(childRoom -> "meiya".equalsIgnoreCase(childRoom.getSupplierCode()));

			if (serviceFeeFlag) {
				InitResponseBo initResponseBo = supplierCompanyClientLoader.searchSupplierConfig(baseUserInfo.getCorpId(), "meiya");
				if (initResponseBo == null || initResponseBo.getSupplierCompany() == null) {
					throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_SERVICE_CHARGE);
				}
				serviceFee = initResponseBo.getSupplierCompany();
			}

			VerifyTravelStandardRequest verifyRequest = createVerifyRequest(requestBo, serviceFee, rooms,supplierStarMap);
			// verifyResult 0 通过（不超标），1 超标且不可预订，2 超标条件可预订
			List<ResourcesVerifyResponse> responses = applyTripClientLoader.verifyTravelStandard(verifyRequest);
			// 是否超标Map
			Map<String, Integer> responsesMap = responses.stream()
					.filter(response -> ObjectUtil.isNotEmpty(response.getExceed())) // 过滤掉exceed为空的对象
					.collect(Collectors.toMap(
							ResourcesVerifyResponse::getResourcesId,
							ResourcesVerifyResponse::getExceed,
							(oldValue, newValue) -> oldValue
					));
			// 是否配置禁止预订
			Map<String, Boolean> bookableMap = responses.stream()
					.filter(this::isValidResponse)
					.collect(Collectors.toMap(
							ResourcesVerifyResponse::getResourcesId,
							ResourcesVerifyResponse::getBookable,
							(oldValue, newValue) -> oldValue
					));
			// 超标管控方式
			Map<String, Set<String>> rejectTypesMap = responses.stream()
					.filter(r -> CollectionUtil.isNotEmpty(r.getRejectTypes()))
					.collect(Collectors.toMap(ResourcesVerifyResponse::getResourcesId,ResourcesVerifyResponse::getRejectTypes,(oldValue, newValue) -> oldValue));
			setOperateTypesForRooms(rooms, responsesMap, rejectTypesMap,bookableMap);
		} catch (Exception e) {
			log.error("校验酒店房间是否超标失败:",e);
			throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_CHECK_HOTEL_ROOM);
		}
	}

	private boolean isValidResponse(ResourcesVerifyResponse response) {
		return Optional.ofNullable(response.getResourcesId()).isPresent() &&
				Optional.ofNullable(response.getBookable()).isPresent();
	}
	/**
	 * 设置房间超标管管控方式
	 * @param rooms
	 * @param responsesMap
	 * @param rejectTypesMap
	 */
	private static void setOperateTypesForRooms(List<HotelDetailResponseVO.Room> rooms, Map<String, Integer> responsesMap,
												Map<String, Set<String>> rejectTypesMap,Map<String, Boolean> bookableMap) {
		rooms.forEach(room -> {
			List<HotelDetailResponseVO.ChildRoom> childRoomList = room.getChildRooms();
			if (childRoomList != null) {
				for (HotelDetailResponseVO.ChildRoom childRoom : childRoomList) {
					// 【差标接口返回超标管控方式】
					//    F("F", "禁止预订", 0),
					//    C("C", "选择原因后继续预订", 1),
					//    M("M", "支持混付", 3),

					// 【前端展示逻辑】
					// 0：禁止预订。 展示【预订】按钮，无法点击
					// 1：选择原因。 展示【预订】按钮，点击选择rc
					// 2：预订触发审批。??
					// 3：混付， 展示【随心订】按钮
					// 4：混合和选择原因,展示【预订】【随心订】按钮，点击预订选择rc，随心订不选择rc
					// null:没有超标，展示【预订按钮】
					// 配置了禁止预订
					if(CollectionUtil.isNotEmpty(bookableMap) && BooleanUtil.isFalse(bookableMap.get(childRoom.getProductId()))){
						childRoom.setOperateType(0);
						String exceedStandard = "产线管控：禁止预订";
						childRoom.setExceedStandard(Collections.singletonList(exceedStandard));
						continue;
					}
					int operateType = CollectionUtil.isEmpty(responsesMap)?0:responsesMap.get(childRoom.getRoomKey());
					if(0 == operateType){ // 没有超标
						childRoom.setOperateType(null);
					}else{
						Set<String> rejectTypes = rejectTypesMap.get(childRoom.getRoomKey()); // 超标管控方式
						if(CollectionUtil.isNotEmpty(rejectTypes)){
							if(rejectTypes.contains("F")){
								childRoom.setOperateType(0);
							}else if(rejectTypes.contains("C") & rejectTypes.contains("M")){
								childRoom.setOperateType(4);
							}else if(rejectTypes.contains("C")){
								childRoom.setOperateType(1);
							}else if(rejectTypes.contains("M")){
								childRoom.setOperateType(3);
							}
						}
					}
				}
			}
		});
	}

	private VerifyTravelStandardRequest createVerifyRequest(LocalHotelDetailRequestBo requestBo,
															InitResponseBo.SupplierCompanyBo serviceFee, List<HotelDetailResponseVO.Room> rooms,
															Map<String, SupplierStarBO> supplierStarMap ) {
		VerifyTravelStandardRequest request = new VerifyTravelStandardRequest();

		request.setTravelStandardToken(requestBo.getTravelStandardToken());
		request.setBizType("3"); // 国内酒店

		List<HotelVerifyRequest> hotelList = new ArrayList<>();
		for (HotelDetailResponseVO.Room room : rooms) {
			for (HotelDetailResponseVO.ChildRoom childRoom : room.getChildRooms()) {
				SupplierStarBO supplierStarBo = supplierStarMap.get(childRoom.getSupplierCode());
				hotelList.add(createHotelVerifyRequest(childRoom, serviceFee, supplierStarBo));
			}
		}
		request.setHotelList(hotelList);
		return request;
	}

	private HotelVerifyRequest createHotelVerifyRequest(HotelDetailResponseVO.ChildRoom childRoom, InitResponseBo.SupplierCompanyBo serviceFee, SupplierStarBO supplierStarBo) {
		HotelVerifyRequest request = new HotelVerifyRequest();
		request.setResourcesId(childRoom.getRoomKey()); // roomkey作为资源id
		request.setPrice(childRoom.getPrice());
		if(childRoom.getSupplierCode().equals("meiya")){
			if("C".equalsIgnoreCase(childRoom.getRoomType())){ // C 为协议酒店,取协议酒店服务费
				request.setServiceFee(serviceFee.getPersonalPubServiceFee());
			}else {
				request.setServiceFee(serviceFee.getAccountPubOtherServiceFee());
			}
		}
        if (supplierStarBo != null) {
            request.setStar(supplierStarBo.getStar());
            request.setStarLicence(supplierStarBo.getStarLicence());
        }
		request.setOldProcess(true);
		return request;
	}
	private HotelControlVo hotelSharingData(HotelControlVo hotelControl, LocalHotelDetailRequestBo request) {
		log.info("hotelControl:{},request:{}", JsonUtils.toJsonString(hotelControl),JsonUtils.toJsonString(request));
		if(ObjectUtil.isNull(hotelControl)){ // 兼容pre代码逻辑
			hotelControl = new HotelControlVo();
		}
		HotelControlVo allDifferentialPrice = null;
		String travelStandardToken = request.getTravelStandardToken();
		if(StrUtil.isNotBlank(travelStandardToken)){
			allDifferentialPrice = travelStandardService.getHotelControlVoByToken(travelStandardToken);
			if(ObjectUtil.isNull(allDifferentialPrice.getOffPeakSeasonSet()) && ObjectUtil.isNull(allDifferentialPrice.getAveragePriceSet())){
				hotelControl.setAveragePriceSet(null);
			}
		} else {
			List<Room> liveWithVOList = JsonUtils.parseArray(JsonUtils.toJsonString(request.getRooms()), Room.class);
			GetAllDifferentialPriceRequest getAllDifferentialPriceRequest = new GetAllDifferentialPriceRequest();
			getAllDifferentialPriceRequest.setCityId(request.getCityID());
			getAllDifferentialPriceRequest.setEDate(request.getCheckOutDate());
			getAllDifferentialPriceRequest.setSDate(request.getCheckInDate());
			getAllDifferentialPriceRequest.setPolicy(request.getPolicyId());
			getAllDifferentialPriceRequest.setPolicyOrgId(request.getPolicyOrgId());
			getAllDifferentialPriceRequest.setHotelControl(hotelControl);
			List<GetAllDifferentialPriceRequest.Room> rooms = new ArrayList<>();
			for (Room room : liveWithVOList) {
				rooms.add(new GetAllDifferentialPriceRequest.Room(room.getRoomNumber(), ListUtils.copyList(room.getResidentList(), GetAllDifferentialPriceRequest.Resident.class)));
			}
			getAllDifferentialPriceRequest.setRooms(rooms);
			allDifferentialPrice = organizationClientLoader.getAllDifferentialPrice(getAllDifferentialPriceRequest, request.getBaseUserInfo());
		}
		log.info("查询接口返回的数据v:{}", JsonUtils.toJsonString(allDifferentialPrice));
		if(ObjectUtil.isNull(allDifferentialPrice)){
			return hotelControl;
		}
		if(ObjectUtil.isNotNull(allDifferentialPrice.getOffPeakSeasonSet())){
			hotelControl.setAveragePriceSet(allDifferentialPrice.getOffPeakSeasonSet());
		}else if(ObjectUtil.isNotNull(allDifferentialPrice.getAveragePriceSet())){
			hotelControl.setAveragePriceSet(allDifferentialPrice.getAveragePriceSet());
		}
		if(ObjectUtil.isNotNull(allDifferentialPrice.getControl())){
			hotelControl.setControl(allDifferentialPrice.getControl());
		}
		hotelControl.setControl(allDifferentialPrice.getControl());
		hotelControl.setAverageStarSet(allDifferentialPrice.getAverageStarSet());
		hotelControl.setExceedReasonList(allDifferentialPrice.getExceedReasonList());
		return hotelControl;
	}

	/**
     * 列表页查询差标
     */
    private HotelControlVo getPriceLevel(LocalHotelListRequestBo requestBo) {
		GetHotelDetailRequest request = new GetHotelDetailRequest();
		request.setCityCode(requestBo.getCityCode());
		request.setStartDate(DateUtil.stringToDate(requestBo.getSDate(), DateUtil.DF_YMD));
		request.setEndDate(DateUtil.stringToDate(requestBo.getEDate(), DateUtil.DF_YMD));
		if (StringUtils.isNotBlank(requestBo.getPolicyId())) {
			request.setUid(requestBo.getPolicyId());
			request.setOrgId(requestBo.getPolicyOrgId());
		} else {
			request.setUid(requestBo.getUid());
			request.setOrgId(requestBo.getOrgId());
		}
		return travelStandardPostClientLoader.getHotelDetail(request);
	}

    /**
     * 详情页查询差标
     */
    private HotelControlVo getHotelControlVo(LocalHotelDetailRequestBo requestBo) {
		if (Boolean.TRUE.equals(requestBo.getSearchOnly())) {
			return null;
		}
		GetHotelDetailRequest request = new GetHotelDetailRequest();
		request.setStartDate(DateUtil.stringToDate(requestBo.getCheckInDate(), DateUtil.DF_YMD));
		request.setEndDate(DateUtil.stringToDate(requestBo.getCheckOutDate(), DateUtil.DF_YMD));
		request.setCityCode(requestBo.getCityID());
		if (StringUtils.isNotBlank(requestBo.getPolicyId())) {
			request.setUid(requestBo.getPolicyId());
			request.setOrgId(requestBo.getPolicyOrgId());
		} else {
			request.setUid(requestBo.getUid());
			request.setOrgId(requestBo.getOrgId());
		}
		request.setPostId(requestBo.getPostId());
		return travelStandardPostClientLoader.getHotelDetail(request);
	}

	private HotelControlVo getHotelControlVoToTrip(QueryApplyTripStandardResponse hotelApplyTripStandard) {
		HotelControlVo limitVo = HotelControlVo.getNotLimitVo();
		if (TravelUnLimitedTypeEnum.isUnLimitAMOUNT(hotelApplyTripStandard.getUnLimitedType())) {
			return limitVo;
		}
		StandardAmountSyncRequest amount = hotelApplyTripStandard.getStandardAmount();
		AveragePriceSet averagePriceSet = new AveragePriceSet();
		averagePriceSet.setPriceFloor("0");
		averagePriceSet.setPriceCeiling(amount.getPriceUpperLimit().toString());
		limitVo.setAveragePriceSet(averagePriceSet);
		Integer value = ControlTypeEnum.getEnumByCode(amount.getControlType()).getValue();
		limitVo.setControl(value);
		return limitVo;
	}

	/**
	 * 获取rc原因
	 */
	private List<HotelDetailResponseVO.ReasonInfo> getHotelRcReason(LocalHotelDetailRequestBo requestBo) {

		if (Boolean.TRUE.equals(requestBo.getSearchOnly())) {
			return Lists.newArrayList();
		}
		String cityId = requestBo.getCityID();
		String uid = StringUtils.isNotBlank(requestBo.getPolicyId()) ? requestBo.getPolicyId() : requestBo.getUid();
		String orgId = StringUtils.isNotBlank(requestBo.getPolicyOrgId()) ? requestBo.getPolicyOrgId() : requestBo.getOrgId();
		List<ReasonInfo> hotelRcInfoList = travelStandardPostClientLoader.getHotelRcInfo(uid, cityId, orgId);
		if (CollectionUtils.isEmpty(hotelRcInfoList)) {
			return new ArrayList<>();
		}
		HotelDetailResponseVO.ReasonInfo reasonInfo = new HotelDetailResponseVO.ReasonInfo();
		Map<String, List<HotelRcValue>> map = hotelRcInfoList.get(0).getSelectValue();
		Set<String> set = map.keySet();
		reasonInfo.setName(set.stream().findFirst().orElse(null));
		reasonInfo.setSelectValue(hotelQueryConvert.convertFromRC(map.get(reasonInfo.getName())));
		return Lists.newArrayList(reasonInfo);
	}

    /**
     * 查询支付方式
     */
    private List<PayInfoResponse> getPayWay(LocalHotelDetailRequestBo requestBo) {
		if (Boolean.TRUE.equals(requestBo.getSearchOnly())) {
			return Lists.newArrayList();
		}
		PayInfoRequest payInfoRequest = new PayInfoRequest();
		payInfoRequest.setName(requestBo.getCorpPayType());
		payInfoRequest.setTransport(TransportEnum.HOTEL.toString());
		if (StringUtils.isNotBlank(requestBo.getPolicyOrgId())) {
			payInfoRequest.setOrgId(requestBo.getPolicyOrgId());
		} else {
			payInfoRequest.setOrgId(requestBo.getOrgId());
		}
		return switchClientLoader.getUserPayInfo(payInfoRequest);
	}

	/**
	 * 合并多供应商酒店详情结果
	 */
	private HotelDetailResponseVO surveyResponse(List<HotelDetailResponseVO> responseVos,
												 List<HotelDetailResponseVO.ReasonInfo> reasonInfos,
												 LocalHotelDetailRequestBo requestBo) {
		// 获取供应商排序
		List<String> supplierSortList = Null.or(commonService.getSupplierSort(Optional.ofNullable(requestBo.getBaseUserInfo()).map(BaseUserInfo::getCorpId).orElse(null), requestBo.getCorpPayType()), new ArrayList<>(0));
		log.info("供应商顺序 supplierSortList:{}", supplierSortList);
		List<HotelDetailResponseVO> responseList = responseVos.stream().filter(Objects::nonNull).sorted(Comparator.comparing(item -> {
			List<HotelDetailResponseVO.SupplierInfo> supplierList = item.getSupplierList();
			if (supplierList == null || supplierList.get(0) == null || !supplierSortList.contains(supplierList.get(0).getSupplierCode())) {
				return Integer.MAX_VALUE;
			}
			return supplierSortList.indexOf(supplierList.get(0).getSupplierCode());
		})).collect(Collectors.toList());
		log.info("供应商列表 responseList:{}", responseList);

		// 构造返回体
		HotelDetailResponseVO responseVo = new HotelDetailResponseVO();
		if (CollectionUtils.isEmpty(responseList)) {
			return responseVo;
		}

		// RC原因
		responseVo.setReasonInfo(reasonInfos);
		// 供应商信息
		List<HotelDetailResponseVO.SupplierInfo> supplierList = responseList.stream().map(HotelDetailResponseVO::getSupplierList).reduce(BaseUtils::combine).orElse(new ArrayList<>());
		if(CollectionUtil.isNotEmpty(supplierList)){
			for (HotelDetailResponseVO.SupplierInfo supplierInfo : supplierList) {
				String supplierLowPrice = supplierInfo.getSupplierLowPrice();
				if(ObjectUtil.isNotEmpty(supplierLowPrice)){
					// 使用setScale方法进行四舍五入到两位小数
					BigDecimal roundedPrice = new BigDecimal(supplierLowPrice).setScale(2, RoundingMode.HALF_UP);
					supplierInfo.setSupplierLowPrice(String.valueOf(roundedPrice));
				}
			}
		}
        // 供应商信息去重
        supplierList = removeRepeatSupplier(supplierList);
		responseVo.setSupplierList(supplierList);

		// 优先级处理
		for (HotelDetailResponseVO hotelDetailResponseVO : responseList) {
			if (hotelDetailResponseVO == null) {
				continue;
			}
			// 图片信息
			List<HotelDetailResponseVO.PicBean> picBeanList = hotelDetailResponseVO.getPicBeans();
			if (responseVo.getPicBeans() == null && CollectionUtils.isNotEmpty(picBeanList)) {
				responseVo.setPicBeans(picBeanList);
			}
			// 酒店名称
			String fullName = hotelDetailResponseVO.getFullName();
			if (responseVo.getFullName() == null && StringUtils.isNotBlank(fullName)) {
				responseVo.setFullName(fullName);
			}
			// 酒店评分
			HotelDetailResponseVO.Comment comment = hotelDetailResponseVO.getComment();
			if (responseVo.getComment() == null && comment != null) {
				responseVo.setComment(comment);
			}
			// 酒店介绍
			HotelDetailResponseVO.Intro intro = hotelDetailResponseVO.getIntro();
			if (responseVo.getIntro() == null && intro != null) {
				responseVo.setIntro(intro);
			}
			else if (responseVo.getIntro() != null && intro != null) {
				if (StringUtils.isBlank(responseVo.getIntro().getFax()) && StringUtils.isNotBlank(intro.getFax())) {
					responseVo.getIntro().setFax(intro.getFax());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getPhone()) && StringUtils.isNotBlank(intro.getPhone())) {
					responseVo.getIntro().setPhone(intro.getPhone());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getOpenYear()) && StringUtils.isNotBlank(intro.getOpenYear())) {
					responseVo.getIntro().setOpenYear(intro.getOpenYear());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getFitmentYear()) && StringUtils.isNotBlank(intro.getFitmentYear())) {
					responseVo.getIntro().setFitmentYear(intro.getFitmentYear());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getOpenRenovationDesc()) && StringUtils.isNotBlank(intro.getOpenRenovationDesc())) {
					responseVo.getIntro().setOpenRenovationDesc(intro.getOpenRenovationDesc());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getHotelType()) && StringUtils.isNotBlank(intro.getHotelType())) {
					responseVo.getIntro().setHotelType(intro.getHotelType());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getLevel()) && StringUtils.isNotBlank(intro.getLevel())) {
					responseVo.getIntro().setLevel(intro.getLevel());
				}
				if (responseVo.getIntro().getStarLicence() == null && intro.getStarLicence() != null) {
					responseVo.getIntro().setStarLicence(intro.getStarLicence());
				}
				if (CollectionUtils.isEmpty(responseVo.getIntro().getServiceTips()) && CollectionUtils.isNotEmpty(intro.getServiceTips())) {
					responseVo.getIntro().setServiceTips(intro.getServiceTips());
				}
				if (responseVo.getIntro().getRoomQuantity() == null && intro.getRoomQuantity() != null) {
					responseVo.getIntro().setRoomQuantity(intro.getRoomQuantity());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getDesc()) && StringUtils.isNotBlank(intro.getDesc())) {
					responseVo.getIntro().setDesc(intro.getDesc());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getHotelOpenRenovationDesc()) && StringUtils.isNotBlank(intro.getHotelOpenRenovationDesc())) {
					responseVo.getIntro().setHotelOpenRenovationDesc(intro.getHotelOpenRenovationDesc());
				}
				if (StringUtils.isBlank(responseVo.getIntro().getBaseInfoDesc()) && StringUtils.isNotBlank(intro.getBaseInfoDesc())) {
					responseVo.getIntro().setBaseInfoDesc(intro.getBaseInfoDesc());
				}
			}
			// 酒店地址
			HotelDetailResponseVO.AddressInfo addressInfo = hotelDetailResponseVO.getAddressInfo();
			if (responseVo.getAddressInfo() == null && addressInfo != null) {
				responseVo.setAddressInfo(addressInfo);
			}
			else if (responseVo.getAddressInfo() != null && addressInfo != null) {
				if (StringUtils.isBlank(responseVo.getAddressInfo().getAddressDetail()) && StringUtils.isNotBlank(addressInfo.getAddressDetail())) {
					responseVo.getAddressInfo().setAddressDetail(addressInfo.getAddressDetail());
				}
				if (StringUtils.isBlank(responseVo.getAddressInfo().getDistrict()) && StringUtils.isNotBlank(addressInfo.getDistrict())) {
					responseVo.getAddressInfo().setDistrict(addressInfo.getDistrict());
				}
				if (CollectionUtils.isEmpty(responseVo.getAddressInfo().getLatlon()) && CollectionUtils.isNotEmpty(addressInfo.getLatlon())) {
					responseVo.getAddressInfo().setLatlon(addressInfo.getLatlon());
				}
			}
			// 酒店周边信息
			List<HotelDetailResponseVO.AroundFacility> aroundFacilitieList = hotelDetailResponseVO.getAroundFacilities();
			if (CollectionUtils.isEmpty(responseVo.getAroundFacilities()) && CollectionUtils.isNotEmpty(aroundFacilitieList)) {
				responseVo.setAroundFacilities(aroundFacilitieList);
			}
			// 酒店设施
			List<HotelDetailResponseVO.HotelFacility> hotelFacilityList = hotelDetailResponseVO.getHotelFacilities();
			if (CollectionUtils.isEmpty(responseVo.getHotelFacilities()) && CollectionUtils.isNotEmpty(hotelFacilityList)) {
				responseVo.setHotelFacilities(hotelFacilityList);
			}
			// 设施组
			List<HotelDetailResponseVO.FacilityGroup> facilityGroupList = hotelDetailResponseVO.getFacilityGroupList();
			if (CollectionUtils.isEmpty(responseVo.getFacilityGroupList()) && CollectionUtils.isNotEmpty(facilityGroupList)) {
				responseVo.setFacilityGroupList(facilityGroupList);
			}
			// 停车信息
			List<HotelDetailResponseVO.ParkingDetailInfo> parkingServiceInfoList = hotelDetailResponseVO.getParkingServiceInfoList();
			if (CollectionUtils.isEmpty(responseVo.getParkingServiceInfoList()) && CollectionUtils.isNotEmpty(parkingServiceInfoList)) {
				responseVo.setParkingServiceInfoList(parkingServiceInfoList);
			}
			// 充电桩信息
			List<HotelDetailResponseVO.ChargingPointInfo> chargingPointList = hotelDetailResponseVO.getChargingPointList();
			if (CollectionUtils.isEmpty(responseVo.getChargingPointList()) && CollectionUtils.isNotEmpty(chargingPointList)) {
				responseVo.setChargingPointList(chargingPointList);
			}
			// 交通信息
			List<HotelDetailResponseVO.PcTransport> pcTransports = hotelDetailResponseVO.getPcTransports();
			if (CollectionUtils.isEmpty(responseVo.getPcTransports()) && CollectionUtils.isNotEmpty(pcTransports)) {
				responseVo.setPcTransports(pcTransports);
			}
			List<HotelDetailResponseVO.AppTransport> appTransports = hotelDetailResponseVO.getAppTransports();
			if (CollectionUtils.isEmpty(responseVo.getAppTransports()) && CollectionUtils.isNotEmpty(appTransports)) {
				responseVo.setAppTransports(appTransports);
			}
			// 酒店提示
			List<String> notices = hotelDetailResponseVO.getNotices();
			if (CollectionUtils.isEmpty(responseVo.getNotices()) && CollectionUtils.isNotEmpty(notices)) {
				responseVo.setNotices(notices);
			}
			// 酒店政策
			HotelDetailResponseVO.HotelPolicy hotelPolicy = hotelDetailResponseVO.getHotelPolicy();
			if (responseVo.getHotelPolicy() == null && hotelPolicy != null) {
				responseVo.setHotelPolicy(hotelPolicy);
			}
			else if (responseVo.getHotelPolicy() != null && hotelPolicy != null) {
				if (StringUtils.isBlank(responseVo.getHotelPolicy().getArrivalanddeparture()) && StringUtils.isNotBlank(hotelPolicy.getArrivalanddeparture())) {
					responseVo.getHotelPolicy().setArrivalanddeparture(hotelPolicy.getArrivalanddeparture());
				}
				if (StringUtils.isBlank(responseVo.getHotelPolicy().getCancel()) && StringUtils.isNotBlank(hotelPolicy.getCancel())) {
					responseVo.getHotelPolicy().setCancel(hotelPolicy.getCancel());
				}
				if (StringUtils.isBlank(responseVo.getHotelPolicy().getChild()) && StringUtils.isNotBlank(hotelPolicy.getChild())) {
					responseVo.getHotelPolicy().setChild(hotelPolicy.getChild());
				}
				if (StringUtils.isBlank(responseVo.getHotelPolicy().getBreakfast()) && StringUtils.isNotBlank(hotelPolicy.getBreakfast())) {
					responseVo.getHotelPolicy().setBreakfast(hotelPolicy.getBreakfast());
				}
				if (StringUtils.isBlank(responseVo.getHotelPolicy().getPet()) && StringUtils.isNotBlank(hotelPolicy.getPet())) {
					responseVo.getHotelPolicy().setPet(hotelPolicy.getPet());
				}
				if (StringUtils.isBlank(responseVo.getHotelPolicy().getRequirements()) && StringUtils.isNotBlank(hotelPolicy.getRequirements())) {
					responseVo.getHotelPolicy().setRequirements(hotelPolicy.getRequirements());
				}
				if (StringUtils.isBlank(responseVo.getHotelPolicy().getPolicy()) && StringUtils.isNotBlank(hotelPolicy.getPolicy())) {
					responseVo.getHotelPolicy().setPolicy(hotelPolicy.getPolicy());
				}
			}
		}

		// 房型信息
		List<HotelDetailResponseVO.Room> roomList = responseList.stream().map(HotelDetailResponseVO::getRooms).reduce(BaseUtils::combine).orElse(new ArrayList<>());
		String supplierCodeFilter = requestBo.getSupplierCodeFilter();
		// 供应商过滤
		if (StringUtils.isNotBlank(supplierCodeFilter)) {
			roomList = roomList.stream().filter(item -> item != null && CollectionUtils.isNotEmpty(item.getChildRooms()) && StringUtils.equalsIgnoreCase(supplierCodeFilter, item.getChildRooms().get(0).getSupplierCode())).collect(Collectors.toList());
			log.info("供应商筛选 supplierCodeFilter={} roomList={}", supplierCodeFilter, JsonUtils.toJsonString(roomList));
		}
		// 匹配处理
		roomList = matchRoom(roomList, requestBo, supplierSortList);

		responseVo.setRooms(this.process(roomList, supplierSortList));
		return responseVo;
	}

	private List<HotelDetailResponseVO.SupplierInfo> removeRepeatSupplier(List<HotelDetailResponseVO.SupplierInfo> supplierList) {
		if (CollectionUtils.isEmpty(supplierList)) {
			return supplierList;
		}
		Map<String, List<HotelDetailResponseVO.SupplierInfo>> tmpMap = supplierList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(HotelDetailResponseVO.SupplierInfo::getSupplierCode));
		if (CollectionUtils.isEmpty(tmpMap)) {
			return supplierList;
		}
		List<HotelDetailResponseVO.SupplierInfo> result = new ArrayList<>();
		tmpMap.forEach((key, value) -> {
			if (CollectionUtils.isEmpty(value)) {
				return;
			}
			if (value.size() == 1) {
				result.add(value.get(0));
			} else {
				HotelDetailResponseVO.SupplierInfo supplierInfo = new HotelDetailResponseVO.SupplierInfo();
				supplierInfo.setSupplierName(value.get(0).getSupplierName());
				supplierInfo.setSupplierCode(value.get(0).getSupplierCode());
				BigDecimal minPrice = value.stream().map(item -> new BigDecimal(item.getSupplierLowPrice())).min(BigDecimal::compareTo).orElse(null);
				if (minPrice != null) {
					supplierInfo.setSupplierLowPrice(String.valueOf(minPrice));
					result.add(supplierInfo);
				}
			}
		});
		log.info("供应商标签去重后的结果 前标签={} 后标签={}", supplierList, result);
		return result;
	}

	private List<HotelDetailResponseVO.Room> matchRoom(List<HotelDetailResponseVO.Room> roomList, LocalHotelDetailRequestBo requestBo, List<String> supplierSortList) {
		if (CollectionUtils.isEmpty(roomList)) {
			log.info("无任何房型，无需进行房型匹配");
			return roomList;
		}
		// 房型匹配分流处理
		// 携程匹配开关
		Boolean openCtripRoomMatchFeature = shuntConfigDao.openFeature("ctripRoomMatch");
		// 自匹配开关
		Boolean openSelfRoomMatchFeature = shuntConfigDao.openFeature("selfRoomMatch");
		// 携程匹配和自匹配开关都为false
		if (!Boolean.TRUE.equals(openCtripRoomMatchFeature) && !Boolean.TRUE.equals(openSelfRoomMatchFeature)) {
			log.info("分流管控，不进行房型匹配");
			return roomList;
		}

		// 只有一家供应商则直接跳过房型匹配
		List<Tree.Kv> kvList = Optional.ofNullable(requestBo).map(LocalHotelDetailRequestBo::getTree).map(Tree::getKvs).orElse(new ArrayList<>(0));
		log.info("提供服务的供应商：{} 房型：{}", kvList, roomList);
		if (kvList.size() < 2) {
			log.info("提供服务的供应商少于两家，不进行房型匹配");
			return roomList;
		}

		// 获取匹配关系
		Map<String, Set<String>> roomRelationMap = new HashMap<>();
		// 携程匹配
		getCtripRoomRelation(roomRelationMap, openCtripRoomMatchFeature, kvList);
		// 自匹配
		getSelfRoomRelation(roomRelationMap, roomList, openSelfRoomMatchFeature);
		log.info("最终匹配关系 roomRelationMap={}", roomRelationMap);
		if (CollectionUtils.isEmpty(roomRelationMap)) {
			log.info("无任何匹配关系");
			return roomList;
		}

		// 获取房型卡片
		Map<String, HotelDetailResponseVO.Room> roomMap = new HashMap<>(8);
		for (HotelDetailResponseVO.Room room : roomList) {
			String key = buildKey(room);
			if (StringUtils.isNotBlank(key) && room.getRoomDetail().getPrice() != null && room.getRoomDetail().getPrice().compareTo(new BigDecimal("0")) > 0) {
				roomMap.put(key, room);
			}
		}
		log.info("房型卡片 roomMap={}", roomMap);

		// 开始匹配
		List<HotelDetailResponseVO.Room> result = new ArrayList<>(roomList.size());
		Set<String> viewRoomSet = new HashSet<>();
		for (HotelDetailResponseVO.Room room : roomList) {
			String key = buildKey(room);
			// 如果构建key失败，直接放入
			if (StringUtils.isBlank(key)) {
				result.add(room);
				continue;
			}
			// 如果出现过了就直接跳出
			if (viewRoomSet.contains(key)) {
				continue;
			}
			// 如果不存在匹配关系直接放入
			Set<String> relationSet = roomRelationMap.get(key);
			if (CollectionUtils.isEmpty(relationSet)) {
				result.add(room);
				viewRoomSet.add(key);
				continue;
			}
			// 全量关系
			relationSet.add(key);
			viewRoomSet.addAll(relationSet);
			// 聚合
			HotelDetailResponseVO.Room mergeRoom = mergeRoom(relationSet, roomMap, supplierSortList);
			if (mergeRoom != null) {
				result.add(mergeRoom);
			}
		}
		log.info("匹配后的房型 result={}", result);
		return result;
	}

	private HotelDetailResponseVO.Room mergeRoom(Set<String> relationSet, Map<String, HotelDetailResponseVO.Room> roomMap, List<String> supplierSortList) {
		List<String> relationList = new ArrayList<>(relationSet);
		// 先去除无关匹配
		List<String> tmpList = relationList.stream()
				.filter(item -> roomMap.get(item) != null && CollectionUtils.isNotEmpty(roomMap.get(item).getChildRooms()))
				.sorted(Comparator.comparing(item -> {
					HotelDetailResponseVO.ChildRoom childRoom = roomMap.get(item).getChildRooms().get(0);
					if (childRoom == null || supplierSortList == null) {
						return Integer.MAX_VALUE;
					}
					int index = supplierSortList.indexOf(childRoom.getSupplierCode());
					return index == -1 ? Integer.MAX_VALUE : index;
				})).collect(Collectors.toList());
		log.info("房型合并 relationList={} tmpList={} roomMap={}", relationList, tmpList, roomMap);
		if (CollectionUtils.isEmpty(tmpList)) {
			return null;
		}

		// 基准房型卡片
		String basicKey = tmpList.get(0);
		HotelDetailResponseVO.Room basicRoom = roomMap.get(basicKey);
		if (basicRoom == null) {
			return null;
		}

		// 房型名称和图片优先级选择
		HotelDetailResponseVO.RoomDetail roomDetail = basicRoom.getRoomDetail();
		if (roomDetail == null) {
			roomDetail = new HotelDetailResponseVO.RoomDetail();
		}
		for (String key : tmpList) {
			if (key.equals(basicKey)) {
				continue;
			}
			HotelDetailResponseVO.RoomDetail tmpRoomDetail = roomMap.get(key).getRoomDetail();
			if (tmpRoomDetail == null) {
				continue;
			}
			if (StringUtils.isBlank(roomDetail.getName()) && StringUtils.isNotBlank(tmpRoomDetail.getName())) {
				roomDetail.setName(tmpRoomDetail.getName());
			}
			if (CollectionUtils.isEmpty(roomDetail.getPicUrls()) && CollectionUtils.isNotEmpty(tmpRoomDetail.getPicUrls())) {
				roomDetail.setPicUrls(tmpRoomDetail.getPicUrls());
			}
			if (StringUtils.isBlank(roomDetail.getLogoUrl()) && StringUtils.isNotBlank(tmpRoomDetail.getLogoUrl())) {
				roomDetail.setLogoUrl(tmpRoomDetail.getLogoUrl());
			}
		}
		basicRoom.setRoomDetail(roomDetail);

		// 属性融合
		mergeRoomAttr(basicRoom, tmpList, roomMap);

		// 合并
		for (String key : tmpList) {
			if (key.equals(basicKey)) {
				continue;
			}
			HotelDetailResponseVO.Room tmp = roomMap.get(key);
			// 子房型填入
			basicRoom.getChildRooms().addAll(tmp.getChildRooms());
			// 低价比较
			if (basicRoom.getRoomDetail().getPrice().compareTo(tmp.getRoomDetail().getPrice()) > 0) {
				basicRoom.getRoomDetail().setPrice(tmp.getRoomDetail().getPrice());
			}
			// 不可订比较
			if (Boolean.FALSE.equals(basicRoom.getBasicRoomEnabled()) && Boolean.TRUE.equals(tmp.getBasicRoomEnabled())) {
				basicRoom.setBasicRoomEnabled(true);
			}
			// 有协议价
			roomDetail = basicRoom.getRoomDetail();
			HotelDetailResponseVO.RoomDetail tmpRoomDetail = tmp.getRoomDetail();
			if ((roomDetail == null || !Boolean.TRUE.equals(roomDetail.getShowTMCLabel())) && tmpRoomDetail != null && Boolean.TRUE.equals(tmpRoomDetail.getShowTMCLabel())) {
				if (roomDetail == null) {
					roomDetail = new HotelDetailResponseVO.RoomDetail();
				}
				roomDetail.setShowTMCLabel(true);
				basicRoom.setRoomDetail(roomDetail);
			}
		}
		return basicRoom;
	}

	private void mergeRoomAttr(HotelDetailResponseVO.Room basicRoom, List<String> tmpList, Map<String, HotelDetailResponseVO.Room> roomMap) {
		Boolean roomAttrMerge = shuntConfigDao.openFeature("roomAttrMerge");
		if (!Boolean.TRUE.equals(roomAttrMerge)) {
			return;
		}
		List<String> areaList = new ArrayList<>();
		List<String> floorList = new ArrayList<>();
		List<String> bedList = new ArrayList<>();
		// 填充数据
		for (String key : tmpList) {
			HotelDetailResponseVO.Room tmp = roomMap.get(key);
			List<String> attrList = Optional.ofNullable(tmp.getRoomDetail()).map(HotelDetailResponseVO.RoomDetail::getBasicInfo).orElse(new ArrayList<>(0));
			if (CollectionUtils.isEmpty(attrList) && attrList.size() < 8) {
				continue;
			}
			String area = attrList.get(0);
			if (StringUtils.isNotBlank(area)) {
				areaList.add(area);
			}
			String floor = attrList.get(1);
			if (StringUtils.isNotBlank(floor)) {
				floorList.add(floor);
			}
			String bed = attrList.get(3);
			if (StringUtils.isNotBlank(bed)) {
				bedList.add(bed);
			}
		}

		// 属性解析融合
		List<String> basicInfoList = Optional.ofNullable(basicRoom.getRoomDetail()).map(HotelDetailResponseVO.RoomDetail::getBasicInfo).orElse(new ArrayList<>(8));
		// 面积
		mergeBasicRoomArea(basicInfoList, areaList);
		// 楼层
		mergeBasicRoomFloor(basicInfoList, floorList);
		// 床型
		mergeBasicRoomBed(basicInfoList, bedList);

		// 赋值
		if (basicRoom.getRoomDetail() == null) {
			basicRoom.setRoomDetail(new HotelDetailResponseVO.RoomDetail());
		}
		basicRoom.getRoomDetail().setBasicInfo(basicInfoList);
	}

	private void mergeBasicRoomBed(List<String> basicInfoList, List<String> bedList) {
		String bedMerge = null;
		Set<String> set = new TreeSet<>(bedList);
		List<String> tmp = set.stream().filter(Objects::nonNull).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(tmp)) {
			bedMerge = StringUtils.join(tmp, "或");
		}
		if (StringUtils.isNotBlank(bedMerge)) {
			basicInfoList.set(3, bedMerge);
		}
		log.info("床型融合结果 床型={} 融合值={}", bedList, bedMerge);
	}

	private void mergeBasicRoomFloor(List<String> basicInfoList, List<String> floorList) {
		StringBuilder floorMerge = new StringBuilder();
		try {
			TreeSet<Integer> set = new TreeSet<>();
			// 解析
			for (String item : floorList) {
				String replace = item.replace(" ", "").replace("F", "").replace("/", ",").replace("f", "").replace("层", "").replace("，", ",");
				// 分割,
				String[] split = replace.split(",");
				for (String str : split) {
					String[] split1 = str.split("-");
					if (split1.length == 1) {
						set.add(Integer.valueOf(split1[0]));
					}
					else if (split1.length == 2) {
						IntStream.rangeClosed(Integer.parseInt(split1[0]), Integer.parseInt(split1[1])).forEach(set::add);
					}
					else {
						throw new RuntimeException("解析失败:" + item);
					}
				}
			}
			// 融合
			Integer prev = null;
			Integer start = null;
			for (Integer current : set) {
				if (start == null) {
					start = current;
					floorMerge.append(start);
				}
				else if (current - prev != 1) {
					if (!start.equals(prev)) {
						if (prev - start != 1) {
							floorMerge.append("-").append(prev);
						}
						else {
							floorMerge.append(",").append(prev);
						}
					}
					floorMerge.append(",").append(current);
					start = current;
				}
				prev = current;
			}
			if (start != null && !start.equals(prev)) {
				floorMerge.append("-").append(prev);
			}
			// 赋值
			if (StringUtils.isNotBlank(floorMerge.toString())) {
				basicInfoList.set(1, floorMerge.toString());
			}
		} catch (Exception e) {
			log.error("楼层融合失败:", e);
		}
		log.info("楼层融合结果 楼层={} 融合值={}", floorList, floorMerge);
	}

	private void mergeBasicRoomArea(List<String> basicInfoList, List<String> areaList) {
		String areaMerge = null;
		try {
			TreeSet<Integer> set = new TreeSet<>();
			// 解析
			for (String item : areaList) {
				String[] tmp = item.replace(" ", "").replace("㎡", "").replace("m²", "").replace("平方米", "").split("-");
				if (tmp.length > 2) {
					throw new RuntimeException("解析失败:" + item);
				}
				for (String str : tmp) {
					set.add(Integer.parseInt(str));
				}
			}
			// 融合
			if (set.size() == 1) {
				areaMerge = String.valueOf(set.first());
			}
			else {
				areaMerge = set.first() + "-" + set.last();
			}
			// 赋值
			if (StringUtils.isNotBlank(areaMerge)) {
				basicInfoList.set(0, areaMerge);
			}
		} catch (Exception e) {
			log.error("面积融合失败:", e);
		}
		log.info("面积融合结果 面积={} 融合值={}", areaList, areaMerge);
	}

	private String buildKey(HotelDetailResponseVO.Room room) {
		if (room == null) {
			return null;
		}
		String roomID = room.getRoomID();
		if (StringUtils.isBlank(roomID)) {
			return null;
		}
		List<HotelDetailResponseVO.ChildRoom> childRooms = room.getChildRooms();
		if (CollectionUtils.isEmpty(childRooms)) {
			return null;
		}
		String supplierCode = Optional.ofNullable(childRooms.get(0)).map(HotelDetailResponseVO.ChildRoom::getSupplierCode).orElse("");
		if (StringUtils.isBlank(supplierCode)) {
			return null;
		}
		return supplierCode + roomID;
	}

	private void getCtripRoomRelation(Map<String, Set<String>> roomRelationMap, Boolean openCtripRoomMatchFeature, List<Tree.Kv> kvList) {
		if (!Boolean.TRUE.equals(openCtripRoomMatchFeature)) {
			log.info("不进行携程匹配");
			return;
		}
		// 获取查询条件
		String ctripHotelId = null;
		List<String> supplierCodeList = new ArrayList<>(4);
		for (Tree.Kv item : kvList) {
			if (Objects.equals(item.getSupplier(), SupplierEnum.CTRIP.getCode())) {
				ctripHotelId = item.getHotelId();
				continue;
			}
			supplierCodeList.add(item.getSupplier());
		}
		if (StringUtils.isBlank(ctripHotelId) || CollectionUtils.isEmpty(supplierCodeList)) {
			log.info("携程匹配无结果");
			return;
		}
		// 携程匹配关系查询
		List<HotelRoomRelationDo> hotelRoomRelationDoList = hotelRoomRelationMapper.listMatchedRoomRelation(SupplierEnum.CTRIP.getCode(), ctripHotelId, supplierCodeList);
		log.info("携程匹配关系：{}", hotelRoomRelationDoList);
		if (CollectionUtils.isEmpty(hotelRoomRelationDoList)) {
			return;
		}
		Map<String, Set<String>> forwardRoomRelationMap = new HashMap<>();
		for (HotelRoomRelationDo item : hotelRoomRelationDoList) {
			String key1 = item.getMasterSupplierCode() + item.getMasterRoomId();
			String key2 = item.getSubSupplierCode() + item.getSubRoomId();
			// 正向存入
			Set<String> set = forwardRoomRelationMap.getOrDefault(key1, new HashSet<>());
			set.add(key2);
			forwardRoomRelationMap.put(key1, set);
		}
		for (String key1 : forwardRoomRelationMap.keySet()) {
			Set<String> tmpSet = forwardRoomRelationMap.get(key1);
			tmpSet.add(key1);
			// 全向关系导入
			for (String tmp : tmpSet) {
				Set<String> set = roomRelationMap.getOrDefault(tmp, new HashSet<>());
				set.addAll(tmpSet);
				roomRelationMap.put(tmp, set);
			}
		}
	}

	private void getSelfRoomRelation(Map<String, Set<String>> roomRelationMap, List<HotelDetailResponseVO.Room> roomList, Boolean openSelfRoomMatchFeature) {
		if (!Boolean.TRUE.equals(openSelfRoomMatchFeature)) {
			log.info("不进行自匹配");
			return;
		}
		Map<String, List<HotelDetailResponseVO.Room>> selfRoomRelationMap = roomList.stream().filter(room -> room != null && room.getRoomDetail() != null && room.getRoomDetail().getName() != null).collect(Collectors.groupingBy(room -> room.getRoomDetail().getName()));
		log.info("自匹配名称相同匹配结果 selfRoomRelationMap={}", selfRoomRelationMap);
		if (CollectionUtils.isEmpty(selfRoomRelationMap)) {
			return;
		}
		for (String key : selfRoomRelationMap.keySet()) {
			List<HotelDetailResponseVO.Room> tmpList = selfRoomRelationMap.get(key);
			if (CollectionUtils.isEmpty(tmpList) || tmpList.size() < 2) {
				continue;
			}
			Set<String> tmpSet = new HashSet<>();
			for (HotelDetailResponseVO.Room room : tmpList) {
				String tmp = buildKey(room);
				if (StringUtils.isNotBlank(key)) {
					tmpSet.add(tmp);
				}
			}
			if (CollectionUtils.isEmpty(tmpSet) || tmpSet.size() < 2) {
				continue;
			}
			// 全向关系导入
            Set<String> set = new HashSet<>(tmpSet);
			for (String tmp : tmpSet) {
				Set<String> set1 = roomRelationMap.get(tmp);
				if (CollectionUtils.isNotEmpty(set1)) {
					set.addAll(set1);
				}
			}
			for (String tmp : set) {
				roomRelationMap.put(tmp, set);
			}
		}
	}

	private List<HotelDetailResponseVO.Room> process(List<HotelDetailResponseVO.Room> rooms, List<String> supplierSortList) {
		for (HotelDetailResponseVO.Room room : rooms) {
			HotelDetailResponseVO.RoomDetail roomDetail = room.getRoomDetail();
			if(ObjectUtil.isAllNotEmpty(roomDetail,roomDetail.getPrice())){
				BigDecimal priceDetail = roomDetail.getPrice();
				BigDecimal roundedPrice = priceDetail.setScale(2, RoundingMode.HALF_UP);
				roomDetail.setPrice(roundedPrice);
			}
			// 设置酒店价格四舍五入保留两位小数
			List<HotelDetailResponseVO.ChildRoom> childRooms = room.getChildRooms();
			if (CollectionUtils.isNotEmpty(childRooms)) {
				for (HotelDetailResponseVO.ChildRoom childRoom : childRooms) {
					BigDecimal price = childRoom.getPrice();
					childRoom.setPrice(ObjectUtil.isNotEmpty(price)? price.setScale(2, RoundingMode.HALF_UP):BigDecimal.ZERO);
					BigDecimal averagePrice = childRoom.getAveragePrice();
					childRoom.setAveragePrice(ObjectUtil.isNotEmpty(averagePrice)? averagePrice.setScale(2, RoundingMode.HALF_UP):BigDecimal.ZERO);
				}
			}
			if (CollectionUtils.isEmpty(supplierSortList)) {
				room.getChildRooms().sort(Comparator.comparing(HotelDetailResponseVO.ChildRoom::getPrice));
			} else {
				room.getChildRooms().sort(Comparator.comparing(HotelDetailResponseVO.ChildRoom::getPrice)
						.thenComparing(item -> {
							int index = supplierSortList.indexOf(item.getSupplierCode());
							return index == -1 ? Integer.MAX_VALUE : index;
						}));
			}
		}
		return rooms.stream().sorted(Comparator.comparing(HotelDetailResponseVO.Room::getBasicRoomEnabled).reversed().thenComparing(i -> i.getRoomDetail().getPrice())).collect(Collectors.toList());
	}


	public void save(SaveOrderRequestBo.HotelInfo hotelInfo) {
		HoHotel hoHotel = new HoHotel();
		BeanUtils.copyProperties(hotelInfo, hoHotel);
		hoHotel.setConfirmTime(new Date());
		hoHotelLoader.insertSelective(hoHotel);
	}

	public HoHotel selectByOrderId(Long orderId) {
		return hoHotelLoader.selectByOrderId(orderId);
	}

	/**
	 * 计算
	 *
	 * @param calcFeeRequest
	 * @return
	 */
	public List<CalculateTravelFee> calcFee(CalcFeeRequest calcFeeRequest) {
		//行程根据日期排序
		List<CalcFeeRequest.Trip> tripList = calcFeeRequest.getTrips().stream().sorted(Comparator.comparing(Trip::getTripDate)).collect(Collectors.toList());
		List<CalculateTravelFee> result = Lists.newArrayList();
		for (CalcFeeRequest.UserInfo userInfo : calcFeeRequest.getUserInfos()) {
			CalculateTravelFee fee = calcPerHuman(userInfo, tripList);
			result.add(fee);
		}
		return result;
    }

    private CalculateTravelFee calcPerHuman(CalcFeeRequest.UserInfo userInfo, List<CalcFeeRequest.Trip> tripList) {
		int sum = 0;
		CalculateTravelFee singlePersonDto = new CalculateTravelFee();
		singlePersonDto.setUid(userInfo.getUid());
		singlePersonDto.setOrgId(userInfo.getOrgId());
		for (int i = 0; i < tripList.size() - 1; i++) {
			//差标地点
			String endCityCode = tripList.get(i).getEndCityCode();
			//计算tripList[i+1]与tripList[i]间隔天数
			long a = (tripList.get(i + 1).getTripDate().getTime() - tripList.get(i).getTripDate().getTime()) / (60 * 60 * 1000 * 24);
			//请求差标服务
			GetHotelDetailRequest request = new GetHotelDetailRequest();
			request.setUid(userInfo.getUid());
			request.setOrgId(userInfo.getOrgId());
			request.setCityCode(endCityCode);
			request.setStartDate(tripList.get(i).getTripDate());
			request.setEndDate(tripList.get(i + 1).getTripDate());
			log.info("请求差标request:{}", JsonUtils.toJsonString(request));
			HotelControlVo controlVo = travelStandardPostClientLoader.getHotelDetail(request);
			//获取最高差标价格
			if (controlVo != null && controlVo.getAveragePriceSet().getPriceCeiling() != null) {
				log.info("获取酒店差标远程结果:{}", JsonUtils.toJsonString(controlVo));
				sum = sum + (controlVo.getAveragePriceSet().getPriceCeiling() == null ? 0 : (Integer.parseInt(controlVo.getAveragePriceSet().getPriceCeiling())) * (int) a);
			} else {
				singlePersonDto.setErrMessage("差标价格有误");
				singlePersonDto.setPrice(null);
			}
		}
		singlePersonDto.setPrice(BigDecimal.valueOf(sum));
		return singlePersonDto;
	}
}
