package com.corpgovernment.hotel.product.external.dto.order.modify;

import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 标准契约-订单修改详情 请求类
 *
 * <AUTHOR> din<PERSON><PERSON><PERSON>
 * @date : 2024/4/10 13:19
 * @see <a href="https://openapi.ctripbiz.com/#/serviceApi?apiId=1000593"></a>
 * @since : 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StandardOrderModificationDetailRequest extends BaseExternalRequest {

    /**
     * 供应商订单号
     */
    @ApiModelProperty("供应商订单号")
    private String orderID;

    /**
     * 供应商公司ID
     */
    @ApiModelProperty("供应商公司ID")
    private String corpID;

    /**
     * 申请修改编号
     */
    @ApiModelProperty("申请修改编号")
    private String applyFormID;
    
    @ApiModelProperty("附加信息")
    private Map<String, Object> additionalInfoMap;

}
