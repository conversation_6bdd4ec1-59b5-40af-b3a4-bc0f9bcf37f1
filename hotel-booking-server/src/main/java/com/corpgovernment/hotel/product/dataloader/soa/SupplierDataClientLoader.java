package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.supplier.soa.MbSupplierClient;
import com.corpgovernment.api.supplier.soa.constant.ProductTypeEnum;
import com.corpgovernment.api.supplier.soa.request.GetSupplierProductConfigRequestBo;
import com.corpgovernment.api.supplier.soa.response.MbSupplierProductResponse;
import com.corpgovernment.api.supplier.vo.MbSupplierInfoVo;
import com.corpgovernment.common.base.JSONResult;
import lombok.extern.slf4j.Slf4j;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SupplierDataClientLoader {

	@Autowired
	private MbSupplierClient supplierClient;

	/**
	 * 查询供应商信息
	 *
	 * @param supplierCode
	 * @return
	 */
	public MbSupplierInfoVo findBySupplierCode(String supplierCode) {
		if (StringUtils.isEmpty(supplierCode)) {
			return null;
		}
		JSONResult<MbSupplierInfoVo> result = supplierClient.getSupplierInfoBy(supplierCode);
		if (result == null || result.getData() == null) {
			log.error("查询供应商信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}


	/**
	 * 获取产品url
	 *
	 * @param supplierCode
	 * @return
	 */
	public MbSupplierProductResponse getSupplierProductBy(String supplierCode, String operateType) {
		if (StringUtils.isBlank(supplierCode) || StringUtils.isBlank(operateType)) {
			return null;
		}
		GetSupplierProductConfigRequestBo request = new GetSupplierProductConfigRequestBo();
		request.setSupplierCode(supplierCode);
		request.setOperateType(operateType);
		request.setProductType(ProductTypeEnum.hotel.name());
		JSONResult<MbSupplierProductResponse> result = supplierClient.getSupplierProductConfig(request);
		if (result == null || result.getData() == null) {
			log.error("获取产品url异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}
}
