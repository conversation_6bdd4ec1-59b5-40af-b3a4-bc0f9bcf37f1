package com.corpgovernment.hotel.product.external.dto.order.cancel;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标准契约-取消订单问询请求类
 *
 * @see <a href="https://openapi.ctripbiz.com/#/serviceApi?apiId=1000589"></a>
 */
@Data
public class StandardCancelOrderInquiryResponse {

    @ApiModelProperty("取消问询结果")
    private CancelQueryInfo cancelQueryInfo;

    @Data
    public static class CancelQueryInfo {

        @ApiModelProperty("取消提示话术code，15分钟内的阶梯订单：LADDER_LT15,15分钟外的阶梯订单:LADDER_GT15,其他订单取消:OTHER")
        private String cancelDescCode;

        @ApiModelProperty("阶梯惩罚金额")
        private BigDecimal ladderPenaltyAmount;

        @ApiModelProperty("是否可以取消")
        private Boolean canCancel;

        @ApiModelProperty("不可取消原因code,20011-订单状态未提交,20012-订单状态已成交20013-订单状态已取消," +
                "2002-散客返回不可修改,2003-存在单据,2004-海外Non-GDS酒店现付订单，或手工GDS订单，不支持" +
                "2005-其他场景暂未开放,1009-协议手工单不支持取消")
        private String nonCancelReasonCode;

        @ApiModelProperty("是否可以取消")
        private Boolean canApplyCancel;

        /**
         * 不可申请取消原因code
         * 20011-订单状态未提交
         * 20012-订单状态已成交
         * 20013-订单状态已取消
         * 1002-可取消
         * 1003-存在修改单据或取消单据（非处理失败）
         * 1004-晚于入住日+1天
         * 1005-存在处理失败取消单据
         * 1006-代订场景：出行人不止一个
         * 1007-代订场景：账户未配置1008-会员订单-散客返回不可申请取消
         * 1009-协议手工单不支持申请取消
         * 1010-超过最大申请次数
         * 1011-改集团不支持申请取消
         */
        private String nonApplyCancelReasonCode;

        @ApiModelProperty("申请取消类型")
        private String applyCancelType;

        @ApiModelProperty("是否已提交过取消申请")
        private Boolean applyCancelSubmittedFlag;
    }


}
