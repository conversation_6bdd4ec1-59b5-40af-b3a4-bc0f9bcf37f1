package com.corpgovernment.hotel.product.supplier.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: SupplierEnum
 * @description: 供应商枚举类
 * @author: zdwang
 * @date: Created in 13:37 2019/7/9
 * @Version: 1.0
 **/
public enum HotelServiceEnum {
    /**
     * 推荐级别排序
     */
    WIFI("WIFI", "WIFI"),
    RESTAURANT("RESTAURANT", "餐厅"),
    RAIRPORT("RAIRPORT", "接机服务"),
    FITNESS("FITNESS", "健身房"),
    SWIMMING("SWIMMING", "游泳池"),
    FUNNY("FUNNY", "娱乐设施");


    private final String code;
    private final String desc;

    private static final Map<String, HotelServiceEnum> lookup = new HashMap<>();

    static {
        for (HotelServiceEnum s : EnumSet.allOf(HotelServiceEnum.class)) {
            lookup.put(s.getCode(), s);
        }
    }

    HotelServiceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public String getDesc() {
        return this.desc;
    }

    public String getCode() {
        return this.code;
    }

    public static HotelServiceEnum get(String code) {
        return lookup.get(code);
    }

    public static boolean exists(String code) {
        return lookup.containsKey(code);
    }
}