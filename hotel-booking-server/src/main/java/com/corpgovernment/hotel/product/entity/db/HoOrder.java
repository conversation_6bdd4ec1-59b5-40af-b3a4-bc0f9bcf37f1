package com.corpgovernment.hotel.product.entity.db;

import com.corpgovernment.api.hotel.product.model.response.HoOrderBo;
import com.corpgovernment.common.handler.Sm4TypeHandler;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 *
 **/
@Data
@Table(name = "ho_order")
@NoArgsConstructor
public class HoOrder implements Serializable {

    public HoOrder(Long orderId, String supplierOrderId, String orderStatus) {
        this.orderId = orderId;
        this.supplierOrderId = supplierOrderId;
        this.orderStatus = orderStatus;
    }

    /**
     * 订单号
     **/
    private Long orderId;

    /**
     * 供应商订单号
     **/
    private String supplierOrderId;

    /**
     * 供应商卡号
     **/
    private String supplierUid;

    /**
     * 供应商公司ID
     **/
    private String supplierCorpId;

    /**
     * 供应商简称
     **/
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商电话
     */
    private String supplierPhone;
    /**
     * 预订人
     **/
    private String uid;

    /**
     * 预订人名
     **/
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String uname;

    /**
     * 部门ID
     **/
    private String deptId;

    /**
     * 公司ID
     **/
    private String corpId;
    /**
     * 组织Id
     */
    private String orgId;
    /**
     * 订单来源（Web/APP）
     **/
    private String source;

    /**
     * 预订时间
     **/
    private java.util.Date orderDate;

    /**
     * 订单状态。SI提交中、PW待支付、TW待出票、TI出票中（拆单1程已出票，1程未出票）、TA已出票、CA已取消、RP部分退票、EP部分改签、RA全部退票、EA全部改签
     **/
    private String orderStatus;

    /**
     * 订单总价
     **/
    private BigDecimal amount;

    /**
     * 取消阶梯价格
     */
    private BigDecimal ladderAmount;
    /**
     * 配送方式
     **/
    private String deliveryType;

    /**
     * 配送费
     **/
    @Column(name = "delivery_price")
    private BigDecimal deliveryPrice;

    /**
     * 费用类型，PUB因公、OWN因私
     **/
    @Column(name = "corp_pay_type")
    private String corpPayType;

    /**
     * 支付方式（ACCNT公司支付）
     **/
    @Column(name = "paytype")
    private String paytype;

    /**
     * 支付渠道  支付宝:AliPay 微信:WechatPay 银联:UnionPay
     **/
    private String payChannel;

    /**
     * 联系人姓名
     **/
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String contactName;

    /**
     * 联系人手机号
     **/
    @ColumnType(typeHandler = Sm4TypeHandler.class)
    private String contactMobilePhone;

    /**
     * 联系人国家码
     **/
    private String contactCountryCode;

    /**
     * 联系人邮箱
     **/
    private String contactEmail;

    /**
     * 出差申请单号
     **/
    private String tripApplyNo;

    /**
     * 出差申请行程号
     **/
    private Long tripTrafficId;

    /**
     * 结算状态（C结算确认）
     **/
    private String settlementStatus;

    /**
     * 结算确认时间
     **/
    private java.util.Date settlementConfirmTime;

    /**
     * 差旅标准
     **/
    private String travelStandard;

    /**
     * 执行差标金额（结算执行差标字段取该值）
     * amountHigh仅在超标时有值
     */
    private BigDecimal travelStandardAmount;

    /**
     * 特别需求
     **/
    private String specialNeed;

    /**
     * 出票时间（出票成功推送，记录时间）
     **/
    private java.util.Date ticketIssuedTime;

    /**
     * 代订人uid
     */
    private String agentUid;

    /**
     * 是否删除
     **/
    private Boolean isDeleted;

    /**
     * 创建时间
     **/
    private java.util.Date datachangeCreatetime;

    /**
     * 修改时间
     **/
    private java.util.Date datachangeLasttime;

    /**
     * 修改时间
     **/
    private java.util.Date cancelTime;

    /**
     * 审批单号
     */
    private String approvalId;

    /**
     * 支付流水号
     */
    private String payNo;
    /**
     * 混付情况下个人支付部分金额
     */
    private BigDecimal pPayAmount;
    /**
     * 混付情况下公司支付部分金额
     */
    private BigDecimal aPayAmount;
	/**
	 * 供应商订单submit时间
	 */
	private java.util.Date submitTime;
    /**
     * 供应商订单Approving时间
     */
    private java.util.Date approvingTime;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 是否超标(0:超标,1:未超标)
     */
    private Integer rcType;

    /**
     * 紧急预定
     */
    @Column(name = "urgent_apply")
    private Boolean urgentApply;

    /**
     * 供应商主账户ID
     */
    @Column(name = "supplier_account_id")
    private String supplierAccountId;

    /**
     * 订单取消原因Code
     */
    @Column(name = "cancel_reason_code")
    private String cancelReasonCode;
    /**
     * 订单取消原因描述
     */
    @Column(name = "cancel_reason_desc")
    private String cancelReasonDesc;

    /**
     * 异步取消
     */
    @Column(name = "async_cancel")
    private Boolean asyncCancel;

    /**
     * 间夜均价
     */
    @Column(name = "room_night_avg_price")
    private BigDecimal roomNightAvgPrice;

    /**
     * 差标Token
     */
    @Column(name = "travel_standard_token")
    private String travelStandardToken;

    /**
     * 退款总金额
     */
    @Column(name = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 确认酒店申请时间
     */
    @Column(name = "confirm_apply_time")
    private Date confirmApplyTime;

    /**
     * 同住管理开关是否开启
     */
    @Column(name = "shared_manage_status")
    private String sharedManageStatus;
    /**
     * 酒店差标管控规则
     */
    @Column(name = "hotel_manage_rules")
    private String hotelManageRules;
    /**
     * 合住百分比
     */
    @Column(name = "shared_percentage")
    private BigDecimal sharedPercentage;
    /**
     * 酒店差标管控策略
     */
    @Column(name = "hotel_manage_strategy")
    private String hotelManageStrategy;
    /**
     * 取消费
     */
    @Column(name = "cancel_fee")
    private BigDecimal cancelFee;

    /**
     * 赔付费
     */
    @Column(name = "compensation_fee")
    private BigDecimal compensationFee;
    /**
     * 手续费
     */
    @Column(name = "commission_fee")
    private BigDecimal commissionFee;

    /**
     * 其他费用
     */
    @Column(name = "other_fee")
    private BigDecimal otherFee;


    /**
     * 取消申请时间
     **/
    @Column(name = "cancel_apply_time")
    private Date cancelApplyTime;
    /**
     * 混付类型
     *
     * @see com.corpgovernment.hotel.booking.enums.MixPayTypeEnum
     */
    @Column(name = "mix_pay_type")
    private Integer mixPayType;

    /**
     * 入住时间
     **/
    @Column(name = "check_in_time")
    private Date checkInTime;

    /**
     * 离店时间
     **/
    @Column(name = "check_out_time")
    private Date checkOutTime;

    /**
     * 实际入住时间
     **/
    @Column(name = "actual_check_in_time")
    private Date actualCheckInTime;

    /**
     * 实际离店时间
     **/
    @Column(name = "actual_check_out_time")
    private Date actualCheckOutTime;

    /**
     * 订单资源 supplier-供应商 direct-直连
     */
    @Column(name = "order_resource")
    private String orderResource;

    /**
     * 审批方式 0, "无须审批" 1, "预设审批" 2, "接口审批"
     */
    @Column(name = "approval_way")
    private String approvalWay;

    /**
     * 是否新系统创建订单 0 否 1 是
     */
    @Column(name = "new_system")
    private Boolean newSystem;

    /**
     * alter table ho_order add column
     * cancel_type char null comment '订单取消类型，A用户主动取消，B供应商取消，C超时取消';
     */
    @Column(name = "cancel_source")
    private String cancelSource;

    /**
     * 自定义备注
     */
    @Column(name = "custom_remark")
    private String customRemark;

    /**
     * 最新入离日期
     */
    @Column(name =  "newest_check_in_out_date")
    private String newestCheckInOutDate;

    /**
     * 服务费策略
     */
    @Column(name = "service_fee_strategy")
    private String serviceFeeStrategy;

    /**
     * 服务费策略值
     */
    @Column(name = "service_fee_strategy_value")
    private BigDecimal serviceFeeStrategyValue;


    /**
     * 后收服务费
     */
    @Column(name = "post_service_fee")
    private BigDecimal postServiceFee;


    public HoOrderBo convertHoOrderBo() {
        HoOrderBo hoOrderBo = new HoOrderBo();
        BeanUtils.copyProperties(this, hoOrderBo);
        hoOrderBo.setPayType(this.paytype);
        return hoOrderBo;
    }
}
