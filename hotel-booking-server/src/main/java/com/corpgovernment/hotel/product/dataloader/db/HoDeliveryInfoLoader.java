package com.corpgovernment.hotel.product.dataloader.db;

import com.corpgovernment.hotel.product.entity.db.HoDeliveryInfo;
import com.corpgovernment.hotel.product.mapper.HoDeliveryInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2022-01-05-17:43
 */
@Component
public class HoDeliveryInfoLoader {

	@Autowired
	private HoDeliveryInfoMapper hoDeliveryInfoMapper;

	public HoDeliveryInfo selectByOrderId(Long orderId) {
		if (orderId == null) {
			return null;
		}
		HoDeliveryInfo record = new HoDeliveryInfo();
		record.setOrderId(orderId);
		return hoDeliveryInfoMapper.selectOne(record);
	}

	public int insertSelective(HoDeliveryInfo record) {
		return hoDeliveryInfoMapper.insertSelective(record);
	}
}
