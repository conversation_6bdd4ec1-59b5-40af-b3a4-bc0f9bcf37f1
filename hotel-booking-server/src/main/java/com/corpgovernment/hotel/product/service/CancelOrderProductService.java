package com.corpgovernment.hotel.product.service;

import static com.corpgovernment.common.enums.CancelPolicyEnum.*;
import static com.corpgovernment.hotel.product.external.constant.SupplierConstant.CancelOrderInquiry.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import com.corpgovernment.hotel.booking.bo.CheckInOutDateInfoBo;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.applytrip.enums.LineVerifyTypeEnum;
import com.corpgovernment.api.applytrip.metadata.ApplyTripStockOrderStatus;
import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficReturnType;
import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficType;
import com.corpgovernment.api.applytrip.soa.request.UseApplyTripTrafficRequest;
import com.corpgovernment.api.approvalsystem.service.ApprovalSystemClient;
import com.corpgovernment.api.approvalsystem.service.request.StopApprovalRequest;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderQueryResponse;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderQueryResponseTypeVo;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderStatus;
import com.corpgovernment.api.hotel.booking.orderstatus.response.CancelOrderDetailResponseVo;
import com.corpgovernment.api.hotel.booking.orderstatus.response.CancelOrderStatusItem;
import com.corpgovernment.api.hotel.product.dto.AsyncCancelRequest;
import com.corpgovernment.api.hotel.product.dto.AsyncCancelResponse;
import com.corpgovernment.api.hotel.product.dto.CancelReasonRequest;
import com.corpgovernment.api.hotel.product.dto.CancelReasonResponse;
import com.corpgovernment.api.hotel.product.model.enums.CancelOrderRuleEnum;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.hotel.product.model.request.CancelOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.request.CancelOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.response.CancelOrderInitResponse;
import com.corpgovernment.api.messageadvice.enums.MsgBizType;
import com.corpgovernment.api.messageadvice.vo.MessageVo;
import com.corpgovernment.api.order.common.dos.OcApplyTripControlRecord;
import com.corpgovernment.api.order.common.enums.ApplyTripControlOperationTypeEnum;
import com.corpgovernment.api.order.common.enums.ApplyTripControlTypeEnum;
import com.corpgovernment.api.ordercenter.dto.OcUtils;
import com.corpgovernment.api.ordercenter.dto.request.AddOrderDetailLogRequest;
import com.corpgovernment.api.ordercenter.enums.OperationTitleEnums;
import com.corpgovernment.api.ordercenter.enums.OrderTypeEnums;
import com.corpgovernment.api.platform.soa.cancelPayemntBill.CancelPayemntBillRequest;
import com.corpgovernment.api.platform.soa.paymentbill.PpPaymentBillDto;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.*;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.hotel.booking.enums.CancelFormStatusEnum;
import com.corpgovernment.hotel.booking.enums.CancelFormTypeEnum;
import com.corpgovernment.hotel.booking.enums.CancelReasonEnums;
import com.corpgovernment.hotel.booking.enums.HotelModifyStatusEnum;
import com.corpgovernment.hotel.product.dataloader.db.*;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CommonOrderClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayBillClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayClientLoader;
import com.corpgovernment.hotel.product.entity.db.*;
import com.corpgovernment.hotel.product.external.client.SupplierSoaClient;
import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import com.corpgovernment.hotel.product.external.dto.order.cancel.*;
import com.corpgovernment.hotel.product.model.ctrip.request.SupplierCancelOrderRequest;
import com.corpgovernment.hotel.product.mq.OrderStatusProducer;
import com.corpgovernment.hotel.product.producer.SendMessageMQProducer;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CancelOrderProductService {

    private static final int FIRST_VIEW_SORT = 1;

    @Autowired
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private CommonService commonService;
    @Autowired
    private HoOrderCancelRuleLoader hoOrderCancelRuleLoader;
    @Autowired
    private CommonOrderClientLoader commonOrderClientLoader;
    @Autowired
    private HoRoomLoader hoRoomLoader;
    @Autowired
    private HoHotelLoader hoHotelLoader;
    @Autowired
    private HoPassengerLoader hoPassengerLoader;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private ApprovalSystemClient approvalSystemService;
    @Autowired
    private SendMessageMQProducer sendMessageMQProducer;
    @Autowired
    private OrderStatusProducer orderStatusProducer;
    @Autowired
    private PayBillClientLoader payBillClientLoader;
    @Autowired
    @Qualifier(value = "basicThreadPoolExecutor")
    private ThreadPoolExecutor basicThreadPoolExecutor;
    @Autowired
    @Qualifier("logThreadPoolExecutor")
    private ThreadPoolExecutor logThreadPoolExecutor;
    @Autowired
    private OrderMonitorService orderMonitorService;
    @Autowired
    private SupplierSoaClient supplierSoaClient;
    @Autowired
    private HoHotelApplyLoader hoHotelApplyLoader;
    @Autowired
    private PayClientLoader payClientLoader;
    @Autowired
    private HoOrderCancelFormLoader hoOrderCancelFormLoader;
    @Autowired
    private HoOrderCancelFormStatusRecordLoader hoOrderCancelFormStatusRecordLoader;


    public CancelOrderResponseBo cancelOrder(CancelOrderRequestBo request) {
        if (Objects.isNull(request.getForceCancel())) {
            request.setForceCancel(false);
        }
        // 查询订单
        Long orderId = request.getOrderId();
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        // 强制取消
        if (request.getForceCancel()) {
            recordCancelForm(hoOrder, request);
            return CancelOrderResponseBo.success();
        }

        request.setOrgId(hoOrder.getCorpId());
        // 美亚需要调用供应商取消，不然会出现重复下单校验 todo 可以删除
        if (Objects.equals("ctrip", hoOrder.getSupplierCode())
            && Objects.equals(hoOrder.getOrderStatus(), OrderStatusEnum.PW.getCode())) {
            List<String> payTypeList = Lists.newArrayList(PayTypeEnum.MIXPAY.getType(), PayTypeEnum.PPAY.getType());
            if (payTypeList.contains(hoOrder.getPaytype())) {
                List<PpPaymentBillDto> paymentBillList = payBillClientLoader.paymentBillInfo(orderId);
                PpPaymentBillDto paymentBill = paymentBillList.stream().filter(e -> Objects.equals(e.getStatus(), "U"))
                    .filter(e -> Objects.equals(e.getPayType(), PayTypeEnum.PPAY.getType())).findFirst().orElse(null);
                if (paymentBill != null) {
                    this.cancelLocalOrder(request);
                    return CancelOrderResponseBo.success();
                }
            }
        }
        // 状态修改
        CancelOrderResponseBo cancelOrderResponseBo = cancelSupplierOrder(request, hoOrder);
        if (cancelOrderResponseBo.isSuccess()) {
            //更新取消申请时间
            HoOrder record = new HoOrder();
            record.setOrderId(orderId);
            record.setCancelApplyTime(new Date());
            hoOrderLoader.updateByOrderId(record);
            if (BooleanUtils.isFalse(request.getApplyCancelFlag()) && BooleanUtils.isNotTrue(hoOrder.getAsyncCancel())) {
                this.cancelLocalOrder(request);
            } else {
                if (StringUtils.isNotBlank(request.getCancelSource())) {
                    hoOrderLoader.updateCancelSource(orderId, request.getCancelSource());
                }
            }

            // 记录取消单据 申请取消或 直接取消中的[异步取消]
            if (request.getApplyCancelFlag() || hoOrder.getAsyncCancel()) {
                recordCancelForm(hoOrder, request);
            }
        }
        return cancelOrderResponseBo;
    }

    private void cancelLocalOrder(CancelOrderRequestBo cancelOrderRequestBo) {
        // 查询订单
        Long orderId = cancelOrderRequestBo.getOrderId();
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        HoRoom hoRoom = hoRoomLoader.selectByOrderId(orderId);
        HoHotel hoHotel = hoHotelLoader.selectByOrderId(orderId);
        // 更新订单为已取消
        hoOrderLoader.updateOrderStatus(orderId, null, OrderStatusEnum.CA.getCode());
        // 写入取消原因
        hoOrderLoader.updateCancelReason(orderId, hoOrder.getOrderStatus());

        if (OrderStatusEnum.PW.getCode().equals(hoOrder.getOrderStatus())) { // 待支付的订单需要取消支付单
            CancelPayemntBillRequest cancelPayemntBillRequest = new CancelPayemntBillRequest();
            cancelPayemntBillRequest.setOrderId(String.valueOf(hoOrder.getOrderId()));
            payClientLoader.cancelPaymentBill(cancelPayemntBillRequest);
        }

        if(StringUtils.isNotBlank(cancelOrderRequestBo.getCancelSource())){
            hoOrderLoader.updateCancelSource(orderId,cancelOrderRequestBo.getCancelSource());
        }
        orderStatusProducer
            .sendOrderStatusMsg(OcUtils.initOcReq(orderId, OrderStatusEnum.CA.getCode(), null, OrderTypeEnum.HN));

        List<HoPassenger> hoPassengers = hoPassengerLoader.selectByOrderId(orderId);
        sendMessage(cancelOrderRequestBo, orderId, hoOrder, hoRoom, hoHotel, hoPassengers);
        // 回滚出差申请单有效性
        rollbackApplyTrip(hoOrder, hoRoom, hoHotel, hoPassengers, ApplyTripStockOrderStatus.CANCEL_SUCCESS);
        CompletableFuture.runAsync(() -> {
            StopApprovalRequest request = new StopApprovalRequest();
            request.setExternalId(hoOrder.getApprovalId());
            approvalSystemService.stopApproval(request);
        }, basicThreadPoolExecutor);
        CompletableFuture.runAsync(() -> addLog(cancelOrderRequestBo.getCancelType(), orderId, hoOrder.getUid()),
            basicThreadPoolExecutor);
    }

    /**
     * 记录取消单据
     *
     * @param hoOrder         订单对象
     * @param request         请求对象
     */
    private void recordCancelForm(HoOrder hoOrder, CancelOrderRequestBo request) {

        Boolean forceCancel = request.getForceCancel();
        Boolean applyCancelFlag = request.getApplyCancelFlag();

        HoOrderCancelForm hoOrderCancelForm = new HoOrderCancelForm();
        hoOrderCancelForm.setOrderId(hoOrder.getOrderId());
        hoOrderCancelForm.setSupplierOrderId(hoOrder.getSupplierOrderId());

        CancelReasonEnums cancelReasonEnums = CancelReasonEnums.getByType(request.getCancelReason());
        Optional.ofNullable(cancelReasonEnums).ifPresent(cancelReason -> {
            hoOrderCancelForm.setReasonCode(cancelReason.getType());
            hoOrderCancelForm.setReasonDesc(cancelReason.getDesc());
        });

        hoOrderCancelForm.setFormType(applyCancelFlag || forceCancel ? CancelFormTypeEnum.APPLY_CANCEL.getCode() : CancelFormTypeEnum.DIRECT_CANCEL.getCode());
        if (forceCancel) {
            hoOrderCancelForm.setStatus(CancelFormStatusEnum.PLATFORM_SUBMITTED.getCode());
            hoOrderCancelForm.setSupplierFormId(IdUtil.fastSimpleUUID());
        } else {
            hoOrderCancelForm.setStatus(CancelFormStatusEnum.SUBMITTED.getCode());
        }
        hoOrderCancelFormLoader.insertSelective(hoOrderCancelForm);

        HoOrderCancelFormStatusRecord submitRecord = new HoOrderCancelFormStatusRecord();
        submitRecord.setCancelFormId(hoOrderCancelForm.getId());
        submitRecord.setStatus(forceCancel ? CancelFormStatusEnum.PLATFORM_SUBMITTED.getCode() : CancelFormStatusEnum.SUBMITTED.getCode());
        submitRecord.setHandleTime(new Date());
        submitRecord.setViewSort(FIRST_VIEW_SORT);
        hoOrderCancelFormStatusRecordLoader.insertSelective(submitRecord);
    }

    private void addLog(String cancelType, Long orderId, String uid) {
        try {
            logThreadPoolExecutor.execute(() -> {
                try {
                    AddOrderDetailLogRequest logRequest = new AddOrderDetailLogRequest();
                    logRequest.setOrderType(OrderTypeEnums.HOTEL.getCode());
                    logRequest.setUid(uid);
                    logRequest.setOrderId(String.valueOf(orderId));
                    logRequest.setOperationTitle(OperationTitleEnums.ORDERCANCEL.getDesc());
                    if ("AUTO".equals(cancelType)) {
                        logRequest.setOperationDetails("30分钟自动申请取消订单! [SUCCESS]");
                    } else {
                        logRequest.setOperationDetails("申请取消订单! [SUCCESS]");
                    }
                    log.info("订单日志入参" + JsonUtils.toJsonString(logRequest));
                    commonOrderClientLoader.addLog(logRequest);
                } catch (Exception e) {
                    // 记录log
                    log.error("申请取消订单:", e);
                }
            });
        } catch (Exception e) {
            log.error("申请取消订单:", e);
        }
    }

    private void sendMessage(CancelOrderRequestBo cancelOrderRequestBo, Long orderId, HoOrder hoOrder, HoRoom hoRoom,
        HoHotel hoHotel, List<HoPassenger> hoPassengers) {
        MsgBizType aliyunMsgEnum = MsgBizType.HOTEL_ORDER_CANCEL_UNREFUND;
        if (!PayTypeEnum.CASH.getType().equals(hoOrder.getPaytype()) &&
            (OrderStatusEnum.TW.getCode().equals(hoOrder.getOrderStatus())
                || OrderStatusEnum.TA.getCode().equals(hoOrder.getOrderStatus()))) {
            aliyunMsgEnum = MsgBizType.HOTEL_ORDER_CANCEL_REFUND;
        }
        // 发送短信
        if ("AUTO".equals(cancelOrderRequestBo.getCancelType())) {
            aliyunMsgEnum = MsgBizType.HOTEL_ORDER_CANCEL_UNPAY;
        }
        Map<String, Object> aliyunKeyList = new HashMap<>();
        String supplierOrderId = hoOrder.getSupplierOrderId();
        if (StringUtils.isBlank(supplierOrderId)){
            supplierOrderId = String.valueOf(orderId);
        }
        aliyunKeyList.put("orderId", supplierOrderId);
        aliyunKeyList.put("checkInDate", DateUtil.dateToString(hoRoom.getCheckInDate()));
        aliyunKeyList.put("hotelName", hoHotel.getHotelName());
        aliyunKeyList.put("roomName", hoRoom.getRoomName());
        aliyunKeyList.put("roomType", hoRoom.getRoomName());
        log.info("发送短信给联系人: {}", JsonUtils.toJsonString(hoOrder));
        MessageVo messageVo = new MessageVo();
        MessageVo.Sms sms = new MessageVo.Sms();
        sms.setBizType(aliyunMsgEnum);
        sms.setVars(new HashMap<>(aliyunKeyList));
        sms.setCorpId(hoOrder.getCorpId());
        sms.setCountryCode(hoOrder.getContactCountryCode());
        sms.setPhone(hoOrder.getContactMobilePhone());
        messageVo.setSms(sms);
        MessageVo.AppPush appPush = new MessageVo.AppPush();
        appPush.setBizType(aliyunMsgEnum);
        appPush.setBizId(String.valueOf(hoOrder.getOrderId()));
        appPush.setVars(new HashMap<>(aliyunKeyList));
        appPush.setOrgId(hoOrder.getOrgId());
        appPush.setUid(hoOrder.getUid());
        messageVo.setAppPush(appPush);
        MessageVo.StationMessage stationMessage = new MessageVo.StationMessage();
        BeanUtils.copyProperties(appPush, stationMessage);
        stationMessage.setVars(new HashMap<>(aliyunKeyList));
        messageVo.setStationMessage(stationMessage);
        sendMessageMQProducer.sendMsg(messageVo);
        // 乘客发送短信
        MessageVo passMessageVo = new MessageVo();
        MessageVo.Sms passSms = new MessageVo.Sms();
        MessageVo.AppPush passAppPush = new MessageVo.AppPush();
        for (HoPassenger hoPassenger : hoPassengers) {
            if (Boolean.TRUE.equals(hoPassenger.getIsSendSms())
                && StringUtils.isNotBlank(hoPassenger.getMobilePhone())
                && !hoPassenger.getPassengerName().equals(hoOrder.getContactName())) {
                log.info("发送短信给入住人 {}", JsonUtils.toJsonString(hoPassenger));
                passSms.setCountryCode(hoPassenger.getCountryCode());
                passSms.setPhone(hoPassenger.getMobilePhone());
                passSms.setCorpId(hoOrder.getCorpId());
                passSms.setVars(new HashMap<>(aliyunKeyList));
                passSms.setBizType(aliyunMsgEnum);
                passMessageVo.setSms(passSms);
                passAppPush.setUid(hoPassenger.getUid());
                passAppPush.setOrgId(hoPassenger.getOrgId());
                passAppPush.setVars(new HashMap<>(aliyunKeyList));
                passAppPush.setBizId(String.valueOf(hoOrder.getOrderId()));
                passAppPush.setBizType(aliyunMsgEnum);
                if (StringUtils.isNotBlank(hoPassenger.getUid())) {
                    MessageVo.StationMessage passStationMessage = new MessageVo.StationMessage();
                    BeanUtils.copyProperties(passAppPush, passStationMessage);
                    passStationMessage.setVars(new HashMap<>(aliyunKeyList));
                    passMessageVo.setStationMessage(passStationMessage);
                    passMessageVo.setAppPush(passAppPush);
                }
                sendMessageMQProducer.sendMsg(passMessageVo);
            }
        }
    }

    private void rollbackApplyTrip(HoOrder hoOrder, HoRoom hoRoom, HoHotel hoHotel, List<HoPassenger> hoPassengers, ApplyTripStockOrderStatus applyTripStockOrderStatus) {
        if (StringUtils.isNotBlank(hoOrder.getTripApplyNo())) {
            UseApplyTripTrafficRequest hotelRequest = new UseApplyTripTrafficRequest();
            hotelRequest.setOrderId(String.valueOf(hoOrder.getOrderId()));
            hotelRequest.setApplyNo(hoOrder.getTripApplyNo());
            hotelRequest.setTrafficId(hoOrder.getTripTrafficId());
            BigDecimal amount;
            if (ApplyTripStockOrderStatus.SUBMIT_REJECT.equals(applyTripStockOrderStatus)) { //驳回
                amount = getOrderCompanyPayAmount(hoOrder);
            } else { //取消成功
                boolean unConfirmCancel = OrderStatusEnum.TW.getCode().equalsIgnoreCase(hoOrder.getOrderStatus());
                boolean payTimeoutCancel = OrderStatusEnum.PW.getCode().equalsIgnoreCase(hoOrder.getOrderStatus());
                boolean unApproveCancel = OrderStatusEnum.AW.getCode().equalsIgnoreCase(hoOrder.getOrderStatus());
                if (unConfirmCancel || payTimeoutCancel || unApproveCancel) {
                    amount = getOrderCompanyPayAmount(hoOrder);
                } else { //已确认取消 等待已退款推送回滚金额
                    amount = BigDecimal.ZERO;
                }
            }
            hotelRequest.setAmount(amount);
            List<String> uidList = hoPassengers
                    .stream()
                    .map(x -> StringUtils.isBlank(x.getUid()) ? String.valueOf(x.getNoEmployeeId()) : x.getUid())
                    .collect(Collectors.toList());
            hotelRequest.setUid(uidList);


            hotelRequest.setStartDate(DateUtil.dateToString(hoRoom.getCheckInDate(), "yyyy-MM-dd"));
            hotelRequest.setEndDate(DateUtil.dateToString(hoRoom.getCheckOutDate(), "yyyy-MM-dd"));
            //间夜数 = 房间数 * 夜数
            Integer roomQuantity = hoRoom.getRoomQuantity();
            String newestCheckInOutDateStr = hoOrder.getNewestCheckInOutDate();
            List<CheckInOutDateInfoBo> checkInOutDateInfoBoList = JsonUtils.parseArray(newestCheckInOutDateStr, CheckInOutDateInfoBo.class);
            Integer nightCount = checkInOutDateInfoBoList.stream().map(checkInOutDateInfoBo -> DateUtil.betweenDay(checkInOutDateInfoBo.getCheckInDate(), checkInOutDateInfoBo.getCheckOutDate()))
                    .reduce(0, Integer::sum);
            hotelRequest.setHotelCount(roomQuantity * nightCount);
            hotelRequest.setEndCityCode(hoHotel.getCityId());

            hotelRequest.setTrafficType(EApplyTripTrafficType.HOTEL.getValue());
            hotelRequest.setReturnType(EApplyTripTrafficReturnType.HOTEL.getValue());
            hotelRequest.setBizOperation(applyTripStockOrderStatus.getValue());
            hotelRequest.setVerifyType(LineVerifyTypeEnum.ALL_TYPE.getCode());

            hotelRequest.setBizKey(hotelRequest.getOrderId().concat(StrUtil.COLON).concat(hotelRequest.getBizOperation()));

            OcApplyTripControlRecord controlRecord = new OcApplyTripControlRecord();
            controlRecord.setScene(hotelRequest.getBizOperation());
            controlRecord.setControlType(ApplyTripControlOperationTypeEnum.RELEASE.getCode());
            controlRecord.setOperationType(ApplyTripControlTypeEnum.CONTROL_ALL.getCode());

            boolean result = applyTripClientLoader.rollbackApplyTripUseStatusAndRecord(hotelRequest, controlRecord);
            if (!result) {
                log.debug("还原出差申请失败 {}", result);
            }
        }
    }

    /**
     * 获取回滚出差申请单金额
     *
     * @param hoOrder 订单
     * @param hoHotel 订单酒店
     * @param hoRoom  订单房型
     * @return {@link BigDecimal} 回滚出差申请单金额
     */
    private BigDecimal getRollbackApplyTripAmount(HoOrder hoOrder, HoRoom hoRoom, HoHotel hoHotel) {
        Integer cancelPolicyType = hoRoom.getCancelPolicyType();
        //无取消政策
        if (Objects.isNull(cancelPolicyType)) {
            log.warn("订单无取消政策");
            return BigDecimal.ZERO;
        }
        CancelPolicyEnum orderCancelPolicy = CancelPolicyEnum.getByType(cancelPolicyType);
        //未匹配到取消政策值
        if (Objects.isNull(orderCancelPolicy)) {
            log.warn("订单未匹配到取消政策：{}", cancelPolicyType);
            return BigDecimal.ZERO;
        }

        if (orderCancelPolicy.equals(OverTimeGuaranteeLimitedCancel)) {
            log.warn("订单目前不存在该取消政策:超时担保,有限制取消");
            return BigDecimal.ZERO;
        }

        if (FreeCancel.equals(orderCancelPolicy)) { //免费取消
            return getFreeCancelRollbackAmount(hoOrder);
        }

        if (LimitedCancel.equals(orderCancelPolicy)) { //限时取消 阶梯取消是限时取消的一种 通过查询order_cancel_rule是否存在数据判定
            return getLimitedCancelRollbackAmount(hoOrder, hoHotel);
        }

        //不可取消
        log.warn("订单的取消政策为确认后不可取消或修改");

        return BigDecimal.ZERO;
    }

    /**
     * 免费取消 - 订单状态待确认               回滚公付金额
     * 免费取消 - 订单状态已确认 - 无修改单据    回滚公付金额 - 前收服务费
     * 免费取消 - 订单状态已确认 - 有修改单据    不回滚
     *
     * @param hoOrder 订单
     * @return {@link BigDecimal}   免费取消回滚金额
     */
    private BigDecimal getFreeCancelRollbackAmount(HoOrder hoOrder) {
        boolean unConfirmed = OrderStatusEnum.TW.getCode().equalsIgnoreCase(hoOrder.getOrderStatus());
        BigDecimal orderCompanyPayAmount = getOrderCompanyPayAmount(hoOrder);

        if (unConfirmed) {
            return orderCompanyPayAmount;
        }

        if (!existHotelApply(hoOrder.getOrderId())) {
            if (BigDecimal.ZERO.compareTo(orderCompanyPayAmount) == 0) {
                return orderCompanyPayAmount;
            }
            BigDecimal serviceFee = Optional.ofNullable(hoOrder.getServiceFee()).orElse(BigDecimal.ZERO);
            return orderCompanyPayAmount.subtract(serviceFee);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 限时/阶梯取消 - 未过最晚免费取消时间 - 订单待确认                 回滚公付金额
     * 限时/阶梯取消 - 未过最晚免费取消时间 - 订单已确认 - 无修改单据      回滚公付金额 - 前收服务费
     * 限时/阶梯取消 - 未过最晚免费取消时间 - 订单已确认 - 有修改单据      不回滚
     * 限时/阶梯取消 - 已过最晚免费取消时间                             不回滚
     *
     * @param hoOrder     订单
     * @param hoHotel     订单酒店
     * @return {@link BigDecimal}   限时/阶梯取消回滚金额
     */
    private BigDecimal getLimitedCancelRollbackAmount(HoOrder hoOrder, HoHotel hoHotel) {
        Date cancelApplyTime = hoOrder.getCancelApplyTime();
        //历史数据不存在该字段无需回滚
        if (Objects.isNull(cancelApplyTime)) {
            return BigDecimal.ZERO;
        }

        //最晚免费取消时间
        Date lastCancelTime = hoHotel.getLastCancelTime();
        boolean beforeLastCancelTime = cancelApplyTime.before(lastCancelTime);

        if (!beforeLastCancelTime) { //已过最晚免费取消时间
            return BigDecimal.ZERO;
        }

        BigDecimal orderCompanyPayAmount = getOrderCompanyPayAmount(hoOrder);

        boolean unConfirmed = OrderStatusEnum.TW.getCode().equalsIgnoreCase(hoOrder.getOrderStatus());
        if (unConfirmed) {
            return orderCompanyPayAmount;
        }

        if (existHotelApply(hoOrder.getOrderId())) { //有修改单据
            return BigDecimal.ZERO;
        }
        //无修改单据
        if (BigDecimal.ZERO.compareTo(orderCompanyPayAmount) == 0) {
            return orderCompanyPayAmount;
        }
        BigDecimal serviceFee = Optional.ofNullable(hoOrder.getServiceFee()).orElse(BigDecimal.ZERO);
        return orderCompanyPayAmount.subtract(serviceFee);

    }

    /**
     * 获取订单公司付款金额
     * 因公出行-公司支付	订单金额（amount）
     * 因公出行-混合支付	订单公付金额（a_pay_amount）
     * 因公出行-个人支付	0
     * 因公出行-线下支付	0
     *
     * @param hoOrder 订单
     * @return {@link BigDecimal} 公司付款金额
     */
    private BigDecimal getOrderCompanyPayAmount(HoOrder hoOrder) {
        //公司支付
        boolean companyPay = PayTypeEnum.ACCNT.getType().equalsIgnoreCase(hoOrder.getPaytype());
        if (companyPay) {
            return hoOrder.getAmount();
        }
        //混合支付
        boolean mixPay = PayTypeEnum.MIXPAY.getType().equalsIgnoreCase(hoOrder.getPaytype());
        if (mixPay) {
            return hoOrder.getAPayAmount();
        }
        //个付或者线下支付
        return BigDecimal.ZERO;
    }

    /**
     * 有无修改单单据 是否有以下状态的单据
     * 【已提交 、待处理 、与酒店协调中 、修改成功、修改取消中】
     *
     * @param orderId 订单号
     * @return {@link Boolean} true 存在 false 不存在
     */
    private Boolean existHotelApply(Long orderId) {
        List<HoHotelApply> hoHotelApplyList = hoHotelApplyLoader.select(orderId);
        return hoHotelApplyList.stream().anyMatch(item -> !HotelModifyStatusEnum.FAILED.getCode().equals(item.getStatus()) && !HotelModifyStatusEnum.CANCELED.getCode().equals(item.getStatus()));
    }

    public CancelOrderInitResponse cancelOrderInit(CancelOrderRequestBo cancelOrderRequestBo) {
        CancelOrderInitResponse cancelOrderResponse = new CancelOrderInitResponse();
        // 查询规则
        HoRoom hoRoom = hoRoomLoader.selectByOrderId(cancelOrderRequestBo.getOrderId());
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(cancelOrderRequestBo.getOrderId());
        HoHotel hoHotel = hoHotelLoader.selectByOrderId(cancelOrderRequestBo.getOrderId());
        if (OrderStatusEnum.SI.getCode().equals(hoOrder.getOrderStatus())
            || OrderStatusEnum.PW.getCode().equals(hoOrder.getOrderStatus())
            || OrderStatusEnum.TW.getCode().equals(hoOrder.getOrderStatus())) {
            cancelOrderResponse.setCanCancelFlag(true);
            cancelOrderResponse.setCancelType(0);
            return cancelOrderResponse;
        }
        if ("8".equals(String.valueOf(hoRoom.getCancelPolicyType()))) { // 不能取消
            if (OrderStatusEnum.TA.getCode().equals(hoOrder.getOrderStatus())) {
                cancelOrderResponse.setCanCancelFlag(false);
                cancelOrderResponse.setCancelType(1);
                return cancelOrderResponse;
            }
            cancelOrderResponse.setCanCancelFlag(true);
            cancelOrderResponse.setCancelType(0);
            return cancelOrderResponse;
        } else if ("1".equals(String.valueOf(hoRoom.getCancelPolicyType()))) {// 免费取消
            cancelOrderResponse.setCanCancelFlag(true);
            cancelOrderResponse.setCancelType(0);
            return cancelOrderResponse;
        } else if ("2".equals(String.valueOf(hoRoom.getCancelPolicyType()))) { // 限时取消
            if (hoHotel.getLastCancelTime().after(Calendar.getInstance().getTime())) {
                cancelOrderResponse.setCanCancelFlag(true);
                cancelOrderResponse.setCancelType(0);
            }
            return ladderCancelDeal(cancelOrderResponse, cancelOrderRequestBo.getOrderId());
        } else if ("4".equals(String.valueOf(hoRoom.getCancelPolicyType()))) {// 阶梯取消
            List<HoOrderCancelRule> orderCancelRuleList =
                hoOrderCancelRuleLoader.selectByOrderId(cancelOrderRequestBo.getOrderId());
            // CancelType 0 是免费 1 是扣全额 2 扣部分
            if (CollectionUtils.isEmpty(orderCancelRuleList)) {
                cancelOrderResponse.setCanCancelFlag(true);
                cancelOrderResponse.setCancelType(0);
                return cancelOrderResponse;
            }
            return ladderCancelDeal(cancelOrderResponse, cancelOrderRequestBo.getOrderId());
        }
        return cancelOrderResponse;
    }

    public CancelOrderQueryResponse cancelOrderQuery(CancelOrderRequestBo request) {
        CancelOrderQueryResponse queryResponse = new CancelOrderQueryResponse();
        queryResponse.setSuccess(false);
        Long orderId = request.getOrderId();
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        if (StringUtils.isBlank(hoOrder.getSupplierOrderId())) {
            return queryResponse;
        }
        request.setOrgId(hoOrder.getCorpId());
        if (StringUtils.isBlank(request.getUid())) {
            request.setUid(hoOrder.getUid());
        }
        SupplierProductBo supplierProduct = commonService.listSupplierProduct(request.getOrgId(),
            hoOrder.getSupplierCode(), hotelOperatorTypeConfig.getCancelOrderQuery(), hoOrder.getCorpPayType());
        if (supplierProduct == null) {
            return queryResponse;
        }
        String supplierCode = supplierProduct.getSupplierCode();
        String url = supplierProduct.getProductUrl();
        String userKey = supplierProduct.getUserKey();
        SupplierCancelOrderRequest supplierCancelOrderRequest = new SupplierCancelOrderRequest();
        supplierCancelOrderRequest.setOrderId(hoOrder.getSupplierOrderId());
        supplierCancelOrderRequest.setUid(hoOrder.getSupplierUid());
        supplierCancelOrderRequest.setCorpID(hoOrder.getSupplierCorpId());
        supplierCancelOrderRequest.setSid(hoOrder.getUid());
        String responseResult = null;
        try {
            responseResult =
                commonService.doPostJSON(supplierCode, OrderMonitorService.CANCEL_ORDER_QUERY, url, userKey,
                    JsonUtils.toJsonString(supplierCancelOrderRequest));
        } catch (IOException e) {
            log.error("调用供应商取消订单异常{}", e);
            if (e instanceof SocketTimeoutException) {
                orderMonitorService.saveGenOrderMonitor(String.valueOf(hoOrder.getOrderId()), MonitorTypeEnums.IO,
                    OrderMonitorService.CANCEL_ORDER_QUERY);
            } else {
                orderMonitorService.saveGenOrderMonitor(String.valueOf(hoOrder.getOrderId()), MonitorTypeEnums.IE,
                    OrderMonitorService.CANCEL_ORDER_QUERY);
            }
            return queryResponse;
        }
        if (StringUtils.isNotBlank(responseResult)) {
            CancelOrderQueryResponseTypeVo responseTypeVo =
                JsonUtils.parse(responseResult, CancelOrderQueryResponseTypeVo.class);
            log.info("取消问询结果返回:{}", JsonUtils.toJsonString(responseTypeVo));
            CancelOrderStatus status = responseTypeVo.getStatus();
            queryResponse.setSuccess(status.getSuccess());
            queryResponse.setResponseTypeVo(responseTypeVo);
            if (Boolean.FALSE.equals(status.getSuccess())) {
                orderMonitorService.saveOrderMonitor(String.valueOf(hoOrder.getOrderId()),
                    MonitorTypeEnums.IE, MonitorLevelEnums.GEN,
                    JsonUtils.toJsonString(responseTypeVo),
                    status.getErrorCode().toString(), status.getErrorMessage(),
                    OrderMonitorService.CANCEL_ORDER_QUERY);
            }
        }
        return queryResponse;
    }

    public StandardCancelOrderInquiryResponse cancelOrderQueryNew(CancelOrderRequestBo request) {

        Long orderId = request.getOrderId();
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        request.setOrgId(hoOrder.getCorpId());
        if (StringUtils.isBlank(request.getUid())) {
            request.setUid(hoOrder.getUid());
        }

        StandardCancelOrderInquiryRequest standardCancelOrderInquiryRequest = new StandardCancelOrderInquiryRequest();
        standardCancelOrderInquiryRequest.setSupplierCode(hoOrder.getSupplierCode());
        standardCancelOrderInquiryRequest.setCompanyCode(request.getOrgId());
        standardCancelOrderInquiryRequest.setCorpPayType(hoOrder.getCorpPayType());

        //标准字段
        standardCancelOrderInquiryRequest.setOrderID(hoOrder.getSupplierOrderId());
        standardCancelOrderInquiryRequest.setSupplierUid(hoOrder.getSupplierUid());
        Map<String, Object> additionalInformationMap = new HashMap<>();
        //美亚使用
        additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_SID, hoOrder.getUid());
        //标准实现类中使用
        additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_ORDER_RESOURCE, hoOrder.getOrderResource());
        additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_ORDER_ID, hoOrder.getOrderId());
        standardCancelOrderInquiryRequest.setAdditionalInformationMap(additionalInformationMap);

        return supplierSoaClient.cancelOrderInquiry(standardCancelOrderInquiryRequest);
    }

    public CancelOrderDetailResponseVo cancelOrderDetail(Long orderId) {
        CancelOrderDetailResponseVo response = new CancelOrderDetailResponseVo();
        if (Objects.isNull(orderId)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.ORDER_ID_IS_NULL);
        }
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        if (Objects.isNull(hoOrder.getSupplierOrderId())) { // 直连场景可能存在供应商订单号为空 即订单还未确认也不存在取消也不会有取消详情
            return response;
        }

        // 查询db
        HoOrderCancelForm hoOrderCancelForm = hoOrderCancelFormLoader.selectNewestCancelFormByOrderId(orderId);
        if (Objects.isNull(hoOrderCancelForm)) {
            return response;
        }

        List<HoOrderCancelFormStatusRecord> hoOrderCancelFormStatusRecordList =
            hoOrderCancelFormStatusRecordLoader.listByCancelFormId(hoOrderCancelForm.getId());
        response.setFormId(hoOrderCancelForm.getSupplierFormId());
        response.setStatus(hoOrderCancelForm.getStatus());
        Optional.ofNullable(CancelFormStatusEnum.getByCode(hoOrderCancelForm.getStatus()))
            .ifPresent(statusEnum -> response.setStatusDesc(statusEnum.getDesc()));
        response.setReasonDesc(hoOrderCancelForm.getReasonDesc());
        List<CancelOrderStatusItem> cancelOrderStatusItemList = hoOrderCancelFormStatusRecordList.stream().map(item -> {
            CancelOrderStatusItem statusItem = new CancelOrderStatusItem();
            statusItem.setStatus(item.getStatus());
            Optional.ofNullable(CancelFormStatusEnum.getByCode(item.getStatus()))
                .ifPresent(statusEnum -> statusItem.setStatusDesc(statusEnum.getDesc()));
            statusItem.setStatusTime(DateUtil.dateToString(item.getHandleTime(), DateUtil.DF_YMD_HMS));
            return statusItem;
        }).collect(Collectors.toList());
        response.setStatusItemList(cancelOrderStatusItemList);

        //StandardCancelOrderDetailRequest cancelOrderDetailRequest = new StandardCancelOrderDetailRequest();
        //cancelOrderDetailRequest.setCompanyCode(hoOrder.getCorpId());
        //cancelOrderDetailRequest.setCorpPayType(hoOrder.getCorpPayType());
        //cancelOrderDetailRequest.setSupplierCode(hoOrder.getSupplierCode());
        //
        //cancelOrderDetailRequest.setOrderID(hoOrder.getSupplierOrderId());
        //
        //StandardCancelOrderDetailResponse standardCancelOrderDetailResponse =
        //    supplierSoaClient.cancelOrderDetail(cancelOrderDetailRequest);
        //log.info("酒店取消详情结果返回:{}", JsonUtils.toJsonString(standardCancelOrderDetailResponse));
        //List<StandardCancelOrderDetailResponse.FormDetail> detailList =
        //    standardCancelOrderDetailResponse.getFormDetailList();
        //if (CollectionUtils.isNotEmpty(detailList)) {
        //
        //    StandardCancelOrderDetailResponse.FormDetail applyDetail = detailList.stream()
        //        .filter(i -> "apply".equals(i.getFormType())).sorted(Comparator
        //            .comparing(StandardCancelOrderDetailResponse.FormDetail::getCreateTime).reversed())
        //        .findFirst().orElse(new StandardCancelOrderDetailResponse.FormDetail());
        //
        //    List<StandardCancelOrderDetailResponse.StatusItem> statusItemList = applyDetail.getStatusItemList();
        //    if (CollectionUtils.isNotEmpty(statusItemList)) {
        //        List<CancelOrderStatusItem> collect = statusItemList.stream()
        //            .sorted(Comparator.comparing(StandardCancelOrderDetailResponse.StatusItem::getStatus)).map(i -> {
        //                CancelOrderStatusItem item = new CancelOrderStatusItem();
        //                item.setStatus(i.getStatus());
        //                item.setStatusDesc(Optional.ofNullable(CancelOrderDetailEnum.getEnum(i.getStatus()))
        //                    .map(CancelOrderDetailEnum::getDesc).orElse(StringUtils.EMPTY));
        //                item.setStatusTime(i.getStatusTime());
        //                return item;
        //            }).collect(Collectors.toList());
        //        response.setFormId(applyDetail.getFormID());
        //        response.setReasonDesc(Optional.ofNullable(CancelReasonEnums.getByType(applyDetail.getReasonCode()))
        //            .map(CancelReasonEnums::getDesc).orElse(StringUtils.EMPTY));
        //        response.setStatus(applyDetail.getStatus());
        //        response.setStatusDesc(Optional.ofNullable(CancelOrderDetailEnum.getEnum(applyDetail.getStatus()))
        //            .map(CancelOrderDetailEnum::getDesc).orElse(StringUtils.EMPTY));
        //        response.setStatusItemList(collect);
        //    }
        //}
        return response;
    }

    private CancelOrderInitResponse ladderCancelDeal(CancelOrderInitResponse cancelOrderResponse, Long orderId) {
        List<HoOrderCancelRule> orderCancelRuleList = hoOrderCancelRuleLoader.selectByOrderId(orderId);
        Date now = Calendar.getInstance().getTime();
        if (CollectionUtils.isNotEmpty(orderCancelRuleList)) {
            for (HoOrderCancelRule hoOrderCancelRule : orderCancelRuleList) {
                if (hoOrderCancelRule.getStartDeductTime().before(now)
                    && hoOrderCancelRule.getEndDeductTime().after(now)) {
                    if (CancelOrderRuleEnum.FREE.getType().equals(hoOrderCancelRule.getDeductionType())) {
                        cancelOrderResponse.setCanCancelFlag(true);
                        cancelOrderResponse.setCancelType(0);
                        return cancelOrderResponse;
                    } else if (CancelOrderRuleEnum.LADDER.getType().equals(hoOrderCancelRule.getDeductionType())) {
                        cancelOrderResponse.setCanCancelFlag(true);
                        cancelOrderResponse.setAmount(hoOrderCancelRule.getAmount());
                        BigDecimal bigDecimal = hoOrderCancelRule.getDeductionRatio();
                        cancelOrderResponse.setCancelType(2);
                        if (bigDecimal.compareTo(BigDecimal.ONE) == 0) {
                            cancelOrderResponse.setCancelType(1);
                        }
                        cancelOrderResponse.setPercentage(bigDecimal.multiply(new BigDecimal(100)) + "%");
                        return cancelOrderResponse;
                    } else if (CancelOrderRuleEnum.CANNOT.getType().equals(hoOrderCancelRule.getDeductionType())) {
                        cancelOrderResponse.setCanCancelFlag(false);
                        return cancelOrderResponse;
                    }
                }
            }
        }
        return cancelOrderResponse;
    }

    public CancelOrderResponseBo cancelSupplierOrder(CancelOrderRequestBo cancelOrderRequestBo, HoOrder hoOrder) {
        HoHotel hoHotel = hoHotelLoader.selectByOrderId(hoOrder.getOrderId());
        CancelOrderResponseBo cancelOrderResponseBo = new CancelOrderResponseBo();
        if (StringUtils.isBlank(hoOrder.getSupplierOrderId())) {
            cancelOrderResponseBo.setSuccess(true);
            return cancelOrderResponseBo;
        }
        // 查询URL

        if (StringUtils.isBlank(cancelOrderRequestBo.getUid())) {
            cancelOrderRequestBo.setUid(hoOrder.getUid());
        }

        StandardCancelOrderRequest request = new StandardCancelOrderRequest();
        request.setCompanyCode(cancelOrderRequestBo.getOrgId());
        request.setSupplierCode(hoOrder.getSupplierCode());
        request.setCorpPayType(hoOrder.getCorpPayType());

        request.setOrderID(hoOrder.getSupplierOrderId());
        request.setCorpID(hoOrder.getSupplierCorpId());
        request.setCancelReason(cancelOrderRequestBo.getCancelReason());
        request.setApplyCancelFlag(cancelOrderRequestBo.getApplyCancelFlag());
        request.setCancelType(Objects.nonNull(request.getApplyCancelFlag()) && request.getApplyCancelFlag() ? "CANCELORDER" : "C");
        request.setCancelCategory(Objects.nonNull(request.getApplyCancelFlag()) && request.getApplyCancelFlag() ? "BOOKINGERROR" : "PCANCEL");

        Map<String, Object> requestAdditionalInformationMap = new HashMap<>();
        requestAdditionalInformationMap.put(SupplierConstant.CancelOrder.REQUEST_ADDITIONAL_MAP_KEY_UID, hoOrder.getSupplierUid());
        requestAdditionalInformationMap.put(SupplierConstant.CancelOrder.REQUEST_ADDITIONAL_MAP_KEY_SID, hoOrder.getUid());
        requestAdditionalInformationMap.put(SupplierConstant.CancelOrder.REQUEST_ADDITIONAL_MAP_KEY_ORDER_TYPE,hoHotel.getHotelType());
        request.setAdditionalInformationMap(requestAdditionalInformationMap);
        try {
            StandardCancelOrderResponse response = supplierSoaClient.cancelOrder(request);
            // 取消失败
            if (Objects.isNull(response) || SupplierConstant.CancelStatus.CANCEL_STATUS_CANCEL_FAILED.equals(response.getCancelStatus())){
                throw new CorpBusinessException(HotelResponseCodeEnum.HOTEL_CANCEL_FAILED);
            }
            // 异步取消-取消中
            if (SupplierConstant.CancelStatus.CANCEL_STATUS_CANCELLING.equals(response.getCancelStatus())){
                cancelOrderResponseBo.setErrorCode(String.valueOf(HotelResponseCodeEnum.HOTEL_CANCEL_NEED_SUPPLIER_CONFIRM.code()));
                cancelOrderResponseBo.setMsg(HotelResponseCodeEnum.HOTEL_CANCEL_NEED_SUPPLIER_CONFIRM.message());
                // 数据库更新 异步取消标识
                hoOrder.setAsyncCancel(Boolean.TRUE);
                boolean updateAsyncCancel = hoOrderLoader.updateAsyncCancel(hoOrder.getOrderId(), Boolean.TRUE);
                log.info("异步取消标识更新结果：" + updateAsyncCancel);
            }
            cancelOrderResponseBo.setSuccess(Boolean.TRUE);
        } catch (CorpBusinessException e){
            log.error("酒店取消失败", e);
            cancelOrderResponseBo.setSuccess(false);
            cancelOrderResponseBo.setErrorCode(e.getResultCode().toString());
            cancelOrderResponseBo.setMsg(e.getMessage());
        }
        return cancelOrderResponseBo;
    }

    /**
     * 审批驳回取消订单
     */
    public void approvalRejectCancelOrder(Long orderId) {
        log.info("审批驳回取消订单orderId:{}", orderId);
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        HoRoom hoRoom = hoRoomLoader.selectByOrderId(orderId);
        HoHotel hoHotel = hoHotelLoader.selectByOrderId(orderId);
        List<HoPassenger> hoPassengers = hoPassengerLoader.selectByOrderId(orderId);
        CancelOrderRequestBo cancelOrderRequestBo = new CancelOrderRequestBo();
        cancelOrderRequestBo.setOrderId(orderId);
        cancelOrderRequestBo.setOrgId(hoOrder.getCorpId());
        // 更新订单为已取消
        hoOrderLoader.updateOrderStatus(orderId, null, OrderStatusEnum.CA.getCode());
        // 写入取消原因
        hoOrderLoader.updateCancelReason(orderId, hoOrder.getOrderStatus());

        hoOrderLoader.updateCancelSource(orderId, FlightCancelTypeEnum.R.getCode());

        orderStatusProducer
            .sendOrderStatusMsg(OcUtils.initOcReq(orderId, OrderStatusEnum.CA.getCode(), null, OrderTypeEnum.HN));

        // 状态修改
        CompletableFuture.runAsync(() -> {
            CancelOrderResponseBo cancelOrderResponseBo = cancelSupplierOrder(cancelOrderRequestBo, hoOrder);
            log.info("审批驳回取消调供应商接口返回值:{}", JsonUtils.toJsonString(cancelOrderResponseBo));
        }, basicThreadPoolExecutor);
        sendMessage(cancelOrderRequestBo, orderId, hoOrder, hoRoom, hoHotel, hoPassengers);
        // 回滚出差申请单有效性
        rollbackApplyTrip(hoOrder, hoRoom, hoHotel, hoPassengers, ApplyTripStockOrderStatus.SUBMIT_REJECT);
        CompletableFuture.runAsync(() -> addLog(cancelOrderRequestBo.getCancelType(), orderId, hoOrder.getUid()),
            basicThreadPoolExecutor);
    }

    public CancelReasonResponse saveCancelReason(CancelReasonRequest request) {
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(request.getOrderId());

        CancelReasonResponse response = new CancelReasonResponse();
        // 已经存在不做更新
        if (StringUtils.isNotBlank(hoOrder.getCancelReasonCode())) {
            response.setSuccess(true);
            return response;
        }
        boolean result = hoOrderLoader.updateCancelReason(request.getOrderId(), request.getCancelReasonCode(),
            request.getCancelReasonDesc());
        response.setSuccess(result);
        return response;
    }

    public CancelReasonResponse saveCancelSource(CancelReasonRequest request) {
        CancelReasonResponse response = new CancelReasonResponse();
        boolean result = hoOrderLoader.updateCancelSource(request.getOrderId(),
                request.getCancelSource());
        response.setSuccess(result);
        return response;
    }

    public AsyncCancelResponse updateAsyncCancel(AsyncCancelRequest request) {
        AsyncCancelResponse response = new AsyncCancelResponse();
        boolean result = hoOrderLoader.updateAsyncCancel(request.getOrderId(), request.getAsyncCancel());
        log.info("异步取消标识更新结果：" + result);
        response.setSuccess(result);
        return response;
    }

    public Boolean syncCancelFormDetail(Long orderId) {
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);

        //获取供应商订单取消详情
        StandardCancelOrderDetailRequest cancelOrderDetailRequest = new StandardCancelOrderDetailRequest();
        cancelOrderDetailRequest.setCompanyCode(hoOrder.getCorpId());
        cancelOrderDetailRequest.setCorpPayType(hoOrder.getCorpPayType());
        cancelOrderDetailRequest.setSupplierCode(hoOrder.getSupplierCode());
        cancelOrderDetailRequest.setOrderID(hoOrder.getSupplierOrderId());

        StandardCancelOrderDetailResponse standardCancelOrderDetailResponse = supplierSoaClient.cancelOrderDetail(cancelOrderDetailRequest);
        if (Objects.isNull(standardCancelOrderDetailResponse) || CollectionUtils.isEmpty(standardCancelOrderDetailResponse.getFormDetailList())) {
            log.info("未查询到相应的订单取消详情");
            return Boolean.FALSE;
        }

        // 同步取消单 目前逻辑 由于取消时供应商未返回供应商申请单据号 无法匹配到对应的平台取消单据 只能通过时间倒序获取最新的与平台最新取消单据(supplier_form_id为空)匹配
        // 后续需求变更 等产品推动供应商完成申请单据号的返回 通过供应商申请单据号匹配
        List<StandardCancelOrderDetailResponse.FormDetail> formDetailList = standardCancelOrderDetailResponse.getFormDetailList();
        formDetailList.sort(Comparator.comparing(StandardCancelOrderDetailResponse.FormDetail::getCreateTime).reversed());
        Optional<StandardCancelOrderDetailResponse.FormDetail> newestFormDetailOpt = formDetailList.stream().findFirst();

        newestFormDetailOpt.ifPresent(newestFormDetail -> {
            log.info("最新的供应商取消单据详情：{}", JsonUtils.toJsonString(newestFormDetail));
            HoOrderCancelForm newestHoOrderCancelForm = hoOrderCancelFormLoader.getNewestHoOrderCancelFormBySupplierOrderId(newestFormDetail.getOrderID());
            log.info("最新的平台取消单据详情：{}", JsonUtils.toJsonString(newestHoOrderCancelForm));
            if (Objects.nonNull(newestHoOrderCancelForm)) {
                //更新单据
                newestHoOrderCancelForm.setSupplierFormId(newestFormDetail.getFormID());
                newestHoOrderCancelForm.setStatus(newestFormDetail.getStatus());
                newestHoOrderCancelForm.setResultCode(newestFormDetail.getResultCode());
                newestHoOrderCancelForm.setResultDesc(newestFormDetail.getResultDesc());
                hoOrderCancelFormLoader.updateByPrimaryKeySelective(newestHoOrderCancelForm);
                //更新单据状态明细 删除原有数据 以供应商数据为准
                int deleteCount = hoOrderCancelFormStatusRecordLoader.deleteByCancelFormId(newestHoOrderCancelForm.getId());
                if (deleteCount > 0) {
                    List<StandardCancelOrderDetailResponse.StatusItem> statusItemList = newestFormDetail.getStatusItemList();
                    statusItemList.stream().map(statusItem -> {
                        HoOrderCancelFormStatusRecord hoOrderCancelFormStatusRecord = new HoOrderCancelFormStatusRecord();
                        hoOrderCancelFormStatusRecord.setCancelFormId(newestHoOrderCancelForm.getId());
                        hoOrderCancelFormStatusRecord.setStatus(statusItem.getStatus());
                        hoOrderCancelFormStatusRecord.setHandleTime(DateUtil.stringToDate(statusItem.getStatusTime(), DateUtil.DF_YMD_HMS));
                        hoOrderCancelFormStatusRecord.setViewSort(statusItem.getStatusSortIndex());
                        return hoOrderCancelFormStatusRecord;
                    }).forEach(hoOrderCancelFormStatusRecordLoader::insertSelective);
                }
            }
        });

        return Boolean.TRUE;
    }
}
