package com.corpgovernment.hotel.product.external.convert;

import com.corpgovernment.hotel.product.external.dto.order.modify.*;
import org.mapstruct.Mapper;

import org.mapstruct.Mapping;

/**
 * 订单修改转换
 *
 * <AUTHOR> ding<PERSON>an
 * @date : 2024/4/11 11:11
 * @since : 1.0
 */
@Mapper(componentModel = "spring")
public interface OrderModificationConvert {

    /**
     * 标准订单修改问询请求转成携程订单修改问询请求
     * 
     * @param request
     * @return
     */
    @Mapping(source = "orderID", target = "orderId")
    CtripOrderModificationInquiryRequest
        convertToCtripOrderModificationInquiryRequest(StandardOrderModificationInquiryRequest request);

    /**
     * 标准订单修改问询请求转成美亚订单修改问询请求
     * 
     * @param request
     * @return
     */
    @Mapping(source = "orderID", target = "orderId")
    MeiyaOrderModificationInquiryRequest
        convertToMeiyaOrderModificationInquiryRequest(StandardOrderModificationInquiryRequest request);

    /**
     * 携程订单修改问询响应转成标准订单修改问询响应
     * 
     * @param response
     * @return
     */
    StandardOrderModificationInquiryResponse convertToStandardOrderModificationInquiryResponse(
        CtripOrderModificationInquiryResponse response);

    /**
     * 美亚订单修改响应转成标准订单修改响应
     * 
     * @param response
     * @return
     */
    StandardOrderModificationInquiryResponse convertToStandardOrderModificationInquiryResponse(
        MeiyaOrderModificationInquiryResponse response);

    /**
     * 标准订单修改请求转成携程订单修改请求
     *
     * @param request
     * @return
     */
    @Mapping(source = "orderID", target = "orderId")
    CtripOrderModificationRequest
        convertToCtripOrderModificationRequest(StandardOrderModificationRequest request);

    /**
     * 标准订单修改请求转成美亚订单修改请求
     *
     * @param request
     * @return
     */
    @Mapping(source = "orderID", target = "orderId")
    MeiyaOrderModificationRequest
        convertToMeiyaOrderModificationRequest(StandardOrderModificationRequest request);

    /**
     * 携程订单修改响应转成标准订单修改响应
     *
     * @param response
     * @return
     */
    @Mapping(source = "applyId", target = "applyFormID")
    StandardOrderModificationResponse convertToStandardOrderModificationResponse(
        CtripOrderModificationResponse response);

    /**
     * 美亚订单修改响应转成标准订单修改响应
     *
     * @param response
     * @return
     */
    @Mapping(source = "applyId", target = "applyFormID")
    StandardOrderModificationResponse convertToStandardOrderModificationResponse(
        MeiyaOrderModificationResponse response);

    /**
     * 标准订单修改详情请求转成携程订单修改详情请求
     *
     * @param request
     * @return
     */
    @Mapping(source = "applyFormID", target = "applyId")
    @Mapping(source = "orderID", target = "orderId")
    CtripOrderModificationDetailRequest
        convertToCtripOrderModificationDetailRequest(StandardOrderModificationDetailRequest request);

    /**
     * 标准订单修改详情请求转成美亚订单修改详情请求
     *
     * @param request
     * @return
     */
    @Mapping(source = "applyFormID", target = "applyId")
    @Mapping(source = "orderID", target = "orderId")
    MeiyaOrderModificationDetailRequest
        convertToMeiyaOrderModificationDetailRequest(StandardOrderModificationDetailRequest request);


    /**
     * 携程订单修改详情响应转成标准订单修改详情响应
     * @param applyFormDetail
     * @return
     */
    @Mapping(source = "applyId", target = "applyFormID")
    @Mapping(source = "orderId", target = "orderID")
    StandardOrderModificationDetailResponse.ApplyFormDetail convertApplyFormDetail(
        CtripOrderModificationDetailResponse.ApplyFormDetail applyFormDetail);

    /**
     * 携程订单修改详情响应转成标准订单修改详情响应
     *
     * @param response
     * @return
     */
    StandardOrderModificationDetailResponse convertToStandardOrderModificationDetailResponse(
        CtripOrderModificationDetailResponse response);


    /**
     * 美亚订单修改详情响应转成标准订单修改详情响应
     * 
     * @param applyFormDetail
     * @return
     */
    @Mapping(source = "applyId", target = "applyFormID")
    @Mapping(source = "orderId", target = "orderID")
    StandardOrderModificationDetailResponse.ApplyFormDetail convertMeiyaApplyFormDetail(
        MeiyaOrderModificationDetailResponse.ApplyFormDetail applyFormDetail);

    /**
     * 美亚订单修改详情响应转成标准订单修改详情响应
     *
     * @param response
     * @return
     */
    StandardOrderModificationDetailResponse convertToStandardOrderModificationDetailResponse(
        MeiyaOrderModificationDetailResponse response);

    /**
     * 标准订单修改间夜查询请求转成携程订单修改间夜查询请求
     *
     * @param request
     * @return
     */
    @Mapping(source = "orderID", target = "orderId")
    CtripOrderModifiableRoomNightQueryRequest
    convertToCtripOrderModifiableRoomNightQueryRequest(StandardOrderModifiableRoomNightQueryRequest request);


    /**
     * 携程订单修改间夜查询响应转成标准订单修改间夜查询响应
     *
     * @param response
     * @return
     */
    StandardOrderModifiableRoomNightQueryResponse convertToStandardOrderModifiableRoomNightQueryResponse(
            CtripOrderModifiableRoomNightQueryResponse response);

}
