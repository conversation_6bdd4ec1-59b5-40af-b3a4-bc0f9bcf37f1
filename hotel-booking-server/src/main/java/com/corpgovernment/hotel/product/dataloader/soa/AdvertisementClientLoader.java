package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.advertisement.soa.AdvertisementClient;
import com.corpgovernment.api.advertisement.vo.AdsInfoVo;
import com.corpgovernment.common.base.JSONResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdvertisementClientLoader {

	@Autowired
	private AdvertisementClient advertisementClient;

	/**
	 * 展示广告
	 *
	 * @param request
	 * @return
	 */
	public List<AdsInfoVo> adShow(AdsInfoVo request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<AdsInfoVo>> result = advertisementClient.adShow(request);
		if (result == null || result.getData() == null) {
			log.error("展示广告异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	public List<AdsInfoVo> getAdsListByChannelAndLocation(int channel, int location) {
		JSONResult<List<AdsInfoVo>> result = advertisementClient.getAdsListByChannelAndLocation(channel, location);
		if (result == null || result.getData() == null) {
			log.error("展示广告异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}
}
