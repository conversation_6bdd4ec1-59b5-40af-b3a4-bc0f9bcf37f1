package com.corpgovernment.hotel.product.model.ctrip.v2;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RoomItemV2 implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 时间信息
	 */
	public TimeInformationV2 timeInformation;

	/**
	 * 费用信息
	 */
	public PriceInformationV2 priceInformation;

	/**
	 * 限制信息
	 */
	public LimitInformationV2 limitInformation;

	/**
	 * 取消政策
	 */
	public List<LadderDeductionInfoV2> ladderDeductionInfoList;

	/**
	 * 是否接受文本备注，true时才可在下单接口填入备注
	 */
	public Boolean receiveTextRemark;

	/**
	 * 可选备注列表
	 */
	public List<RemarkV2> remarkList;

	/**
	 * 酒店特别提示
	 */
	public List<String> specialTipList;

	/**
	 * 开票限制(携程开票/酒店开票/携程酒店都可开票/不可开票) NotSupported 不支持开票;ByCtrip/ByVendor 携程开票	ByHotel酒店开票ByBoth
	 */
	public String invoiceLimitType;

	/**
	 * 房型可开的增值税发票类型。None无；Special增值税专用发票；Ordinary增值税普通发票
	 */
	public String availableVATInvoiceType;

	/**
	 * 支持的发票类型列表
	 */
	public List<String> supportInvoiceTypeList;

	/**
	 * 积分信息
	 */
	public BonusPointInfo bonusPointInfo;
	/**
	 * 供应商下单透传额外信息
	 */
	public List<String> additionalSupplierInfo;
	
	/**
	 * 特惠房型信息
	 */
	private SpecialOfferRoomInfo specialOfferRoomInfo;
	
	/**
	 * 餐食信息
	 */
	private RoomMealInfo roomMealInfo;
	
	private List<ServiceChargeInfo> serviceChargeInfoList;
	
	private Boolean hourlyRoom;
	
	private HourRoomDetail hourRoomDetail;
	
	@Data
	public static class HourRoomDetail {
		
		private Integer earliestArriveTime;
		
		private Integer latestLeaveTime;
		
		private Integer duration;
		
	}
	
	@Data
	public static class ServiceChargeInfo {
		
		private String roomPaymentMethod;
		
		private Price customChargePrice;
		
		private Price customChargePricePerRoomNights;
		
		private List<ServiceChargeDetailInfo> serviceChargeDetailInfoList;
		
	}
	
	@Data
	public static class ServiceChargeDetailInfo {
		
		private String chargeType;
		
		private String chargingStrategy;
		
		private Price customChargePricePerUnit;
		
	}
	
	@Data
	public static class Price {
		
		private BigDecimal price;
		
		private String currency;
		
	}
	
	@Data
	public static class RoomMealInfo {
		
		private Integer mealType;
		
	}
	
	@Data
	public static class SpecialOfferRoomInfo {
		
		/**
		 * 支持的证件类型
		 * IdentityCard（身份证）；Passport（护照）；TaiwaneseCertificate（台胞证）；TaiwanPass（大陆居民往来台湾通行证）；HKAndMacauPass（港澳通行证）；HometownPermit（回乡证）
		 */
		private List<String> supportCertificateType;
		
	}

	/**
	 * 取消惩罚政策
	 */
	private CancelPenaltyModel cancelPenalties;

	@Data
	public static class BonusPointInfo {
		private Boolean bonusPointRoom;
		private String bonusPointCode;
	}
}
