package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.platform.bo.CreateDirectRefundOrderRequest;
import com.corpgovernment.api.platform.soa.PayClient;
import com.corpgovernment.api.platform.soa.cancelPayemntBill.CancelPayemntBillRequest;
import com.corpgovernment.api.platform.soa.paymentbill.CreatePaymentBillRequest;
import com.corpgovernment.api.platform.soa.paymentbill.CreatePaymentBillResponse;
import com.corpgovernment.api.platform.soa.paymentbill.PaymentBillDetailRequest;
import com.corpgovernment.api.platform.soa.paymentbill.PaymentBillDetailResponse;
import com.corpgovernment.api.platform.soa.refund.FlightPaymentRefundRequest;
import com.corpgovernment.common.base.JSONResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PayClientLoader {

	@Autowired
	private PayClient payClient;

	/**
	 * 创建支付单
	 *
	 * @param
	 * @return
	 */
	public CreatePaymentBillResponse createPaymentBillRequest(CreatePaymentBillRequest request) {
		if (request == null) {
			return null;
		}
		JSONResult<CreatePaymentBillResponse> result = payClient.createPaymentBillRequest(request);
		if (result == null || result.getData() == null) {
			log.error("创建支付单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 获取支付单详情
	 *
	 * @param orderId
	 * @return
	 */
	public List<PaymentBillDetailResponse> getPaymentBillDetailList(Long orderId) {
		if (orderId == null || orderId <= 0) {
			return new ArrayList<>();
		}
		PaymentBillDetailRequest request = new PaymentBillDetailRequest();
		request.setOrderId(String.valueOf(orderId));
		JSONResult<List<PaymentBillDetailResponse>> result = payClient.getPaymentBillDetailList(request);
		if (result == null || result.getData() == null) {
			log.error("创建支付单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}


	/**
	 * 获取支付单详情
	 *
	 * @param request
	 * @return
	 */
	public List<PaymentBillDetailResponse> getPaymentBillDetailList(PaymentBillDetailRequest request) {
		if (request == null) {
			return new ArrayList<>();
		}
		JSONResult<List<PaymentBillDetailResponse>> result = payClient.getPaymentBillDetailList(request);
		if (result == null || result.getData() == null) {
			log.error("创建支付单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 创建机票退款单
	 *
	 * @param request
	 * @return
	 */
	public boolean createFlightPaymentRefund(FlightPaymentRefundRequest request) {
		if (request == null) {
			return false;
		}
		JSONResult result = payClient.createPaymentRefundByPayType(request);
		if (result == null || !result.isSUCCESS()) {
			log.error("创建支付单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return false;
		}
		return true;
	}

	/**
	 * 补录创支付单
	 *
	 * @param request
	 * @return
	 */
	public Long supplementPaymentBill(CreatePaymentBillRequest request) {
		if (request == null) {
			return null;
		}
		JSONResult<CreatePaymentBillResponse> result = payClient.supplementPaymentBill(request);
		if (result == null || result.getData() == null) {
			log.error("创建支付单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData().getBillId();

	}

	/**
	 * 补录创退款单
	 * @param request
	 * @return
	 */
	public Long createDirectRefundOrder(CreateDirectRefundOrderRequest request) {
		if (request == null) {
			return null;
		}
		JSONResult<Long> result = payClient.createDirectRefundOrder(request);
		if (result == null || result.getData() == null) {
			log.error("创建退款单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();

	}

    /**
     * 取消支付单
     *
     * @param request
     * @return
     */
    public boolean cancelPaymentBill(CancelPayemntBillRequest request) {
        if (request == null) {
            return false;
        }
        JSONResult<Boolean> result = payClient.cancelPaymentBill(request);
        if (result == null || result.getData() == null) {
            log.error("取消支付单异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
            return false;
        }
        return result.getData();
    }

}
