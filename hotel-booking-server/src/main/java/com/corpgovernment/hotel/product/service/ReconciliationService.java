package com.corpgovernment.hotel.product.service;

import com.corpgovernment.api.applytrip.soa.request.GetProjectInfoByTravelerRequest;
import com.corpgovernment.api.applytrip.soa.response.GetProjectInfoByTravelerResponse;
import com.corpgovernment.api.car.product.dto.response.QueryOrderDetailResponseVo;
import com.corpgovernment.api.hotel.product.bo.GetHotelBasicInfoRequestBO;
import com.corpgovernment.api.hotel.product.bo.GetHotelBasicInfoResponseBO;
import com.corpgovernment.api.hotel.product.bo.HotelBasicInfoBO;
import com.corpgovernment.api.hotel.product.bo.reconciliation.GetHotelReconciliationRecordRequestBO;
import com.corpgovernment.api.hotel.product.bo.reconciliation.GetHotelReconciliationRecordResponseBO;
import com.corpgovernment.api.hotel.product.bo.reconciliation.HotelReconciliationRecordBO;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.hotel.product.vo.VerifyHotelEndOrderResponse;
import com.corpgovernment.api.organization.dto.request.CheckAllOrderAmountRequest;
import com.corpgovernment.common.base.AbstractBaseService;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.dto.bill.OrderBillDto;
import com.corpgovernment.common.enums.OrderSourceEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.common.utils.EncryptUtils;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayBillClientLoader;
import com.corpgovernment.hotel.product.dto.HotelOrderBo;
import com.corpgovernment.hotel.product.dto.OrderInfoDTO;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.mapper.HoOrderMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReconciliationService extends AbstractBaseService {
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private PayBillClientLoader payBillClientLoader;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private HoOrderMapper hoOrderMapper;
    /**
     * 订单拆分提供给对账使用
     *
     * @param request
     * @return
     */
	/*public GetHotelReconciliationRecordResponseBO getHotelRecordList(GetHotelReconciliationRecordRequestBO request) {

		try{
			initElkLog();
			// 根据出票时间查询    已完成且已离店和已取消订单
			List<OrderInfoDTO> orderInfoList = hoOrderLoader.getHotelOrderList(request.getStartTime(), request.getEndTime(), request.getOrderIds());
			addElkInfoLog("查询出的订单 %s", orderInfoList);
			// 订单数据解密
			decryptOrder(orderInfoList);
			//处理当前支付单数据
			List<Long> orderIds = orderInfoList.stream().map(OrderInfoDTO::getOrderId).collect(Collectors.toList());
			//根据对账时间查询出时间内得退款单数据
			List<Long> refundOrderId = payBillClientLoader.searchPaymentRefund(request);
			if (CollectionUtils.isEmpty(orderInfoList) && CollectionUtils.isEmpty(refundOrderId)) {
				return new GetHotelReconciliationRecordResponseBO();
			}
			addElkInfoLog("查询出的退款单 %s", refundOrderId);
			refundOrderId = refundOrderId.stream().filter(a -> !orderIds.contains(a)).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(refundOrderId)) {
				List<OrderInfoDTO> refundOrderInfo = hoOrderLoader.getHotelOrderList(null, null, refundOrderId);
				orderInfoList.addAll(refundOrderInfo);
				orderIds.addAll(refundOrderInfo.stream().map(OrderInfoDTO::getOrderId).collect(Collectors.toList()));
			}

			List<PpBillDto> payBillList = payBillClientLoader.searchBillInfo(orderIds);
			Map<Long, List<PpBillDto>> payBillMap = payBillList.stream().collect(Collectors.groupingBy(PpBillDto::getOrderId));
			List<HotelReconciliationRecordBO> recordList = Lists.newArrayList();
			List<Long> finalRefundOrderId = refundOrderId;
			orderInfoList.forEach(orderInfo -> {
				Long orderId = orderInfo.getOrderId();
				List<PpBillDto> billList = Optional.ofNullable(payBillMap.get(orderId)).orElse(new ArrayList<>());
				addElkInfoLog("支付单%s", billList);
				billList = billList.stream().filter(e -> Objects.equals(e.getCorpPayType(), "PUB")).collect(Collectors.toList());
				if (CollectionUtils.isEmpty(billList)) {
					return;
				}
				HotelReconciliationRecordBO record = new HotelReconciliationRecordBO();
				List<HotelReconciliationRecordBO> collect = recordList.stream().filter(a -> Objects.equals(a.getOrderId(), orderId)).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(collect)) {
					record = collect.get(0);
				}

				String tripApplyNo = orderInfo.getTripApplyNo();
				if (StringUtils.isNotBlank(tripApplyNo)){
					GetProjectInfoByTravelerRequest projectRequest = new GetProjectInfoByTravelerRequest();
					projectRequest.setApplyNo(tripApplyNo);
					List<GetProjectInfoByTravelerResponse> projectInfoByTraveler = applyTripClientLoader.getProjectInfoByTraveler(projectRequest);
					record.setProjectNo(projectInfoByTraveler.stream().map(GetProjectInfoByTravelerResponse::getProjectCode).collect(Collectors.joining(",")));
					record.setNoProjectDesc(projectInfoByTraveler.stream().map(GetProjectInfoByTravelerResponse::getNoSelectProjectDesc).collect(Collectors.joining(",")));
				}
				record.setOrderId(orderId);
				record.setCorpId(orderInfo.getCorpId());
				record.setSupplierCode(orderInfo.getSupplierCode());
				record.setPassengerName(orderInfo.getPassengerName());
				record.setConfirmDate(orderInfo.getTicketIssuedTime());
				record.setCheckInDate(orderInfo.getCheckInDate());
				record.setCheckOutDate(orderInfo.getCheckOutDate());
				record.setCityName(orderInfo.getCityName());
				record.setHotelName(orderInfo.getHotelName());
				record.setType("H");
				record.setCostCenterCode(orderInfo.getCostCenterCode());
				record.setCostCenterName(orderInfo.getCostCenterName());
				record.setProjectNo(orderInfo.getProjectNo());
				record.setUid(orderInfo.getUid());
				record.setRoomType(orderInfo.getRoomName());
				record.setStar(orderInfo.getStar());
				record.setNextDay(DateUtil.betweenDay(orderInfo.getCheckInDate(), orderInfo.getCheckOutDate()));
				record.setSupplierOrderId(orderInfo.getSupplierOrderId());
				// 总价
				BigDecimal amount = billList.stream().map(PpBillDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
				if (Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.CA.getCode())) {

					if (finalRefundOrderId.contains(orderId)) {
						addElkInfoLog("非本周期退票单 %s", orderId);
						//退款项:  放价 配送     收款项:  退票费
						BigDecimal deliveryPrice = Optional.ofNullable(orderInfo.getDeliveryPrice()).orElse(BigDecimal.ZERO);
						record.setPrice(BigDecimal.ZERO.subtract(amount.subtract(deliveryPrice)));
						record.setDeliveryFee(BigDecimal.ZERO.subtract(deliveryPrice));
						record.setCancelFee(orderInfo.getLadderAmount());
						record.setTotalAmount(record.getPrice().add(orderInfo.getLadderAmount() == null ? BigDecimal.ZERO : orderInfo.getLadderAmount()));
					} else {
						addElkInfoLog("预定收费 %s", orderId);
						// 取消的情况,只收阶梯取消费
						record.setCancelFee(orderInfo.getLadderAmount());
						record.setTotalAmount(orderInfo.getLadderAmount());
					}
				} else {
					// 非取消，只收配送，房价
					BigDecimal deliveryPrice = Optional.ofNullable(orderInfo.getDeliveryPrice()).orElse(BigDecimal.ZERO);
					record.setPrice(amount.subtract(deliveryPrice));
					record.setDeliveryFee(deliveryPrice);
					record.setTotalAmount(amount);
				}
				recordList.add(record);
			});
			return GetHotelReconciliationRecordResponseBO.create(recordList);
		} finally {
			log.info("getHotelOrderListInfoData:{} {}", System.lineSeparator(), getElkInfoLog());
			clearElkLog();
		}
	}*/


    /**
     * 下单订单  :返回总支付金额
     * 退房订单	  :计算下单金额与退房金额得差价    及  展示扣款原因(退房手续费, 退房服务费)
     * 下单->退房订单	:计算下单金额与退房金额得差价    及  展示扣款原因(退房手续费, 退房服务费)
     *
     * @param request
     * @return
     */
    public GetHotelReconciliationRecordResponseBO getHotelRecordList(GetHotelReconciliationRecordRequestBO request) {

        try {
            initElkLog();
            addElkInfoLog("request： %s", JsonUtils.toJsonString(request));

            if (CollectionUtils.isEmpty(request.getOrderBillDto().getPaymentBillList()) && CollectionUtils.isEmpty(request.getOrderBillDto().getPaymentRefundList())) {
                addElkInfoLog("本时间周期内暂无酒店下单及退店数据 %s->%s", request.getStartTime(), request.getEndTime());
                return new GetHotelReconciliationRecordResponseBO();
            }
            List<OrderBillDto.PaymentBill> paymentBillList = request.getOrderBillDto().getPaymentBillList();
            List<OrderBillDto.PaymentRefund> paymentRefundList = request.getOrderBillDto().getPaymentRefundList();
            List<Long> billOrderId = Optional.ofNullable(paymentBillList).orElse(new ArrayList<>()).stream().map(OrderBillDto.PaymentBill::getOrderId).collect(Collectors.toList());
            List<Long> refundOrderId = Optional.ofNullable(paymentRefundList).orElse(new ArrayList<>()).stream().map(OrderBillDto.PaymentRefund::getOrderId).collect(Collectors.toList());

            List<Long> allOrderId = new ArrayList<>();
            allOrderId.addAll(billOrderId);
            allOrderId.addAll(refundOrderId);
            //查询支付单及退款单得所有订单数据
            List<OrderInfoDTO> orderInfoList = hoOrderLoader.getHotelOrderList(allOrderId);
            addElkInfoLog("查询出的订单 %s", JsonUtils.toJsonString(orderInfoList));
            // 订单数据解密
            decryptOrder(orderInfoList);
            List<HotelReconciliationRecordBO> recordList = Lists.newArrayList();
            orderInfoList.forEach(orderInfo -> {
                HotelReconciliationRecordBO record = new HotelReconciliationRecordBO();
                record.setOrderId(orderInfo.getOrderId());
                record.setCorpId(orderInfo.getCorpId());
                record.setDeptId(orderInfo.getDeptId());
                record.setSupplierCode(orderInfo.getSupplierCode());
                record.setSupplierCorpId(orderInfo.getSupplierCorpId());
                record.setPassengerName(orderInfo.getPassengerName());
                record.setConfirmDate(orderInfo.getTicketIssuedTime());
                record.setCheckInDate(orderInfo.getCheckInDate());
                record.setCheckOutDate(orderInfo.getCheckOutDate());
                record.setCityName(orderInfo.getCityName());
                record.setHotelName(orderInfo.getHotelName());
                record.setHotelType(orderInfo.getHotelType());
                record.setType("H");
                record.setSource(orderInfo.getSource());
                record.setCostCenterCode(orderInfo.getCostCenterCode());
                record.setCostCenterName(orderInfo.getCostCenterName());
                record.setCostCenterRemark(orderInfo.getCostCenterRemark());
                record.setProjectNo(orderInfo.getProjectCode());
                record.setProjectName(orderInfo.getProjectName());
                record.setProjectRemark(orderInfo.getWbsRemark());
                record.setUid(orderInfo.getUid());
                record.setRoomType(orderInfo.getRoomName());
                record.setStar(orderInfo.getStar());
                record.setNextDay(DateUtil.betweenDay(orderInfo.getCheckInDate(), orderInfo.getCheckOutDate()));
                record.setSupplierOrderId(orderInfo.getSupplierOrderId());
                record.setSupplierName(orderInfo.getSupplierName());
                record.setTripApplyno(orderInfo.getTripApplyNo());
                record.setRoomNum(orderInfo.getRoomQuantity());
                record.setPassengerArr(orderInfo.getPassengerArr());
                record.setBookUid(orderInfo.getBookUid());
                record.setBookName(orderInfo.getBookName());
                record.setOrderStatus(orderInfo.getOrderStatus());
                record.setOrderDate(orderInfo.getOrderDate());
                //下单
                if (billOrderId.contains(orderInfo.getOrderId()) && refundOrderId.contains(orderInfo.getOrderId())) {
                    //统一周期内   预定 ==> 退房   只扣除退房手续费
                    record.setCancelFee(orderInfo.getLadderAmount());
                    record.setPrice(orderInfo.getLadderAmount());
                    record.setTotalAmount(orderInfo.getLadderAmount());
                    record.setServiceFee(orderInfo.getServicePrice());
                } else if (billOrderId.contains(orderInfo.getOrderId())) {
                    OrderBillDto.PaymentBill paymentBill = paymentBillList.stream().filter(i -> Objects.equals(orderInfo.getOrderId(), i.getOrderId())).findAny().get();
                    BigDecimal amount = paymentBill.getAmount();
                    //收配送，房价
                    BigDecimal deliveryPrice = Optional.ofNullable(orderInfo.getDeliveryPrice()).orElse(BigDecimal.ZERO);
                    record.setPrice(amount.subtract(deliveryPrice));
                    record.setDeliveryFee(deliveryPrice);
                    record.setTotalAmount(amount);
                    record.setServiceFee(orderInfo.getServicePrice());
                } else if (!billOrderId.contains(orderInfo.getOrderId()) && refundOrderId.contains(orderInfo.getOrderId())) {
                    OrderBillDto.PaymentRefund paymentBill = paymentRefundList.stream().filter(i -> Objects.equals(orderInfo.getOrderId(), i.getOrderId())).findAny().get();
                    BigDecimal amount = paymentBill.getAmount();
                    //不在同一个周期 退房       扣除退票手续费
                    record.setCancelFee(orderInfo.getLadderAmount());
                    record.setPrice(BigDecimal.ZERO.subtract(amount));
                    record.setTotalAmount(BigDecimal.ZERO.subtract(amount));
                    record.setServiceFee(BigDecimal.ZERO);
                }
                if (amount(record.getTotalAmount()).add(amount(record.getCancelFee())).add(amount(record.getDeliveryFee())).add(amount(record.getPrice())).add(Optional.ofNullable(record.getServiceFee()).orElse(BigDecimal.ZERO)).add(amount(record.getOtherFee())).compareTo(BigDecimal.ZERO) != 0) {
                    recordList.add(record);
                }
            });
            return GetHotelReconciliationRecordResponseBO.create(recordList);
        } finally {
            log.info("getHotelOrderListInfoData:{} {}", System.lineSeparator(), getElkInfoLog());
            clearElkLog();
        }
    }

    private BigDecimal amount(BigDecimal bigDecimal) {
        return Optional.ofNullable(bigDecimal)
                .orElse(BigDecimal.ZERO);
    }

    public GetHotelBasicInfoResponseBO getHotelBasicInfo(GetHotelBasicInfoRequestBO request) {
        if (request == null || CollectionUtils.isEmpty(request.getOrderIdList())){
            return null;
        }

        try {
            initElkLog();
            addElkInfoLog("getHotelBasicInfo.request： %s", JsonUtils.toJsonString(request));
            List<OrderInfoDTO> orderInfoList = hoOrderLoader.getHotelOrderList(request.getOrderIdList());
            addElkInfoLog("getHotelBasicInfo.查询出的订单 %s", orderInfoList);
            // 订单数据解密
            decryptOrder(orderInfoList);
            List<HotelBasicInfoBO> recordList = Lists.newArrayList();
            orderInfoList.forEach(orderInfo -> {
                HotelBasicInfoBO record = new HotelBasicInfoBO();
                //设置成本中心、项目号信息
                record.setCostCenterInfo(orderInfo.getCostCenterCode(), orderInfo.getCostCenterName(), orderInfo.getCostCenterRemark());
                record.setProjectInfo(orderInfo.getProjectCode(), orderInfo.getProjectName(), orderInfo.getWbsRemark(), orderInfo.getNoProjectDesc());
                record.setOrderId(orderInfo.getOrderId());
                record.setCorpId(orderInfo.getCorpId());
                record.setDeptId(orderInfo.getDeptId());
                record.setSupplierCode(orderInfo.getSupplierCode());
                record.setSupplierCorpId(orderInfo.getSupplierCorpId());
                record.setPassengerName(orderInfo.getPassengerName());
                record.setConfirmDate(orderInfo.getTicketIssuedTime());
                record.setCheckInDate(orderInfo.getCheckInDate());
                record.setCheckOutDate(orderInfo.getCheckOutDate());
                record.setCityName(orderInfo.getCityName());
                record.setHotelName(orderInfo.getHotelName());
                record.setType("H");
                record.setUid(orderInfo.getUid());
                record.setRoomType(orderInfo.getRoomName());
                record.setStar(orderInfo.getStar());
                record.setNextDay(DateUtil.betweenDay(orderInfo.getCheckInDate(), orderInfo.getCheckOutDate()));
                record.setSupplierOrderId(orderInfo.getSupplierOrderId());
                record.setSupplierName(orderInfo.getSupplierName());
                record.setTripApplyNo(orderInfo.getTripApplyNo());
                record.setRoomNum(orderInfo.getRoomQuantity());
                record.setPassengerArr(orderInfo.getPassengerArr());
                record.setBookUid(orderInfo.getBookUid());
                record.setBookName(orderInfo.getBookName());
                record.setOrderStatus(orderInfo.getOrderStatus());
                record.setPayType(orderInfo.getPayType());
                record.setAmountHigh(orderInfo.getAmountHigh());
                record.setSource(orderInfo.getSource());
                record.setEmployeeType(orderInfo.getEmployeeType());
                record.setCorpPayType(orderInfo.getCorpPayType());
                recordList.add(record);
            });
            GetHotelBasicInfoResponseBO responseBo = new GetHotelBasicInfoResponseBO();
            responseBo.setHotelList(recordList);
            return responseBo;
        } finally {
            log.info("getHotelBasicInfo:{} {}", System.lineSeparator(), getElkInfoLog());
            clearElkLog();
        }
    }

    /**
     * 解密
     *
     * @param hotelOrderList
     */
    private void decryptOrder(List<OrderInfoDTO> hotelOrderList) {
        hotelOrderList.forEach(e -> {
            String passengerName = e.getPassengerName();
            if (StringUtils.isNotBlank(passengerName)) {
                String[] passengerNameArr = passengerName.split(",");
                e.setPassengerName(Arrays.stream(passengerNameArr).map(EncryptUtils::sm4Decrypt).collect(Collectors.joining(",")));
            }
            String costCenterName = e.getCostCenterName();
            if (StringUtils.isNotBlank(costCenterName)) {
                String[] costCenterNameArr = costCenterName.split(",");
                e.setCostCenterName(Arrays.stream(costCenterNameArr).filter(StringUtils::isNotBlank).map(EncryptUtils::sm4Decrypt).collect(Collectors.joining(",")));
            }
        });
    }

    public Map<Long, List<QueryOrderDetailResponseVo>> reconciliationService(CheckAllOrderAmountRequest request) {
        //根据酒店状态和离店时间来判断当前酒店是否为已完成状态   状态为已确认   离店时间小于今天得订单
        Map<Long, List<QueryOrderDetailResponseVo>> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(request.getOrderIds())) {
            List<HoOrder> completeStatusOrder = hoOrderLoader.getCompleteStatusOrder(DateUtil.getYesterdayYMD(), DateUtil.getNowDayYMD());

            if (CollectionUtils.isEmpty(completeStatusOrder)) {
                return Maps.newHashMap();
            }

            List<QueryOrderDetailResponseVo> list = Lists.newArrayList();
            for (HoOrder responseVo : completeStatusOrder) {
                QueryOrderDetailResponseVo re = new QueryOrderDetailResponseVo();
                BeanUtils.copyProperties(responseVo, re);
                list.add(re);
            }


            map.put(1L, list);
        } else {
            Example example = new Example(HoOrder.class);
            example.createCriteria().andIn("orderId", request.getOrderIds());
            List<HoOrder> hoOrders = hoOrderLoader.selectByExample(example);
            if (CollectionUtils.isEmpty(hoOrders)) {
                return Maps.newHashMap();
            }

            List<QueryOrderDetailResponseVo> list = Lists.newArrayList();
            for (HoOrder responseVo : hoOrders) {
                QueryOrderDetailResponseVo re = new QueryOrderDetailResponseVo();
                BeanUtils.copyProperties(responseVo, re);
                list.add(re);
            }

            map.put(2L, list);
        }

        return map;
    }

    public JSONResult<VerifyHotelEndOrderResponse> verifyEndOrder(String applyNo) {
        VerifyHotelEndOrderResponse verifyHotelEndOrderResponse = new VerifyHotelEndOrderResponse();
        List<HotelOrderBo> hotelOrderBos = hoOrderMapper.verifyEndOrder(applyNo);
        if (CollectionUtils.isNotEmpty(hotelOrderBos)) {
            Date now = new Date();
            List<VerifyHotelEndOrderResponse.Info> infos = new ArrayList<>();
            for (HotelOrderBo bo : hotelOrderBos) {
                VerifyHotelEndOrderResponse.Info info = new VerifyHotelEndOrderResponse.Info();
                info.setOrderId(bo.getOrderId());
                info.setOrderStatus(bo.getOrderStatus());
                if (bo.getIsDeleted()) {
                    info.setResult(Boolean.TRUE);
                    infos.add(info);
                    continue;
                }
                if (OrderSourceEnum.Supplier.name().equalsIgnoreCase(bo.getSource())||
                        OrderSourceEnum.Supplement.name().equalsIgnoreCase(bo.getSource())) {
                    info.setResult(Boolean.TRUE);
                    infos.add(info);
                    continue;
                }
                String orderStatus = bo.getOrderStatus();
                Date checkOutDate = bo.getCheckOutDate();
                //取消或完成，已确定且过来退费时间
                if (OrderStatusEnum.ED.getCode().equals(orderStatus)
                        || OrderStatusEnum.CA.getCode().equals(orderStatus)
                        || (OrderStatusEnum.TA.getCode().equals(orderStatus) && now.after(checkOutDate))) {
                    info.setResult(Boolean.TRUE);
                } else {
                    info.setResult(Boolean.FALSE);
                }
                infos.add(info);
            }
            verifyHotelEndOrderResponse.setInfos(infos);
        }
        return JSONResult.success(verifyHotelEndOrderResponse);
    }
}