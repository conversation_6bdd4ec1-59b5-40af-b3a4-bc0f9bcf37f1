package com.corpgovernment.hotel.product.model.ctrip.v2;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 酒店阶梯扣款详细信息
 *
 * <AUTHOR>
 */
@Data
public class LadderDeductionDetailEntityV2 implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * * 扣款开始时间(北京时间)
	 */
	private String startDeductTime;

	/**
	 * * 扣款结束时间(北京时间)
	 */
	private String endDeductTime;

	/**
	 * * 扣款比例(例：0.1)
	 */
	private BigDecimal deductionRatio;

	/**
	 * * 扣款金额
	 */
	private BigDecimal amount;

	/**
	 * * 扣款币种
	 */
	private String currency;
	/**
	 * 原币种价格
	 */
	private BigDecimal originalAmount;
	/**
	 * 原币种
	 */
	private String originalCurrency;
}
