package com.corpgovernment.hotel.product.entity.db;

import com.corpgovernment.api.hotel.product.model.response.HoRoomBo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 *
 **/
@Data
@Table(name = "ho_room")
public class HoRoom implements Serializable {

    /**
     * 订单号
     **/
    private Long orderId;

    /**
     * 房间类型
     **/
    private String roomId;

    /**
     * 房间名称
     **/
    private String roomName;

    /**
     * 入住时间
     **/
    private Date checkInDate;

    /**
     * 离店时间
     **/
    private Date checkOutDate;

    /**
     * 天数
     **/
    private Integer nextDay;

    /**
     * 价格描述
     */
    private String applicativeAreaDesc;
    /**
     * 价格标题
     */
    private String applicativeAreaTitle;

    /**
     * 取消类型
     **/
    private Integer cancelPolicyType;

    /**
     * 取消描述
     **/
    private String cancelPolicyDesc;

    /**
     * 可住人数
     **/
    private Integer personCount;

    /**
     * 预订房间数
     **/
    private Integer roomQuantity;

    /**
     * 取消修改说明
     **/
    private String cancelModifyNote;

    /**
     * 床型名称
     */
    private String bedName;

    /**
     * 创建时间
     **/
    private Date datachangeCreatetime;

    /**
     * 最后修改时间
     **/
    private Date datachangeLasttime;

    /**
     * true则视为对应产品包含酒店套餐
     */
    private Boolean packageRoom;

    /**
     * 打包售卖房型打包Id
     */
    private Integer packageId;
    /**
     * 房型图片
     */
    private String picUrls;
    /**
     * 房型信息
     */
    private String roomInfoContext;
    /**
     * 套餐内容
     */
    private String packageRoomContext;
    /**
     * 所属母房型下最低价非协议房型的房费均价
     */
    @Column(name = "non_protocol_min_avg_price")
    private BigDecimal nonProtocolMinAvgPrice;
    /**
     * 所属母房型下最高价非协议房型的房费均价
     */
    @Column(name = "non_protocol_max_avg_price")
    private BigDecimal nonProtocolMaxAvgPrice;
    /**
     * 所属母房型下最低价协议房型的房费均价
     */
    @Column(name = "protocol_min_avg_price")
    private BigDecimal protocolMinAvgPrice;
    /**
     * 所属母房型下最低价协议房型的所属供应商
     */
    @Column(name = "protocol_min_avg_price_supplier_code")
    private String protocolMinAvgPriceSupplierCode;
    /**
     * 床型描述
     */
    @Column(name = "bed_desc")
    private String bedDesc;
    /**
     * 床型详细描述
     */
    @Column(name = "bed_detail_desc")
    private String bedDetailDesc;

    /**
     * 物理房型id （母房型id）
     */
    @Column(name = "basic_room_id")
    private String basicRoomId;

    /**
     * 餐食类型
     */
    @Column(name = "meal_type")
    private Integer mealType;

    /**
     * 最晚取消时间
     */
    @Column(name = "last_cancel_time")
    private Date lastCancelTime;

    /**
     * 最小连住天数
     */
    @Column(name = "min_consecutive_days")
    private Integer minConsecutiveDays;

    public HoRoomBo convertHoRoomBo() {
        if (this == null) {
            return null;
        }
        HoRoomBo hoRoomBo = new HoRoomBo();
        BeanUtils.copyProperties(this, hoRoomBo);
        return hoRoomBo;
    }


}
