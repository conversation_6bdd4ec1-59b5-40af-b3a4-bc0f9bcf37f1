package com.corpgovernment.hotel.product.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.corpgovernment.api.ordercenter.vo.OrderMonitorVo;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.enums.MonitorLevelEnums;
import com.corpgovernment.common.enums.MonitorTypeEnums;
import com.corpgovernment.hotel.product.dataloader.soa.OrderMonitorClientLoader;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: pwang27
 * @Date: 2023/8/21 17:30
 * @Description:
 */
@Component
@Slf4j
public class OrderMonitorService {

    @Autowired
    private OrderMonitorClientLoader orderMonitorClientLoader;

    @Autowired
    @Qualifier("logThreadPoolExecutor")
    private ThreadPoolExecutor logThreadPoolExecutor;

    public static final String ERROR_CODE = "-1";

    public static final String BOOK_ORDER = "预订订单";

    public static final String CREATE_ORDER = "下单接口";

    public static final String CANCEL_ORDER_QUERY = "订单取消问询";

    public static final String ORDER_CONFIRM = "订单确认";

    public static final String CANCEL_ORDER_DETAIL = "酒店取消详情";
    public static final String CANCEL_ORDER = "取消订单";

    public static final String QUERY_ORDER_MODIFICATION = "订单修改问询";

    public static final String CREATE_ORDER_MODIFICATION = "订单修改申请";
    public static final String ORDER_MODIFICATION_DETAILS = "订单修改详情";
    public static final String ORDER_DETAILS = "供应商详情";

    public static final String MODIFIABLE_ROOM_NIGHT_QUERY = "可修改间夜查询";



    /**
     * 国内酒店产线标识
     */
    public static final String BUSINESS_TYPE = "hotel";

    /**
     * 告警是否处理， Y-已处理N-未处理
     */
    public static final String HANDLE_STATE_N = "N";

    /**
     * 返回值数据最大长度
     */
    public static final int DATA_LENGTH = 500;

    /**
     * 普通预警
     *
     * @param orderId
     * @param monitorType
     * @param source
     */
    public void saveGenOrderMonitor(String orderId, MonitorTypeEnums monitorType, String source) {
        this.saveOrderMonitor(orderId, monitorType, MonitorLevelEnums.GEN, monitorType.getDesc(), ERROR_CODE,
            monitorType.getDesc(), source);
    }

    /**
     * 重要预警
     *
     * @param orderId
     * @param monitorType
     * @param source
     */
    public void saveImpOrderMonitor(String orderId, MonitorTypeEnums monitorType, String source) {
        this.saveOrderMonitor(orderId, monitorType, MonitorLevelEnums.IMP, monitorType.getDesc(), ERROR_CODE,
            monitorType.getDesc(), source);
    }

    /**
     * 机票预警
     *
     * @param orderId
     * @param monitorType
     * @param monitorLevel
     * @param responseData
     * @param errorCode
     * @param errorMessage
     * @param source
     */
    public void saveOrderMonitor(String orderId, MonitorTypeEnums monitorType, MonitorLevelEnums monitorLevel,
        String responseData, String errorCode, String errorMessage, String source) {
        if (StringUtils.isEmpty(orderId)) {
            return;
        }
        try {
            logThreadPoolExecutor
                .execute(() -> save(orderId, monitorType, monitorLevel, responseData, errorCode, errorMessage, source));
        } catch (Exception ex) {
            log.error("机票预警线程错误", ex);
        }
    }

    /**
     * 保存数据
     *
     * @param orderId
     * @param monitorType
     * @param monitorLevel
     * @param responseData
     * @param errorCode
     * @param errorMessage
     * @param source
     */
    public void save(String orderId, MonitorTypeEnums monitorType, MonitorLevelEnums monitorLevel,
        String responseData, String errorCode, String errorMessage, String source) {
        OrderMonitorVo monitorVo = orderMonitorClientLoader.getMonitorInfo(Long.parseLong(orderId));
        if (Objects.isNull(monitorVo)) {
            monitorVo = createOrderMonitor();
        }

        monitorVo.setOrderId(Long.parseLong(orderId));
        monitorVo.setMonitorType(monitorType.toString());
        monitorVo.setMonitorLevel(monitorLevel.toString());
        if (responseData.length() > DATA_LENGTH) {
            responseData = responseData.substring(0, DATA_LENGTH);
        }
        monitorVo.setResponseData(responseData);
        monitorVo.setErrorCode(StringUtils.isBlank(errorCode) ? ERROR_CODE : errorCode);
        monitorVo.setErrorMessage(errorMessage);
        monitorVo.setSource(source);
        orderMonitorClientLoader.save(monitorVo);
    }

    /**
     * 创建默认的监控
     *
     * @return
     */
    private OrderMonitorVo createOrderMonitor() {
        OrderMonitorVo monitorVo = new OrderMonitorVo();
        monitorVo.setOrderDate(new Date());
        monitorVo.setAmount(BigDecimal.ZERO);
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        if (Objects.nonNull(baseUserInfo)) {
            monitorVo.setCorpName(baseUserInfo.getCorpName());
            monitorVo.setSupplierCode(baseUserInfo.getSupplierCode());
        }
        monitorVo.setBusinessType(BUSINESS_TYPE);
        monitorVo.setHandleState(HANDLE_STATE_N);
        return monitorVo;
    }

}
