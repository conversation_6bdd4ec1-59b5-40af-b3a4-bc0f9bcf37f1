package com.corpgovernment.hotel.product.controller;

import com.corpgovernment.api.hotel.product.bo.GetHotelBasicInfoRequestBO;
import com.corpgovernment.api.hotel.product.bo.GetHotelBasicInfoResponseBO;
import com.corpgovernment.api.hotel.product.bo.reconciliation.GetHotelReconciliationRecordRequestBO;
import com.corpgovernment.api.hotel.product.bo.reconciliation.GetHotelReconciliationRecordResponseBO;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.bookorder.response.LocalBookOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.hotel.product.model.pay.request.PayCancelOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.pay.request.PayGetOrderStatusRequestBo;
import com.corpgovernment.api.hotel.product.model.pay.response.PayCancelOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.pay.response.PayGetOrderStatusResponseBo;
import com.corpgovernment.api.hotel.product.model.updateorder.request.UpdateOrderRequestBo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.hotel.product.service.HotelOrderService;
import com.corpgovernment.hotel.product.service.HotelPayService;
import com.corpgovernment.hotel.product.service.ReconciliationService;
import com.corpgovernment.hotel.product.service.SaveOrderProductService;
import com.corpgovernment.hotel.product.supplier.CtripSlSupplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/hotelProduct")
public class HotelProductController {

    @Autowired
    private CtripSlSupplier ctripSlSupplier;
    @Autowired
    private SaveOrderProductService saveOrderProductService;
    @Autowired
    private HotelPayService hotelPayService;
    @Autowired
    private HotelOrderService hotelOrderService;
    @Autowired
    private ReconciliationService reconciliationService;

    @RequestMapping("/checkAvail")
    public JSONResult<LocalCheckAvailResponseBo> checkAvail(@RequestBody LocalCheckAvailRequestBo request) {
        return JSONResult.success(ctripSlSupplier.checkAvail(request));
    }

    /**
     * 预订
     */
    @RequestMapping({"/bookOrder"})
    public JSONResult<LocalBookOrderResponseBo> bookOrder(@RequestBody LocalBookOrderRequestBo request) {
        JSONResult ok = JSONResult.ok();
        try {
            return JSONResult.success(ctripSlSupplier.bookOrder(request));
        } catch (IOException e) {
            if (e instanceof SocketTimeoutException) {
                ok.setStatus(408);
                ok.setMsg("酒店预订超时");
                return ok;
            }
        }
        return ok;
    }

//    /**
//     * 预订
//     */
//    @RequestMapping({"/saveOrder"})
//    public JSONResult<SaveOrderResponseBo> saveOrder(@RequestBody SaveOrderRequestBo request) {
//        saveOrderProductService.saveOrder(request);
//        return JSONResult.success(SaveOrderResponseBo.success(request.getOrderInfo().getOrderId()));
//    }

    /**
     * 更新订单状态
     */
    @RequestMapping({"/updateOrder"})
    public JSONResult updateOrder(@RequestBody UpdateOrderRequestBo request) {
        saveOrderProductService.updateOrder(request);
        return JSONResult.ok();
    }

    /**
     * 获取订单状态
     */
    @RequestMapping({"/payCancelOrder"})
    public JSONResult<PayCancelOrderResponseBo> payCancelOrder(@RequestBody PayCancelOrderRequestBo requestBo) {
        return JSONResult.success(hotelPayService.payCancelOrder(requestBo));
    }

    /**
     * 获取订单状态
     */
    @RequestMapping({"/payGetOrderStatus"})
    public JSONResult<PayGetOrderStatusResponseBo> payGetOrderStatus(@RequestBody PayGetOrderStatusRequestBo requestBo) {
        return JSONResult.success(hotelPayService.payGetOrderStatus(requestBo));
    }

//    /**
//     *
//     */
//    @RequestMapping("/findFinancialOrder")
//    public JSONResult findFinancialOrder(FinancialOrderRequestBo requestBo) {
//        return JSONResult.success(hotelOrderService.findFinancialOrder(requestBo));
//    }

    /**
     * 获取对账信息
     */
    @RequestMapping(value = "/getHotelRecordList")
	public  JSONResult<GetHotelReconciliationRecordResponseBO> getHotelRecordList(@RequestBody GetHotelReconciliationRecordRequestBO request){
        return JSONResult.success(reconciliationService.getHotelRecordList(request));
    }

    @RequestMapping(value = "/getHotelBasicInfo")
    public  JSONResult<GetHotelBasicInfoResponseBO> getHotelBasicInfo(@RequestBody GetHotelBasicInfoRequestBO request) {
        return JSONResult.success(reconciliationService.getHotelBasicInfo(request));
    }

//    @RequestMapping("/getHotelOrderAmount")
//    public JSONResult<Map<Long, List<QueryOrderDetailResponseVo>>> getHotelOrderAmount (@RequestBody @Valid CheckAllOrderAmountRequest request) {
//        return JSONResult.success(reconciliationService.reconciliationService(request));
//    }
}