package com.corpgovernment.hotel.product.dataloader.db;

import com.corpgovernment.hotel.booking.enums.HotelModifyStatusEnum;
import com.corpgovernment.hotel.product.entity.db.HoHotelApply;
import com.corpgovernment.hotel.product.mapper.HoHotelApplyMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/31
 */
@Component
public class HoHotelApplyLoader {

    private static final String ORDER_ID = "orderId";
    private static final String STATUS = "status";

    @Autowired
    private HoHotelApplyMapper hoHotelApplyMapper;
    public int insert(HoHotelApply hoHotelApply){
        return hoHotelApplyMapper.insertSelective(hoHotelApply);
    }
    public List<HoHotelApply> select(Long orderId){
        if (orderId == null) {
            return null;
        }
        HoHotelApply record = new HoHotelApply();
        record.setOrderId(orderId);
        return hoHotelApplyMapper.select(record);
    }
    public HoHotelApply selectByApplyId(String applyId){
        if (applyId == null) {
            return null;
        }
        HoHotelApply record = new HoHotelApply();
        record.setApplyId(applyId);
        return hoHotelApplyMapper.selectOne(record);
    }


    public int updateByPrimaryKey(HoHotelApply record) {
        if (record == null || record.getId() == null) {
            return 0;
        }
        return hoHotelApplyMapper.updateByPrimaryKeySelective(record);
    }

    public Integer updateRefundAmountBatch(List<HoHotelApply> hoHotelApplyList) {
        return hoHotelApplyMapper.updateRefundAmountBatch(hoHotelApplyList);
    }

    public List<HoHotelApply> listSuccessByOrderIds(Collection<Long> orderIds) {
        if(CollectionUtils.isEmpty(orderIds)){
            return Collections.emptyList();
        }
        return hoHotelApplyMapper.listSuccessByOrderIds(orderIds);
    }

    public List<HoHotelApply> listNoFailByOrderIds(Collection<Long> orderIds) {
        if(CollectionUtils.isEmpty(orderIds)){
            return Collections.emptyList();
        }
        return hoHotelApplyMapper.listNoFailByOrderIds(orderIds);
    }

    /**
     * 订单是否存在正在修改中的单据
     *
     * @param orderId 订单号
     * @return {@link Boolean } true:存在 false:不存在
     */
    public Boolean orderExistBeingModified(Long orderId) {
        List<Integer> beingModifiedStatusList = CollectionUtils.newArrayList(HotelModifyStatusEnum.PLATFORM_SUBMITTED.getCode(), HotelModifyStatusEnum.SUBMITTED.getCode(), HotelModifyStatusEnum.PENDING.getCode(), HotelModifyStatusEnum.COORDINATION.getCode(), HotelModifyStatusEnum.CANCELING.getCode());
        Example example = new Example(HoHotelApply.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ORDER_ID, orderId).andIn(STATUS, beingModifiedStatusList);
        return hoHotelApplyMapper.selectCountByExample(example) > 0;
    }

    /**
     * 按订单 ID 列出正在修改的单据
     *
     * @param orderIds 订单 ID
     * @return {@link List }<{@link HoHotelApply }>
     */
    public List<HoHotelApply> listBeingModifiedByOrderIds(Collection<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        List<Integer> beingModifiedStatusList =
            CollectionUtils.newArrayList(HotelModifyStatusEnum.PLATFORM_SUBMITTED.getCode(),
                HotelModifyStatusEnum.SUBMITTED.getCode(), HotelModifyStatusEnum.PENDING.getCode(),
                HotelModifyStatusEnum.COORDINATION.getCode(), HotelModifyStatusEnum.CANCELING.getCode());
        Example example = new Example(HoHotelApply.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn(ORDER_ID, orderIds).andIn(STATUS, beingModifiedStatusList);
        return hoHotelApplyMapper.selectByExample(example);
    }
}
