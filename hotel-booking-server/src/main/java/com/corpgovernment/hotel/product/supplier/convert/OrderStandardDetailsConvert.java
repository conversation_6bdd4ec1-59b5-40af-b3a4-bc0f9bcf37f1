package com.corpgovernment.hotel.product.supplier.convert;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.corpgovernment.api.hotel.product.model.orderdetail.response.PayInfo;
import com.corpgovernment.api.hotel.product.model.orderdetail.response.RefundInfo;
import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import com.corpgovernment.hotel.product.external.dto.order.detail.CtripOrderDetailResponse;
import org.mapstruct.Mapper;

import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailResponse;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

/**
 * 订单详情转换类
 * 标准契约返回值-> 携程返回值
 * 因现阶段无法大面积修改标准契约返回值，故将标准契约返回值 转为携程返回值 以适配现有逻辑
 * 
 * <AUTHOR> dingjian
 * @date : 2024/4/9 21:32
 * @since : 1.0
 */
@Mapper(componentModel = "spring")
public interface OrderStandardDetailsConvert {

    default OrderDetailResponse.HotelOrderInfo
        convertOrderDetailResponse(StandardOrderDetailResponse standardOrderDetailResponse) {
        OrderDetailResponse.HotelOrderInfo ctripHotelInfo =
            new OrderDetailResponse.HotelOrderInfo();
        if (Objects.isNull(standardOrderDetailResponse)
            || CollectionUtils.isEmpty(standardOrderDetailResponse.getHotelOrderInfoList())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.ORDER_DETAIL_IS_NULL);
        }
        StandardOrderDetailResponse.HotelOrderInfo standardHotelOrderInfo =
            standardOrderDetailResponse.getHotelOrderInfoList().get(0);
        if (Objects.isNull(standardHotelOrderInfo)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.ORDER_DETAIL_IS_NULL);
        }
        StandardOrderDetailResponse.BasicOrderInfo basicOrderInfo = standardHotelOrderInfo.getBasicOrderInfo();
        StandardOrderDetailResponse.FeeDetailInfo feeDetailInfo = standardHotelOrderInfo.getFeeDetailInfo();

        ctripHotelInfo.setOrderId(basicOrderInfo.getOrderID());
        ctripHotelInfo.setPlatformOrderId(basicOrderInfo.getPlatformOrderID());
        ctripHotelInfo.setOrderDetailStatus(basicOrderInfo.getOrderDetailStatus());
        ctripHotelInfo.setOrderDetailStatusName(basicOrderInfo.getOrderDetailStatusName());
        ctripHotelInfo.setRoomDays(basicOrderInfo.getRoomDays());
        ctripHotelInfo.setRoomQuantity(basicOrderInfo.getRoomQuantity());
        ctripHotelInfo.setCustomPayAmount(feeDetailInfo.getActualPayAmount());
        ctripHotelInfo.setLadderDeductAmount(feeDetailInfo.getLadderDeductAmount());
        ctripHotelInfo.setSettlementACCNTAmt(feeDetailInfo.getMixPayAccntAmount());
        ctripHotelInfo.setSettlementPersonAmt(feeDetailInfo.getMixPayPersonAmount());
        if (null != standardHotelOrderInfo.getAdditionalInformationMap()) {
            Map<String, Object> additionalInformationMap = standardHotelOrderInfo.getAdditionalInformationMap();
            ctripHotelInfo.setHotelName(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_HOTEL_NAME, "").toString());
            ctripHotelInfo.setCityId(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_CITY_ID, "").toString());
            ctripHotelInfo.setCityName(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_CITY_NAME, "").toString());
            ctripHotelInfo.setHotelAddress(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_HOTEL_ADDRESS, "").toString());
            ctripHotelInfo.setHotelPhone(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_HOTEL_PHONE, "").toString());
            ctripHotelInfo.setContactName(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_CONTACT_NAME, "").toString());
            ctripHotelInfo.setContactPhone(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_CONTACT_PHONE, "").toString());
            ctripHotelInfo.setCheckInDate(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_CHECK_IN_DATE, "").toString());
            ctripHotelInfo.setCheckOutDate(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_CHECK_OUT_DATE, "").toString());
            ctripHotelInfo.setVatFlag(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_VAT_FLAG, "").toString());
            ctripHotelInfo.setHotelType(additionalInformationMap
                .getOrDefault(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_HOTEL_TYPE_CODE, "").toString());
            Object serviceFeeObj =
                additionalInformationMap.get(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_SERVICE_FEE);
            if (null != serviceFeeObj) {
                ctripHotelInfo.setServiceFee(new BigDecimal(serviceFeeObj.toString()));
            }
            Object postServiceFeeObj =
                    additionalInformationMap.get(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_POST_SERVICE_FEE);
            if (null != postServiceFeeObj) {
                ctripHotelInfo.setPostServiceFee(new BigDecimal(postServiceFeeObj.toString()));
            }
            Object deliveryFeeObj =
                additionalInformationMap.get(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_DELIVERY_FEE);
            if (null != deliveryFeeObj) {
                ctripHotelInfo.setDeliveryFee(new BigDecimal(deliveryFeeObj.toString()));
            }

            Object addedFeesObj =
                    additionalInformationMap.get(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_ADDED_FEES);
            if (null != addedFeesObj) {
                ctripHotelInfo.setAddedFees(new BigDecimal(addedFeesObj.toString()));
            }

            Object cancelFeeObj =
                    additionalInformationMap.get(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_SUBCRITION_FEE);
            if (null != cancelFeeObj) {
                ctripHotelInfo.setCancelFee(new BigDecimal(cancelFeeObj.toString()));
            }

            Object roomInfoListObj =
                    additionalInformationMap.get(SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_CTRIP_ROOM_INFO_LIST);
            if (null != roomInfoListObj) {
                List<CtripOrderDetailResponse.RoomInfo> roomInfoList = (List<CtripOrderDetailResponse.RoomInfo>) roomInfoListObj;
                ctripHotelInfo.setRoomInfoList(roomInfoList.stream().map(roomInfo -> {
                    OrderDetailResponse.RoomInfo room = new OrderDetailResponse.RoomInfo();
                    room.setRoomName(roomInfo.getRoomName());
                    room.setBedType(roomInfo.getBedType());
                    room.setPrice(roomInfo.getPrice());
                    room.setBreakfast(roomInfo.getBreakfast());
                    return room;
                }).collect(Collectors.toList()));
            }
        }

        // 入住人信息
        ctripHotelInfo.setClientInfo(getClientInfo(standardHotelOrderInfo));
        // 订单退款信息
        ctripHotelInfo.setRefundInfo(getRefundInfos(standardHotelOrderInfo));
        // 人房详情
        ctripHotelInfo.setRoomDetailList(getRoomDetailInfos(standardHotelOrderInfo));
        // 新供应商的支付退款信息
        ctripHotelInfo.setPaymentList(getPayInfoList(standardHotelOrderInfo));
        ctripHotelInfo.setRefundInfoList(getRefundInfoList(standardHotelOrderInfo));
        return ctripHotelInfo;
    }


    static List<OrderDetailResponse.RoomDetailInfo>
        getRoomDetailInfos(StandardOrderDetailResponse.HotelOrderInfo standardHotelOrderInfo) {
        List<OrderDetailResponse.RoomDetailInfo> roomDetailList = new ArrayList<>();
        List<StandardOrderDetailResponse.RoomDetail> detailList = standardHotelOrderInfo.getRoomDetailList();
        if (CollectionUtils.isEmpty(detailList)) {
            return roomDetailList;
        }
        detailList.forEach(roomDetail -> {
            OrderDetailResponse.RoomDetailInfo roomDetailInfo = new OrderDetailResponse.RoomDetailInfo();
            roomDetailInfo.setRoomId(roomDetail.getRoomID());
            roomDetailInfo.setHotelId(roomDetail.getHotelID());
            roomDetailInfo.setCheckInDate(roomDetail.getCheckInDate());
            roomDetailInfo.setCheckOutDate(roomDetail.getCheckOutDate());
            roomDetailInfo.setPassengerName(roomDetail.getGuestName());
            roomDetailInfo.setCancelFee(roomDetail.getCancelFee());
            List<OrderDetailResponse.RoomPriceInfo> roomPriceList = new ArrayList<>();
            roomDetail.getRoomPriceList().forEach(roomPrice -> {
                OrderDetailResponse.RoomPriceInfo roomPriceInfo = new OrderDetailResponse.RoomPriceInfo();
                roomPriceInfo.setPrice(roomPrice.getPrice());
                roomPriceInfo.setDate(roomPrice.getDate());
                roomPriceList.add(roomPriceInfo);
            });
            roomDetailInfo.setRoomPriceList(roomPriceList);
            roomDetailList.add(roomDetailInfo);
        });
        return roomDetailList;
    }

    default List<OrderDetailResponse.RefundInfo>
        getRefundInfos(StandardOrderDetailResponse.HotelOrderInfo standardHotelOrderInfo) {
        List<OrderDetailResponse.RefundInfo> refundInfos = new ArrayList<>();
        List<StandardOrderDetailResponse.RefundInfo> refundInfoList = standardHotelOrderInfo.getRefundInfoList();
        if (CollectionUtils.isEmpty(refundInfoList)) {
            return refundInfos;
        }
        refundInfoList.forEach(standardRefundInfo -> {
            OrderDetailResponse.RefundInfo refundInfo = new OrderDetailResponse.RefundInfo();
            refundInfo.setRefundInfoID(standardRefundInfo.getRefundInfoID());
            List<OrderDetailResponse.RefundPaymentInfo> refundPaymentList = new ArrayList<>();
            standardRefundInfo.getRefundPaymentList().forEach(standardRefundPayment -> {
                OrderDetailResponse.RefundPaymentInfo refundPaymentInfo = new OrderDetailResponse.RefundPaymentInfo();
                refundPaymentInfo.setAmount(standardRefundPayment.getRefundAmount());
                refundPaymentInfo.setRefundChannel(standardRefundPayment.getRefundChannel());
                refundPaymentList.add(refundPaymentInfo);
            });
            refundInfo.setRefundPaymentList(refundPaymentList);
            refundInfos.add(refundInfo);
        });
        return refundInfos;
    }

    default List<OrderDetailResponse.ClientInfoEntity>
        getClientInfo(StandardOrderDetailResponse.HotelOrderInfo standardHotelOrderInfo) {
        List<OrderDetailResponse.ClientInfoEntity> clientInfo = new ArrayList<>();
        List<StandardOrderDetailResponse.GuestInfo> guestInfoList = standardHotelOrderInfo.getGuestInfoList();
        if (CollectionUtils.isEmpty(guestInfoList)) {
            return clientInfo;
        }
        Map<String, Object> additionalInformationMap = standardHotelOrderInfo.getAdditionalInformationMap();
        guestInfoList.forEach(guestInfo -> {
            OrderDetailResponse.ClientInfoEntity clientInfoEntity =
                new OrderDetailResponse.ClientInfoEntity();
            clientInfoEntity.setClientName(guestInfo.getName());
            clientInfoEntity.setRoomIndex(guestInfo.getRoomIndex());
            clientInfoEntity.setUID(guestInfo.getUID());
            clientInfoEntity.setActualCheckInTime(guestInfo.getActualCheckInTime());
            clientInfoEntity.setActualDepartureTime(guestInfo.getActualDepartureTime());
            if (null != additionalInformationMap) {
                clientInfoEntity.setMobilePhone(additionalInformationMap
                    .getOrDefault(MessageFormat.format(
                        SupplierConstant.OrderDetail.RESPONSE_ADDITIONAL_MAP_KEY_CLIENT_MOBILE_PHONE, guestInfo.getName()), "")
                    .toString());
            }
            clientInfo.add(clientInfoEntity);
        });
        return clientInfo;
    }

    default List<PayInfo> getPayInfoList(StandardOrderDetailResponse.HotelOrderInfo standardHotelOrderInfo) {
        List<PayInfo> payInfoList = new ArrayList<>();
        List<StandardOrderDetailResponse.PayInfo> standardPayInfoList = standardHotelOrderInfo.getPayInfoList();
        if (CollectionUtils.isEmpty(standardPayInfoList)) {
            return payInfoList;
        }
        standardPayInfoList.forEach(standardPayInfo -> {
            PayInfo payInfo = new PayInfo();
            payInfo.setPaymentNo(standardPayInfo.getPaymentNo());
            payInfo.setOperationID(standardPayInfo.getOperationID());
            payInfo.setOperationType(standardPayInfo.getOperationType());
            payInfo.setPayTotalAmount(standardPayInfo.getPayTotalAmount());
            List<StandardOrderDetailResponse.Payment> standardPaymentList = standardPayInfo.getPaymentList();
            List<PayInfo.Payment> paymentList = new ArrayList<>();
            standardPaymentList.forEach(standardPayment -> {
                PayInfo.Payment payment = new PayInfo.Payment();
                payment.setUID(standardPayment.getUID());
                payment.setGuestName(standardPayment.getGuestName());
                payment.setRoomIndex(standardPayment.getRoomIndex());
                payment.setPayAmount(standardPayment.getPayAmount());
                payment.setPayTime(standardPayment.getPayTime());
                payment.setPayChannel(standardPayment.getPayChannel());
                payment.setFeeCode(standardPayment.getFeeCode());
                paymentList.add(payment);
            });
            payInfo.setPaymentList(paymentList);
            payInfoList.add(payInfo);
        });
        return payInfoList;
    }

    default List<RefundInfo> getRefundInfoList(StandardOrderDetailResponse.HotelOrderInfo standardHotelOrderInfo) {
        List<RefundInfo> refundInfoList = new ArrayList<>();
        List<StandardOrderDetailResponse.RefundInfo> standardRefundInfoList = standardHotelOrderInfo.getRefundInfoList();
        if (CollectionUtils.isEmpty(standardRefundInfoList)) {
            return refundInfoList;
        }
        standardRefundInfoList.forEach(standardRefundInfo -> {
            RefundInfo refundInfo = new RefundInfo();
            refundInfo.setRefundInfoID(standardRefundInfo.getRefundInfoID());
            refundInfo.setOperationID(standardRefundInfo.getOperationID());
            refundInfo.setOperationType(standardRefundInfo.getOperationType());
            refundInfo.setRefundTotalAmount(standardRefundInfo.getRefundTotalAmount());
            List<StandardOrderDetailResponse.RefundPayment> standardRefundPaymentList = standardRefundInfo.getRefundPaymentList();
            List<RefundInfo.RefundPayment> refundPaymentList = new ArrayList<>();
            standardRefundPaymentList.forEach(standardRefundPayment -> {
                RefundInfo.RefundPayment refundPayment = new RefundInfo.RefundPayment();
                refundPayment.setUID(standardRefundPayment.getUID());
                refundPayment.setGuestName(standardRefundPayment.getGuestName());
                refundPayment.setRoomIndex(standardRefundPayment.getRoomIndex());
                refundPayment.setRefundAmount(standardRefundPayment.getRefundAmount());
                refundPayment.setRefundTime(standardRefundPayment.getRefundTime());
                refundPayment.setRefundChannel(standardRefundPayment.getRefundChannel());
                refundPayment.setFeeCode(standardRefundPayment.getFeeCode());
                refundPaymentList.add(refundPayment);
            });
            refundInfo.setRefundPaymentList(refundPaymentList);
            refundInfoList.add(refundInfo);
        });
        return refundInfoList;
    }

}
