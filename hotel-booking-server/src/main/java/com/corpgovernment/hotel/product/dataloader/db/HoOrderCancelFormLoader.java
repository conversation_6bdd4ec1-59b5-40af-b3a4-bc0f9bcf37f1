package com.corpgovernment.hotel.product.dataloader.db;

import com.corpgovernment.hotel.booking.enums.CancelFormStatusEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.corpgovernment.hotel.product.entity.db.HoOrderCancelForm;
import com.corpgovernment.hotel.product.mapper.HoOrderCancelFormMapper;
import tk.mybatis.mapper.entity.Example;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022-01-05-18:31
 */
@Component
public class HoOrderCancelFormLoader {

	private static final String ORDER_ID = "orderId";
	private static final String STATUS = "status";
	private static final String IS_DELETED = "isDeleted";
	private static final String SUPPLIER_FORM_ID = "supplierFormId";
	private static final String SUPPLIER_ORDER_ID = "supplierOrderId";

	@Autowired
	private HoOrderCancelFormMapper hoOrderCancelFormMapper;

	public int insertSelective(HoOrderCancelForm record) {
		return hoOrderCancelFormMapper.insertSelective(record);
	}

	/**
	 * 订单是否存在正在取消中的单据
	 *
	 * @param orderId 订单号
	 * @return {@link Boolean } true:存在 false:不存在
	 */
    public Boolean orderExistBeingCanceled(Long orderId) {
        List<Integer> beingCanceledStatusList = CollectionUtils.newArrayList(CancelFormStatusEnum.PLATFORM_SUBMITTED.getCode(),
                CancelFormStatusEnum.SUBMITTED.getCode(), CancelFormStatusEnum.COORDINATING.getCode());
        Example example = new Example(HoOrderCancelForm.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ORDER_ID, orderId).andIn(STATUS, beingCanceledStatusList);
        return hoOrderCancelFormMapper.selectCountByExample(example) > 0;
    }

	/**
	 * 按订单 ID 选择最新取消申请单
	 *
	 * @param orderId 订单号
	 * @return {@link HoOrderCancelForm }
	 */
	public HoOrderCancelForm selectNewestCancelFormByOrderId(Long orderId) {
		Example example = new Example(HoOrderCancelForm.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(ORDER_ID, orderId).andEqualTo(IS_DELETED, 0);
		example.setOrderByClause("datachange_createtime desc limit 1");
		List<HoOrderCancelForm> hoOrderCancelFormList = hoOrderCancelFormMapper.selectByExample(example);
		return CollectionUtils.isEmpty(hoOrderCancelFormList) ? null : hoOrderCancelFormList.get(0);
	}

	/**
	 * 按订单 ID 列出正在取消的单据
	 *
	 * @param orderIdSet 订单 ID 设置
	 * @return {@link List }<{@link HoOrderCancelForm }>
	 */
	public List<HoOrderCancelForm> listBeingCanceledFormByOrderIds(Set<Long> orderIdSet) {
        if (CollectionUtils.isEmpty(orderIdSet)) {
            return Collections.emptyList();
        }
        List<Integer> beingCanceledStatusList =
            CollectionUtils.newArrayList(CancelFormStatusEnum.PLATFORM_SUBMITTED.getCode(),
                CancelFormStatusEnum.SUBMITTED.getCode(), CancelFormStatusEnum.COORDINATING.getCode());
        Example example = new Example(HoOrderCancelForm.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn(ORDER_ID, orderIdSet).andIn(STATUS, beingCanceledStatusList).andEqualTo(IS_DELETED, 0);
        return hoOrderCancelFormMapper.selectByExample(example);
    }

    public List<HoOrderCancelForm> listBySupplierFormId(Collection<String> supplierFormIdList) {
        Example example = new Example(HoOrderCancelForm.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn(SUPPLIER_FORM_ID, supplierFormIdList).andEqualTo(IS_DELETED, 0);
        return hoOrderCancelFormMapper.selectByExample(example);
    }
	
    public HoOrderCancelForm getNewestHoOrderCancelFormBySupplierOrderId(String supplierOrderId) {
        Example example = new Example(HoOrderCancelForm.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(SUPPLIER_ORDER_ID, supplierOrderId).andIsNull(SUPPLIER_FORM_ID).andEqualTo(IS_DELETED, 0);
        return hoOrderCancelFormMapper.selectOneByExample(example);
    }

	public int updateByPrimaryKeySelective(HoOrderCancelForm hoOrderCancelForm){
		return hoOrderCancelFormMapper.updateByPrimaryKeySelective(hoOrderCancelForm);
	}
}
