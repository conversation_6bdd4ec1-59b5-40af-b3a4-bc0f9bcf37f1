package com.corpgovernment.hotel.product.supplier;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.corpgovernment.api.hotel.product.bo.HotelConfirmOrderResponseBO;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.bookorder.response.LocalBookOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelDetailRequestBo;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelListRequestBo;
import com.corpgovernment.api.hotel.product.model.request.Tree;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO.ChildRoomDesc;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.basic.constant.HotelStarLicenceEnum;
import com.corpgovernment.basicdata.bo.HotelCityBo;
import com.corpgovernment.common.common.ValidGroup;
import com.corpgovernment.common.utils.BigDecimalUtil;
import com.corpgovernment.hotel.booking.vo.hotelmodify.HotelModifyDetailSupplierRequest;
import com.corpgovernment.hotel.booking.vo.hotelmodify.HotelModifyDetailSupplierResponse;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModificationDetailResponse;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.request.OrderDetailRequest;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse;
import com.corpgovernment.hotel.product.model.ctrip.request.HotelDataRequest;
import com.corpgovernment.hotel.product.model.ctrip.request.HotelDetailRequest;
import com.corpgovernment.hotel.product.model.ctrip.request.HotelFacilitiesEntity;
import com.corpgovernment.hotel.product.model.ctrip.request.MapSearchHotelEntity;
import com.corpgovernment.hotel.product.model.ctrip.response.detail.HotelInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.response.detail.RoomDataEntity;
import com.corpgovernment.hotel.product.model.ctrip.response.page.HotelDataEntity;
import com.corpgovernment.hotel.product.model.ctrip.response.page.NumEntity;
import com.corpgovernment.hotel.product.model.ctrip.response.staticinfo.HotelStaticInfoResponse;
import com.corpgovernment.hotel.product.supplier.enums.HotelServiceEnum;
import com.corpgovernment.hotel.product.supplier.enums.RoomTypeEnum;
import com.corpgovernment.mapping.bo.HmHotelDetailImport;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: AbstractSupplier
 * @description: AbstractSupplier
 * @author: zdwang
 * @date: Created in 19:05 2019/8/25
 * @Version: 1.0
 **/
@Slf4j
public abstract class AbstractSupplier {
    protected abstract LocalHotelListResponseBo page(LocalHotelListRequestBo requestBo, SupplierProductBo supplierProductBo, HotelCityBo hotelCity);

    protected abstract LocalCheckAvailResponseBo checkAvail(LocalCheckAvailRequestBo request);

    protected abstract LocalBookOrderResponseBo bookOrder(LocalBookOrderRequestBo request) throws IOException;

    protected abstract OrderDetailResponse supplierOrderDetail(OrderDetailRequest request);

    protected abstract OrderDetailResponse.HotelOrderInfo
        supplierOrderDetail(StandardOrderDetailRequest request,Class<? extends ValidGroup>... groups);

    protected abstract HotelConfirmOrderResponseBO confirmOrder(HoOrder orderInfo, SupplierProductBo supplierProduct);

    protected abstract HotelOperatorTypeConfig getHotelOperatorTypeConfig();

    protected abstract CommonService getCommonService();

    /**
     * 是否携程
     */
    protected abstract Boolean isCtrip();

    /**
     * URI前缀
     */
    protected abstract String getFilePrefix();

    /**
     * url处理
     */
    protected abstract String convertPicUrl(String url, Boolean needPrifix);

    protected abstract HmHotelDetailImport getLocalStaticInfo(String hotelId, String supplierCode);

    public abstract HotelModifyDetailSupplierResponse getSupplierModifyDetail(HotelModifyDetailSupplierRequest request);

    public abstract StandardOrderModificationDetailResponse getStandardSupplierModifyDetail(String applyFormId,
        HoOrder hoOrder,Class<? extends ValidGroup>... groups);

    /**
     * 解析距离
     */
    private void translateDistance(LocalHotelListRequestBo localHotelListRequestBo, HotelInfoEntity hotelInfo
            , HotelDataRequest hotelDataRequest, LocalHotelListResponseBo.HotelListBean hotelListBean) {
        if (StringUtils.isBlank(hotelInfo.getLandMarkDistance())){
            log.info("hotelInfo.getLandMarkDistance() 为空");
            return;
        }
        BigDecimal landMarkDistance = new BigDecimal(hotelInfo.getLandMarkDistance());
        String mulDistance = landMarkDistance.multiply(new BigDecimal(1000)).setScale(0, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
        String distance = landMarkDistance.setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
        if (Boolean.TRUE.equals(localHotelListRequestBo.getTag())) {
            if (landMarkDistance.compareTo(BigDecimal.ONE) < 0) {
                hotelListBean.setAppDistance("距离您" + mulDistance + "米");
            } else {
                hotelListBean.setAppDistance("距离您" + distance + "公里");
            }
        } else if (StringUtils.isNotBlank(localHotelListRequestBo.getTitle()) && StringUtils.isNotBlank(localHotelListRequestBo.getKey()) && Boolean.TRUE.equals(hotelDataRequest.getDefaultChannel())) {
            if (landMarkDistance.compareTo(BigDecimal.ONE) < 0) {
                hotelListBean.setAppDistance("距离" + localHotelListRequestBo.getTitle() + mulDistance + "米");
            } else {
                hotelListBean.setAppDistance("距离" + localHotelListRequestBo.getTitle() + distance + "公里");
            }
        } else {
            if (landMarkDistance.compareTo(BigDecimal.ONE) < 0) {
                hotelListBean.setAppDistance("距离" + hotelInfo.getCityName() + "市中心" + mulDistance + "米");
            } else {
                hotelListBean.setAppDistance("距离" + hotelInfo.getCityName() + "市中心" + distance + "公里");
            }
        }
    }

    /**
     * 解析取消规则
     */
    private void translateCancelDesc(Integer policyType, ChildRoomDesc childRoomDesc, RoomDataEntity roomDataEntity) {
        switch (policyType) {
            case 1:
                childRoomDesc.setTitle("免费取消");
                childRoomDesc.setContent("预订后可随时免费取消，请放心预订");
                break;
            case 2:
                childRoomDesc.setTitle("限时取消");
                childRoomDesc.setContent(roomDataEntity.getLastCancelTime() + "之前可以免费取消，请放心预订");
                break;
            case 8:
                childRoomDesc.setTitle("不可取消");
                childRoomDesc.setContent("订单确认后不可取消或修改");
                break;
            default:
                break;
        }
    }

    /**
     * 星级解析
     */
    private String translateHotelType(BigDecimal star) {
        if (star == null){
            return "";
        }
        switch (star.intValue()) {
            case 2:
                return "经济型";
            case 3:
                return "舒适型";
            case 4:
                return "高档型";
            case 5:
                return "豪华型";
            default:
                return "";
        }
    }

    /**
     * 服务解析
     */
    private List<HotelDetailResponseVO.Tip> getServiceTips(HotelInfoEntity infoEntity) {
        List<HotelDetailResponseVO.Tip> result = Lists.newArrayList();
        HotelFacilitiesEntity facilities = infoEntity.getHotelFacilities();
        if (facilities == null) {
            return result;
        }
        //是否有wifi
        if (Boolean.TRUE.equals(facilities.getHasWirelessBroadNet())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.WIFI.getCode()).desc(HotelServiceEnum.WIFI.getDesc()).build());
        }
        //是否含有餐饮
        if (Boolean.TRUE.equals(facilities.getRestaurant())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.RESTAURANT.getCode()).desc(HotelServiceEnum.RESTAURANT.getDesc()).build());
        }
        //是否有接送机服务
        if (Boolean.TRUE.equals(facilities.getHasReceiveAirport())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.RAIRPORT.getCode()).desc(HotelServiceEnum.RAIRPORT.getDesc()).build());
        }
        //是否有娱乐设施
        if (Boolean.TRUE) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.FUNNY.getCode()).desc(HotelServiceEnum.FUNNY.getDesc()).build());
        }
        //是否有健身房
        if (Boolean.TRUE.equals(facilities.getFitnessCenter())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.FITNESS.getCode()).desc(HotelServiceEnum.FITNESS.getDesc()).build());
        }
        //是否有游泳池
        if (Boolean.TRUE.equals(facilities.getSwimmingPool())) {
            result.add(HotelDetailResponseVO.Tip.builder().key(HotelServiceEnum.SWIMMING.getCode()).desc(HotelServiceEnum.SWIMMING.getDesc()).build());
        }
        return result;
    }

    /**
     * 房间过滤
     */
    private List<RoomDataEntity> filterRoom(List<RoomDataEntity> dataList, LocalHotelDetailRequestBo requestBo, HotelControlVo travelStandard) {
        if (Boolean.TRUE.equals(requestBo.getRcpolicy()) && travelStandard != null) {
            dataList = dataList.stream().filter(i -> {
                BigDecimal price = i.getAveragePrice();
                return (price.compareTo(new BigDecimal(travelStandard.getAveragePriceSet().getPriceCeiling())) <= 0);
            }).collect(Collectors.toList());
        }
        //协议酒店
        if (Boolean.TRUE.equals(requestBo.getProtatal())) {
            dataList = dataList.stream().filter(i -> RoomTypeEnum.C.getCode().equals(i.getRoomInfo().getRoomType())).collect(Collectors.toList());
        }
        //含早餐
        if (Boolean.TRUE.equals(requestBo.getBreakfast())) {
            dataList = dataList.stream().filter(i -> i.getRoomDInfoList().get(0).getBreakfast() > 0).collect(Collectors.toList());
        }
        //立即确认
        if (Boolean.TRUE.equals(requestBo.getCheckImmediate())) {
            dataList = dataList.stream().filter(i -> i.getRoomInfo().getJustifyConfirm().equals(Boolean.TRUE)).collect(Collectors.toList());
        }
        //免费取消
        if (Boolean.TRUE.equals(requestBo.getFreeCancel())) {
            dataList = dataList.stream().filter(i -> ((i.getRoomInfo().getCancelationPolicy().getPolicyType() == 1) || i.getRoomInfo().getCancelationPolicy().getPolicyType() == 2)).collect(Collectors.toList());
        }
        //统一支付过滤前台现付
        //update because of zhongtie needs
//        if ("PUB".equals(requestBo.getCorpPayType())) {
//            dataList = dataList.stream().filter(i -> "PP".equals(i.getRoomInfo().getPayType())).collect(Collectors.toList());
//        }

        //床型
        if (StringUtils.isNotBlank(requestBo.getBedType())) {
            dataList = dataList.stream().filter(i -> requestBo.getBedType().equals(i.getRoomInfo().getBedType())).collect(Collectors.toList());
        }
        return dataList;
    }

    /**
     * 酒店列表入参转换
     */
    protected HotelDataRequest convertHotelDataRequest(LocalHotelListRequestBo requestBo, SupplierProductBo supplierProductBo,
                                                       HotelCityBo hotelCity) {
        HotelDataRequest hotelDataRequest = new HotelDataRequest();
        MapSearchHotelEntity searchHotelEntity = new MapSearchHotelEntity();
        if (hotelCity != null) {
            searchHotelEntity.setDotX(hotelCity.getCenterLat());
            searchHotelEntity.setDotY(hotelCity.getCenterLon());
            hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
        }
        hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
        hotelDataRequest.setCorpID(supplierProductBo.getSupplierCorpId());
        hotelDataRequest.setUID(supplierProductBo.getSupplierUid());
        //星级列表
        hotelDataRequest.setStarList(requestBo.getStarList());
        //入住日期
        hotelDataRequest.setCheckInDate(requestBo.getSDate());
        //离店日期
        hotelDataRequest.setCheckOutDate(requestBo.getEDate());
        //城市id
        hotelDataRequest.setCityID(requestBo.getSupplierCityId());
        //排序类型
        chooseOrderType(requestBo, hotelDataRequest);

        HotelFacilitiesEntity query = new HotelFacilitiesEntity();
        //最低价
        query.setLowPrice(requestBo.getMinPrice() == null ? null : requestBo.getMinPrice().intValue());
        //最高价
        query.setHighPrice(requestBo.getMaxPrice() == null ? null : requestBo.getMaxPrice().intValue());
        //品牌列表
        List<String> catagory = requestBo.getCatagory();
        if (catagory != null) {
            List<String> brandIds = Lists.newArrayList();
            for (String s : catagory) {
                String[] strs = StringUtils.split(s, "&");
                if (strs != null) {
                    String brandId = strs[0];
                    brandIds.add(brandId);
                }
            }
            query.setHotelBrandList(StringUtils.join(brandIds, ","));
        }
        //是否立即确认
        query.setJustifyConfirm(Boolean.TRUE.equals(requestBo.getCheckImmediate()) ? "T" : null);
        hotelDataRequest.setFacilityEntity(query);
        //是否免费取消
        hotelDataRequest.setHotelFilterTagList(Boolean.TRUE.equals(requestBo.getFreeCancel()) ? "1" : null);

        //是否协议酒店
        hotelDataRequest.setOnlyContractHotel(requestBo.getProtatal());
        //是否含早餐
        hotelDataRequest.setHasBreakFast(requestBo.getBreakfast());
        //分页参数
        hotelDataRequest.setPageIndex(requestBo.getPageNum());
        hotelDataRequest.setHotelCount(requestBo.getPageSize());
        hotelDataRequest.setHiddenHotelList(requestBo.getHiddenHotelList());

        //经纬度
        if (StringUtils.isBlank(requestBo.getKey())) {
            if (StringUtils.isBlank(query.getHotelBrandList())){
                hotelDataRequest.setKeyWord(requestBo.getTitle());
            }
        } else {
            //解析key
            String key = requestBo.getKey();
            String[] strs = StringUtils.split(key, "&");
            if (strs != null) {
                String destinationId = strs[0];
                String destinationType = strs[1];
                chooseDest(hotelDataRequest, destinationId, destinationType, query, strs, requestBo);
            }
        }
        return hotelDataRequest;
    }

    /**
     * 设置排序类型
     */
    private void chooseOrderType(LocalHotelListRequestBo requestBo, HotelDataRequest hotelDataRequest) {
        switch (requestBo.getRecommendSort()) {
			case 1:
                hotelDataRequest.setOrderByName("MIN_PRICE");
                hotelDataRequest.setOrderByType("ASC");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.FALSE);
                }
                break;
            case 2:
                hotelDataRequest.setOrderByName("MIN_PRICE");
                hotelDataRequest.setOrderByType("DESC");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.FALSE);
                }
                break;
            case 3:
                hotelDataRequest.setOrderByName("DISTANCE");
                hotelDataRequest.setOrderByType("ASC");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.FALSE);
                }
                break;
            case 4:
                hotelDataRequest.setOrderByName("DISTANCE");
                hotelDataRequest.setOrderByType("DESC");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.FALSE);
                }
                break;
            case 5:
                hotelDataRequest.setOrderByName("STAR");
                hotelDataRequest.setOrderByType("ASC");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.FALSE);
                }
                break;
            case 6:
                hotelDataRequest.setOrderByName("STAR");
                hotelDataRequest.setOrderByType("DESC");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.FALSE);
                }
                break;
            case 7:
                hotelDataRequest.setOrderByName("H_RATING_OVERALL");
                hotelDataRequest.setOrderByType("ASC");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.FALSE);
                }
                break;
            case 8:
                hotelDataRequest.setOrderByName("H_RATING_OVERALL");
                hotelDataRequest.setOrderByType("DESC");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.FALSE);
                }
                break;
            default:
                hotelDataRequest.setOrderByName("RECOMMEND");
                if (isCtrip()) {
                    hotelDataRequest.setCorpRecommend(Boolean.TRUE);
                }
                break;
        }
    }

    /**
     * 解析查询条件key
     */
    private void chooseDest(HotelDataRequest hotelDataRequest, String destinationId, String destinationType,
                              HotelFacilitiesEntity hotelFacilitiesEntity, String[] strs, LocalHotelListRequestBo requestBo) {
        requestBo.setTag(false);
        String lon = "*".equals(strs[2]) || "null".equalsIgnoreCase(strs[2]) ? null : strs[2];
        String lat = "*".equals(strs[3]) || "null".equalsIgnoreCase(strs[3]) ? null : strs[3];
        MapSearchHotelEntity searchHotelEntity = null;
        if (StringUtils.isNotBlank(lon) && StringUtils.isNotBlank(lat)){
            searchHotelEntity = new MapSearchHotelEntity();
            searchHotelEntity.setDotX(lat);//纬度
            searchHotelEntity.setDotY(lon);//经度
        }

        switch (destinationType) {
            case "LOCATION":
                hotelDataRequest.setLocationID(destinationId);
                break;
            case "ZONE":
                hotelDataRequest.setZoneID(destinationId);
                if (searchHotelEntity != null){
                    hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
                    hotelDataRequest.setDefaultChannel(true);
                }
                break;
            case "METRO_STATION":
                hotelDataRequest.setMetroStationID(destinationId);
                if (searchHotelEntity != null){
                    searchHotelEntity.setRadius(5.0);
                    hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
                    hotelDataRequest.setDefaultChannel(true);
                }
                break;
            case "METRO":
                hotelDataRequest.setMetroID(destinationId);
                MapSearchHotelEntity mapSearchHotelEntity = hotelDataRequest.getMapSearchHotelEntity();
                mapSearchHotelEntity.setRadius(5.0);
                break;
            case "HOTEL_BRAND":
                hotelFacilitiesEntity.setHotelBrandList(destinationId);
                hotelDataRequest.setFacilityEntity(hotelFacilitiesEntity);
//                hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
                break;
            case "HOTEL":
                hotelDataRequest.setHotelList(destinationId);
//                hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
                break;
            case "PERSON":
                hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity); /* 个人定位需求*/
                hotelDataRequest.setKeyWord(requestBo.getTitle());
                requestBo.setTag(true);
                break;
            case "SCENIC_AREA":
                //景区只传景区id
                hotelDataRequest.setDistrictID(destinationId);
                if (searchHotelEntity != null){
                    hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
                    hotelDataRequest.setDefaultChannel(true);
                }
                break;
            case "LANDMARK":
			case "RAILWAY_STATION":
			case "AIRPORT":
				//机场
				//火车站
				//地标
                if (searchHotelEntity != null){
                    searchHotelEntity.setRadius(5.0);
                    hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
                    hotelDataRequest.setDefaultChannel(true);
                }
                break;
			case "INTL_AIRPORT":
			case "CORP_PLACE":
				//公司地标
				//国际机场
                if (searchHotelEntity != null){
                    hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
                    hotelDataRequest.setDefaultChannel(true);
                }
                break;
			case "HOTEL_GROUP":
                //酒店集团
                hotelFacilitiesEntity.setHotelMgrGroupList(destinationId);
                hotelDataRequest.setFacilityEntity(hotelFacilitiesEntity);
//                hotelDataRequest.setMapSearchHotelEntity(searchHotelEntity);
                break;
			default:
        }
    }

    /**
     * 酒店列表出参转换
     */
    protected LocalHotelListResponseBo convertLocalHotelListResponse(List<HotelDataEntity> hotelDataList, NumEntity numEntity, LocalHotelListRequestBo requestBo,
                                                                     HotelDataRequest request, SupplierProductBo supplierProductBo) {
        long start = System.currentTimeMillis();
        LocalHotelListResponseBo responseBo = new LocalHotelListResponseBo();
        //供应商标识
        responseBo.setSupplier(supplierProductBo.getSupplierCode());
        responseBo.setSupplierName(supplierProductBo.getSupplierName());
        responseBo.setTotalCount(numEntity.getResultCount());
        responseBo.setCurrentPage(requestBo.getPageNum());
        responseBo.setPageSize(requestBo.getPageSize());
        responseBo.setTotalPage((numEntity.getResultCount() + requestBo.getPageSize() - 1) / requestBo.getPageSize());
        List<LocalHotelListResponseBo.HotelListBean> hotelListBeans = hotelDataList.stream().map(entity -> {
            LocalHotelListResponseBo.HotelListBean hotelListBean = new LocalHotelListResponseBo.HotelListBean();
            HotelInfoEntity hotelInfo = entity.getHotelInfo();
            if (hotelInfo == null) {
                log.warn("酒店基础信息异常");
                return null;
            }
            hotelListBean.setSupplier(supplierProductBo.getSupplierCode());
            hotelListBean.setSupplierName(supplierProductBo.getSupplierName());
            hotelListBean.setKey(hotelInfo.getHotelID());
            hotelListBean.setHotelId(hotelInfo.getHotelID());
            hotelListBean.setShowTMCLabel(hotelInfo.getHasContractRoom());
            hotelListBean.setHotelName(hotelInfo.getHotelName());
            hotelListBean.setPic(hotelInfo.getLogoUrl());
            hotelListBean.setScore(BigDecimalUtil.getScaleString(hotelInfo.gethRatingOverall(), 2));
            LocalHotelListResponseBo.HotelListBean.StarLevel starLevel = new LocalHotelListResponseBo.HotelListBean.StarLevel();
            starLevel.setKey(BigDecimal.ZERO.compareTo(hotelInfo.getStar()) > 0 ? "0" : hotelInfo.getStar().toPlainString());
            starLevel.setValue(translateHotelType(hotelInfo.getStar()));
            // 是否星级钻级
            HotelStarLicenceEnum licenceEnum = HotelStarLicenceEnum.getByLicence(hotelInfo.getStarLicence());
            if(licenceEnum!=null){
                starLevel.setLicence(licenceEnum.getCode());
            }
            hotelListBean.setStarLevel(starLevel);
            hotelListBean.setDistance(hotelInfo.getLandMarkDistance());
            String zoneName = StringUtils.isBlank(hotelInfo.getZoneName()) ? "" : String.format("(%s)", hotelInfo.getZoneName());
            hotelListBean.setAddress(hotelInfo.getLocationName() + hotelInfo.getAddress() + zoneName);
            //列表酒店距离话术
            hotelListBean.setAppPlace(hotelInfo.getZoneName());
            translateDistance(requestBo, hotelInfo, request, hotelListBean);
            LocalHotelListResponseBo.HotelListBean.PriceBean priceBean = new LocalHotelListResponseBo.HotelListBean.PriceBean();
            //目前只有一家供应商
            List<LocalHotelListResponseBo.HotelListBean.PriceBean> priceBeans = Lists.newArrayList();
            priceBean.setName(supplierProductBo.getSupplierName());
            priceBean.setPrice(hotelInfo.getMinPrice());
            priceBeans.add(priceBean);
            hotelListBean.setPrice(priceBeans);
            hotelListBean.setProtatal(hotelInfo.getShowTMCLabel());
            hotelListBean.setWifi(hotelInfo.getHotelFacilities() == null ? Boolean.FALSE : hotelInfo.getHotelFacilities().getHasWirelessBroadNet());
            hotelListBean.setParking(hotelInfo.getHotelFacilities() == null ? Boolean.FALSE : hotelInfo.getHotelFacilities().getPark());
            List<HotelDetailResponseVO.Tip> serviceTips = getServiceTips(hotelInfo);
            hotelListBean.setServiceTips(serviceTips);
            hotelListBean.setLongitude(hotelInfo.getgDLON());
            hotelListBean.setLatitude(hotelInfo.getgDLAT());
            hotelListBean.setPic(convertPicUrl(hotelInfo.getLogoUrl(), false));
            return hotelListBean;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        responseBo.setHotelList(hotelListBeans);
        log.info("convertLocalHotelListResponse 耗时(毫秒)：" + (System.currentTimeMillis() - start));
        return responseBo;
    }

    /**
     * 酒店详情请求参数转换
     */
    protected HotelDetailRequest convertHotelDetailRequest(LocalHotelDetailRequestBo requestBo, HotelDetailRequest hotelDetailRequest, SupplierProductBo supplierProductBo) {
        Tree.Kv kv = requestBo.getTree().getKvs().get(0);
        hotelDetailRequest.setHotelID(kv.getHotelId());
        hotelDetailRequest.setCheckInDate(requestBo.getCheckInDate());
        hotelDetailRequest.setCheckOutDate(requestBo.getCheckOutDate());
        hotelDetailRequest.setCorpID(supplierProductBo.getSupplierCorpId());
        hotelDetailRequest.setUID(supplierProductBo.getSupplierUid());
        hotelDetailRequest.setCityID(requestBo.getSupplierCityId());
        if (isCtrip()){
            hotelDetailRequest.setLanguage("ZH_CN");
            hotelDetailRequest.setBasicRoomTypeCountPerHotel(-1);
        }
        return hotelDetailRequest;
    }

    /**
     * 获取酒店静态资源
     */
    protected HotelStaticInfoResponse getStaticInfo(LocalHotelDetailRequestBo requestBo, String supplierCode){
        try {
            SupplierProductBo staticSupplier = getCommonService().listSupplierProduct(requestBo.getOrgId(), supplierCode, getHotelOperatorTypeConfig().getGetHotelStaticData(), requestBo.getCorpPayType());
            if (staticSupplier == null) {
                log.warn("获取供应商url失败");
                return null;
            }
            String staticInfoUrl = staticSupplier.getProductUrl();
            //组装请求参数
            Map staticInfoRequest = new HashMap();
            staticInfoRequest.put("hotelID", Lists.newArrayList(requestBo.getTree().getKvs().get(0).getHotelId()));
            if (isCtrip()){
                staticInfoRequest.put("returnDataTypeList", Lists.newArrayList("HotelCommentEntity", "HotelLogoPic"));
            }
            HotelStaticInfoResponse staticInfo = getCommonService().doPostJSON(staticSupplier.getSupplierCode(), "酒店静态信息查询", staticInfoUrl, staticSupplier.getUserKey(), JsonUtils.toJsonString(staticInfoRequest), HotelStaticInfoResponse.class);
            if (!isCtrip() && !(staticInfo != null && (staticInfo.getErrorCode() == null || "0".equals(staticInfo.getErrorCode())))){
                log.warn(supplierCode + "静态数据查询失败：" + JsonUtils.toJsonString(staticInfo));
                return null;
            }
            if (isCtrip() && !(staticInfo != null && "Success".equals(staticInfo.getResponseStatus().getAck()))){
                log.warn(supplierCode + "静态数据查询失败：" + JsonUtils.toJsonString(staticInfo));
                return null;
            }
            return staticInfo;
        } catch (Exception e){
            log.warn("获取酒店静态资源失败", e);
        }
        return null;
    }
}