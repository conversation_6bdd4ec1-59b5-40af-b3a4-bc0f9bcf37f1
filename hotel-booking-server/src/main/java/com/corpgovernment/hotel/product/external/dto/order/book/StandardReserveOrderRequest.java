package com.corpgovernment.hotel.product.external.dto.order.book;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.corpgovernment.api.costcenter.model.TempCostCenterVo;
import com.corpgovernment.hotel.product.external.dto.order.BaseExternalRequest;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标准契约-预定下单请求类
 *
 * @see <a href="https://openapi.ctripbiz.com/#/serviceApi?apiId=1000512"></a>
 */
@Data
public class StandardReserveOrderRequest extends BaseExternalRequest {

    @ApiModelProperty("订单基础信息")
    private OrderBasicInfo orderBasicInfo;

    @ApiModelProperty("产品价格编码 房型主键，取值：预定流程中酒店详情productID")
    private String productID;

    @ApiModelProperty("订单房型信息")
    private RoomInfo roomInfo;

    @ApiModelProperty("入住人列表")
    private List<GuestInfo> guestInfoList;

    @ApiModelProperty("联系人")
    private ContactorInfo contactorInfo;

    @ApiModelProperty("支付信息")
    private PayInfo payInfo;

    @ApiModelProperty("发票信息")
    private InvoiceInfo invoiceInfo;

    @ApiModelProperty("发票配送信息")
    private InvoiceDeliveryInfo invoiceDeliveryInfo;

    @ApiModelProperty("价格信息")
    private PriceInfo priceInfo;

    @ApiModelProperty("供应商下单透传额外信息")
    public List<String> additionalSupplierInfo;

    @JsonIgnore
    @ApiModelProperty("附加信息")
    private Map<String, Object> additionalInformationMap;

    @Data
    public static class OrderBasicInfo {

        @ApiModelProperty("供应商公司ID")
        private String corpID;

        @ApiModelProperty("程曦平台订单号")
        private String platformOrderID;

        @ApiModelProperty("预定人UID")
        private String bookerUID;

    }

    @Data
    public static class RoomInfo {

        @ApiModelProperty("预订房间数量")
        private Integer roomQuantity;

        @ApiModelProperty("入住人数")
        private Integer guestQuantity;

        @ApiModelProperty("入住时间，格式：yyyy-MM-dd")
        private String checkInDate;

        @ApiModelProperty("离店时间，格式：yyyy-MM-dd")
        private String checkOutDate;

    }

    @Data
    public static class GuestInfo {

        @ApiModelProperty("【程曦平台】入住人唯一标识")
        @JsonProperty("uID")
        private String uID;

        @ApiModelProperty("入住人名称")
        private String name;

        @ApiModelProperty("手机号码国家码")
        private String countryCode;

        @ApiModelProperty("入住人手机号码")
        private String mobilePhone;

        @ApiModelProperty("入住人邮箱")
        private String email;

        @ApiModelProperty("房间索引")
        private Integer roomIndex;

        @ApiModelProperty("项目编码")
        private String projectCode;

        @ApiModelProperty("成本中心编码")
        private String costCenterCode;

        @ApiModelProperty("证件类型")
        private Integer cardType;

        @ApiModelProperty("证件号")
        private String cardNo;

        @ApiModelProperty("成本中心名称")
        private String costCenterName;

        /**
         * 成本中心关联法人机构Code
         */
        @ApiModelProperty("成本中心关联法人机构Code")
        private String costCenterCorporationCode;

        /**
         * 成本中心关联法人机构名字
         */
        @ApiModelProperty("成本中心关联法人机构名字")
        private String costCenterCorporationName;
        /**
         * 成本中心关联法人机构主账户
         */
        @ApiModelProperty("成本中心关联法人机构主账户")
        private String supplierAccountId;

        private List<TempCostCenterVo> costCenterVoList;
        private List<TempCostCenterVo> costCenterInfoList;

        private Map<String, Object> fieldMap;
    }

    @Data
    public static class ContactorInfo {

        @ApiModelProperty("联系人名称")
        private String name;

        @ApiModelProperty("手机号码国家码")
        private String countryCode;

        @ApiModelProperty("入住人手机号码")
        private String mobilePhone;

        @ApiModelProperty("联系人邮箱")
        private String email;

    }

    @Data
    public static class PayInfo {

        @ApiModelProperty("支付方式（ACCNT 公司账户支付、MIXPAY：混付、PPAY 个人支付")
        private String payType;

        private List<MixPayWayInfo> mixPayWayInfoList;
    }

    @Data
    public static class MixPayWayInfo {

        @ApiModelProperty("混合支付方式（ACCNT 公司账户支付、PPAY 个人支付")
        private String mixPayWay;

        @ApiModelProperty("支付金额")
        private BigDecimal payAmount;
    }

    @Data
    public static class InvoiceInfo {

        @ApiModelProperty("发票类型（VAT 增值税纸质普票、EVAT 电子普通发票、VATSpecial 增值税纸质专票  DInvoice:增值税数电普票  DVatInvoice: 增值税数电专票）")
        private String orderInvoiceTargetType;

        @ApiModelProperty("发票抬头类型 P:个人 E:企业 G:政府机关行政单位")
        private String invoiceTitleType;

        @ApiModelProperty("发票抬头")
        private String invoiceTitle;

        @ApiModelProperty("纳税人识别号")
        private String taxpayerNumber;

        @ApiModelProperty("公司名称")
        private String companyName;

        @ApiModelProperty("公司地址")
        private String companyAddress;

        @ApiModelProperty("公司电话")
        private String companyPhone;

        @ApiModelProperty("开户银行名称")
        private String companyBankName;

        @ApiModelProperty("开户银行账号")
        private String companyBankAccount;

        @ApiModelProperty("电子邮箱")
        private String email;
    }

    @Data
    public static class InvoiceDeliveryInfo {

        @ApiModelProperty("联系人名字")
        private String contactName;

        @ApiModelProperty("发票配送联系方式（手机号）")
        private String postPhone;

        @ApiModelProperty("发票地址省份")
        private String province;

        @ApiModelProperty("发票地址城市")
        private String city;

        @ApiModelProperty("发票地址行政区")
        private String canton;

        @ApiModelProperty("发票具体地址")
        private String address;
    }

    @Data
    public static class PriceInfo {

        @ApiModelProperty("总价【房费+服务费+配送费")
        private BigDecimal totalAmount;

        @ApiModelProperty("服务费")
        private BigDecimal serviceFee;
    }

}
