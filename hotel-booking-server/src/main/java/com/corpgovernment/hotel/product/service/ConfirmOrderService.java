package com.corpgovernment.hotel.product.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.corpgovernment.hotel.product.external.dto.order.confirm.StandardConfirmOrderRequest;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.corpgovernment.api.hotel.product.bo.HotelConfirmOrderRequestBO;
import com.corpgovernment.api.hotel.product.bo.HotelConfirmOrderResponseBO;
import com.corpgovernment.api.ordercenter.enums.OperationTitleEnums;
import com.corpgovernment.api.ordercenter.enums.OrderStatusEnum;
import com.corpgovernment.api.platform.soa.paymentbill.PaymentBillDetailResponse;
import com.corpgovernment.api.supplier.bo.suppliercompany.ListSupplierProductRequestBo;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.soa.constant.PubOwnEnum;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.apollo.BaseConfig;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseConst;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.common.base.BaseSupplierProductRequestBO;
import com.corpgovernment.common.bo.HttpRetryBo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.SiteEnum;
import com.corpgovernment.common.supplier.HttpRetryInspect;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.common.utils.Md5Util;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BasicDataClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierCompanyClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.external.client.SupplierSoaClient;
import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import com.corpgovernment.hotel.product.external.dto.order.confirm.StandardConfirmOrderResponse;
import com.corpgovernment.hotel.product.model.ctrip.confirmorder.SupplierConfirmOrderRequestBO;
import com.corpgovernment.hotel.product.model.ctrip.confirmorder.SupplierConfirmOrderResponseBO;
import com.corpgovernment.hotel.product.model.ctrip.response.OrderAuditResponse;
import com.corpgovernment.hotel.product.model.ctrip.response.ResponseStatus;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.corpgovernment.hotel.product.supplier.enums.SupplierEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

import static com.corpgovernment.hotel.product.external.constant.SupplierConstant.ConfirmOrder.REQUEST_ADDITIONAL_MAP_KEY_UID;

/**
 * 订单确认
 */
@Service
@Slf4j
public class ConfirmOrderService extends BaseService {

    @Autowired
    protected CommonService commonService;
    @Autowired
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    @Autowired
    private PayClientLoader payClientLoader;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private SupplierCompanyClientLoader supplierCompanyClientLoader;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private BasicDataClientLoader basicDataClientLoader;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private SupplierSoaClient supplierSoaClient;

    private final static String CONFIRM_ORDER_PREFIX = "confirm_order_";
    private final static String CONFIRMED_ORDER_PREFIX = "confirmed_order_";

    /**
     * 订单确认
     */
    @Transactional
    public HotelConfirmOrderResponseBO confirmOrder(HotelConfirmOrderRequestBO request) {
        StringBuilder logContent = new StringBuilder();
        Long orderId = request.getOrderId();
        logContent.append("订单确认:订单号：").append(orderId);

        /**
         * 把获取数据等耗时操作，放在锁外面，避免前请求持有锁但确认失败，后请求获取不到锁未执行，导致最终未订单确认
         */
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        if (hoOrder == null) {
            addElkInfoLog("订单号：%s未查询到订单，确认流程结束", orderId);
            return HotelConfirmOrderResponseBO.failure("查询订单失败，无法确认订单");
        }
        if (!Objects.equals(hoOrder.getOrderStatus(), OrderStatusEnum.TW.getType())) {
            addElkInfoLog("订单号：%s的状态为：%s，无法确认订单", orderId, hoOrder.getOrderStatus());
            return HotelConfirmOrderResponseBO.failure("订单状态不对，无法确认订单");
        }
        // 校验供应商订单状态，判断是否可确认订单
        if (!validateOrderStatusPush(hoOrder)) {
            addElkInfoLog("订单号：%s校验供应商订单状态失败，无法确认订单", orderId);
            return HotelConfirmOrderResponseBO.failure("供应商订单状态不对，无法确认订单");
        }
        List<PaymentBillDetailResponse> paymentBillDetailList = payClientLoader.getPaymentBillDetailList(orderId);
        if (CollectionUtils.isEmpty(paymentBillDetailList)) {
            addElkInfoLog("订单号：%s未查询到支付单，确认流程结束", orderId);
            return HotelConfirmOrderResponseBO.failure("查询支付单失败，无法确认订单");
        }
        BigDecimal paidAmount = paymentBillDetailList.stream().filter(e -> Objects.equals(e.getStatus(), "S"))
            .map(PaymentBillDetailResponse::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal amount = hoOrder.getAmount();
        // 判断已支付金额和订单金额
        // if (paidAmount.compareTo(amount) <= 0) {
        // addElkInfoLog("订单总金额为：%s，已支付金额为：%s，未完成全部支付", amount, paidAmount);
        // return HotelConfirmOrderResponseBO.failure("未完成全部支付，无法确认订单");
        // }

        String serialNo = paymentBillDetailList.stream().map(PaymentBillDetailResponse::getSerialNo)
            .filter(Objects::nonNull).findFirst().orElse(null);
        if (StringUtils.isBlank(serialNo)) {
            serialNo = request.getSerialNo();
        }

        PaymentBillDetailResponse paymentBillDetailResponse = paymentBillDetailList.stream()
            .filter(e -> Objects.equals(e.getStatus(), "S"))
            .findFirst()
            .get();

		String key = CONFIRM_ORDER_PREFIX + orderId;
		RLock lock = redissonClient.getLock(key);
		initElkLog();
		addElkInfoLog("进入订单确认流程");
		boolean flag = lock.tryLock();
		if (!flag) {
			addElkInfoLog("获取redis锁失败，key：{}, 时间：{}", key, DateUtil.dateToString(new Date()));
			return HotelConfirmOrderResponseBO.failure("获取redis锁失败，无法确认订单");
		}
		RLock lock1 = redissonClient.getLock(CONFIRMED_ORDER_PREFIX + orderId);
		if (lock1.isLocked()) {
			addElkInfoLog("订单：%s, 已调过确认接口，无需再次确认订单", orderId);
			return HotelConfirmOrderResponseBO.failure("已调过确认接口，无需再次确认订单");
		}
		try {
			// TODO 优化改用Spring-retry做失败重试
			HotelConfirmOrderResponseBO result = null;
			int maxRetryCount = Integer.parseInt(hotelApollo.getAppProperty("confirm.order.max.retry.count", "0"));
			long sleepMillis = Long.parseLong(hotelApollo.getAppProperty("confirm.order.retry.sleep.millis", "0"));
			for (int retryCount = 0; retryCount <= maxRetryCount; retryCount++) {
				// 失败重试前先sleep一下
				if (retryCount > 0) {
					logContent.append("失败重试第" + retryCount + "次");
					if (sleepMillis > 0) {
						try {
							Thread.sleep(sleepMillis);
						} catch (InterruptedException e) {
							log.error("订单确认重试Sleep异常", e);
							logContent.append("sleep InterruptedException");
						}
					}
				}
				result = this.convertFromSupplier(this.confirmOrder(hoOrder, serialNo,
                        Objects.nonNull(paymentBillDetailResponse) ? paymentBillDetailResponse.getPayChannel() : ""));
				if (result.isSuccess()) {
					HoOrder record = new HoOrder();
					record.setOrderId(hoOrder.getOrderId());
					record.setConfirmApplyTime(new Date());
					hoOrderLoader.updateByOrderId(record);
					// 确认成功，订单加锁，防止重复调用供应商确认接口
					lock1.lock(10, TimeUnit.MINUTES);
					break;
				}
			}
			logContent.append("确认结果：").append(JsonUtils.toJsonString(result));
			if (result == null || !result.isSuccess()) {
				log.error("酒店订单确认异常，订单号：" + orderId);
			}
			return result;
		} catch (Exception e) {
			log.error("订单确认处理异常", e);
			logContent.append(String.format("，%s(%s)", StringUtils.isBlank(e.getMessage()) ? "空指针异常" : e.getMessage(), "-1"));
			addElkInfoLog("订单确认处理异常");
			return HotelConfirmOrderResponseBO.failure("订单确认处理异常");
		} finally {
			commonService.addLog(BaseConst.OPERATOR_SYSTEM, String.valueOf(orderId), OperationTitleEnums.HOTELCONFIRM_SUPPLIER, logContent.toString());
			log.info("ConfirmOrderService.confirmOrder订单确认{} request：{}{}{}{}", System.lineSeparator(), JsonUtils.toJsonString(request), System.lineSeparator(), System.lineSeparator(), this.getElkInfoLog());
			clearElkLog();
			lock.unlock();
		}
	}


    /**
     * 根据供应商订单状态，判断是否可确认订单
     *
     * @param hoOrder
     * @return
     */
    private boolean validateOrderStatusPush(HoOrder hoOrder) {
        String supplierCode = hoOrder.getSupplierCode();

        // 供应商是携程时，供应商订单需要有Approving状态才能确认订单
        if (Objects.equals(SupplierEnum.CTRIP.getCode(), supplierCode)) {
            return hoOrder.getApprovingTime() != null;
        }

        // 默认，供应商订单需要有Submitted状态才能确认订单
        return hoOrder.getSubmitTime() != null;
    }

    private HotelConfirmOrderResponseBO convertFromSupplier(StandardConfirmOrderResponse standardConfirmOrderResponse) {
        if (!SupplierConstant.AUDIT_TYPE_T.equals(standardConfirmOrderResponse.getAuditType())) {
            return HotelConfirmOrderResponseBO.failure("订单确认处理异常");
        }

        return HotelConfirmOrderResponseBO.create(String.valueOf(HotelResponseCodeEnum.SUCCESS_CODE.code()),
            HotelResponseCodeEnum.SUCCESS_CODE.message());
    }

    public StandardConfirmOrderResponse confirmOrder(HoOrder orderInfo, String serialNo, String payChannel) {
        StandardConfirmOrderRequest request = new StandardConfirmOrderRequest();
        request.setOrderID(orderInfo.getSupplierOrderId());
        request.setAuditType(SupplierConstant.AUDIT_TYPE_T);
        request.setSerialNo(serialNo);
        request.setPayChannel(payChannel);
        request.setCorpID(orderInfo.getSupplierCorpId());

        request.setSupplierCode(orderInfo.getSupplierCode());
        request.setCorpPayType(orderInfo.getCorpPayType());
        request.setCompanyCode(orderInfo.getCorpId());
        HashMap<String, Object> additionalInfoMap = Maps.newHashMap();
        additionalInfoMap.put(REQUEST_ADDITIONAL_MAP_KEY_UID, orderInfo.getSupplierUid());
        request.setAdditionalInfoMap(additionalInfoMap);
        addElkInfoLog("进入供应商订单确认流程 标准契约请求参数: %s", JsonUtils.toJsonString(request));

        return supplierSoaClient.confirmOrder(request);
    }

    public List<SupplierProductBo> getSupplierList(BaseSupplierProductRequestBO request) {
        ListSupplierProductRequestBo requestBo = new ListSupplierProductRequestBo();
        requestBo.setSupplierCode(request.getSupplierCode());
        requestBo.setProductType(SiteEnum.FLIGHTINTL.getCode());
        requestBo.setOperateType(hotelOperatorTypeConfig.getConfirmOrder());
        requestBo.setCompanyCode(request.getCompanyCode());
        // 转换因公因私
        requestBo.setBusPriType(PubOwnEnum.OWN.name().equals(request.getCorpPayType()) ? 2 : 1);
        return supplierCompanyClientLoader.listSupplierProduct(requestBo);
    }

    /**
     * 获取供应商request
     *
     * @param orderInfo
     * @return
     */
    private BaseSupplierProductRequestBO toBaseSupplierRequest(HoOrder orderInfo) {
        BaseSupplierProductRequestBO request = new BaseSupplierProductRequestBO();
        request.setSupplierCode(orderInfo.getSupplierCode());
        request.setCompanyCode(orderInfo.getCorpId());
        request.setCorpPayType(orderInfo.getCorpPayType());
        return request;
    }

    /**
     * 调供应商确认接口
     *
     * @param orderInfo
     * @param supplierProduct
     * @return
     */
    public SupplierConfirmOrderResponseBO commonConfirmOrder(HoOrder orderInfo, SupplierProductBo supplierProduct,
        String serialNo, String payChannel) {
        log.info("订单确认请求:" + new Date());
        String supplierCode = supplierProduct.getSupplierCode();
        String url = supplierProduct.getProductUrl();
        SupplierConfirmOrderRequestBO request = new SupplierConfirmOrderRequestBO();
        request.setOrderID(orderInfo.getSupplierOrderId());
        request.setUID(orderInfo.getSupplierUid());
        request.setCorpID(orderInfo.getSupplierCorpId());
        request.setSerialNo(serialNo);
        request.setPayChannel(payChannel);
        try {
            SupplierConfirmOrderResponseBO response = null;
            if (Boolean.FALSE.equals(BaseConfig.defaultConfig().enabledSupplierClient())) {
                String httpResult = commonService.doPostJSON(supplierCode, "订单确认", url, supplierProduct.getUserKey(),
                    JsonUtils.toJsonString(request));
                response = JsonUtils.parse(httpResult, SupplierConfirmOrderResponseBO.class);
            } else {
                response =
                    commonService.invokeSupplierInterface(supplierCode, "订单确认", url, supplierProduct.getUserKey(),
                        JsonUtils.toJsonString(request), SupplierConfirmOrderResponseBO.class);
            }
            if (response == null) {
                return SupplierConfirmOrderResponseBO.failure("供应商接口异常");
            }
            return response;
        } catch (IOException e) {
            log.error("供应商" + supplierCode + "订单确认" + "入参:" + JsonUtils.toJsonString(request), e);
            return SupplierConfirmOrderResponseBO.failure("供应商接口异常");
        } catch (CorpBusinessException e) {
            log.error("供应商订单确认接口异常", e);
            SupplierConfirmOrderResponseBO supplierConfirmOrderResponseBO = new SupplierConfirmOrderResponseBO();
            supplierConfirmOrderResponseBO.setErrorCode(String.valueOf(e.getResultCode()));
            supplierConfirmOrderResponseBO.setMessage(e.getMsg());
            return supplierConfirmOrderResponseBO;
        }
    }

    public SupplierConfirmOrderResponseBO ctripConfirmOrder(HoOrder orderInfo, SupplierProductBo supplierProduct) {
        addElkInfoLog("订单确认请求:%s", DateUtil.dateToString(new Date()));
        /*String payType = orderInfo.getPaytype();
        if (Objects.equals(payType, PayTypeEnum.PPAY.getType())) {
        	addElkInfoLog("订单号：%s个付无需调确认接口，确认流程结束", orderInfo.getOrderId());
        	return SupplierConfirmOrderResponseBO.create("0", "个付无需调确认接口");
        }*/
        String supplierCode = supplierProduct.getSupplierCode();
        String url = supplierProduct.getProductUrl();
        Map requestParam = new HashMap();
        String ticket = basicDataClientLoader.ctripTokenByCorpID(orderInfo.getSupplierCorpId(), supplierCode);
        SupplierConfirmOrderRequestBO.Auth auth = new SupplierConfirmOrderRequestBO.Auth();
        auth.setAppKey("obk_" + orderInfo.getSupplierCorpId());
        auth.setTicket(ticket);
        String orderId = orderInfo.getSupplierOrderId();
        // 酒店产线
        String orderType = "2";
        // 同意授权
        String auditType = "T";
        String appSecurity =
            Md5Util.md5Hex(basicDataClientLoader.getAppSecurity(orderInfo.getSupplierCorpId(), supplierCode));
        String signature = Md5Util.md5Hex(orderId + orderType + auditType + appSecurity);
        SupplierConfirmOrderRequestBO requestBO = new SupplierConfirmOrderRequestBO();
        requestBO.setAuth(auth);
        requestBO.setSignature(signature);
        requestBO.setOrderID(orderId);
        requestBO.setOrderType(orderType);
        requestBO.setAuditType(auditType);

        String httpResult = HttpRetryInspect.retry(
            new HttpRetryBo(JsonUtils.toJsonString(requestBO), orderInfo.getSupplierCorpId(), supplierCode), data -> {
                try {
                    if (Boolean.FALSE.equals(BaseConfig.defaultConfig().enabledSupplierClient())) {
                        return commonService.doPostJSON(supplierCode, "订单确认", url, "", data);
                    }
                    SupplierConfirmOrderResponseBO supplierConfirmOrderResponseBO = commonService
                        .invokeSupplierInterface(supplierCode, "订单确认", url, "", data,
                            SupplierConfirmOrderResponseBO.class);
                    return JsonUtils.toJsonString(supplierConfirmOrderResponseBO);
                } catch (CorpBusinessException e) {
                    log.error("供应商订单确认接口异常", e);
                    SupplierConfirmOrderResponseBO failedResponseBO = new SupplierConfirmOrderResponseBO();
                    failedResponseBO.setErrorCode(String.valueOf(e.getResultCode()));
                    failedResponseBO.setMessage(e.getMsg());
                    return JsonUtils.toJsonString(failedResponseBO);
                } catch (Exception ex) {
                    log.error("酒店订单确认异常", ex);
                }
                return null;
            });
        OrderAuditResponse response = JsonUtils.parse(httpResult, OrderAuditResponse.class);
        if (response == null || response.getStatus() == null) {
            return SupplierConfirmOrderResponseBO.failure("供应商接口异常");
        }
        ResponseStatus responseStatus = response.getStatus();
        return SupplierConfirmOrderResponseBO.create(responseStatus.getErrorCode(), responseStatus.getMessage());
    }

    public HotelConfirmOrderResponseBO confirmOrderWithoutCheck(HotelConfirmOrderRequestBO request) {
        Long orderId = request.getOrderId();
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        if (hoOrder == null) {
            return HotelConfirmOrderResponseBO.failure("查询订单失败，无法确认订单");
        }
        List<PaymentBillDetailResponse> paymentBillDetailList = payClientLoader.getPaymentBillDetailList(orderId);
        String serialNo = paymentBillDetailList.stream().map(PaymentBillDetailResponse::getSerialNo)
                .filter(Objects::nonNull).findFirst().orElse(null);

        Optional<PaymentBillDetailResponse> paymentBillDetailResponseOptional = paymentBillDetailList.stream()
                .filter(e -> Objects.equals(e.getStatus(), "S"))
                .findFirst();


        StandardConfirmOrderResponse standardConfirmOrderResponse = this.confirmOrder(hoOrder, serialNo, paymentBillDetailResponseOptional.isPresent() ? paymentBillDetailResponseOptional.get().getPayChannel() : "");
        return this.convertFromSupplier(standardConfirmOrderResponse);
    }
}
