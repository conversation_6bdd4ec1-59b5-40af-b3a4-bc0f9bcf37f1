package com.corpgovernment.hotel.product.entity.db;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/22
 */
@Data
@Table(name = "ms_hotel_room_relation")
public class HotelRoomRelationDo {

    @Id
    private Long id;

    private String masterSupplierCode;

    private String masterHotelId;

    private String masterRoomId;

    private String subSupplierCode;

    private String subHotelId;

    private String subRoomId;

    private String matchScore;

    private Boolean matchStatus;

    private Boolean deleted;

    private Date createTime;

    private Date updateTime;

}
