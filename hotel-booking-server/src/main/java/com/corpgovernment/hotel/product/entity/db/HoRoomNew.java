package com.corpgovernment.hotel.product.entity.db;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.*;

import lombok.Data;


@Data
@Table(name = "ho_room_new")
public class HoRoomNew implements Serializable {

    /**
     * id
     **/
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 订单号
     **/
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 房间id
     **/
    @Column(name = "room_id")
    private String roomId;

    /**
     * 房间名称
     **/
    @Column(name = "room_name")
    private String roomName;

    /**
     * 天数
     **/
    @Column(name = "days")
    private Integer days;

    /**
     * 价格标题
     */
    @Column(name = "applicative_area_title")
    private String applicativeAreaTitle;

    /**
     * 价格描述
     */
    @Column(name = "applicative_area_desc")
    private String applicativeAreaDesc;

    /**
     * 取消类型
     **/
    @Column(name = "cancel_policy_type")
    private Integer cancelPolicyType;

    /**
     * 取消描述
     **/
    @Column(name = "cancel_policy_desc")
    private String cancelPolicyDesc;

    /**
     * 可住人数
     **/
    @Column(name = "person_count")
    private Integer personCount;

    /**
     * 预订房间数
     **/
    @Column(name = "room_quantity")
    private Integer roomQuantity;

    /**
     * 床型名称
     */
    @Column(name = "bed_name")
    private String bedName;

    /**
     * true则视为对应产品包含酒店套餐
     */
    @Column(name = "package_room")
    private Boolean packageRoom;

    /**
     * 打包售卖房型打包Id
     */
    @Column(name = "package_id")
    private Integer packageId;

    /**
     * 房型图片
     */
    @Column(name = "pic_urls")
    private String picUrls;

    /**
     * 房型信息
     */
    @Column(name = "room_info_context")
    private String roomInfoContext;

    /**
     * 套餐内容
     */
    @Column(name = "package_room_context")
    private String packageRoomContext;

    /**
     * 所属母房型下最低价非协议房型的房费均价
     */
    @Column(name = "non_protocol_min_avg_price")
    private BigDecimal nonProtocolMinAvgPrice;

    /**
     * 所属母房型下最高价非协议房型的房费均价
     */
    @Column(name = "non_protocol_max_avg_price")
    private BigDecimal nonProtocolMaxAvgPrice;

    /**
     * 所属母房型下最低价协议房型的房费均价
     */
    @Column(name = "protocol_min_avg_price")
    private BigDecimal protocolMinAvgPrice;

    /**
     * 所属母房型下最低价协议房型的所属供应商
     */
    @Column(name = "protocol_min_avg_price_supplier_code")
    private String protocolMinAvgPriceSupplierCode;

    /**
     * 物理房型id
     */
    @Column(name = "basic_room_id")
    private String basicRoomId;

    /**
     * 餐食类型
     */
    @Column(name = "meal_type")
    private Integer mealType;

    /**
     * 最晚取消时间
     */
    @Column(name = "last_cancel_time")
    private Date lastCancelTime;

    /**
     * 最小连住天数
     */
    @Column(name = "min_consecutive_days")
    private Integer minConsecutiveDays;


    /**
     * 创建时间
     **/
    @Column(name = "datachange_createtime")
    private Date datachangeCreatetime;

    /**
     * 最后修改时间
     **/
    @Column(name = "datachange_lasttime")
    private Date datachangeLasttime;

}
