package com.corpgovernment.hotel.product.external.dto.order.modify;

import java.util.List;

import lombok.Data;

/**
 * 携程订单可修改间夜查询 返回值
 *
 * <AUTHOR> dingjian
 * @date : 2024/4/9 21:32
 * @since : 1.0
 */
@Data
public class CtripOrderModifiableRoomNightQueryResponse {

    /**
     * 间夜信息列表
     */
    private List<RoomNightInfo> roomNightInfoList;

    /**
     * 接口返回状态
     */
    private ResponseStatus status;

    @Data
    public static class ResponseStatus {

        private Boolean success;

        private Integer errorCode;

        private String errorMessage;
    }

    @Data
    public static class RoomNightInfo {
        /**
         * 日期 format：yyyy-MM-dd
         */
        private String date;
        /**
         * 房间数
         */
        private Integer quantity;
        /**
         * 入住人信息列表
         */
        private List<ClientInfo> clientInfoList;
    }

    @Data
    public static class ClientInfo {
        /**
         * 入住人ID
         */
        private Long clientInfoId;
        /**
         * 房间索引
         */
        private Integer roomIndex;
        /**
         * 入住人姓名
         */
        private String name;
    }
}
