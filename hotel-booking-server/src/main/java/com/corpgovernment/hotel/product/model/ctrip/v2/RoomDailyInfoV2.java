package com.corpgovernment.hotel.product.model.ctrip.v2;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class RoomDailyInfoV2 implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 日期（输出格式化为String）
	 */
	private String effectDate;

	/**
	 * 售价
	 */
	private BigDecimal sellPrice;

	/**
	 * 币种，实价币种代码
	 */
	private String currency;

	/**
	 * 餐食数量
	 */
	private Integer meals;
}
