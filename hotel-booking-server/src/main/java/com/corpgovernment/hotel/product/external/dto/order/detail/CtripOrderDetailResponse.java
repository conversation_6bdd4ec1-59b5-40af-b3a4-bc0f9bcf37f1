package com.corpgovernment.hotel.product.external.dto.order.detail;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
/**
 * 携程契约 -订单详情响应类
 */
@Data
public class CtripOrderDetailResponse {
    /**
     * 非携程返回
     */
    private String errorCode;
    /**
     * 非携程返回
     */
    private String message;
    /**
     * 携程返回
     */
    private ResponseStatus status;
    private List<Itinerary> itineraryList;

    @Data
    public static class ResponseStatus {
        private Boolean success;
        private String message;
        private String errorCode;
    }

    @Data
    public static class Itinerary {
        private List<HotelOrderInfo> hotelOrderInfoList;
    }

    @Data
    public static class HotelOrderInfo {
        private String orderId;
        @JsonProperty(value = "uID")
        private String supplierUid;
        @JsonProperty(value = "platformOrderID")
        private String platformOrderId;
        @JsonProperty(value = "orderDetailStatus")
        private String orderDetailStatus;
        @JsonProperty(value = "orderDetailStatusName")
        private String orderDetailStatusName;
        @JsonProperty(value = "customPayAmount")
        private BigDecimal customPayAmount;
        @JsonProperty(value = "ladderDeductAmount")
        private BigDecimal ladderDeductAmount;
        /**
         * 人房详情 用于提前离店
         */
        private List<RoomDetailInfo> roomDetailList;
        /**
         * 订单退款信息
         */
        private List<RefundInfo> refundInfo;
        private List<ClientInfoEntity> clientInfo;
        /**
         * 房间数
         */
        private Integer roomQuantity;
        /**
         * 间夜数
         */
        private Integer roomDays;

        /**
         * 公司支付金额
         */
        private BigDecimal settlementACCNTAmt;

        /**
         * 个人支付金额
         */
        private BigDecimal settlementPersonAmt;

        /**
         * 订单取消原因Code
         */
        private String cancelReasonCode;

        /**
         * 订单取消原因描述
         */
        private String cancelReasonDesc;

        /**
         * 酒店名称
         */
        private String hotelName;
        /**
         * 城市ID
         */
        private String cityId;
        /**
         * 城市名称
         */
        private String cityName;
        /**
         * 酒店地址
         */
        private String address;
        /**
         * 酒店电话
         */
        private String telephone;
        /**
         * 联系人
         */
        private String contactName;
        /**
         * 联系人电话
         */
        private String contactTel;
        /**
         * 入住时间
         */
        private String startTime;
        /**
         * 离店时间
         */
        private String endTime;
        /**
         * T：专票，F：普票
         */
        private String vatFlag;
        /**
         * C:协议；M:会员
         */
        private String hotelTypeCode;
        /**
         * 前收服务费
         */
        private BigDecimal frontendServiceFee;
        /**
         * 后收服务费
         */
        private BigDecimal afterServiceFee;
        /**
         * 快递费/配送费
         */
        private BigDecimal postAmount;
        /**
         * 	每日房价信息
         */
        private List<RoomInfo> roomInfo;
        /**
         * 	加收税额（手续费）
         */
        private BigDecimal addedFees;
        /**
         * 退款金额信息
         */
        private RefundAmountInfo refundAmountInfo;
    }

    @Data
    public static class RefundAmountInfo{
        /**
         * 退订费：罚金（阶梯罚金）+违约金（提前离店酒店收取费用）
         */
        private BigDecimal subcriptionFee;
    }

    @Data
    public static class RoomInfo {
        /**
         * 房型名称
         */
        private String roomName;
        /**
         * 床型
         */
        private String bedType;
        /**
         * 价格
         */
        private BigDecimal price;
        /**
         * 早餐份数
         */
        private Integer breakfast;
        /**
         * 入住时间
         */
        private String ETA;
        /**
         * 离店时间
         */
        private String ETD;
    }

    @Data
    public static class RefundInfo {
        private String refundInfoID;
        private List<RefundPaymentInfo> refundPaymentList;

    }

    @Data
    public static class RefundPaymentInfo {

        /**
         * 退款流水ID
         */
        private String refundId;
        /**
         * 退款途径：CorpAccount-退公司账户；Personal-退个人账户；Welfare—退福利金
         */
        private String refundChannel;
        /**
         * 退款金额
         */
        private BigDecimal amount;
    }

    @Data
    public static class RoomDetailInfo {
        /**
         * 供应商酒店ID
         */
        @JsonProperty(value = "hotelID")
        private String hotelId;
        /**
         * 供应商房间ID
         */
        @JsonProperty(value = "roomID")
        private String roomId;
        /**
         * 入住日期，格式：yyyy-MM-dd
         */
        @JsonProperty(value = "checkInDate")
        private String checkInDate;
        /**
         * 离店日期，格式：yyyy-MM-dd
         */
        @JsonProperty(value = "checkOutDate")
        private String checkOutDate;
        /**
         * 入住人名字
         */
        @JsonProperty(value = "passengerName")
        private String passengerName;
        /**
         * 服务费
         */
        @JsonProperty(value = "serviceFee")
        private BigDecimal serviceFee;
        /**
         * 取消费用
         */
        @JsonProperty(value = "cancelFee")
        private BigDecimal cancelFee;
        /**
         * 房费明细
         */
        @JsonProperty(value = "roomPriceList")
        private List<RoomPriceInfo> roomPriceList;
    }

    @Data
    public static class RoomPriceInfo {
        /**
         * 日期，格式：yyyy-MM-dd
         */
        @JsonProperty(value = "date")
        private String date;
        /**
         * 房费
         */
        @JsonProperty(value = "price")
        private BigDecimal price;
    }

    @Data
    public static class ClientInfoEntity {
        /**
         * 旅客姓名
         */
        private String clientName;
        /**
         * RoomIndex
         */
        private Integer roomIndex;

        /**
         * 房间数
         */
        private Integer quantity;

        /**
         * 实际离店日期,格式：yyyy-MM-dd hh:mm:ss
         */
        private String actualDepartureTime;

        /**
         * 实际入住日期,格式：yyyy-MM-dd hh:mm:ss
         */
        private String actualCheckInTime;

        /**
         * 入住人手机号码
         */
        private String mobilePhone;
    }
}
