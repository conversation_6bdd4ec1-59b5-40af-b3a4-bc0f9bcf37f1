package com.corpgovernment.hotel.product.model.ctrip.v2;


import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RemarkV2 implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 可选备注key
	 */
	private String key;
	/**
	 * 可选备注id
	 */
	private String id;
	/**
	 * 标题，在可选项多选选择时，可以根据title分组
	 */
	private String title;
	/**
	 * 描述
	 */
	private String desc;
	/**
	 * 单选还是多选
	 */
	private String unique;
	/**
	 * true:客户端如果不填写这个分组下的任何选项，那么用这个项作为group的value处理。
	 * false：客户端不会使用这个选项作为默认值。没有指定值：那么这个Group不会有默认值填充。
	 */
	private Boolean defaultOption;
	/**
	 是否要求客户输入可选项。例如7天可选项：牙具，杯具，拖鞋数量等。
	 */
	private Boolean needUserValue;
}
