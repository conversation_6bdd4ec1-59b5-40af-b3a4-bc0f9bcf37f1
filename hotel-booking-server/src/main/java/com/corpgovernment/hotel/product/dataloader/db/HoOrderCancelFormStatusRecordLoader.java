package com.corpgovernment.hotel.product.dataloader.db;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.corpgovernment.hotel.product.entity.db.HoOrderCancelFormStatusRecord;
import com.corpgovernment.hotel.product.mapper.HoOrderCancelFormStatusRecordMapper;
import tk.mybatis.mapper.entity.Example;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-01-05-18:31
 */
@Component
public class HoOrderCancelFormStatusRecordLoader {

	private static final String CANCEL_FORM_ID = "cancelFormId";
	private static final String IS_DELETED = "isDeleted";

	@Autowired
	private HoOrderCancelFormStatusRecordMapper hoOrderCancelFormStatusRecordMapper;

	public int insertSelective(HoOrderCancelFormStatusRecord record) {
		return hoOrderCancelFormStatusRecordMapper.insertSelective(record);
	}

	public List<HoOrderCancelFormStatusRecord> listByCancelFormId(Long cancelFormId) {
		Example example = new Example(HoOrderCancelFormStatusRecord.class);
		example.createCriteria().andEqualTo(CANCEL_FORM_ID, cancelFormId).andEqualTo(IS_DELETED, 0);
		return hoOrderCancelFormStatusRecordMapper.selectByExample(example);
	}

	public int deleteByCancelFormId(Long cancelFormId) {
		List<HoOrderCancelFormStatusRecord> hoOrderCancelFormStatusRecordList = listByCancelFormId(cancelFormId);
		hoOrderCancelFormStatusRecordList.forEach(record -> {
			record.setIsDeleted(1);
			hoOrderCancelFormStatusRecordMapper.updateByPrimaryKeySelective(record);
		});
		return hoOrderCancelFormStatusRecordList.size();
	}

}
