package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.api.organization.bo.LandmarkBO;
import com.corpgovernment.api.organization.bo.SearchLandmarkRequestBO;
import com.corpgovernment.api.organization.bo.SearchLandmarkResponseBO;
import com.corpgovernment.api.organization.model.org.OrgInfoVo;
import com.corpgovernment.api.organization.model.org.request.GetOrgConfigRequest;
import com.corpgovernment.api.organization.model.org.response.GetOrgConfigResponse;
import com.corpgovernment.api.organization.model.switchinfo.GetSwitchListRequest;
import com.corpgovernment.api.organization.model.switchinfo.GetSwitchListResponse;
import com.corpgovernment.api.organization.model.switchinfo.SwitchInfoVo;
import com.corpgovernment.api.organization.soa.ILandmarkClient;
import com.corpgovernment.api.organization.soa.IOrganizationClient;
import com.corpgovernment.api.organization.soa.resident.GetAllDifferentialPriceRequest;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrganizationClientLoader {

	@Autowired
	private IOrganizationClient organizationClient;
	@Autowired
	private ILandmarkClient landmarkClient;


	/**
	 * 根据组织id查询组织信息
	 *
	 * @param orgId
	 * @return
	 */
	public OrgInfoVo findOrgInfoByOrgId(String orgId) {
		if (StringUtils.isBlank(orgId)) {
			return null;
		}
		JSONResult<OrgInfoVo> result = organizationClient.findOrgInfoByOrgId(orgId);
		if (result == null || result.getData() == null) {
			log.error("查询组织信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 批量查询组织信息
	 *
	 * @param orgIds
	 * @return
	 */
	public List<OrgInfoVo> findOrgInfoByOrgIds(List<String> orgIds) {
		if (CollectionUtils.isEmpty(orgIds)) {
			return new ArrayList<>();
		}
		JSONResult<List<OrgInfoVo>> result = organizationClient.findOrgInfoByOrgIds(orgIds);
		if (result == null || result.getData() == null) {
			log.error("批量查询组织信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData();
	}

	/**
	 * 查询组织地标
	 *
	 * @param cityId
	 * @param corpId
	 * @return
	 */
	public List<LandmarkBO> searchOrgLandmark(String cityId, String corpId) {
		if (StringUtils.isBlank(cityId) || StringUtils.isBlank(corpId)) {
			return new ArrayList<>();
		}
		SearchLandmarkRequestBO request = new SearchLandmarkRequestBO();
		request.setOrgId(corpId);
		request.setCityCode(cityId);
		JSONResult<SearchLandmarkResponseBO> result = landmarkClient.search(request);
		if (result == null || result.getData() == null || CollectionUtils.isEmpty(result.getData().getLandmarkList())) {
			log.error("批量查询组织信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return new ArrayList<>();
		}
		return result.getData().getLandmarkList();
	}

	public HotelControlVo getAllDifferentialPrice (GetAllDifferentialPriceRequest request, BaseUserInfo userInfo) {
		request.setUserInfo(userInfo);
		JSONResult<HotelControlVo> result = landmarkClient.getAllDifferentialPrice(request);
		if (result == null || result.getData() == null) {
			log.error("批量查询组织信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 获取组织配置
	 * @param orgId
	 * @return
	 */
	public GetOrgConfigResponse orgConfigGet(String orgId){
		GetOrgConfigRequest getOrgConfigRequest = new GetOrgConfigRequest();
		getOrgConfigRequest.setOrgId(orgId);
		JSONResult<GetOrgConfigResponse> result = organizationClient.orgConfigGet(getOrgConfigRequest);
		if (result == null || result.getData() == null) {
			log.error("获取组织配置异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return null;
		}
		return result.getData();
	}

	/**
	 * 获取差旅属性
	 */
	public List<SwitchInfoVo> getTravelAttribute(String orgId) {
		if (StringUtils.isBlank(orgId)) {
			return null;
		}
		GetSwitchListRequest getSwitchListRequest = new GetSwitchListRequest();
		getSwitchListRequest.setOrgId(orgId);
		JSONResult<GetSwitchListResponse> result = organizationClient.getTravelAttributeSwitch(getSwitchListRequest);
		if (result == null || result.getData() == null) {
			log.error("获取差旅属性失败。result={}", result);
			return null;
		}
		return result.getData().getSwitchInfoList();
	}

	public List<OrgInfoVo> findOrgInfoOrHistoryByOrgIds(List<String> orgIds){
		if (CollectionUtils.isEmpty(orgIds)){
			return Collections.emptyList();
		}
		JSONResult<List<OrgInfoVo>> result = organizationClient.findOrgInfoOrHistoryByOrgIds(orgIds);
		if (result == null || BooleanUtils.isFalse(result.isSUCCESS()) || result.getData() == null) {
			log.error("findOrgInfoOrHistoryByOrgIds error: {}", JsonUtils.toJsonString(result));
			return Collections.emptyList();
		}
		return result.getData();
	}
}
