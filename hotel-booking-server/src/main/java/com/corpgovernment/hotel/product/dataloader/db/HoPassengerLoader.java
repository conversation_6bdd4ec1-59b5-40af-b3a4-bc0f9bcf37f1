package com.corpgovernment.hotel.product.dataloader.db;

import com.corpgovernment.common.utils.EncryptUtils;
import com.corpgovernment.hotel.product.entity.db.HoPassenger;
import com.corpgovernment.hotel.product.mapper.HoPassengerMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-01-06-18:55
 */
@Component
public class HoPassengerLoader {

	@Autowired
	private HoPassengerMapper hoPassengerMapper;

	public List<HoPassenger> selectByOrderId(Long orderId) {
		if (orderId == null) {
			return new ArrayList<>();
		}
		HoPassenger record = new HoPassenger();
		record.setOrderId(orderId);
		return hoPassengerMapper.select(record);
	}

	public int insertSelective(HoPassenger record) {
		return hoPassengerMapper.insertSelective(record);
	}

	public List<HoPassenger> selectByOrderIds(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return new ArrayList<>();
		}
		Example example = new Example(HoPassenger.class);
		example.createCriteria().andIn("orderId", orderIds);
		return hoPassengerMapper.selectByExample(example);
	}

	/**
	 * 查询所有出行人数据（含已删除）
	 * @return
	 */
	public List<HoPassenger> getALLPassengerContainDel(){
		return hoPassengerMapper.selectAll();
	}

	/**
	 * 查询所有traveledName为空的数据
	 *
	 * @param startOrderId 起始订单id（不包含）
	 * @return
	 */
	public List<HoPassenger> selectTravelNameIsNullCollection(Long startOrderId) {
		Example example = new Example(HoPassenger.class);
		Criteria criteria = example.createCriteria().andIsNull("travelerName");
		if (startOrderId != null) {
			criteria.andGreaterThan("orderId", startOrderId);
		}
		example.setOrderByClause("order_id asc limit 30");
		return hoPassengerMapper.selectByExample(example);
	}

	/**
	 * 修改
	 * @param hoPassenger
	 * @return
	 */
	public int update(HoPassenger hoPassenger){
		Example example = new Example(HoPassenger.class);
		Example.Criteria criteria = example.createCriteria();
		if (null != hoPassenger.getOrderId()){
			criteria.andEqualTo("orderId", hoPassenger.getOrderId());
		}
		if (StringUtils.isNotBlank(hoPassenger.getPassengerName())){
			criteria.andEqualTo("passengerName", EncryptUtils.sm4Encrypt(hoPassenger.getPassengerName()));
		}
		return hoPassengerMapper.updateByExampleSelective(hoPassenger, example);
	}


	public List<HoPassenger> listByPassengerIds(List<String> uIds, List<String> nonEmployeeIds) {
		if(CollectionUtils.isEmpty(uIds) && CollectionUtils.isEmpty(nonEmployeeIds)){
			return Collections.emptyList();
		}
		return hoPassengerMapper.listByPassengerIds(uIds, nonEmployeeIds);
	}
}
