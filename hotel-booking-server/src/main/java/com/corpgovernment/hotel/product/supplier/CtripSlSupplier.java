package com.corpgovernment.hotel.product.supplier;

import static com.corpgovernment.hotel.product.external.constant.SupplierConstant.CancelOrderInquiry.REQUEST_ADDITIONAL_MAP_KEY_ORDER_ID;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.corpgovernment.common.common.ValidGroup;
import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.corpgovernment.api.hotel.product.bo.HotelConfirmOrderResponseBO;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.bookorder.response.LocalBookOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelListRequestBo;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo;
import com.corpgovernment.api.ordercenter.enums.OperationTitleEnums;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.soa.constant.ProductTypeEnum;
import com.corpgovernment.api.supplier.soa.response.MbSupplierProductResponse;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basicdata.bo.HotelCityBo;
import com.corpgovernment.common.apollo.BaseConfig;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.bo.HttpRetryBo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.MonitorLevelEnums;
import com.corpgovernment.common.enums.MonitorTypeEnums;
import com.corpgovernment.common.supplier.HttpRetryInspect;
import com.corpgovernment.common.utils.Md5Util;
import com.corpgovernment.hotel.booking.cache.HotelInfoCacheManager;
import com.corpgovernment.hotel.booking.vo.hotelmodify.HotelModifyDetailSupplierRequest;
import com.corpgovernment.hotel.booking.vo.hotelmodify.HotelModifyDetailSupplierResponse;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BasicDataClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.external.client.SupplierSoaClient;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailResponse;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModificationDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModificationDetailResponse;
import com.corpgovernment.hotel.product.model.ctrip.bookorder.request.BookOrderRequest;
import com.corpgovernment.hotel.product.model.ctrip.bookorder.response.BookOrderResponse;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.request.OrderDetailRequest;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse;
import com.corpgovernment.hotel.product.model.ctrip.request.HotelDataRequest;
import com.corpgovernment.hotel.product.model.ctrip.response.OrderAuditResponse;
import com.corpgovernment.hotel.product.model.ctrip.response.ResponseStatus;
import com.corpgovernment.hotel.product.model.ctrip.response.page.HotelListResponse;
import com.corpgovernment.hotel.product.service.OrderMonitorService;
import com.corpgovernment.hotel.product.supplier.convert.OrderStandardDetailsConvert;
import com.corpgovernment.mapping.bo.HmHotelDetailImport;
import com.corpgovernment.mapping.bo.HpSupplier;
import com.corpgovernment.mapping.mapper.HmHotelDetailImportMapper;
import com.corpgovernment.mapping.mapper.HpSupplierMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: CtripSlSupplier
 * @description: CtripSlSupplier 携程供应商
 * @author: zdwang
 * @date: Created in 19:07 2019/8/25
 * @Version: 1.0
 **/
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Component
public class CtripSlSupplier extends AbstractSupplier {
    private static final String ORDER_RESOURCE_DIRECT = "direct";
    @Autowired
    private CommonService commonService;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    @Autowired
    private HmHotelDetailImportMapper hmHotelDetailImportMapper;
    @Autowired
    private HotelInfoCacheManager hotelInfoCacheManager;
    @Autowired
    private HpSupplierMapper hpSupplierMapper;
    @Autowired
    private BasicDataClientLoader basicDataClientLoader;

    @Autowired
    private SupplierSoaClient supplierSoaClient;

    @Autowired
    private OrderStandardDetailsConvert orderStandardDetailsConvert;

    @Autowired
    private HoOrderLoader hoOrderLoader;

    @Override
    public HotelOperatorTypeConfig getHotelOperatorTypeConfig() {
        return hotelOperatorTypeConfig;
    }

    @Override
    public CommonService getCommonService() {
        return commonService;
    }

    @Override
    public Boolean isCtrip() {
        return true;
    }

    @Override
    public String getFilePrefix() {
        return hotelApollo.getCtripHotelPicUrlPrefix();
    }

    @Autowired
    private OrderMonitorService orderMonitorService;

    /**
     * 列表页有前缀，无需添加，
     * 详情页无前缀，需要添加
     */
    @Override
    public String convertPicUrl(String url, Boolean needPrifix) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        StringBuilder sb = new StringBuilder(url);
        log.info("*******************酒店详情图片Url************:{}", url);
        if (url.contains("_R")) {
            sb.insert(url.indexOf("_R") + 2, "_480_480");
        } else {
            int index = url.toUpperCase().indexOf(".");
            sb.insert(index, "_R_480_480");
        }
        if (needPrifix) {
            sb.insert(0, getFilePrefix());
        }
        return sb.toString();
    }

    @Override
    public HmHotelDetailImport getLocalStaticInfo(String hotelId, String supplierCode) {
        HpSupplier supplier = new HpSupplier();
        supplier.setSupplierCode(supplierCode);
        supplier = hpSupplierMapper.selectOne(supplier);
        if (supplier == null) {
            return null;
        }

        HmHotelDetailImport query = new HmHotelDetailImport();
        query.setSupplierId(supplier.getSupplierId());
        query.setHotelNo(hotelId);
        return hmHotelDetailImportMapper.selectOne(query);
    }

    @Override
    public HotelModifyDetailSupplierResponse getSupplierModifyDetail(HotelModifyDetailSupplierRequest request) {
        SupplierProductBo supplierProduct = commonService.listSupplierProduct(
            request.getCorpId(), request.getSupplierCode(),
            "modifyOrderDetail", request.getCorpPayType());
        if (supplierProduct == null) {
            return null;
        }
        String supplierCode = supplierProduct.getSupplierCode();
        String url = supplierProduct.getProductUrl();
        String userKey = supplierProduct.getUserKey();
        HotelModifyDetailSupplierRequest supplierRequest = new HotelModifyDetailSupplierRequest();
        supplierRequest.setOrderId(request.getOrderId());
        supplierRequest.setApplyId(request.getApplyId());
        try {
            String responseResult = commonService.doPostJSON(supplierCode, "申请修改订单详情", url, userKey,
                JsonUtils.toJsonString(supplierRequest));
            if (StringUtils.isNotBlank(responseResult)) {
                HotelModifyDetailSupplierResponse response =
                    JsonUtils.parse(responseResult, HotelModifyDetailSupplierResponse.class);
                if (null == response || BooleanUtils.isFalse(response.getStatus().getSuccess())
                    || CollectionUtils.isEmpty(response.getApplyFormDetailList())) {
                    throw new CorpBusinessException(
                        HotelResponseCodeEnum.FAILED_OBTAIN_REQUEST_MODIFICATION_ORDER_DETAILS);
                }
                return response;
            }
        } catch (Exception e) {
            log.error("获取供应商修改单详情异常", e);
        }
        return null;
    }

    @Override
    public StandardOrderModificationDetailResponse getStandardSupplierModifyDetail(String applyFormId, HoOrder hoOrder,
                                                                                   Class<? extends ValidGroup>... groups) {

        try {
            StandardOrderModificationDetailRequest standardOrderModificationDetailRequest = new StandardOrderModificationDetailRequest();
            standardOrderModificationDetailRequest.setSupplierCode(hoOrder.getSupplierCode());
            standardOrderModificationDetailRequest.setCompanyCode(hoOrder.getCorpId());
            standardOrderModificationDetailRequest.setCorpPayType(hoOrder.getCorpPayType());

            standardOrderModificationDetailRequest.setOrderID(hoOrder.getSupplierOrderId());
            standardOrderModificationDetailRequest.setApplyFormID(applyFormId);

            return supplierSoaClient.orderModificationDetails(standardOrderModificationDetailRequest, groups);
        } catch (Exception e) {
            log.error("获取供应商修改单详情异常", e);
        }
        return null;
    }

    /**
     * 酒店列表
     */
    @Override
    public LocalHotelListResponseBo page(LocalHotelListRequestBo requestBo, SupplierProductBo supplierProductBo,
        HotelCityBo hotelCity) {
        try {
            String productUrl = supplierProductBo.getProductUrl();
            HotelDataRequest request = convertHotelDataRequest(requestBo, supplierProductBo, hotelCity);
            log.info("请求远程携程酒店入参:{}", JsonUtils.toJsonString(request));
            HotelListResponse result =
                commonService.doPostJSON(supplierProductBo.getSupplierCode(), "酒店列表查询", productUrl,
                    "", JsonUtils.toJsonString(request), HotelListResponse.class);
            if (result == null || result.getStatus() == null || !result.getStatus().isSuccess()
                || result.getHotelDataList() == null) {
                log.warn("获取供应商携程酒店列表数据失败:" + JsonUtils.toJsonString(result));
                return null;
            }
            return convertLocalHotelListResponse(result.getHotelDataList(), result.getNumEntity(), requestBo, request,
                supplierProductBo);
        } catch (Exception e) {
            log.warn("获取供应商携程酒店列表数据失败", e);
        }
        return null;
    }

    @Override
    public LocalCheckAvailResponseBo checkAvail(LocalCheckAvailRequestBo request) {
        return null;
    }

    @Override
    public LocalBookOrderResponseBo bookOrder(LocalBookOrderRequestBo request) throws IOException {

        StringBuilder logContent = new StringBuilder();
        logContent.append("供应商下单：");
        try {
            BookOrderRequest bookOrderRequest = toBookOrderRequest(request);
            SupplierProductBo supplierProduct =
                commonService.listSupplierProduct(request.getCorpId(), request.getSupplierCode(),
                    hotelOperatorTypeConfig.getBookOrder(), request.getCorpPayType());
            if (supplierProduct == null) {
                throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUPPLIER_INFORMATION);
            }
            String productUrl = supplierProduct.getProductUrl();
            String json = commonService.doPostJSON(request.getSupplierCode(), OrderMonitorService.BOOK_ORDER,
                productUrl, "", JsonUtils.toJsonString(bookOrderRequest));
            BookOrderResponse bookOrderResponse = JsonUtils.parse(json, BookOrderResponse.class);
            if (bookOrderResponse == null || bookOrderResponse.getStatus() == null) {
                logContent.append("供应商返回信息为空(-1)");
            } else {
                String msg = bookOrderResponse.getOrderId() == null ? "" : ",供应商订单号：" + bookOrderResponse.getOrderId();
                logContent.append(String.format("%s(%s)%s", bookOrderResponse.getStatus().getErrorMessage(),
                    bookOrderResponse.getStatus().getErrorCode(), msg));
            }
            if (bookOrderResponse == null || bookOrderResponse.getStatus() == null
                || !Boolean.TRUE.equals(bookOrderResponse.getStatus().getSuccess())) {
                orderMonitorService.saveOrderMonitor(request.getExtInfo().getPlatformOrderId(),
                    MonitorTypeEnums.SF,
                    MonitorLevelEnums.IMP, JsonUtils.toJsonString(bookOrderResponse),
                    Optional.ofNullable(bookOrderResponse).map(BookOrderResponse::getStatus)
                        .map(BookOrderResponse.ResponseStatus::getErrorCode).orElse(null),
                    Optional.ofNullable(bookOrderResponse).map(BookOrderResponse::getStatus)
                        .map(BookOrderResponse.ResponseStatus::getErrorMessage).orElse(null),
                    OrderMonitorService.BOOK_ORDER);
            }
            return BookOrderResponse.toLocalBookOrderResponse(bookOrderResponse);
        } catch (Exception e) {
            log.warn("供应商下单失败：", e);
            logContent.append(String.format("%s(%s)",
                StringUtils.isBlank(e.getMessage()) ? "空指针异常" : e.getMessage(), "-1"));
            orderMonitorService.saveImpOrderMonitor(request.getExtInfo().getPlatformOrderId(), MonitorTypeEnums.SF,
                OrderMonitorService.BOOK_ORDER);
            throw e;
        } finally {
            commonService.addLog(request.getUid(), request.getExtInfo().getPlatformOrderId(),
                OperationTitleEnums.SUBMIT_ORDER_SUPPLIER, logContent.toString());
        }
    }

    @Override
    public OrderDetailResponse supplierOrderDetail(OrderDetailRequest request) {
        MbSupplierProductResponse supplierProduct = commonService.getSupplierInfo(request.getSupplierCode(),
            ProductTypeEnum.hotel.name(), hotelOperatorTypeConfig.getSearchOrder());
        String productUrl = supplierProduct.getProductUrl();

        String result = HttpRetryInspect.retry(
            new HttpRetryBo(JsonUtils.toJsonString(request), request.getSupplierCorpId(), request.getSupplierCode()),
            data -> {
                try {
                    if (Boolean.FALSE.equals(BaseConfig.defaultConfig().enabledSupplierClient())) {
                        return commonService.doPostJSON(request.getSupplierCode(), "供应商订单详情", productUrl, "", data);
                    }
                    OrderDetailResponse orderDetailResponse = commonService.invokeSupplierInterface(
                        request.getSupplierCode(), "供应商订单详情", productUrl, "", data, OrderDetailResponse.class);
                    return JsonUtils.toJsonString(orderDetailResponse);
                } catch (CorpBusinessException e) {
                    log.error("供应商订单详情查询异常", e);
                    OrderDetailResponse orderDetailResponse = new OrderDetailResponse();
                    orderDetailResponse.setErrorCode(String.valueOf(e.getResultCode()));
                    orderDetailResponse.setMessage(e.getMsg());
                    return JsonUtils.toJsonString(orderDetailResponse);
                } catch (Exception ex) {
                    log.error("订单详情查询异常", ex);
                }
                return null;
            });
        return JsonUtils.parse(result, OrderDetailResponse.class);
    }

    @Override
    public OrderDetailResponse.HotelOrderInfo
        supplierOrderDetail(StandardOrderDetailRequest request,Class<? extends ValidGroup>... groups) {

        HoOrder hoOrder = hoOrderLoader.selectBySupplierOrderId(request.getOrderID());
        if (Objects.isNull(hoOrder)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_ORDER_FAILED_BY_SUPPLIER_ORDER_ID);
        }
        // 统一设置公司id
        request.setCompanyCode(hoOrder.getCorpId());

        //直连场景需要设置平台订单号
        if (ORDER_RESOURCE_DIRECT.equals(hoOrder.getOrderResource())) {
            Map<String, Object> additionalInfoMap = new HashMap<>();
            additionalInfoMap.put(SupplierConstant.OrderDetail.REQUEST_ADDITIONAL_MAP_KEY_PLATFORM_ORDER_ID, hoOrder.getOrderId());
            request.setAdditionalInfoMap(additionalInfoMap);
        }

        // 调用标准契约接口
        StandardOrderDetailResponse standardOrderDetailResponse =
            supplierSoaClient.orderDetail(request, groups);
        OrderDetailResponse.HotelOrderInfo hotelOrderInfo =
            orderStandardDetailsConvert.convertOrderDetailResponse(standardOrderDetailResponse);
        hotelOrderInfo.setOrderId(request.getOrderID());
        return hotelOrderInfo;
    }

    private BookOrderRequest toBookOrderRequest(LocalBookOrderRequestBo request) {
        BookOrderRequest bookOrderRequest = new BookOrderRequest();
        bookOrderRequest.setUid(request.getSupplierUid());
        bookOrderRequest.setCheckCode(request.getCheckCode());
        bookOrderRequest.setCorpId(request.getSupplierCorpId());
        {
            LocalBookOrderRequestBo.CreateBaseInfo baseInfo = request.getBaseInfo();
            BookOrderRequest.CreateBaseInfo createBaseInfo = new BookOrderRequest.CreateBaseInfo();
            BeanUtils.copyProperties(baseInfo, createBaseInfo);
            bookOrderRequest.setBaseInfo(createBaseInfo);
        }
        {
            List<LocalBookOrderRequestBo.ClientInfo> clientList = request.getClientList();
            List<BookOrderRequest.ClientInfo> clientInfoList = clientList.stream().map(e -> {
                BookOrderRequest.ClientInfo clientInfo = new BookOrderRequest.ClientInfo();
                BeanUtils.copyProperties(e, clientInfo);
                return clientInfo;
            }).collect(Collectors.toList());
            bookOrderRequest.setClientList(clientInfoList);
        }
        {
            LocalBookOrderRequestBo.ContactInfo contactInfo = request.getContactInfo();
            BookOrderRequest.ContactInfo contact = new BookOrderRequest.ContactInfo();
            BeanUtils.copyProperties(contactInfo, contact);
            bookOrderRequest.setContactInfo(contact);
            LocalBookOrderRequestBo.CorpOrderInfo corpOrderInfo = request.getCorpOrderInfo();
            BookOrderRequest.CorpOrderInfo corpOrder = new BookOrderRequest.CorpOrderInfo();
            BeanUtils.copyProperties(corpOrderInfo, corpOrder);
            corpOrder.setPrepayType(request.getCorpOrderInfo().getPrepayType());
            bookOrderRequest.setCorpOrderInfo(corpOrder);
        }
        {
            LocalBookOrderRequestBo.PriceInfo priceInfo = request.getPriceInfo();
            BookOrderRequest.PriceInfo price = new BookOrderRequest.PriceInfo();
            BeanUtils.copyProperties(priceInfo, price);
            bookOrderRequest.setPriceInfo(price);
        }
        {
            LocalBookOrderRequestBo.CreateRoomInfo roomInfo = request.getRoomInfo();
            BookOrderRequest.CreateRoomInfo createRoomInfo = new BookOrderRequest.CreateRoomInfo();
            BeanUtils.copyProperties(roomInfo, createRoomInfo);
            bookOrderRequest.setRoomInfo(createRoomInfo);
        }
        {
            LocalBookOrderRequestBo.RemarkInfo remarkInfo = request.getRemarkInfo();
            if (remarkInfo != null) {
                BookOrderRequest.RemarkInfo remark = new BookOrderRequest.RemarkInfo();
                BeanUtils.copyProperties(remarkInfo, remark);
                bookOrderRequest.setRemarkInfo(remark);
            }
        }
        {
            BookOrderRequest.ExtInfo extInfo = new BookOrderRequest.ExtInfo();
            extInfo.setPlatformOrderId(request.getExtInfo().getPlatformOrderId());
            bookOrderRequest.setExtInfo(extInfo);
        }
        return bookOrderRequest;
    }

    @Override
    public HotelConfirmOrderResponseBO confirmOrder(HoOrder orderInfo, SupplierProductBo supplierProduct) {
        log.info("订单确认请求:" + new Date());
        String supplierCode = supplierProduct.getSupplierCode();
        String url = supplierProduct.getProductUrl();
        Map requestParam = new HashMap<>();
        String ticket = basicDataClientLoader.ctripTokenByCorpID(orderInfo.getSupplierCorpId(), supplierCode);
        OrderDetailRequest.Auth auth = new OrderDetailRequest.Auth();
        auth.setAppKey("obk_" + orderInfo.getSupplierCorpId());
        auth.setTicket(ticket);
        requestParam.put("auth", auth);
        String orderId = orderInfo.getSupplierOrderId();
        // 酒店产线
        String orderType = "2";
        // 同意授权
        String auditType = "T";
        String appSecurity =
            Md5Util.md5Hex(basicDataClientLoader.getAppSecurity(orderInfo.getSupplierCorpId(), supplierCode));
        String signature = Md5Util.md5Hex(orderId + orderType + auditType + appSecurity);
        requestParam.put("signature", signature);
        requestParam.put("orderID", orderId);
        requestParam.put("orderType", orderType);
        requestParam.put("auditType", auditType);

        String httpResult = HttpRetryInspect.retry(
            new HttpRetryBo(JsonUtils.toJsonString(requestParam), orderInfo.getSupplierCorpId(), supplierCode),
            data -> {
                try {
                    return commonService.doPostJSON(supplierCode, "订单确认", url, "", data);
                } catch (Exception ex) {
                    log.error("酒店订单确认异常", ex);
                }
                return null;
            });
        OrderAuditResponse response = JsonUtils.parse(httpResult, OrderAuditResponse.class);
        if (response == null || response.getStatus() == null) {
            return HotelConfirmOrderResponseBO.failure("供应商接口异常");
        }
        ResponseStatus responseStatus = response.getStatus();
        return HotelConfirmOrderResponseBO.create(responseStatus.getErrorCode(), responseStatus.getMessage());
    }
}