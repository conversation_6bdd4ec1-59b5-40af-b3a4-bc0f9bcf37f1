package com.corpgovernment.hotel.product.entity.db;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @author: lilayzzz
 * @since: 2024/3/27
 * @description:
 */
@Data
@Table(name = "ho_hotel_member")
public class HoHotelMember implements Serializable {

    /**
     * 订单号
     **/
    @Column(name = "order_id")
    private Long orderId;
    /**
     * 集团ID
     */
    @Column(name = "group_id")
    private String groupId;
    /**
     * 集团名称
     */
    @Column(name = "group_name")
    private String groupName;
    /**
     * 是否积分
     */
    @Column(name = "enabled_bonus_point")
    private Boolean enabledBonusPoint;
    /**
     * 持卡人姓名
     */
    @Column(name = "member_cardholder")
    private String memberCardholder;
    /**
     * 会员卡号
     */
    @Column(name = "member_card_no")
    private String memberCardNo;
    /**
     * 会员规则描述
     */
    @Column(name = "member_rule_desc")
    private String memberRuleDesc;
    /**
     * 积分类型
     */
    @Column(name = "bonus_point_type")
    private String bonusPointType;
    /**
     * 积分规则编码
     */
    @Column(name = "bonus_point_code")
    private String bonusPointCode;
}
