package com.corpgovernment.hotel.product.dataloader.soa;

import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.dto.snapshot.SnapshotQtyCmd;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11
 */
@Component
@Slf4j
public class BookingCoreClientLoader {

    @Autowired
    private CoreServiceClient coreServiceClient;

    public QuerySnapshotResponseDTO getSnapshot(String token, List<String> dataTypeList) {
        if (StringUtils.isBlank(token) || CollectionUtils.isEmpty(dataTypeList)) {
            return null;
        }
        SnapshotQtyCmd snapshotQtyCmd = new SnapshotQtyCmd();
        snapshotQtyCmd.setDataTypeList(dataTypeList);
        snapshotQtyCmd.setToken(token);
        JSONResult<QuerySnapshotResponseDTO> result = coreServiceClient.getSnapshot(snapshotQtyCmd);
        if (result == null || result.getData() == null) {
            log.error("获取快照失败");
            return null;
        }
        return result.getData();
    }

}
