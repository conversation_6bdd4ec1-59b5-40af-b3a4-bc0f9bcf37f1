package com.corpgovernment.hotel.product.dataloader.soa;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.corpgovernment.api.ordercenter.soa.IOrderUserClient;
import com.corpgovernment.api.ordercenter.vo.OrderUserInfoVo;
import com.corpgovernment.common.base.JSONResult;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: pwang27
 * @Date: 2023/10/9 16:03
 * @Description:
 */
@Component
@Slf4j
public class OrderUserClientLoader {

    @Autowired
    private IOrderUserClient orderUserClient;

    /**
     * 查询订单用户信息
     *
     * @param orderId
     * @return
     */
    public void save(Long orderId, String platUid, String supplierUid, String userName) {
        OrderUserInfoVo userInfoVo = OrderUserInfoVo.builder()
            .orderId(orderId)
            .platUid(platUid)
            .supplierUid(supplierUid)
            .userName(userName)
            .build();
        JSONResult<Boolean> result = orderUserClient.save(userInfoVo);
        if (result == null || result.getData()) {
            log.error("保存订单用户关联信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
        }
    }
}
