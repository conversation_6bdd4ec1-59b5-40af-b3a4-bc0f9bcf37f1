package com.corpgovernment.hotel.product.external.dto.order.modify;

import java.util.List;

import lombok.Data;

/**
 * 携程订单修改详情
 *
 * <AUTHOR> ding<PERSON>an
 * @date : 2024/4/11 15:18
 * @since : 1.0
 */
@Data
public class CtripOrderModificationDetailResponse {

    private ResponseStatus status;
    private List<ApplyFormDetail> applyFormDetailList;

    @Data
    public static class ResponseStatus {
        private Boolean success;
        private String errorMessage;
        private String errorCode;
    }

    @Data
    public static class ApplyFormDetail {
        /**
         * 申请单ID
         */
        private String applyId;
        /**
         * 订单号
         */
        private String orderId;
        /**
         * 申请时间 （格式yyyy-MM-dd HH:mm:ss ）
         */
        private String applyTime;
        /**
         * 申请时间UTC （格式yyyy-MM-dd'T'HH:mm:ssXXX ）
         */
        private String applyTimeUTC;
        /**
         * 状态：【1：已提交 2：待处理 3：与酒店协调中 4：修改成功 5：修改失败 6：修改取消,7 修改取消中】
         */
        private Integer status;
        /**
         * 处理状态明细
         */
        private List<StatusItem> statusItemList;

        /**
         * 修改内容-基本信息
         */
        private List<ModifyContent> modifyContentList;

        /**
         * 修改内容-间夜信息
         */
        private ModifyRoomNight modifyRoomNight;
        /**
         * 修改原因
         */
        private String reasonCode;
        /**
         * 原因说明
         */
        private String reasonDesc;
    }

    @Data
    public static class StatusItem {
        /**
         * 状态：【1：已提交 2：待处理 3：与酒店协调中 4：修改成功 5：修改失败 6：修改取消,7 修改取消中】
         */
        private Integer status;
        /**
         * 处理时间（格式yyyy-MM-dd HH:mm:ss ）
         */
        private String statusTime;
        /**
         * 处理时间（格式yyyy-MM-dd HH:mm:ss ）
         */
        private String statusTimeUTC;
        /**
         * 根据业务流转对状态排序（数字越大的状态，在业务时间线越靠后，如 提交《处理《修改成功； 修改成功 = 修改失败）
         */
        private Integer statusSortIndex;
    }

    @Data
    public static class ModifyContent {
        /**
         * 修改单场景【4：推迟入住 5：中段取消 6：提前离店 9：减少间数】
         */
        private Integer scene;

        /**
         * 修改内容项
         */
        private List<Content> contentList;
    }

    @Data
    public static class Content {
        /**
         * 修改内容类型【checkInTime-入住时间（yyyy-MM-dd HH:mm:ss）、checkOutTime-离店时间（yyyy-MM-dd HH:mm:ss）】
         */
        private String type;
        private String originalValue;
        private String currentValue;

    }

    @Data
    public static class ModifyRoomNight {
        /**
         * 原始间夜信息
         */
        private OriginalRoomNight originalRoomNight;
        /**
         * 当前的/减少的 间夜信息
         */
        private CurrentRoomNight currentRoomNight;

    }

    @Data
    public static class OriginalRoomNight {
        /**
         * 剩余总间夜数
         */
        private Integer restTotalRoomNight;
        /**
         * 间夜信息
         */
        private List<OriginalRoomNightDetailInfo> originalRoomNightDetailInfoList;

    }

    @Data
    public static class OriginalRoomNightDetailInfo {
        /**
         * 房间日期（格式yyyy-MM-dd ）
         */
        private String roomDate;
        /**
         * 房间数量
         */
        private Integer quantity;
        /**
         * 房间入住人信息
         */
        private List<ClientInfo> clientInfoList;

    }

    @Data
    public static class ClientInfo {
        /**
         * 入住人ID
         */
        private String clientInfoId;
        /**
         * 房间索引
         */
        private Integer roomIndex;
        /**
         * 入住人姓名
         */
        private String name;

    }

    @Data
    public static class CurrentRoomNight {
        /**
         * 剩余总间夜数
         */
        private Integer restTotalRoomNight;
        /**
         * 间夜信息
         */
        private List<CurrentRoomNightDetailInfo> currentRoomNightDetailInfoList;

    }

    @Data
    public static class CurrentRoomNightDetailInfo {
        /**
         * 房间日期（格式yyyy-MM-dd ）
         */
        private String roomDate;
        /**
         * 房间数量
         */
        private Integer quantity;
        /**
         * 修改间夜场景类型：5 中段取消 9 减少间数
         */
        private Integer scenarioType;

        /**
         * 房间入住人信息
         */
        private List<ClientInfo> clientInfoList;
    }
}
