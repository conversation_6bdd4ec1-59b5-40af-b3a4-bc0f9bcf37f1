package com.corpgovernment.hotel.product.mq;

import com.corpgovernment.api.platform.soa.handlerPaymentBill.PushPayResultModel;
import com.corpgovernment.common.mq.consumer.AbstractMultiTenantRocketMQConsumer;
import com.corpgovernment.common.mq.enums.MessageBizTypeEnum;
import com.corpgovernment.common.utils.LogSplicingUtils;
import com.corpgovernment.hotel.product.service.PaymentResultProcessService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 机票支付推送consumer
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PayTenantSuccessMsgConsumer extends AbstractMultiTenantRocketMQConsumer {

	@Resource
	private PaymentResultProcessService paymentResultProcessService;

	@Override
	protected void consume(MessageExt messageExt, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
		StringBuilder logContext = new StringBuilder();
		try {
			String msg = new String(messageExt.getBody(), StandardCharsets.UTF_8);
			LogSplicingUtils.addLogContext(logContext, "接受到的消息为：[%s]，MsgId：[%s]", msg, messageExt.getMsgId());
			PushPayResultModel pushPayResultModel = JsonUtils.parse(msg, PushPayResultModel.class);
			paymentResultProcessService.processPaymentResult(pushPayResultModel, logContext);
		} catch (Exception e) {
			log.error("消费异常：" + e.getMessage(), e);
			throw e;
		} finally {
			log.info("{}", logContext);
		}
	}

	@Override
	protected MessageBizTypeEnum getMessageBizType() {
		return MessageBizTypeEnum.HOTEL_ORDER_PUSH;
	}

}