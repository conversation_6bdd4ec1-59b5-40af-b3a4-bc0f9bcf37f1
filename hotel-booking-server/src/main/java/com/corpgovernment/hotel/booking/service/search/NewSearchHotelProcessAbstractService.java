package com.corpgovernment.hotel.booking.service.search;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.corpgovernment.api.applytrip.apply.sync.StandardAmountSyncRequest;
import com.corpgovernment.api.applytrip.soa.response.QueryApplyTripStandardResponse;
import com.corpgovernment.api.basic.bo.StaticMapListRequestBo;
import com.corpgovernment.api.basic.bo.StaticMapRequestBo;
import com.corpgovernment.api.basic.enums.HotelRequestTypeEnum;
import com.corpgovernment.api.basic.response.StaticMapResponse;
import com.corpgovernment.api.hotel.booking.hotel.request.Room;
import com.corpgovernment.api.hotel.booking.hotel.request.SearchHotelListRequestVO;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelListRequestBo;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo.HotelListBean;
import com.corpgovernment.api.hotel.product.model.response.OffLineHotel;
import com.corpgovernment.api.hotel.product.model.response.SearchHotelListResponseVO;
import com.corpgovernment.api.hotel.product.vo.HotelSearchRequestVo;
import com.corpgovernment.api.organization.model.switchinfo.SwitchInfoVo;
import com.corpgovernment.api.organization.soa.resident.GetAllDifferentialPriceRequest;
import com.corpgovernment.api.supplier.bo.suppliercompany.ListSupplierProductRequestBo;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.api.supplier.soa.constant.PubOwnEnum;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.api.travelstandard.vo.AveragePriceSet;
import com.corpgovernment.api.travelstandard.vo.AverageStarSet;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.api.travelstandard.vo.request.GetHotelDetailRequest;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.impl.HotelBasicDataService;
import com.corpgovernment.basic.impl.HotelIndexService;
import com.corpgovernment.basicdata.bo.HotelCityBo;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseRequestVO;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.utils.*;
import com.corpgovernment.hotel.booking.bo.SearchHotelListBo;
import com.corpgovernment.hotel.booking.cache.HotelListPageCacheInfo;
import com.corpgovernment.hotel.booking.cache.SupplierHotelModel;
import com.corpgovernment.hotel.booking.convert.SearchHotelListConvert;
import com.corpgovernment.hotel.booking.enums.SearchHotelSortEnum;
import com.corpgovernment.hotel.booking.enums.TravelUnLimitedTypeEnum;
import com.corpgovernment.hotel.booking.service.HotelManager;
import com.corpgovernment.hotel.booking.service.TokenAccessService;
import com.corpgovernment.hotel.booking.service.TravelStandardService;
import com.corpgovernment.hotel.product.cache.HotelListCacheManager;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.OrganizationClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierCompanyClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.TravelStandardPostClientLoader;
import com.corpgovernment.mapping.mapper.HpResultHotelMapper;
import com.corpgovernment.mapping.service.StaticMapResultService;
import com.ctrip.corp.obt.generic.core.context.RequestContext;
import com.ctrip.corp.obt.generic.exception.CommonException;
import com.ctrip.corp.obt.generic.utils.Conditional;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.GET_TRAVEL_ATTRIBUTE_ERROR;


@Service
@Slf4j
public abstract class NewSearchHotelProcessAbstractService extends BaseService {

    @Autowired
    private TravelStandardPostClientLoader travelStandardPostClientLoader;
    @Autowired
    private HpResultHotelMapper hpResultHotelMapper;
    @Autowired
    private HotelListCacheManager hotelListCacheManager;
    @Autowired
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    @Autowired
    private HotelBasicDataService hotelBasicDataService;
    @Autowired
    private StaticMapResultService mapResultService;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private SupplierCompanyClientLoader supplierCompanyClientLoader;
    @Autowired
    private SearchHotelListConvert searchHotelListConvert;
    @Autowired
    private HotelManager hotelManager;
    @Autowired
    private OrganizationClientLoader organizationClientLoader;
    @Autowired
    private HotelIndexService hotelIndexService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    @Qualifier(value = "queryThreadPoolExecutor")
    private ThreadPoolExecutor queryThreadPoolExecutor;

    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;

    @Autowired
    private TravelStandardService travelStandardService;
    @Autowired
    private TokenAccessService tokenAccessService;

    Id queryListMetricId = Metrics.REGISTRY.createId("hotel.booking.queryList");


    /**
     * 替换符
     */
    private final static String LOCATION_DESC = "{0}&{1}&{2}&{3}";

    /**
     * 品牌
     */
    private final static String SEARCH_FILTER_BRAND = "HOTEL_BRAND";

    /**
     * 地铁
     */
    private final static String SEARCH_FILTER_METRO_STATION = "METRO_STATION";

    /**
     * 商业区
     */
    private final static String SEARCH_FILTER_ZONE = "ZONE";

    /**
     * 星级排序
     */
    private final static String SEARCH_STAR_SORT = "STAR_SORT";

    /**
     * 距离排序
     */
    private final static String SEARCH_DISTANCE_SORT = "DISTANCE_SORT";

    /**
     * 价格排序
     */
    private final static String SEARCH_PRICE_SORT = "PRICE_SORT";


    public SearchHotelListResponseVO process(SearchHotelListBo searchHotelListBo) {
        try {
            initElkLog();
            addElkInfoLog("进入酒店列表查询");

            // 检查停留时间
            hotelManager.checkStateTime(true);
            //校验参数正确性
            this.checkParams(searchHotelListBo);

            SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();
            // 因公出行-差旅属性设置
            if (CorpPayTypeEnum.PUB.getType().equals(pageRequest.getCorpPayType())) {
                // 获取差旅属性
                Map<String, SwitchInfoVo> travelAttributeMap = getTravelAttribute(Optional.ofNullable(pageRequest.getUserInfo()).map(BaseRequestVO.UserInfo::getCorpId).orElse(""));
                // 星级屏蔽控制
                List<String> starList = processHotelStarShieldControl(travelAttributeMap, pageRequest);
                // 配置了星级屏蔽，且最终用户可选择的星级为空
                if (starList != null && starList.isEmpty()) {
                    log.info("差旅属性，星级屏蔽导致列表无结果。请求={}，差旅属性={}", searchHotelListBo, travelAttributeMap);
                    return SearchHotelListResponseVO.empty(searchHotelListBo.getPageRequest().getPageNum(), searchHotelListBo.getPageRequest().getPageSize());
                }
            }

            // 查询差标->命中差标->将差标拆解放入request
            if (configHotelControl(searchHotelListBo.getPageRequest())) {
                PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.DO_NOT_MEET_TRAVEL_CRITERIA.code());
                return SearchHotelListResponseVO.empty(searchHotelListBo.getPageRequest().getPageNum(), searchHotelListBo.getPageRequest().getPageSize());
            }

            //参数补充
            this.addParams(searchHotelListBo);

            //获取供应商基础信息->供应商对应查询地址->供应商对应得城市编码->获取酒店缓存
            this.querySupplierConfig(searchHotelListBo);

            //查询供应商酒店数据
            this.querySupplierHotel(searchHotelListBo);

            //酒店Mapping
            this.hotelMapping(searchHotelListBo);

            List<HotelListBean> supplierHotelList = Optional.ofNullable(searchHotelListBo.getCurrentPageCacheInfo()).map(HotelListPageCacheInfo::getUnShowedHotelListInfo).orElse(new ArrayList<>(0));
            RequestContext.getCurrentContext().addContextParams("supplierHotelList", supplierHotelList);
            log.info("全量酒店列表={}", JsonUtils.toJsonString(supplierHotelList));

            // 后过滤
            postFilter(searchHotelListBo);

            //排序
            List<HotelListBean> sortedHotelList = this.sortHotelList(searchHotelListBo);

            //分页
            SearchHotelListResponseVO responseVO = cutListPage(searchHotelListBo, sortedHotelList);

            // 设置缓存
            hotelListCacheManager.setHotelListCacheInfo(searchHotelListBo.getRedisKey(), searchHotelListBo.getPageRequest().getPageNum(), searchHotelListBo.getCurrentPageCacheInfo());

            setMetric(responseVO);
            return responseVO;
        } finally {
            log.info("HoHotelService.page酒店列表查询{} request：{}{}{}{}", System.lineSeparator(), JsonUtils.toJsonString(searchHotelListBo.getPageRequest()), System.lineSeparator(), System.lineSeparator(), this.getElkInfoLog());
            clearElkLog();

        }
    }

    protected abstract String type();
    /**
     * 查询供应商酒店
     */
    protected abstract void querySupplierHotel(SearchHotelListBo searchHotelListBo);

    /**
     * 酒店Mapping
     */
    protected abstract void hotelMapping(SearchHotelListBo searchHotelListBo);

    /**
     * 排序
     */
    protected abstract List<HotelListBean> sortHotelList(SearchHotelListBo searchHotelListBo);

    /**
     * 分页
     */
    protected abstract SearchHotelListResponseVO cutListPage(SearchHotelListBo searchHotelListBo, List<HotelListBean> sortList);

    /**
     * 后过滤
     */
    protected abstract void postFilter(SearchHotelListBo searchHotelListBo);

    /**
     * 校验参数是否正确
     * @param searchHotelListBo
     */
    protected void checkParams(SearchHotelListBo searchHotelListBo){
        SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();
        if (StringUtils.isBlank(pageRequest.getSDate())){
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.CHECK_IN_TIME_IS_EMPTY);
        }
        if (StringUtils.isBlank(pageRequest.getEDate())){
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.CHECK_OUT_TIME_IS_EMPTY);
        }
        if (pageRequest.getSDate().equals(pageRequest.getEDate())){
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.CHECK_IN_TIME_IS_SAME_AS_CHECK_OUT_TIME);
        }
        if (pageRequest.getSDate().compareTo(pageRequest.getEDate()) >0){
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.CHECK_IN_TIME_CAN_NOT_BE_LONGER_THAN_CHECK_OUT_TIME);
        }
    }

    /**
     * 参数补充
     * @param searchHotelListBo
     */
    private void addParams(SearchHotelListBo searchHotelListBo){
        SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();
        //文本输入搜索不为空
        if (StringUtils.isNotBlank(pageRequest.getTitle())){
            //前端未命中分销组装key值
            if (StringUtils.isNotBlank(pageRequest.getKey())){
                addElkInfoLog("进行参数补充");
                HotelSearchRequestVo hotelSearchRequestVo = new HotelSearchRequestVo();
                hotelSearchRequestVo.setCityid(pageRequest.getCityCode());
                hotelSearchRequestVo.setKeyword(pageRequest.getTitle());
                List<Map<String, List<Object>>> locationList = hotelIndexService.searchLocation(hotelSearchRequestVo);
                if (CollectionUtils.isEmpty(locationList)){
                    return;
                }
                for (int i = 0; i< locationList.size(); i++){
                    Map<String, List<Object>> infoMap = locationList.get(i);
                    if (infoMap != null && infoMap.size() >0){
                        infoMap.forEach((key, infoList) ->{
                            if (CollectionUtils.isNotEmpty(infoList)){
                                //接口为模糊搜索，这里完全命中进行参数补充
                                if (Optional.ofNullable(infoList.get(0)).map(String::valueOf).orElse("")
                                        .equals(pageRequest.getTitle())){
                                    addElkInfoLog("补充参数为：%s", key);
                                    pageRequest.setKey(key);
                                    return;
                                }
                            }
                        });
                    }
                }
            }
        }
    }


    /**
     * 查询供应商基础配置
     */
    protected void querySupplierConfig(SearchHotelListBo searchHotelListBo){
        //获取缓存key
        SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();
        String redisKey = this.getHotelListRequestMd5(pageRequest);
        searchHotelListBo.setRedisKey(redisKey);
        //获取redis供应商酒店缓存
        HotelListPageCacheInfo hotelListPreCacheInfo = hotelListCacheManager.getHotelListCacheInfo(redisKey, pageRequest.getPageNum() - 1);
        // 分页缓存为空或者为第一页，则初始化供应商信息，并且从第一页开始查询
        if (hotelListPreCacheInfo == null || pageRequest.getPageNum() == 1) {
            pageRequest.setPageNum(1);
            hotelListPreCacheInfo = new HotelListPageCacheInfo();
            hotelListPreCacheInfo.setSupplierInfo(Optional.ofNullable(getSupplierConfig(searchHotelListBo.getPageRequest())).orElse(Collections.emptyMap()));
        }

        searchHotelListBo.setPrePageCacheInfo(hotelListPreCacheInfo);

        // 初始化当前页缓存信息
        HotelListPageCacheInfo hotelListCurrentCacheInfo = new HotelListPageCacheInfo();
        hotelListCurrentCacheInfo.setSupplierInfo(hotelListPreCacheInfo.getSupplierInfo());
        searchHotelListBo.setCurrentPageCacheInfo( hotelListCurrentCacheInfo);

        if (hotelListPreCacheInfo.getSupplierInfo() == null || hotelListPreCacheInfo.getSupplierInfo().size() == 0){
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.ENTERPRISE_SUBSCRIPTION_SERVICE_EXPIRES.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.ENTERPRISE_SUBSCRIPTION_SERVICE_EXPIRES);
        }
    }

    /**
     * 获取供应商基础配置
     * 1.请求得url
     * 2.城市信息
     * 3.其他信息如：品牌|行政区|地铁线
     * @return
     */
    public Map<String, SupplierHotelModel> getSupplierConfig(SearchHotelListRequestVO request){
        addElkInfoLog("request: %s", JsonUtils.toJsonString(request));
        List<SupplierProductBo> supplierProductList = getSupplierProductList(request);

        addElkInfoLog("supplierProductList: %s", JsonUtils.toJsonString(supplierProductList));

        if (CollectionUtils.isEmpty(supplierProductList)){
            return null;
        }

        Set<String> supplierCodeSet = new HashSet<>(supplierProductList.size());
        for (SupplierProductBo supplierProductBo : supplierProductList) {
            String supplierCode = supplierProductBo.getSupplierCode();
            if ("meiya".equals(supplierCode) || "ctrip".equals(supplierCode)) {
                supplierCodeSet.add(supplierCode);
            } else {
                supplierCodeSet.add("ctrip");
            }
        }
        List<String> supplierCodeList = new ArrayList<>(supplierCodeSet);

        addElkInfoLog("supplierCodeList: %s", JsonUtils.toJsonString(supplierCodeList));

        CompletableFuture<HotelCityBo> hotelCityFuture = CompletableFuture.supplyAsync(() ->
                hotelBasicDataService.getCityInfo(request.getCityCode()), queryThreadPoolExecutor);
        CompletableFuture<Map<String, String>> citySupplierFuture = CompletableFuture.supplyAsync(() ->
                this.getSupplierCityMap(request.getCityCode(), supplierCodeList), queryThreadPoolExecutor);
        CompletableFuture<Map<String, String>> analysisKeyFuture = CompletableFuture.supplyAsync(() ->
                this.analysisKey(request, supplierCodeList), queryThreadPoolExecutor);
        CompletableFuture<Map<String, List<String>>> analysisCategoryFuture = CompletableFuture.supplyAsync(() ->
                this.analysisCategory(request.getCityCode(), supplierCodeList, request.getCatagory()), queryThreadPoolExecutor);

        try{

            HotelCityBo hotelCity = hotelCityFuture.get();
            addElkInfoLog("hotelCity: %s", JsonUtils.toJsonString(hotelCity));

            //城市为空说明城市数据有问题
            if (Objects.isNull(hotelCity)){
                throw new CorpBusinessException(HotelResponseCodeEnum.URBAN_BASIC_DATA_IS_MISSING);
            }

            Map<String, String> supplierCityMap = citySupplierFuture.get();
            addElkInfoLog("supplierCityMap: %s", JsonUtils.toJsonString(supplierCityMap));

            Map<String, String> analysisKey = analysisKeyFuture.get();
            addElkInfoLog("analysisKey: %s", JsonUtils.toJsonString(analysisKey));

            Map<String, List<String>> analysisCategory = analysisCategoryFuture.get();
            addElkInfoLog("analysisCategory: %s", JsonUtils.toJsonString(analysisCategory));

            String hotelSearchSupplierFilter = hotelApollo.getHotelSearchSupplierFilter();
            Map<String, List<String>> hotelSearchSupplierFilterMap = new HashedMap();
            if (StringUtils.isNotBlank(hotelSearchSupplierFilter)){
                hotelSearchSupplierFilterMap.putAll(JsonUtils.parse(hotelSearchSupplierFilter, new TypeReference<Map<String, List<String>>>() {}));
            }

            Map<String, SupplierHotelModel> supplierHotelMap = supplierProductList.stream().map(supplierProduct -> {
                SupplierHotelModel data = new SupplierHotelModel();
                String supplierCode = supplierProduct.getSupplierCode();
                // 基础数据mapping忽略
                if (!"meiya".equals(supplierProduct.getSupplierCode())){
                    log.info("切换supplierCode完成request构建");
                    supplierCode = "ctrip";
                }

                data.setSupplierCode(supplierProduct.getSupplierCode());
                data.setSupplierProduct(supplierProduct);
                data.setBasicCity(hotelCity);

                // 资源屏蔽
                processSupplierShield(data, supplierProduct.getSupplierCode(), hotelSearchSupplierFilterMap, request);

                // 获取供应商对应的城市id
                String supplierCity = supplierCityMap.get(supplierCode);

                // 解析请求中的key,适配不同供应商入参静态数据
                LocalHotelListRequestBo supplierRequest = searchHotelListConvert.convertToBo(request);

                if (StringUtils.isBlank(supplierCity)) {
                    addElkInfoLog(supplierProduct.getSupplierCode() + "未匹配到供应商城市：" + request.getCityCode());
                    data.setRequest(supplierRequest);
                    return data;
                }

                // 解析请求中的key,适配不同供应商入参静态数据
                supplierRequest.setSupplierCityId(supplierCity);

                if (StringUtils.isNotBlank(request.getKey()) && !analysisKey.containsKey(supplierCode)) {
                    addElkInfoLog(supplierProduct.getSupplierCode() + "静态数据map失败，不进行请求");
                    data.setRequest(supplierRequest);
                    return data;
                }

                supplierRequest.setKey(request.getKey());
                supplierRequest.setCatagory(analysisCategory.get(supplierCode));
                if (CollectionUtils.isEmpty(supplierRequest.getCatagory()) && CollectionUtils.isNotEmpty(request.getCatagory())) {
                    addElkInfoLog(supplierProduct.getSupplierCode() + "Category静态数据map失败，不进行请求");
                    data.setRequest(supplierRequest);
                    return data;
                }

                //获取运营后台被屏蔽的酒店
                List<OffLineHotel> offLineHotels = hpResultHotelMapper.selectOffLine(supplierProduct.getSupplierCode());
                List<String> offlines = offLineHotels.stream().map(OffLineHotel::getHotelId).distinct().collect(Collectors.toList());
                supplierRequest.setHiddenHotelList(offlines);
                data.setRequest(supplierRequest);
                return data;
            }).filter(item -> StringUtils.isNotBlank(item.getSupplierCode())).
                    collect(Collectors.toMap(SupplierHotelModel::getSupplierCode, Function.identity(), (o, n) -> n));
            log.info("获取完整的供应商基础信息 supplierHotelMap={}", JsonUtils.toJsonString(supplierHotelMap));
            addElkInfoLog("获取供应商基础信息：%s", JsonUtils.toJsonString(supplierHotelMap));
            return supplierHotelMap;
        }catch (CommonException e){
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.QUERY_SUPPLIER_DATA_FAIL.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.NEW_GET_SUPPLIER_CONFIG_METHOD_EXCEPTION,e.getMessage());
        }
        catch (Exception e){
            log.error("组装酒店查询供应商信息异常：{}", e);
        }
        return Collections.emptyMap();
    }

    private void processSupplierShield(SupplierHotelModel data, String supplierCode, Map<String, List<String>> hotelSearchSupplierFilterMap, SearchHotelListRequestVO request) {
        if (StringUtils.isBlank(supplierCode)
                || com.ctrip.corp.obt.generic.utils.CollectionUtils.isEmpty(hotelSearchSupplierFilterMap)
                || com.ctrip.corp.obt.generic.utils.CollectionUtils.isEmpty(hotelSearchSupplierFilterMap.get(supplierCode))) {
            return;
        }
        List<String> filterList = hotelSearchSupplierFilterMap.get(supplierCode);
        //根据配置判断是否支持筛选功能 如美亚不支持品牌搜索则赛选条件包含品牌时候
        data.setSupplierShield(false);
        for (String condition : filterList){
            if (StringUtils.isBlank(condition)){
                continue;
            }
            //单条件查询key
            if (StringUtils.isNotBlank(request.getKey()) && request.getKey().contains(condition)){
                data.setSupplierShield(true);
                return;
            }
            //品牌
            if (SEARCH_FILTER_BRAND.equals(condition) && CollectionUtils.isNotEmpty(request.getCatagory())){
                data.setSupplierShield(true);
                return;
            }
            //地铁
            if (SEARCH_FILTER_METRO_STATION.equals(condition) && StringUtils.isNotBlank(request.getSubway())){
                data.setSupplierShield(true);
                return;
            }
            //商业区
            if (SEARCH_FILTER_ZONE.equals(condition) && StringUtils.isNotBlank(request.getBusiness())){
                data.setSupplierShield(true);
                return;
            }
            // 价格排序
            if (SEARCH_PRICE_SORT.equals(condition) && Lists.newArrayList(SearchHotelSortEnum.SEARCH_PRICE_SORT_ASC.getType(), SearchHotelSortEnum.SEARCH_PRICE_SORT_DESC.getType()).contains(request.getRecommendSort())) {
                data.setSupplierShield(true);
                return;
            }
            // 距离排序
            if (SEARCH_DISTANCE_SORT.equals(condition) && Lists.newArrayList(SearchHotelSortEnum.SEARCH_DISTANCE_SORT_ASC.getType(), SearchHotelSortEnum.SEARCH_DISTANCE_SORT_DESC.getType()).contains(request.getRecommendSort())) {
                data.setSupplierShield(true);
                return;
            }
            // 星级排序
            if (SEARCH_STAR_SORT.equals(condition) && Lists.newArrayList(SearchHotelSortEnum.SEARCH_STAR_SORT_ASC.getType(), SearchHotelSortEnum.SEARCH_STAR_SORT_DESC.getType()).contains(request.getRecommendSort())) {
                data.setSupplierShield(true);
                return;
            }
        }
    }

    /**
     * 获取供应商产品信息
     * @param request
     * @return
     */
    public List<SupplierProductBo> getSupplierProductList(SearchHotelListRequestVO request) {
        ListSupplierProductRequestBo requestBo = new ListSupplierProductRequestBo();
        requestBo.setOperateType(hotelOperatorTypeConfig.getGetBusinessHotelList());
        requestBo.setCompanyCode(request.getUserInfo().getCorpId());
        // 转换因公因私
        requestBo.setBusPriType(PubOwnEnum.OWN.name().equals(request.getCorpPayType()) ? 2 : 1);
        return supplierCompanyClientLoader.listSupplierProduct(requestBo);
    }

    /**
     * 通过md5判断是不是相同请求
     */
    public String getHotelListRequestMd5(SearchHotelListRequestVO request) {
        SearchHotelListRequestVO data = JsonUtils.convert(request, SearchHotelListRequestVO.class);
        data.setPageNum(null);
        data.setPageSize(null);
        String result = Md5Util.md5Hex(JsonUtils.toJsonString(data));
        addElkInfoLog("酒店列表缓存key：%s", result);
        return result;
    }


    /**
     * 配置差标
     */
    private Boolean configHotelControl(SearchHotelListRequestVO request){
        //是否有差标
        if (!request.getRcpolicy()){
            addElkInfoLog("无需处理差标");
            return false;
        }

        //查询出差申请单差标
        QueryApplyTripStandardResponse hotelApplyTripStandard = applyTripClientLoader.getHotelApplyTripStandard(request.getTrafficId());
        if (Objects.isNull(hotelApplyTripStandard)) {
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.APPLY_CONTROL_IS_NULL.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.APPLY_CONTROL_IS_NULL);
        }

        if (BooleanUtils.isTrue(hotelApplyTripStandard.getEnable())) {
            return this.packageSearchListRequestToTripControl(request, hotelApplyTripStandard);
        }

        //查询差标
        HotelControlVo hotelControl = this.getHotelControl(request);
        addElkInfoLog("酒店差标内容：%s", JsonUtils.toJsonString(hotelControl));

        //处理同住
        if (CollectionUtils.isNotEmpty(request.getRooms()) || StrUtil.isNotBlank(request.getTravelStandardToken())) {
            hotelControl = this.hotelSharingData(hotelControl, request);
            addElkInfoLog("合住差标计算最后数据 %s", JsonUtils.toJsonString(hotelControl));
        }

        // 接入token
        if (StringUtils.isNotBlank(request.getTravelStandardToken()) && CorpPayTypeEnum.PUB.getType().equals(request.getCorpPayType())) {
            HotelControlVo hotelControlVo = tokenAccessService.accessToken(request.getTravelStandardToken());
            log.info("接入token后的差标 hotelControlVo={}", hotelControlVo);
            if (hotelControlVo == null) {
                throw new CorpBusinessException(HotelResponseCodeEnum.TOKEN_ACCESS_ERROR);
            }
            hotelControl = hotelControlVo;
        }

        // 根据差标设置查询条件
        return this.packageSearchListRequest(request, hotelControl);
    }

    /**
     * 获取差标属性
     */
    private Map<String, SwitchInfoVo> getTravelAttribute(String corpId) {
        if (StringUtils.isBlank(corpId)) {
            return null;
        }
        // 获取差旅属性
        List<SwitchInfoVo> travelAttributeList = organizationClientLoader.getTravelAttribute(corpId);
        if (travelAttributeList == null) {
            throw new CorpBusinessException(GET_TRAVEL_ATTRIBUTE_ERROR);
        }
        Map<String, SwitchInfoVo> switchInfoVoMap = travelAttributeList.stream().collect(Collectors.toMap(SwitchInfoVo::getKey, item -> item, (a, b) -> a));
        log.info("差旅属性={}", switchInfoVoMap);
        return switchInfoVoMap;
    }

    /**
     * 处理差标属性-星级屏蔽功能
     */
    private List<String> processHotelStarShieldControl(Map<String, SwitchInfoVo> travelAttributeMap, SearchHotelListRequestVO request) {
        if (travelAttributeMap == null) {
            return null;
        }
        // 获取星级屏蔽
        SwitchInfoVo hotelStarShieldControl = travelAttributeMap.get("hotel_star_shield_control");
        // 无星级屏蔽
        if (hotelStarShieldControl == null || CollectionUtils.isEmpty(hotelStarShieldControl.getNowVal())) {
            log.info("差标属性，无星级屏蔽，travelAttributeMap={}", travelAttributeMap);
            return null;
        }
        // 星级屏蔽
        List<?> result = hotelStarShieldControl.getNowVal();
        Set<String> shieldStarSet = result.stream().map(Object::toString).collect(Collectors.toSet());
        // 2是指1和2星级酒店
        if (shieldStarSet.stream().anyMatch("2"::equals)) {
            shieldStarSet.add("1");
        }
        // 用户筛选星级
        String star = request.getStarList();
        // 空则表示不限制星级
        if (StringUtils.isBlank(star)) {
            star = "1,2,3,4,5";
        }
        List<String> starList = Arrays.asList(StringUtils.split(star, ","));
        // 集合的差集
        List<String> disjunction = new ArrayList<>(8);
        starList.forEach(item -> {
            if (shieldStarSet.contains(item)) {
                return;
            }
            disjunction.add(item);
        });
        request.setStarList(String.join(",", disjunction));
        log.info("差旅属性星级屏蔽配置={}；用户筛选星级={}；结果星级={}", shieldStarSet, starList, disjunction);
        return disjunction;
    }

    /**
     * 查询差标数据
     * @param request
     * @param hotelApplyTripStandard
     * @return
     */
    private Boolean packageSearchListRequestToTripControl(SearchHotelListRequestVO request, QueryApplyTripStandardResponse hotelApplyTripStandard) {
        if (!TravelUnLimitedTypeEnum.isUnLimitAMOUNT(hotelApplyTripStandard.getUnLimitedType())) {
            StandardAmountSyncRequest amount = hotelApplyTripStandard.getStandardAmount();
            if (Objects.isNull(request.getMaxPrice())) {
                request.setMaxPrice(amount.getPriceUpperLimit());
            }
            if (Objects.isNull(request.getMinPrice())) {
                request.setMinPrice(BigDecimal.ZERO);
            }
            if (request.getMinPrice().compareTo(amount.getPriceUpperLimit()) > 0) {
                return true;
            }
            // 均价上限在最低价和最高价之间
            if (request.getMinPrice().compareTo(amount.getPriceUpperLimit()) < 0 && amount.getPriceUpperLimit().compareTo(request.getMaxPrice()) < 0) {
                request.setMaxPrice(amount.getPriceUpperLimit());
            }
        }
        return false;
    }

    /**
     * 获取差标
     */
    private HotelControlVo getHotelControl(SearchHotelListRequestVO request) {
        GetHotelDetailRequest detailRequest = new GetHotelDetailRequest();
        detailRequest.setCityCode(request.getCityCode());
        detailRequest.setStartDate(DateUtil.stringToDate(request.getSDate(), DateUtil.DF_YMD));
        detailRequest.setEndDate(DateUtil.stringToDate(request.getEDate(), DateUtil.DF_YMD));

        if (Objects.nonNull(request.getPostId()) && request.getPostId() > 0) {
            detailRequest.setPostId(request.getPostId());
        } else {
            if (StringUtils.isNotBlank(request.getPolicyId())) {
                detailRequest.setUid(request.getPolicyId());
                detailRequest.setOrgId(request.getPolicyOrgId());
            } else {
                BaseRequestVO.UserInfo userInfo = request.getUserInfo();
                detailRequest.setUid(userInfo.getUid());
                detailRequest.setOrgId(userInfo.getOrgId());
            }
        }

        return travelStandardPostClientLoader.getHotelDetail(detailRequest);
    }

    private HotelControlVo hotelSharingData(HotelControlVo hotelControl, SearchHotelListRequestVO request) {
        if(ObjectUtil.isEmpty(hotelControl)){
            hotelControl = new HotelControlVo();
        }
        HotelControlVo allDifferentialPrice = null;
        String travelStandardToken = request.getTravelStandardToken();
        // 根据同住token获取同住差标
        if(StrUtil.isNotBlank(travelStandardToken)){
            allDifferentialPrice = travelStandardService.getHotelControlVoByToken(travelStandardToken);
            if(ObjectUtil.isNull(allDifferentialPrice.getOffPeakSeasonSet()) && ObjectUtil.isNull(allDifferentialPrice.getAveragePriceSet())){
                hotelControl.setAveragePriceSet(null);
            }
        } else {
            GetAllDifferentialPriceRequest getAllDifferentialPriceRequest = new GetAllDifferentialPriceRequest();
            getAllDifferentialPriceRequest.setCityId(request.getCityCode());
            getAllDifferentialPriceRequest.setEDate(request.getEDate());
            getAllDifferentialPriceRequest.setSDate(request.getSDate());
            getAllDifferentialPriceRequest.setPolicy(request.getPolicyId());
            getAllDifferentialPriceRequest.setPolicyOrgId(request.getPolicyOrgId());
            getAllDifferentialPriceRequest.setHotelControl(hotelControl);
            List<GetAllDifferentialPriceRequest.Room> rooms = new ArrayList<>();
            for (Room room : request.getRooms()) {
                rooms.add(new GetAllDifferentialPriceRequest.Room(room.getRoomNumber(), ListUtils.copyList(room.getResidentList(), GetAllDifferentialPriceRequest.Resident.class)));
            }
            getAllDifferentialPriceRequest.setRooms(rooms);
            allDifferentialPrice = organizationClientLoader.getAllDifferentialPrice(getAllDifferentialPriceRequest, ObjectUtils.copyProperties(request.getUserInfo(), BaseUserInfo.class));
        }

        if (Objects.nonNull(allDifferentialPrice)){
            if(ObjectUtil.isNotNull(allDifferentialPrice.getOffPeakSeasonSet())){
                hotelControl.setAveragePriceSet(allDifferentialPrice.getOffPeakSeasonSet());
            }else if(ObjectUtil.isNotNull(allDifferentialPrice.getAveragePriceSet())){
                hotelControl.setAveragePriceSet(allDifferentialPrice.getAveragePriceSet());
            }
            if(ObjectUtil.isNotNull(allDifferentialPrice.getAverageStarSet())){
                hotelControl.setAverageStarSet(allDifferentialPrice.getAverageStarSet());
            }
        }
        return hotelControl;
    }

    /**
     * 组装差标 带入查询条件
     */
    private Boolean packageSearchListRequest(SearchHotelListRequestVO request, HotelControlVo hotelControl) {
        if (hotelControl == null) {
            return false;
        }
        Boolean result = Boolean.FALSE;
        AveragePriceSet averagePriceSet = hotelControl.getAveragePriceSet();
        BigDecimal priceCeiling = new BigDecimal(0);
        BigDecimal priceFloor = new BigDecimal(0);
        AveragePriceSet offPeakSeasonSet = hotelControl.getOffPeakSeasonSet();
        if (offPeakSeasonSet != null) {
            priceCeiling = StringUtils.isNotBlank(offPeakSeasonSet.getPriceCeiling())?new BigDecimal(offPeakSeasonSet.getPriceCeiling()):null;
            priceFloor = StringUtils.isNotBlank(offPeakSeasonSet.getPriceFloor())?new BigDecimal(offPeakSeasonSet.getPriceFloor()):null;
        } else if (averagePriceSet != null) {
            priceCeiling = StringUtils.isNotBlank(averagePriceSet.getPriceCeiling())?new BigDecimal(averagePriceSet.getPriceCeiling()):null;
            priceFloor = StringUtils.isNotBlank(averagePriceSet.getPriceFloor())?new BigDecimal(averagePriceSet.getPriceFloor()):null;
        }

        addElkInfoLog("HoHotelService.page 价格管控：%s", JsonUtils.toJsonString(averagePriceSet));

        BigDecimal minPrice = request.getMinPrice();
        BigDecimal maxPrice = request.getMaxPrice();
        if (priceCeiling.compareTo(BigDecimal.ZERO) > 0) {
            if (minPrice == null) {
                request.setMinPrice(priceFloor);
            }
            if (maxPrice == null) {
                request.setMaxPrice(priceCeiling);
            }
            // 最低价大于均价上限，价格超标
            if (request.getMinPrice().compareTo(priceCeiling) > 0) {
                result = Boolean.TRUE;
            }
            // 均价上限在最低价和最高价之间
            if (request.getMinPrice().compareTo(priceCeiling) < 0 && priceCeiling.compareTo(request.getMaxPrice()) < 0) {
                request.setMaxPrice(priceCeiling);
            }
        }

        AverageStarSet averageStarSet = hotelControl.getAverageStarSet();
        addElkInfoLog("HoHotelService.page 星级管控：%s", JsonUtils.toJsonString(averageStarSet));
        if (averageStarSet != null) {
            // 星级管控     0为不限
            int maxStarCeiling = Integer.parseInt(averageStarSet.getStarCeiling().getValue());
            maxStarCeiling = 0 == maxStarCeiling ? 5 : maxStarCeiling;
            if (StringUtils.isEmpty(request.getStarList())) {
                String starList = IntStream.rangeClosed(1, maxStarCeiling).boxed().map(Objects::toString).collect(Collectors.joining(","));
                request.setStarList(starList);
            } else {
                String[] stars = StringUtils.split(request.getStarList(), ",");
                if (ArrayUtils.isNotEmpty(stars)) {
                    List<String> resultStarList = new ArrayList<>();
                    for (String star : stars) {
                        if (Integer.parseInt(star) > maxStarCeiling) {
                            continue;
                        }
                        resultStarList.add(star);
                    }
                    // 星级超标
                    if (CollectionUtils.isEmpty(resultStarList)) {
                        result = Boolean.TRUE;
                        log.info("星级超标");
                    }
                    else {
                        request.setStarList(String.join(",", resultStarList));
                    }
                    log.info("星级管控 请求星级={} 星级上限={} 最终星级={}", stars, maxStarCeiling, resultStarList);
                }
            }
        }
        return result;
    }

    private Map<String, List<String>> analysisCategory(String cityId, List<String> supplierCodeList, List<String> category) {
        Map<String, List<String>> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(category)) {
            //没有需要解析的key
            return result;
        }
        supplierCodeList.forEach(supplier -> {
            result.put(supplier, Lists.newArrayList());
        });
        StaticMapListRequestBo listRequest = new StaticMapListRequestBo();
        List<StaticMapRequestBo> list = category.stream().map(key -> {
            String[] strs = StringUtils.split(key, "&");
            String destinationId = strs[0];
            String destinationType = strs[1];
            HotelRequestTypeEnum typeEnum = HotelRequestTypeEnum.get(destinationType);
            StaticMapRequestBo request = new StaticMapRequestBo();
            //解析key
            request.setTypeEnum(typeEnum);
            request.setCityId(cityId);
            request.setWantedSuppliers(supplierCodeList);
            request.setId(destinationId);
            return request;
        }).collect(Collectors.toList());
        listRequest.setList(list);
        List<StaticMapResponse> listResponse = mapResultService.mapResponseList(listRequest);
        if (CollectionUtils.isEmpty(listResponse)) {
            return result;
        }
        for (int i = 0; i < listResponse.size(); i++) {
            StaticMapResponse response = listResponse.get(i);
            String key = category.get(i);
            String[] strs = StringUtils.split(key, "&");
            String destinationType = strs[1];
            String lon = strs[2];
            String lat = strs[3];
            supplierCodeList.forEach(supplierCode -> {
                String id = response.getResult().get(supplierCode);
                if (StringUtils.isBlank(id)) {
                    return;
                }
                result.get(supplierCode).add(MessageFormat.format(LOCATION_DESC, id, destinationType, lon, lat));
            });
        }
        return result;
    }

    /**
     * 供应商基础数据mapping
     */
    private Map<String, String>  analysisKey(SearchHotelListRequestVO request, List<String> supplierCodeList) {
        log.info("需要解析的request{}", JsonUtils.toJsonString(request));
        Map<String, String> result = Maps.newHashMap();
        if (StringUtils.isEmpty(request.getKey())) {
            //没有需要解析的key
            return result;
        }
        //解析key
        String key = request.getKey();
        String[] strs = StringUtils.split(key, "&");
        String destinationId = strs[0];
        String destinationType = strs[1];
        String lon = strs[2];
        String lat = strs[3];
        HotelRequestTypeEnum typeEnum = HotelRequestTypeEnum.get(destinationType);
        List<String> wantedSuppliers = Lists.newArrayList();
        StaticMapRequestBo staticMapRequest = new StaticMapRequestBo();

        supplierCodeList.forEach(supplierCode -> {
            if (Boolean.TRUE.equals(request.getFromKeywordSearch())) {
                // 模糊查询，则取模糊查询接口的供应商code
                String srcSupplierCode = hotelApollo.getSearchLocationSupplierCode();
                staticMapRequest.setSupplier(srcSupplierCode);
                if (supplierCode.equals(srcSupplierCode)) {
                    // 当前供应商code==模糊查询接口的供应商code 无需mapping
                    log.info("当前供应商code==模糊查询接口的供应商code，无需对key进行mapping处理");
                    result.put(supplierCode, key);
                    return;
                }
            }
            if (HotelRequestTypeEnum.PERSON.equals(typeEnum)) {
                addElkInfoLog("PERSON  不走mapping");
                result.put(supplierCode, key);
                return;
            }
            wantedSuppliers.add(supplierCode);
        });
        if (CollectionUtils.isEmpty(wantedSuppliers)) {
            log.info("无需对Key进行mapping处理，key：{}", key);
            return result;
        }
        //解析key
        staticMapRequest.setTypeEnum(typeEnum);
        staticMapRequest.setCityId(request.getCityCode());
        staticMapRequest.setWantedSuppliers(wantedSuppliers);
        staticMapRequest.setId(destinationId);
        staticMapRequest.setSupplier("ctrip");
        log.info("多供应商静态数据StaticMapRequest：{}", JsonUtils.toJsonString(staticMapRequest));
        StaticMapResponse response = mapResultService.mapResponse(staticMapRequest);
        log.info("多供应商静态数据StaticMapResponse：{}", JsonUtils.toJsonString(response));
        if (Optional.ofNullable(response).map(StaticMapResponse::getResult).isPresent()) {
            wantedSuppliers.forEach(supplier -> {
                String id = response.getResult().get(supplier);
                if (StringUtils.isBlank(id)) {
                    log.info("供应商{}静态条件数据获取失败", supplier);
                    return;
                }
                result.put(supplier, MessageFormat.format(LOCATION_DESC, id, destinationType, lon, lat));
            });
        } else {
            log.info("供应商静态条件数据获取失败");
        }
        return result;
    }

    /**
     * 查询供应商城市信息
     */
    private Map<String, String> getSupplierCityMap(String cityCode, List<String> supplierCodeList) {
        StaticMapRequestBo supplierCityRequest = new StaticMapRequestBo();
        supplierCityRequest.setCityId(cityCode);
        supplierCityRequest.setId(cityCode);
        supplierCityRequest.setTypeEnum(HotelRequestTypeEnum.CITY);
        supplierCityRequest.setWantedSuppliers(supplierCodeList);
        StaticMapResponse cityResp = mapResultService.mapResponse(supplierCityRequest);
        if (cityResp == null || cityResp.getResult() == null) {
            return new HashMap<>();
        }
        return cityResp.getResult();
    }

    private void setMetric(SearchHotelListResponseVO responseVO){
        Metrics.REGISTRY.counter(queryListMetricId.withTags("hasResult",
                Conditional.ofNullable(responseVO).map(SearchHotelListResponseVO::getHotelList)
                        .map(t -> t.size() > 0 ? Boolean.TRUE.toString() : Boolean.FALSE.toString())
                        .orElse(Boolean.FALSE.toString())))
                .increment();
    }
}
