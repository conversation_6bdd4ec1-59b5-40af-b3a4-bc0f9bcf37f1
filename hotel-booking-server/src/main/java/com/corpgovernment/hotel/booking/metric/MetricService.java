package com.corpgovernment.hotel.booking.metric;

import com.corpgovernment.api.approvalsystem.bean.FlowDetail;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderResponseVo;
import com.corpgovernment.api.hotel.booking.saveorder.response.SaveOrderResponseVo;
import com.corpgovernment.api.hotel.product.model.bookorder.response.LocalBookOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.saveorder.request.SaveOrderRequestBo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.common.ValidGroup;
import com.corpgovernment.common.utils.ValidationUtil;
import com.corpgovernment.hotel.booking.config.apollo.HotelConfig;
import com.corpgovernment.hotel.booking.vo.HotelSubmitResponseVO;
import com.corpgovernment.hotel.product.dto.SupplierResponseDTO;
import com.ctrip.corp.obt.generic.constants.GenericConstants;
import com.ctrip.corp.obt.generic.utils.Conditional;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 酒店埋点
 *
 * <AUTHOR>
 * @since 2023/8/25
 */
@Component
@Slf4j
public class MetricService {

    @Autowired
    @Qualifier("logThreadPoolExecutor")
    private ThreadPoolExecutor logThreadPoolExecutor;

    /**
     * 创单接口埋点
     */
    private static final Id SAVE_ORDER = Metrics.REGISTRY.createId("hotel.domestic.response.saveOrder");
    /**
     * 取消接口埋点
     */
    private static final Id CANCEL_ORDER = Metrics.REGISTRY.createId("hotel.domestic.response.cancelOrder");
    /**
     * 供应商创单接口埋点
     */
    private static final Id SUPPLIER_SAVE_ORDER =
        Metrics.REGISTRY.createId("hotel.domestic.supplier.response.saveOrder");
    /**
     * 酒店修改（提前离店）
     */
    private static final Id hotelModifyId = Metrics.REGISTRY.createId("hotel.domestic.response.hotelModify");

    /**
     * 下单因公无审批流
     */
    private static final Id APPROVAL_FLOW = Metrics.REGISTRY.createId("hotel.domestic.approval.flow.detail");
    /**
     * 服务费埋点
     */
    private static final Id SERVICEFEE_ID = Metrics.REGISTRY.createId("hotel.domestic.servicefee");

    private static final Id ORDER_CHECK_STANDARD = Metrics.REGISTRY.createId("hotel.domestic.order.check.standard");


    private static final Id STANDARD_LEGITIMACY_CHECK_STANDARD = Metrics.REGISTRY.createId("hotel.standard.legitimacy.check");

    /**
     * 分销接口埋点
     */
    private static final Id SUPPLIER_API_RESULT = Metrics.REGISTRY.createId("hotel.domestic.supplier.api.result");

    /**
     * 订单补录
     */
    private static final Id ORDER_SUPPLEMENT_ID = Metrics.REGISTRY.createId("hotel.order.supplement");

    private static final Id SUPPLIER_ORDER_DETAIL = Metrics.REGISTRY.createId("hotel.supplier.order.detail");

    /**
     * 供应商返回信息校验
     */
    private static final Id SUPPLIER_ORDER_INFO_CHECK = Metrics.REGISTRY.createId("hotel.supplier.response.valid");

    private static final Id HOTEL_ORDER_RISK_CONTROL = Metrics.REGISTRY.createId("hotel.domestic.order.risk.control");


    /**
     * 埋点的tag
     */
    private static final String SUCCESS = "success";
    private static final String RCTYPE = "rcType";
    private static final String FLOWTYPE = "flowType";
    private static final String SUPPLIER_CODE = "supplierCode";
    private static final String ERROR_CODE = "errorCode";
    private static final String ERROR_MESSAGE = "errorMessage";
    private static final String TYPE = "type";
    private static final String SERVICEFEE = "servicefee";

    private static final String RESPONSE_CODE = "responseCode";
    private static final String MESSAGE = "message";
    private static final String NAME = "name";

    private static final String DEFAULT_VALUE = "-1";
    private static final String UNKNOWN = "未知";

    private static final String ORDER_ID = "orderId";

    private static final String RISK_CONTROL_RESULT = "riskControlResult";


    /**
     * 执行并且埋点创单次数以及耗时
     *
     * @param saveOrderFunc 创单函数
     */
    public JSONResult<SaveOrderResponseVo> execAndMetricSaveOrder(
        Function<SaveOrderMetricContext, SaveOrderResponseVo> saveOrderFunc) {

        Instant start = Instant.now();
        SaveOrderMetricContext metricContext = new SaveOrderMetricContext();
        SaveOrderResponseVo response = null;

        try {
            response = saveOrderFunc.apply(metricContext);
            return JSONResult.success(response);
        } finally {
            SaveOrderResponseVo finalResponse = response;
            // 埋点请求次数耗时
            logThreadPoolExecutor.execute(() -> Metrics.REGISTRY
                .timer(SAVE_ORDER
                    .withTag("responseCode",
                        Optional.ofNullable(finalResponse).map(SaveOrderResponseVo::getErrorCode)
                            .orElse(StringUtils.EMPTY))
                    .withTag("responseMessage", Optional.ofNullable(finalResponse)
                        .map(SaveOrderResponseVo::getErrorMsg)
                        .orElse(StringUtils.EMPTY))
                    .withTag("supplierCode",
                        Optional.ofNullable(metricContext.getSupplierCode()).orElse(StringUtils.EMPTY)))
                .record(getDuration(start)));
        }
    }

    /**
     * 执行并且埋点供应商创单次数及耗时
     *
     * @param saveOrderFunc 创单函数
     */
    public LocalBookOrderResponseBo execAndMetricSupplierSaveOrder(Supplier<LocalBookOrderResponseBo> saveOrderFunc,
        String supplierCode) {
        LocalBookOrderResponseBo response = null;
        Instant start = Instant.now();
        String resultCode = StringUtils.EMPTY;
        String msg = StringUtils.EMPTY;

        try {
            response = saveOrderFunc.get();
            return response;
        } catch (CorpBusinessException e) {
            resultCode = String.valueOf(e.getResultCode());
            msg = e.getMsg();
            throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_SUPPLIER_ERROR.code(), msg);
        } finally {
            LocalBookOrderResponseBo finalResponse = response;
            String finalResultCode = resultCode;
            String finalErrorMessage = msg;
            logThreadPoolExecutor.execute(() -> {
                // 判断是否创单成功
                String errorCode = finalResultCode;
                if (Optional.ofNullable(finalResponse).map(LocalBookOrderResponseBo::getSupplierOrderId)
                    .filter(StringUtils::isNotBlank).isPresent()) {
                    errorCode = String.valueOf(HotelResponseCodeEnum.SUCCESS_CODE.code());
                }
                Metrics.REGISTRY.timer(SUPPLIER_SAVE_ORDER
                    .withTag("supplierCode", supplierCode)
                    .withTag("errorCode", errorCode)
                    .withTag("errorMessage", finalErrorMessage))
                    .record(getDuration(start));
            });
        }
    }

    /**
     * 获取到startTime到当前时间的耗时
     */
    private Duration getDuration(Instant startTime) {
        return Duration.between(startTime, Instant.now());
    }

    /**
     * 埋点提交取消计数
     *
     * @param cancelOrderResponseVo
     */
    public void metricCancelOrder(CancelOrderResponseVo cancelOrderResponseVo) {
        Metrics.REGISTRY.counter(CANCEL_ORDER.withTag("cancelResult",
            Conditional.ofNullable(cancelOrderResponseVo)
                .map(CancelOrderResponseVo::isSuccess)
                .map(Object::toString).orElse(GenericConstants.UNKNOWN)))
            .increment();

    }

    /**
     * 埋点超标检查
     */
    public void metricsOrderCheckStandard(boolean result) {
        try {
            Metrics.REGISTRY.counter(ORDER_CHECK_STANDARD.withTag(Boolean.toString(result), result)).increment();
        } catch (Exception e) {
            log.error("埋点超标检查监控失败", e);
        }
    }

    public void metricsStandardLegitimacyCheck(boolean result) {
        try {
            Metrics.REGISTRY.counter(STANDARD_LEGITIMACY_CHECK_STANDARD.withTag(Boolean.toString(result), result)).increment();
        } catch (Exception e) {
            log.error("埋点差标合法性校验监控失败", e);
        }
    }

    /**
     * 埋点审批流
     *
     * @param approvalFlowDetail
     */
    public void metricApprovalFlowDetail(FlowDetail approvalFlowDetail) {
        logThreadPoolExecutor.execute(() -> {
            try {
                // 判断是否创单成功
                Metrics.REGISTRY.counter(APPROVAL_FLOW
                    .withTag(SUCCESS, Objects.nonNull(approvalFlowDetail))
                    .withTag(RCTYPE,
                        Optional.ofNullable(approvalFlowDetail).map(FlowDetail::getRcType).orElse(0).toString())
                    .withTag(FLOWTYPE,
                        Optional.ofNullable(approvalFlowDetail).map(FlowDetail::getFlowType).orElse(0).toString()))
                    .increment();
            } catch (Exception e) {
                log.error("埋点审批流监控失败", e);
            }

        });
    }

    /**
     * 申请修改（提前离店）
     * 
     * @param hotelModifyMetricBo
     */
    public void metricHotelModify(HotelModifyMetricBo hotelModifyMetricBo) {
        if (null == hotelModifyMetricBo) {
            return;
        }
        logThreadPoolExecutor.execute(() -> {
            try {
                Map<String, String> paramsMap = new HashMap<>();
                paramsMap.put(SUPPLIER_CODE, StringUtils.isNotBlank(hotelModifyMetricBo.getSupplierCode())
                    ? hotelModifyMetricBo.getSupplierCode() : StringUtils.EMPTY);
                paramsMap.put(ERROR_CODE, StringUtils.isNotBlank(hotelModifyMetricBo.getErrorCode())
                    ? hotelModifyMetricBo.getErrorCode() : StringUtils.EMPTY);
                paramsMap.put(ERROR_MESSAGE, StringUtils.isNotBlank(hotelModifyMetricBo.getErrorMessage())
                    ? hotelModifyMetricBo.getErrorMessage() : StringUtils.EMPTY);
                Metrics.REGISTRY.counter(hotelModifyId.withTags(paramsMap)).increment();
            } catch (Exception e) {
                log.error("埋点酒店修改监控失败", e);
            }
        });
    }

    /**
     * 服务费埋点
     *
     * @param orderInfo
     */
    public void metricServiceFee(SaveOrderRequestBo.OrderInfo orderInfo) {
        try {
            BigDecimal serviceFee = Optional.ofNullable(orderInfo.getServiceFee()).orElse(BigDecimal.ZERO);
            logThreadPoolExecutor.execute(() -> Metrics.REGISTRY.counter(SERVICEFEE_ID
                .withTag(SUPPLIER_CODE, Optional.ofNullable(orderInfo.getSupplierCode()).orElse(StringUtils.EMPTY))
                .withTag(TYPE, serviceFee.compareTo(BigDecimal.ZERO) > 0 ? "preService" : "noService")
                .withTag(SERVICEFEE, serviceFee.toString()))
                .increment());
        } catch (Exception e) {
            log.error("服务费信息埋点监控失败", e);
        }
    }
    /**
     * 分销接口埋点
     *
     * @param responseBody
     */
    public void metricSupplierApiResult(String responseBody, String supplierCode, String name, long elapsed,
        HotelResponseCodeEnum hotelResponseCodeEnum) {
        try {
            logThreadPoolExecutor.execute(() -> {
                SupplierResponseDTO response = null;
                if (StringUtils.isNotBlank(responseBody)) {
                    response = JsonUtils.parse(responseBody, SupplierResponseDTO.class);
                }

                String errorCode = "";
                String message = "";
                if (Objects.nonNull(response)) {
                    if (StringUtils.isNotBlank(response.getResultCode())) {
                        errorCode = response.getResultCode();
                        message = response.getMessage();
                    } else if (Objects.nonNull(response.getStatus())
                        && StringUtils.isNotBlank(response.getStatus().getErrorCode())) {
                        errorCode = response.getStatus().getErrorCode();
                        message = response.getStatus().getErrorMessage();
                    } else {
                        errorCode = String.valueOf(hotelResponseCodeEnum.code());
                        message = hotelResponseCodeEnum.message();
                    }
                }
                Metrics.REGISTRY.timer(SUPPLIER_API_RESULT
                    .withTag(RESPONSE_CODE, StringUtils.isNotBlank(errorCode) ? errorCode : DEFAULT_VALUE)
                    .withTag(MESSAGE, StringUtils.isNotBlank(message) ? message : DEFAULT_VALUE)
                    .withTag(SUPPLIER_CODE, StringUtils.isNotBlank(supplierCode) ? supplierCode : UNKNOWN)
                    .withTag(NAME, name)).record(Duration.ofMillis(elapsed));
            });
        } catch (Exception e) {
            log.error("分销接口埋点监控失败", e);
        }
    }

    public HotelSubmitResponseVO metricOrderSupplement(String supplierCode, Supplier<HotelSubmitResponseVO> supplier) {
        Instant startTime = Instant.now();
        boolean success = false;
        try {
            HotelSubmitResponseVO result = supplier.get();
            if (result != null && result.getOrderId() != null){
                success = true;
            }
            return result;
        } finally {
            Metrics.REGISTRY.timer(ORDER_SUPPLEMENT_ID
                            .withTag(SUPPLIER_CODE, supplierCode)
                            .withTag(SUCCESS, success))
                    .record(getDuration(startTime));
        }
    }
    @SafeVarargs
    public final void metricSupplierReturnInfoValid(String orderId, String supplierCode, Object response,
                                              String methodName,
                                              Class<? extends ValidGroup>... validGroupClass) {
        String errMsg = StringUtils.EMPTY;
        try {
            errMsg = ValidationUtil.validate(response, HotelConfig.needValidateResponse(), validGroupClass);
        } finally {
            boolean success = StringUtils.isBlank(errMsg);
            String finalErrMsg = errMsg;
            if (!success) {
                logThreadPoolExecutor.execute(() -> {
                    try {
                        Metrics.REGISTRY.counter(SUPPLIER_ORDER_INFO_CHECK
                                        .withTag(SUCCESS, success)
                                        .withTag(ORDER_ID, orderId)
                                        .withTag(ERROR_MESSAGE, finalErrMsg)
                                        .withTag(SUPPLIER_CODE, supplierCode))
                                .increment();
                    } catch (Exception e) {
                        log.error("供应商" + methodName + "校验失败", e);
                    }
                });
            }

        }
    }

    public void metricOrderRiskControl(Long orderId, String status) {
        try {
            logThreadPoolExecutor.execute(() -> Metrics.REGISTRY.counter(HOTEL_ORDER_RISK_CONTROL
                    .withTag(ORDER_ID, String.valueOf(orderId))
                    .withTag(RISK_CONTROL_RESULT, status)).increment());
        } catch (Exception e) {
            log.error("风控信息埋点监控失败:" + e.getMessage(), e);
        }
    }
}
