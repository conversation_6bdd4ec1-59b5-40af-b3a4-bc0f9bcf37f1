package com.corpgovernment.hotel.booking.bo;

import com.corpgovernment.api.hotel.booking.hotel.request.SearchHotelListRequestVO;
import com.corpgovernment.hotel.booking.cache.HotelListPageCacheInfo;
import com.corpgovernment.hotel.booking.cache.SupplierHotelModel;
import lombok.Data;

import java.util.Map;

@Data
public class SearchHotelListBo {

    /**
     * 前端页面请求参数
     */
    private SearchHotelListRequestVO pageRequest;

    /**
     * 供应商数据-->(key：供应商编码，value：调用供应商请求信息，供应商城市信息，供应商酒店查询返回结果)
     */
    private Map<String, SupplierHotelModel> supplierHotelMap;

    /**
     * 列表查询上一页缓存信息
     */
    private HotelListPageCacheInfo prePageCacheInfo;

    /**
     * 列表查询当前页缓存信息
     */
    private HotelListPageCacheInfo currentPageCacheInfo;

    /**
     * 存入redis的key值
     */
    private String redisKey;

    /**
     * 是否进行mapping操作，缓存足够页面分页使用则不需要mapping因为缓存数据都是mapping过的，新数据都需要进行mapping
     */
    private Boolean isMapping = Boolean.TRUE;

    /**
     * 分布式锁key
     */
    private String searchKey;
}
