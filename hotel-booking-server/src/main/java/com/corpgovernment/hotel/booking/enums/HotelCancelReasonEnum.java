package com.corpgovernment.hotel.booking.enums;

import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.common.base.BaseUserInfo;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: lilayzzz
 * @since: 2024/1/12
 * @description:
 */
@Getter
public enum HotelCancelReasonEnum {

    SCHEDULE_CHANGE("SCHEDULE CHANGE", "行程改变", false, "", ""),
    RESERVATION_ERROR("RESERVATION ERROR", "预订错误", false, "", ""),
    BOOKING_ERROR("BOOKINGERROR", "预订错误", false, "", ""),
    LOWER_PRICE("LOWER PRICE", "酒店价格太贵", false, "", ""),
    NO_ROOM("NO_ROOM", "已满房", true, "酒店已满房，若需出行可重新预订", ""),
    HOTEL_REASON("HOTEL_REASON", "酒店原因", true, "因酒店原因取消：%s，若需出行可重新预订", "因酒店原因取消，若需出行可重新预订"),
    MALAISE("MALAISE", "身体不适", false, "", ""),
    FALLILL("FALLILL", "身体不适", false, "", ""),
    TRAFFIC_DELAY("TRAFFIC_DELAY", "交通延误/取消", false, "", ""),
    TRAFFICDELAY("TRAFFICDELAY", "交通延误/取消", false, "", ""),
    OTHER("OTHER", "其他", false, "", ""),
    RCCANCEL("RCCANCEL", "管控取消", false, "", ""),
    JOURALTER("JOURALTER", "行程变更", false, "", ""),
    NOTFULFIL("NOTFULFIL", "无法满足需求", false, "", ""),
    MINNOTICE("MINNOTICE", "15分钟内通知取消", false, "", ""),
    OTHERORDER("OTHERORDER", "其他途径预订", false, "", ""),
    COTHER("COTHER", "其他", false, "", ""),
    BADPRICE("BADPRICE", "价格不准确", false, "", ""),
    DBLORDER("DBLORDER", "重复预订", true, "重复预订，若需出行可重新预订", ""),
    HHALFDATA("HHALFDATA", "入住登记不详细", true, "入住登记不详细，若需出行可重新预订", ""),
    NOPREPAY("NOPREPAY", "未收到预付款", true, "付款异常，若需出行可重新预订", ""),
    SELFCANCELFAILED("SELFCANCELFAILED", "客户自助取消失败", false, "", ""),
    TESTING("TESTING", "测试", true, "测试订单，若需出行可重新预订", ""),
    PCANCEL("PCANCEL", "online/app客户取消", false, "", ""),
    APPROVAL_REJECTION("APPROVAL_REJECTION", "审批驳回订单取消", true, "审批驳回，驳回原因：%s，若需出行可重新预订", "审批驳回，若需出行可重新预订"),
    PAYMENT_TIMEOUT("PAYMENT_TIMEOUT", "订单超时未支付", true, "订单超时未支付，若需出行可重新预订", ""),
    ERROR_TIMEOUT("ERROR_TIMEOUT", "系统异常提交失败", true, "系统异常订单提交失败，若需出行可重新预订", ""),
    ERROR_SUPPLIER("ERROR_SUPPLIER", "供应商订单提交失败", true, "供应商订单提交失败，若需出行可重新预订", ""),
    ;

    /**
     * 供应商取消编码
     */
    private final String code;
    /**
     * 取消原因
     */
    private final String reason;
    /**
     * APP/PC是否需要展示原因
     */
    private final Boolean view;
    /**
     * APP/PC展示文案
     */
    private final String desc;

    /**
     * APP/PC展示文案默认值
     */
    private final String defaultDesc;

    HotelCancelReasonEnum(String code, String reason, Boolean view, String desc, String defaultDesc) {
        this.code = code;
        this.reason = reason;
        this.view = view;
        this.desc = desc;
        this.defaultDesc = defaultDesc;
    }

    public static HotelCancelReasonEnum getEnumByOrderStatus(String orderStatus) {
        if (StringUtils.isBlank(orderStatus)) {
            return null;
        }
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        if (Objects.nonNull(baseUserInfo) && StringUtils.isNotBlank(baseUserInfo.getUid())) {
            return null;
        }
        if (OrderStatusEnum.SI.getCode().equalsIgnoreCase(orderStatus)) {
            // 超时订单未处理
            return HotelCancelReasonEnum.ERROR_TIMEOUT;
        } else if (OrderStatusEnum.PW.getCode().equalsIgnoreCase(orderStatus)) {
            // 超时未支付
            return HotelCancelReasonEnum.PAYMENT_TIMEOUT;
        } else if (OrderStatusEnum.AW.getCode().equalsIgnoreCase(orderStatus)) {
            // 审批驳回取消
            return HotelCancelReasonEnum.APPROVAL_REJECTION;
        }
        return null;
    }

    public static String getViewDescByCode(String reasonCode, String reasonDesc) {
        Optional<HotelCancelReasonEnum> reasonEnumOptional = Arrays.stream(HotelCancelReasonEnum.values())
                .filter(item -> item.getCode().equalsIgnoreCase(reasonCode)).findFirst();
        if (reasonEnumOptional.isPresent()) {
            HotelCancelReasonEnum hotelCancelReasonEnum = reasonEnumOptional.get();
            if (hotelCancelReasonEnum.getDesc().contains("%s")) {
                return StringUtils.isNotBlank(reasonDesc)
                        ? String.format(hotelCancelReasonEnum.getDesc(), reasonDesc)
                        : hotelCancelReasonEnum.getDefaultDesc();
            }
            return hotelCancelReasonEnum.getDesc();
        }
        return StringUtils.EMPTY;
    }

    public static boolean getViewByCode(String reasonCode) {
        Optional<HotelCancelReasonEnum> reasonEnumOptional = Arrays.stream(HotelCancelReasonEnum.values())
                .filter(item -> item.getCode().equalsIgnoreCase(reasonCode)).findFirst();
        if (reasonEnumOptional.isPresent()) {
            return reasonEnumOptional.get().getView();
        }
        return false;
    }

    public static String getReasonByCode(String reasonCode) {
        Optional<HotelCancelReasonEnum> reasonEnumOptional = Arrays.stream(HotelCancelReasonEnum.values())
                .filter(item -> item.getCode().equalsIgnoreCase(reasonCode)).findFirst();
        if (reasonEnumOptional.isPresent()) {
            return reasonEnumOptional.get().getReason();
        }
        return StringUtils.EMPTY;
    }
}
