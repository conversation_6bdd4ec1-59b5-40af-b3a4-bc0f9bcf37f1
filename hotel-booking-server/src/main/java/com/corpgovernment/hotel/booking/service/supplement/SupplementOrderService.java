package com.corpgovernment.hotel.booking.service.supplement;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.order.common.enums.DataStorageTypeEnum;
import com.corpgovernment.api.ordercenter.event.OrderSupplementEvent;
import com.corpgovernment.api.organization.dto.request.business.unit.BusinessUnitReferenceReq;
import com.corpgovernment.api.organization.dto.response.business.unit.BusinessUnitSimpleDto;
import com.corpgovernment.api.platform.bo.CreateDirectRefundOrderRequest;
import com.corpgovernment.api.platform.soa.paymentbill.CreatePaymentBillRequest;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.enums.ExpenseTypeEnum;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.consolidation.sdk.enums.ApplicationEnum;
import com.corpgovernment.consolidation.sdk.event.OrderCreateExternalEvent;
import com.corpgovernment.hotel.product.dataloader.soa.BusinessUnitClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CostCenterClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayClientLoader;
import com.ctrip.corp.obt.api.eventcenter.enums.BusinessTypeEnum;
import com.ctrip.corp.obt.api.eventcenter.eventmodel.OrderEvent;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.event.EventCenter;
import com.ctrip.corp.obt.generic.utils.EnvironmentHolder;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.corpgovernment.api.ordercenter.dto.supplement.HotelOrderSupplementInfo;
import com.corpgovernment.api.ordercenter.dto.supplement.OrderSupplementInfoResponse;
import com.corpgovernment.common.enums.DeliveryTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.hotel.product.dataloader.db.*;
import com.corpgovernment.hotel.product.entity.db.*;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @module SupplementOrderService
 * @date 2024/8/19 14:20
 */
@Slf4j
@Service
public class SupplementOrderService {

    @Autowired
    private SupplementOrderService supplementOrderService;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HoHotelLoader hoHotelLoader;
    @Autowired
    private HoRoomLoader hoRoomLoader;
    @Autowired
    private HoPassengerLoader hoPassengerLoader;
    @Autowired
    private HoRoomDailyInfoLoader hoRoomDailyInfoLoader;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private EventCenter eventCenter;
    @Autowired
    private PayClientLoader payClientLoader;
    @Autowired
    private BusinessUnitClientLoader businessUnitClientLoader;

    @Autowired
    private CostCenterClientLoader costCenterClientLoader;
    @Autowired
    private HoDataStorageLoader hoDataStorageLoader;

    private final static Integer RC_TYPE = 1;

    private static final String HOTEL_DOMESTIC = "HotelDomestic";
    
    private static final String DEFAULT_ROOM_NAME = "补录房型";

    /**
     * 保存订单信息
     * 支持部分成功&部分失败
     *
     * @param supplementInfos
     * @return
     */
    public OrderSupplementInfoResponse saveOrder(List<HotelOrderSupplementInfo> supplementInfos) {

        if (CollectionUtils.isEmpty(supplementInfos)) {
            return new OrderSupplementInfoResponse();
        }

        // 保存成功订单号集合
        List<Long> orderIds = new ArrayList<>(supplementInfos.size());

        for (HotelOrderSupplementInfo supplementInfo : supplementInfos) {
            Long orderId = null;
            try {
                // 保存订单
                orderId = supplementOrderService.saveOrder(supplementInfo);
                orderIds.add(orderId);
            } catch (Exception e) {
                log.error("save order error", e);
            } finally {
                supplementSuccessHandler(orderId);
            }
        }

        OrderSupplementInfoResponse response = new OrderSupplementInfoResponse();
        response.setOrderIds(orderIds);
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long saveOrder(HotelOrderSupplementInfo supplementInfo) {
        // 保存订单
        hoOrderLoader.insertSelective(convertToHoOrder(supplementInfo.getOrder()));
        // 保存酒店信息
        hoHotelLoader.insertSelective(convertToHoHotel(supplementInfo.getHotel()));
        // 保存房间信息
        hoRoomLoader.insertSelective(convertToHoRoom(supplementInfo.getRoom()));
        // 保存房间日信息
        convertToHoRoomDailyInfoList(supplementInfo.getRoomDailyList()).forEach(hoRoomDailyInfoLoader::insertSelective);
        // 保存乘客信息
        convertToHoPassenger(supplementInfo.getPassengers()).forEach(hoPassengerLoader::insertSelective);
        // 创建支付单&退款单
        createPaymentBillAndRefund(supplementInfo.getOrder());
        return supplementInfo.getOrder().getOrderId();
    }

    private void createPaymentBillAndRefund(HotelOrderSupplementInfo.Order order) {
        CreatePaymentBillRequest createPaymentBillRequest = new CreatePaymentBillRequest();
        createPaymentBillRequest.setAmount(order.getAmount());
        createPaymentBillRequest.setOrderId(order.getOrderId());
        createPaymentBillRequest.setUid(order.getUid());
        createPaymentBillRequest.setUserName(order.getUname());
        createPaymentBillRequest.setOrderType(OrderTypeEnum.HN.getType());
        createPaymentBillRequest.setCorpPayType(ExpenseTypeEnum.PUB.getCode());
        createPaymentBillRequest.setPayType(order.getPayType());
        createPaymentBillRequest.setRefundFlag(false);
        Long billId = payClientLoader.supplementPaymentBill(createPaymentBillRequest);
        createPaymentRefund(billId, order);
    }

    private void createPaymentRefund(Long billId, HotelOrderSupplementInfo.Order order){
        if (!OrderStatusEnum.CA.getCode().equals(order.getOrderStatus())){
            log.info("订单未取消 无需补录退款单!");
            return;
        }

        BigDecimal cancelFee = order.getCancelFee();
        if (null == cancelFee || BigDecimal.ZERO.compareTo(cancelFee) == 0){
            log.info("取消费为空！");
            return;
        }

        CreateDirectRefundOrderRequest refundRequest = new CreateDirectRefundOrderRequest();
        refundRequest.setAmount(order.getAmount().subtract(cancelFee));
        refundRequest.setRealRefundAmount(refundRequest.getAmount());
        refundRequest.setBillId(billId);
        refundRequest.setUid(order.getUid());
        refundRequest.setUserName(order.getUname());
        payClientLoader.createDirectRefundOrder(refundRequest);
    }

    /**
     * 订单补录成功处理
     * 
     * @param orderId
     */
    public void supplementSuccessHandler(Long orderId) {
        if (null == orderId) {
            log.error("order id is null");
            return;
        }

        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        if (null == hoOrder) {
            log.error("order is null:{}", orderId);
            return;
        }

        // ocs event
        eventCenter.post(
            new OrderCreateExternalEvent(ApplicationEnum.HOTEL_DOMESTIC.getCode(),
                orderId));
        // supplement event
        sendOrderSupplementEvent(hoOrder);
        // supplement event by tenants
        sendOrderSupplementEventTenants(hoOrder);
    }

    private void sendOrderSupplementEvent(HoOrder hoOrder) {
        try {
            OrderSupplementEvent event = new OrderSupplementEvent();
            event.setProductType(com.corpgovernment.common.enums.SiteEnum.HOTEL.getCode());
            event.setSupplierCode(hoOrder.getSupplierCode());
            event.setPlatformOrderId(hoOrder.getOrderId());
            event.setSupplierOrderIds(Lists.newArrayList(hoOrder.getSupplierOrderId()));
            event.setOrderStatus(hoOrder.getOrderStatus());
            event.setSupplierCorpId(hoOrder.getSupplierCorpId());
            log.info("国内酒店订单补录推送Event：" + JsonUtils.toJsonString(event));
            EnvironmentHolder.getBean(EventCenter.class).post(event);
        } catch (Exception e) {
            log.error("sendOrderSupplementEvent error", e);
        }
    }


    private void sendOrderSupplementEventTenants(HoOrder hoOrder) {
        //发布事件推送
        String hotelSupplementOrderEventPushTenants = hotelApollo.getAppProperty("hotel.supplement-order.event.push.tenants", "");
        log.info("获取hotel.supplement-order.event.push.tenants配置：" + hotelSupplementOrderEventPushTenants);

        if (StringUtils.isBlank(hotelSupplementOrderEventPushTenants)){
            return;
        }
        String[] eventPushTenants = hotelSupplementOrderEventPushTenants.split(",");
        boolean match = Arrays.stream(eventPushTenants).anyMatch(item -> item.equals(TenantContext.getTenantId()));

        if (match) {
            OrderEvent event = new OrderEvent();
            event.setOrderId(String.valueOf(hoOrder.getOrderId()));
            event.setProductType(HOTEL_DOMESTIC);
            event.setStatus(OrderStatusEnum.ED.getName());
            event.setTripApplyNo(hoOrder.getTripApplyNo());
            event.setCorpId(hoOrder.getCorpId());
            event.setFeeType(ExpenseTypeEnum.PUB.getCode());
            event.setSendDate(DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
            event.setBusinessType(BusinessTypeEnum.ORDER.getBusinessType());
            log.info("事件推送event：" + JsonUtils.toJsonString(event));
            EnvironmentHolder.getBean(EventCenter.class).post(event);
        }
    }


    private List<HoPassenger> convertToHoPassenger(List<HotelOrderSupplementInfo.Passenger> passengers) {

        return CollectionUtils.emptyIfNull(passengers).stream().map(passenger -> {
            HoPassenger hoPassenger = new HoPassenger();
            hoPassenger.setOrderId(passenger.getOrderId());
            hoPassenger.setPassengerName(passenger.getPassengerName());
            hoPassenger.setUid(passenger.getUid());
            hoPassenger.setNoEmployeeId(passenger.getNoEmployeeId());
            hoPassenger.setBirthday(passenger.getBirthday());
            hoPassenger.setGender(passenger.getGender());
            hoPassenger.setMobilePhone(passenger.getMobilePhone());
            hoPassenger.setIsSendSms(false);
            hoPassenger.setCountryCode(passenger.getCountryCode());
            hoPassenger.setCostCenterCode(passenger.getCostCenterCode());
            hoPassenger.setCostCenterName(passenger.getCostCenterName());
            hoPassenger.setDepartmentId(passenger.getDepartmentId());
            hoPassenger.setDepartmentName(passenger.getDepartmentName());
            hoPassenger.setOrgId(passenger.getOrgId());
            hoPassenger.setOrgName(passenger.getOrgName());
            hoPassenger.setProjectId(passenger.getProjectId());
            hoPassenger.setProjectCode(passenger.getProjectCode());
            hoPassenger.setProjectName(passenger.getProjectName());
            hoPassenger.setCostCenterId(passenger.getCostCenterId());
            hoPassenger.setWbsRemark(passenger.getWbsRemark());
            hoPassenger.setCostCenterRemark(passenger.getCostCenterRemark());
            hoPassenger.setEmployeeType(passenger.getEmployeeType());
            hoPassenger.setTravelerName(passenger.getPassengerName());
            // 补充成本中心所属法人信息
            hoPassenger.setCostCenterLegalEntityCode(passenger.getCostCenterLegalEntityCode());
            hoPassenger.setCostCenterLegalEntityName(passenger.getCostCenterLegalEntityName());
            hoPassenger.setSupplierAccountId(passenger.getCostCenterLegalEntitySupplierAccountId());
            if(CollectionUtils.isNotEmpty(passenger.getAccountingUnitInfoList())){
                String dataId = hoDataStorageLoader.insertDataStorage(DataStorageTypeEnum.ACCOUNTING_UNIT, JsonUtils.toJsonString(passenger.getAccountingUnitInfoList()));
                hoPassenger.setAccountingUnitDataId(dataId);
            }
            return hoPassenger;
        }).collect(Collectors.toList());
    }



    private List<HoRoomDailyInfo> convertToHoRoomDailyInfoList(List<HotelOrderSupplementInfo.RoomDaily> roomDailies) {
        return CollectionUtils.emptyIfNull(roomDailies).stream().map(roomDaily -> {
            HoRoomDailyInfo hoRoomDailyInfo = new HoRoomDailyInfo();
            hoRoomDailyInfo.setOrderId(roomDaily.getOrderId());
            hoRoomDailyInfo.setEffectDate(roomDaily.getEffectDate());
            hoRoomDailyInfo.setRoomPrice(roomDaily.getRoomPrice());
            hoRoomDailyInfo.setBreakfast(roomDaily.getBreakfast());
            return hoRoomDailyInfo;
        }).collect(Collectors.toList());
    }

    private HoRoom convertToHoRoom(HotelOrderSupplementInfo.Room room) {
        HoRoom hoRoom = new HoRoom();
        hoRoom.setOrderId(room.getOrderId());
        hoRoom.setRoomId(room.getRoomId());
        hoRoom.setRoomName(StringUtils.isNotBlank(room.getRoomName()) ? room.getRoomName() : DEFAULT_ROOM_NAME);
        hoRoom.setCheckInDate(room.getCheckInDate());
        hoRoom.setCheckOutDate(room.getCheckOutDate());
        hoRoom.setNextDay(room.getNextDay());
        hoRoom.setPersonCount(room.getPersonCount());
        hoRoom.setRoomQuantity(room.getRoomQuantity());
        hoRoom.setBedName(room.getBedName());
        return hoRoom;
    }

    private HoHotel convertToHoHotel(HotelOrderSupplementInfo.Hotel hotel) {
        HoHotel hoHotel = new HoHotel();
        hoHotel.setOrderId(hotel.getOrderId());
        hoHotel.setHotelName(hotel.getHotelName());
        hoHotel.setHotelType(hotel.getHotelType());
        hoHotel.setHotelPhone(hotel.getHotelPhone());
        hoHotel.setIsStarLicence(hotel.getIsStarLicence());
        hoHotel.setStar(hotel.getStar());
        hoHotel.setCityId(hotel.getCityId());
        hoHotel.setCityName(hotel.getCityName());
        hoHotel.setAddress(hotel.getAddress());
        hoHotel.setVatFlag(hotel.getVatFlag());
        return hoHotel;
    }

    private HoOrder convertToHoOrder(HotelOrderSupplementInfo.Order order) {
        HoOrder hoOrder = new HoOrder();
        hoOrder.setOrderId(order.getOrderId());
        hoOrder.setSupplierOrderId(order.getSupplierOrderId());
        hoOrder.setSupplierUid(order.getSupplierUid());
        hoOrder.setSupplierCorpId(order.getSupplierCorpId());
        hoOrder.setSupplierCode(order.getSupplierCode());
        hoOrder.setSupplierName(order.getSupplierName());
        hoOrder.setSupplierPhone(order.getSupplierPhone());
        hoOrder.setUid(order.getUid());
        hoOrder.setUname(order.getUname());
        hoOrder.setDeptId(order.getDeptId());
        hoOrder.setCorpId(order.getCorpId());
        hoOrder.setOrgId(order.getOrgId());
        hoOrder.setSource(order.getSource());
        hoOrder.setOrderDate(order.getOrderDate());
        hoOrder.setOrderStatus(order.getOrderStatus());
        hoOrder.setAmount(order.getAmount());
        hoOrder.setPostServiceFee(order.getPostServiceFee());
        hoOrder.setDeliveryPrice(order.getDeliveryPrice());
        hoOrder.setCorpPayType(order.getCorpPayType());
        hoOrder.setPaytype(order.getPayType());
        hoOrder.setContactName(order.getContactName());
        hoOrder.setContactMobilePhone(order.getContactMobilePhone());
        hoOrder.setContactCountryCode(order.getContactCountryCode());
        hoOrder.setTripApplyNo(order.getTripApplyNo());
        hoOrder.setServiceFee(order.getServiceFee());
        hoOrder.setRcType(RC_TYPE);
        hoOrder.setSupplierAccountId(order.getSupplierAccountId());
        hoOrder.setRoomNightAvgPrice(order.getRoomNightAvgPrice());
        hoOrder.setRefundAmount(order.getRefundAmount());
        hoOrder.setCancelFee(order.getCancelFee());
        hoOrder.setCommissionFee(order.getCommissionFee());
        hoOrder.setOtherFee(order.getOtherFee());
        hoOrder.setDeliveryType(PayTypeEnum.ACCNT.getType().equals(order.getPayType())
                ? DeliveryTypeEnum.CJS.getCode() : DeliveryTypeEnum.PJN.getCode());
        hoOrder.setActualCheckInTime(buildDate(order.getActualCheckInTime(), DateUtil.DF_YMD_HMS));
        hoOrder.setActualCheckOutTime(buildDate(order.getActualDepartureTime(), DateUtil.DF_YMD_HMS));
        hoOrder.setAPayAmount(order.getSettlementPersonAmt());
        hoOrder.setPPayAmount(order.getSettlementACCNTAmt());
        return hoOrder;
    }

    private BigDecimal buildBigDecimal(String distance) {
        if (StringUtils.isNotBlank(distance)){
            return new BigDecimal(distance);
        }
        return null;
    }


    private Date buildDate(String dispatchedTime, String pattern) {
        if (StringUtils.isBlank(dispatchedTime)){
            return null;
        }
        return DateUtil.stringToDate(dispatchedTime, pattern);
    }
}
