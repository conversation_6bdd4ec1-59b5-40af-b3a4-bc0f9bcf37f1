package com.corpgovernment.hotel.booking.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplyTripControlBo {

    private Integer star;
    private BigDecimal avgAmount;
    private BigDecimal totalAmount;
    private Integer hotelDayCount;
    private List<String> wantCheckItemCodeList;

}
