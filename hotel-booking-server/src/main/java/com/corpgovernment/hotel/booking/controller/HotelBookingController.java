package com.corpgovernment.hotel.booking.controller;

import com.corpgovernment.api.hotel.booking.checkavail.request.CheckAvailRequestVo;
import com.corpgovernment.api.hotel.booking.checkavail.response.CheckAvailResponseVo;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo;
import com.corpgovernment.api.hotel.booking.checkorder.response.CheckOrderResponseVo;
import com.corpgovernment.api.hotel.booking.initpage.request.InitOrderRequestVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo;
import com.corpgovernment.api.hotel.booking.orderdetail.request.CancelOrderRequestDto;
import com.corpgovernment.api.hotel.booking.orderdetail.request.CancelOrderRequestVo;
import com.corpgovernment.api.hotel.booking.orderdetail.request.ClockOrderRequestVo;
import com.corpgovernment.api.hotel.booking.orderdetail.request.QueryOrderDetailRequestDto;
import com.corpgovernment.api.hotel.booking.orderdetail.request.QueryOrderDetailRequestVo;
import com.corpgovernment.api.hotel.booking.orderdetail.request.SaveSpecialNeedRequestVo;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderInitVo;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderResponseVo;
import com.corpgovernment.api.hotel.booking.orderdetail.response.OrderDetailManageResponseVo;
import com.corpgovernment.api.hotel.booking.orderdetail.response.OrderDetailResponseVo;
import com.corpgovernment.api.hotel.booking.orderstatus.request.PayCancelOrderRequestVo;
import com.corpgovernment.api.hotel.booking.orderstatus.request.PayGetOrderStatusRequestVo;
import com.corpgovernment.api.hotel.booking.orderstatus.response.CancelOrderDetailResponseVo;
import com.corpgovernment.api.hotel.booking.orderstatus.response.PayCancelOrderResponseVo;
import com.corpgovernment.api.hotel.booking.orderstatus.response.PayGetOrderStatusResponseVo;
import com.corpgovernment.api.hotel.booking.saveorder.request.SaveOrderRequestVo;
import com.corpgovernment.api.hotel.booking.saveorder.response.SaveOrderResponseVo;
import com.corpgovernment.api.hotel.product.model.request.OrderStatusPushRequest;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.FlightCancelTypeEnum;
import com.corpgovernment.hotel.booking.bo.OrderInfoBo;
import com.corpgovernment.hotel.booking.metric.MetricService;
import com.corpgovernment.hotel.booking.request.CheckDuplicateBookingRequest;
import com.corpgovernment.hotel.booking.service.CancelOrderService;
import com.corpgovernment.hotel.booking.service.CheckAvailService;
import com.corpgovernment.hotel.booking.service.CheckOrderService;
import com.corpgovernment.hotel.booking.service.ClockOrderService;
import com.corpgovernment.hotel.booking.service.InitOrderService;
import com.corpgovernment.hotel.booking.service.OrderDetailService;
import com.corpgovernment.hotel.booking.service.OrderStatusService;
import com.corpgovernment.hotel.booking.service.SaveOrderService;
import com.corpgovernment.hotel.booking.vo.CheckDuplicateBookingResponse;
import com.corpgovernment.hotel.booking.vo.GetSaveOrderResultRequestVO;
import com.corpgovernment.hotel.booking.vo.GetSaveOrderResultResponseVO;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.service.HotelPushService;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("booking")
@ApiOperation("国内酒店预定流程控制类")
public class HotelBookingController {

    @Autowired
    private CheckOrderService checkOrderService;
    @Autowired
    private CheckAvailService checkAvailService;
    @Autowired
    private InitOrderService initOrderService;
    @Autowired
    private OrderStatusService orderStatusService;
    @Autowired
    private OrderDetailService orderDetailService;
    @Autowired
    private CancelOrderService cancelOrderService;
    @Autowired
    private SaveOrderService saveOrderService;
    @Autowired
    private HotelPushService hotelPushService;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private MetricService metricService;
    @Autowired
    private ClockOrderService clockOrderService;

    @RequestMapping(value = "/initOrder")
    @BusinessBehaviorMonitor
    public JSONResult<InitOrderResponseVo> initOrder(BaseUserInfo userInfo,
        @RequestBody @Valid InitOrderRequestVo request) {
        request.setBaseUserInfo(userInfo);
        return JSONResult.success(initOrderService.initOrder(request));
    }

    @RequestMapping(value = "/checkAvail")
    @BusinessBehaviorMonitor
    public JSONResult<CheckAvailResponseVo> checkAvail(BaseUserInfo userInfo,
        @Validated @RequestBody CheckAvailRequestVo request) {
        request.setBaseUserInfo(userInfo);
        return JSONResult.success(checkAvailService.checkAvail(request));
    }

    @RequestMapping(value = "/checkAvailV2")
    @BusinessBehaviorMonitor
    public JSONResult<CheckAvailResponseVo> checkAvailV2(BaseUserInfo userInfo,
                                                       @Validated @RequestBody CheckAvailRequestVo request) {
        request.setBaseUserInfo(userInfo);
        return JSONResult.success(checkAvailService.checkAvail(request));
    }


    /**
     * 重复预订校验
     * 返回校验结果，提示用户，由用户决定是否继续预定
     */
    @PostMapping(value = "/checkDuplicateBooking")
    @BusinessBehaviorMonitor
    public JSONResult<CheckDuplicateBookingResponse> checkDuplicateBooking(@RequestBody CheckDuplicateBookingRequest request) {
        return JSONResult.success(checkOrderService.checkDuplicateBookingV3(request));
    }

    @RequestMapping(value = "/checkOrder")
    @BusinessBehaviorMonitor
    public JSONResult<CheckOrderResponseVo> checkOrder(BaseUserInfo userInfo,
        @RequestBody CheckOrderRequestVo request) {
        request.setBaseUserInfo(userInfo);
        return JSONResult.success(checkOrderService.checkOrder(request));
    }

    /**
     * 根据预订流程token检查酒店订单
     *
     * @param userInfo 用户基础信息
     * @param request  订单检查请求参数
     * @return 包含订单检查结果的JSON响应
     */
    @RequestMapping(value = "/checkOrderToken")
    @BusinessBehaviorMonitor
    public JSONResult<CheckOrderResponseVo> checkOrderToken(BaseUserInfo userInfo,
                                                            @RequestBody CheckOrderRequestVo request) {
        request.setBaseUserInfo(userInfo);
        return JSONResult.success(checkOrderService.checkOrderPlus(request));
    }

    /**
     * 下单接口
     * 
     * @param userInfo
     * @param request
     * @return
     */
    @RequestMapping(value = "/saveOrder")
    public JSONResult<SaveOrderResponseVo> saveOrder(BaseUserInfo userInfo, @RequestBody SaveOrderRequestVo request) {
        request.setBaseUserInfo(userInfo);
        return metricService
            .execAndMetricSaveOrder(metricContext -> saveOrderService.saveOrder(request, metricContext));
    }

    /**
     * 轮询获取saveOrder接口结果
     *
     * @param request
     * @return
     */
    @PostMapping("/getSaveOrderResult")
    @ApiOperation("轮询获取下单接口结果")
    public JSONResult<GetSaveOrderResultResponseVO>
        getSaveOrderResult(@RequestBody GetSaveOrderResultRequestVO request) {
        return JSONResult.success(saveOrderService.getSaveOrderResult(request));
    }

    @RequestMapping(value = "/orderDetail")
    public JSONResult<OrderDetailResponseVo> orderDetail(
        @RequestBody QueryOrderDetailRequestVo queryOrderDetailRequestVo, @RequestParam("uid") String uid,
        @RequestParam("source") String source) {
        if (StringUtils.isBlank(uid)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.USER_NOT_LOGGER);
        }
        QueryOrderDetailRequestDto queryOrderDetailRequestDto = new QueryOrderDetailRequestDto();
        queryOrderDetailRequestDto.setOrderId(queryOrderDetailRequestVo.getOrderId());
        queryOrderDetailRequestDto.setUid(uid);
        queryOrderDetailRequestDto.setSource(source);
        queryOrderDetailRequestDto.setTaskId(queryOrderDetailRequestVo.getTaskId());
        return JSONResult.success(orderDetailService.queryOrderDetail(queryOrderDetailRequestDto));
    }

    @RequestMapping(value = "/orderDetailForManage")
    public JSONResult<OrderDetailManageResponseVo> orderDetailForManage(
        @RequestBody QueryOrderDetailRequestVo queryOrderDetailRequestVo, @RequestParam("uid") String uid) {
        QueryOrderDetailRequestDto queryOrderDetailRequestDto = new QueryOrderDetailRequestDto();
        queryOrderDetailRequestDto.setOrderId(queryOrderDetailRequestVo.getOrderId());
        queryOrderDetailRequestDto.setUid(uid);
        return JSONResult.success(orderDetailService.queryOrderDetailForManage(queryOrderDetailRequestDto, false));
    }

    @RequestMapping(value = "/cancelOrder")
    public JSONResult<CancelOrderResponseVo> cancelOrder(@RequestBody CancelOrderRequestVo cancelOrderRequestVo,
        @RequestParam("uid") String uid) {
        CancelOrderRequestDto cancelOrderRequestDto = new CancelOrderRequestDto();
        cancelOrderRequestDto.setCancelReason(cancelOrderRequestVo.getCancelReason());
        cancelOrderRequestDto.setOrderId(cancelOrderRequestVo.getOrderId());
        cancelOrderRequestDto.setApplyCancelFlag(cancelOrderRequestVo.getApplyCancelFlag());

        String cancelSource = StringUtils.isEmpty(cancelOrderRequestVo.getCancelSource()) ? FlightCancelTypeEnum.A.getCode() : cancelOrderRequestVo.getCancelSource();
        cancelOrderRequestDto.setCancelSource(cancelSource);

        cancelOrderRequestDto.setForceCancel(cancelOrderRequestVo.getForceCancel());
        CancelOrderResponseVo cancelOrderResponseVo = cancelOrderService.cancelOrder(cancelOrderRequestDto);
        metricService.metricCancelOrder(cancelOrderResponseVo);
        return JSONResult.success(cancelOrderResponseVo);
    }

    @RequestMapping(value = "/saveSpecialNeed")
    public JSONResult saveSpecialNeed(@RequestBody SaveSpecialNeedRequestVo saveSpecialNeedRequestVo,
        @RequestParam("uid") String uid) {
        return orderDetailService.saveSpecialNeed(saveSpecialNeedRequestVo);
    }

    @RequestMapping(value = "/cancelOrderInit")
    public JSONResult cancelOrderInit(@RequestBody CancelOrderRequestVo cancelOrderRequestVo,
        @RequestParam("uid") String uid) {
        CancelOrderRequestDto cancelOrderRequestDto = new CancelOrderRequestDto();
        cancelOrderRequestDto.setOrderId(cancelOrderRequestVo.getOrderId());
        CancelOrderInitVo cancelOrderInitVo = cancelOrderService.cancelOrderInit(cancelOrderRequestDto);
        return JSONResult.success(cancelOrderInitVo);
    }

    @RequestMapping(value = "/handCancel")
    public JSONResult handCancel(@RequestBody CancelOrderRequestVo cancelOrderRequestVo) {
        OrderStatusPushRequest orderStatusPushRequest = new OrderStatusPushRequest();
        orderStatusPushRequest.setOrderId(String.valueOf(cancelOrderRequestVo.getOrderId()));
        CancelOrderResponseVo cancelOrderResponseVo = new CancelOrderResponseVo();
        HoOrder order = hoOrderLoader.selectByOrderId(Long.valueOf(orderStatusPushRequest.getOrderId()));
        if (order == null) {
            return JSONResult.errorMsg("订单不存在");
        }
        orderStatusPushRequest.setCorpId(order.getSupplierCorpId());
        orderStatusPushRequest.setSupplierCode(order.getSupplierCode());
        orderStatusPushRequest.setSupplierOrderId(order.getSupplierOrderId());
        orderStatusPushRequest.setCancelType("HAND");
        JSONResult result = hotelPushService.cancel(orderStatusPushRequest);
        cancelOrderResponseVo.setErrorCode(result.isSUCCESS() ? "0" : "-1");
        cancelOrderResponseVo.setSuccess(result.isSUCCESS());
        cancelOrderResponseVo.setMsg(result.getMsg());
        return JSONResult.success(cancelOrderRequestVo);

    }

    /**
     * 支付获取订单状态
     */
    @RequestMapping(value = "/payGetOrderStatus")
    public JSONResult<PayGetOrderStatusResponseVo> payGetOrderStatus(@RequestBody PayGetOrderStatusRequestVo request) {
        return JSONResult.success(orderStatusService.payGetOrderStatus(request));
    }

    /**
     * 支付取消订单
     */
    @RequestMapping(value = "/payCancelOrder")
    public JSONResult<PayCancelOrderResponseVo> payCancelOrder(@RequestBody PayCancelOrderRequestVo request) {
        return JSONResult.success(orderStatusService.payCancelOrder(request));
    }

    @RequestMapping(value = "/cancelOrderDetail")
    public JSONResult<CancelOrderDetailResponseVo>
        cancelOrderDetail(@RequestBody QueryOrderDetailRequestVo queryOrderDetailRequestVo) {
        return JSONResult.success(cancelOrderService.cancelOrderDetail(queryOrderDetailRequestVo.getOrderId()));
    }

    /**
     * 签到打卡
     */
    @PostMapping(value = "/clock")
    public JSONResult<Void> clock(@Valid @RequestBody ClockOrderRequestVo request) {
        clockOrderService.clock(request);
        return JSONResult.ok();
    }
}
