package com.corpgovernment.hotel.booking.enums;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public enum PpPayOrderStatusEnum {
    SI("SI","提交中",1),
    AW("AW","待审批",7),
    PW("PW","待支付",2),
    TW("TW","待确认",3),
    TA("TA","已确认",4),
    CA("CA","已取消",5),
    ED("ED","已完成",6);
    private String code;
    private String name;
    private Integer node;

    PpPayOrderStatusEnum(String code, String name, Integer node) {
        this.code = code;
        this.name = name;
        this.node = node;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getNode() {
        return node;
    }

    public void setNode(Integer node) {
        this.node = node;
    }

    /**
     * 根据code转换枚举
     */
    public static PpPayOrderStatusEnum getEnum(String code) {
        for (PpPayOrderStatusEnum item : PpPayOrderStatusEnum.values()) {
            if (item.getCode().equalsIgnoreCase(code)) {
                return item;
            }
        }
        return  null;
    }
    public static List<String> getList(String orderStatus){
        List<String> list = new ArrayList<>();
        for (PpPayOrderStatusEnum item : PpPayOrderStatusEnum.values()) {
            if(Objects.equals(orderStatus, "CA") && Objects.equals(item.getCode(), "ED")) {
                continue;
            }
                list.add(item.getName());
        }
        return list;
    }
}
