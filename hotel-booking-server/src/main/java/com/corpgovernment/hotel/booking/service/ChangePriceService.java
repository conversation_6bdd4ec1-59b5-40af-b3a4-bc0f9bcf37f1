package com.corpgovernment.hotel.booking.service;

import cn.hutool.core.util.ObjectUtil;
import com.corpgovernment.api.hotel.booking.initpage.request.InitOrderRequestVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.travelstandard.vo.AveragePriceSet;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.api.travelstandard.vo.request.GetHotelDetailRequest;
import com.corpgovernment.common.base.BaseRequestVO;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.hotel.booking.bo.BookingCheckResultBo;
import com.corpgovernment.hotel.booking.bo.VerifyTravelStandardBo;
import com.corpgovernment.hotel.booking.cache.model.HotelInfoModel;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.TravelStandardPostClientLoader;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.Conditional;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.HOTEL_CHECK_AVAIL_PRICE_EMPTY;

/**
 * <AUTHOR>
 * @date 2024/1/17
 */
@Component
@Slf4j
public class ChangePriceService {
    @Autowired
    private TravelStandardPostClientLoader travelStandardPostClientLoader;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    /**
     * 变价提示
     */
    private static final String CHANGE_PRICE_TIPS = "供应商该房型房费每间夜{0}约¥{1}，请更换房型重新预订";

    private static final String UP = "上调";

    private static final String DOWN = "下调";


    /**
     * null为未发生变价
     * @param orderInfo
     * @param userInfo
     * @param hotelInfo
     * @param checkAvailResponseBo
     * @param originalTotalPrice
     * @param presentTotalPrice
     * @return
     */
    public BookingCheckResultBo handledChangePrice(Boolean verifyTravelStandard,String feeType, OrderInfoModel orderInfo, BaseRequestVO.UserInfo userInfo, HotelInfoModel hotelInfo, LocalCheckAvailResponseBo checkAvailResponseBo, BigDecimal originalTotalPrice, BigDecimal presentTotalPrice) {
        boolean isChange = checkAvailResponseBo.getChangePrice() != null && checkAvailResponseBo.getChangePrice();
        //变价
        if (isChange) {
            //计算前后是否有超标
            Boolean originalRc = Boolean.FALSE;
            Boolean presentRc = Boolean.FALSE;
            //处理变价
            if (Objects.equals(CorpPayTypeEnum.PUB.getType(), feeType)) {
                String uid = StringUtils.isEmpty(hotelInfo.getPolicyId()) ? userInfo.getUid() : hotelInfo.getPolicyId();
                String orgId = StringUtils.isEmpty(hotelInfo.getPolicyOrgId()) ? userInfo.getOrgId() : hotelInfo.getPolicyOrgId();
                HotelControlVo priceLevel = this.getPriceLevel(uid, orgId, String.valueOf(hotelInfo.getCityId()), hotelInfo.getCheckInDate(), hotelInfo.getCheckOutDate());

                if (priceLevel != null) {
                    // 价格上限
                    BigDecimal priceCeiling = Optional.ofNullable(priceLevel.getAveragePriceSet()).map(AveragePriceSet::getPriceCeiling).map(BigDecimal::new).orElse(BigDecimal.ZERO);
                    // 价格下限
                    BigDecimal priceFloor = Optional.ofNullable(priceLevel.getAveragePriceSet()).map(AveragePriceSet::getPriceFloor).map(BigDecimal::new).orElse(BigDecimal.ZERO);
                    // 向上取整，保留0位
                    BigDecimal originalDivide = originalTotalPrice.divide(BigDecimal.valueOf(hotelInfo.getRoomDailyInfoList().size()), 0, RoundingMode.UP);
                    if (originalDivide.compareTo(priceCeiling) > 0 || originalDivide.compareTo(priceFloor) < 0) {
                        originalRc = Boolean.TRUE;
                    }
                    // 调用差标接口校验的结果
                    if(ObjectUtil.isNotNull(verifyTravelStandard) && verifyTravelStandard){
                        log.info("调用差标接口校验的结果,verifyTravelStandard:{}",verifyTravelStandard);
                        presentRc = verifyTravelStandard;
                    }else {
                        // 向上取整，保留0位
                        BigDecimal presentDivide = presentTotalPrice.divide(BigDecimal.valueOf(checkAvailResponseBo.getRoomDailyInfoList().size()), 0, RoundingMode.UP);
                        if (presentDivide.compareTo(priceCeiling) > 0 || presentDivide.compareTo(priceFloor) < 0) {
                            presentRc = Boolean.TRUE;
                        }
                        log.info("向上取整，保留0位,presentDivide:{},presentRc:{}",presentDivide,presentRc);
                    }
                }
            }
            if (CollectionUtils.isEmpty(checkAvailResponseBo.getChangePriceDetailList())){
                throw new CorpBusinessException(HOTEL_CHECK_AVAIL_PRICE_EMPTY);
            }
            Map<String, HotelInfoModel.RoomDailyInfo> roomDailyInfoMap = hotelInfo.getRoomDailyInfoList().stream().collect(Collectors.toMap(t->t.getEffectDate().substring(0,10), Function.identity(), (t1, t2)->t2));
            BigDecimal price = BigDecimal.ZERO;
            for (LocalCheckAvailResponseBo.ChangePriceDetailInfo changePriceDetailInfo:
            checkAvailResponseBo.getChangePriceDetailList()) {
                price = price.add(changePriceDetailInfo.getPrice().subtract(roomDailyInfoMap.get(changePriceDetailInfo.getDate()).getRoomPrice()));
            }
            BigDecimal changeAveragePrice = price.divide(BigDecimal.valueOf(roomDailyInfoMap.size()), 0, RoundingMode.HALF_UP);

            BookingCheckResultBo checkResult = new BookingCheckResultBo();
            //1、填写页可订变价时，若变动前/后价格都未命中“RC原因/禁止预订” 均价差标（命中淡旺季时，读取淡旺季均价），
            // 则弹框提醒“抱歉，酒店价格发生变动，价格由￥xx变动￥xx”，点击“重新选择”，返回酒店详情页并重新获取该酒店价格，点击“ 继续预订” 更新价继续预订。
            if (!originalRc && !presentRc) {
                checkResult.setErrorCode(4);
                checkResult.setErrorMsg(MessageFormat.format(CHANGE_PRICE_TIPS, getPriceChangeInfo(changeAveragePrice), changeAveragePrice.abs()));
                orderInfo.setRcFlag(Boolean.TRUE);
                checkResult.setHitRc(Boolean.FALSE);
                return checkResult;
            }
            //2、填写页可订变价时，若变动前价格已命中“RC原因” 均价差标（命中淡旺季时，读取淡旺季均价），变动后价格命中，则弹框提醒“抱歉，酒店价格发生变动，价格由￥xx变动￥xx”，点击“重新选择”，
            // 返回酒店详情页并重新获取该酒店价格，点击“ 继续预订” 更新价继续预订。
            // 若价格变低未命中时，废除已选RC，相关数据不落库。
            if (originalRc && presentRc) {
                checkResult.setErrorCode(4);
                checkResult.setErrorMsg(MessageFormat.format(CHANGE_PRICE_TIPS, getPriceChangeInfo(changeAveragePrice), changeAveragePrice.abs()));
                orderInfo.setRcFlag(Boolean.FALSE);
                checkResult.setHitRc(Boolean.TRUE);
                return checkResult;
            }
            //2.1若价格变低未命中时，废除已选RC，相关数据不落库。
            if (originalRc && !presentRc) {
                checkResult.setErrorCode(4);
                checkResult.setErrorMsg(MessageFormat.format(CHANGE_PRICE_TIPS, getPriceChangeInfo(changeAveragePrice), changeAveragePrice.abs()));
                orderInfo.setRcFlag(Boolean.TRUE);
                checkResult.setHitRc(Boolean.FALSE);
                return checkResult;
            }
            //3、填写页可订变价时，若变动前价格都未命中“RC原因/禁止预订” 均价差标（命中淡旺季时，读取淡旺季均价），变动后价格命中，
            // 则弹框提醒“抱歉，酒店价格发生变动，价格由￥xx变动￥xx，已违反公司差旅标准”，点击“重新选择”，返回酒店详情页并重新获取该酒店价格。
            checkResult.setErrorCode(5);
            checkResult.setErrorMsg(MessageFormat.format(CHANGE_PRICE_TIPS, getPriceChangeInfo(changeAveragePrice), changeAveragePrice.abs()));
            orderInfo.setRcFlag(Boolean.FALSE);
            checkResult.setHitRc(Boolean.TRUE);
            return checkResult;
        }
        return null;
    }
    /**
     * 调用差标接口校验是否超标
     * @param serviceFeeInfo
     * @param requestVo
     * @return
     */
    public VerifyTravelStandardBo verifyTravelStandard(InitOrderResponseVo.ServiceFeeInfo serviceFeeInfo , InitOrderRequestVo requestVo,HotelInfoModel hotelInfo){
        VerifyTravelStandardRequest request = this.createVerifyTravelStandardRequest(serviceFeeInfo, requestVo,hotelInfo);
        // verifyResult 0 通过（不超标），1 超标且不可预订，2 超标条件可预订
        List<ResourcesVerifyResponse> responses = applyTripClientLoader.verifyTravelStandard(request);
        return this.buildVerifyTravelStandardBo(responses, requestVo);
    }

    /**
     * 构建VerifyTravelStandardBo对象
     * @param responses
     * @param requestVo
     * @return
     */
    private VerifyTravelStandardBo buildVerifyTravelStandardBo(List<ResourcesVerifyResponse> responses, InitOrderRequestVo requestVo) {
        VerifyTravelStandardBo verifyTravelStandardBo = new VerifyTravelStandardBo();
        if (CollectionUtils.isNotEmpty(responses)) {
            Optional<ResourcesVerifyResponse> matchingResponse = responses.stream()
                    .filter(r -> r.getResourcesId().equals(requestVo.getRoomKey()))
                    .findFirst();

            matchingResponse.ifPresent(response -> {
                verifyTravelStandardBo.setVerifyTravelStandard(ObjectUtil.isNotNull(response.getExceed()) && response.getExceed() != 0);
                verifyTravelStandardBo.setExceedAmount(response.getExceedAmount());
            });
        }
        log.info("调用差标接口校验是否超标, verifyTravelStandardBo: {}", JsonUtils.toJsonString(verifyTravelStandardBo));
        return verifyTravelStandardBo;
    }
    /**
     * 创建差标请求
     * @param serviceFeeInfo
     * @param requestVo
     * @return
     */
    private VerifyTravelStandardRequest createVerifyTravelStandardRequest(InitOrderResponseVo.ServiceFeeInfo serviceFeeInfo,
                                                                          InitOrderRequestVo requestVo,HotelInfoModel hotelInfo) {
        VerifyTravelStandardRequest request = new VerifyTravelStandardRequest();
        request.setTravelStandardToken(requestVo.getTravelStandardToken());
        request.setBizType("3"); // 国内酒店

        List<HotelVerifyRequest> hotelList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(requestVo.getBasicRoomInfo())){
            List<InitOrderRequestVo.BasicRoomInfo.ChildRoomsDTO> childRooms = requestVo.getBasicRoomInfo().getChildRooms();
            for (InitOrderRequestVo.BasicRoomInfo.ChildRoomsDTO room : childRooms) {
                hotelList.add(this.createHotelVerifyRequest(room, serviceFeeInfo, hotelInfo));
            }
        }
        request.setHotelList(hotelList);
        return request;
    }

    private HotelVerifyRequest createHotelVerifyRequest(InitOrderRequestVo.BasicRoomInfo.ChildRoomsDTO childRoom, InitOrderResponseVo.ServiceFeeInfo serviceFeeInfo,HotelInfoModel hotelInfo) {
        HotelVerifyRequest request = new HotelVerifyRequest();
        request.setResourcesId(childRoom.getRoomKey()); // roomkey作为资源id
        request.setPrice(childRoom.getPrice());
        if("meiya".equalsIgnoreCase(childRoom.getSupplierCode()) && ObjectUtil.isNotEmpty(serviceFeeInfo)){
            if("C".equalsIgnoreCase(childRoom.getRoomType())){ // C 为协议酒店,取协议酒店服务费
                request.setServiceFee(serviceFeeInfo.getPersonalPubPayServiceFee());
            }else {
                request.setServiceFee(serviceFeeInfo.getAccountPayServiceFee());
            }
        }
        request.setStar(hotelInfo.getStar());
        request.setStarLicence(hotelInfo.getIsStarLicence());
        request.setOldProcess(true);
        return request;
    }
    private  String getPriceChangeInfo(BigDecimal changeAveragePrice) {
        return changeAveragePrice.compareTo(BigDecimal.ZERO) > 0 ? UP : DOWN;
    }

    private HotelControlVo getPriceLevel(String uid, String orgId, String cityCode, String checkInDate, String checkOutDate) {
        GetHotelDetailRequest detailRequest = new GetHotelDetailRequest();
        detailRequest.setUid(uid);
        detailRequest.setOrgId(orgId);
        detailRequest.setCityCode(cityCode);
        detailRequest.setStartDate(DateUtil.stringToDate(checkInDate, "yyyy-MM-dd"));
        detailRequest.setEndDate(DateUtil.stringToDate(checkOutDate, "yyyy-MM-dd"));
        return travelStandardPostClientLoader.getHotelDetail(detailRequest);
    }
}
