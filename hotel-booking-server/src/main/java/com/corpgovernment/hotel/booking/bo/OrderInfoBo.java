package com.corpgovernment.hotel.booking.bo;

import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.hotel.product.entity.db.HoHotel;
import com.corpgovernment.hotel.product.entity.db.HoHotelApplyDetail;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.entity.db.HoRoom;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class OrderInfoBo {

    /**
     * 订单号
     **/
    private Long orderId;

    /**
     * 供应商简称
     **/
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;


    /**
     * 下单时间
     **/
    private Date createOrderTime;

    /**
     * 费用类型，PUB因公、OWN因私
     **/
    private String corpPayType;

    private String corpPayTypeDisplay;

    /**
     * 入住时间
     */
    private Date checkInDate;

    /**
     * 离店时间
     */
    private Date checkOutDate;

    /**
     * 房间数
     */
    private Integer roomCount;

    /**
     * 入住几晚
     */
    private Integer stayNight;

    /**
     * 房型名称
     */
    private String hotelName;

    /**
     * 城市
     */
    private String cityName;

    /**
     * 入住人（已拼接字符串）
     * 如：user1、user2
     */
    private List<String> passengers;

    /**
     * 标签 (修改中/取消中)
     */
    private String tag;

    /**
     * 单据处理后是否重复
     */
    private Boolean duplicateAfterFormProcessed;

    public static OrderInfoBo assembly(HoOrder order, List<String> passengers, HoHotel hotel, HoRoom room){
        if(order == null || CollectionUtils.isEmpty(passengers) || hotel == null || room == null){
            return null;
        }
        OrderInfoBo bo = new OrderInfoBo();
        bo.setOrderId(order.getOrderId());
        bo.setSupplierCode(order.getSupplierCode());
        bo.setSupplierName(order.getSupplierName());
        bo.setCreateOrderTime(order.getDatachangeCreatetime());
        bo.setCorpPayType(order.getCorpPayType());
        bo.setCorpPayTypeDisplay(CorpPayTypeEnum.getNameByType(order.getCorpPayType()));
        bo.setPassengers(passengers);
        bo.setCityName(hotel.getCityName());
        bo.setHotelName(hotel.getHotelName());
        bo.setCheckInDate(room.getCheckInDate());
        bo.setCheckOutDate(room.getCheckOutDate());
        bo.setRoomCount(room.getRoomQuantity());
        bo.setStayNight(room.getNextDay());
        return bo;
    }

    public static OrderInfoBo assembly(HoOrder order, List<String> passengers, HoHotel hotel, HoRoom room, HoHotelApplyDetail applyDetail){
        if(order == null || CollectionUtils.isEmpty(passengers) || hotel == null || room == null || applyDetail == null){
            return null;
        }
        OrderInfoBo bo = new OrderInfoBo();
        bo.setOrderId(order.getOrderId());
        bo.setSupplierCode(order.getSupplierCode());
        bo.setSupplierName(order.getSupplierName());
        bo.setCreateOrderTime(order.getDatachangeCreatetime());
        bo.setCorpPayType(order.getCorpPayType());
        bo.setHotelName(hotel.getHotelName());
        bo.setCityName(hotel.getCityName());
        bo.setCorpPayTypeDisplay(CorpPayTypeEnum.getNameByType(order.getCorpPayType()));
        bo.setPassengers(passengers);
        bo.setCheckInDate(applyDetail.getCheckInDate());
        bo.setCheckOutDate(applyDetail.getCheckOutDate());
        bo.setRoomCount(room.getRoomQuantity());
        bo.setStayNight(applyDetail.getRoomNight());
        return bo;
    }
}