package com.corpgovernment.hotel.booking.vo;

import com.corpgovernment.hotel.booking.bo.OrderInfoBo;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.Data;

import java.util.Collections;
import java.util.List;


@Data
public class CheckDuplicateBookingResponse {

    /**
     * 是否存在交叉订单
     */
    private boolean duplicate;

    /**
     * 交叉订单列表
     */
    private List<OrderInfoBo> orderInfoList;

    /**
     * 重复校验模式：onlyRemind（仅提醒）noReservation（禁止预定）
     * DuplicateCheckModeEnum
     */
    private String duplicateCheckMode;

    public static CheckDuplicateBookingResponse build(List<OrderInfoBo> bos){
        if(CollectionUtils.isEmpty(bos)){
            return buildEmpty();
        }
        CheckDuplicateBookingResponse response = new CheckDuplicateBookingResponse();
        response.duplicate = true;
        response.orderInfoList = bos;
        return response;
    }


    public static CheckDuplicateBookingResponse buildEmpty(){
        CheckDuplicateBookingResponse response = new CheckDuplicateBookingResponse();
        response.duplicate = false;
        response.orderInfoList = Collections.emptyList();
        return response;
    }

}
