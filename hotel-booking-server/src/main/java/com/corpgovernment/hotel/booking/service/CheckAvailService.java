package com.corpgovernment.hotel.booking.service;

import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficType;
import com.corpgovernment.api.applytrip.soa.response.ApplyTripTrafficVerifyResponse;
import com.corpgovernment.api.hotel.booking.checkavail.request.CheckAvailRequestVo;
import com.corpgovernment.api.hotel.booking.checkavail.response.CheckAvailResponseVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.RoomDailyInfo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.RoomItem;
import com.corpgovernment.basic.impl.HotelBasicDataService;
import com.corpgovernment.common.base.BaseRequestVO.UserInfo;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.common.enums.CancelPolicyEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.hotel.booking.bo.BookingCheckResultBo;
import com.corpgovernment.hotel.booking.cache.HotelInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.OrderInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.model.HotelInfoModel;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.booking.enums.CropPayTypeEnum;
import com.corpgovernment.hotel.product.service.CommonSupplierServiceNew;
import com.corpgovernment.mapping.enums.HotelStar;
import com.ctrip.corp.obt.generic.constants.GenericConstants;
import com.ctrip.corp.obt.generic.utils.Conditional;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.ObjectUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CheckAvailService extends BaseService {

    @Autowired
    private OrderInfoCacheManager orderInfoCacheManager;
    @Autowired
    private HotelInfoCacheManager hotelInfoCacheManager;
	@Autowired
	private HotelManager hotelManager;
	@Autowired
	private CommonSupplierServiceNew commonSupplierServiceNew;
	@Autowired
	private HotelBasicDataService hotelBasicDataService;
	@Autowired
	private InitOrderService initOrderService;
	@Autowired
	private ChangePriceService changePriceService;

	Id checkAvailId = Metrics.REGISTRY.createId("hotel.booking.checkAvail");

    public CheckAvailResponseVo checkAvail(CheckAvailRequestVo request) {
		try {
			initElkLog();
			addElkInfoLog("酒店可定查询");
			// 校验停留时间
			hotelManager.checkStateTime(false);
			UserInfo userInfo = request.getUserInfo();
			// 获取酒店房间信息缓存
			HotelInfoModel hotelInfo = hotelInfoCacheManager.getHotelInfo(request.getHotelId(), request.getRoomKey(), userInfo.getToken());
			addElkInfoLog("酒店缓存json-checkAvail" + JsonUtils.toJsonString(hotelInfo));

			CheckAvailResponseVo responseVo = new CheckAvailResponseVo();
//			// 校验 出差申请单是否符合标准 赋值校验超标数据
//			checkApplyTemp(request, hotelInfo, responseVo);
//			if(responseVo != null  && responseVo.getExceedApplyFieldList() != null
//					&& responseVo.getExceedApplyFieldList().size() > 0) {
//				responseVo.setErrorCode("1");
//				responseVo.setErrorMsg("申请单超标");
//				return responseVo;
//			}

			// 可订查询
			LocalCheckAvailRequestBo requestBo = toCheckAvailRequest(hotelInfo, request);
			LocalCheckAvailResponseBo checkAvailResponse = commonSupplierServiceNew.checkAvail(requestBo);
			addElkInfoLog("可订查询结果：" + JsonUtils.toJsonString(checkAvailResponse));

			if (Objects.isNull(checkAvailResponse) || StringUtils.isNotBlank(checkAvailResponse.getFailedReason()) ||
					Objects.isNull(checkAvailResponse.getRoomInfo()) || CollectionUtils.isEmpty(checkAvailResponse.getRoomDailyInfoList())){
				return CheckAvailResponseVo.failure(Optional.ofNullable(checkAvailResponse).map(LocalCheckAvailResponseBo::getCheckCode).orElse("1"), Optional.ofNullable(checkAvailResponse).
						map(LocalCheckAvailResponseBo::getFailedReason).orElse("该房型暂时不可预订，请稍后再试或预订其他房型"));
			}
			// 获取订单大缓存
			OrderInfoModel orderInfo =
					orderInfoCacheManager.getOrderInfo(request.getHotelId(), request.getRoomId(), userInfo.getToken());
			if (OrderInfoModel.isEmpty(orderInfo)){
				return CheckAvailResponseVo.failure("1", "酒店信息发生变化");
			}
			//原价总额
			BigDecimal originalTotalPrice = BigDecimal.ZERO;
			for (HotelInfoModel.RoomDailyInfo roomDailyInfo : hotelInfo.getRoomDailyInfoList()) {
				originalTotalPrice = originalTotalPrice.add(roomDailyInfo.getRoomPrice());
			}
			//现价
			BigDecimal presentTotalPrice = BigDecimal.ZERO;
			for (LocalCheckAvailResponseBo.RoomDailyInfo roomDailyInfo : checkAvailResponse.getRoomDailyInfoList()) {
				presentTotalPrice = presentTotalPrice.add(roomDailyInfo.getSellPrice());
			}
			BookingCheckResultBo checkResult = changePriceService.handledChangePrice(null,request.getExpenseType(),orderInfo,userInfo,hotelInfo,checkAvailResponse,originalTotalPrice,presentTotalPrice);
			InitOrderResponseVo.HotelRoomInfo hotelRoomInfo = initOrderService.toHotelRoomInfo(hotelInfo, checkAvailResponse);
			orderInfo.updateAfterCheckAvail(checkAvailResponse, hotelInfo, hotelRoomInfo);

			// 保存更新后的缓存
			orderInfoCacheManager.saveOrderInfo(orderInfo, request.getHotelId(),request.getRoomId(), userInfo.getToken());


			CheckAvailResponseVo checkAvailResponseVo = toCheckAvailResponse(checkAvailResponse, hotelInfo, responseVo,checkResult);
			setMetric(checkAvailResponseVo);
			return checkAvailResponseVo;
		} finally {
			log.info("CheckAvailService.checkAvail酒店可定查询提交{} request：{}{}{}{}", System.lineSeparator(), JsonUtils.toJsonString(request), System.lineSeparator(), System.lineSeparator(), this.getElkInfoLog());
			clearElkLog();
		}
    }

	/**
	 * 校验模板开启校验字段
	 * @param request
	 * @param hotelInfo
	 * @param responseVo
	 * @return
	 */
	private CheckAvailResponseVo checkApplyTemp(CheckAvailRequestVo request, HotelInfoModel hotelInfo, CheckAvailResponseVo responseVo) {
		if(StringUtil.equals(CropPayTypeEnum.PUB.getCode(), request.getExpenseType()) && request.getTrafficId() != null
		 && !StringUtil.equals(request.getPayInfoCode(), PayTypeEnum.PPAY.getType())) {
			// 查询申请单相关信息
			ApplyTripTrafficVerifyResponse trafficVerifyResponse = hotelBasicDataService.getApplyTripTrafficVerify(request.getTrafficId());
			log.info("关联申请单信息:", JsonUtils.toJsonString(trafficVerifyResponse));
			if(ObjectUtils.isNull(trafficVerifyResponse) || ObjectUtils.isNull(trafficVerifyResponse.getApplyTripTemp())
					|| ObjectUtils.isNull(trafficVerifyResponse.getApplyTripTraffic())) {
				log.info("申请单相关信息存在空");
				return responseVo;
			}
			List<CheckAvailResponseVo.FieldObject> applyOtherFieldList = new ArrayList<>();
			List<CheckAvailResponseVo.FieldObject> exceedApplyFieldList = new ArrayList<>();
			if(trafficVerifyResponse.getApplyTripTemp().getHotelProductType() != null
			        && trafficVerifyResponse.getApplyTripTemp().getHotelProductType() == 2) {
				if(!StringUtils.equalsIgnoreCase(trafficVerifyResponse.getApplyTripTraffic().getProductType(), "99")) {
					// 非不限
					if(trafficVerifyResponse.getApplyTripTraffic().getProductType() == null || hotelInfo.getStar() == null
							|| hotelInfo.getStar() > Integer.parseInt(trafficVerifyResponse.getApplyTripTraffic().getProductType())) {
						// 超标
						CheckAvailResponseVo.FieldObject exceedFieldObject = new CheckAvailResponseVo
								.FieldObject("productType", "酒店星级", HotelStar.getDisplayHotelStar(hotelInfo.getStar()));
						exceedApplyFieldList.add(exceedFieldObject);
					}
				}
				CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo.FieldObject();
				fieldObject.setKey("productTypeName");
				fieldObject.setLabel("酒店星级");
				fieldObject.setValue(trafficVerifyResponse.getApplyTripTraffic().getProductTypeName());
				applyOtherFieldList.add(fieldObject);
			} else if(trafficVerifyResponse.getApplyTripTemp().getHotelProductType() != null
					&& trafficVerifyResponse.getApplyTripTemp().getHotelProductType() == 1) {
				CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo.FieldObject();
				fieldObject.setKey("productTypeName");
				fieldObject.setLabel("酒店星级");
				fieldObject.setValue(trafficVerifyResponse.getApplyTripTraffic().getProductTypeName());
				applyOtherFieldList.add(fieldObject);
			}
			if(trafficVerifyResponse.getApplyTripTemp().getHotelAveragePrice() != null
			         && trafficVerifyResponse.getApplyTripTemp().getHotelAveragePrice() == 2){
				if(trafficVerifyResponse.getApplyTripTraffic().getAveragePrice() == null
						|| hotelInfo.getPrice().compareTo(trafficVerifyResponse.getApplyTripTraffic().getAveragePrice()) > 0) {
					// 超标
					CheckAvailResponseVo.FieldObject exceedFieldObject = new CheckAvailResponseVo
							.FieldObject("averagePrice", "间夜均价", "￥" + hotelInfo.getPrice());
					exceedApplyFieldList.add(exceedFieldObject);
				}
				CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo.FieldObject();
				fieldObject.setKey("averagePrice");
				fieldObject.setLabel("间夜均价");
				fieldObject.setValue("￥" + trafficVerifyResponse.getApplyTripTraffic().getAveragePrice().stripTrailingZeros().toPlainString());
				applyOtherFieldList.add(fieldObject);
			} else if(trafficVerifyResponse.getApplyTripTemp().getHotelAveragePrice() != null
					&& trafficVerifyResponse.getApplyTripTemp().getHotelAveragePrice() == 1) {
				CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo.FieldObject();
				fieldObject.setKey("averagePrice");
				fieldObject.setLabel("间夜均价");
				if(trafficVerifyResponse.getApplyTripTraffic().getAveragePrice() == null) {
					fieldObject.setValue(null);
				} else {
					fieldObject.setValue("￥" + trafficVerifyResponse.getApplyTripTraffic().getAveragePrice().stripTrailingZeros().toPlainString());
				}
				applyOtherFieldList.add(fieldObject);
			}
			if(trafficVerifyResponse.getApplyTripTemp().getHotelAmount() != null
					&& trafficVerifyResponse.getApplyTripTemp().getHotelAmount() == 2){
				// 总金额校验
				CheckAvailResponseVo.FieldObject fieldObject = checkAllAmount(request, hotelInfo, trafficVerifyResponse, exceedApplyFieldList);
				applyOtherFieldList.add(fieldObject);
			} else if(trafficVerifyResponse.getApplyTripTemp().getHotelAmount() != null
					&& trafficVerifyResponse.getApplyTripTemp().getHotelAmount() == 1) {
				CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo.FieldObject();
				fieldObject.setKey("amount");
				fieldObject.setLabel("可用金额");
				if(trafficVerifyResponse.getApplyTripTraffic().getAmount() == null ||
						trafficVerifyResponse.getApplyTripStock() == null ||
						trafficVerifyResponse.getApplyTripStock().getUsedAmount() == null) {
					fieldObject.setValue(null);
				} else {
					fieldObject.setValue("￥" + trafficVerifyResponse.getApplyTripTraffic().getAmount()
							.subtract(trafficVerifyResponse.getApplyTripStock().getUsedAmount())
							.stripTrailingZeros().toPlainString() + "(总金额￥"
							+ trafficVerifyResponse.getApplyTripTraffic().getAmount().stripTrailingZeros().toPlainString() + ")");
				}
				applyOtherFieldList.add(fieldObject);
			}

			if(CollectionUtils.isEmpty(exceedApplyFieldList)) {
				return responseVo;
			}

			List<CheckAvailResponseVo.FieldObject> applyFieldList = new ArrayList<>();
			applyFieldList.add(new CheckAvailResponseVo.FieldObject("applyNo", "核对出差申请单", trafficVerifyResponse.getApplyTripTraffic().getTripApplyNo()));
			applyFieldList.add(new CheckAvailResponseVo.FieldObject("trafficTypeName", "预订类型", EApplyTripTrafficType.HOTEL.getDesc()));
			applyFieldList.add(new CheckAvailResponseVo.FieldObject("endCityName", "入住城市", trafficVerifyResponse.getApplyTripTraffic().getEndCityName()));
			applyFieldList.add(new CheckAvailResponseVo.FieldObject("trafficData", "入离日期",
					hotelBasicDataService.getDateMassage(trafficVerifyResponse.getApplyTripTraffic().getStartDate(),
							trafficVerifyResponse.getApplyTripTraffic().getReturnDate())));
			applyFieldList.addAll(applyOtherFieldList);
			applyFieldList.add(new CheckAvailResponseVo.FieldObject("remark", "备注信息", trafficVerifyResponse.getApplyTripTraffic().getRemark()));

			// 关联申请单字段
			responseVo.setApplyFieldList(applyFieldList);
			// 超标字段
			responseVo.setExceedApplyFieldList(exceedApplyFieldList);
		}
		return responseVo;
	}

	private CheckAvailResponseVo.FieldObject checkAllAmount(CheckAvailRequestVo request, HotelInfoModel hotelInfo,
		ApplyTripTrafficVerifyResponse trafficVerifyResponse, List<CheckAvailResponseVo.FieldObject> exceedApplyFieldList) {
		BigDecimal balanceAmount = BigDecimal.ZERO;
		if(trafficVerifyResponse.getApplyTripTraffic().getAmount() != null
				&& trafficVerifyResponse.getApplyTripStock() != null) {
			balanceAmount = trafficVerifyResponse.getApplyTripTraffic().getAmount()
					.subtract(trafficVerifyResponse.getApplyTripStock().getUsedAmount());
		}
		BigDecimal allAmount = BigDecimal.ZERO;
		// 计算间隔天数
		Long sum = LocalDate.parse(hotelInfo.getCheckInDate()).until(LocalDate.parse(hotelInfo.getCheckOutDate()), ChronoUnit.DAYS);
		log.info("申请单间隔天数{},房间数据{}", sum, request.getRoomQuantity());
		// 多房间多晚相乘
		if (request.getRoomQuantity() != null) {
			sum = sum * request.getRoomQuantity();
		}
		// 混付
		if(StringUtil.equals(request.getPayInfoCode(), PayTypeEnum.MIXPAY.getType())) {
			// 公付金额
			allAmount = hotelInfo.getAmountHigh().multiply(BigDecimal.valueOf(sum));
		} else {
			// 总金额
			allAmount = hotelInfo.getPrice().multiply(BigDecimal.valueOf(sum));
		}
		if(allAmount.compareTo(balanceAmount) > 0) {
			// 超标
			CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo
					.FieldObject("amount", "总金额", "￥" + allAmount.stripTrailingZeros().toPlainString());
			exceedApplyFieldList.add(fieldObject);
		}

		CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo.FieldObject();
		fieldObject.setKey("amount");
		fieldObject.setLabel("可用金额");
		fieldObject.setValue("￥"+balanceAmount.stripTrailingZeros().toPlainString()+"(总金额￥"
				+ trafficVerifyResponse.getApplyTripTraffic().getAmount().stripTrailingZeros().toPlainString() + ")");
		return fieldObject;
	}

	private CheckAvailResponseVo toCheckAvailResponse(LocalCheckAvailResponseBo checkAvailResponse,
													  HotelInfoModel hotelInfo, CheckAvailResponseVo responseVo, BookingCheckResultBo checkResult) {
		responseVo.setErrorMsg(Null.or(checkResult, BookingCheckResultBo::getErrorMsg));
		responseVo.setHitRc(Null.or(checkResult, BookingCheckResultBo::getHitRc));
		responseVo.setErrorCode(Null.or(checkResult, t->String.valueOf(t.getErrorCode())));
	    RoomItem roomInfo = checkAvailResponse.getRoomInfo();
	    List<RoomDailyInfo> roomDailyInfoList = checkAvailResponse.getRoomDailyInfoList();
	    List<CheckAvailResponseVo.RoomDailyInfo> roomDailyInfos = roomDailyInfoList.stream().map(e -> {
		    CheckAvailResponseVo.RoomDailyInfo roomDailyInfo = new CheckAvailResponseVo.RoomDailyInfo();
		    roomDailyInfo.setEffectDate(e.getEffectDate());
		    roomDailyInfo.setRoomPrice(e.getSellPrice());
			return roomDailyInfo;
		}).collect(Collectors.toList());
		responseVo.setRoomDailyInfoList(roomDailyInfos);
		responseVo.setMinBookingRoomNum(Optional.ofNullable(roomInfo.getMinBookingRoomNum()).orElse(0));
		responseVo.setMaxBookingRoomNum(Optional.ofNullable(roomInfo.getMaxBookingRoomNum()).orElse(0));
		responseVo.setGuestPerson(Optional.ofNullable(roomInfo.getGuestPerson()).orElse(0));
		responseVo.setLastArrivalTime(roomInfo.getLastArrivalTime());
		responseVo.setEarlyArrivalTime(roomInfo.getEarlyArrivalTime());
		List<String> specialTipList = roomInfo.getSpecialTipList();
		responseVo.setNotifyList(specialTipList);
		// 酒店积分
		responseVo.setBonusPointInfo(convert(roomInfo.getBonusPointInfo()));

		Integer policyType = hotelInfo.getCancelInfo().getPolicyType();
		addElkInfoLog(
				"显示取消规则: %s  , 结果: %s",
				policyType, CancelPolicyEnum.getCancelDetail(policyType, roomInfo.getLastCancelTime())
		);
		// 先取酒店详情取消规则
		responseVo.setCancelDetailList(CancelPolicyEnum.getCancelDetail(policyType, roomInfo.getLastCancelTime()));
		// 再取可定查询取消规则
		if (CollectionUtils.isNotEmpty(Optional.ofNullable(roomInfo.getLadderDeductionInfoList()).map(LocalCheckAvailResponseBo::getCancelDetailList).orElse(new ArrayList<>(0)))) {
			responseVo.setCancelDetailList(LocalCheckAvailResponseBo.getCancelDetailList(roomInfo.getLadderDeductionInfoList()));
		} else {
			if(CollectionUtils.isEmpty(hotelInfo.getLadderDeductionList())) {
				return responseVo;
			}
			// 酒店缓存中的取消策略
			List<String> cancelDetailList = hotelManager.getCancelDetailList(hotelInfo.getLadderDeductionList());
			if(CollectionUtils.isNotEmpty(cancelDetailList)) {
				responseVo.setCancelDetailList(cancelDetailList);
			}
		}
		return responseVo;
	}

	private CheckAvailResponseVo.BonusPointInfo convert(LocalCheckAvailResponseBo.BonusPointInfo bonusPointInfo) {
		if (bonusPointInfo == null) {
			return null;
		}
		CheckAvailResponseVo.BonusPointInfo tmp = new CheckAvailResponseVo.BonusPointInfo();
		tmp.setGroupId(bonusPointInfo.getGroupId());
		tmp.setGroupName(bonusPointInfo.getGroupName());
		tmp.setBonusPointType(bonusPointInfo.getBonusPointType());
		tmp.setBonusPointDescList(bonusPointInfo.getFillPageRuleDescList());
		return tmp;
	}

    private LocalCheckAvailRequestBo toCheckAvailRequest(HotelInfoModel hotelInfo, CheckAvailRequestVo request) {
        LocalCheckAvailRequestBo requestBo = new LocalCheckAvailRequestBo();
        requestBo.setBaseInfo(toBaseInfo(hotelInfo));
        requestBo.setRoomInfo(toCheckRoomInfo(hotelInfo));
		requestBo.getRoomInfo().setQuantity(request.getRoomQuantity());
		if (request.getPersonQuantity() != null && request.getPersonQuantity() > 0) {
			requestBo.getRoomInfo().setGuestPerson(request.getPersonQuantity());
		}
		requestBo.setCorpPayType(StringUtils.isNotBlank(request.getExpenseType()) ? request.getExpenseType() : "OWN");
		requestBo.setOrgId(request.getUserInfo().getOrgId());
		requestBo.setCorpId(request.getUserInfo().getCorpId());
		requestBo.setUid(request.getUserInfo().getUid());
		// 因公因私标识
		requestBo.getRoomInfo().setFeeType("OWN".equals(request.getExpenseType()) ? "P" : "C");
        requestBo.setGroupId(hotelInfo.getGroupId());
        return requestBo;
    }

    private LocalCheckAvailRequestBo.CheckBaseInfo toBaseInfo(HotelInfoModel hotelInfo) {
        LocalCheckAvailRequestBo.CheckBaseInfo checkBaseInfo = new LocalCheckAvailRequestBo.CheckBaseInfo();
        checkBaseInfo.setSupplierCorpId(hotelInfo.getSupplierCorpId());
        checkBaseInfo.setSupplierUid(hotelInfo.getSupplierUid());
        checkBaseInfo.setSupplierCode(hotelInfo.getSupplierCode());
        return checkBaseInfo;
    }

    private LocalCheckAvailRequestBo.CheckRoomInfo toCheckRoomInfo(HotelInfoModel hotelInfo) {
        LocalCheckAvailRequestBo.CheckRoomInfo checkRoomInfo = new LocalCheckAvailRequestBo.CheckRoomInfo();
        checkRoomInfo.setCheckInDate(hotelInfo.getCheckInDate());
        checkRoomInfo.setCheckOutDate(hotelInfo.getCheckOutDate());
        checkRoomInfo.setProductId(hotelInfo.getProductId());
        checkRoomInfo.setQuantity(hotelInfo.getQuantity());
        checkRoomInfo.setGuestPerson(hotelInfo.getGuestPerson());
		checkRoomInfo.setRoomId(hotelInfo.getRoomId());
        return checkRoomInfo;
    }

	private void setMetric(CheckAvailResponseVo response){
		if(response == null){
			return;
		}
		Metrics.REGISTRY.counter(checkAvailId.withTags("errorCode",
				Conditional.ofNullable(response.getErrorCode()).orElse(GenericConstants.UNKNOWN)))
				.increment();
	}
}
