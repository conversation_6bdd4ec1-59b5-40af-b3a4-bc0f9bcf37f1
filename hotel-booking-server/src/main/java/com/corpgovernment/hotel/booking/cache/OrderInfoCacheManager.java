package com.corpgovernment.hotel.booking.cache;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo;
import com.corpgovernment.api.hotel.booking.saveorder.response.SaveOrderResponseVo;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.booking.cache.model.SupplementOrderInfoModel;
import com.corpgovernment.redis.cache.RedisUtils;
import com.corpgovernment.redis.cache.UserCacheManager;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单信息缓存
 */
@Service
@Slf4j
public class OrderInfoCacheManager {
    private static final String ORDER_INFO_PREFIX = "HotelBooking_OrderInfo";
    private static final Long EXPIRE_TIME = 30 * 60L;
    private static final String ADD_RECORD_ORDER_INFO_PREFIX = "HotelBooking_AddRecord_OrderInfo_";
    private static final String SAVE_ORDER_RESULT = "hotel_save_order_result_";

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private UserCacheManager userCacheManager;
    @Autowired
    private HotelApollo hotelApollo;

    private static final String INIT_ORDER_BASIC_RESPONSE = "InitOrderBasicResponse_HotelBooking_";

    /**
     * 保存补录订单缓存信息
     */
    public void saveSupplementOrderInfo(SupplementOrderInfoModel orderInfo) {
        log.info("orderInfo:" + JsonUtils.toJsonString(orderInfo));
        userCacheManager.setUserCache(ADD_RECORD_ORDER_INFO_PREFIX, orderInfo, hotelApollo.getBookTimeout());
    }

    /**
     * 获取补录订单缓存信息
     */
    public SupplementOrderInfoModel getSupplementOrderInfo() {
        SupplementOrderInfoModel orderInfoModel =
            userCacheManager.getUserCache(ADD_RECORD_ORDER_INFO_PREFIX, SupplementOrderInfoModel.class);
        if (orderInfoModel == null) {
            orderInfoModel = new SupplementOrderInfoModel();
        }
        return orderInfoModel;
    }

    public OrderInfoModel getOrderInfo(String hotelId, String roomId, String token) {
        String key = concatKey(ORDER_INFO_PREFIX, hotelId, roomId, token);
        OrderInfoModel orderInfoModel = redisUtils.getCache(key, OrderInfoModel.class);
        log.info("获取订单缓存，key={}, orderInfo={}", key, JsonUtils.toJsonString(orderInfoModel));
        return orderInfoModel;
    }

    public String saveOrderInfo(OrderInfoModel orderInfo, String hotelId, String roomId, String token) {
        String key = concatKey(ORDER_INFO_PREFIX, hotelId, roomId, token);
        redisUtils.setCache(key, orderInfo, hotelApollo.getBookTimeout());
        log.info("保存订单缓存，key={}, orderInfo={}", key, JsonUtils.toJsonString(orderInfo));
        return key;
    }

    public static String concatKey(String... params) {
        if (params == null || params.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < params.length; i++) {
            String param = params[i];
            if (StringUtils.isBlank(param)) {
                continue;
            }
            sb.append(param);
            if (i != params.length - 1) {
                sb.append("_");
            }
        }
        return sb.toString();
    }

    /**
     * 保存订单初始化基本信息，用于恢复脱敏数据
     * 
     * @param result
     */
    public void saveInitOrderBasicResponse(InitOrderResponseVo result) {
        userCacheManager.setUserCacheUnderToken(INIT_ORDER_BASIC_RESPONSE, result, hotelApollo.getBookTimeout());
    }

    /**
     * 获取订单初始化基本信息，用于恢复脱敏数据
     */
    public InitOrderResponseVo getInitOrderBasicResponse() {
        return userCacheManager.getUserCacheUnderToken(INIT_ORDER_BASIC_RESPONSE, InitOrderResponseVo.class);
    }

    /**
     * 保存下单接口结果
     *
     * @param orderId
     * @param saveOrderResponseVo
     * @return
     */
    public void setSaveOrderResult(Long orderId, SaveOrderResponseVo saveOrderResponseVo) {
        userCacheManager.setUserCacheUnderToken(SAVE_ORDER_RESULT + orderId, saveOrderResponseVo, EXPIRE_TIME);
    }

    /**
     * 获取下单接口结果
     *
     * @param orderId
     * @return
     */
    public SaveOrderResponseVo getSaveOrderResult(Long orderId) {
        return userCacheManager.getUserCacheUnderToken(SAVE_ORDER_RESULT + orderId, SaveOrderResponseVo.class);
    }
}