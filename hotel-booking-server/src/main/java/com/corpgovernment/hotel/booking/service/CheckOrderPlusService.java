package com.corpgovernment.hotel.booking.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.corpgovernment.core.domain.hoteldetail.model.entity.TimeSlot;
import com.corpgovernment.dto.snapshot.dto.hotel.HourlyRoomInfoDTO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.approvalsystem.bean.FlowDetail;
import com.corpgovernment.api.approvalsystem.bean.TraverlerInfo;
import com.corpgovernment.api.approvalsystem.enums.ApprovalEnum;
import com.corpgovernment.api.approvalsystem.enums.ApprovalFlowFlagEnum;
import com.corpgovernment.api.approvalsystem.enums.ApprovalWayEnum;
import com.corpgovernment.api.approvalsystem.enums.UrgentEnum;
import com.corpgovernment.api.approvalsystem.service.ApprovalSystemClient;
import com.corpgovernment.api.approvalsystem.service.request.GetFlowTmplRequest;
import com.corpgovernment.api.approvalsystem.service.response.GetFlowTmplResponse;
import com.corpgovernment.api.basic.dto.BasicCityInfoDto;
import com.corpgovernment.api.basic.enums.BasicCorpBrandTypeEnum;
import com.corpgovernment.api.basic.request.BasicCityListRequest;
import com.corpgovernment.api.basic.request.CorpBrandByBrandIdRequest;
import com.corpgovernment.api.basic.response.BasicGeographyInfoResponse;
import com.corpgovernment.api.basic.response.BasicIntegratedCityResponse;
import com.corpgovernment.api.costcenter.model.CostCenter;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InvoiceInfoVo;
import com.corpgovernment.api.hotel.product.enums.DeductionEnum;
import com.corpgovernment.api.organization.model.switchinfo.GetAllSwitchResponse;
import com.corpgovernment.api.organization.model.switchinfo.GetSwitchListRequest;
import com.corpgovernment.api.supplier.vo.MbSupplierInfoVo;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.enums.HotelSwitchEnum;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.api.travelstandard.vo.HotelPriceControl;
import com.corpgovernment.api.travelstandard.vo.HotelTravelStandardManageVo;
import com.corpgovernment.basic.bo.response.RuleChainBO;
import com.corpgovernment.basic.bo.response.TravelStandardResponseBO;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.common.base.BaseRequestVO;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.CancelPolicyEnum;
import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.enums.ExpenseTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.converter.converterImpl.QueryParamV1Converter;
import com.corpgovernment.converter.factoryImpl.QueryParamConverterFactoryImpl;
import com.corpgovernment.converter.model.queryparam.QueryParamModel;
import com.corpgovernment.core.dao.apollo.impl.HotelCoreApolloDao;
import com.corpgovernment.core.dao.openfeign.IBasicDataClientOpenFeignDao;
import com.corpgovernment.core.dao.openfeign.IHotelCoreOpenFeignDao;
import com.corpgovernment.core.domain.common.model.enums.ResourceModeEnum;
import com.corpgovernment.core.domain.common.model.enums.TravelAttributeEnum;
import com.corpgovernment.core.domain.gateway.HotelSnapshotGateway;
import com.corpgovernment.core.domain.hotelconfig.model.entity.Guest;
import com.corpgovernment.core.domain.hotelconfig.model.entity.HotelRoomCheckInInfo;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.enums.EmployeeTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.GenderEnum;
import com.corpgovernment.core.domain.hotelconfig.service.IHotelConfigDomainService;
import com.corpgovernment.core.domain.hoteldetail.model.enums.CancelRuleEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.MapTypeEnum;
import com.corpgovernment.core.domain.model.snapshot.config.HotelQueryContextModel;
import com.corpgovernment.dto.config.AllSwitchDTO;
import com.corpgovernment.dto.config.ServiceFeeDTO;
import com.corpgovernment.dto.config.SupplierConfigDTO;
import com.corpgovernment.dto.config.SwitchDTO;
import com.corpgovernment.dto.config.response.GetBookingConfigByTokenResponse;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.dto.snapshot.PriceInfoType;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.SnapShotDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.ApplicativeAreaType;
import com.corpgovernment.dto.snapshot.dto.hotel.BasicRoomInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.CancelPolicyType;
import com.corpgovernment.dto.snapshot.dto.hotel.DailyRoomPrice;
import com.corpgovernment.dto.snapshot.dto.hotel.FilterRoomInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.HotelBaseInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.MapInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.PaymentMethodType;
import com.corpgovernment.dto.snapshot.dto.hotel.ReservationNoticeType;
import com.corpgovernment.dto.snapshot.dto.hotel.RoomBaseInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.RoomInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.RoomPackageType;
import com.corpgovernment.dto.snapshot.dto.hotel.RoomPolicyServiceDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.RoomPriceType;
import com.corpgovernment.dto.snapshot.dto.hotel.SupplierStarDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.ZoneType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.BonusPointInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.BookingRulesDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.CalculateResultDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.FeeInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.HotelFeeInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.InvoiceInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.LadderDeductionDetailType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.LadderDeductionInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.RemarkType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.RoomDailyPriceType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.TaxFeeType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.TaxInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.TimeInformationType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.TravelExceedInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelFeeSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelProductSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelFeeSnapshotResponse;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelProductSnapshotResponse;
import com.corpgovernment.dto.token.request.HotelOrderTravelStandardGetReqVo;
import com.corpgovernment.dto.token.response.HotelOrderTravelStandardGetRespVo;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.response.rule.CohabitRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.FloatPriceRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.OffPeakSeasonRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.PriceRuleVO;
import com.corpgovernment.hotel.booking.bo.VerifyTravelStandardBo;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.booking.enums.FeeTypeEnum;
import com.corpgovernment.hotel.booking.enums.MixPayTypeEnum;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BookingCoreClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CoreServiceClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CostCenterClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierDataClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SwitchClientLoader;
import com.corpgovernment.hotel.product.supplier.enums.InvoiceEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * CheckOrder根据预订流程token获取orderInfo信息
 */
@Service
@Slf4j
public class CheckOrderPlusService {

    @Autowired
    private CoreServiceClientLoader coreServiceClientLoader;
    @Autowired
    private HotelSnapshotGateway hotelSnapshotGateway;
    @Resource
    private IHotelCoreOpenFeignDao hotelCoreOpenFeignDao;
    @Autowired
    private BookingCoreClientLoader bookingCoreClientLoader;
    @Autowired
    private IBasicDataClientOpenFeignDao iBasicDataClientOpenFeignDao;
    @Autowired
    private TravelStandardService travelStandardService;
    @Autowired
    private SupplierDataClientLoader supplierDataClientLoader;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private CostCenterClientLoader costCenterClientLoader;
    @Autowired
    private SwitchClientLoader switchClientLoader;
    @Autowired
    private ApprovalSystemClient approvalSystemClient;
    @Autowired
    private HotelCoreApolloDao hotelCoreApolloDao;
    @Resource
    private IHotelConfigDomainService hotelConfigDomainService;

    @Resource
    private CoreServiceClient coreServiceClient;

    /**
     * 根据预订流程token获取订单信息
     *
     * @param request 订单校验请求对象
     * @return 订单信息模型对象
     */
    public OrderInfoModel getOrderInfo(CheckOrderRequestVo request) {
        log.info("根据预订流程token获取订单信息,request:{}",JsonUtils.toJsonString(request));

        // 必须输入参数校验
        String paymentType = request.getPaymentType(); // 支付方式 ONLINE 在线付 CASH 到店付
        if (!"ONLINE".equals(paymentType) && !"CASH".equals(paymentType)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.PAYMENTTYPE_CAN_NOT_BE_EMPTY);
        }

        OrderInfoModel orderInfoModel = new OrderInfoModel();
        orderInfoModel.setNoConfigUrgentApply(Boolean.FALSE);

        BaseRequestVO.UserInfo userInfo = request.getUserInfo();
        Optional<GetHotelFeeSnapshotResponse> feeSnapshot = fetchHotelFeeSnapshot(request); // 获取酒店费用快照响应
        log.info("获取酒店费用快照响应fetchHotelFeeSnapshot request:{} feeSnapshot:{}",JsonUtils.toJsonString(request), JsonUtils.toJsonString(feeSnapshot.get()));

        Optional<GetBookingConfigByTokenResponse> bookingConfig = fetchBookingConfig(request); // 获取预订配置快照
        log.info("获取预订配置快照fetchBookingConfig request:{} bookingConfig:{}",JsonUtils.toJsonString(request), JsonUtils.toJsonString(bookingConfig.get()));

        // 查询服务商信息
        String supplierCode = request.getSupplierCode(); // 供应商code
        MbSupplierInfoVo mbSupplierInfoVo = supplierDataClientLoader.findBySupplierCode(supplierCode);
        log.info("查询服务商信息indBySupplierCode,supplierCode:{},mbSupplierInfoVo:{}", supplierCode,JsonUtils.toJsonString(mbSupplierInfoVo));

        Optional<GetHotelProductSnapshotResponse> productSnapshot = fetchHotelProductSnapshot(request,mbSupplierInfoVo); // 获取酒店产品快照响应
        log.info("获取酒店产品快照响应fetchHotelProductSnapshot request:{} productSnapshot:{}",JsonUtils.toJsonString(request), JsonUtils.toJsonString(productSnapshot.get()));

        Optional<HotelQueryContextModel> hotelContextSnapshot = fetchHotelContextSnapshot(request);//获取酒店查询快照
        log.info("获取酒店查询快照fetchHotelContextSnapshot request:{} hotelContextSnapshot:{}",JsonUtils.toJsonString(request), JsonUtils.toJsonString(hotelContextSnapshot.get()));

        Optional<QueryParamModel> queryParamModel = fetchSnapshot(request);// 获取快照Snapshot
        log.info("获取快照SnapshotfetchSnapshot request:{} queryParamModel:{}",JsonUtils.toJsonString(request), JsonUtils.toJsonString(queryParamModel.get()));

        //获取成本中心
        String policyOrgId = StringUtils.isNotBlank(queryParamModel.get().getPolicyOrgId()) ? queryParamModel.get().getPolicyOrgId() : userInfo.getOrgId();
        List<CostCenter> costCenters = costCenterClientLoader.getCostCenter(policyOrgId);
        log.info("获取成本中心costCenterClientLoader policyOrgId:{},costCenters:{}",policyOrgId,JsonUtils.toJsonString(costCenters));
        CostCenter costCenter = null;
        if (CollectionUtils.isNotEmpty(costCenters)){
            costCenter = costCenters.get(0);
        }
        orderInfoModel.setCostCenter(costCenter);

        // 获取差标信息
        HotelTravelStandardManageVo hotelTravelStandard = getHotelTravelStandardByToken(request.getToken());
        log.info("获取差标信息getHotelTravelStandardManageVo request:{},hotelTravelStandard:{}", JsonUtils.toJsonString(request),JsonUtils.toJsonString(hotelTravelStandard));
        
        Boolean overseasHotelControlIncludeExtraTax = getOverseasHotelControlIncludeExtraTax(hotelContextSnapshot.map(HotelQueryContextModel::getCorpPayType).orElse(null), bookingConfig.orElse(null), queryParamModel.orElse(null));
        Integer roomNum = hotelContextSnapshot.map(HotelQueryContextModel::getRoomNum).orElse(0);
        
        VerifyTravelStandardBo verifyTravelStandardBo = null; // 校验是否超标
        // 因公/因私(PUB/OWN)
        String corpPayType = hotelContextSnapshot.get().getCorpPayType();
        HotelControlVo  hotelControlVo = null; //根据token获取差标
        log.info("校验是否超标,corpPayType:{}",corpPayType);
        if(corpPayType.equalsIgnoreCase("PUB")){
            verifyTravelStandardBo = getVerifyTravelStandardBo(request, productSnapshot, feeSnapshot, overseasHotelControlIncludeExtraTax, roomNum);
            log.info("校验是否超标getVerifyTravelStandardBo request:{},productSnapshot:{},verifyTravelStandardBo:{}",
                    JsonUtils.toJsonString(request),JsonUtils.toJsonString(productSnapshot),JsonUtils.toJsonString(verifyTravelStandardBo));


            hotelControlVo = travelStandardService.getHotelControlVoByToken(request.getToken());
            log.info("根据token获取差标getHotelControlVoByToken hotelControlVo:{}", JsonUtils.toJsonString(hotelControlVo));
            orderInfoModel.setTravelStandard(ObjectUtil.isNotNull(verifyTravelStandardBo)?JsonUtils.toJsonString(verifyTravelStandardBo):null);
        }
        // 是否紧急预订
        Boolean urgentBooking = null;
        HotelFeeInfoDTO hotelFeeInfo = feeSnapshot.get().getHotelFeeInfo();
        if(ObjectUtil.isNotNull(hotelFeeInfo)){
            log.info("feeSnapshot.get().getHotelFeeInfo(),hotelFeeInfo:{}",JsonUtils.toJsonString(feeSnapshot.get()));
            urgentBooking = hotelFeeInfo.getUrgentBooking();
        }
        
        BookingRulesDTO bookingRulesDTO = hotelFeeInfo.getBookingRules();
        log.info("预定规则,bookingRulesDTO:{}",JsonUtils.toJsonString(bookingRulesDTO));
        String language = Optional.ofNullable(bookingRulesDTO)
                .map(BookingRulesDTO::getBillingGuestInfo)
                .map(BookingRulesDTO.BillingGuestInfo::getGuestsNameLanguages)
                .filter(CollectionUtil::isNotEmpty)
                .map(list -> list.get(0))
                .orElse("zh");
        orderInfoModel.setLanguage(language);
        log.info("语言,setLanguage:{}", language);
        
        orderInfoModel.setNeedEmail(Optional.ofNullable(hotelFeeInfo.getBookingRules())
                .map(BookingRulesDTO::getBillingGuestInfo)
                .map(BookingRulesDTO.BillingGuestInfo::getNeedEmail)
                .orElse(null));
        orderInfoModel.setNeedCertificate(Optional.ofNullable(hotelFeeInfo.getBookingRules())
                .map(BookingRulesDTO::getCertificateInfo)
                .map(BookingRulesDTO.CertificateInfo::getNeedCertificate)
                .orElse(null));
        orderInfoModel.setSupportCertificateTypeList(Optional.ofNullable(hotelFeeInfo.getBookingRules())
                .map(BookingRulesDTO::getCertificateInfo)
                .map(BookingRulesDTO.CertificateInfo::getSupportCertificateTypeList)
                .orElse(null));
        
        // 获取审批信息
        // 国际国内标识 HOTEL:国内酒店, HOTEL_INTL:国际酒店
        String productType = request.getProductType();
        Map<Integer, FlowDetail> flowDetailMap = getFlowDetailMap(userInfo,corpPayType,urgentBooking,queryParamModel.get(),productType);
        log.info("获取审批信息flowDetailMap userInfo:{},flowDetailMap:{}", JsonUtils.toJsonString(userInfo),JsonUtils.toJsonString(flowDetailMap));
        if(CorpPayTypeEnum.PUB.getType().equalsIgnoreCase(corpPayType) && Boolean.TRUE.equals(urgentBooking) && !flowDetailMap.containsKey(ApprovalFlowFlagEnum.NORMAL.getCode())){
            orderInfoModel.setNoConfigUrgentApply(Boolean.TRUE);
        }

        // 获取差标信息
        GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
        getTravelStandardByTokenRequest.setTokenList(Arrays.asList(request.getToken()));
        List<TravelStandardResponseBO> travelStandardResponseList = applyTripClientLoader.getTravelStandardByToken(getTravelStandardByTokenRequest);
        log.info("获取差标信息getTravelStandardByToken travelStandardResponseList:{}", JsonUtils.toJsonString(travelStandardResponseList));
        
        // 获取差旅配置
        TravelConfig travelConfig = hotelConfigDomainService.getTravelConfig(request.getToken(), null);
        orderInfoModel.setOverseasHotelControlIncludeExtraTax(travelConfig.getOverseasHotelControlIncludeExtraTax());
        orderInfoModel.setPriceControlStrategyEnum(travelConfig.getPriceControlStrategyEnum());
        
        
        // 获取请求契约中的数据
        buildRequest(orderInfoModel, request);

        //[End] 构建订单信息模型
        buildOrderInfo(request,feeSnapshot.get(), productSnapshot.get(), hotelContextSnapshot.get(),
                bookingConfig.get(),queryParamModel.get(),hotelTravelStandard,flowDetailMap,
                verifyTravelStandardBo,
                hotelControlVo,mbSupplierInfoVo,travelStandardResponseList,orderInfoModel);
        
        // 未合住RC
        CheckOrderRequestVo.NoChummageRc noChummageRc = Optional.ofNullable(request.getNoChummageRc())
                .orElse(new CheckOrderRequestVo.NoChummageRc());
        // 转换
        List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList = convertHotelRoomCheckInInfoList(request);
        // 判断是否有合住
        Boolean haveHotelChummage = hotelConfigDomainService.getHaveHotelChummage(hotelRoomCheckInInfoList);
        orderInfoModel.setChummageInfo(OrderInfoModel.ChummageInfo.builder()
                .haveChummage(haveHotelChummage)
                .noChummageReasonCode(OrderInfoModel.NoChummageReasonCode.builder()
                        .id(noChummageRc.getId())
                        .code(noChummageRc.getCode())
                        .name(noChummageRc.getName())
                        .remark(noChummageRc.getRemark()).build()).build());


        HotelOrderTravelStandardGetRespVo hotelOrderTravelStandard = getHotelOrderTravelStandard(request);

        // 差标执行人同住政策
        orderInfoModel.setCohabitRule(hotelOrderTravelStandard.getCohabitRule());
        // 执行差标价格上限、下限
        orderInfoModel.setMaxAvgPriceRule(hotelOrderTravelStandard.getMaxAvgPriceRule());
        orderInfoModel.setMinAvgPriceRule(hotelOrderTravelStandard.getMinAvgPriceRule());
        // 执行差标星级、品牌
        orderInfoModel.setStarRule(hotelOrderTravelStandard.getStarRule());
        orderInfoModel.setBrandRule(hotelOrderTravelStandard.getBrandRule());
        // 差标执行人是否开启浮动差标，命中的浮动差标场景和浮动策略；(如果返回的浮动差标为空，则说明未命中浮动差标)
        orderInfoModel.setFloatPriceRules(getFloatRule(travelStandardResponseList));
        // 订单是否超标
        orderInfoModel.setOverLimit(hotelOrderTravelStandard.getOrderOverLimit());
        // 超标类型（超标准差标/超浮动差标）（超标时需落库）
        // 订单超标金额（订单超标+混付）
        // 可用的超标管控方式
        orderInfoModel.setTravelExceedInfoType(feeSnapshot
                .map(GetHotelFeeSnapshotResponse::getCalculateResult)
                .map(m -> m.getTravelExceedInfo() == null ? null : m.getTravelExceedInfo())
                .orElse(null)
        );

        // 返回订单信息模型
        return orderInfoModel;
    }

    private List<FloatPriceRuleVO> getFloatRule(List<TravelStandardResponseBO> travelStandardResponseList) {
        if (CollectionUtils.isEmpty(travelStandardResponseList)) {
            log.info("getFloatRule travelStandardResponseList is empty");
            return null;
        }

        return travelStandardResponseList.stream()
                .map(travelStandardResponse -> {
                    RuleChainBO ruleChainBO = travelStandardResponse.getRuleChainBO();
                    if (ruleChainBO == null) {
                        return null;
                    }

                    FloatPriceRuleVO floatPriceRuleVO = ruleChainBO.getFloatPriceRuleVO();
                    // 如果浮动差标为空，则说明未命中浮动差标
                    if (floatPriceRuleVO == null) {
                        return null;
                    }

                    // TODO 获取浮动差标场景

                    return floatPriceRuleVO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private HotelOrderTravelStandardGetRespVo getHotelOrderTravelStandard(CheckOrderRequestVo request) {
        JSONResult<HotelOrderTravelStandardGetRespVo> jsonResult = coreServiceClient.getHotelOrderTravelStandard(new HotelOrderTravelStandardGetReqVo(request.getToken()));
        log.info("getHotelOrderTravelStandard jsonResult={}", JsonUtils.toJsonString(jsonResult));
        if (jsonResult == null || jsonResult.getData() == null) {
            log.error("getHotelOrderTravelStandard jsonResult is null");
            return new HotelOrderTravelStandardGetRespVo();
        }

        return jsonResult.getData();
    }
    
    /**
     * 转换
     * @param request
     * @return
     */
    private List<HotelRoomCheckInInfo> convertHotelRoomCheckInInfoList(CheckOrderRequestVo request) {
        if (request == null || CollectionUtils.isEmpty(request.getPassengerRoomMap())) {
            return null;
        }
        
        // 酒店房间入住人信息列表
        List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList = new ArrayList<>();
        
        // 从前端获取房间入住人信息
        request.getPassengerRoomMap().forEach((roomIndex, passengerInfoList) -> {
            if (CollectionUtils.isEmpty(passengerInfoList)) {
                return;
            }
            hotelRoomCheckInInfoList.add(HotelRoomCheckInInfo.builder()
                    .roomIndex(roomIndex)
                    .guestList(convertGuestList(passengerInfoList))
                    .build());
        });
        return hotelRoomCheckInInfoList;
    }
    
    private List<Guest> convertGuestList(List<CheckOrderRequestVo.PassengerInfo> passengerInfoList) {
        if (CollectionUtils.isEmpty(passengerInfoList)) {
            return null;
        }
        
        List<Guest> guestList = new ArrayList<>();
        for (CheckOrderRequestVo.PassengerInfo passengerInfo : passengerInfoList) {
            if (passengerInfo == null) {
                continue;
            }
            
            // 员工类型
            EmployeeTypeEnum employeeTypeEnum = EmployeeTypeEnum.getEnum(Optional.ofNullable(passengerInfo.getEmployeeType()).map(Object::toString).orElse(null));
            
            guestList.add(Guest.builder()
                    .employeeTypeEnum(employeeTypeEnum)
                    .orgId(passengerInfo.getOrgId())
                    .uid(Objects.equals(EmployeeTypeEnum.EXTERNAL_EMPLOYEE, employeeTypeEnum) ? passengerInfo.getNoEmployeeId() : passengerInfo.getUid())
                    .genderEnum(GenderEnum.getEnum(passengerInfo.getGender()))
                    .build());
        }
        return guestList;
    }
    
    private Boolean getOverseasHotelControlIncludeExtraTax(String expenseType, GetBookingConfigByTokenResponse bookingConfig, QueryParamModel queryParamModel) {
        if (!StringUtils.equalsIgnoreCase(ExpenseTypeEnum.PUB.getCode(), expenseType)) {
            return false;
        }
        
        BizTypeEnum bizTypeEnum = Optional.ofNullable(queryParamModel).map(QueryParamModel::getProductType).map(BizTypeEnum::getByCodeOrName).orElse(null);
        if (!Objects.equals(bizTypeEnum, BizTypeEnum.HOTEL_INTL)) {
            return false;
        }
        
        return Optional.ofNullable(bookingConfig)
                .map(GetBookingConfigByTokenResponse::getAllSwitch)
                .map(AllSwitchDTO::getSwitchInfoSoaMap)
                .map(item -> item.get(TravelAttributeEnum.OVERSEAS_HOTEL_CONTROL_INCLUDE_EXTRA_TAX.getCode()))
                .map(SwitchDTO::getValue)
                .map(item -> JsonUtils.parseArray(item, Integer.class))
                .map(item -> item.get(0))
                .map(item -> item == 1).orElse(false);
    }

    /**
     * 根据用户信息获取审批信息
     *
     * @param userInfo 用户信息
     * @param corpPayType 企业支付方式  因公/因私(PUB/OWN)
     * @param urgentBooking 是否紧急预定
     * @param queryParamModel 查询快照
     * @return 审批信息映射
     */
    private Map<Integer, FlowDetail> getFlowDetailMap(BaseRequestVO.UserInfo userInfo, String corpPayType, Boolean urgentBooking,QueryParamModel queryParamModel,String productType) {
        log.info("根据用户信息获取审批信息,userInfo:{},corpPayType:{},urgentBooking:{},queryParamModel:{}",
                JsonUtils.toJsonString(userInfo),corpPayType,urgentBooking,JsonUtils.toJsonString(queryParamModel));
        //因私不需要审批
        if (Objects.equals(corpPayType, ExpenseTypeEnum.OWN.getCode())) {
            return null;
        }

        String policyIdTemp = queryParamModel.getPolicyUid();
        String policyOrgIdTemp = queryParamModel.getPolicyOrgId();
        // 判断是否选择的外部执行人,如果选择的是外部执行人,则走登录人的审批流
        if(2 == queryParamModel.getPolicyEmployeeType()){ // 2表示外部执行人
            log.info("外部审批流程查询,queryParamModel:{}",JsonUtils.toJsonString(queryParamModel));
            policyIdTemp = userInfo.getUid();
            policyOrgIdTemp = userInfo.getOrgId();
        }
        String policyId = policyIdTemp;
        String policyOrgId = policyOrgIdTemp;
        log.info("获取差标执行人,policyId:{},policyOrgId:{}",policyId,policyOrgId);
        TraverlerInfo traveler = new TraverlerInfo();
        traveler.setTraverlerId(policyId);
        traveler.setOrgId(policyOrgId);
        traveler.setTraverlerName(userInfo.getUsername());

        GetFlowTmplRequest getFlowTmplRequest = new GetFlowTmplRequest();
        getFlowTmplRequest.setApprovalEnum(ApprovalEnum.ON);
        getFlowTmplRequest.setTravelers(Lists.newArrayList(traveler));
        if("HOTEL_INTL".equalsIgnoreCase(productType)){ // 国际国内标识 HOTEL:国内酒店, HOTEL_INTL:国际酒店
            getFlowTmplRequest.setTrafficTypes(Lists.newArrayList(9));
        }else{
            getFlowTmplRequest.setTrafficTypes(Lists.newArrayList(6));
        }
        getFlowTmplRequest.setFlag(ApprovalFlowFlagEnum.NORMAL.getCode());

        Map<Integer, FlowDetail> result = new HashMap<>();

        // 紧急预订只走紧急预订流程
        if(ObjectUtil.isNotNull(urgentBooking) && urgentBooking){
            log.info("紧急预定审批流程查询,urgentBooking:{}",urgentBooking);
            getFlowTmplRequest.setUrgentEnum(UrgentEnum.YES);
            GetFlowTmplResponse flowImpl = getFlowImpl(getFlowTmplRequest);
            FlowDetail flowDetail = Optional.ofNullable(flowImpl.getFlowDetailMap()).map(e -> e.get(genFlowKey(policyId, policyOrgId))).orElse(null);
            if(flowDetail==null){
//                throw new CorpBusinessException(HotelResponseCodeEnum.URGENT_APPLY_APPROVAL_NOT_SETUP);
                log.warn("无可用的紧急预订审批流");
            }else {
                result.put(ApprovalFlowFlagEnum.NORMAL.getCode(), flowDetail);
            }
            log.info("紧急预定审批流程查询结果：{}", JsonUtils.toJsonString(result));
            return result;
        }

        // 查询普通审批
        getFlowTmplRequest.setUrgentEnum(UrgentEnum.NO);

        GetFlowTmplResponse flowImplNormal = getFlowImpl(getFlowTmplRequest);
        log.info("普通审批 getFlowImpl getFlowTmplRequest:{} flowImplNormal:{}",JsonUtils.toJsonString(getFlowTmplRequest) , JsonUtils.toJsonString(flowImplNormal));

        FlowDetail flowDetail = Optional.ofNullable(flowImplNormal.getFlowDetailMap()).map(e -> e.get(genFlowKey(policyId, policyOrgId))).orElse(null);
        result.put(ApprovalFlowFlagEnum.NORMAL.getCode(), flowDetail);

        getFlowTmplRequest.setFlag(ApprovalFlowFlagEnum.EXCEED.getCode());
        GetFlowTmplResponse flowImplExceed = getFlowImpl(getFlowTmplRequest);
        log.info("超标审批 getFlowImpl getFlowTmplRequest:{} flowImplNormal:{}",JsonUtils.toJsonString(getFlowTmplRequest) , JsonUtils.toJsonString(flowImplExceed));
        FlowDetail flowDetailExceed = Optional.ofNullable(flowImplExceed.getFlowDetailMap()).map(e -> e.get(genFlowKey(policyId, policyOrgId))).orElse(null);
        result.put(ApprovalFlowFlagEnum.EXCEED.getCode(), flowDetailExceed);

        log.info("获取审批信息结果,result:{}",JsonUtils.toJsonString(result));
        return result;
    }

    private GetFlowTmplResponse getFlowImpl(GetFlowTmplRequest request) {
        GetFlowTmplResponse flowTmpl = approvalSystemClient.getFlowTmpl(request);
        log.info("查询审批信,request:{},response:{}", JsonUtils.toJsonString(request),JsonUtils.toJsonString(flowTmpl));
        if (flowTmpl == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_APPROVAL_INFORMATION);
        }
        if (CollectionUtils.isNotEmpty(flowTmpl.getFlowDetailMap())){
            flowTmpl.getFlowDetailMap().forEach((key, flowTmplInfo) ->{
                if (flowTmplInfo.getPayTypeEnums().contains(PayTypeEnum.ACCNT.getType())){
                    flowTmplInfo.getPayTypeEnums().add(PayTypeEnum.MIXPAY.getType());
                }
            });
        }
        return flowTmpl;
    }

    /**
     * 根据订单请求获取产品类型
     *
     * @param request 订单请求对象
     * @return HOTEL:国内酒店  HOTEL_INTL:国际酒店
     */
    public String getProductType(CheckOrderRequestVo request){
        Optional<QueryParamModel> queryParamModel = fetchSnapshot(request);// 获取快照Snapshot
        if(queryParamModel.isPresent() && ObjectUtil.isNotEmpty(queryParamModel.get())){
            log.info("获取快照SnapshotfetchSnapshot request:{} queryParamModel:{}",JsonUtils.toJsonString(request), JsonUtils.toJsonString(queryParamModel.get()));
            return queryParamModel.get().getProductType();
        }
        return "";
    }

    /**
     * 获取支付方式
     *
     * @param request 订单校验请求对象
     * @return 支付方式代码，若支付信息为空则返回空字符串
     */
    private String getPayType(CheckOrderRequestVo request) {
        CheckOrderRequestVo.PayInfo payInfo = request.getPayInfo();
        log.info("获取支付方式,payInfo:{}",JsonUtils.toJsonString(payInfo));
        if (ObjectUtil.isNotNull(request)) {
            return payInfo.getCode();
        }
        return "";
    }

    /**
     * 获取费用信息
     *
     * @param request 订单校验请求对象
     * @return 费用信息类型对象，如果未找到则返回null
     */
    public FeeInfoType getFeeInfoType(CheckOrderRequestVo request) {
        log.info("获取费用信息类型getFeeInfoType,request:{}", JsonUtils.toJsonString(request));
        // 获取支付类型
        String payType = getPayType(request);
        // 获取酒店费用快照响应
        Optional<GetHotelFeeSnapshotResponse> feeSnapshot = fetchHotelFeeSnapshot(request); // 获取酒店费用快照响应
        log.info("获取酒店费用快照响应fetchHotelFeeSnapshot request:{} feeSnapshot:{}",JsonUtils.toJsonString(request), JsonUtils.toJsonString(feeSnapshot));

        return feeSnapshot.map(GetHotelFeeSnapshotResponse::getCalculateResult)
                .map(CalculateResultDTO::getFeeInfoMap)
                .map(feeInfoMap -> feeInfoMap.get(payType))
                .map(this::getFirstFeeInfoTypeFromList)
                .orElse(null);
    }

    /**
     * 从给定的费用信息类型列表中获取第一个费用信息类型对象
     *
     * @param feeInfoTypeList 费用信息类型列表，不允许为空且列表中至少有一个元素
     * @return 第一个费用信息类型对象，如果列表为空则返回null
     */
    private FeeInfoType getFirstFeeInfoTypeFromList(List<FeeInfoType> feeInfoTypeList) {
        if (CollectionUtils.isNotEmpty(feeInfoTypeList)) {
            return feeInfoTypeList.get(0);
        }
        return null;
    }

    /**
     * 根据订单请求信息、酒店产品快照信息、酒店费用快照信息获取VerifyTravelStandardBo对象
     *
     * @param request 订单请求信息
     * @param productSnapshot 酒店产品快照信息
     * @param feeSnapshot 酒店费用快照信息
     * @return VerifyTravelStandardBo对象
     */
    private VerifyTravelStandardBo getVerifyTravelStandardBo(CheckOrderRequestVo request, Optional<GetHotelProductSnapshotResponse> productSnapshot, Optional<GetHotelFeeSnapshotResponse> feeSnapshot, Boolean overseasHotelControlIncludeExtraTax, Integer roomNum) {
        //TODO 增加非空校验
        VerifyTravelStandardRequest verifyTravelStandardRequest = new VerifyTravelStandardRequest();
        verifyTravelStandardRequest.setTravelStandardToken(request.getToken());
        verifyTravelStandardRequest.setBizType("3"); // 国内酒店
        List<HotelVerifyRequest> hotelList = new ArrayList<>();
        HotelVerifyRequest hotelVerifyRequest = new HotelVerifyRequest();
        HotelBaseInfoDTO hotelInfo = productSnapshot.get().getHotelInfo();
        Map<String, SupplierStarDTO> supplierStarInfo = hotelInfo.getSupplierStarInfo();
        // 兼容发布过程中老的快照为空
        if (supplierStarInfo != null) {
            SupplierStarDTO supplierStarDto = supplierStarInfo.get(request.getSupplierCode());
            hotelVerifyRequest.setStar(supplierStarDto.getStar());
            hotelVerifyRequest.setStarLicence(supplierStarDto.getStarLicence());
        } else {
            hotelVerifyRequest.setStar(hotelInfo.getStar());
            hotelVerifyRequest.setStarLicence(hotelInfo.getStarLicence());
        }
        feeSnapshot.ifPresent(h -> {
            HotelFeeInfoDTO hotelFeeInfo = h.getHotelFeeInfo();

            RoomDailyPriceType roomDailyPriceType = hotelFeeInfo.getRoomDailyPriceTypeList().get(0);
            PriceInfoType sellPrice = roomDailyPriceType.getSellPrice();
            
            BigDecimal avgExtraTax = null;
            BigDecimal totalExtraTax = getTotalExtraTax(hotelFeeInfo);
            if (roomNum != null && CollectionUtils.isNotEmpty(hotelFeeInfo.getRoomDailyPriceTypeList()) && totalExtraTax != null) {
                avgExtraTax = totalExtraTax.divide(new BigDecimal(roomNum * hotelFeeInfo.getRoomDailyPriceTypeList().size()), 2, RoundingMode.HALF_UP);
            }
            
            if (Boolean.TRUE.equals(overseasHotelControlIncludeExtraTax) && avgExtraTax != null) {
                BigDecimal avgPrice = Null.or(sellPrice.getPrice(), BigDecimal.ZERO);
                hotelVerifyRequest.setPrice(avgPrice.add(avgExtraTax));
            } else {
                hotelVerifyRequest.setPrice(sellPrice.getPrice());
            }

            com.corpgovernment.dto.snapshot.dto.ServiceFeeDTO serviceFee = hotelFeeInfo.getServiceFee();
            if(ObjectUtil.isNotEmpty(serviceFee)){
                PriceInfoType fee = serviceFee.getFee();
                hotelVerifyRequest.setServiceFee(fee.getPrice());
            }

        });
        //TODO 资源ID
        hotelVerifyRequest.setResourcesId(request.getRoomId());
        hotelList.add(hotelVerifyRequest);
        verifyTravelStandardRequest.setHotelList(hotelList);
        List<ResourcesVerifyResponse> responses = applyTripClientLoader.verifyTravelStandard(verifyTravelStandardRequest);
        log.info("verifyTravelStandard verifyTravelStandardRequest:{},responses:{}", JsonUtils.toJsonString(verifyTravelStandardRequest) , JsonUtils.toJsonString(responses));

        //调用差标接口校验是否超标
        VerifyTravelStandardBo verifyTravelStandardBo = buildVerifyTravelStandardBo(responses, request.getRoomId());
        log.info("buildVerifyTravelStandardBo verifyTravelStandardBo:{}", JsonUtils.toJsonString(verifyTravelStandardBo));

        return verifyTravelStandardBo;
    }
    
    private String getTotalExtraTaxCurrency(HotelFeeInfoDTO feeSnapshot) {
        return Optional.ofNullable(feeSnapshot)
                .map(HotelFeeInfoDTO::getTaxInfo)
                .map(TaxInfoType::getTaxFeeInfoList)
                .flatMap(item -> item.stream()
                        .filter(a -> a != null && Boolean.FALSE.equals(a.getIncludeInTotalPrice()))
                        .findFirst())
                .map(TaxFeeType::getSellPrice)
                .map(PriceInfoType::getCurrency)
                .orElse(null);
    }
    
    private BigDecimal getTotalExtraTax(HotelFeeInfoDTO feeSnapshot) {
        List<TaxFeeType> taxFeeTypeList = Optional.ofNullable(feeSnapshot)
                .map(HotelFeeInfoDTO::getTaxInfo)
                .map(TaxInfoType::getTaxFeeInfoList).orElse(null);
        if (CollectionUtils.isEmpty(taxFeeTypeList)) {
            return null;
        }
        
        BigDecimal totalExtraTax = BigDecimal.ZERO;
        
        for (TaxFeeType taxFeeType : taxFeeTypeList) {
            if (taxFeeType == null || !Boolean.FALSE.equals(taxFeeType.getIncludeInTotalPrice())) {
                continue;
            }
            
            totalExtraTax = totalExtraTax.add(Optional.ofNullable(taxFeeType.getSellPrice())
                    .map(PriceInfoType::getPrice).orElse(BigDecimal.ZERO));
        }
        
        return totalExtraTax;
    }

    /**
     * 构建VerifyTravelStandardBo对象
     *
     * @param responses ResourcesVerifyResponse对象列表
     * @param roomId    资源ID
     * @return 验证差旅标准的结果对象VerifyTravelStandardBo
     */
    private VerifyTravelStandardBo buildVerifyTravelStandardBo(List<ResourcesVerifyResponse> responses, String roomId) {
        VerifyTravelStandardBo verifyTravelStandardBo = new VerifyTravelStandardBo();
        if (CollectionUtils.isNotEmpty(responses)) {
            Optional<ResourcesVerifyResponse> matchingResponse = responses.stream()
                    .filter(r -> r.getResourcesId().equals(roomId))
                    .findFirst();

            matchingResponse.ifPresent(response -> {
                verifyTravelStandardBo.setVerifyTravelStandard(ObjectUtil.isNotNull(response.getExceed()) && response.getExceed() != 0);
                verifyTravelStandardBo.setExceedAmount(response.getExceedAmount());
            });
        }
        log.info("调用差标接口校验是否超标, verifyTravelStandardBo: {}", JsonUtils.toJsonString(verifyTravelStandardBo));
        return verifyTravelStandardBo;
    }

    private String genFlowKey(String uid, String orgId) {
        return String.format("%s♀%s", orgId, uid);
    }

    /**
     * 根据城市ID列表获取基础城市信息
     *
     * @param cityIdList 城市ID列表
     * @return 基础城市信息对象，若获取失败则返回null
     */
    public BasicCityInfoDto getBasicCityInfoDto(List<String> cityIdList){
        log.info("根据城市ID列表获取基础城市信息,cityIdList:{}", JsonUtils.toJsonString(cityIdList));
        BasicCityListRequest basicCityListRequest = new BasicCityListRequest();
        basicCityListRequest.setCityIdList(cityIdList);
        BasicIntegratedCityResponse basicIntegratedCityResponse = iBasicDataClientOpenFeignDao.listBasicCityInfoByIds(basicCityListRequest);
        log.info("listBasicCityInfoByIds basicCityListRequest:{}, basicIntegratedCityResponse:{}", JsonUtils.toJsonString(basicCityListRequest),
                JsonUtils.toJsonString(basicIntegratedCityResponse));
        BasicCityInfoDto basicCityInfoDto = null;
        // 设置城市信息
        if(ObjectUtil.isNotEmpty(basicIntegratedCityResponse) && CollectionUtils.isNotEmpty(basicIntegratedCityResponse.getCityInfoList())){
            basicCityInfoDto = basicIntegratedCityResponse.getCityInfoList().get(0);
        }
        log.info("getBasicCityInfoDto basicCityInfoDto:{}", JsonUtils.toJsonString(basicCityInfoDto));
        return basicCityInfoDto;
    }

    /**
     * 根据旅游标准token获取酒店旅游标准管理对象
     *
     * @param travelStandardToken 旅游标准token
     * @return 酒店旅游标准管理对象，若未找到则返回null
     */
    private HotelTravelStandardManageVo getHotelTravelStandardByToken(String travelStandardToken) {
        GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
        getTravelStandardByTokenRequest.setTokenList(Arrays.asList(travelStandardToken));
        List<TravelStandardResponseBO> travelStandardResponseList = applyTripClientLoader.getTravelStandardByToken(getTravelStandardByTokenRequest);
        log.info("getTravelStandardByToken getTravelStandardByTokenRequest:{},travelStandardResponseList:{}",
                JsonUtils.toJsonString(getTravelStandardByTokenRequest),JsonUtils.toJsonString(travelStandardResponseList));
        if (CollectionUtils.isNotEmpty(travelStandardResponseList)) {
            TravelStandardResponseBO travelStandardResponse = travelStandardResponseList.get(0);
            if (travelStandardResponse != null && travelStandardResponse.getRuleChainBO() != null) {
                HotelTravelStandardManageVo hotelTravelStandard = new HotelTravelStandardManageVo();
                HotelPriceControl hotelPriceControl = new HotelPriceControl();
                RuleChainBO ruleChainBO = travelStandardResponse.getRuleChainBO();
                OffPeakSeasonRuleVO offPeakSeasonRuleVO = ruleChainBO.getOffPeakSeasonRuleVO();
                CohabitRuleVO cohabitRuleVO = ruleChainBO.getCohabitRuleVO();
                PriceRuleVO priceRuleVO = ruleChainBO.getPriceRuleVO();
                String hotelPriceRcSetResult = getHotelPriceRcSetResult(offPeakSeasonRuleVO, cohabitRuleVO, priceRuleVO);
                hotelPriceControl.setHotelPriceRcSet(hotelPriceRcSetResult);
                hotelTravelStandard.setHotelPriceControl(hotelPriceControl);
                if(cohabitRuleVO!=null){
                    hotelPriceControl.setSharedManageStatus(HotelSwitchEnum.SharedSwitch.E.getCode());
                    hotelPriceControl.setHotelManageStrategy(cohabitRuleVO.getControllerType());
                    hotelPriceControl.setHotelManageRules(cohabitRuleVO.getCalculateType());
                    hotelPriceControl.setSharedPercentage(cohabitRuleVO.getCalculateRatio());
                }
                log.info("getHotelTravelStandardByToken,hotelTravelStandard:{}",JsonUtils.toJsonString(hotelTravelStandard));
                return hotelTravelStandard;
            }
        }
        return null;
    }

    /**
     * 获取酒店价格控制结果
     *
     * @param offPeakSeasonRuleVO 淡季规则
     * @param cohabitRuleVO 同住规则
     * @param priceRuleVO 价格规则
     * @return
     */
    private String getHotelPriceRcSetResult(OffPeakSeasonRuleVO offPeakSeasonRuleVO, CohabitRuleVO cohabitRuleVO, PriceRuleVO priceRuleVO) {
        String hotelPriceRcSetResult = "";
        if (ObjectUtil.isNotEmpty(offPeakSeasonRuleVO)) {
            hotelPriceRcSetResult = JsonUtils.toJsonString(offPeakSeasonRuleVO.getRejectTypes());
        } else if (ObjectUtil.isNotEmpty(cohabitRuleVO)) {
            hotelPriceRcSetResult = JsonUtils.toJsonString(cohabitRuleVO.getRejectTypes());
        } else if (ObjectUtil.isNotEmpty(priceRuleVO)) {
            hotelPriceRcSetResult = JsonUtils.toJsonString(priceRuleVO.getRejectTypes());
        }
        return hotelPriceRcSetResult;
    }

    /**
     * 获取酒店费用快照
     * @return
     */
    private Optional<GetHotelFeeSnapshotResponse> fetchHotelFeeSnapshot(CheckOrderRequestVo request) {
        GetHotelFeeSnapshotRequest getHotelFeeSnapshotRequest = new GetHotelFeeSnapshotRequest();
        getHotelFeeSnapshotRequest.setToken(request.getToken());
        GetHotelFeeSnapshotResponse response = coreServiceClientLoader.getFeeInfoSnapshot(getHotelFeeSnapshotRequest);
        log.info("fetchHotelFeeSnapshot,getHotelFeeSnapshotRequest:{},getFeeInfoSnapshot: {}",JsonUtils.toJsonString(getHotelFeeSnapshotRequest), JsonUtils.toJsonString(response));
        return Optional.ofNullable(response);
    }

    /**
     * 获取酒店产品快照
     * @return
     */
    private Optional<GetHotelProductSnapshotResponse> fetchHotelProductSnapshot(CheckOrderRequestVo request,MbSupplierInfoVo mbSupplierInfoVo) {
        log.info("获取酒店产品快照,request:{},mbSupplierInfoVo:{}",JsonUtils.toJsonString(request),JsonUtils.toJsonString(mbSupplierInfoVo));
        // 创建获取酒店产品快照请求对象
        GetHotelProductSnapshotRequest getHotelProductSnapshotRequest = new GetHotelProductSnapshotRequest();
        getHotelProductSnapshotRequest.setToken(request.getToken());
        FilterRoomInfoType ft = new FilterRoomInfoType();
        ft.setHotelId(request.getHotelId());
        ft.setRoomId(request.getRoomId());
        ft.setPaymentMethod(request.getPaymentType());
        if(ObjectUtil.isNotEmpty(mbSupplierInfoVo)){
            ft.setSupplierCode(mbSupplierInfoVo.getSupplierCode());
        }
        getHotelProductSnapshotRequest.setFilterRoomInfo(ft);
        // 调用核心服务客户端加载器获取酒店产品快照响应
        GetHotelProductSnapshotResponse response = coreServiceClientLoader.getHotelProductSnapshot(getHotelProductSnapshotRequest);
        log.info("获取酒店产品快照,fetchHotelProductSnapshot,getHotelProductSnapshotRequest:{},getHotelProductSnapshot:{}",
                JsonUtils.toJsonString(getHotelProductSnapshotRequest),JsonUtils.toJsonString(response));
        return Optional.ofNullable(response);
    }

    /**
     * 获取酒店查询快照
     * @param request
     * @return
     */
    private Optional<HotelQueryContextModel> fetchHotelContextSnapshot(CheckOrderRequestVo request) {
        HotelQueryContextModel response = hotelSnapshotGateway.getHotelContextSnapshot(request.getToken());
        log.info("fetchHotelContextSnapshot,request:{},getHotelContextSnapshot: {}", JsonUtils.toJsonString(request),JsonUtils.toJsonString(response));
        return Optional.ofNullable(response);
    }

    /**
     * 获取预订配置快照
     * @param request
     * @return
     */
    private Optional<GetBookingConfigByTokenResponse> fetchBookingConfig(CheckOrderRequestVo request) {
        GetBookingConfigByTokenResponse response = hotelCoreOpenFeignDao.getBookingConfig(request.getToken());
        log.info("fetchBookingConfig,getBookingConfig: {}", JsonUtils.toJsonString(response));
        return Optional.ofNullable(response);
    }

    /**
     * 获取快照Snapshot
     * @param request
     * @return
     */
    private Optional<QueryParamModel> fetchSnapshot(CheckOrderRequestVo request) {
        // 调用服务获取快照
        QuerySnapshotResponseDTO snapshot = bookingCoreClientLoader.getSnapshot(request.getToken(), Arrays.asList("query_param", "passenger_data"));
        log.info("fetchSnapshot request:{},getSnapshot:{}",JsonUtils.toJsonString(request) ,JsonUtils.toJsonString(snapshot));

        return Optional.ofNullable(snapshot)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(this::getConverterAndConvert);
    }

    /**
     * 获取转换器并转换快照数据为查询参数模型
     *
     * @param snapShotDTO 快照数据对象
     * @return 转换后的查询参数模型
     */
    private QueryParamModel getConverterAndConvert(SnapShotDTO snapShotDTO) {
        QueryParamV1Converter converter = QueryParamConverterFactoryImpl.getConverter(snapShotDTO);
        return converter.convert(snapShotDTO.getSnapshotData());
    }

    /**
     * 构建订单信息模型
     *
     * @return 订单信息模型
     */
    public OrderInfoModel buildOrderInfo(CheckOrderRequestVo request, GetHotelFeeSnapshotResponse getHotelFeeSnapshotResponse,GetHotelProductSnapshotResponse getHotelProductSnapshotResponse,
                                         HotelQueryContextModel hotelQueryContextModel,GetBookingConfigByTokenResponse bookingConfig,QueryParamModel queryParamModel,
                                         HotelTravelStandardManageVo hotelTravelStandard,Map<Integer, FlowDetail> flowDetailMap,
                                         VerifyTravelStandardBo verifyTravelStandardBo,HotelControlVo  hotelControlVo,MbSupplierInfoVo mbSupplierInfoVo,
                                         List<TravelStandardResponseBO> travelStandardResponseList,OrderInfoModel orderInfoModel){
        log.info("CheckOrderRequestVo request:{}, GetHotelFeeSnapshotResponse getHotelFeeSnapshotResponse:{},GetHotelProductSnapshotResponse getHotelProductSnapshotResponse:{},\n" +
                "                                         HotelQueryContextModel hotelQueryContextModel:{},GetBookingConfigByTokenResponse bookingConfig:{},QueryParamModel queryParamModel:{},\n" +
                "                                         ,HotelTravelStandardManageVo hotelTravelStandard:{},Map<Integer, FlowDetail> flowDetailMap:{},\n" +
                "                                         VerifyTravelStandardBo verifyTravelStandardBo:{}," +
                        "HotelControlVo  hotelControlVo:{},MbSupplierInfoVo mbSupplierInfoVo:{},List<TravelStandardResponseBO> travelStandardResponseList:{}"
                ,JsonUtils.toJsonString(request), JsonUtils.toJsonString(getHotelFeeSnapshotResponse),
                JsonUtils.toJsonString(getHotelProductSnapshotResponse), JsonUtils.toJsonString(hotelQueryContextModel), JsonUtils.toJsonString(bookingConfig),JsonUtils.toJsonString(queryParamModel),
                JsonUtils.toJsonString(hotelTravelStandard), JsonUtils.toJsonString(flowDetailMap),JsonUtils.toJsonString(verifyTravelStandardBo),
                JsonUtils.toJsonString(hotelControlVo), JsonUtils.toJsonString(mbSupplierInfoVo),JsonUtils.toJsonString(travelStandardResponseList));

        log.info("构建订单信息模型,queryParamModel:{}",JsonUtils.toJsonString(queryParamModel));
        if(ObjectUtil.isNotEmpty(queryParamModel)){
            orderInfoModel.setUrgentApply(queryParamModel.getUrgentApply());//是否是紧急预订
            orderInfoModel.setApplyNo(queryParamModel.getApprovalNo());
            orderInfoModel.setTrafficId(StringUtils.isNotBlank(queryParamModel.getTravelNo())?Long.parseLong(queryParamModel.getTravelNo()):null);
            orderInfoModel.setPolicyId(queryParamModel.getPolicyUid());
            orderInfoModel.setPolicyOrgId(queryParamModel.getPolicyOrgId());
            orderInfoModel.setProductType(queryParamModel.getProductType());
        }

        orderInfoModel.setOrderDate(new Date());

        // 设置审批信息
        orderInfoModel.setFlowDetailMap(flowDetailMap);
        orderInfoModel.setCreateApproval(ObjectUtil.isNotNull(flowDetailMap));
        String approvalWay = getApprovalWay(flowDetailMap, verifyTravelStandardBo).toString();
        orderInfoModel.setApprovalWay(approvalWay);
        log.info("设置审批信息,flowDetailMap:{}, approvalWay:{}",JsonUtils.toJsonString(flowDetailMap), approvalWay );

        // 根据差标信息填充同住管控
        if(ObjectUtil.isNotEmpty(hotelTravelStandard)){
            log.info("根据差标信息填充同住管控hotelTravelStandard:",JsonUtils.toJsonString(hotelTravelStandard));
            HotelPriceControl priceControl = hotelTravelStandard.getHotelPriceControl();
            if(ObjectUtil.isNotEmpty(priceControl)){
                orderInfoModel.setSharedManageStatus(priceControl.getSharedManageStatus());
                orderInfoModel.setSharedPercentage(priceControl.getSharedPercentage());
                orderInfoModel.setHotelManageRules(priceControl.getHotelManageRules());
                orderInfoModel.setHotelManageStrategy(priceControl.getHotelManageStrategy());
            }
        }
        orderInfoModel.setTravelStandardToken(request.getToken());
        if(ObjectUtil.isNotNull(verifyTravelStandardBo)){
            // 设置校验差标是否超标信息
            orderInfoModel.setExceedAmount(verifyTravelStandardBo.getExceedAmount());
            orderInfoModel.setExceedTravelStandard(ObjectUtil.isNotNull(verifyTravelStandardBo.isVerifyTravelStandard()) && verifyTravelStandardBo.isVerifyTravelStandard()); //TODO 重复赋值
            log.info("设置校验差标是否超标信息verifyTravelStandardBo:{}",JsonUtils.toJsonString(verifyTravelStandardBo));
        }

        // 设置价格信息 ok
        FeeInfoType feeInfoType = getFeeInfoType(request);
        log.info("FeeInfoType feeInfoType:{}",JsonUtils.toJsonString(feeInfoType));
        if(ObjectUtil.isNotNull(feeInfoType) && ObjectUtil.isNotEmpty(feeInfoType.getOrderAmount())){
            OrderInfoModel.PriceInfo priceInfo = new OrderInfoModel.PriceInfo();
            priceInfo.setAmountCny(feeInfoType.getRoomsAmount().getPrice());
            priceInfo.setOriginAmount(feeInfoType.getOrderAmount().getPrice());
            priceInfo.setCustomAmount(feeInfoType.getOrderAmount().getPrice());
            orderInfoModel.setPriceInfo(priceInfo);
            log.info("orderInfoModel.setPriceInfo.priceInfo:{}",JsonUtils.toJsonString(priceInfo));
        }
        if (ObjectUtil.isNotNull(feeInfoType)) {
            PriceInfoType totalServiceCharge = feeInfoType.getTotalServiceCharge();
            if (totalServiceCharge != null) {
                orderInfoModel.setTotalServiceCharge(totalServiceCharge.getPrice());
            }
            orderInfoModel.setServiceChargeStrategy(feeInfoType.getServiceChargeStrategy());
            orderInfoModel.setServiceChargeStrategyValue(feeInfoType.getServiceChargeStrategyValue());
        }
        log.info("设置差旅标准hotelControlVo:{}",JsonUtils.toJsonString(hotelControlVo));
        if(ObjectUtil.isNotNull(hotelControlVo)){
            //  淡旺季、均价、同住取值规则
            //  开了同住：取淡旺季的价格，按照同住计算规则计算差标 = 同住差标 > 取均价；
            //  没有同住：取淡旺季价格 >  取均价；
            if(ObjectUtil.isNotNull(hotelControlVo.getOffPeakSeasonSet())){
                orderInfoModel.setAmountHigh(new BigDecimal(hotelControlVo.getOffPeakSeasonSet().getPriceCeiling()));
            }else if(ObjectUtil.isNotNull(hotelControlVo.getAveragePriceSet())){
                orderInfoModel.setAmountHigh(new BigDecimal(hotelControlVo.getAveragePriceSet().getPriceCeiling()));
            }
        }
        log.info("设置差旅标准mbSupplierInfoVo:{}",JsonUtils.toJsonString(mbSupplierInfoVo));
        if(ObjectUtil.isNotEmpty(mbSupplierInfoVo)){
            orderInfoModel.setSupplierPhone(mbSupplierInfoVo.getSupplierPhone());
        }

        HotelFeeInfoDTO hotelFeeInfo = getHotelFeeSnapshotResponse.getHotelFeeInfo();
        log.info("getHotelFeeSnapshotResponse.getHotelFeeInfo(),hotelFeeInfo:{}", JsonUtils.toJsonString(hotelFeeInfo));
        
        // 到店另付税费
        BigDecimal totalExtraTax = getTotalExtraTax(hotelFeeInfo);
        String totalExtraTaxCurrency = getTotalExtraTaxCurrency(hotelFeeInfo);
        OrderInfoModel.ExtraTax extraTax = new OrderInfoModel.ExtraTax();
        extraTax.setOriginCurrency(totalExtraTaxCurrency);
        extraTax.setOriginExtraTax(totalExtraTax);
        orderInfoModel.setExtraTax(extraTax);
        Integer roomNum = Optional.ofNullable(hotelQueryContextModel).map(HotelQueryContextModel::getRoomNum).orElse(null);
        Integer dayNum = Optional.ofNullable(hotelFeeInfo).map(HotelFeeInfoDTO::getRoomDailyPriceTypeList).map(List::size).orElse(null);
        if (totalExtraTax != null && roomNum != null && dayNum != null) {
            orderInfoModel.setAvgExtraTax(totalExtraTax.divide(new BigDecimal(roomNum * dayNum), 2, RoundingMode.HALF_UP));
        }
        
        // 供应商下单透传额外信息
        BonusPointInfoType bonusPointInfo = hotelFeeInfo.getBonusPointInfo();
        if (ObjectUtil.isNotNull(bonusPointInfo)) {
            orderInfoModel.setEnabledBonusPoint(true);
        }else {
            orderInfoModel.setEnabledBonusPoint(false);
        }
        log.info("供应商下单透传额外信息,buildHotelFeeInfo,hotelFeeInfo:{}", JsonUtils.toJsonString(hotelFeeInfo));

        // 酒店信息
        HotelBaseInfoDTO hotelBaseInfoDTO = getHotelProductSnapshotResponse.getHotelInfo(); // 酒店信息
        log.info("酒店信息,hotelBaseInfoDTO:{}", JsonUtils.toJsonString(hotelBaseInfoDTO));
        List<BasicRoomInfoDTO> basicRoomInfo = getHotelProductSnapshotResponse.getBasicRoomInfo(); // 基础房型信息
        RoomInfoDTO roomInfoDTO = null;
        if(CollectionUtils.isNotEmpty(basicRoomInfo) && ObjectUtil.isNotEmpty(basicRoomInfo.get(0))){
            List<RoomInfoDTO> roomCardList = basicRoomInfo.get(0).getRoomCardList();
            if(CollectionUtils.isNotEmpty(roomCardList)){
                roomInfoDTO = roomCardList.get(0);
            }
        }
        log.info("基础房型信息,roomInfoDTO:{}", JsonUtils.toJsonString(roomInfoDTO));

        // 根据城市ID列表获取基础城市信息
        String cityId = hotelQueryContextModel.getCityId();
        BasicCityInfoDto basicCityInfoDto = getBasicCityInfoDto(Arrays.asList(cityId));
        log.info("根据城市ID列表获取基础城市信息,basicCityInfoDto:{}", JsonUtils.toJsonString(basicCityInfoDto));

        CancelPolicyType cancelPolicy = null; // 取消政策
        if(ObjectUtil.isNotEmpty(roomInfoDTO) && ObjectUtil.isNotEmpty(roomInfoDTO.getRoomPolicyService())) {
            cancelPolicy = roomInfoDTO.getRoomPolicyService().getCancelPolicy();
        }
        log.info("取消政策,cancelPolicy:{}",JsonUtils.toJsonString(cancelPolicy));
        // 基础房型信息
        BasicRoomInfoDTO basicRoomInfoDTO = null;
        TimeInformationType timeInformationType = null;
        // 酒店信息
        OrderInfoModel.HotelInfo hotelInfo = new OrderInfoModel.HotelInfo();
        // 房间信息
        OrderInfoModel.RoomInfo  roomInfo = new OrderInfoModel.RoomInfo();

        // 按照优先级: 高德、谷歌、百度
        assignMapType(hotelBaseInfoDTO, hotelInfo);

        // 餐食类型
        if(ObjectUtil.isNotNull(hotelFeeInfo)){
            roomInfo.setMealType(hotelFeeInfo.getMealType());
        }

        RoomPolicyServiceDTO roomPolicyService = null; // 房型政策
        List<BasicRoomInfoDTO> basicRoomInfoList = getHotelProductSnapshotResponse.getBasicRoomInfo();
        if (CollectionUtils.isNotEmpty(basicRoomInfoList)) {
            basicRoomInfoDTO = basicRoomInfoList.get(0);

            if(ObjectUtil.isNotEmpty(hotelQueryContextModel.getCheckOutDate()) && ObjectUtil.isNotEmpty(hotelQueryContextModel.getCheckInDate())){
                long betweened = DateUtil.between(DateUtil.parseDate(hotelQueryContextModel.getCheckOutDate()), DateUtil.parseDate(hotelQueryContextModel.getCheckInDate()), DateUnit.DAY);
                roomInfo.setNextDay((int) betweened);
            }
            roomInfo.setAmadeus(null);// mld
//            roomInfo.setCancelModifyNote();
            log.info("基础房型信息,basicRoomInfoDTO:{}", JsonUtils.toJsonString(basicRoomInfoDTO));

            if(ObjectUtil.isNotEmpty(hotelQueryContextModel)){
                log.info("hotelQueryContextModel,hotelQueryContextModel.hotelQueryContextModel:{}", JsonUtils.toJsonString(hotelQueryContextModel));
                orderInfoModel.setCorpPayType(hotelQueryContextModel.getCorpPayType()); // 出行类型，PUB因公、OWN因私
                roomInfo.setCheckInDate(hotelQueryContextModel.getCheckInDate());
                roomInfo.setCheckOutDate(hotelQueryContextModel.getCheckOutDate());
//                roomInfo.setLastArrivalTime(getLastArrivalTime(hotelQueryContextModel.getCheckInDate()));
//                if(ObjectUtil.isNotEmpty(cancelPolicy)){
//                    roomInfo.setLastCancelTime(cancelPolicy.getEndFreeCancelTime());
//                }
//                roomInfo.setPersonCount(hotelQueryContextModel.getPersonNum());
                log.info("基础房型信息,hotelQueryContextModel:{}", JsonUtils.toJsonString(hotelQueryContextModel));
            }

            if(ObjectUtil.isNotNull(roomInfoDTO)){
                log.info("基础房型信息ObjectUtil.isNotNull,roomInfoDTO:{}", JsonUtils.toJsonString(roomInfoDTO));
                orderInfoModel.setProductId(roomInfoDTO.getProductId());
                roomInfo.setHotelId(roomInfoDTO.getHotelId());
                roomInfo.setCityId(roomInfoDTO.getCityId());
                roomInfo.setGroupId(roomInfoDTO.getGroupId());
                roomInfo.setRoomId(roomInfoDTO.getRoomId());
                roomInfo.setBasicRoomId(roomInfoDTO.getBasicRoomId());
                roomInfo.setSupplierCityId(roomInfoDTO.getCityId());
                if(ObjectUtil.isNotEmpty(roomInfoDTO.getRoomPolicyService())){
                    roomPolicyService = roomInfoDTO.getRoomPolicyService();
                    if(ObjectUtil.isNotEmpty(roomPolicyService)){
                        log.info("基础房型信息,roomPolicyService:{}",JsonUtils.toJsonString(roomPolicyService));
                        ApplicativeAreaType applicativeArea = roomPolicyService.getApplicativeArea();
                        if(ObjectUtil.isNotEmpty(applicativeArea)){
                            roomInfo.setApplicativeAreaDesc(StrUtil.join(":",applicativeArea.getName(),applicativeArea.getDesc()));
                            roomInfo.setApplicativeAreaTitle(applicativeArea.getName());
                        }
                        if(ObjectUtil.isNotEmpty(roomPolicyService.getProtocolType()) &&
                                (roomPolicyService.getProtocolType() == 2 || roomPolicyService.getProtocolType() == 3)){
                            roomInfo.setHotelType("C");// 如果是2,3 就转成C  其余都是M  mld
                        }else{
                            roomInfo.setHotelType("M"); // 默认是M
                        }
                        roomInfo.setBreakfast(roomPolicyService.getBreakfastCount());
                        roomInfo.setBreakfastName(ObjectUtil.isNull(roomPolicyService.getBreakfastCount()) || roomPolicyService.getBreakfastCount() == 0?
                                "无早餐":roomPolicyService.getBreakfastCount()+"份早餐");
                        PaymentMethodType paymentMethod = roomPolicyService.getPaymentMethod();
                        Integer protocolType = roomPolicyService.getProtocolType(); // 协议类型  2:平台协议价  3:租户+协议价
                        log.info("协议类型,roomPolicyService.protocolType:{}",protocolType);
                        if(ObjectUtil.isNotNull(protocolType) && protocolType == 2){
                            roomInfo.setProtocolTag("平台协议价");
                        }else if(ObjectUtil.isNotNull(protocolType) && protocolType == 3) {
                            log.info("协议类型,hotelCoreApolloDao.getCompanyShortName:{}",hotelCoreApolloDao.getCompanyShortName());
                            roomInfo.setProtocolTag(hotelCoreApolloDao.getCompanyShortName());
                        }
                        roomInfo.setProtocolType(protocolType);
                        if(ObjectUtil.isNotEmpty(paymentMethod)){
                            roomInfo.setBalanceType(paymentMethod.getType());//房型的结算类型(FG:现付, PP:预付到携程, PH:预付到酒店，UseFG:现转预房型-预付)
                        }
                    }

                }

                RoomPackageType roomPackage =roomInfoDTO.getRoomPackage();
                log.info("房型套餐信息,roomPackage:{}",JsonUtils.toJsonString(roomPackage));
                if(ObjectUtil.isNotNull(roomPackage) && StringUtils.isNotBlank(roomPackage.getPackageId()) &&
                        CollectionUtils.isNotEmpty(roomPackage.getPackageProductList())){
                    roomInfo.setPackageRoom(true); //RoomPackageType.packageId && packageProductList都不为空 true
                    roomInfo.setPackageId(Integer.valueOf(roomPackage.getPackageId()));//RoomPackageType.packageId
                }else{
                    roomInfo.setPackageRoom(false);
                }

                RoomBaseInfoType roomBaseInfo = roomInfoDTO.getRoomBaseInfo();
                log.info("房型基本信息,roomBaseInfo:{}",JsonUtils.toJsonString(roomBaseInfo));
                if(ObjectUtil.isNotEmpty(roomBaseInfo)){
                    roomInfo.setGuestPerson(roomBaseInfo.getMaxGuestNum());// 【房型人数限制】每间房最多可住几人
                    roomInfo.setBedType(roomBaseInfo.getBedDesc());
                    roomInfo.setBasicInfo(getAllRoomBaseInfo(roomBaseInfo));
                }
                RoomPriceType roomPrice = roomInfoDTO.getRoomPrice();
                log.info("房型价格信息,roomPrice:{}",JsonUtils.toJsonString(roomPrice));
                if(ObjectUtil.isNotEmpty(roomPrice)){
//                    roomInfo.setQuantity(roomPrice.getRoomQuantity());
                    if(CollectionUtils.isNotEmpty(roomPrice.getDailyRateList())){
                        DailyRoomPrice dailyRoomPrice = roomPrice.getDailyRateList().get(0);
                        roomInfo.setPrice(dailyRoomPrice.getAvgPriceIncludeTax());
                    }
                }
                
                log.info("roomInfo,roomInfo:{}",JsonUtils.toJsonString(roomInfo));
            }

            // 每日房价信息
            List<RoomDailyPriceType> roomDailyPriceTypeList = hotelFeeInfo.getRoomDailyPriceTypeList();
            log.info("每日房价信息,roomDailyPriceTypeList:{}", JsonUtils.toJsonString(roomDailyPriceTypeList));
            if(CollectionUtils.isNotEmpty(roomDailyPriceTypeList)){
                List<OrderInfoModel.RoomDailyInfo> roomDailyInfoList = new ArrayList<>();
                for(RoomDailyPriceType roomDailyPriceType:roomDailyPriceTypeList){
                    log.info("每日房价信息roomDailyPriceType:{}",JsonUtils.toJsonString(roomDailyPriceType));
                    OrderInfoModel.RoomDailyInfo roomDailyInfo = new OrderInfoModel.RoomDailyInfo();
                    roomDailyInfo.setEffectDate(roomDailyPriceType.getEffectDate());
                    PriceInfoType sellPrice = roomDailyPriceType.getSellPrice();
                    if(ObjectUtil.isNotEmpty(sellPrice)){
                        log.info("每日房价信息,roomDailyPriceType.sellPrice:{}", JsonUtils.toJsonString(sellPrice));
                        roomDailyInfo.setRoomPrice(sellPrice.getPrice());
                    }
                    if(ObjectUtil.isNotNull(roomPolicyService) && ObjectUtil.isNotNull(roomPolicyService.getBreakfastCount())){
                        roomDailyInfo.setBreakfast(roomPolicyService.getBreakfastCount());
                        roomDailyInfo.setBreakfastName(roomPolicyService.getBreakfastCount() == 0?"无早餐":roomPolicyService.getBreakfastCount()+"份早餐");
                    }
                    roomDailyInfo.setServiceFeeList(new ArrayList<>()); //先设置null czl
                    roomDailyInfo.setMeals(roomDailyPriceType.getMealCount());
                    roomDailyInfoList.add(roomDailyInfo);
                }
                orderInfoModel.setRoomDailyInfoList(roomDailyInfoList);
            }

            log.info("母房型信息basicRoomInfoDTO:{}",JsonUtils.toJsonString(basicRoomInfoDTO));
            if(ObjectUtil.isNotEmpty(basicRoomInfoDTO)){
                roomInfo.setPicUrls(basicRoomInfoDTO.getPictureList());
                roomInfo.setName(basicRoomInfoDTO.getBedDesc());
                roomInfo.setRoomName(basicRoomInfoDTO.getName());

                // 母房型信息
                OrderInfoModel.ParentRoomInfo parentRoomInfo = new OrderInfoModel.ParentRoomInfo();
                parentRoomInfo.setNonProtocolMinAvgPrice(basicRoomInfoDTO.getNonProtocolMinAvgPrice());
                parentRoomInfo.setNonProtocolMaxAvgPrice(basicRoomInfoDTO.getNonProtocolMaxAvgPrice());
                parentRoomInfo.setProtocolMinAvgPrice(basicRoomInfoDTO.getProtocolMinAvgPrice());
                parentRoomInfo.setProtocolMinAvgPriceSupplierCode(basicRoomInfoDTO.getProtocolMinAvgPriceSupplierCode());
                roomInfo.setParentRoomInfo(parentRoomInfo);
            }

            if(ObjectUtil.isNotEmpty(hotelFeeInfo)){
                log.info("bonusPointInfoFee,hotelFeeInfo:{}",JsonUtils.toJsonString(hotelFeeInfo));
                roomInfo.setAdditionalSupplierInfo(hotelFeeInfo.getAdditionalSupplierInfo());

                // 设置积分信息
                BonusPointInfoType bonusPointInfoFee = hotelFeeInfo.getBonusPointInfo();
                if(ObjectUtil.isNotEmpty(bonusPointInfoFee)) {
                    log.info("设置积分信息bonusPointInfoFee,bonusPointInfoFee:{}",JsonUtils.toJsonString(bonusPointInfoFee));
                    OrderInfoModel.BonusPointInfo bonusPointInfoResult = new OrderInfoModel.BonusPointInfo();
                    bonusPointInfoResult.setSupplierCode(bonusPointInfoFee.getSupplierCode());
                    bonusPointInfoResult.setGroupId(bonusPointInfoFee.getGroupId());
                    bonusPointInfoResult.setGroupName(bonusPointInfoFee.getGroupName());
                    bonusPointInfoResult.setBonusPointCode(bonusPointInfoFee.getBonusPointCode());
                    bonusPointInfoResult.setBonusPointType(bonusPointInfoFee.getBonusPointType());
                    bonusPointInfoResult.setOrderDetailPageRuleDescList(bonusPointInfoFee.getOrderDetailPageRuleDescList());
                    roomInfo.setBonusPointInfo(bonusPointInfoResult);
                }

                // 设置可选备注列表
                if(ObjectUtil.isNotNull(hotelFeeInfo.getRemarkInfo()) && CollectionUtils.isNotEmpty(hotelFeeInfo.getRemarkInfo().getRemarkList())){
                    List<RemarkType> remarkList = hotelFeeInfo.getRemarkInfo().getRemarkList();
                    log.info("设置可选备注列表remarkList:{}",JsonUtils.toJsonString(remarkList));
                    OrderInfoModel.RemarkInfo remarkInfo = orderInfoModel.getRemarkInfo();
                    if(ObjectUtil.isNotNull(remarkInfo) && CollectionUtils.isNotEmpty(remarkList)){
                        List<OrderInfoModel.OptionalRemark> optionalRemarkList = new ArrayList<>();
                        for (RemarkType remark : remarkList) {
                            OrderInfoModel.OptionalRemark optionalRemark = new OrderInfoModel.OptionalRemark();
                            optionalRemark.setId(remark.getId());
                            optionalRemark.setKey(remark.getKey());
                            optionalRemark.setTitle(remark.getTitle());
                            optionalRemark.setValue(remark.getDesc());
                            optionalRemarkList.add(optionalRemark);
                        }
                        remarkInfo.setOptionalRemarkList(optionalRemarkList);
                    }
                    log.info("设置可选备注列表,remarkInfo:{}",JsonUtils.toJsonString(remarkInfo));
                }

                // 设置时间信息(取消修改时间/最早到店时间/最晚到店时间/最晚保留时间
                timeInformationType = hotelFeeInfo.getTimeInformationType();
                if(ObjectUtil.isNotNull(timeInformationType)){
                    log.info("设置时间信息timeInformationType:{}",JsonUtils.toJsonString(timeInformationType));
                    roomInfo.setEarlyArrivalTime(timeInformationType.getEarlyArrivalTime());
                    roomInfo.setLastArrivalTime(timeInformationType.getLastArrivalTime());
                    roomInfo.setLastCancelTime(timeInformationType.getLastCancelTime());

                    hotelInfo.setEarlyArrivalTime(timeInformationType.getEarlyArrivalTime());
                    hotelInfo.setLastArrivalTime(timeInformationType.getLastArrivalTime());
                    hotelInfo.setLastCancelTime(timeInformationType.getLastCancelTime());
                }
            }
            if(ObjectUtil.isNotEmpty(cancelPolicy)){
                log.info("ObjectUtil.isNotEmpty.cancelPolicy,cancelPolicy:{}",JsonUtils.toJsonString(cancelPolicy));
                roomInfo.setPolicyType(CancelRuleEnum.getTypeByCode(cancelPolicy.getCancelRuleType()));
                List<String> cancelDetailList = CancelPolicyEnum.getCancelDetail(roomInfo.getPolicyType(), cancelPolicy.getEndFreeCancelTime());
                if(CollectionUtils.isNotEmpty(cancelDetailList)){
                    roomInfo.setPolicyDesc(cancelDetailList.stream().collect(Collectors.joining(",")));
                }
            }
            
            // 最少入住天数
            roomInfo.setMinConsecutiveDays(extractMinLOS(getHotelFeeSnapshotResponse));
            
            // 填充钟点房信息
            fillInHourlyRoomInfo(roomInfo, roomInfoDTO, request.getSelectedTimeSlot());

            log.info("orderInfoModel.setRoomInfo,roomInfo:{}",JsonUtils.toJsonString(roomInfo));
            orderInfoModel.setRoomInfo(roomInfo);
        }


        if (ObjectUtil.isNotEmpty(hotelBaseInfoDTO)) {
            log.info("hotelBaseInfoDTO,hotelBaseInfoDTO:",JsonUtils.toJsonString(hotelBaseInfoDTO));
//            if(ObjectUtil.isNotNull(hotelQueryContextModel)){
//                hotelInfo.setLastArrivalTime(getLastArrivalTime(hotelQueryContextModel.getCheckInDate()));
//            }
//            if(ObjectUtil.isNotEmpty(cancelPolicy)){
//                hotelInfo.setLastCancelTime(cancelPolicy.getEndFreeCancelTime());
//            }

            // hotelInfo.remarks
            hotelInfo.setRemarks(request.getTextRemark());

            log.info("hotelInfo,hotelInfo:{}",JsonUtils.toJsonString(hotelInfo));
            hotelInfo.setHotelName(hotelBaseInfoDTO.getName());
            hotelInfo.setIsStarLicence(hotelBaseInfoDTO.getStarLicence());
            hotelInfo.setStar(hotelBaseInfoDTO.getStar());

            if(ObjectUtil.isNotEmpty(roomInfoDTO)){
                log.info("roomInfoDTO,roomInfoDTO:{}",JsonUtils.toJsonString(roomInfoDTO));
                if(ObjectUtil.isNotEmpty(basicCityInfoDto)){
                    hotelInfo.setCityName(basicCityInfoDto.getCityName());
                    hotelInfo.setCountryId(basicCityInfoDto.getCountryId());
                    hotelInfo.setProvinceId(basicCityInfoDto.getProvinceId());
                    // 设置港澳台信息  AreaType 1:海外城市 2:中国大陆城市   countryId  1:中国 2:国外
                    hotelInfo.setHongKongMacaoTaiwan(("1").equalsIgnoreCase(basicCityInfoDto.getAreaType())
                            && ("1").equalsIgnoreCase(basicCityInfoDto.getCountryId()));
                }
                hotelInfo.setSupplierCityId(roomInfoDTO.getCityId());
                log.info("hotelInfo,basicCityInfoDto,hotelInfo:{}",JsonUtils.toJsonString(hotelInfo));

                hotelInfo.setCityId(roomInfoDTO.getCityId());
                hotelInfo.setHotelId(roomInfoDTO.getHotelId());
                hotelInfo.setGroupId(roomInfoDTO.getGroupId());
                hotelInfo.setLocationId(roomInfoDTO.getDistrictId());
                String brandId = roomInfoDTO.getBrandId();
                hotelInfo.setBrandId(brandId); // 品牌Id
                if(StringUtils.isNotBlank(brandId)){
                    BasicGeographyInfoResponse basicGeographyInfoResponse = getBasicGeographyInfoResponse(Arrays.asList(roomInfoDTO.getBrandId()));
                    log.info("getBasicGeographyInfoResponse,basicGeographyInfoResponse:{}",JsonUtils.toJsonString(basicGeographyInfoResponse));
                    // 设置酒店品牌名称
                    if(ObjectUtil.isNotEmpty(basicGeographyInfoResponse) && CollectionUtils.isNotEmpty(basicGeographyInfoResponse.getCorpBrandList()) &&
                            ObjectUtil.isNotEmpty(basicGeographyInfoResponse.getCorpBrandList().get(0))){
                        String brandName = basicGeographyInfoResponse.getCorpBrandList().get(0).getBrandName();
                        hotelInfo.setBrandName(brandName);
                    }
                }
                log.info("roomInfoDTO.getZoneList,roomInfoDTO:{}",JsonUtils.toJsonString(roomInfoDTO));
                if(CollectionUtils.isNotEmpty(roomInfoDTO.getZoneList()) && ObjectUtil.isNotEmpty(roomInfoDTO.getZoneList().get(0))){
                    ZoneType zoneType = roomInfoDTO.getZoneList().get(0);
                    hotelInfo.setZoneId(zoneType.getZoneId());
                    hotelInfo.setZoneName(zoneType.getZoneName());
                }
            }
            hotelInfo.setLocationName(hotelBaseInfoDTO.getDistrictName());
            hotelInfo.setLongitude(ObjectUtil.isEmpty(hotelBaseInfoDTO.getLon()) ? null : hotelBaseInfoDTO.getLon().toString());
            hotelInfo.setLatitude(ObjectUtil.isEmpty(hotelBaseInfoDTO.getLat()) ? null : hotelBaseInfoDTO.getLat().toString());
            hotelInfo.setAddress(hotelBaseInfoDTO.getAddress());
            hotelInfo.setHotelPhone(hotelBaseInfoDTO.getTelephone());

            // 订房提醒
            List<ReservationNoticeType> reservationNoticeList = getHotelProductSnapshotResponse.getReservationNoticeList();
            if(CollectionUtils.isNotEmpty(reservationNoticeList)){
                log.info("getHotelProductSnapshotResponse.getReservationNoticeList,reservationNoticeList:{}",JsonUtils.toJsonString(reservationNoticeList));
                String hotelTips = reservationNoticeList.stream().map(r -> {
                    // 兼容上游title为null,避免在订单详情酒店提示信息null外露
                    if(ObjectUtil.isEmpty(r.getTitle())){
                        return r.getDesc();
                    }
                    return r.getTitle()+":"+r.getDesc();
                }).collect(Collectors.joining("; "));
                hotelInfo.setHotelTips(hotelTips);
            }

        }
        
        // 酒店VAT信息
        assembleVatFlag(hotelInfo, hotelFeeInfo);
        
        orderInfoModel.setHotelInfo(hotelInfo);
        log.info("hotelInfo,setLocationName,hotelInfo:{}",JsonUtils.toJsonString(hotelInfo));

        // 设置供应商信息、支付方式、服务费 ok
        if(ObjectUtil.isNotNull(bookingConfig) && CollectionUtils.isNotEmpty(bookingConfig.getSupplierConfigList())){
            SupplierConfigDTO supplierConfig = bookingConfig.getSupplierConfigList().stream()
                    .filter(s -> s.getSupplierCode().equals(mbSupplierInfoVo.getSupplierCode())).findFirst().orElse(null);
            if(ObjectUtil.isNotNull(supplierConfig)){
                log.info("设置供应商信息、支付方式、服务费supplierConfig,supplierConfig:{}", JsonUtils.toJsonString(supplierConfig));
                orderInfoModel.setSupplierAccountId(supplierConfig.getSupplierAccountId());
                orderInfoModel.setSupplierCode(supplierConfig.getSupplierCode());
                orderInfoModel.setSupplierCorpId(supplierConfig.getSupplierCorpId());
                orderInfoModel.setSupplierName(supplierConfig.getSupplierName());
                orderInfoModel.setSupplierUid(supplierConfig.getSupplierUid());
                orderInfoModel.setResourceMode(Boolean.TRUE.equals(supplierConfig.getDirectSupplier()) ? ResourceModeEnum.DIRECT.getCode() : ResourceModeEnum.SUPPLIER.getCode());
            }
        }

        // 设置超标管控方式
        if(ObjectUtil.isNotNull(getHotelFeeSnapshotResponse.getCalculateResult()) &&
                ObjectUtil.isNotNull(getHotelFeeSnapshotResponse.getCalculateResult().getTravelExceedInfo())){
            Set<String> rejectTypes = getHotelFeeSnapshotResponse.getCalculateResult().getTravelExceedInfo().getRejectTypes();
            log.info("设置超标管控方式,rejectTypes:{}",JsonUtils.toJsonString(rejectTypes));
            // 拒绝策略
            // 【差标接口返回超标管控方式】
            //  F("F", "禁止预订", 0),
            //  C("C", "选择原因后继续预订", 1),
            //  M("M", "支持混付", 3),
            if(CollectionUtils.isNotEmpty(rejectTypes)){
                Integer operateType = null;
                if(rejectTypes.contains(ControlTypeEnum.M.getCode())){
                    operateType = ControlTypeEnum.M.getValue();
                }else if(rejectTypes.contains(ControlTypeEnum.F.getCode())){
                    operateType = ControlTypeEnum.F.getValue();
                }else if(rejectTypes.contains(ControlTypeEnum.C.getCode())){
                    operateType = ControlTypeEnum.C.getValue();
                }
                orderInfoModel.setOperateType(operateType);
            }
        }

        // 设置取消规则
        List<LadderDeductionInfoType> ladderDeductionInfoList = hotelFeeInfo.getLadderDeductionInfoList();
        if(CollectionUtils.isNotEmpty(ladderDeductionInfoList)){
            log.info("设置取消规则,ladderDeductionInfoList:{}",JsonUtils.toJsonString(ladderDeductionInfoList));
            List<OrderInfoModel.OrderCancelRule> orderCancelRule = new ArrayList<>();
            for(LadderDeductionInfoType ladderDeductionInfo : ladderDeductionInfoList){
                OrderInfoModel.OrderCancelRule orderCancel = new OrderInfoModel.OrderCancelRule();
//                orderCancel.setOrderId(); 代码中没有使用的地方
                orderCancel.setDeductionType(ladderDeductionInfo.getDeductionType());
                LadderDeductionDetailType ladderDeductionDetailType = ladderDeductionInfo.getLadderDeductionInfo();
                if(ObjectUtil.isNotNull(ladderDeductionDetailType)){
                    orderCancel.setStartDeductTime(ladderDeductionDetailType.getStartDeductTime());
                    orderCancel.setEndDeductTime(ladderDeductionDetailType.getEndDeductTime());
                    orderCancel.setAmount(ladderDeductionDetailType.getAmount());
                    orderCancel.setDeductionRatio(ladderDeductionDetailType.getDeductionRatio());
                }
                orderCancelRule.add(orderCancel);
            }
            log.info("设置完成取消规则,orderCancelRule:{}",JsonUtils.toJsonString(orderCancelRule));
            orderInfoModel.setOrderCancelRule(orderCancelRule);
            
            // 订单取消政策描述兼容
            List<String> stepCancelDetailList = getStepCancelDetailList(orderCancelRule);
            if (CollectionUtils.isNotEmpty(stepCancelDetailList)) {
                roomInfo.setPolicyType(2);
                roomInfo.setPolicyDesc(String.join(",", stepCancelDetailList));
                log.info("阶梯取消政策赋值 stepCancelDetailList={}", JsonUtils.toJsonString(stepCancelDetailList));
            }
        }
        
        // 超标信息
        orderInfoModel.setExceedTravelStandard(Optional.ofNullable(getHotelFeeSnapshotResponse.getCalculateResult())
                .map(CalculateResultDTO::getTravelExceedInfo)
                .map(TravelExceedInfoType::isExceed).orElse(false));
        orderInfoModel.setExceedAmount(Optional.ofNullable(getHotelFeeSnapshotResponse.getCalculateResult())
                .map(CalculateResultDTO::getFeeInfoMap)
                .map(item -> item.get(Optional.ofNullable(request.getPayInfo()).map(CheckOrderRequestVo.PayInfo::getCode).orElse(null)))
                .map(item -> item.get(0)).map(FeeInfoType::getPriPayAmount)
                .map(PriceInfoType::getPrice)
                .orElse(BigDecimal.ZERO));
        orderInfoModel.setExceedType(Optional.ofNullable(getHotelFeeSnapshotResponse.getCalculateResult())
                .map(CalculateResultDTO::getTravelExceedInfo).map(TravelExceedInfoType::getExceedType)
                .orElse("standardTravelStandard"));
        orderInfoModel.setRoomExceedInfoList(Optional.ofNullable(getHotelFeeSnapshotResponse.getCalculateResult())
                .map(CalculateResultDTO::getTravelExceedInfo).map(TravelExceedInfoType::getRoomExceedInfoList)
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .map(item -> OrderInfoModel.RoomExceedInfo.builder()
                        .exceed(item.isExceed())
                        .exceedType(item.getExceedType()).build())
                .collect(Collectors.toList()));

        log.info("构造orderInfoModel完成,orderInfoModel:{}",JsonUtils.toJsonString(orderInfoModel));
        return orderInfoModel;
    }
    
    private void fillInHourlyRoomInfo(OrderInfoModel.RoomInfo roomInfo, RoomInfoDTO roomInfoDTO, String selectedTimeSlot) {
        if (roomInfo == null || roomInfoDTO == null || roomInfoDTO.getHourlyRoomInfo() == null) {
            return;
        }
        
        HourlyRoomInfoDTO hourlyRoomInfo = roomInfoDTO.getHourlyRoomInfo();
        Boolean hourlyRoom = hourlyRoomInfo.getHourlyRoom();
        roomInfo.setHourlyRoom(hourlyRoom);
        if (!Boolean.TRUE.equals(hourlyRoom)) {
            return;
        }
        
        String checkInDate = roomInfo.getCheckInDate();
        TimeSlot timeSlot = new TimeSlot(selectedTimeSlot, checkInDate);
        
        roomInfo.setCheckInDate(timeSlot.extractCheckInDateStr());
        roomInfo.setCheckOutDate(timeSlot.extractCheckOutDateStr());
        
        OrderInfoModel.HourRoomDetail hourRoomDetail = new OrderInfoModel.HourRoomDetail();
        hourRoomDetail.setCheckInTime(timeSlot.extractCheckInTimeStr());
        hourRoomDetail.setCheckOutTime(timeSlot.extractCheckOutTimeStr());
        hourRoomDetail.setEarliestArriveTime(hourlyRoomInfo.getIntervalStartMinute());
        hourRoomDetail.setLatestLeaveTime(hourlyRoomInfo.getIntervalEndMinute());
        hourRoomDetail.setDuration(hourlyRoomInfo.getDurationHour());
        roomInfo.setHourRoomDetail(hourRoomDetail);
    }
    
    private Integer extractMinLOS(GetHotelFeeSnapshotResponse getHotelFeeSnapshotResponse) {
        return Optional.ofNullable(getHotelFeeSnapshotResponse)
                .map(GetHotelFeeSnapshotResponse::getHotelFeeInfo)
                .map(HotelFeeInfoDTO::getBookingRules)
                .map(BookingRulesDTO::getMinLOS)
                .orElse(null);
    }
    
    /**
     * 为OrderInfoModel.RoomInfo对象分配地图类型
     *
     * @param hotelBaseInfoDTO 酒店基本信息数据传输对象
     * @param hotelInfo 订单信息模型中的房间信息对象
     */
    private static void assignMapType(HotelBaseInfoDTO hotelBaseInfoDTO, OrderInfoModel.HotelInfo hotelInfo) {
        log.info("分配地图类型,hotelBaseInfoDTO:{},hotelInfo:{}",JsonUtils.toJsonString(hotelBaseInfoDTO),JsonUtils.toJsonString(hotelInfo));
        if(ObjectUtil.isNull(hotelBaseInfoDTO) || ObjectUtil.isNull(hotelInfo)) {
            return;
        }
        List<MapInfoDTO> mapInfoList = hotelBaseInfoDTO.getMapInfoList();
        if(CollectionUtils.isEmpty(mapInfoList)){
            log.warn("地图信息列表为空");
            return;
        }
        log.info("地图信息,mapInfoList:{}", JsonUtils.toJsonString(mapInfoList));
        String mapType = "";
        for (MapInfoDTO mapInfoDTO : mapInfoList) {
            if (StrUtil.isNotBlank(mapInfoDTO.getMapType())) {
                if (mapInfoDTO.getMapType().equalsIgnoreCase(MapTypeEnum.GAODE.getCode())) {
                    mapType = MapTypeEnum.GAODE.getCode();
                    break;
                } else if (mapInfoDTO.getMapType().equalsIgnoreCase(MapTypeEnum.GOOGLE.getCode())) {
                    mapType = MapTypeEnum.GOOGLE.getCode();
                    break;
                } else if (mapInfoDTO.getMapType().equalsIgnoreCase(MapTypeEnum.BAIDU.getCode())) {
                    mapType = MapTypeEnum.BAIDU.getCode();
                    break;
                }
            }
        }
        hotelInfo.setMapType(mapType);
    }
    
    private List<String> getStepCancelDetailList(List<OrderInfoModel.OrderCancelRule> orderCancelRuleList) {
        if (CollectionUtils.isEmpty(orderCancelRuleList)) {
            return null;
        }
        List<String> cancelDetailList = new ArrayList<>();
        orderCancelRuleList.forEach((item) -> {
            String deductionType = item.getDeductionType();
            BigDecimal amount = Optional.ofNullable(item.getAmount()).orElse(new BigDecimal(0));
            Date endDeductTime = item.getEndDeductTime();
            Date startDeductTime = item.getStartDeductTime();
            if (DeductionEnum.Free.name().equals(deductionType)) {
                cancelDetailList.add(com.corpgovernment.common.utils.DateUtil.dateToString(endDeductTime, com.corpgovernment.common.utils.DateUtil.DF_YMD_HM) + "之前可以免费取消");
            }
            
            if (DeductionEnum.Ladder.name().equals(deductionType)) {
                cancelDetailList.add(com.corpgovernment.common.utils.DateUtil.dateToString(startDeductTime, com.corpgovernment.common.utils.DateUtil.DF_YMD_HM) + "~" + com.corpgovernment.common.utils.DateUtil.dateToString(endDeductTime, com.corpgovernment.common.utils.DateUtil.DF_YMD_HM) + "收取" + amount + "元取消费");
            }
            
            if (DeductionEnum.CanNotCancelation.name().equals(deductionType)) {
                cancelDetailList.add(com.corpgovernment.common.utils.DateUtil.dateToString(startDeductTime, com.corpgovernment.common.utils.DateUtil.DF_YMD_HM) + "之后不可取消");
            }
            
        });
        return cancelDetailList;
    }

    private void assembleVatFlag(OrderInfoModel.HotelInfo hotelInfo, HotelFeeInfoDTO hotelFeeInfo) {
        if (hotelInfo == null) {
            return;
        }
        
        List<String> invoiceTypeList = Optional.ofNullable(hotelFeeInfo)
                .map(HotelFeeInfoDTO::getInvoiceInfo)
                .map(InvoiceInfoType::getInvoiceList)
                .orElse(null);
        log.info("invoiceTypeList={}", JsonUtils.toJsonString(invoiceTypeList));
        if (CollectionUtils.isEmpty(invoiceTypeList)) {
            return;
        }
        
        String vatFlag = null;
        // 可开专票
        if (invoiceTypeList.stream().anyMatch(item ->
                StringUtils.equalsIgnoreCase(item, InvoiceEnum.VATSpecial.getCode())
                || StringUtils.equalsIgnoreCase(item, InvoiceEnum.DVatInvoice.getCode())
                || StringUtils.equalsIgnoreCase(item, InvoiceEnum.EVATSpecial.getCode()))) {
            vatFlag = "T";
        }
        // 可开普票
        else if (invoiceTypeList.stream().anyMatch(item ->
                StringUtils.equalsIgnoreCase(item, InvoiceEnum.VAT.getCode())
                        || StringUtils.equalsIgnoreCase(item, InvoiceEnum.EVAT.getCode())
                        || StringUtils.equalsIgnoreCase(item, InvoiceEnum.DInvoice.getCode()))) {
            vatFlag = "F";
        }
        hotelInfo.setVatFlag(vatFlag);
        log.info("填充VatFlag vatFlag={}", vatFlag);
    }
    
    /**
     * 获取最晚到店时间
     */
    private String getLastArrivalTime(String time) {
        Date date = com.corpgovernment.common.utils.DateUtil.stringToDate(time, com.corpgovernment.common.utils.DateUtil.DF_YMD);
        if (date == null) {
            return null;
        }
        String result = com.corpgovernment.common.utils.DateUtil.dateToYMD(date) + " 23:59:59";
        log.info("LastArrivalTime取入住当天最后一秒时间：" + result);
        return result;
    }

    /**
     * 根据品牌ID列表获取基础地理信息响应
     *
     * @param brandIds 品牌ID列表
     * @return 基础地理信息响应
     */
    public BasicGeographyInfoResponse getBasicGeographyInfoResponse(List<String> brandIds){
        CorpBrandByBrandIdRequest corpBrandByBrandIdRequest = new CorpBrandByBrandIdRequest();
        corpBrandByBrandIdRequest.setBrandIds(brandIds);
        corpBrandByBrandIdRequest.setType(BasicCorpBrandTypeEnum.CORP_BRAND_SINGLE_BRAND.getType());
        BasicGeographyInfoResponse basicGeographyInfoResponse = iBasicDataClientOpenFeignDao.searchCorpBrandByBrandId(corpBrandByBrandIdRequest);
        log.info("searchCorpBrandByBrandId basicGeographyInfoResponse:{}", JsonUtils.toJsonString(basicGeographyInfoResponse));
        return basicGeographyInfoResponse;
    }


    private Integer getApprovalWay(Map<Integer, FlowDetail> flowDetailMap, VerifyTravelStandardBo verifyTravelStandardBo) {

        if (MapUtil.isEmpty(flowDetailMap)) {
            return ApprovalWayEnum.NO_APPROVAL.getCode();
        }

        if (MapUtil.isNotEmpty(flowDetailMap)) {
            FlowDetail flowDetail;
            if (BooleanUtil.isTrue(verifyTravelStandardBo.isVerifyTravelStandard())) {
                flowDetail = flowDetailMap.get(ApprovalFlowFlagEnum.EXCEED.getCode());
            }else {
                flowDetail = flowDetailMap.get(ApprovalFlowFlagEnum.NORMAL.getCode());
            }
            if(flowDetail == null){
                return ApprovalWayEnum.NO_APPROVAL.getCode();
            }
            if (flowDetail.getApprovalWay()!=null) {
                return flowDetail.getApprovalWay();
            }
        }
        return ApprovalWayEnum.PRESET_APPROVAL.getCode();
    }
    /**
     * 校验是否支持紧急预订并设置紧急支付方式
     *
     * @param corpId 企业ID
     * @param uid 用户ID
     * @param orderInfo 订单信息
     * @throws CorpBusinessException 当紧急预订开关关闭时抛出异常
     */
    private void checkUrgentEnable(String corpId, String uid, OrderInfoModel orderInfo ){
        if(orderInfo.getUrgentApply()==null || Boolean.FALSE.equals(orderInfo.getUrgentApply())){
            return;
        }
        // 校验是否支持紧急预订
        GetSwitchListRequest switchRequest = new GetSwitchListRequest();
        switchRequest.setOrgId(corpId);
        switchRequest.setUId(uid);
        GetAllSwitchResponse switchResponse = switchClientLoader.allSwitch(switchRequest);
        if(switchResponse==null || Boolean.FALSE.equals(switchResponse.getUrgentEnable())){
            throw new CorpBusinessException(HotelResponseCodeEnum.URGENT_APPLY_DISABLE);
        }
        orderInfo.setUrgentPayType(switchResponse.getUrgentPayType());
    }

    /**
     * 从请求对象中拿数据构建orderInfoModel对象
     *
     * @param orderInfoModel 订单信息模型对象
     * @param checkOrderRequestVo        订单校验请求对象
     */
    public void buildRequest(OrderInfoModel orderInfoModel, CheckOrderRequestVo checkOrderRequestVo){
        log.info("从请求对象中拿数据构建orderInfoModel对象,checkOrderRequestVo:",JsonUtils.toJsonString(checkOrderRequestVo));
        if(ObjectUtil.isEmpty(orderInfoModel) || ObjectUtil.isEmpty(checkOrderRequestVo)){
            log.info("从请求对象中拿数据构建orderInfoModel对象,都不能为空");
            return;
        }
        BaseRequestVO.UserInfo userInfo = checkOrderRequestVo.getUserInfo();
        if(ObjectUtil.isEmpty(userInfo)){
            log.info("从请求对象中拿数据构建orderInfoModel对象,userInfo不能为空");
            return;
        }
        orderInfoModel.setCheckCode("1");
        // 设置联系方式
        CheckOrderRequestVo.ContactsInfo contactsInfo = checkOrderRequestVo.getContactsInfo();
        log.info("checkOrderRequestVo.getContactsInfo(),contactsInfo:{}",JsonUtils.toJsonString(contactsInfo));
        if(ObjectUtil.isNotEmpty(contactsInfo)){
            OrderInfoModel.ContactInfo contactInfoResult = new OrderInfoModel.ContactInfo();
//            contactInfoResult.setConfirmType();
            contactInfoResult.setName(contactsInfo.getName());
            contactInfoResult.setEmail(contactsInfo.getEmail());
            contactInfoResult.setMobilePhone(contactsInfo.getPhone());
            contactInfoResult.setMobilePhoneCountryCode(contactsInfo.getCountryCode());
            orderInfoModel.setContactInfo(contactInfoResult);
            log.info("orderInfoModel.setContactInfo(contactInfoResult),contactInfoResult:{}",JsonUtils.toJsonString(contactInfoResult));
        }

        // 设置发票信息
        InvoiceInfoVo invoiceInfo = checkOrderRequestVo.getInvoiceInfo();
        log.info("checkOrderRequestVo.getInvoiceInfo(),invoiceInfo:{}",JsonUtils.toJsonString(invoiceInfo));
        if(ObjectUtil.isNotEmpty(invoiceInfo)){
            OrderInfoModel.InvoiceInfo invoiceInfoResult = new OrderInfoModel.InvoiceInfo();
            invoiceInfoResult.setId(invoiceInfo.getId());
//            invoiceInfoResult.setOrderId();
            invoiceInfoResult.setAccountBank(invoiceInfo.getAccountBank());
            invoiceInfoResult.setAccountCardNo(invoiceInfo.getAccountCardNo());
            invoiceInfoResult.setCorporationAddress(invoiceInfo.getCorporationAddress());
            invoiceInfoResult.setCorporationTel(invoiceInfo.getCorporationTel());
            invoiceInfoResult.setInvoiceTitle(invoiceInfo.getInvoiceTitle());
            invoiceInfoResult.setInvoiceTitleType(invoiceInfo.getInvoiceTitleType());
            invoiceInfoResult.setInvoiceType(invoiceInfo.getInvoiceType());
            invoiceInfoResult.setInvoiceContent(invoiceInfo.getInvoiceContent());
            invoiceInfoResult.setTaxpayerNumber(invoiceInfo.getTaxpayerNumber());
            invoiceInfoResult.setEmail(invoiceInfo.getEmail());
            orderInfoModel.setInvoiceInfo(invoiceInfoResult);
            log.info("orderInfoModel.setInvoiceInfo(invoiceInfoResult),invoiceInfoResult:{}",JsonUtils.toJsonString(invoiceInfoResult));
        }

        orderInfoModel.setUid(userInfo.getUid());
        orderInfoModel.setOrgId(userInfo.getOrgId());
        orderInfoModel.setDeptId(userInfo.getOrgId());
        orderInfoModel.setDeptName(userInfo.getOrgName());
        orderInfoModel.setCorpId(userInfo.getCorpId());
        orderInfoModel.setUname(userInfo.getUsername());
        orderInfoModel.setBookingChannel(userInfo.getSource());
        checkUrgentEnable(userInfo.getCorpId(), userInfo.getUid(),  orderInfoModel); // setUrgentPayType
//        orderInfoModel.setCorpPayType(checkOrderRequestVo.getExpenseType()); // TODO
//        orderInfoModel.setAgentUid(checkOrderRequestVo.getAgentUid()); // TODO
//        orderInfoModel.setApplyNo(checkOrderRequestVo.getApplyNo());
//        orderInfoModel.setTrafficId(checkOrderRequestVo.getTrafficId());
//        orderInfoModel.setOperateType(checkOrderRequestVo.getOperateType()); // TODO
//        orderInfoModel.setExceedTravelStandard(checkOrderRequestVo.isExceedTravelStandard());

        // 备注信息都用前端传过来的
        OrderInfoModel.RemarkInfo remarkInfoResult = new OrderInfoModel.RemarkInfo();
        remarkInfoResult.setCustomRemark(checkOrderRequestVo.getTextRemark());
        orderInfoModel.setRemarkInfo(remarkInfoResult);
        log.info("备注信息都用前端传过来的remarkInfoResult:{}",JsonUtils.toJsonString(remarkInfoResult));

        // 设置支付方式
        String payType = getPayType(checkOrderRequestVo);
        log.info("设置支付方式,payType:{}",payType);
        orderInfoModel.setPayType(payType);
        log.info("设置支付方式,payType:{}",payType);
        // 混付标识预处理
        if (StringUtils.equalsIgnoreCase(payType, PayTypeEnum.MIXPAY.getType())) {
            orderInfoModel.setMixPayType(MixPayTypeEnum.TRAVEL_STANDARD.getCode());
        }

    }

    /**
     * 获取所有房间基础信息
     *
     * @param roomBaseInfoType 房间基础信息类型
     * @return 包含房间基础信息的字符串
     */
    public List<String> getAllRoomBaseInfo(RoomBaseInfoType roomBaseInfoType) {
        List<String> propertyValues = new ArrayList<>();
        propertyValues.add(roomBaseInfoType.getFloorDesc());
        propertyValues.add(roomBaseInfoType.getWindowDesc());
        propertyValues.add(roomBaseInfoType.getSmokeDesc());
        if (roomBaseInfoType.getMaxGuestNum() != null) {
            propertyValues.add(roomBaseInfoType.getMaxGuestNum().toString());
        } else {
            propertyValues.add("");
        }
        propertyValues.add(roomBaseInfoType.getBedDesc());
        propertyValues.add(roomBaseInfoType.getWifiDesc());
        propertyValues.add(roomBaseInfoType.getAreaDesc());
        return propertyValues;
    }

}
