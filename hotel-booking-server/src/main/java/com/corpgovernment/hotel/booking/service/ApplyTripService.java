package com.corpgovernment.hotel.booking.service;

import com.corpgovernment.api.applytrip.enums.ApplyTripTempEnableEnum;
import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficType;
import com.corpgovernment.api.applytrip.soa.response.ApplyTripTrafficVerifyResponse;
import com.corpgovernment.api.applytrip.vo.AoApplyTripStockVo;
import com.corpgovernment.api.applytrip.vo.AoApplyTripTempVo;
import com.corpgovernment.api.applytrip.vo.AoApplyTripTrafficVo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.impl.HotelBasicDataService;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.hotel.booking.bo.ApplyTripControlBo;
import com.corpgovernment.hotel.booking.bo.ApplyTripItemBo;
import com.corpgovernment.mapping.enums.HotelStar;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Service
@Slf4j
public class ApplyTripService {

    @Autowired
    private HotelBasicDataService hotelBasicDataService;

    public List<ApplyTripItemBo> getApplyTripItemList(Long trafficId, String productType) {
        List<ApplyTripItemBo> applyTripItemList = getApplyTripItemList(trafficId, BizTypeEnum.getByCodeOrName(productType));
        if (CollectionUtils.isEmpty(applyTripItemList)) {
            return applyTripItemList;
        }
        
        BizTypeEnum bizTypeEnum = BizTypeEnum.getByCodeOrName(productType);
        
        for (ApplyTripItemBo applyTripItemBo : applyTripItemList) {
            if (applyTripItemBo == null) {
                continue;
            }

            if (StringUtils.equalsIgnoreCase(applyTripItemBo.getCode(), "trafficTypeName")
                    && StringUtils.equalsIgnoreCase(applyTripItemBo.getDesc(), EApplyTripTrafficType.HOTEL.getDesc())
                    && Objects.equals(bizTypeEnum, BizTypeEnum.HOTEL_INTL)) {
                applyTripItemBo.setDesc(EApplyTripTrafficType.HOTELINTL.getDesc());
            }
        }
        return applyTripItemList;
    }
    
    public List<ApplyTripItemBo> getApplyTripItemList(Long trafficId, BizTypeEnum bizTypeEnum) {
        if (trafficId == null) {
            return new ArrayList<>(0);
        }
        ApplyTripTrafficVerifyResponse applyTripTrafficVerify = hotelBasicDataService.getApplyTripTrafficVerify(trafficId);
        log.info("关联申请单信息 applyTripTrafficVerify={}", JsonUtils.toJsonString(applyTripTrafficVerify));
        if (applyTripTrafficVerify == null || applyTripTrafficVerify.getApplyTripTemp() == null
                || applyTripTrafficVerify.getApplyTripTraffic() == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.APPLY_TRIP_GET_ERROR);
        }
        AoApplyTripTempVo applyTripTemp = applyTripTrafficVerify.getApplyTripTemp();
        AoApplyTripTrafficVo traffic = applyTripTrafficVerify.getApplyTripTraffic();
        AoApplyTripStockVo stockVo = applyTripTrafficVerify.getApplyTripStock();
        List<ApplyTripItemBo> applyTripItemList;
        // 事由行程直接返回全部字段
        if(Objects.equals(traffic.getTrafficType(), EApplyTripTrafficType.TRAFFIC.getValue())) {
            applyTripItemList = getApplyTripItemByTraffic(traffic, stockVo);
        } else {
            applyTripItemList = getApplyTripItemByHotel(applyTripTemp, traffic, stockVo, bizTypeEnum);
        }

        // 其他信息
        applyTripItemList.add(ApplyTripItemBo.builder().name("备注信息").desc(traffic.getRemark()).build());
        log.info("trafficId={} bizTypeEnum={} applyTripTrafficVerify={} applyTripItemList={}", trafficId, bizTypeEnum, JsonUtils.toJsonString(applyTripTrafficVerify), JsonUtils.toJsonString(applyTripItemList));
        return applyTripItemList;
    }

    public List<ApplyTripItemBo> checkApplyTripItemList(List<ApplyTripItemBo> applyTripItemList, ApplyTripControlBo applyTripControl) {
        if (CollectionUtils.isEmpty(applyTripItemList) || applyTripControl == null || CollectionUtils.isEmpty(applyTripControl.getWantCheckItemCodeList())) {
            return applyTripItemList;
        }
        log.info("校验出差申请单，applyTripItemList：{}，applyTripControl：{}", JsonUtils.toJsonString(applyTripItemList), JsonUtils.toJsonString(applyTripControl));

        // 对象隔离
        List<ApplyTripItemBo> resultList = new ArrayList<>();

        Set<String> wantCheckItemCodeSet = new HashSet<>(applyTripControl.getWantCheckItemCodeList());
        for (ApplyTripItemBo applyTripItemBo : applyTripItemList) {
            if (applyTripItemBo == null) {
                continue;
            }

            ApplyTripItemBo result = ApplyTripItemBo.builder()
                    .code(applyTripItemBo.getCode())
                    .name(applyTripItemBo.getName())
                    .value(applyTripItemBo.getValue())
                    .desc(applyTripItemBo.getDesc())
                    .needCheck(applyTripItemBo.getNeedCheck()).build();

            // 无需校验
            if (!Boolean.TRUE.equals(applyTripItemBo.getNeedCheck())
                    || !wantCheckItemCodeSet.contains(applyTripItemBo.getCode())) {
                resultList.add(result);
                continue;
            }

            // 星级校验
            String code = applyTripItemBo.getCode();
            if (StringUtils.equalsIgnoreCase(code, "hotelProductType")) {
                String value = applyTripItemBo.getValue() == null ? null : (String) applyTripItemBo.getValue();
                Integer star = applyTripControl.getStar();
                if (StringUtils.equalsIgnoreCase(value, "99")) {
                    continue;
                }
                if (star == null || StringUtils.isBlank(value) || star > Integer.parseInt(value)) {
                    result.setOverLimit(true);
                    result.setOverLimitDesc(star == null ? "未知" : HotelStar.getDisplayHotelStar(star));
                }
            }
            // 金额校验
            else if (StringUtils.equalsIgnoreCase(code, "hotelAveragePrice")) {
                BigDecimal avgAmount = applyTripItemBo.getValue() == null ? null : (BigDecimal) applyTripItemBo.getValue();
                BigDecimal hotelAvgAmount = applyTripControl.getAvgAmount();
                if (avgAmount == null || hotelAvgAmount == null || hotelAvgAmount.compareTo(avgAmount) > 0) {
                    result.setOverLimit(true);
                    result.setOverLimitDesc(hotelAvgAmount == null ? "未知" : "¥" + hotelAvgAmount.stripTrailingZeros().toPlainString());
                }
            }
            // 总金额校验
            else if (StringUtils.equalsIgnoreCase(code, "hotelAmount")) {
                BigDecimal totalAmount = applyTripItemBo.getValue() == null ? null : (BigDecimal) applyTripItemBo.getValue();
                BigDecimal hotelTotalAmount = applyTripControl.getTotalAmount();
                if (totalAmount == null || hotelTotalAmount == null || hotelTotalAmount.compareTo(totalAmount) > 0) {
                    result.setOverLimit(true);
                    result.setName("总金额");
                    result.setOverLimitDesc(hotelTotalAmount == null ? "未知" : "¥" + hotelTotalAmount.stripTrailingZeros().toPlainString());
                }
            }
            // 预算金额校验
            else if (StringUtils.equalsIgnoreCase(code, "amount")) {
                BigDecimal totalAmount = applyTripItemBo.getValue() == null ? null : (BigDecimal) applyTripItemBo.getValue();
                BigDecimal hotelTotalAmount = applyTripControl.getTotalAmount();
                if (totalAmount != null && hotelTotalAmount != null && hotelTotalAmount.compareTo(totalAmount) > 0) {
                    result.setOverLimit(true);
                    result.setOverLimitDesc("¥" + hotelTotalAmount.stripTrailingZeros().toPlainString());
                }
            }
            // 酒店间夜校验
            else if (StringUtils.equalsIgnoreCase(code, "hotelDayCount")) {
                Integer remainDay = applyTripItemBo.getValue() == null ? null : (Integer) applyTripItemBo.getValue();
                Integer requestDays = applyTripControl.getHotelDayCount();
                if (remainDay != null && requestDays != null && requestDays > remainDay) {
                    result.setOverLimit(true);
                    result.setOverLimitDesc(requestDays.toString());
                }
            }

            resultList.add(result);
        }
        log.info("校验出差申请单完成，resultList：{}", JsonUtils.toJsonString(resultList));
        return resultList;
    }

    /**
     * 返回酒店行程类型申请单字段
     * @param applyTripTemp
     * @param applyTripTraffic
     * @param applyTripStock
     * @return
     */
    private List<ApplyTripItemBo> getApplyTripItemByHotel(AoApplyTripTempVo applyTripTemp,
                                                          AoApplyTripTrafficVo applyTripTraffic,
                                                          AoApplyTripStockVo applyTripStock,
                                                          BizTypeEnum bizTypeEnum) {
        // 产线判断
        boolean hotelIntl = Objects.equals(bizTypeEnum, BizTypeEnum.HOTEL_INTL);
        List<ApplyTripItemBo> applyTripItemBoList = new ArrayList<>();
        // 基础信息
        applyTripItemBoList.add(ApplyTripItemBo.builder().code("applyNo").name("核对出差申请单").desc(applyTripTraffic.getTripApplyNo()).build());
        applyTripItemBoList.add(ApplyTripItemBo.builder().code("trafficTypeName").name("预订类型").desc(hotelIntl ? EApplyTripTrafficType.HOTELINTL.getDesc() : EApplyTripTrafficType.HOTEL.getDesc()).build());
        applyTripItemBoList.add(ApplyTripItemBo.builder().code("endCityName").name("入住城市").desc(applyTripTraffic.getEndCityName()).build());
        applyTripItemBoList.add(ApplyTripItemBo.builder().code("trafficData").name("入离日期").desc(hotelBasicDataService.getDateMassage(applyTripTraffic.getStartDate(), applyTripTraffic.getReturnDate())).build());
        // 星级
        Integer hotelProductType = hotelIntl ? applyTripTemp.getHotelIntlProductType() : applyTripTemp.getHotelProductType();
        if (hotelProductType != null && (hotelProductType == 1 || hotelProductType == 2)) {
            ApplyTripItemBo applyTripItemBo = new ApplyTripItemBo();
            applyTripItemBo.setCode("hotelProductType");
            applyTripItemBo.setName("酒店星级");
            String productType = applyTripTraffic.getProductType();
            if (StringUtils.isNotBlank(productType)) {
                applyTripItemBo.setValue(productType);
                applyTripItemBo.setDesc(applyTripTraffic.getProductTypeName());
            } else {
                applyTripItemBo.setDesc("-");
            }
            applyTripItemBo.setNeedCheck(hotelProductType == 2);
            applyTripItemBoList.add(applyTripItemBo);
        }
        // 间夜均价
        Integer hotelAveragePrice =  hotelIntl ? applyTripTemp.getHotelIntlAveragePrice() : applyTripTemp.getHotelAveragePrice();
        if (hotelAveragePrice != null && (hotelAveragePrice == 1 || hotelAveragePrice == 2)) {
            ApplyTripItemBo applyTripItemBo = new ApplyTripItemBo();
            applyTripItemBo.setCode("hotelAveragePrice");
            applyTripItemBo.setName("间夜均价");
            BigDecimal averagePrice = applyTripTraffic.getAveragePrice();
            if (averagePrice != null) {
                applyTripItemBo.setDesc("¥" + averagePrice.stripTrailingZeros().toPlainString());
                applyTripItemBo.setValue(averagePrice);
            } else {
                applyTripItemBo.setDesc("-");
            }
            applyTripItemBo.setNeedCheck(hotelAveragePrice == 2);
            applyTripItemBoList.add(applyTripItemBo);
        }
        // 总金额，如果酒店申请金额校验就不用判断产线是否校验
        Integer hotelAmount = Objects.equals(ApplyTripTempEnableEnum.VERIFY.getCode(), applyTripTemp.getHotelFee())?ApplyTripTempEnableEnum.VERIFY.getCode() : hotelIntl ? applyTripTemp.getHotelIntlAmount() : applyTripTemp.getHotelAmount();
        if (hotelAmount != null && (hotelAmount == 1 || hotelAmount == 2)) {
            ApplyTripItemBo applyTripItemBo = new ApplyTripItemBo();
            applyTripItemBo.setCode("hotelAmount");
            applyTripItemBo.setName("可用金额");
            BigDecimal amount = applyTripStock==null?applyTripTraffic.getAmount():applyTripStock.getInitAmount();
            if (amount != null) {
                BigDecimal subtract = applyTripStock==null?amount:amount.subtract(Null.or(applyTripStock.getUsedAmount(), BigDecimal.ZERO));
                applyTripItemBo.setValue(subtract);
                applyTripItemBo.setDesc("¥" + subtract.stripTrailingZeros().toPlainString() + "(总金额¥" + amount.stripTrailingZeros().toPlainString() + ")");
            } else {
                applyTripItemBo.setDesc("-");
            }
            applyTripItemBo.setNeedCheck(hotelAmount == 2);
            applyTripItemBoList.add(applyTripItemBo);
        }
        return applyTripItemBoList;
    }

    /**
     * 返回事由行程类型申请单字段
     * @param traffic
     * @param stockVo
     * @return
     */
    private List<ApplyTripItemBo> getApplyTripItemByTraffic(AoApplyTripTrafficVo traffic,
                                                          AoApplyTripStockVo stockVo) {
        List<ApplyTripItemBo> list = new ArrayList<>();
        BigDecimal remainAmount = traffic.getAmount() == null?null:traffic.getAmount().subtract(stockVo==null||stockVo.getUsedAmount()==null?BigDecimal.ZERO:stockVo.getUsedAmount());
        Integer remainDay = traffic.getHotelDayCount()==null?null:traffic.getHotelDayCount() - (stockVo==null||stockVo.getUsedHotelDayCount()==null?0:stockVo.getUsedHotelDayCount());
        Integer remainCount = traffic.getCount()==null?null: traffic.getCount().intValue()-(stockVo==null||stockVo.getUsedCount()==null?0:stockVo.getUsedCount());
        // 基础信息
        list.add(ApplyTripItemBo.builder().code("applyNo").name("核对出差申请单").desc(traffic.getTripApplyNo()).build());
        list.add(ApplyTripItemBo.builder().code("trafficTypeName").name("预订类型").desc(EApplyTripTrafficType.TRAFFIC.getDesc()).build());
        list.add(ApplyTripItemBo.builder().code("startCityName").name("出发城市").desc(traffic.getStartCityName()).build());
        list.add(ApplyTripItemBo.builder().code("endCityName").name("到达城市").desc(traffic.getEndCityName()).build());
        list.add(ApplyTripItemBo.builder().code("startDate").name("出发日期").desc(traffic.getEndCityName()).build());
        list.add(ApplyTripItemBo.builder().code("endDate").name("返程日期").desc(traffic.getStartDate()).build());
        list.add(ApplyTripItemBo.builder().code("count").value(remainCount).name("行程次数").needCheck(false).desc(traffic.getCount()==null?"-":remainCount+"/"+traffic.getCount()).build());
        list.add(ApplyTripItemBo.builder().code("hotelDayCount").value(remainDay).name("酒店间夜").needCheck(traffic.getHotelDayCount()!=null).desc(traffic.getHotelDayCount()==null?"-":remainDay+"/"+traffic.getHotelDayCount()).build());
        list.add(ApplyTripItemBo.builder().code("amount").value(remainAmount).name("预算金额").needCheck(traffic.getAmount()!=null).desc(traffic.getAmount()==null?"-":remainAmount.stripTrailingZeros().toPlainString()+"/"+traffic.getAmount().stripTrailingZeros().toPlainString()).build());
        return list;
    }
}
