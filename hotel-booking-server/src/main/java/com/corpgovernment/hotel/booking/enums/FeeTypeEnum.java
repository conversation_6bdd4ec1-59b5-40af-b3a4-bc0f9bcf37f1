package com.corpgovernment.hotel.booking.enums;

/**
 * <AUTHOR>
 * @date 2024/3/21
 */
public enum FeeTypeEnum {
    OWN("OWN", "因私"),
    PUB("PUB", "因公"),
    ;
    /**
     * 收取方式
     */
    private  String feeTypeName;
    /**
     * code
     */
    private  String code;

    FeeTypeEnum(String code, String feeTypeName) {
        this.feeTypeName = feeTypeName;
        this.code = code;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }



    public String getCode() {
        return code;
    }


}
