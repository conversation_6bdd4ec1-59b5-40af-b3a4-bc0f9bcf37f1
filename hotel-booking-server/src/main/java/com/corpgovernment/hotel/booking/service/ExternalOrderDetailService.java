package com.corpgovernment.hotel.booking.service;

import com.corpgovernment.api.applytrip.enums.ApplyTripEmployeeEnum;
import com.corpgovernment.api.applytrip.vo.AoApplyTripTrafficVo;
import com.corpgovernment.api.ordercenter.dto.orderdetail.HotelOrderInfoResponse;
import com.corpgovernment.api.ordercenter.dto.orderdetail.OrderInfoRequest;
import com.corpgovernment.api.ordercenter.dto.orderdetail.OrderInfoResponse;
import com.corpgovernment.api.organization.model.org.OrgInfoVo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.core.domain.openapi.model.enums.OrderStatusEnum;
import com.corpgovernment.hotel.booking.bo.CheckInOutDateInfoBo;
import com.corpgovernment.hotel.product.dataloader.db.*;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.OrganizationClientLoader;
import com.corpgovernment.hotel.product.entity.db.*;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse;
import com.corpgovernment.hotel.product.service.HotelModifyService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.DateUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ExternalOrderDetailService {

    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HoHotelLoader hoHotelLoader;
    @Autowired
    private HoRoomLoader hoRoomLoader;
    @Autowired
    private HoPassengerLoader hoPassengerLoader;
    @Autowired
    private OrderDetailService orderDetailService;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private HoHotelApplyLoader hoHotelApplyLoader;
    @Autowired
    private HoHotelApplyDetailLoader hoHotelApplyDetailLoader;
    @Autowired
    private HotelModifyService hotelModifyService;
    @Autowired
    private OrganizationClientLoader organizationClientLoader;
    @Autowired
    private HoRoomDailyInfoLoader hoRoomDailyInfoLoader;

    public OrderInfoResponse queryHotelOrderDetail(OrderInfoRequest request) {
        try {
            // 校验参数
            checkParams(request);
            // 查询订单信息
            HoOrder hoOrder = hoOrderLoader.selectByOrderId(request.getOrderId());
            if (Objects.isNull(hoOrder)) {
                throw new CorpBusinessException(HotelResponseCodeEnum.ORDER_IS_NULL);
            }
            // 查询酒店信息
            HoHotel hoHotel = hoHotelLoader.selectByOrderId(hoOrder.getOrderId());
            // 查询房型信息
            HoRoom hoRoom = hoRoomLoader.selectByOrderId(hoOrder.getOrderId());
            // 查询每日房间信息
            List<HoRoomDailyInfo> hoRoomDailyInfoList = hoRoomDailyInfoLoader.selectByOrderId(hoOrder.getOrderId());
            // 查询入住人信息
            List<HoPassenger> hoPassengers = hoPassengerLoader.selectByOrderId(hoOrder.getOrderId());
            // 查询供应商订单详情
            OrderDetailResponse.HotelOrderInfo supplierOrderDetail = this.getSupplierOrderDetail(
                hoOrder.getSupplierOrderId(), hoOrder.getSupplierCode(), hoOrder.getSupplierCorpId());

            // 组装返回值
            HotelOrderInfoResponse response = new HotelOrderInfoResponse();
            // 订单信息
            response.setOrderInfo(getOrderInfo(hoOrder));
            // 酒店信息
            HotelOrderInfoResponse.HotelInfo hotelInfo = getHotelInfo(hoHotel, hoRoom);
            // 行程信息
            hotelInfo.setPassengerTripList(getHotelTripPassengerList(hoPassengers, supplierOrderDetail));
            response.setHotelInfo(hotelInfo);
            // 入住人信息
            response.setPassengerInfoList(getPassengerList(hoPassengers));
            // 费用信息
            response.setPayInfo(getPayInfo(hoOrder));
            // 申请修改单详情
            response.setApplyFormDetailList(getApplyFormDetailList(hoOrder.getOrderId(), hoRoom));
            // 退款信息
            response.setRefundInfo(getRefundInfo(hoOrder));
            // 每日房间信息
            response.setDailyRoomInfoList(getDailyRoomInfo(hoOrder, hoRoomDailyInfoList, hoRoom.getRoomQuantity()));
            return OrderInfoResponse.success(response);
        } catch (CorpBusinessException e) {
            log.error("查询酒店订单详情异常", e);
            return OrderInfoResponse.error(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询酒店订单详情异常", e);
            return OrderInfoResponse.error(HotelResponseCodeEnum.SYSTEM_ERROR.code(),
                HotelResponseCodeEnum.SYSTEM_ERROR.message());
        }

    }

    private List<HotelOrderInfoResponse.ApplyFormDetail> getApplyFormDetailList(Long orderId, HoRoom hoRoom) {
        List<HoHotelApply> hoHotelApplyList = hoHotelApplyLoader.select(orderId);
        if (CollectionUtils.isEmpty(hoHotelApplyList)) {
            return Collections.emptyList();
        }
        return hoHotelApplyList.stream().map(hoHotelApply -> {
            HotelOrderInfoResponse.ApplyFormDetail applyFormDetail = new HotelOrderInfoResponse.ApplyFormDetail();
            applyFormDetail.setApplyId(hoHotelApply.getApplyId());
            applyFormDetail
                .setApplyTime(DateUtil.dateToString(hoHotelApply.getDatachangeCreatetime(), DateUtil.DF_YMD_HMS));
            applyFormDetail.setStatus(hoHotelApply.getStatus());
            applyFormDetail.setReasonCode(hoHotelApply.getReasonCode());
            applyFormDetail.setReasonDesc(hoHotelApply.getReasonDesc());
            applyFormDetail.setModifyRoomNight(getModifyRoomNight(hoHotelApply.getApplyId(), hoRoom));
            return applyFormDetail;
        }).collect(Collectors.toList());
    }

    private HotelOrderInfoResponse.ModifyRoomNight getModifyRoomNight(String applyId, HoRoom hoRoom) {
        List<HoHotelApplyDetail> hoHotelApplyDetailList = hoHotelApplyDetailLoader.select(applyId);
        if (CollectionUtils.isEmpty(hoHotelApplyDetailList)) {
            return null;
        }
        HotelOrderInfoResponse.ModifyRoomNight modifyRoomNight = new HotelOrderInfoResponse.ModifyRoomNight();
        hoHotelApplyDetailList.stream().filter(detail -> BooleanUtils.isFalse(detail.getAfterRecord())).findFirst()
            .ifPresent(detail -> {
                modifyRoomNight.setModifyBeforeInfoList(getModifyInfoList(detail, hoRoom));
            });

        hoHotelApplyDetailList.stream().filter(detail -> BooleanUtils.isTrue(detail.getAfterRecord())).findFirst()
            .ifPresent(detail -> {
                modifyRoomNight.setModifyAfterInfoList(getModifyInfoList(detail, hoRoom));
            });
        return modifyRoomNight;
    }

    private List<HotelOrderInfoResponse.ModifyInfo> getModifyInfoList(HoHotelApplyDetail hoHotelApplyDetail,
        HoRoom hoRoom) {
        List<String> datesBetween = hotelModifyService.getDatesBetween(hoHotelApplyDetail.getCheckInDate(),
            DateUtil.addDays(hoHotelApplyDetail.getCheckOutDate(), -1));
        return datesBetween.stream().map(date -> {
            HotelOrderInfoResponse.ModifyInfo modifyInfo = new HotelOrderInfoResponse.ModifyInfo();
            modifyInfo.setRoomDate(date);
            modifyInfo.setRoomQuantity(hoRoom.getRoomQuantity());
            return modifyInfo;
        }).collect(Collectors.toList());
    }

    private HotelOrderInfoResponse.PayInfo getPayInfo(HoOrder hoOrder) {
        HotelOrderInfoResponse.PayInfo payInfo = new HotelOrderInfoResponse.PayInfo();
        payInfo.setTotalAmount(hoOrder.getAmount());
        payInfo.setServiceFee(hoOrder.getServiceFee());
        payInfo.setDeliveryFee(hoOrder.getDeliveryPrice());
        if (PayTypeEnum.MIXPAY.getType().equalsIgnoreCase(hoOrder.getPaytype())) {
            HotelOrderInfoResponse.MixPayInfo mixPayInfo = new HotelOrderInfoResponse.MixPayInfo();
            mixPayInfo.setAPayAmount(hoOrder.getAPayAmount());
            mixPayInfo.setPPayAmount(hoOrder.getPPayAmount());
            payInfo.setMixPayInfo(mixPayInfo);
        }
        if (Objects.nonNull(hoOrder.getCancelFee())) {
            payInfo.setCancelFee(hoOrder.getCancelFee().abs());
        }
        return payInfo;
    }

    private List<HotelOrderInfoResponse.PassengerInfo> getPassengerList(List<HoPassenger> hoPassengers) {
        List<HotelOrderInfoResponse.PassengerInfo> passengerInfoList = new ArrayList<>(hoPassengers.size());
        for (HoPassenger passenger : hoPassengers) {
            HotelOrderInfoResponse.PassengerInfo passengerInfo = new HotelOrderInfoResponse.PassengerInfo();
            passengerInfo.setPassengerName(StringUtils.isNotBlank(passenger.getTravelerName())
                    ? passenger.getTravelerName() : passenger.getPassengerName());
            passengerInfo.setPassengerUid(passenger.getUid());
            passengerInfo
                .setPassengerType(Optional.ofNullable(ApplyTripEmployeeEnum.getByCode(passenger.getEmployeeType()))
                    .map(ApplyTripEmployeeEnum::getName).orElse(StringUtils.EMPTY));
            passengerInfo.setProjectCode(passenger.getProjectCode());
            passengerInfo.setProjectName(passenger.getProjectName());
            passengerInfo.setCostCenterCode(passenger.getCostCenterCode());
            passengerInfo.setCostCenterName(passenger.getCostCenterName());
            passengerInfo.setCardType(passenger.getCardType());
            passengerInfo.setCardNo(passenger.getCardNo());
            passengerInfo.setEmail(passenger.getEmail());
            passengerInfoList.add(passengerInfo);
        }
        return passengerInfoList;
    }

    private HotelOrderInfoResponse.HotelInfo getHotelInfo(HoHotel hoHotel, HoRoom hoRoom) {
        HotelOrderInfoResponse.HotelInfo hotelInfo = new HotelOrderInfoResponse.HotelInfo();
        hotelInfo.setHotelName(hoHotel.getHotelName());
        hotelInfo.setHotelEnName(hoHotel.getHotelEnName());
        hotelInfo.setHotelType(hoHotel.getHotelType());
        hotelInfo.setCityId(hoHotel.getCityId());
        hotelInfo.setCityName(hoHotel.getCityName());
        hotelInfo.setHotelAddress(hoHotel.getAddress());
        hotelInfo.setLatitude(hoHotel.getLatitude());
        hotelInfo.setLongitude(hoHotel.getLongitude());
        hotelInfo.setMapType(hoHotel.getMapType());
        hotelInfo.setHotelPhone(hoHotel.getHotelPhone());
        hotelInfo.setRoomName(hoRoom.getRoomName());
        hotelInfo.setNextDay(hoRoom.getNextDay());
        hotelInfo.setRoomQuantity(hoRoom.getRoomQuantity());
        hotelInfo.setStarLicence(Optional.ofNullable(hoHotel.getIsStarLicence()).orElse(Boolean.FALSE));
        hotelInfo.setStar(hoHotel.getStar());
        hotelInfo.setCheckInDate(Optional.ofNullable((hoRoom.getCheckInDate()))
            .map(e -> DateUtils.format(e, DateUtils.DATE_TIME_FORMAT)).orElse(""));
        hotelInfo.setCheckOutDate(Optional.ofNullable((hoRoom.getCheckOutDate()))
            .map(e -> DateUtils.format(e, DateUtils.DATE_TIME_FORMAT)).orElse(""));
        hotelInfo.setMealType(hoRoom.getMealType());
        hotelInfo.setProtocolType(hoHotel.getProtocolType());
        return hotelInfo;
    }

    private List<HotelOrderInfoResponse.HotelTripPassenger> getHotelTripPassengerList(List<HoPassenger> hoPassengers,
        OrderDetailResponse.HotelOrderInfo supplierOrderDetail) {
        List<HotelOrderInfoResponse.HotelTripPassenger> hotelTripPassengerList = new ArrayList<>(hoPassengers.size());
        Map<String, List<OrderDetailResponse.ClientInfoEntity>> clientInfoMap = new HashMap<>();
        if (null != supplierOrderDetail && CollectionUtils.isNotEmpty(supplierOrderDetail.getClientInfo())) {
            // 获取入住人信息
            clientInfoMap = supplierOrderDetail.getClientInfo()
                .stream().collect(Collectors.groupingBy(OrderDetailResponse.ClientInfoEntity::getClientName));
        }

        for (HoPassenger hoPassenger : hoPassengers) {
            HotelOrderInfoResponse.HotelTripPassenger hotelTripPassenger =
                new HotelOrderInfoResponse.HotelTripPassenger();
            hotelTripPassenger.setPassengerName(StringUtils.isNotBlank(hoPassenger.getTravelerName())
                    ? hoPassenger.getTravelerName() : hoPassenger.getPassengerName());
            hotelTripPassenger.setRoomIndex(hoPassenger.getRoomIndex());
            ApplyTripEmployeeEnum employeeTypeEnum = ApplyTripEmployeeEnum.getByCode(hoPassenger.getEmployeeType());
            if (Objects.isNull(employeeTypeEnum)) { // 未知员工类型
                if (StringUtils.isNotBlank(hoPassenger.getUid())) {
                    hotelTripPassenger.setUID(hoPassenger.getUid());
                } else {
                    hotelTripPassenger.setUID(Objects.nonNull(hoPassenger.getNoEmployeeId())
                        ? String.valueOf(hoPassenger.getNoEmployeeId()) : null);
                }
            } else { // 已知员工类型
                if (ApplyTripEmployeeEnum.EXTERNAL_EMPLOYEE.equals(employeeTypeEnum)) {
                    hotelTripPassenger.setUID(Objects.nonNull(hoPassenger.getNoEmployeeId())
                        ? String.valueOf(hoPassenger.getNoEmployeeId()) : null);
                } else {
                    hotelTripPassenger.setUID(hoPassenger.getUid());
                }
            }

            List<OrderDetailResponse.ClientInfoEntity> clientInfoList = clientInfoMap.get(hoPassenger.getPassengerName());
            if (CollectionUtils.isNotEmpty(clientInfoList) && null != clientInfoList.get(0)
                && StringUtils.isNotBlank(clientInfoList.get(0).getActualCheckInTime())
                && StringUtils.isNotBlank(clientInfoList.get(0).getActualDepartureTime())) {
                hotelTripPassenger.setActualCheckInDate(clientInfoList.get(0).getActualCheckInTime());
                hotelTripPassenger.setActualCheckOutDate(clientInfoList.get(0).getActualDepartureTime());
            }
            hotelTripPassengerList.add(hotelTripPassenger);
        }
        return hotelTripPassengerList;
    }

    private HotelOrderInfoResponse.OrderInfo getOrderInfo(HoOrder hoOrder) {
        HotelOrderInfoResponse.OrderInfo orderInfo = new HotelOrderInfoResponse.OrderInfo();
        orderInfo.setOrderId(hoOrder.getOrderId());
        orderInfo.setOrderStatus(hoOrder.getOrderStatus());
        orderInfo.setSupplierCode(hoOrder.getSupplierCode());
        orderInfo.setSupplierName(hoOrder.getSupplierName());
        orderInfo.setCorpPayType(hoOrder.getCorpPayType());
        orderInfo.setPayType(hoOrder.getPaytype());
        orderInfo.setBookUid(hoOrder.getUid());
        orderInfo.setBookName(hoOrder.getUname());
        orderInfo.setTripApplyNo(hoOrder.getTripApplyNo());
        orderInfo.setInternationHotel(Boolean.FALSE);
        // 客户方行程号
        AoApplyTripTrafficVo applyTripTrafficVo =
            applyTripClientLoader.getApplyTripTrafficById(hoOrder.getTripTrafficId());
        orderInfo
            .setTravelNo(Optional.ofNullable(applyTripTrafficVo).map(AoApplyTripTrafficVo::getTravelNo).orElse(null));
        orderInfo.setBookingTime(hoOrder.getOrderDate() != null ? DateUtil.dateToString(hoOrder.getOrderDate(), DateUtil.DF_YMD_HMS) : "");
        List<OrgInfoVo> orgInfoVos = organizationClientLoader.findOrgInfoOrHistoryByOrgIds(Stream
            .of(hoOrder.getCorpId(), hoOrder.getDeptId()).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        orgInfoVos.forEach(orgInfoVo -> {
            if (StringUtils.isBlank(orgInfoVo.getOrgId())) {
                return;
            }
            if (Optional.ofNullable(hoOrder.getCorpId()).orElse(StringUtils.EMPTY).equals(orgInfoVo.getOrgId())) {
                orderInfo.setBookerCorpName(orgInfoVo.getName());
            }
            if (Optional.ofNullable(hoOrder.getDeptId()).orElse(StringUtils.EMPTY).equals(orgInfoVo.getOrgId())) {
                orderInfo.setBookerDeptName(orgInfoVo.getName());
            }
        });
        orderInfo.setContactName(hoOrder.getContactName());
        orderInfo.setContactPhone(hoOrder.getContactMobilePhone());
        orderInfo.setContactEmail(hoOrder.getContactEmail());
        return orderInfo;
    }

    private HotelOrderInfoResponse.RefundInfo getRefundInfo(HoOrder hoOrder) {
        if (Optional.ofNullable(hoOrder.getRefundAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        HotelOrderInfoResponse.RefundInfo refundInfo = new HotelOrderInfoResponse.RefundInfo();
        refundInfo.setTotalRefundAmount(hoOrder.getRefundAmount());
        return refundInfo;
    }

    private void checkParams(OrderInfoRequest request) {
        if (Objects.isNull(request.getOrderId()) || request.getOrderId() <= 0) {
            throw new CorpBusinessException(HotelResponseCodeEnum.ORDER_ID_IS_NULL);
        }
    }

    private OrderDetailResponse.HotelOrderInfo getSupplierOrderDetail(String supplierOrderId, String supplierCode,
        String supplierCorpId) {
        try {
            return orderDetailService.searchOrder(supplierOrderId, supplierCode, supplierCorpId);
        } catch (Exception e) {
            log.error("查询供应商订单详情异常", e);
        }
        return null;
    }


    private List<HotelOrderInfoResponse.DailyRoomInfo> getDailyRoomInfo(HoOrder hoOrder, List<HoRoomDailyInfo> hoRoomDailyInfoList, Integer roomQuantity) {
        List<HotelOrderInfoResponse.DailyRoomInfo> dailyRoomInfoList = hoRoomDailyInfoList.stream().map(item -> {
            HotelOrderInfoResponse.DailyRoomInfo dailyRoomInfo = new HotelOrderInfoResponse.DailyRoomInfo();
            dailyRoomInfo.setDate(DateUtil.dateToString(item.getEffectDate(), DateUtil.DF_YMD));
            dailyRoomInfo.setMeals(item.getMeals());
            return dailyRoomInfo;
        }).collect(Collectors.toList());

        // 订单为取消 每个日期的房间信息的 剩余房间数为0 退款房间数为roomQuantity
        String orderStatus = hoOrder.getOrderStatus();
        boolean orderCancelled = OrderStatusEnum.CA.getCode().equals(orderStatus);
        if (orderCancelled) {
            dailyRoomInfoList.forEach(dailyRoomInfo -> {
                dailyRoomInfo.setRemainQuantity(0);
                dailyRoomInfo.setRefundQuantity(roomQuantity);
            });
            return dailyRoomInfoList;
        }

        // 订单非取消 每个日期的房间信息的 修改掉的日期 = 剩余房间数为0 退款房间数为roomQuantity / 非修改的日期   剩余房间数为roomQuantity 退款房间数为0
        String newestCheckInOutDateStr = hoOrder.getNewestCheckInOutDate();
        boolean oldData = StringUtils.isBlank(newestCheckInOutDateStr);
        if (oldData) {
            return dailyRoomInfoList;
        }

        List<CheckInOutDateInfoBo> checkInOutDateInfoBoList = JsonUtils.parseArray(newestCheckInOutDateStr, CheckInOutDateInfoBo.class);
        checkInOutDateInfoBoList = checkInOutDateInfoBoList.stream().filter(item -> Objects.nonNull(item.getCheckInDate()) && Objects.nonNull(item.getCheckOutDate()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(checkInOutDateInfoBoList)) {
            List<String> newestBookingDateList = checkInOutDateInfoBoList.stream().flatMap(item -> getRoomNightBetween(item.getCheckInDate(), item.getCheckOutDate()).stream()).collect(Collectors.toList());
            dailyRoomInfoList.forEach(dailyRoomInfo -> {
                boolean notModifyDate = newestBookingDateList.contains(dailyRoomInfo.getDate());
                dailyRoomInfo.setRemainQuantity(notModifyDate ? roomQuantity : 0);
                dailyRoomInfo.setRefundQuantity(notModifyDate ? 0 : roomQuantity);
            });
        }
        return dailyRoomInfoList;
    }

    private List<String> getRoomNightBetween(Date startDate, Date endDate) {
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DF_YMD, Locale.getDefault());
        List<String> dates = new ArrayList<>();
        for (int i = 0; i < java.time.temporal.ChronoUnit.DAYS.between(startLocalDate, endLocalDate); i++) {
            LocalDate date = startLocalDate.plusDays(i);
            String dateString = date.format(formatter);
            dates.add(dateString);
        }
        return dates;
    }
}
