package com.corpgovernment.hotel.booking.config.apollo;

import cn.hutool.core.util.StrUtil;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class OrderSnapshotDataConfig {

    @Value("${order-snapshot-data-grey-tenantId:}")
    private String orderSnapshotDataGreyTenantId;

    @Value("${order-snapshot-data-booking-config-attr:}")
    private String orderSnapshotDataBookingConfigAttr;

    public List<String> getOrderSnapshotDataGreyTenantId() {
        if (StringUtils.isBlank(orderSnapshotDataGreyTenantId)) {
            return Collections.emptyList();
        }
        return Arrays.stream(orderSnapshotDataGreyTenantId.split(StrUtil.COMMA)).collect(Collectors.toList());
    }

    public List<String> getOrderSnapshotDataBookingConfigAttr() {
        if (StringUtils.isBlank(orderSnapshotDataBookingConfigAttr)) {
            return Collections.emptyList();
        }
        return Arrays.stream(orderSnapshotDataBookingConfigAttr.split(StrUtil.COMMA)).collect(Collectors.toList());
    }

}
