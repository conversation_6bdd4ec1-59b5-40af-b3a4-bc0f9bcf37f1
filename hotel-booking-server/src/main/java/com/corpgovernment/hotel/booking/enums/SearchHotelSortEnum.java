package com.corpgovernment.hotel.booking.enums;

public enum SearchHotelSortEnum {

    DEFAULT_SORT(0, "默认排序"),
    SEARCH_PRICE_SORT_ASC(1,"价格从低到高"),
    SEARCH_PRICE_SORT_DESC(2,"价格从高到低"),
    SEARCH_DISTANCE_SORT_ASC(3,"距离由近到远"),
    SEARCH_DISTANCE_SORT_DESC(4,"距离由远到近"),
    SEARCH_STAR_SORT_ASC(5,"星级从低到高"),
    SEARCH_STAR_SORT_DESC(6,"星级从高到低"),
    ;

    private Integer type;
    private String desc;

    SearchHotelSortEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
