package com.corpgovernment.hotel.booking.service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.corpgovernment.api.hotel.product.model.enums.CancelOrderRuleEnum;
import com.corpgovernment.api.hotel.product.enums.OrderSnapshotDataTypeEnum;
import com.corpgovernment.api.order.common.supplement.CommonSupplementService;
import com.corpgovernment.api.organization.model.org.OrgAccountingUnitCategoryConfig;
import com.corpgovernment.core.domain.model.snapshot.product.BasicRoomInfoModel;
import com.corpgovernment.core.domain.model.snapshot.product.ProductSnapshotModel;
import com.corpgovernment.core.domain.model.snapshot.product.RoomInfoModel;
import com.corpgovernment.core.domain.model.snapshot.product.RoomPolicyServiceInfo;
import com.corpgovernment.dto.snapshot.dto.hotel.*;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelProductSnapshotResponse;
import com.corpgovernment.orgsdk.client.ContentDicitionaryClient;
import com.corpgovernment.orgsdk.response.AgreementInfoQueryResp;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.applytrip.enums.ApplyTripEmployeeEnum;
import com.corpgovernment.api.applytrip.soa.request.ApplyTripPersonRequest;
import com.corpgovernment.api.applytrip.soa.request.GetProjectInfoByTravelerRequest;
import com.corpgovernment.api.applytrip.soa.response.ApplyTripPersonResponse;
import com.corpgovernment.api.applytrip.soa.response.GetProjectInfoByTravelerResponse;
import com.corpgovernment.api.applytrip.vo.AoApplyTripTrafficVo;
import com.corpgovernment.api.approvalsystem.bean.ExecutionDetail;
import com.corpgovernment.api.approvalsystem.service.ApprovalSystemClient;
import com.corpgovernment.api.approvalsystem.service.response.IsTaskProcessingResponse;
import com.corpgovernment.api.basic.enums.SourceEnum;
import com.corpgovernment.api.comment.dto.OrderInfoResponse;
import com.corpgovernment.api.costcenter.model.TempCostCenterVo;
import com.corpgovernment.api.hotel.booking.enums.HotelClockTypeEnum;
import com.corpgovernment.api.hotel.booking.initpage.response.InvoiceInfoVo;
import com.corpgovernment.api.hotel.booking.orderdetail.QueryOrderListHotelResponse;
import com.corpgovernment.api.hotel.booking.orderdetail.request.CancelOrderRequestDto;
import com.corpgovernment.api.hotel.booking.orderdetail.request.QueryOrderDetailRequestDto;
import com.corpgovernment.api.hotel.booking.orderdetail.request.ReCreateInvoiceRequestVo;
import com.corpgovernment.api.hotel.booking.orderdetail.request.SaveSpecialNeedRequestVo;
import com.corpgovernment.api.hotel.booking.orderdetail.response.*;
import com.corpgovernment.api.hotel.product.model.enums.HotelTypeEnum;
import com.corpgovernment.api.hotel.product.model.enums.InvoiceTypeEnum;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.hotel.product.model.request.QueryOrderDetailRequestBo;
import com.corpgovernment.api.hotel.product.model.request.ReCreateInvoiceRequestBo;
import com.corpgovernment.api.hotel.product.model.response.*;
import com.corpgovernment.api.organization.enums.SwitchEnum;
import com.corpgovernment.api.organization.model.employee.EmployeeInfoBo;
import com.corpgovernment.api.organization.model.org.OrgInfoVo;
import com.corpgovernment.api.organization.model.switchinfo.GetSwitchListRequest;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoRequest;
import com.corpgovernment.api.platform.soa.paymentbill.PaymentBillDetailRequest;
import com.corpgovernment.api.platform.soa.paymentbill.PaymentBillDetailResponse;
import com.corpgovernment.api.platform.soa.paymentbill.PpPaymentBillDto;
import com.corpgovernment.api.platform.soa.request.SearchPassengerRequest;
import com.corpgovernment.api.platform.soa.response.SearchPassengerResponse;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierCompanyBo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.api.supplier.vo.MbSupplierInfoVo;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.*;
import com.corpgovernment.common.pay.PPayUtil;
import com.corpgovernment.common.pay.PayTokenInfo;
import com.corpgovernment.common.utils.BaseUtils;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.constants.TravelStandardOwnerTypeEnum;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.corpgovernment.dto.travelstandard.response.TravelStandardTokenResponse;
import com.corpgovernment.dto.travelstandard.response.rule.*;
import com.corpgovernment.hotel.booking.bo.CheckInOutDateInfoBo;
import com.corpgovernment.hotel.booking.bo.CohabitStandardBo;
import com.corpgovernment.hotel.booking.enums.*;
import com.corpgovernment.hotel.booking.util.SwitchUtil;
import com.corpgovernment.hotel.product.dataloader.db.*;
import com.corpgovernment.hotel.product.dataloader.soa.*;
import com.corpgovernment.hotel.product.entity.db.HoHotelApply;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.entity.db.HoOrderClockRecord;
import com.corpgovernment.hotel.product.entity.db.HoPassenger;
import com.corpgovernment.hotel.product.external.client.SupplierSoaClient;
import com.corpgovernment.hotel.product.external.constant.SupplierConstant;
import com.corpgovernment.hotel.product.external.dto.order.cancel.StandardCancelOrderInquiryResponse;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModificationInquiryRequest;
import com.corpgovernment.hotel.product.external.dto.order.modify.StandardOrderModificationInquiryResponse;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse.ClientInfoEntity;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse.HotelOrderInfo;
import com.corpgovernment.hotel.product.service.HoOrderDetailService;
import com.corpgovernment.hotel.product.service.HotelModifyService;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.corpgovernment.hotel.product.supplier.CtripSlSupplier;
import com.corpgovernment.hotel.product.supplier.enums.InvoiceEnum;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.utils.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

import static com.corpgovernment.core.service.impl.HotelListService.THREE_PARTY_CONTRACT;
import static com.corpgovernment.core.service.impl.HotelListService.TWO_PARTY_CONTRACT;

@Service
@Slf4j
public class OrderDetailService {
    public static final String CONTROL_TYPE = "超标管控方式";
    public static final String PRICE = "价格";
    public static final String STAR = "星级";
    public static final String BRAND = "品牌";
    public static final String NO_LIMIT = "不限";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String HOTEL_ENABLE_VISIT_CONTROL_ENABLE = "1";
    private static final String HOTEL_ORDER_MODIFY_CANCEL_CONTROL_ENABLE = "1";

    @Autowired
    private HoOrderDetailService orderDetailService;
    @Autowired
    private PayBillClientLoader payBillClientLoader;
    @Autowired
    private ServiceSatisfactionCommentClientLoader serviceSatisfactionCommentClientLoader;
    @Autowired
    private SupplierDataClientLoader supplierDataClientLoader;
    @Autowired
    private OrganizationClientLoader organizationClientLoader;
    @Autowired
    private SwitchUtil switchUtil;
    @Autowired
    private ApprovalSystemClient approvalSystemService;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private CancelOrderService cancelOrderService;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HoPassengerLoader hoPassengerLoader;
    @Autowired
    private CommonService commonService;
    @Autowired
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    @Autowired
    private HoHotelApplyLoader hoHotelApplyLoader;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private BasicDataClientLoader basicDataClientLoader;
    @Autowired
    private CtripSlSupplier ctripSlSupplier;
    @Autowired
    @Qualifier(value = "queryThreadPoolExecutor")
    private ThreadPoolExecutor queryThreadPoolExecutor;
    @Autowired
    private ApprovalSystemClientLoader approvalSystemClientLoader;
    @Autowired
    private TravelStandardClientLoader travelStandardClientLoader;
    @Autowired
    private SupplierSoaClient supplierSoaClient;
    @Autowired
    private HotelModifyService hotelModifyService;
    @Autowired
    private TravelStandardService travelStandardService;
    @Autowired
    private HoOrderClockRecordLoader hoOrderClockRecordLoader;
    @Autowired
    private OrganizationEmployeeClientLoader organizationEmployeeClientLoader;
    @Autowired
    private SupplierCompanyClientLoader supplierCompanyClientLoader;
    @Autowired
    private HoOrderCancelFormLoader hoOrderCancelFormLoader;
    @Autowired
    private SwitchClientLoader switchClientLoader;
    @Autowired
    private ContentDicitionaryClient contentDicitionaryClient;

    private static final Long SECOND_1000 = 1000L;

    private static final String TX_DEDUCTION = "K";

    private static final String TX_REFUND = "T";

    public static final String AGREEMENT_TAG_ENABLE = "1";

    /**
     * 金额管控
     */
    private final static String[] STANDARD_LOW_PRICE= {"lowPrice", "均价上限"};
    /**
     * 星级管控
     */
    private final static String[] STANDARD_STAR= {"star", "可订星级"};
    /**
     * 超标
     */
    private final static String STANDARD_VERIFY_RESULT_1 = "1";


    public OrderDetailResponseVo queryOrderDetail(QueryOrderDetailRequestDto queryOrderDetailRequestDto) {
        // 查询订单基本详情
        OrderDetailResponseBo orderDetail = getOrderInfo(queryOrderDetailRequestDto);
        // 数据转换
        OrderDetailResponseVo orderDetailResponseVo = new OrderDetailResponseVo();
        HoOrderBo hoOrderBo = orderDetail.getOrderInfo();
        HoHotelBo hoHotelBO = orderDetail.getHoHotelInfo();
        HoRoomBo hoRoomBo = orderDetail.getHoRoomInfo();
        HoHotelLowBo hoHotelLowBo = orderDetail.getHoHotelLow();
        List<HoRoomDailyBo> hoRoomDailyBoList = orderDetail.getHoRoomDailyInfoList();

        // 差标token
        orderDetailResponseVo.setTravelStandardToken(hoOrderBo.getTravelStandardToken());

        // 查询支付单
        PaymentBillDetailRequest paymentBillRequest = new PaymentBillDetailRequest();
        paymentBillRequest.setOrderId(String.valueOf(hoOrderBo.getOrderId()));
        PaymentBillDetailResponse paymentBillResponse = null;
        // 查询退款单
        OrderFeeDetail orderFeeDetail = new OrderFeeDetail();
        orderFeeDetail.setServiceFee(hoOrderBo.getServiceFee());
        List<PpPaymentBillDto> paymentBillDtoList = payBillClientLoader.paymentBillInfo(hoOrderBo.getOrderId());
        // 前台现付且存在前收服务费
        boolean cashAndHasServiceFee = Objects.equals(hoOrderBo.getPayType(), PayTypeEnum.CASH.getType()) && Objects.nonNull(hoOrderBo.getServiceFee()) && hoOrderBo.getServiceFee().compareTo(BigDecimal.ZERO) > 0;
        // 因私设置支付时间
        if (Objects.equals(hoOrderBo.getPayType(), PayTypeEnum.PPAY.getType())
                || Objects.equals(hoOrderBo.getPayType(), PayTypeEnum.MIXPAY.getType()) || cashAndHasServiceFee) {
            // 系统时间
            Date date = new Date();
            long nowTime = date.getTime();

            // 预定时间
            Date ticketDate =
                    paymentBillDtoList.stream().filter(e -> PayStatusEnum.U.getType().equalsIgnoreCase(e.getStatus()))
                            .findFirst().map(PpPaymentBillDto::getTxTime).orElse(new Date());
            long yd = ticketDate.getTime();

            // 系统设定支付时间（毫秒）
            Long payTimeLimit = switchUtil.getPayTimeLimitMillisecond();

            long l = (payTimeLimit + yd - nowTime) / SECOND_1000;

            orderDetailResponseVo.setRestTime(l);

            orderDetailResponseVo
                .setDeadlinePayTime(DateUtil.dateToString(new Date(payTimeLimit + yd), DateUtil.DF_YMD_HMS));
        }

        BigDecimal refundFeeAmount = null;
        if (CollectionUtils.isNotEmpty(paymentBillDtoList)) {
            paymentBillResponse = new PaymentBillDetailResponse();
            String payStatus = null;
            for (PpPaymentBillDto paymentBillDto : paymentBillDtoList) {
                String payType = paymentBillDto.getTxType();
                if (payStatus == null && "K".equals(payType)) {
                    payStatus = paymentBillDto.getStatus();
                }
                if (!"K".equals(payType)) {
                    if (refundFeeAmount == null) {
                        refundFeeAmount = BigDecimal.ZERO;
                    }
                    refundFeeAmount = refundFeeAmount.add(paymentBillDto.getAmount());
                }
            }
            orderFeeDetail.setRefundAmount(refundFeeAmount);
            paymentBillResponse.setStatus(payStatus);
        }
        orderFeeDetail.setAPayAmount(hoOrderBo.getAPayAmount());
        orderFeeDetail.setPPayAmount(hoOrderBo.getPPayAmount());
        HoDeliveryInfoBo hoDeliveryInfoBo = orderDetail.getHoDeliveryInfoBo();
        HoInvoiceBo hoInvoiceBo = orderDetail.getHoInvoiceInfo();
        List<HoPassengerBo> hoPassengerBoList = orderDetail.getHoPassengerList();
        log.info("人员信息为：{}", JsonUtils.toJsonString(hoPassengerBoList));
        List<OrderPassengerVo> passengerNameList = new ArrayList<>();
        OrderPassengerVo orderPassengerVo = null;
        Map<String, GetProjectInfoByTravelerResponse> projectMapByUidMap = new HashMap<>();
        // 判断当前订单是否关联出差单 如有关联则查询出当前订单入住人得项目信息
        if (StringUtils.isNotBlank(orderDetail.getOrderInfo().getTripApplyNo())) {
            List<String> uids = hoPassengerBoList.stream().map(HoPassengerBo::getUid).collect(Collectors.toList());
            GetProjectInfoByTravelerRequest request = new GetProjectInfoByTravelerRequest();
            uids.addAll(hoPassengerBoList.stream().map(HoPassengerBo::getNoEmployeeId).map(String::valueOf)
                    .collect(Collectors.toList()));
            request.setUids(uids);
            request.setApplyNo(orderDetail.getOrderInfo().getTripApplyNo());
            request.setTrafficId(orderDetail.getOrderInfo().getTripTrafficId());
            List<GetProjectInfoByTravelerResponse> projectInfoByTraveler =
                    applyTripClientLoader.getProjectInfoByTraveler(request);
            projectMapByUidMap = projectInfoByTraveler.stream()
                    .collect(Collectors.toMap(GetProjectInfoByTravelerResponse::getUid, item -> item));

            List<ApplyTripPersonResponse> applyTripPerson = applyTripClientLoader
                    .getApplyTripPerson(new ApplyTripPersonRequest(orderDetail.getOrderInfo().getTripApplyNo(), null));
            Map<String, List<ApplyTripPersonResponse>> applyTripPersonMap =
                    applyTripPerson.stream().collect(Collectors.groupingBy(item -> item.getUid()));
            hoPassengerBoList.forEach(item -> {
                if (StringUtils.isBlank(item.getUid())) {
                    item.setUid(item.getNoEmployeeId() + "");
                }
                // 取最新的出差申请单中的备注，所以会覆盖
                List<ApplyTripPersonResponse> applyTripPersonResponses = applyTripPersonMap.get(item.getUid());
                if (CollectionUtils.isNotEmpty(applyTripPersonResponses) && applyTripPersonResponses.size() > 0) {
                    item.setWbsRemark(applyTripPersonResponses.get(0).getWbsRemark());
                    item.setCostCenterRemark(applyTripPersonResponses.get(0).getCostCenterRemark());
                }
            });

        }
        for (HoPassengerBo hoPassengerBo : hoPassengerBoList) {
            orderPassengerVo = new OrderPassengerVo();
            orderPassengerVo.setPassengerName(hoPassengerBo.getPassengerName());
            orderPassengerVo.setTravelerName(hoPassengerBo.getTravelerName());
            orderPassengerVo.setStaffFlag(StringUtils.isNotBlank(hoPassengerBo.getUid()));
            orderPassengerVo.setWbsRemark(hoPassengerBo.getWbsRemark());
            orderPassengerVo.setCostCenterName(hoPassengerBo.getCostCenterName());
            orderPassengerVo.setCostCenterRemark(hoPassengerBo.getCostCenterRemark());
            orderPassengerVo.setCostCenterCode(hoPassengerBo.getCostCenterCode());
            orderPassengerVo.setCostCenterId(hoPassengerBo.getCostCenterId());
            orderPassengerVo.setProjectCode(hoPassengerBo.getProjectCode());
            orderPassengerVo.setProjectName(hoPassengerBo.getProjectName());
            orderPassengerVo.setProjectId(hoPassengerBo.getProjectId());
            orderPassengerVo.setNoSelectProjectDesc(hoPassengerBo.getNoSelectProjectDesc());
            // 根据uid 拿到当前入住人项目信息
            // String mapKey = StringUtil.isBlank(hoPassengerBo.getUid()) ? hoPassengerBo.getNoEmployeeId().toString() :
            // hoPassengerBo.getUid();
            // if (projectMapByUidMap.get(mapKey) != null) {
            // GetProjectInfoByTravelerResponse getProjectInfoByTravelerResponse = projectMapByUidMap.get(mapKey);
            // orderPassengerVo.setProjectCode(getProjectInfoByTravelerResponse.getProjectCode());
            // orderPassengerVo.setProjectName(getProjectInfoByTravelerResponse.getProjectName());
            // orderPassengerVo.setNoSelectProjectDesc(getProjectInfoByTravelerResponse.getNoSelectProjectDesc());
            // }
            String mapKey = StringUtils.isBlank(hoPassengerBo.getUid()) ? hoPassengerBo.getNoEmployeeId().toString()
                    : hoPassengerBo.getUid();
            if (projectMapByUidMap.get(mapKey) != null) {
                GetProjectInfoByTravelerResponse getProjectInfoByTravelerResponse = projectMapByUidMap.get(mapKey);
                orderPassengerVo.setProjectCode(getProjectInfoByTravelerResponse.getProjectCode());
                orderPassengerVo.setProjectName(getProjectInfoByTravelerResponse.getProjectName());
                orderPassengerVo.setNoSelectProjectDesc(getProjectInfoByTravelerResponse.getNoSelectProjectDesc());
            }
            orderPassengerVo.setEmployeeType(hoPassengerBo.getEmployeeType());
            orderPassengerVo.setEmployeeDesc(ApplyTripEmployeeEnum.getNameByCode(hoPassengerBo.getEmployeeType()));
            orderPassengerVo.setRoomIndex(hoPassengerBo.getRoomIndex());
            orderPassengerVo.setCardNo(hoPassengerBo.getCardNo());
            orderPassengerVo.setEmail(hoPassengerBo.getEmail());
            orderPassengerVo.setPassengerUid(hoPassengerBo.getUid());

            orderPassengerVo.setCostCenterVoList(convertMultiCostCenter(hoPassengerBo.getMultiCostCenterDataJSON()));
            orderPassengerVo.setAccountingUnitList(convertAccountingUnitInfo(hoPassengerBo.getAccountingUnitJSON()));
            passengerNameList.add(orderPassengerVo);
        }

        // 联系人信息
        ContactInfo contactInfo = new ContactInfo();
        contactInfo.setCountryCode(hoOrderBo.getContactCountryCode());
        contactInfo.setEmail(hoOrderBo.getContactEmail());
        contactInfo.setName(hoOrderBo.getContactName());
        contactInfo.setPhone(hoOrderBo.getContactMobilePhone());
        orderDetailResponseVo.setContactInfo(contactInfo);
        orderDetailResponseVo.setCorpPayTypeName(CropPayTypeEnum.getLongName(hoOrderBo.getCorpPayType()));
        orderDetailResponseVo.setCorpPayType(hoOrderBo.getCorpPayType());
        orderDetailResponseVo.setOrderCreateTime(DateUtil.dateToString(hoOrderBo.getOrderDate(), DateUtil.DF_YMD_HM));
        orderDetailResponseVo.setPassengerList(passengerNameList);
        orderDetailResponseVo.setOrderId(hoOrderBo.getOrderId());
        orderDetailResponseVo.setApprovalId(hoOrderBo.getApprovalId());
        OrderHotelVo orderHotelVo = new OrderHotelVo();
        orderHotelVo.setOrderId(hoOrderBo.getOrderId());
        orderHotelVo.setHotelName(hoHotelBO.getHotelName());
        orderHotelVo.setHotelEnName(hoHotelBO.getHotelEnName());
        orderHotelVo.setCancelNote(hoRoomBo.getCancelModifyNote());
        if (Objects.equals(OrderSourceEnum.APP.name(), queryOrderDetailRequestDto.getSource())) {
            orderHotelVo.setCheckInDate(DateUtil.dateToString(hoRoomBo.getCheckInDate(), DateUtil.DF_MD_CN));
            orderHotelVo.setCheckOutDate(DateUtil.dateToString(hoRoomBo.getCheckOutDate(), DateUtil.DF_MD_CN));
        } else {
            orderHotelVo.setCheckInDate(DateUtil.dateToString(hoRoomBo.getCheckInDate(), DateUtil.DF_YMD));
            orderHotelVo.setCheckOutDate(DateUtil.dateToString(hoRoomBo.getCheckOutDate(), DateUtil.DF_YMD));
        }
        orderHotelVo.setCheckInDateNew(DateUtil.dateToString(hoRoomBo.getCheckInDate(), DateUtil.DF_YMD));
        orderHotelVo.setCheckOutDateNew(DateUtil.dateToString(hoRoomBo.getCheckOutDate(), DateUtil.DF_YMD));
        orderHotelVo.setNextDay(hoRoomBo.getNextDay());
        orderHotelVo.setNewLastArrival(DateUtil.dateToString(hoHotelBO.getLastArrivalTime(), DateUtil.DF_YMD_HM));
        orderHotelVo.setEarlyArrivalTime(DateUtil.dateToString(hoHotelBO.getEarlyArrivalTime(), DateUtil.DF_YMD_HM));
        orderHotelVo.setRoomQuantity(hoRoomBo.getRoomQuantity());
        orderHotelVo.setRoomName(hoRoomBo.getRoomName());
        orderHotelVo.setPersonCount(hoRoomBo.getPersonCount());
        orderHotelVo.setHotelTips(hoHotelBO.getHotelTips());
        orderHotelVo.setHotelAddress(hoHotelBO.getAddress());
        orderHotelVo.setLatitude(String.valueOf(hoHotelBO.getLatitude()));
        orderHotelVo.setLongitude(String.valueOf(hoHotelBO.getLongitude()));
        orderHotelVo.setHotelPhone(hoHotelBO.getHotelPhone());
        orderHotelVo.setBedName(hoRoomBo.getBedName());
        orderHotelVo.setBreakfast(hoRoomDailyBoList.get(0).getBreakfastName());
        orderHotelVo.setHotelType(hoHotelBO.getHotelType());
        orderHotelVo.setRemarks(hoHotelBO.getRemarks());
        orderHotelVo.setPackageRoom(Optional.ofNullable(hoRoomBo.getPackageRoom()).orElse(Boolean.FALSE));
        orderHotelVo.setPackageId(hoRoomBo.getPackageId());
        orderHotelVo.setPicUrls(StringUtils.isNotBlank(hoRoomBo.getPicUrls())
                ? JsonUtils.parseArray(hoRoomBo.getPicUrls(), String.class) : Collections.emptyList());
        orderHotelVo.setRoomBasicInfo(StringUtils.isNotBlank(hoRoomBo.getRoomInfoContext())
                ? JsonUtils.parseArray(hoRoomBo.getRoomInfoContext(), String.class) : Collections.emptyList());
        orderHotelVo.setPackageProductList(StringUtils.isNotBlank(hoRoomBo.getPackageRoomContext())
                ? JsonUtils.parseArray(hoRoomBo.getPackageRoomContext(), OrderHotelVo.PackageRoomProductInfo.class)
                : Collections.emptyList());
        orderHotelVo.setOtherInfo(this.packageOtherInfo(hoRoomBo));
        orderHotelVo.setProtocolType(hoHotelBO.getProtocolType());
        AgreementInfoQueryResp content = getContent();
        if (hoHotelBO.getProtocolType()!=null&&content!=null){
            if(TWO_PARTY_CONTRACT.equals(hoHotelBO.getProtocolType())) {
                if (StringUtils.isNotBlank(content.getTwoContent())&&content.getTwoSwitch().equals(AGREEMENT_TAG_ENABLE)){
                    orderHotelVo.setProtocolTag(content.getTwoContent());
                }
            }else if (THREE_PARTY_CONTRACT.equals(hoHotelBO.getProtocolType())){
                if (StringUtils.isNotBlank(content.getThreeContent())&&content.getThreeSwitch().equals(AGREEMENT_TAG_ENABLE)){
                    orderHotelVo.setProtocolTag(content.getThreeContent());
                }
            }
        }
        log.info("orderHotelVo.getProtocolTag：{}" , JsonUtils.toJsonString(orderHotelVo.getProtocolTag()));
        orderHotelVo.setLogoPicUrl(hoHotelBO.getLogoPicUrl());
        // 查询服务商信息
        MbSupplierInfoVo supplierInfo =
                Optional.ofNullable(supplierDataClientLoader.findBySupplierCode(hoOrderBo.getSupplierCode()))
                        .orElse(new MbSupplierInfoVo());
        orderDetailResponseVo.setSupplierHotLine(supplierInfo.getHotLine());
        orderDetailResponseVo.setOrderHotelVo(orderHotelVo);
        orderDetailResponseVo.setUrgentApply(BooleanUtils.isTrue(hoOrderBo.getUrgentApply()));
        String provideBillDesc = null;
        if (PayTypeEnum.ACCNT.getType().equals(hoOrderBo.getPayType())) {
            provideBillDesc = "发票统一寄送给公司，无需单独配送";
        } else if (PayTypeEnum.PPAY.getType().equals(hoOrderBo.getPayType()) || PayTypeEnum.MIXPAY.getType().equals(hoOrderBo.getPayType())) {
            if (hoInvoiceBo != null) {
                provideBillDesc = "电子发票将在您离店后发送到联系人邮箱:" + hoOrderBo.getContactEmail();
            }
        }
        // 报销凭证
        ProvideBillInfoVo provideBillInfoVo = null;
        if (hoInvoiceBo != null) {
            provideBillInfoVo = new ProvideBillInfoVo();
            BeanUtils.copyProperties(hoInvoiceBo, provideBillInfoVo);
            // 处理类型
            String invoiceTypeDesc = InvoiceEnum.getDesc(hoInvoiceBo.getInvoiceType());
            if(StringUtils.isBlank(invoiceTypeDesc)){
                log.error("+++++ illegal invoice type:{}", hoInvoiceBo.getInvoiceType());
            }
            provideBillInfoVo.setInvoiceType(invoiceTypeDesc);
            orderDetailResponseVo.setProvideBillFlag(true);
        } else {
            orderDetailResponseVo.setProvideBillFlag(false);
        }

        if (hoDeliveryInfoBo != null && hoInvoiceBo != null) {
            provideBillInfoVo.setDeliveryAddress(hoDeliveryInfoBo.getAddress());
            provideBillInfoVo.setDeliveryProvinceName(hoDeliveryInfoBo.getProvinceName());
            provideBillInfoVo.setDeliveryDistrictName(hoDeliveryInfoBo.getDistrictName());
            provideBillInfoVo.setDeliveryCityName(hoDeliveryInfoBo.getCityName());
            provideBillInfoVo.setDeliveryFee(hoOrderBo.getDeliveryPrice());
            provideBillInfoVo.setDeliveryTypeName(DeliveryTypeEnum.getByCode(hoOrderBo.getDeliveryType()).getDesc());
        }
        orderDetailResponseVo.setProvideBillInfoVo(provideBillInfoVo);

        orderFeeDetail.setTotalAmount(hoOrderBo.getAmount());
        orderFeeDetail.setDeliveryAmount(hoOrderBo.getDeliveryPrice());
        RoomDailyInfoVo roomDailyInfoVo = null;
        List<RoomDailyInfoVo> roomDailyInfoVoList = new ArrayList<>();
        for (HoRoomDailyBo hoRoomDailyBo : hoRoomDailyBoList) {
            roomDailyInfoVo = new RoomDailyInfoVo();
            roomDailyInfoVo.setBookingDate(DateUtil.dateToString(hoRoomDailyBo.getEffectDate(), DateUtil.DF_YMD));
            roomDailyInfoVo.setBreakfastName(hoRoomDailyBo.getBreakfastName());
            roomDailyInfoVo.setPrice(hoRoomDailyBo.getRoomPrice());
            roomDailyInfoVoList.add(roomDailyInfoVo);
        }
        orderFeeDetail.setRoomDailyInfoVoList(roomDailyInfoVoList);
        orderDetailResponseVo.setOrderFeeDetail(orderFeeDetail);
        orderDetailResponseVo.setProvideBillDesc(provideBillDesc);
        if (Objects.equals(TravelEnum.OWN.getType(), hoOrderBo.getCorpPayType())) {
            orderDetailResponseVo.setProvideBillDesc(null);
        }
        orderDetailResponseVo.setSpecialNeed(hoOrderBo.getSpecialNeed());
        orderDetailResponseVo.setOrderStatus(hoOrderBo.getOrderStatus());
        orderDetailResponseVo.setOrderStatusName(OrderStatusEnum.getEnum(hoOrderBo.getOrderStatus()).getName());
        orderDetailResponseVo.setPayType(hoOrderBo.getPayType());
        orderDetailResponseVo.setPayTypeName(PayTypeEnum.get(hoOrderBo.getPayType()).getName());
        orderDetailResponseVo.setPayChannel(hoOrderBo.getPayChannel() == null ? null
                : PayTypeEnum.getByTypeIgnoreCase(hoOrderBo.getPayChannel()).getName());
        orderDetailResponseVo.setPayChannelName(hoOrderBo.getPayChannel() == null ? null
                : PayTypeEnum.getByTypeIgnoreCase(hoOrderBo.getPayChannel()).getName());
        orderDetailResponseVo.setSupplierName(hoOrderBo.getSupplierName());
        orderDetailResponseVo.setSupplierCode(hoOrderBo.getSupplierCode());
        orderDetailResponseVo.setSource(hoOrderBo.getSource());
        orderDetailResponseVo.setCancelNote(hoRoomBo.getCancelPolicyDesc());
        orderDetailResponseVo
                .setCancelTypeDesc(Optional.ofNullable(HotelCancelEnum.getByCode(hoRoomBo.getCancelPolicyType()))
                        .map(e -> e.getDesc()).orElse(StringUtils.EMPTY));
        orderDetailResponseVo.setCancelModifyNote(hoRoomBo.getCancelModifyNote());
        orderDetailResponseVo.setSupplierOrderId(hoOrderBo.getSupplierOrderId());
        orderDetailResponseVo.setTripApplyNo(hoOrderBo.getTripApplyNo());
        orderDetailResponseVo.setFileList(buildFileList(orderDetail));
        orderDetailResponseVo.setRefundAmount(hoOrderBo.getRefundAmount());
        if (HotelCancelReasonEnum.getViewByCode(hoOrderBo.getCancelReasonCode())) {
            orderDetailResponseVo.setCancelReasonCode(hoOrderBo.getCancelReasonCode());
            orderDetailResponseVo
                    .setCancelReason(HotelCancelReasonEnum.getReasonByCode(hoOrderBo.getCancelReasonCode()));
            orderDetailResponseVo.setCancelReasonDesc(
                    HotelCancelReasonEnum.getViewDescByCode(hoOrderBo.getCancelReasonCode(),
                            this.getCancelReasonDesc(hoOrderBo)));
        }
        RcInfoVo rcInfoVo = new RcInfoVo();
        if (hoHotelLowBo != null) {
            rcInfoVo.setAmountHigh(hoHotelLowBo.getAmountHigh());
            rcInfoVo.setReason(hoHotelLowBo.getReason());
            rcInfoVo.setReasonNote(hoHotelLowBo.getReasonNote());
            orderDetailResponseVo.setRcReason(rcInfoVo);
        }

        // 订单状态为ED或者（订单状态为TA并且当前时间大于离店时间）
        boolean isEnd = OrderStatusEnum.ED.getCode().equals(hoOrderBo.getOrderStatus())
                || (OrderStatusEnum.TA.getCode().equals(hoOrderBo.getOrderStatus())
                && hoRoomBo.getCheckOutDate().before(new Date()));

        if (isEnd) {
            OrderInfoResponse orderInfoResponse = serviceSatisfactionCommentClientLoader
                    .getMySatisfactionComment(hoOrderBo.getOrderId(), queryOrderDetailRequestDto.getUid());
            if (orderInfoResponse != null) {
                CommentDataVo commentDataVo = new CommentDataVo();
                commentDataVo.setComment(orderInfoResponse.getComment());
                commentDataVo.setGrade(orderInfoResponse.getGrade().stream()
                        .map(e -> CommentDataVo.Grade.builder().star(e.getStar()).title(e.getTitle()).build())
                        .collect(Collectors.toList()));
                orderDetailResponseVo.setCommentData(commentDataVo);
                orderDetailResponseVo.setCommentFlag(false);
            } else {
                orderDetailResponseVo.setCommentFlag(true);
            }
        }
        String orderStatus = hoOrderBo.getOrderStatus();

        // 补开发票标识
        Boolean reCreateInvoiceFlag = reCreateInvoiceFlag(orderStatus, hoInvoiceBo, hoOrderBo.getPayType(),
                paymentBillResponse, hoOrderBo.getLadderAmount(), hoOrderBo);
        orderDetailResponseVo.setReCreateInvoiceFlag(reCreateInvoiceFlag);
        if (OrderStatusEnum.PW.getCode().equals(orderStatus)) {
            orderDetailResponseVo
                    .setPayTokenInfo(createPayTokenInfo(hoOrderBo, hoHotelBO, hoRoomBo, hoPassengerBoList));
        }
        orderDetailResponseVo.setBookingUid(hoOrderBo.getUid());
        orderDetailResponseVo.setAgentUid(hoOrderBo.getAgentUid());
        orderDetailResponseVo.setAsyncCancel(hoOrderBo.getAsyncCancel());
        // 获取配置 若勾选则允许出行人修改 若没有勾选 则不允许出行人修改
        boolean travelerCancellationAndChangeAuthorityControl = getTravelerCancellationAndChangeAuthorityControl(hoOrderBo.getCorpId(), hoOrderBo.getUid());
        log.info("出行人是否有修改权限：{}", travelerCancellationAndChangeAuthorityControl);
        // 查询供应商取消问询接口
        cancelOrderQueryNew(orderDetailResponseVo, hoOrderBo.getCorpId(), travelerCancellationAndChangeAuthorityControl);
        // 返回审批按钮
        orderDetailResponseVo.setTaskProcessing(false);
        if (SourceEnum.APP.name().equals(queryOrderDetailRequestDto.getSource())
                && StringUtils.isNotBlank(queryOrderDetailRequestDto.getTaskId())) {
            IsTaskProcessingResponse isTaskProcessingResponse =
                    approvalSystemService.isTaskProcessing(queryOrderDetailRequestDto.getTaskId());
            if (isTaskProcessingResponse != null && isTaskProcessingResponse.isTaskProcessing()) {
                orderDetailResponseVo.setTaskProcessing(true);
            }
        }
        try {
            orderDetailResponseVo.setModifyType(HotelModifyTypeEnum.NON.getCode());
            orderDetailResponseVo.setForceModify(Boolean.FALSE);
            orderDetailResponseVo.setAllowTravelerModify(Boolean.TRUE);
            Optional
                .ofNullable(
                    canModifyOrder(queryOrderDetailRequestDto.getOrderId(), orderDetailResponseVo.getSupplierOrderId()))
                .ifPresent(item -> {
                    boolean canEarlyDeparture = "T".equalsIgnoreCase(item.getCanEarlyDeparture());
                    boolean canEarlyDepartureAheadLimit = "T".equalsIgnoreCase(item.getCanEarlyDepartureAheadLimit());
                    orderDetailResponseVo.setCanModify(canEarlyDeparture || canEarlyDepartureAheadLimit);
                    if (!orderDetailResponseVo.getCanModify()) {
                        orderDetailResponseVo.setModifyType(HotelModifyTypeEnum.NON.getCode());
                    } else if (canEarlyDeparture) {
                        orderDetailResponseVo.setModifyType(HotelModifyTypeEnum.APPLY_MODIFY.getCode());
                    } else if (canEarlyDepartureAheadLimit) {
                        orderDetailResponseVo.setModifyType(HotelModifyTypeEnum.MODIFY.getCode());
                    }
                    if (CollectionUtils.isNotEmpty(item.getModifyReasonList())) {
                        orderDetailResponseVo.setModifyReasonList(item.getModifyReasonList().stream().map(reason -> {
                            OrderDetailResponseVo.ModifyReason modifyReason = new OrderDetailResponseVo.ModifyReason();
                            modifyReason.setReasonCode(reason.getReasonCode());
                            modifyReason.setReasonDesc(reason.getReasonDesc());
                            return modifyReason;
                        }).collect(Collectors.toList()));
                    }
                    //供应商返回不可修改 且订单最新间夜数不等于1时 需要获取配置是否支持强制修改
                    boolean cannotModify = HotelModifyTypeEnum.NON.getCode().equals(orderDetailResponseVo.getModifyType());
                    boolean onlyOneRoomNight = false;
                    String newestCheckInOutDateStr = hoOrderBo.getNewestCheckInOutDate();
                    if (StringUtils.isNotBlank(newestCheckInOutDateStr)) {
                        List<CheckInOutDateInfoBo> checkInOutDateInfoBoList = JsonUtils.parseArray(newestCheckInOutDateStr, CheckInOutDateInfoBo.class);
                        if (CollectionUtils.isNotEmpty(checkInOutDateInfoBoList)) {
                            Integer actualRoomNight = checkInOutDateInfoBoList.stream().map(checkInOutDateInfoBo -> DateUtil.betweenDay(checkInOutDateInfoBo.getCheckInDate(), checkInOutDateInfoBo.getCheckOutDate()))
                                    .reduce(0, Integer::sum);
                            onlyOneRoomNight = actualRoomNight == 1;
                        }
                    }
                    if (cannotModify && !onlyOneRoomNight) {
                        // 获取[是否支持强制修改]配置
                        SupplierCompanyBo supplierCompany = supplierCompanyClientLoader
                            .findSupplierCompany(hoOrderBo.getCorpId(), orderDetailResponseVo.getSupplierCode(), 3);
                        Optional.ofNullable(supplierCompany).map(SupplierCompanyBo::getForceCancelModify).ifPresent(
                            forceCancelModify -> {
                                if (BooleanUtils.toBoolean(forceCancelModify)) {
                                    Long orderId = orderDetailResponseVo.getOrderId();
                                    // 订单非异步取消且无修改中/取消中的单据
                                    Boolean asyncCancel = orderDetailResponseVo.getAsyncCancel();
                                    Boolean existBeingModified = hoHotelApplyLoader.orderExistBeingModified(orderId);
                                    Boolean existBeingCanceled =
                                        hoOrderCancelFormLoader.orderExistBeingCanceled(orderId);
                                    if (!asyncCancel && !existBeingModified && !existBeingCanceled) {
                                        orderDetailResponseVo.setForceModify(BooleanUtils.toBoolean(forceCancelModify));
                                    }
                                }
                            });
                    }

                    boolean travelForBusiness = CorpPayTypeEnum.PUB.getType().equals(orderDetailResponseVo.getCorpPayType());
                    if (travelForBusiness) {
                        // 勾选【酒店订单取消/修改整单】时，预订人、出行人可见取消/修改按钮（服务商返回可取消/可修改的前提下），可进行取消/修改操作
                        boolean supportModify = orderDetailResponseVo.getForceModify() || !cannotModify;
                        if (supportModify) {
                            if (!travelerCancellationAndChangeAuthorityControl) {
                                BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
                                boolean isBooker = baseUserInfo.getUid().equals(orderDetailResponseVo.getBookingUid());
                                if (!isBooker) {
                                    orderDetailResponseVo.setForceModify(Boolean.FALSE);
                                    orderDetailResponseVo.setModifyType(HotelModifyTypeEnum.NON.getCode());
                                    orderDetailResponseVo.setAllowTravelerModify(Boolean.FALSE);
                                }
                            }
                        }
                    }
                });
            orderDetailResponseVo
                    .setDisplayModifyProgress(displayModifyProcess(queryOrderDetailRequestDto.getOrderId()));
            orderDetailResponseVo.setHotelMemberInfo(toHotelMemberInfo(orderDetail.getHoHotelMemberBo()));
            orderDetailResponseVo.setOrderResource(hoOrderBo.getOrderResource());
        } catch (Exception e) {
            log.error("该订单暂不支持", e);
        }
        //打卡明细
        orderDetailResponseVo.setClockDetailList(getClockDetailList(orderDetail));
        //合住信息
        orderDetailResponseVo.setChummageInfo(getChummageInfo(orderDetail.getChummageInfoBo()));
        orderDetailResponseVo.setCustomRemark(hoOrderBo.getCustomRemark());
        orderDetailResponseVo.setCancelPolicyType(hoRoomBo.getCancelPolicyType());
        orderDetailResponseVo.setCancelPolicyDesc(hoRoomBo.getCancelPolicyDesc());
        orderDetailResponseVo.setLastCancelTime(DateUtil.dateToString(hoRoomBo.getLastCancelTime(), DateUtil.DF_YMD_HMS));
        orderDetailResponseVo.setOrderCancelRuleList(getOrderCancelRuleList(orderDetail.getHoOrderCancelRuleBoList()));
        orderDetailResponseVo.setLastFreeCancelTime(getLastFreeCancelTime(hoRoomBo, orderDetail.getHoOrderCancelRuleBoList()));
        orderDetailResponseVo.setCancelPolicyVO(getCancelPolicyVO(orderDetail.getOrderSnapshotDataBoList()));
        orderDetailResponseVo.setServiceFee(hoOrderBo.getServiceFee());
        orderDetailResponseVo.setServiceFeeStrategy(hoOrderBo.getServiceFeeStrategy());
        orderDetailResponseVo.setServiceFeeStrategyValue(hoOrderBo.getServiceFeeStrategyValue());
        log.info("orderDetailResponseVo:{}" , JsonUtils.toJsonString(orderDetailResponseVo));
        return orderDetailResponseVo;
    }

    private String getLastFreeCancelTime(HoRoomBo hoRoomBo, List<HoOrderCancelRuleBo> hoOrderCancelRuleBoList) {
        Integer cancelPolicyType = hoRoomBo.getCancelPolicyType();
        if (Objects.isNull(cancelPolicyType)) {
            return null;
        }

        boolean freeOrNotCanCancel = CancelPolicyEnum.FreeCancel.getType().equals(cancelPolicyType)
                || CancelPolicyEnum.CanNotCancel.getType().equals(cancelPolicyType);
        if (freeOrNotCanCancel) {
            return null;
        }

        // 限时取消
        boolean limitedCancel = CancelPolicyEnum.LimitedCancel.getType().equals(cancelPolicyType);
        if (limitedCancel) {
            boolean ladderCancel = CollectionUtils.isNotEmpty(hoOrderCancelRuleBoList);
            if (!ladderCancel) { //非阶梯取消
                return Optional.ofNullable(hoRoomBo.getLastCancelTime())
                        .map(item -> DateUtil.dateToString(item, DateUtil.DF_YMD_HMS))
                        .orElse(null);
            }

            Optional<HoOrderCancelRuleBo> orderCancelRuleBoOpt = hoOrderCancelRuleBoList.stream()
                    .filter(item -> item.getDeductionType().equals(CancelOrderRuleEnum.FREE.getType()))
                    .findFirst();
            if (orderCancelRuleBoOpt.isPresent()) {
                HoOrderCancelRuleBo hoOrderCancelRuleBo = orderCancelRuleBoOpt.get();
                return Optional.ofNullable(hoOrderCancelRuleBo.getEndDeductTime())
                        .map(item -> DateUtil.dateToString(item, DateUtil.DF_YMD_HMS))
                        .orElse(null);
            }
        }
        return null;
    }

    /**
     * 获取出行人退改权限控制
     *
     * @return boolean
     */
    private boolean getTravelerCancellationAndChangeAuthorityControl(String orgId, String uid) {
        GetSwitchListRequest request = new GetSwitchListRequest();
        request.setOrgId(orgId);
        request.setUId(uid);
        request.setSwitchKey(SwitchEnum.AGENCY_BOOKING_REFUND_MODIFICATION_CONTROL.getKey());
        Map<String, Object> switchValueMap = switchClientLoader.getSwitchValueMap(request);
        if (CollectionUtils.isEmpty(switchValueMap)) { //获取配置失败 按照配置默认值走 即启用
            return true;
        }
        Object switchValue = switchValueMap.get(SwitchEnum.AGENCY_BOOKING_REFUND_MODIFICATION_CONTROL.getKey());
        if (Objects.isNull(switchValue)) { //获取配置失败 按照配置默认值走 即启用
            return true;
        }
        List<String> switchValueList = JsonUtils.parseArray(switchValue.toString(), String.class);
        return switchValueList.contains(HOTEL_ORDER_MODIFY_CANCEL_CONTROL_ENABLE);
    }

    private CancelPolicyVO getCancelPolicyVO(List<OrderSnapshotDataBo> orderSnapshotDataBoList) {
        try {
            if (CollectionUtils.isEmpty(orderSnapshotDataBoList)) {
                return null;
            }

            Optional<OrderSnapshotDataBo> orderSnapshotDataBoOpt = orderSnapshotDataBoList.stream()
                .filter(item -> OrderSnapshotDataTypeEnum.PRODUCT_INFO.getCode().equals(item.getDataType()))
                .findFirst();
            if (!orderSnapshotDataBoOpt.isPresent()) {
                return null;
            }

            OrderSnapshotDataBo orderSnapshotDataBo = orderSnapshotDataBoOpt.get();
            GetHotelProductSnapshotResponse hotelProductSnapshot =
                JsonUtils.parse(orderSnapshotDataBo.getDataContent(), GetHotelProductSnapshotResponse.class);
            if (Objects.isNull(hotelProductSnapshot)) {
                return null;
            }

            List<BasicRoomInfoDTO> basicRoomInfoList = hotelProductSnapshot.getBasicRoomInfo();
            if (CollectionUtils.isEmpty(basicRoomInfoList)) {
                return null;
            }

            BasicRoomInfoDTO basicRoomInfoDTO = basicRoomInfoList.get(0);
            if (Objects.isNull(basicRoomInfoDTO)) {
                return null;
            }

            List<RoomInfoDTO> roomCardList = basicRoomInfoDTO.getRoomCardList();
            if (CollectionUtils.isEmpty(roomCardList)) {
                return null;
            }

            RoomInfoDTO roomInfoDTO = roomCardList.get(0);
            if (Objects.isNull(roomInfoDTO)) {
                return null;
            }

            RoomPolicyServiceDTO roomPolicyService = roomInfoDTO.getRoomPolicyService();
            if (Objects.isNull(roomPolicyService)) {
                return null;
            }

            CancelPolicyType cancelPolicy = roomPolicyService.getCancelPolicy();
            if (Objects.isNull(cancelPolicy)) {
                return null;
            }
            CancelPolicyVO cancelPolicyVO = new CancelPolicyVO();
            cancelPolicyVO.setType(cancelPolicy.getCancelRuleType());
            cancelPolicyVO.setEndFreeCancelTime(cancelPolicy.getEndFreeCancelTime());
            if (HotelCancelPolicyEnum.LADDER.name().equals(cancelPolicy.getCancelRuleType())) {
                cancelPolicyVO.setStepCancelPolicyList(
                    subRoomStepCancelPolicyListConvert(cancelPolicy.getStepCancelPolicyList()));
            }
            return cancelPolicyVO;
        } catch (Exception e) {
            log.error("转换取消政策失败：{}", e.getMessage());
        }

        return null;
    }

    private static List<CancelPolicyVO.TipNodeVO> subRoomStepCancelPolicyListConvert(List<StepCancelPolicyType> stepCancelPolicyList) {
        ArrayList<CancelPolicyVO.TipNodeVO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(stepCancelPolicyList)){
            return list;
        }
        List<StepCancelPolicyType> sortedList = stepCancelPolicyList.stream().sorted(Comparator.comparing(StepCancelPolicyType::getEndTime)).collect(Collectors.toList());
        sortedList.forEach(e ->{
            CancelPolicyVO.TipNodeVO vo = new CancelPolicyVO.TipNodeVO();
            vo.setType(e.getCancelRuleType());
            if (HotelCancelPolicyEnum.FREE.name().equals(e.getCancelRuleType())){
                vo.setTitle(e.getEndTime() + "前");
                vo.setContent(HotelCancelPolicyEnum.FREE.getDesc());
            }
            if (HotelCancelPolicyEnum.LADDER.name().equals(e.getCancelRuleType()) && Objects.nonNull(e.getPrice())){
                vo.setTitle(e.getEndTime() + "前");
                vo.setContent("¥" + e.getPrice().toString());
            }
            if (HotelCancelPolicyEnum.CANNOT_CANCEL.name().equals(e.getCancelRuleType())){
                vo.setTitle(e.getStartTime() + "后");
                vo.setContent(HotelCancelPolicyEnum.CANNOT_CANCEL.getDesc());
            }
            list.add(vo);
        });
        return list;
    }

    private List<OrderCancelRuleVo> getOrderCancelRuleList(List<HoOrderCancelRuleBo> hoOrderCancelRuleBoList) {
        if (CollectionUtils.isEmpty(hoOrderCancelRuleBoList)) {
            return Collections.emptyList();
        }
        return hoOrderCancelRuleBoList.stream().map(item -> {
            OrderCancelRuleVo orderCancelRuleVo = new OrderCancelRuleVo();
            orderCancelRuleVo.setDeductionType(item.getDeductionType());
            orderCancelRuleVo.setStartDeductTime(item.getStartDeductTime());
            orderCancelRuleVo.setEndDeductTime(item.getEndDeductTime());
            orderCancelRuleVo.setAmount(item.getAmount());
            orderCancelRuleVo.setDeductionRatio(item.getDeductionRatio());
            return orderCancelRuleVo;
        }).collect(Collectors.toList());
    }

    private OrderDetailResponseVo.ChummageInfo getChummageInfo(HoChummageInfoBo chummageInfoBo) {
        if(ObjectUtils.notNull(chummageInfoBo)){
            OrderDetailResponseVo.ChummageInfo chummageInfo = new OrderDetailResponseVo.ChummageInfo();
            chummageInfo.setHaveChummage(chummageInfo.getHaveChummage());
            OrderDetailResponseVo.NoChummageReasonCode noChummageReasonCode = new OrderDetailResponseVo.NoChummageReasonCode();
            noChummageReasonCode.setCode(chummageInfoBo.getNoChummageReasonCode());
            noChummageReasonCode.setName(chummageInfoBo.getNoChummageReasonName());
            noChummageReasonCode.setId(chummageInfoBo.getNoChummageReasonId());
            noChummageReasonCode.setRemark(chummageInfoBo.getRemark());
            chummageInfo.setNoChummageReasonCode(noChummageReasonCode);
            return chummageInfo;
        }
        return null;
    }

    /**
     * 转换核算单元
     *
     * @param accountingUnitDataJSON
     */
    private static List<AccountingUnitInfoVo> convertAccountingUnitInfo(String accountingUnitDataJSON) {
        if (StringUtils.isBlank(accountingUnitDataJSON)) {
            return null;
        }
        try {
            return JsonUtils.parseArray(accountingUnitDataJSON, AccountingUnitInfoVo.class);
        } catch (Exception e) {
            log.error("反序列化核算单元Json字符串失败: " + accountingUnitDataJSON + "\\n", e);
            return null;
        }
    }

    private static List<AccountingUnitInfoVo> convertAccountingUnitConfig(String orgId, List<AccountingUnitInfoVo> accountingUnitList) {
        if (CollectionUtils.isEmpty(accountingUnitList)) {
            return accountingUnitList;
        }

        List<OrgAccountingUnitCategoryConfig> orgConfig = EnvironmentHolder.getBean(CommonSupplementService.class).getOrgConfig(orgId);
        Map<String, OrgAccountingUnitCategoryConfig> groupByCategoryCode = Optional.ofNullable(orgConfig).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OrgAccountingUnitCategoryConfig::getCategoryCode, Function.identity()));

        for (AccountingUnitInfoVo accountingUnitInfoVo : accountingUnitList) {
            if(groupByCategoryCode.containsKey(accountingUnitInfoVo.getAccountingUnitTypeCode())){
                OrgAccountingUnitCategoryConfig categoryConfig = groupByCategoryCode.get(accountingUnitInfoVo.getAccountingUnitTypeCode());

                accountingUnitInfoVo.setShow(categoryConfig.getShow());
                accountingUnitInfoVo.setLinkParent(categoryConfig.getLinkParent());
                accountingUnitInfoVo.setRequiredType(categoryConfig.getRequiredType());
                accountingUnitInfoVo.setShowType(categoryConfig.getShowType());
            }
        }

        return accountingUnitList;
    }


    /**
     * 转换多成本中心
     *
     * @param multiCostCenterDataJSON
     */
    private static List<TempCostCenterVo> convertMultiCostCenter(String multiCostCenterDataJSON) {
        if (StringUtils.isBlank(multiCostCenterDataJSON)) {
            return null;
        }
        try {
            return JsonUtils.parseArray(multiCostCenterDataJSON, TempCostCenterVo.class);
        } catch (Exception e) {
            log.error("反序列化多成本中心Json字符串失败: " + multiCostCenterDataJSON + "\\n", e);
            return null;
        }
    }

    private OrderDetailResponseVo.HotelMemberInfo toHotelMemberInfo(HoHotelMemberBo hoHotelMemberBo) {
        if (null == hoHotelMemberBo) {
            return null;
        }
        OrderDetailResponseVo.HotelMemberInfo hotelMemberInfo = new OrderDetailResponseVo.HotelMemberInfo();
        hotelMemberInfo.setGroupId(hoHotelMemberBo.getGroupId());
        hotelMemberInfo.setGroupName(hoHotelMemberBo.getGroupName());
        hotelMemberInfo.setEnabledBonusPoint(hoHotelMemberBo.getEnabledBonusPoint());
        hotelMemberInfo.setMemberCardNo(hoHotelMemberBo.getMemberCardNo());
        hotelMemberInfo.setMemberCardholder(hoHotelMemberBo.getMemberCardholder());
        if (StringUtils.isNotBlank(hoHotelMemberBo.getMemberRuleDesc())) {
            hotelMemberInfo.setMemberRuleDescList(JsonUtils.parseArray(hoHotelMemberBo.getMemberRuleDesc(), String.class));
        }
        hotelMemberInfo.setBonusPointType(hoHotelMemberBo.getBonusPointType());
        return hotelMemberInfo;
    }

    private String getCancelReasonDesc(HoOrderBo hoOrderBo) {
        if (HotelCancelReasonEnum.APPROVAL_REJECTION.getCode().equalsIgnoreCase(hoOrderBo.getCancelReasonCode())
                && StringUtils.isNotBlank(hoOrderBo.getApprovalId())) {
            ExecutionDetail approvalDetail = approvalSystemClientLoader.getApprovalDetail(hoOrderBo.getApprovalId());
            if (null != approvalDetail) {
                return approvalDetail.getRejectReason();
            }
        }
        return hoOrderBo.getCancelReasonDesc();
    }

    /**
     * 组装订单详情页面 "其他信息"
     *
     * @param hoRoomBo
     * @return
     */
    private List<OrderHotelVo.OtherInfo> packageOtherInfo(HoRoomBo hoRoomBo) {
        List<OrderHotelVo.OtherInfo> otherInfoList = new ArrayList<>(2);
        if (null != hoRoomBo.getCancelPolicyType() && StringUtils.isNotBlank(hoRoomBo.getCancelPolicyDesc())) {
            OrderHotelVo.OtherInfo cancelInfo = new OrderHotelVo.OtherInfo();
            cancelInfo.setTitle(Optional.ofNullable(HotelCancelEnum.getByCode(hoRoomBo.getCancelPolicyType()))
                    .map(HotelCancelEnum::getDesc).orElse(StringUtils.EMPTY));
            cancelInfo.setContent(hoRoomBo.getCancelPolicyDesc());
            otherInfoList.add(cancelInfo);
        }
        if (StringUtils.isNotBlank(hoRoomBo.getApplicativeAreaTitle())
                && StringUtils.isNotBlank(hoRoomBo.getApplicativeAreaDesc())) {
            OrderHotelVo.OtherInfo applicativeAreaInfo = new OrderHotelVo.OtherInfo();
            applicativeAreaInfo.setTitle(hoRoomBo.getApplicativeAreaTitle());
            applicativeAreaInfo.setContent(hoRoomBo.getApplicativeAreaDesc());
            otherInfoList.add(applicativeAreaInfo);
        }
        return otherInfoList;
    }

    public OrderDetailManageResponseVo queryOrderDetailForManage(QueryOrderDetailRequestDto queryOrderDetailRequestDto,
                                                                 boolean needActualCheckInOutDate) {
        // 查询订单基本详情
        OrderDetailResponseBo orderDetailResponseBo = getOrderInfo(queryOrderDetailRequestDto);

        // 数据转换
        OrderDetailManageResponseVo orderDetailManageResponse = new OrderDetailManageResponseVo();
        List<HoRoomDailyBo> hoRoomDailyBoList = orderDetailResponseBo.getHoRoomDailyInfoList();
        HoHotelBo hoHotelBo = orderDetailResponseBo.getHoHotelInfo();
        HoOrderBo hoOrderBo = orderDetailResponseBo.getOrderInfo();
        HoDeliveryInfoBo hoDeliveryInfoBo = orderDetailResponseBo.getHoDeliveryInfoBo();
        List<HoPassengerBo> hoPassengerBoList = orderDetailResponseBo.getHoPassengerList();
        HoInvoiceBo hoInvoiceBo = orderDetailResponseBo.getHoInvoiceInfo();
        HoRoomBo hoRoomBo = orderDetailResponseBo.getHoRoomInfo();
        List<HoRoomDailyBo> roomDailyBoList = orderDetailResponseBo.getHoRoomDailyInfoList();
        HoHotelLowBo hoHotelLowBo = orderDetailResponseBo.getHoHotelLow();
        HoOrderInfoVo hoOrderInfoVo = new HoOrderInfoVo();
        hoOrderInfoVo.setUid(hoOrderBo.getUid());
        hoOrderInfoVo.setBookingName(hoOrderBo.getUname());
        hoOrderInfoVo.setContactCountryCode(hoOrderBo.getContactCountryCode());
        hoOrderInfoVo.setContactEmail(hoOrderBo.getContactEmail());
        hoOrderInfoVo.setContactName(hoOrderBo.getContactName());
        hoOrderInfoVo.setContactPhone(hoOrderBo.getContactMobilePhone());
        TravelEnum travelEnum = TravelEnum.getByType(hoOrderBo.getCorpPayType());
        if (travelEnum != null) {
            hoOrderInfoVo.setOrderBusTypeName(travelEnum.getDesc());
        }
        hoOrderInfoVo.setOrderBusType(hoOrderBo.getCorpPayType());
        hoOrderInfoVo.setOrderCreateTime(DateUtil.dateToString(hoOrderBo.getOrderDate(), DateUtil.DF_YMD_HM));
        // if (Objects.equals(hoOrderBo.getSource(), OrderSourceEnum.Supplement.name())){
        // hoOrderInfoVo.setSource( OrderSourceEnum.Supplement.getName());
        // }else{
        hoOrderInfoVo.setSource(hoOrderBo.getSource());
        // }
        hoOrderInfoVo.setOrderId(hoOrderBo.getOrderId());
        hoOrderInfoVo.setApprovalId(hoOrderBo.getApprovalId());
        hoOrderInfoVo.setTripApplyNo(hoOrderBo.getTripApplyNo());
        // 根据uid 查询名称
        if (StringUtils.isNotBlank(hoOrderBo.getCorpId())) {
            OrgInfoVo orgInfo = organizationClientLoader.findOrgInfoByOrgId(hoOrderBo.getCorpId());
            hoOrderInfoVo.setCorpName(Optional.ofNullable(orgInfo).map(OrgInfoVo::getName).orElse(null));
        }
        if (StringUtils.isNotBlank(hoOrderBo.getDeptId())) {
            OrgInfoVo departmentInfo = Optional
                    .ofNullable(organizationClientLoader.findOrgInfoByOrgId(hoOrderBo.getDeptId())).orElse(new OrgInfoVo());
            hoOrderInfoVo.setDeptName(departmentInfo.getName());
        }
        //国内酒店订单查询并填充出员工入住离店打卡数据
        OrderDetailManageResponseVo.ClockInfo clockInfo = getClockInfo(hoOrderBo);
        orderDetailManageResponse.setClockInfo(clockInfo);

        hoOrderInfoVo.setSupplierCode(hoOrderBo.getSupplierCode());
        hoOrderInfoVo.setSupplierName(hoOrderBo.getSupplierName());
        hoOrderInfoVo.setTravelStrandName(hoOrderBo.getTravelStandard());
        hoOrderInfoVo.setSupplierOrderId(hoOrderBo.getSupplierOrderId());
        hoOrderInfoVo.setOrderStatus(hoOrderBo.getOrderStatus());
        hoOrderInfoVo.setOrderStatusName(AccountOrderStatusEnum.getEnum(hoOrderBo.getOrderStatus()).getName());
        hoOrderInfoVo.setTravelStrandDetails(
                packageTravelStrandDetailList(hoOrderBo.getTravelStandardToken(), hoOrderBo.getTravelStandard()));
        if (hoHotelLowBo != null) {
            hoOrderInfoVo.setRcInfo(hoHotelLowBo.getReason());
        }

        // 查询服务商信息
        MbSupplierInfoVo supplierInfo =
                Optional.ofNullable(supplierDataClientLoader.findBySupplierCode(hoOrderBo.getSupplierCode()))
                        .orElse(new MbSupplierInfoVo());
        hoOrderInfoVo.setSupplierHotLine(supplierInfo.getHotLine());
        hoOrderInfoVo.setSupplierPhone(supplierInfo.getHotLine());
        // 客户方行程号
        AoApplyTripTrafficVo applyTripTrafficVo =
                applyTripClientLoader.getApplyTripTrafficById(hoOrderBo.getTripTrafficId());
        hoOrderInfoVo
                .setTravelNo(Optional.ofNullable(applyTripTrafficVo).map(AoApplyTripTrafficVo::getTravelNo).orElse(null));
        hoOrderInfoVo.setUrgentApply(hoOrderBo.getUrgentApply());
        hoOrderInfoVo.setCancelReasonCode(hoOrderBo.getCancelReasonCode());
        hoOrderInfoVo.setCancelReason(HotelCancelReasonEnum.getReasonByCode(hoOrderBo.getCancelReasonCode()));
        hoOrderInfoVo.setCancelReasonDesc(HotelCancelReasonEnum.getViewDescByCode(hoOrderBo.getCancelReasonCode(),
                this.getCancelReasonDesc(hoOrderBo)));
        hoOrderInfoVo.setCancelPolicyVO(getCancelPolicyVO(orderDetailResponseBo.getOrderSnapshotDataBoList()));
        hoOrderInfoVo.setServiceFee(hoOrderBo.getServiceFee());
        hoOrderInfoVo.setServiceFeeStrategy(hoOrderBo.getServiceFeeStrategy());
        hoOrderInfoVo.setServiceFeeStrategyValue(hoOrderBo.getServiceFeeStrategyValue());
        orderDetailManageResponse.setHoOrderInfo(hoOrderInfoVo);
        // 查询分销订单信息
        CompletableFuture<HotelOrderInfo> searchOrderFeature = CompletableFuture.completedFuture(null);
        if (needActualCheckInOutDate) {
            searchOrderFeature = CompletableFuture.supplyAsync(() -> searchOrder(hoOrderBo.getSupplierOrderId(),
                supplierInfo.getSupplierCode(), hoOrderBo.getCorpId()), queryThreadPoolExecutor);
        }

        // 支付配送信息
        PayDeliverInfoVo payDeliverInfoVo = new PayDeliverInfoVo();
        OrderFeeDetail orderFeeDetail = buildOrderFeeDetail(hoHotelBo, hoOrderBo, roomDailyBoList);
        payDeliverInfoVo.setOrderFeeDetail(orderFeeDetail);
        payDeliverInfoVo.setPayTypeCode(hoOrderBo.getPayType());
        if (hoInvoiceBo != null) {
            InvoiceInfoVo invoiceInfoVo = new InvoiceInfoVo();
            BeanUtils.copyProperties(hoInvoiceBo, invoiceInfoVo);
            payDeliverInfoVo.setInvoiceInfoVo(invoiceInfoVo);
            payDeliverInfoVo.setDeliveryInfoList(Arrays.asList(buildOrderDeliverInfo(hoHotelBo, hoOrderBo,
                    hoDeliveryInfoBo, hoPassengerBoList, hoInvoiceBo, hoRoomBo)));
        }
        PaymentBillDetailResponse paymentBillResponse = null;
        List<PpPaymentBillDto> paymentBillDtoList = payBillClientLoader.paymentBillInfo(hoOrderBo.getOrderId());

        if (CollectionUtils.isNotEmpty(paymentBillDtoList)) {
            paymentBillResponse = new PaymentBillDetailResponse();
            String payStatus = null;
            BigDecimal refundFeeAmount = BigDecimal.ZERO;
            List<OrderPayInfoVo> orderPayInfoVoList = new ArrayList<>();
            for (PpPaymentBillDto paymentBillDto : paymentBillDtoList) {
                String payType = paymentBillDto.getTxType();
                if (payStatus == null && TX_DEDUCTION.equals(payType)) {
                    payStatus = paymentBillDto.getStatus();
                }
                if (!TX_DEDUCTION.equals(payType)) {
                    if (refundFeeAmount == null) {
                        refundFeeAmount = BigDecimal.ZERO;
                    }
                    refundFeeAmount = refundFeeAmount.add(paymentBillDto.getAmount());
                }
                orderPayInfoVoList.add(buildOrderPayInfo(paymentBillDto, payType));
            }
            orderFeeDetail.setRefundAmount(refundFeeAmount);
            paymentBillResponse.setStatus(payStatus);
            payDeliverInfoVo.setOrderPayInfoList(orderPayInfoVoList);
        }

        HotelOrderInfo searchOrderInfo = BaseUtils.getFuture("查询订单", searchOrderFeature);

        orderDetailManageResponse.setPayDeliverInfo(payDeliverInfoVo);
        orderDetailManageResponse.setOrderHotelInfo(buildOrderHotelVo(hoRoomDailyBoList, hoHotelBo, hoOrderBo, hoRoomBo,
                orderDetailResponseBo.getHoHotelApplyBoList()));
        orderDetailManageResponse.setOrderPassengerList(buildOrderPassengerList(hoPassengerBoList, searchOrderInfo));
        orderDetailManageResponse.setOrderStatusInfo(buildOrderStatusInfo(hoOrderBo, hoInvoiceBo, paymentBillResponse));
        orderDetailManageResponse.setFileList(buildFileList(orderDetailResponseBo));
        orderDetailManageResponse.setApplyFormDetailList(
                packageApplyFormDetailList(hoRoomBo, orderDetailResponseBo.getHoHotelApplyBoList()));
        orderDetailManageResponse.setStandardInfo(getStandardInfo(hoOrderBo, hoHotelLowBo));
        orderDetailManageResponse.setHoChummageInfo(getChummageInfoVO(orderDetailResponseBo.getChummageInfoBo()));
        return orderDetailManageResponse;
    }

    private OrderDetailManageResponseVo.ChummageInfoVo getChummageInfoVO(HoChummageInfoBo chummageInfoBo) {
        if(ObjectUtils.notNull(chummageInfoBo)){
            OrderDetailManageResponseVo.ChummageInfoVo chummageInfo = new OrderDetailManageResponseVo.ChummageInfoVo();
            chummageInfo.setHaveChummage(chummageInfo.getHaveChummage());
            OrderDetailManageResponseVo.NoChummageReasonCodeVo noChummageReasonCode = new OrderDetailManageResponseVo.NoChummageReasonCodeVo();
            noChummageReasonCode.setCode(chummageInfoBo.getNoChummageReasonCode());
            noChummageReasonCode.setName(chummageInfoBo.getNoChummageReasonName());
            noChummageReasonCode.setId(chummageInfoBo.getNoChummageReasonId());
            noChummageReasonCode.setRemark(chummageInfoBo.getRemark());
            chummageInfo.setNoChummageReasonCode(noChummageReasonCode);
            return chummageInfo;
        }
        return null;
    }


    /**
     * 目前只有国内酒店订单才有打卡记录
     * @param hoOrderBo
     */
    private OrderDetailManageResponseVo.ClockInfo getClockInfo(HoOrderBo hoOrderBo) {
        List<HoOrderClockRecord> hoOrderClockRecords = hoOrderClockRecordLoader.listByOrderId(hoOrderBo.getOrderId());
        if(CollectionUtils.isNotEmpty(hoOrderClockRecords)) {
            OrderDetailManageResponseVo.ClockInfo clockInfo = new OrderDetailManageResponseVo.ClockInfo();
            Map<Integer, HoOrderClockRecord> hoOrderClockRecordMap =
                    hoOrderClockRecords.stream().collect(Collectors.toMap(HoOrderClockRecord::getType, item->item, (v1,v2)->v1));
            if(hoOrderClockRecordMap.containsKey(HotelClockTypeEnum.CLOCK_IN.getCode())){
                HoOrderClockRecord hoOrderClockRecord = hoOrderClockRecordMap.get(HotelClockTypeEnum.CLOCK_IN.getCode());
                clockInfo.setCheckInClockTime(DateUtil.dateToString(hoOrderClockRecord.getClockTime(), DateUtil.DF_YMD_HM));
                EmployeeInfoBo employeeInfo = organizationEmployeeClientLoader.findEmployeeInfoByUid(hoOrderClockRecord.getClockUid());
                if(Objects.nonNull(employeeInfo)) {
                    clockInfo.setCheckInClockName(employeeInfo.getName());
                }
            }
            if(hoOrderClockRecordMap.containsKey(HotelClockTypeEnum.CLOCK_OUT.getCode())){
                HoOrderClockRecord hoOrderClockRecord = hoOrderClockRecordMap.get(HotelClockTypeEnum.CLOCK_OUT.getCode());
                clockInfo.setCheckOutClockTime(DateUtil.dateToString(hoOrderClockRecord.getClockTime(), DateUtil.DF_YMD_HM));
                EmployeeInfoBo employeeInfo = organizationEmployeeClientLoader.findEmployeeInfoByUid(hoOrderClockRecord.getClockUid());
                if(Objects.nonNull(employeeInfo)) {
                    clockInfo.setCheckOutClockName(employeeInfo.getName());
                }
            }
            return clockInfo;
        }
        return null;
    }

    public List<OrderDetailManageResponseVo.ApplyFormDetail> packageApplyFormDetailList(HoRoomBo hoRoomBo,
                                                                                        List<HoHotelApplyBo> hoHotelApplyBoList) {
        if (CollectionUtils.isEmpty(hoHotelApplyBoList)) {
            return Collections.emptyList();
        }
        List<OrderDetailManageResponseVo.ApplyFormDetail> applyFormDetailList = new ArrayList<>();
        for (HoHotelApplyBo hoHotelApplyBo : hoHotelApplyBoList) {
            OrderDetailManageResponseVo.ApplyFormDetail applyFormDetail =
                    new OrderDetailManageResponseVo.ApplyFormDetail();
            applyFormDetail.setApplyId(hoHotelApplyBo.getApplyId());
            applyFormDetail.setApplyTime(hoHotelApplyBo.getApplyTime());
            applyFormDetail.setStatus(hoHotelApplyBo.getStatus());
            applyFormDetail.setReasonCode(hoHotelApplyBo.getReasonCode());
            applyFormDetail.setReasonDesc(hoHotelApplyBo.getReasonDesc());
            applyFormDetail
                    .setModifyRoomNight(packageModifyRoomNight(hoRoomBo, hoHotelApplyBo.getHoHotelApplyDetailBoList()));
            applyFormDetailList.add(applyFormDetail);
        }
        return applyFormDetailList;
    }

    public OrderDetailManageResponseVo.ModifyRoomNight packageModifyRoomNight(HoRoomBo hoRoomBo,
                                                                              List<HoHotelApplyDetailBo> hoHotelApplyDetailBoList) {
        if (CollectionUtils.isEmpty(hoHotelApplyDetailBoList)) {
            return null;
        }
        OrderDetailManageResponseVo.ModifyRoomNight modifyRoomNight = new OrderDetailManageResponseVo.ModifyRoomNight();
        // 修改前
        hoHotelApplyDetailBoList.stream().filter(detail -> BooleanUtils.isFalse(detail.getAfterRecord())).findFirst()
                .ifPresent(detail -> {
                    modifyRoomNight.setModifyBeforeInfoList(packageModifyInfoList(hoRoomBo, detail));
                });
        // 修改后
        hoHotelApplyDetailBoList.stream().filter(detail -> BooleanUtils.isTrue(detail.getAfterRecord())).findFirst()
                .ifPresent(detail -> {
                    modifyRoomNight.setModifyAfterInfoList(packageModifyInfoList(hoRoomBo, detail));
                });
        return modifyRoomNight;
    }

    public List<OrderDetailManageResponseVo.ModifyInfo> packageModifyInfoList(HoRoomBo hoRoomBo,
                                                                              HoHotelApplyDetailBo hoHotelApplyDetailBo) {
        Date checkInDate = DateUtil.stringToDate(hoHotelApplyDetailBo.getCheckInDate(), DateUtil.DF_YMD);
        Date checkOutDate = DateUtil.stringToDate(hoHotelApplyDetailBo.getCheckOutDate(), DateUtil.DF_YMD);
        List<String> datesBetween = hotelModifyService.getDatesBetween(checkInDate, DateUtil.addDays(checkOutDate, -1));
        return datesBetween.stream().map(date -> {
            OrderDetailManageResponseVo.ModifyInfo modifyInfo = new OrderDetailManageResponseVo.ModifyInfo();
            modifyInfo.setRoomDate(date);
            modifyInfo.setRoomQuantity(hoRoomBo.getRoomQuantity());
            return modifyInfo;
        }).collect(Collectors.toList());
    }
    private OrderDetailManageResponseVo.StandardInfo getStandardInfo(HoOrderBo hoOrderBo, HoHotelLowBo hoHotelLowBo) {
        if (StringUtils.isBlank(hoOrderBo.getTravelStandard())) {
            return null;
        }
        HotelControlVo hotelControlVo = JsonUtils.parse(hoOrderBo.getTravelStandard(), HotelControlVo.class);
        if (hotelControlVo == null && StringUtils.isBlank(hoOrderBo.getTravelStandardToken())) {
            return null;
        }

        Boolean exceed = Integer.valueOf(0).equals(hoOrderBo.getRcType())
            || StringUtils.isNotBlank(Optional.ofNullable(hoHotelLowBo).map(HoHotelLowBo::getReason).orElse(null));
        // 无超标无需返回
        if (BooleanUtils.isFalse(exceed)) {
            return null;
        }
        OrderDetailManageResponseVo.StandardInfo standardInfo = new OrderDetailManageResponseVo.StandardInfo();
        standardInfo.setExceedStandard(exceed);
        // 金额管控
        OrderDetailManageResponseVo.StandardDetail standardDetail = new OrderDetailManageResponseVo.StandardDetail();
        standardDetail.setStandardCode(STANDARD_LOW_PRICE[0]);
        standardDetail.setStandardTitle(STANDARD_LOW_PRICE[1]);
        standardDetail.setReasonContent(Optional.ofNullable(hoHotelLowBo).map(HoHotelLowBo::getReason).orElse(null));
        standardDetail.setExceedStandard(exceed);


        CohabitStandardBo cohabitStandardBo = travelStandardService.cohabitStandardContentList(hoOrderBo.getTravelStandardToken());
        if (null != cohabitStandardBo && CollectionUtils.isNotEmpty(cohabitStandardBo.getRoomInfoList())) {
            List<String> contentList = cohabitStandardBo.getRoomInfoList().stream().map(roomInfo -> {
                String content;
                if (roomInfo.getAmount() == null
                    || BigDecimal.ZERO.compareTo(roomInfo.getAmount()) == 0) {
                    content = NO_LIMIT;
                } else {
                    content = String.format("￥%s", roomInfo.getAmount());
                }
                return String.format("房间%s：%s", roomInfo.getRoomIndex(), content);
            }).collect(Collectors.toList());
            standardDetail.setStandardContent(String.join(",", contentList));
        } else {
            if (hotelControlVo.getAveragePriceSet() == null) {
                return null;
            }
            if (BigDecimal.ZERO.compareTo(new BigDecimal(hotelControlVo.getAveragePriceSet().getPriceCeiling())) == 0) {
                standardDetail.setStandardContent(NO_LIMIT);
            } else {
                standardDetail.setStandardContent("¥" + hotelControlVo.getAveragePriceSet().getPriceCeiling() + "/间夜");
            }
        }
        standardInfo.setStandardDetailList(Lists.newArrayList(standardDetail));
        return standardInfo;
    }

    public List<BaseValue> packageTravelStrandDetailList(String travelStandardToken, String travelStandard) {
        try {
            //处理旧数据
            if (StringUtils.isBlank(travelStandardToken)) {
                return packageTravelStrand(travelStandard);
            }

            //获取差标
            List<TravelStandardResponse> travelStandardResponseList = travelStandardService.getTravelStandardResponseList(travelStandardToken);
            log.info("差标数据：{}" , JsonUtils.toJsonString(travelStandardResponseList));
            if (CollectionUtils.isEmpty(travelStandardResponseList)) {
                return Collections.emptyList();
            }

            // 定制差标
            TravelStandardResponse customTravelStandard = travelStandardResponseList.stream().filter(t -> t.getTravelStandardToken().getOwnerType() == TravelStandardOwnerTypeEnum.CUSTOMIZED.getCode()).findFirst().orElse(null);
            // 普通差标
            TravelStandardResponse commonTravelStandard = travelStandardResponseList.stream().filter(t -> t.getTravelStandardToken().getOwnerType() == TravelStandardOwnerTypeEnum.REQUEST.getCode()).findFirst().orElse(travelStandardResponseList.get(0));
            // 同住差标
            List<TravelStandardResponse> userGroupTravelStandardList = travelStandardResponseList.stream()
                    .filter(t -> t.getTravelStandardToken().getOwnerType() == TravelStandardOwnerTypeEnum.USER_GROUP.getCode()).collect(Collectors.toList());

            boolean existCustomTravelStandard = Objects.nonNull(customTravelStandard);
            
            if (CollectionUtils.isNotEmpty(userGroupTravelStandardList)) { //只处理房间差标 星级或者品牌取自定制或者普通差标
                
                if (cohabitControllerTypeIsOrder(userGroupTravelStandardList)) { // 校验同住管控类型是否为按订单管控
                    // 按订单管控则取ownerType = 4, 展示订单差标无需展示同住房间差标
                    return convertCommonTravelStandard(commonTravelStandard);
                }

                return convertUserGroupTravelStandard(userGroupTravelStandardList, existCustomTravelStandard,
                    existCustomTravelStandard ? customTravelStandard : commonTravelStandard);
            }

            if (existCustomTravelStandard) { //定制差标
                return convertCustomTravelStandard(customTravelStandard);
            }

            if (Objects.nonNull(commonTravelStandard)) { //普通差标
                return convertCommonTravelStandard(commonTravelStandard);
            }

        } catch (Exception e) {
            log.error("差标查询异常", e);
        }
        return Collections.emptyList();
    }

    /**
     * 校验同住管控类型是否为订单维度管控
     * 
     * @param userGroupTravelStandardList
     * @return
     */
    private boolean cohabitControllerTypeIsOrder(List<TravelStandardResponse> userGroupTravelStandardList) {
        // 取出同住管控规则
        Optional<TravelStandardRuleVO> anyCohabitRuleOptional =
            userGroupTravelStandardList.stream().map(TravelStandardResponse::getRuleChain).map(RuleChainVO::getRuleList)
                .flatMap(List::stream).filter(item -> "CohabitRule".equals(item.getName())).findAny();
        // 正常数据结构不会为空
        if (!anyCohabitRuleOptional.isPresent()) {
            return false;
        }
        // 强转实现类
        CohabitRuleVO cohabitRule = (CohabitRuleVO)anyCohabitRuleOptional.get();
        // 酒店同住管控类型
        // order：按订单维度管控
        if ("order".equals(cohabitRule.getControllerType())) {
            return true;
        }
        return false;
    }

    private List<BaseValue> convertUserGroupTravelStandard(List<TravelStandardResponse> userGroupTravelStandardList, boolean existCustomTravelStandard, TravelStandardResponse travelStandard) {
        List<BaseValue> list = new ArrayList<>(4);
        BaseValue controlType = new BaseValue();
        controlType.setTitle(CONTROL_TYPE);
        controlType.setContent(cohabitControlTypeConvert(userGroupTravelStandardList));
        list.add(controlType);

        BaseValue price = new BaseValue();
        price.setTitle(PRICE);
        price.setContent(cohabitPriceContentConvert(userGroupTravelStandardList));
        list.add(price);

        if (existCustomTravelStandard) { //定制差标存在星级品牌取自定制差标
            StepStandardVo stepStandardVo = getStepStandardVo(travelStandard);
            if (Objects.nonNull(stepStandardVo)) {
                BaseValue star = new BaseValue();
                star.setTitle(STAR);
                star.setContent(customStarContentConvert(stepStandardVo));
                list.add(star);
                BaseValue brand = new BaseValue();
                brand.setTitle(BRAND);
                brand.setContent(customBrandContentConvert(stepStandardVo));
                list.add(brand);
                return list;
            }
        }

        //不存在或定制差标获取失败则取自普通差标
        if (Objects.nonNull(travelStandard)) {
            List<TravelStandardRuleVO> ruleList = getCommonRuleList(travelStandard);
            BaseValue star = new BaseValue();
            star.setTitle(STAR);
            star.setContent(starContentConvert(ruleList));
            list.add(star);
            BaseValue brand = new BaseValue();
            brand.setTitle(BRAND);
            brand.setContent(brandContentConvert(ruleList));
            list.add(brand);
            return list;
        }

        return list;
    }

    private String cohabitControlTypeConvert(List<TravelStandardResponse> userGroupTravelStandardList) {
        String controlType = ControlTypeEnum.W.getName();
        for (TravelStandardResponse travelStandardResponse : userGroupTravelStandardList) {

            RuleChainVO ruleChain = travelStandardResponse.getRuleChain();
            if (Objects.isNull(ruleChain)) {
                continue;
            }

            List<TravelStandardRuleVO> ruleVOList = ruleChain.getRuleList();
            if (CollectionUtils.isEmpty(ruleVOList)) {
                continue;
            }

            Optional<TravelStandardRuleVO> travelStandardRuleVOOpt = ruleVOList.stream()
                .filter(item -> "CohabitRule".equals(item.getName()) || "OffPeakSeasonRule".equals(item.getName())
                    || "PriceRule".equals(item.getName()))
                .findFirst();
            if (travelStandardRuleVOOpt.isPresent()) {
                TravelStandardRuleVO travelStandardRuleVO = travelStandardRuleVOOpt.get();
                String[] rejectTypes = travelStandardRuleVO.getRejectTypes();
                controlType = controlTypeContentConvert(rejectTypes);
                break;
            }
        }
        return controlType;
    }

    private List<BaseValue> packageTravelStrand(String travelStandard) {
        List<BaseValue> baseValueList = new ArrayList<>();
        if (StringUtils.isNotBlank(travelStandard)) {
            JsonNode jsonNode = JsonUtils.getJsonNode(travelStandard);
            int control = jsonNode.get("control").asInt();
            BaseValue travelStandardValue = new BaseValue();
            travelStandardValue.setTitle("超标管控方式");
            if (control == 0) {
                travelStandardValue.setContent(ControlTypeEnum.F.getName());
            } else if (control == 1) {
                travelStandardValue.setContent(ControlTypeEnum.C.getName());
            } else if (control == 2) {
                travelStandardValue.setContent(ControlTypeEnum.A.getName());
            } else if (control == 3) {
                travelStandardValue.setContent(ControlTypeEnum.M.getName());
            } else if (control == 4) {
                travelStandardValue.setContent(ControlTypeEnum.C.getName() + "," + ControlTypeEnum.M.getName());
            }
            baseValueList.add(travelStandardValue);
            travelStandardValue = new BaseValue();
            travelStandardValue.setTitle("价格");
            String priceFloor = jsonNode.findPath("averagePriceSet").findPath("priceFloor").toString();
            String priceCeiling = jsonNode.findPath("averagePriceSet").findPath("priceCeiling").toString();
            // Long.MAX_VALUE
            if ("\"9.223372036854776E18\"".equals(priceCeiling) || "\"9223372036854775807\"".equals(priceCeiling)
                || StringUtils.isBlank(priceCeiling)) {
                travelStandardValue.setContent(NO_LIMIT);
            } else {
                travelStandardValue.setContent(priceFloor + "~" + priceCeiling);
            }
            baseValueList.add(travelStandardValue);
        }
        return baseValueList;
    }


    private List<BaseValue> convertCustomTravelStandard(TravelStandardResponse customTravelStandard) {
        StepStandardVo stepStandardVo = getStepStandardVo(customTravelStandard);
        if (Objects.isNull(stepStandardVo)) {
            return Collections.emptyList();
        }

        List<BaseValue> list = new ArrayList<>(3);
        BaseValue price = new BaseValue();
        price.setTitle(PRICE);
        price.setContent(customPriceContentConvert(stepStandardVo));
        list.add(price);
        BaseValue star = new BaseValue();
        star.setTitle(STAR);
        star.setContent(customStarContentConvert(stepStandardVo));
        list.add(star);
        BaseValue brand = new BaseValue();
        brand.setTitle(BRAND);
        brand.setContent(customBrandContentConvert(stepStandardVo));
        list.add(brand);
        return list;
    }

    private StepStandardVo getStepStandardVo(TravelStandardResponse customTravelStandard) {
        RuleChainVO ruleChain = customTravelStandard.getRuleChain();
        if (Objects.isNull(ruleChain)) {
            return null;
        }
        List<TravelStandardRuleVO> ruleList = ruleChain.getRuleList();
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }
        Optional<HotelStepRuleVo> hotelStepRuleVoOpt = ruleList.stream().filter(item -> "HotelStepRule".equals(item.getName())).map(item -> (HotelStepRuleVo) item).findFirst();
        if (!hotelStepRuleVoOpt.isPresent()) {
            return null;
        }
        HotelStepRuleVo hotelStepRuleVo = hotelStepRuleVoOpt.get();

        Optional<StepStandardVo> stepStandardVoOpt = hotelStepRuleVo.getStepStandardList().stream().sorted(Comparator.comparingInt(StepStandardVo::getSort)).findFirst();
        return stepStandardVoOpt.orElse(null);

    }

    public List<BaseValue> convertCommonTravelStandard(TravelStandardResponse travelStandard) {
        List<TravelStandardRuleVO> ruleList = getCommonRuleList(travelStandard);
        if (CollectionUtils.isEmpty(ruleList)) {
            return Collections.emptyList();
        }
        
        List<BaseValue> list = new ArrayList<>(4);
        BaseValue controlType = new BaseValue();
        controlType.setTitle(CONTROL_TYPE);
        controlType.setContent(controlTypeConvert(ruleList));
        list.add(controlType);
        BaseValue price = new BaseValue();
        price.setTitle(PRICE);
        price.setContent(priceContentConvert(ruleList));
        list.add(price);
        BaseValue star = new BaseValue();
        star.setTitle(STAR);
        star.setContent(starContentConvert(ruleList));
        list.add(star);
        BaseValue brand = new BaseValue();
        brand.setTitle(BRAND);
        brand.setContent(brandContentConvert(ruleList));
        list.add(brand);
        return list;
    }

    private List<TravelStandardRuleVO> getCommonRuleList(TravelStandardResponse travelStandard) {
        RuleChainVO ruleChain = travelStandard.getRuleChain();
        if (Objects.isNull(ruleChain)) {
            return Collections.emptyList();
        }
        return ruleChain.getRuleList();
    }


    private String customPriceContentConvert(StepStandardVo stepStandardVo) {
        if (Objects.isNull(stepStandardVo.getUpperLimit()) && (Objects.isNull(stepStandardVo.getLowerLimit()) || BigDecimal.ZERO.compareTo(stepStandardVo.getLowerLimit()) == 0)){
            return NO_LIMIT;
        }
        return (Objects.nonNull(stepStandardVo.getLowerLimit()) ? "¥" + stepStandardVo.getLowerLimit().stripTrailingZeros().toPlainString() : "0")
                + " ~ " + (Objects.nonNull(stepStandardVo.getUpperLimit()) ? "¥" + stepStandardVo.getUpperLimit().stripTrailingZeros().toPlainString() + "/间夜" : NO_LIMIT);
    }

    private String customStarContentConvert(StepStandardVo stepStandardVo) {
        return Boolean.TRUE.equals(stepStandardVo.getStarLimit()) ? NO_LIMIT : starListConvert(stepStandardVo.getStarList());
    }

    private String customBrandContentConvert(StepStandardVo stepStandardVo) {
        return Boolean.TRUE.equals(stepStandardVo.getBrandLimit()) ? NO_LIMIT : stepStandardVo.getBrandList().stream().map(BrandInfoDTO::getBrandName).collect(Collectors.joining("、"));
    }

    private String starListConvert(List<Integer> starList) {
        if (CollectionUtils.isEmpty(starList) || (starList.size() == 1 && starList.get(0) == 0)) {
            return NO_LIMIT;
        }
        return starList.stream().map(e -> e + "星级" + (e == 2 ? "及以下" : "")).collect(Collectors.joining("、"));
    }

    private String controlTypeConvert(List<TravelStandardRuleVO> ruleVOList) {
        // 同住 淡旺季 普通 任取其一
        Optional<TravelStandardRuleVO> travelStandardRuleVOOpt = ruleVOList.stream()
            .filter(item -> "CohabitRule".equals(item.getName()) || "OffPeakSeasonRule".equals(item.getName())
                || "PriceRule".equals(item.getName()))
            .findFirst();
        if (travelStandardRuleVOOpt.isPresent()) {
            TravelStandardRuleVO travelStandardRuleVO = travelStandardRuleVOOpt.get();
            String[] rejectTypes = travelStandardRuleVO.getRejectTypes();
            return controlTypeContentConvert(rejectTypes);
        }
        return ControlTypeEnum.W.getName();
    }

    private String controlTypeContentConvert(String[] rejectTypes) {
        return Arrays.stream(rejectTypes).map(ControlTypeEnum::getEnumByCode)
                .map(ControlTypeEnum::getName).collect(Collectors.joining(","));
    }

    private String priceContentConvert(List<TravelStandardRuleVO> ruleVOList) {
        //先使用同住差标
        Optional<CohabitRuleVO> cohabitRuleVOOpt = ruleVOList.stream().filter(item -> "CohabitRule".equals(item.getName())).map(item -> (CohabitRuleVO) item).findFirst();
        if (cohabitRuleVOOpt.isPresent()) {
            CohabitRuleVO cohabitRuleVO = cohabitRuleVOOpt.get();
            BigDecimal minPrice = cohabitRuleVO.getMinPrice();
            BigDecimal maxPrice = cohabitRuleVO.getMaxPrice();
            return priceRangeContentConvert(minPrice, maxPrice);
        }

        //再淡旺季差标
        List<OffPeakSeasonRuleVO> offPeakSeasonRuleVOList = ruleVOList.stream().filter(item -> "OffPeakSeasonRule".equals(item.getName())).map(item -> (OffPeakSeasonRuleVO) item).collect(Collectors.toList());
        Optional<OffPeakSeasonRuleVO> cityOffPeakSeasonRuleVOOpt = offPeakSeasonRuleVOList.stream().filter(item -> "4".equals(item.getCityType())).findFirst();
        OffPeakSeasonRuleVO offPeakSeasonRuleVO = null;
        if (cityOffPeakSeasonRuleVOOpt.isPresent()) {
             offPeakSeasonRuleVO = cityOffPeakSeasonRuleVOOpt.get();
        } else {
            Optional<OffPeakSeasonRuleVO> offPeakSeasonRuleVOOpt = offPeakSeasonRuleVOList.stream().findFirst();
            if (offPeakSeasonRuleVOOpt.isPresent()) {
                offPeakSeasonRuleVO = offPeakSeasonRuleVOOpt.get();
            }
        }

        if (Objects.nonNull(offPeakSeasonRuleVO)) {
            return priceRangeContentConvert(offPeakSeasonRuleVO.getMinPrice(), offPeakSeasonRuleVO.getMaxPrice());
        }

        //再普通差标
        List<PriceRuleVO> priceRuleVOList = ruleVOList.stream().filter(item -> "PriceRule".equals(item.getName())).map(item -> (PriceRuleVO) item).collect(Collectors.toList());
        Optional<PriceRuleVO> cityPriceRuleVOListOpt = priceRuleVOList.stream().filter(item -> "4".equals(item.getCityType())).findFirst();
        PriceRuleVO priceRuleVO = null;
        if (cityPriceRuleVOListOpt.isPresent()) {
            priceRuleVO = cityPriceRuleVOListOpt.get();
        } else {
            Optional<PriceRuleVO> priceRuleVoOpt = priceRuleVOList.stream().findFirst();
            if (priceRuleVoOpt.isPresent()) {
                priceRuleVO = priceRuleVoOpt.get();
            }
        }

        if (Objects.nonNull(priceRuleVO)) {
            return priceRangeContentConvert(priceRuleVO.getMinPrice(), priceRuleVO.getMaxPrice());
        }
        return null;
    }

    private String cohabitPriceContentConvert(List<TravelStandardResponse> userGroupTravelStandardList) {
        List<String> contentList = new ArrayList<>();

        for (TravelStandardResponse travelStandardResponse : userGroupTravelStandardList) {
            TravelStandardTokenResponse travelStandardToken = travelStandardResponse.getTravelStandardToken();
            if (Objects.isNull(travelStandardToken)) {
                continue;
            }
            RuleChainVO ruleChain = travelStandardResponse.getRuleChain();
            if (Objects.isNull(ruleChain)) {
                continue;
            }
            List<TravelStandardRuleVO> ruleVOList = ruleChain.getRuleList();
            if (CollectionUtils.isEmpty(ruleVOList)) {
                continue;
            }
            List<CohabitRuleVO> cohabitRuleVOList =
                ruleVOList.stream().filter(item -> "CohabitRule".equals(item.getName()))
                    .map(item -> (CohabitRuleVO)item).collect(Collectors.toList());
            Optional<CohabitRuleVO> cohabitRuleVOOpt = cohabitRuleVOList.stream().findFirst();
            if (!cohabitRuleVOOpt.isPresent()) {
                continue;
            }
            CohabitRuleVO cohabitRuleVO = cohabitRuleVOOpt.get();
            log.info("CohabitRule: {}" ,JsonUtils.toJsonString(cohabitRuleVO));
            BigDecimal minPrice = cohabitRuleVO.getMinPrice();
            BigDecimal maxPrice = cohabitRuleVO.getMaxPrice();
            String price;

            if (maxPrice == null || BigDecimal.ZERO.compareTo(maxPrice) == 0) {
                price = NO_LIMIT;
            } else {
                price = String.format("\"%s\"~\"%s\"", minPrice, maxPrice);
            }

            String content = String.format("房间%s：%s", travelStandardToken.getOwnerId(), price);
            contentList.add(content);
        }
        log.info("cohabitPriceContent:{}", JsonUtils.toJsonString(contentList));
        return String.join("<br>", contentList);
    }
    
    private String priceRangeContentConvert(BigDecimal minPrice, BigDecimal maxPrice) {
        if (Objects.isNull(maxPrice)) {
            return NO_LIMIT;
        }
        return Optional.ofNullable(minPrice).map(BigDecimal::toPlainString).orElse("0") + " ~ " + "¥" + maxPrice + "/间夜";
    }

    private String starContentConvert(List<TravelStandardRuleVO> ruleVOList) {
        Optional<StarRuleVO> starRuleVOOpt = ruleVOList.stream().filter(item -> "StarRule".equals(item.getName()))
            .map(item -> (StarRuleVO)item).findFirst();
        if (starRuleVOOpt.isPresent()) {
            StarRuleVO starRuleVO = starRuleVOOpt.get();
            List<Integer> starList = starRuleVO.getStarList();
            return starListConvert(starList);
        }
        return null;
    }

    private String brandContentConvert(List<TravelStandardRuleVO> ruleVOList) {
        Optional<BrandRuleVO> brandRuleVOOpt = ruleVOList.stream().filter(item -> "BrandRule".equals(item.getName()))
            .map(item -> (BrandRuleVO)item).findFirst();
        if (brandRuleVOOpt.isPresent()) {
            BrandRuleVO brandRuleVO = brandRuleVOOpt.get();
            List<BrandInfoDTO> brandList = brandRuleVO.getBrandList();
            return CollectionUtils.isEmpty(brandList) ? NO_LIMIT
                : brandList.stream().map(BrandInfoDTO::getBrandName).collect(Collectors.joining("、"));
        }
        return null;
    }

    /**
     * 文件列表赋值
     *
     * @param orderDetailResponseBo
     * @return
     */
    private List<BusinessFileInfoVo> buildFileList(OrderDetailResponseBo orderDetailResponseBo) {
        if (CollectionUtils.isEmpty(orderDetailResponseBo.getFileList())) {
            return Collections.emptyList();
        }

        return orderDetailResponseBo.getFileList().stream().map(this::createBusinessFileInfoVo)
                .collect(Collectors.toList());
    }

    /**
     * 组装文件内容
     *
     * @param file
     * @return
     */
    private BusinessFileInfoVo
    createBusinessFileInfoVo(com.corpgovernment.api.hotel.product.model.BusinessFileInfoVo file) {
        return BusinessFileInfoVo.builder()
                .fileName(file.getFileName())
                .filePath(file.getFilePath())
                .fileSize(file.getFileSize())
                .fileType(file.getFileType())
                .fileUrl(file.getFileUrl())
                .build();
    }

    /**
     * 构建订单费用信息
     */
    private OrderFeeDetail buildOrderFeeDetail(HoHotelBo hoHotelBo, HoOrderBo hoOrderBo,
                                               List<HoRoomDailyBo> roomDailyBoList) {
        OrderFeeDetail orderFeeDetail = new OrderFeeDetail();
        orderFeeDetail.setServiceFee(hoOrderBo.getServiceFee());
        orderFeeDetail.setTotalAmount(hoOrderBo.getAmount());
        orderFeeDetail.setDeliveryAmount(hoOrderBo.getDeliveryPrice());
        orderFeeDetail.setRoomDailyInfoVoList(
                roomDailyBoList.stream().map(this::buildRoomDailyInfo).collect(Collectors.toList()));
        orderFeeDetail.setAPayAmount(hoOrderBo.getAPayAmount());
        orderFeeDetail.setPPayAmount(hoOrderBo.getPPayAmount());
        return orderFeeDetail;
    }

    /**
     * 构建每日房价信息
     */
    private RoomDailyInfoVo buildRoomDailyInfo(HoRoomDailyBo hoRoomDailyBo) {
        RoomDailyInfoVo roomDailyInfoVo = new RoomDailyInfoVo();
        roomDailyInfoVo.setBookingDate(DateUtil.dateToString(hoRoomDailyBo.getEffectDate(), DateUtil.DF_YMD));
        roomDailyInfoVo.setBreakfastName(hoRoomDailyBo.getBreakfastName());
        roomDailyInfoVo.setPrice(hoRoomDailyBo.getRoomPrice());
        return roomDailyInfoVo;
    }

    /**
     * 构建订单支付信息
     */
    private OrderPayInfoVo buildOrderPayInfo(PpPaymentBillDto paymentBillDto, String payType) {
        OrderPayInfoVo orderPayInfoVo = new OrderPayInfoVo();
        orderPayInfoVo.setPayAmount("￥" + paymentBillDto.getAmount());
        PayTypeEnum payChannel = PayTypeEnum.get(paymentBillDto.getPayType());
        orderPayInfoVo.setPayChannel(payChannel == null ? null : payChannel.getName());
        orderPayInfoVo.setPayTypeName(Objects.equals(payType, "K") ? "扣款" : "退款");
        orderPayInfoVo.setPayDateStr(DateUtil.dateToString(paymentBillDto.getTxTime()));
        orderPayInfoVo.setPayStatusName(PayStatusEnum.valueOf(paymentBillDto.getStatus()).getName());
        orderPayInfoVo.setRemark(paymentBillDto.getRemark());
        return orderPayInfoVo;
    }

    /**
     * 构建订单状态信息
     *
     * @param hoOrderBo 订单信息
     * @param hoInvoiceBo 发票信息
     * @param paymentBillResponse 支付单明细信息
     */
    private OrderStatusInfoVo buildOrderStatusInfo(HoOrderBo hoOrderBo, HoInvoiceBo hoInvoiceBo,
                                                   PaymentBillDetailResponse paymentBillResponse) {
        OrderStatusInfoVo orderStatusInfoVo = new OrderStatusInfoVo();
        if (PayTypeEnum.ACCNT.getType().equals(hoOrderBo.getPayType())) {
            AccountOrderStatusEnum orderStatusEnum = AccountOrderStatusEnum.getEnum(hoOrderBo.getOrderStatus());
            if (orderStatusEnum != null) {
                orderStatusInfoVo.setStatusName(orderStatusEnum.getName());
                orderStatusInfoVo.setStatusNode(orderStatusEnum.getNode());
                orderStatusInfoVo.setOrderStatusNameList(orderStatusEnum.getList(hoOrderBo.getOrderStatus()));
            }
        } else {
            PpPayOrderStatusEnum orderStatusEnum = PpPayOrderStatusEnum.getEnum(hoOrderBo.getOrderStatus());
            if (orderStatusEnum != null) {
                orderStatusInfoVo.setStatusName(orderStatusEnum.getName());
                orderStatusInfoVo.setStatusNode(orderStatusEnum.getNode());
                orderStatusInfoVo.setOrderStatusNameList(PpPayOrderStatusEnum.getList(hoOrderBo.getOrderStatus()));
            }
        }
        if (OrderStatusEnum.PW.getCode().equals(hoOrderBo.getOrderStatus())
                || OrderStatusEnum.AW.getCode().equals(hoOrderBo.getOrderStatus())
                || OrderStatusEnum.TA.getCode().equals(hoOrderBo.getOrderStatus())
                || OrderStatusEnum.TW.getCode().equals(hoOrderBo.getOrderStatus())) {
            orderStatusInfoVo.setCancelCheck(true);
        }
        orderStatusInfoVo.setCreateInvoiceFlag(reCreateInvoiceFlag(hoOrderBo.getOrderStatus(),
                hoInvoiceBo, hoOrderBo.getPayType(), paymentBillResponse, hoOrderBo.getLadderAmount(), hoOrderBo));
        return orderStatusInfoVo;
    }

    /**
     * 构建订单配送信息
     *
     * @param hoHotelBo 酒店信息
     * @param hoOrderBo 订单信息
     * @param hoDeliveryInfoBo 配送信息
     * @param hoPassengerBoList 入住人信息
     * @param hoInvoiceBo 发票信息
     * @param hoRoomBo 房型信息
     */
    private OrderDeliverInfoVo buildOrderDeliverInfo(HoHotelBo hoHotelBo, HoOrderBo hoOrderBo,
                                                     HoDeliveryInfoBo hoDeliveryInfoBo, List<HoPassengerBo> hoPassengerBoList, HoInvoiceBo hoInvoiceBo,
                                                     HoRoomBo hoRoomBo) {
        OrderDeliverInfoVo orderDeliverInfoVo = new OrderDeliverInfoVo();

        orderDeliverInfoVo.setAmount(hoInvoiceBo.getInvoiceAmount());
        orderDeliverInfoVo.setPassengerName(StringUtils.isNotBlank(hoPassengerBoList.get(0).getTravelerName())
                ? hoPassengerBoList.get(0).getTravelerName() : hoPassengerBoList.get(0).getPassengerName());
        orderDeliverInfoVo.setCheckInDesc(
                hoHotelBo.getHotelName() + " " + DateUtil.dateToString(hoRoomBo.getCheckInDate(), DateUtil.DF_YMD) + "-"
                        + DateUtil.dateToString(hoRoomBo.getCheckOutDate(), DateUtil.DF_YMD));
        if(StringUtils.isNotBlank(hoInvoiceBo.getInvoiceType())){
            String desc = InvoiceEnum.getDesc(hoInvoiceBo.getInvoiceType());
            if(desc == null){
                log.warn("+++++++ illegal invoice type:{}", hoInvoiceBo.getInvoiceType());
            }
            orderDeliverInfoVo.setInvoiceTypeName(desc);
        }
        orderDeliverInfoVo.setDeliveryType(DeliveryTypeEnum.getByCode(hoOrderBo.getDeliveryType()).getDesc());
        orderDeliverInfoVo.setProvideBillTypeName(ProvideBillTypeEnum.getByType(hoInvoiceBo.getLadderCancel()).getDesc());
        orderDeliverInfoVo.setDeliveryDetails(buildDeliveryDetailList(hoOrderBo, hoDeliveryInfoBo, hoInvoiceBo));
        return orderDeliverInfoVo;
    }

    /**
     * 构建配送信息明细列表
     *
     * @param hoOrderBo 订单信息
     * @param hoDeliveryInfoBo 配送信息
     * @param hoInvoiceBo 发票信息
     */
    private List<BaseValue> buildDeliveryDetailList(HoOrderBo hoOrderBo, HoDeliveryInfoBo hoDeliveryInfoBo,
                                                    HoInvoiceBo hoInvoiceBo) {
        List<BaseValue> baseValueList = new ArrayList<>();
        if (InvoiceTypeEnum.C.getType().equals(hoInvoiceBo.getInvoiceType())) {
            addDeliverDetail(baseValueList, "收件人", hoDeliveryInfoBo.getRecipientName());
            addDeliverDetail(baseValueList, "收件电话", hoDeliveryInfoBo.getRecipientMobilePhone());
            addDeliverDetail(baseValueList, "收件地址", hoDeliveryInfoBo.getProvinceName() + hoDeliveryInfoBo.getCityName()
                    + hoDeliveryInfoBo.getDistrictName() + hoDeliveryInfoBo.getAddress());
            addDeliverDetail(baseValueList, "配送费", String.valueOf(hoOrderBo.getDeliveryPrice()));
        } else {
            addDeliverDetail(baseValueList, "联系人姓名", hoOrderBo.getContactName());
            addDeliverDetail(baseValueList, "联系人邮箱", hoOrderBo.getContactEmail());
        }
        addDeliverDetail(baseValueList, "发票抬头", hoInvoiceBo.getInvoiceTitle());
        addDeliverDetail(baseValueList, "纳税人识别号", hoInvoiceBo.getTaxpayerNumber());
        addDeliverDetail(baseValueList, "公司地址", hoInvoiceBo.getCorporationAddress());
        addDeliverDetail(baseValueList, "公司电话", hoInvoiceBo.getCorporationTel());
        addDeliverDetail(baseValueList, "开户银行地址", hoInvoiceBo.getAccountBank());
        addDeliverDetail(baseValueList, "开户银行卡号", hoInvoiceBo.getAccountCardNo());
        return baseValueList;
    }

    /**
     * 添加配送明细信息
     *
     * @param detailList 明细列表
     * @param title 配送信息标题
     * @param content 配送信息内容
     */
    private void addDeliverDetail(List<BaseValue> detailList, String title, String content) {
        BaseValue baseValue = new BaseValue();
        baseValue.setTitle(title);
        baseValue.setContent(content);
        detailList.add(baseValue);
    }

    /**
     * 构建酒店信息
     *
     * @param hoRoomDailyBoList 每日房价信息
     * @param hoHotelBo 酒店信息
     * @param hoOrderBo 订单信息
     * @param hoRoomBo 房型信息
     */
    private OrderHotelVo buildOrderHotelVo(List<HoRoomDailyBo> hoRoomDailyBoList, HoHotelBo hoHotelBo,
                                           HoOrderBo hoOrderBo, HoRoomBo hoRoomBo, List<HoHotelApplyBo> hoHotelApplyBoList) {
        OrderHotelVo orderHotelVo = new OrderHotelVo();
        orderHotelVo.setOrderId(hoOrderBo.getOrderId());
        orderHotelVo.setHotelName(hoHotelBo.getHotelName());
        orderHotelVo.setCheckInDate(DateUtil.dateToString(hoRoomBo.getCheckInDate(), DateUtil.DF_YMD));
        orderHotelVo.setCheckOutDate(DateUtil.dateToString(hoRoomBo.getCheckOutDate(), DateUtil.DF_YMD));
        orderHotelVo.setRoomName(hoRoomBo.getRoomName());
        orderHotelVo.setNextDay(hoRoomBo.getNextDay());
        orderHotelVo.setRoomQuantity(hoRoomBo.getRoomQuantity());
        orderHotelVo.setHotelPhone(hoHotelBo.getHotelPhone());
        CancelPolicyEnum cancelPolicyEnum = CancelPolicyEnum.getByType(hoRoomBo.getCancelPolicyType());
        orderHotelVo.setCancelType(cancelPolicyEnum == null ? null : cancelPolicyEnum.getTitle());
        orderHotelVo.setCancelNote(hoRoomBo.getCancelPolicyDesc());
        orderHotelVo.setNewLastArrival(DateUtil.dateToString(hoHotelBo.getLastArrivalTime(), DateUtil.DF_YMD_HM));
        orderHotelVo.setSpecialNeed(hoOrderBo.getSpecialNeed());
        orderHotelVo.setBreakfast(hoRoomDailyBoList.get(0).getBreakfastName());
        orderHotelVo.setHotelAddress(hoHotelBo.getAddress());
        orderHotelVo.setHotelTips(hoHotelBo.getHotelTips());
        orderHotelVo.setLongitude(hoHotelBo.getLongitude());
        orderHotelVo.setLatitude(hoHotelBo.getLatitude());
        orderHotelVo.setBedName(hoRoomBo.getBedName());
        orderHotelVo.setHotelType(hoHotelBo.getHotelType());
        orderHotelVo.setCityId(hoHotelBo.getCityId());
        orderHotelVo.setCityName(hoHotelBo.getCityName());
        orderHotelVo.setBrandId(hoHotelBo.getBrandId());
        orderHotelVo.setBrandName(hoHotelBo.getBrandName());
        orderHotelVo.setContractedFlag(HotelTypeEnum.C.getType().equals(hoHotelBo.getHotelType()));
        orderHotelVo.setEarlyArrivalTime(DateUtil.dateToString(hoHotelBo.getEarlyArrivalTime(), DateUtil.DF_YMD_HM));
        orderHotelVo.setHasHotelModify(hasHotelModify(hoHotelApplyBoList));
        return orderHotelVo;
    }

    private boolean hasHotelModify(List<HoHotelApplyBo> hoHotelApplyBoList) {
        if (CollectionUtils.isEmpty(hoHotelApplyBoList)) {
            return false;
        }
        return hoHotelApplyBoList.stream()
                .anyMatch(item -> !Lists
                        .newArrayList(HotelModifyStatusEnum.FAILED.getCode(), HotelModifyStatusEnum.CANCELED.getCode())
                        .contains(item.getStatus()));
    }

    /**
     * 构建入住人信息列表
     *
     * @param hoPassengerBoList 数据库中的入住人信息
     * @param searchOrderInfo 分销查询到的订单信息
     */
    private List<OrderPassengerVo> buildOrderPassengerList(List<HoPassengerBo> hoPassengerBoList,
                                                           HotelOrderInfo searchOrderInfo) {
        // 获取入住人信息
        Map<String, List<ClientInfoEntity>> clientInfoMap = Optional.ofNullable(searchOrderInfo)
                .map(HotelOrderInfo::getClientInfo)
                .orElse(Collections.emptyList()).stream()
                .collect(Collectors.groupingBy(ClientInfoEntity::getClientName));

        List<OrderPassengerVo> orderPassengerVos = new ArrayList<>();
        for (int i = 0; i < hoPassengerBoList.size(); i++) {
            HoPassengerBo hoPassengerBo = hoPassengerBoList.get(i);

            // 获取从分销获取到的对应入住人
            List<ClientInfoEntity> clientInfoList =
                    clientInfoMap.getOrDefault(StringUtils.isNotBlank(hoPassengerBo.getTravelerName())
                            ? hoPassengerBo.getTravelerName() : hoPassengerBo.getPassengerName(), Collections.emptyList());
            ClientInfoEntity clientInfo = clientInfoList.stream().findFirst().orElse(null);

            // 如果通过姓名找到的入住人数大于等于2，说明有同名同姓的人，理论上不该有，先记个日志
            int clientCount = clientInfoList.size();
            if (clientCount >= 2) {
                log.error("查询分销酒店订单，发现有同名同姓入住人，入住人信息：" + JsonUtils.toJsonString(clientInfoMap));
            }

            // 构建入住人信息
            orderPassengerVos.add(buildOrderPassengerVo(i, hoPassengerBo, clientInfo));
        }
        return orderPassengerVos;
    }

    /**
     * 构建入住人信息
     *
     * @param i 编号
     * @param hoPassengerBo 落库的入住人信息
     * @param clientInfo 分销返回的入住人信息
     */
    private OrderPassengerVo buildOrderPassengerVo(int i, HoPassengerBo hoPassengerBo, ClientInfoEntity clientInfo) {
        OrderPassengerVo orderPassengerVo = new OrderPassengerVo();
        orderPassengerVo.setPassengerUid(hoPassengerBo.getUid());
        orderPassengerVo.setSequenceNo(i + 1);
        orderPassengerVo.setIsSendMessage(hoPassengerBo.getIsSendSms() ? "是" : "否");
        orderPassengerVo.setPassengerPhone(hoPassengerBo.getMobilePhone());
        orderPassengerVo.setPassengerName(hoPassengerBo.getPassengerName());
        orderPassengerVo.setTravelerName(hoPassengerBo.getTravelerName());
        GenderEnum genderEnum = GenderEnum.getByCode(hoPassengerBo.getGender());
        orderPassengerVo.setGender(genderEnum == null ? null : genderEnum.getDesc());
        orderPassengerVo.setStaffFlag(StringUtils.isNotBlank(hoPassengerBo.getUid()));
        orderPassengerVo.setVipLevel(hoPassengerBo.getVipLevel());
        orderPassengerVo.setCostCenterId(hoPassengerBo.getCostCenterId());
        orderPassengerVo.setCostCenterCode(hoPassengerBo.getCostCenterCode());
        orderPassengerVo.setCostCenterName(hoPassengerBo.getCostCenterName());
        orderPassengerVo.setCostCenterRemark(hoPassengerBo.getCostCenterRemark());

        orderPassengerVo.setProjectId(hoPassengerBo.getProjectId());
        orderPassengerVo.setProjectCode(hoPassengerBo.getProjectCode());
        orderPassengerVo.setProjectName(hoPassengerBo.getProjectName());
        orderPassengerVo.setWbsRemark(hoPassengerBo.getWbsRemark());
        orderPassengerVo.setNoSelectProjectDesc(hoPassengerBo.getNoSelectProjectDesc());

        orderPassengerVo.setDepartmentName(hoPassengerBo.getDepartmentName());
        orderPassengerVo.setEmployeeType(hoPassengerBo.getEmployeeType());
        orderPassengerVo.setEmployeeDesc(ApplyTripEmployeeEnum.getNameByCode(hoPassengerBo.getEmployeeType()));

        if (clientInfo != null && clientInfo.getActualCheckInTime() != null
                && clientInfo.getActualDepartureTime() != null) {
            // 分销返回的是带时分秒的结构，截取年月日部分
            if (clientInfo.getActualCheckInTime().length() > 10) {
                orderPassengerVo.setActualCheckInDate(clientInfo.getActualCheckInTime().substring(0, 10));
            }
            if (clientInfo.getActualDepartureTime().length() > 10) {
                orderPassengerVo.setActualCheckOutDate(clientInfo.getActualDepartureTime().substring(0, 10));
            }
        }
        orderPassengerVo.setRoomIndex(hoPassengerBo.getRoomIndex());
        orderPassengerVo.setEmail(hoPassengerBo.getEmail());
        orderPassengerVo.setCardNo(hoPassengerBo.getCardNo());

        orderPassengerVo.setCostCenterVoList(convertMultiCostCenter(hoPassengerBo.getMultiCostCenterDataJSON()));
        orderPassengerVo.setAccountingUnitList(convertAccountingUnitConfig(hoPassengerBo.getOrgId(),convertAccountingUnitInfo(hoPassengerBo.getAccountingUnitJSON())));
        return orderPassengerVo;
    }

    /**
     * 获取供应订单详情
     */
    public OrderDetailResponse.HotelOrderInfo searchOrder(String supplierOrderId, String supplierCode,
        String supplierCorpId) {
        StandardOrderDetailRequest orderDetailRequest = new StandardOrderDetailRequest();
        orderDetailRequest.setOrderID(supplierOrderId);
        orderDetailRequest.setSupplierCode(supplierCode);
        return ctripSlSupplier.supplierOrderDetail(orderDetailRequest);
    }

    private OrderDetailResponseBo getOrderInfo(QueryOrderDetailRequestDto queryOrderDetailRequestDto) {
        // 查询订单详情
        QueryOrderDetailRequestBo queryOrderDetailRequestBo = new QueryOrderDetailRequestBo();
        queryOrderDetailRequestBo.setOrderId(queryOrderDetailRequestDto.getOrderId());
        OrderDetailResponseBo orderDetailResponseBo = orderDetailService.queryOrderDetail(queryOrderDetailRequestBo);
        if (orderDetailResponseBo == null || StringUtils.isNotBlank(orderDetailResponseBo.getMessage())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.EXCEPTION_QUERY_ORDER_DETAILS,
                    orderDetailResponseBo == null ? "查询订单详情异常" : orderDetailResponseBo.getMessage());
        }
        log.info("查询到的订单详情:{}", JsonUtils.toJsonString(orderDetailResponseBo));
        return orderDetailResponseBo;
    }

    // 可重开发票标识
    private Boolean reCreateInvoiceFlag(String orderStatus, HoInvoiceBo hoInvoiceBo, String payType,
                                        PaymentBillDetailResponse paymentBillResponse, BigDecimal ladderAmount, HoOrderBo hoOrderBo) {
        Boolean reCreateInvoiceFlag = true;
        if (PayTypeEnum.ACCNT.getType().equals(payType)
                || (hoInvoiceBo != null && (!OrderStatusEnum.CA.getCode().equals(orderStatus)))) {
            reCreateInvoiceFlag = false;
        }
        if (!(OrderStatusEnum.TA.getCode().equals(orderStatus) || OrderStatusEnum.CA.getCode().equals(orderStatus)
                || OrderStatusEnum.ED.getCode().equals(orderStatus))) {
            reCreateInvoiceFlag = false;
        }
        if (OrderStatusEnum.CA.getCode().equals(orderStatus)
                && (ladderAmount == null || ladderAmount.compareTo(BigDecimal.ZERO) <= 0)) {
            reCreateInvoiceFlag = false;
        }
        if (paymentBillResponse == null || !"S".equals(paymentBillResponse.getStatus())) {
            reCreateInvoiceFlag = false;
        }
        if (Objects.equals(hoOrderBo.getPayType(), PayTypeEnum.CASH.getType())) {
            reCreateInvoiceFlag = false;
        }
        return reCreateInvoiceFlag;
    }

    public JSONResult reCreateInvoice(ReCreateInvoiceRequestVo reCreateInvoiceRequestVo) {
        ReCreateInvoiceRequestBo reCreateInvoiceRequestBo = new ReCreateInvoiceRequestBo();
        BeanUtils.copyProperties(reCreateInvoiceRequestVo, reCreateInvoiceRequestBo);
        return orderDetailService.reCreateInvoice(reCreateInvoiceRequestBo);
    }

    public JSONResult saveSpecialNeed(SaveSpecialNeedRequestVo saveSpecialNeedRequestVo) {
        HoOrderBo hoOrderBo = new HoOrderBo();
        hoOrderBo.setOrderId(saveSpecialNeedRequestVo.getOrderId());
        hoOrderBo.setSpecialNeed(saveSpecialNeedRequestVo.getContent());
        return orderDetailService.saveSpecialNeed(hoOrderBo);
    }

    private PayTokenInfo createPayTokenInfo(HoOrderBo hoOrderBo, HoHotelBo hoHotelBo, HoRoomBo hoRoomBo,
                                            List<HoPassengerBo> hoPassengerBos) {
        PayTokenInfo payTokenInfo = new PayTokenInfo();
        payTokenInfo.setOrderId(hoOrderBo.getOrderId().toString());
        String subject = String.format("%s %s", hoHotelBo.getCityName(), hoHotelBo.getHotelName());
        payTokenInfo.setSubject(subject);
        String context =
                String.format("入住时间：%s 离店时间%s", DateUtil.dateToString(hoRoomBo.getCheckInDate(), DateUtil.DF_YMD),
                        DateUtil.dateToString(hoRoomBo.getCheckOutDate(), DateUtil.DF_YMD));
        payTokenInfo.setGoods(context);
        String userNames =
                hoPassengerBos.stream().map(HoPassengerBo::getPassengerName).collect(Collectors.joining("，"));
        payTokenInfo.setUserNames(userNames);
        // 支付订单title
        payTokenInfo.setTitle(subject);
        PayInfoRequest payInfoRequest = new PayInfoRequest();
        payInfoRequest.setTransport("hotel");
        payInfoRequest.setName(hoOrderBo.getCorpPayType());
        // todo 支付方式修改
        // payInfoRequest.setUId(hoOrderBo.getUid());
        // 无原单支付方式时，走默认支付方式
        // if (CollectionUtils.isEmpty(payTokenInfo.getPayKeyList())) {
        // PayInfoResponse userPayInfo = switchSoaService.getUserPayInfo(payInfoRequest);
        // if (userPayInfo != null) {
        // payTokenInfo.setPayKeyList(userPayInfo.getType());
        // }
        // }
        return PPayUtil.createPayTokenInfo(payTokenInfo);
    }

    /**
     * 查询订单详情时，调用供应商取消问询接口确认订单可以进行哪种取消方式
     *
     * @param orderDetailResponseVo
     */
    private void cancelOrderQuery(OrderDetailResponseVo orderDetailResponseVo) {
        CancelOrderRequestDto cancelOrderRequestDto = new CancelOrderRequestDto();
        cancelOrderRequestDto.setOrderId(orderDetailResponseVo.getOrderId());
        CancelOrderQueryResponse queryResponse = cancelOrderService.cancelOrderQuery(cancelOrderRequestDto);
        if (BooleanUtils.isTrue(queryResponse.getSuccess()) && queryResponse.getResponseTypeVo() != null) {
            CancelOrderQueryResponseTypeVo responseTypeVo = queryResponse.getResponseTypeVo();
            CancelQueryInfoType cancelQueryInfo = responseTypeVo.getCancelQueryInfo();
            orderDetailResponseVo.setCanApplyCancel(
                    Objects.nonNull(cancelQueryInfo.getCanApplyCancel()) && cancelQueryInfo.getCanApplyCancel());
            orderDetailResponseVo
                    .setCanCancel(Objects.nonNull(cancelQueryInfo.getCanCancel()) && cancelQueryInfo.getCanCancel());
        }
    }

    /**
     * 查询订单详情时，调用供应商取消问询接口确认订单可以进行哪种取消方式
     *
     * @param orderDetailResponseVo
     * @param orgId
     * @param travelerCancellationAndChangeAuthorityControl
     */
    private void cancelOrderQueryNew(OrderDetailResponseVo orderDetailResponseVo, String orgId, boolean travelerCancellationAndChangeAuthorityControl) {
        CancelOrderRequestDto cancelOrderRequestDto = new CancelOrderRequestDto();
        cancelOrderRequestDto.setOrderId(orderDetailResponseVo.getOrderId());
        StandardCancelOrderInquiryResponse response = cancelOrderService.cancelOrderQueryNew(cancelOrderRequestDto);
        if (response != null && response.getCancelQueryInfo() != null) {
            orderDetailResponseVo.setCanApplyCancel(Objects.nonNull(response.getCancelQueryInfo().getCanApplyCancel()) && response.getCancelQueryInfo().getCanApplyCancel());
            orderDetailResponseVo.setCanCancel(Objects.nonNull(response.getCancelQueryInfo().getCanCancel()) && response.getCancelQueryInfo().getCanCancel());
        }

        orderDetailResponseVo.setForceCancel(Boolean.FALSE);
        boolean cannotCancel = !Boolean.TRUE.equals(orderDetailResponseVo.getCanApplyCancel()) && !Boolean.TRUE.equals(orderDetailResponseVo.getCanCancel());
        if (cannotCancel) {
            //获取[是否支持强制取消]配置
            SupplierCompanyBo supplierCompany = supplierCompanyClientLoader.findSupplierCompany(orgId, orderDetailResponseVo.getSupplierCode(), 3);
            Optional.ofNullable(supplierCompany).map(SupplierCompanyBo::getForceCancelModify).ifPresent(
                forceCancelModify -> {
                    if (BooleanUtils.toBoolean(forceCancelModify)) {
                        Long orderId = orderDetailResponseVo.getOrderId();
                        //订单非取消且无修改中/取消中的单据
                        Boolean asyncCancel = orderDetailResponseVo.getAsyncCancel();
                        Boolean existBeingModified = hoHotelApplyLoader.orderExistBeingModified(orderId);
                        Boolean existBeingCanceled = hoOrderCancelFormLoader.orderExistBeingCanceled(orderId);
                        if (!asyncCancel && !existBeingModified && !existBeingCanceled) {
                            orderDetailResponseVo.setForceCancel(BooleanUtils.toBoolean(forceCancelModify));
                        }
                    }
                });
        }

        orderDetailResponseVo.setAllowTravelerCancel(Boolean.TRUE);
        boolean travelForBusiness = CorpPayTypeEnum.PUB.getType().equals(orderDetailResponseVo.getCorpPayType());
        if (travelForBusiness) {
            // 勾选【酒店订单取消/修改整单】时，预订人、出行人可见取消/修改按钮（服务商返回可取消/可修改的前提下），可进行取消/修改操作
            boolean supportCancel = orderDetailResponseVo.getForceCancel() || !cannotCancel;
            if (supportCancel) {
                if (!travelerCancellationAndChangeAuthorityControl) {
                    BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
                    boolean isBooker = baseUserInfo.getUid().equals(orderDetailResponseVo.getBookingUid());
                    if (!isBooker) {
                        orderDetailResponseVo.setForceCancel(Boolean.FALSE);
                        orderDetailResponseVo.setCanCancel(Boolean.FALSE);
                        orderDetailResponseVo.setCanApplyCancel(Boolean.FALSE);
                        orderDetailResponseVo.setAllowTravelerCancel(Boolean.FALSE);
                    }
                }
            }
        }
    }

    public QueryOrderListHotelResponse queryHotelOrderDetail(Long orderId) {
        QueryOrderDetailRequestDto searchOrderRequestDto = new QueryOrderDetailRequestDto();
        searchOrderRequestDto.setOrderId(orderId);
        OrderDetailManageResponseVo orderDetailManageResponseVo =
                this.queryOrderDetailForManage(searchOrderRequestDto, true);
        // 修改了QueryOrderListHotelResponse，还需要重新打包下basic-manage服务，不然修改也不生效
        return JsonUtils.convert(orderDetailManageResponseVo, QueryOrderListHotelResponse.class);
    }

    public List<SearchPassengerResponse> queryHotelPassenger(SearchPassengerRequest request) {
        List<SearchPassengerResponse> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(request.getOrderIds())) {
            return list;
        }
        List<HoOrder> orderList = hoOrderLoader.selectByOrderIds(request.getOrderIds());

        Map<Long, String> orderMap = orderList.stream().filter(item -> StringUtils.isNotBlank(item.getTripApplyNo()))
                .collect(Collectors.toMap(HoOrder::getOrderId, HoOrder::getTripApplyNo, (k1, k2) -> k1));

        List<HoPassenger> toPassengers = hoPassengerLoader
                .selectByOrderIds(orderList.stream().map(HoOrder::getOrderId).collect(Collectors.toList()));

        toPassengers.forEach(item -> {
            SearchPassengerResponse response = new SearchPassengerResponse();
            response.setOrderId(item.getOrderId());
            response.setUid(item.getUid());
            if (StringUtils.isBlank(item.getUid())) {
                response.setUid(item.getNoEmployeeId() + "");
            }
            response.setWbsRemark(item.getWbsRemark());
            response.setCostCenterRemark(item.getCostCenterRemark());
            response.setApplyNo(orderMap.get(item.getOrderId()));
            response.setPassengerName(item.getPassengerName());
            response.setEmployeeType(item.getEmployeeType());
            list.add(response);
        });
        return list;

    }

    private StandardOrderModificationInquiryResponse canModifyOrder(Long orderId, String supplierOrderId) {
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        if (hoOrder == null) {
            return null;
        }
        StandardOrderModificationInquiryRequest standardOrderModificationInquiryRequest = new StandardOrderModificationInquiryRequest();
        standardOrderModificationInquiryRequest.setSupplierCode(hoOrder.getSupplierCode());
        standardOrderModificationInquiryRequest.setCompanyCode(hoOrder.getCorpId());
        standardOrderModificationInquiryRequest.setCorpPayType(hoOrder.getCorpPayType());

        standardOrderModificationInquiryRequest.setOrderID(supplierOrderId);
        Map<String, Object> additionalInformationMap = new HashMap<>();
        //标准实现类中使用
        additionalInformationMap.put(SupplierConstant.OrderModifyInquiry.REQUEST_ADDITIONAL_MAP_KEY_ORDER_ID, hoOrder.getOrderId());
        additionalInformationMap.put(SupplierConstant.OrderModifyInquiry.REQUEST_ADDITIONAL_MAP_KEY_ORDER_RESOURCE, hoOrder.getOrderResource());
        standardOrderModificationInquiryRequest.setAdditionalInformationMap(additionalInformationMap);
        log.info("additionalInformationMap:{}", JsonUtils.toJsonString(additionalInformationMap));
        return supplierSoaClient.queryOrderModification(standardOrderModificationInquiryRequest);
    }

    /**
     * 是否展示提前离店进度
     */
    private Boolean displayModifyProcess(Long orderId) {
        List<HoHotelApply> list = hoHotelApplyLoader.select(orderId);
        return CollectionUtils.isNotEmpty(list);
    }

    private List<OrderDetailResponseVo.ClockDetail> getClockDetailList(OrderDetailResponseBo orderDetail) {
        //判定是否启用酒店打卡功能
        if (!getClockEnable()) { // 未启用不展示
            return Collections.emptyList();
        }
        HoOrderBo orderInfo = orderDetail.getOrderInfo();
        Date actualCheckInTime = orderInfo.getActualCheckInTime();
        Date actualCheckOutTime = orderInfo.getActualCheckOutTime();
        //后续废弃以上字段时 需修改为根据newestCheckInOutDate判断
        boolean historyRecord = Objects.isNull(actualCheckInTime) || Objects.isNull(actualCheckOutTime);
        if (historyRecord) { //历史数据不展示
            return Collections.emptyList();
        }

        String corpPayType = orderInfo.getCorpPayType();
        boolean travelByOwn = CorpPayTypeEnum.OWN.getType().equals(corpPayType);
        if (travelByOwn) { //因私不展示打卡
            return Collections.emptyList();
        }

        String orderStatus = orderInfo.getOrderStatus();
        boolean notConfirmedOrNotCompleted = !OrderStatusEnum.TA.getCode().equals(orderStatus) && !OrderStatusEnum.ED.getCode().equals(orderStatus);
        if (notConfirmedOrNotCompleted) {
            return Collections.emptyList();
        }

        List<OrderDetailResponseVo.ClockDetail> clockDetailList = new ArrayList<>(2);
        Date now = new Date();
        List<HoOrderClockRecord> hoOrderClockRecordList = hoOrderClockRecordLoader.listByOrderId(orderInfo.getOrderId());
        clockDetailList.add(getClockInDetail(orderInfo, now, hoOrderClockRecordList));
        clockDetailList.add(getClockOutDetail(orderInfo, now, hoOrderClockRecordList));
        return clockDetailList;
    }

    /**
     * 获取酒店打卡启用
     *
     * @return boolean
     */
    private boolean getClockEnable() {
        GetSwitchListRequest request = new GetSwitchListRequest();
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        request.setOrgId(baseUserInfo.getOrgId());
        request.setUId(baseUserInfo.getUid());
        request.setSwitchKey(SwitchEnum.HOTEL_ENABLE_VISIT_CONTROL.getKey());
        Map<String, Object> switchValueMap = switchClientLoader.getSwitchValueMap(request);
        if (CollectionUtils.isEmpty(switchValueMap)) { //获取配置失败 按照配置默认值走 即启用
            return true;
        }
        Object switchValue = switchValueMap.get(SwitchEnum.HOTEL_ENABLE_VISIT_CONTROL.getKey());
        if (Objects.isNull(switchValue)) { //获取配置失败 按照配置默认值走 即启用
            return true;
        }
        List<String> switchValueList = JsonUtils.parseArray(switchValue.toString(), String.class);
        return switchValueList.contains(HOTEL_ENABLE_VISIT_CONTROL_ENABLE);
    }

    /**
     * 获取入住打卡明细
     *
     * @param orderInfo 订单信息
     * @param now 现在时间
     * @param hoOrderClockRecordList 打卡记录信息
     * @return {@link OrderDetailResponseVo.ClockDetail }
     */
    private OrderDetailResponseVo.ClockDetail getClockInDetail(HoOrderBo orderInfo, Date now,
        List<HoOrderClockRecord> hoOrderClockRecordList) {

        OrderDetailResponseVo.ClockDetail clockInDetail = new OrderDetailResponseVo.ClockDetail();
        clockInDetail.setType(HotelClockTypeEnum.CLOCK_IN.getCode());

        Date earliestCheckInDate = getEarliestCheckInDate(orderInfo);
        boolean earliestCheckInTimeNotUp = now.before(DateUtils.getDayBegin(earliestCheckInDate));
        if (earliestCheckInTimeNotUp) { //未到实际入住日0点
            clockInDetail.setButtonShow(Boolean.FALSE);
            clockInDetail.setTips(ClockTipEnum.CHECK_IN_TIME_NOT_UP.getZh());
            return clockInDetail;
        }

        // 入住打卡
        Optional<HoOrderClockRecord> hoOrderClockInRecordOpt = hoOrderClockRecordList.stream()
            .filter(item -> HotelClockTypeEnum.CLOCK_IN.getCode().equals(item.getType())).findFirst();
        boolean clockedIn = hoOrderClockInRecordOpt.isPresent();

        Date latestCheckOutTime = getLatestCheckOutTime(orderInfo);
        Date latestCheckOutTimeNoon = getLatestCheckOutTimeNoon(latestCheckOutTime);

        boolean passedLatestCheckOutTimeNoon = now.after(latestCheckOutTimeNoon);
        if (passedLatestCheckOutTimeNoon) { // 已过实际离店日12点
            clockInDetail.setButtonShow(Boolean.FALSE);
            clockInDetail.setTag(clockedIn ? ClockTipEnum.CLOCKED_TAG.getZh() : ClockTipEnum.EXPIRED_TAG.getZh());
            clockInDetail.setTips(clockedIn ? getClockInCompletedTips(hoOrderClockInRecordOpt.get().getClockTime()) : getClockInTimeTips(earliestCheckInDate, latestCheckOutTimeNoon));
            return clockInDetail;
        }

        //实际入住日0点 < 当前时间 < 实际离店日12点
        clockInDetail.setButtonShow(clockedIn ? Boolean.FALSE : Boolean.TRUE);
        clockInDetail.setTag(clockedIn ? ClockTipEnum.CLOCKED_TAG.getZh() : null);
        clockInDetail.setTips(clockedIn ? getClockInCompletedTips(hoOrderClockInRecordOpt.get().getClockTime()) : getClockInTimeTips(earliestCheckInDate, latestCheckOutTimeNoon));
        return clockInDetail;
    }


    /**
     * 获取离店打卡明细
     *
     * @param orderInfo              订单信息
     * @param now                    现在
     * @param hoOrderClockRecordList 打卡记录信息
     * @return {@link OrderDetailResponseVo.ClockDetail }
     */
    private OrderDetailResponseVo.ClockDetail getClockOutDetail(HoOrderBo orderInfo, Date now, List<HoOrderClockRecord> hoOrderClockRecordList) {
        OrderDetailResponseVo.ClockDetail clockOutDetail = new OrderDetailResponseVo.ClockDetail();
        clockOutDetail.setType(HotelClockTypeEnum.CLOCK_OUT.getCode());

        Date latestCheckOutTime = getLatestCheckOutTime(orderInfo);
        Date latestCheckOutTimeMidNight = DateUtils.getDayEnd(latestCheckOutTime);
        boolean latestCheckOutTimeMidNightNotUp = now.before(latestCheckOutTimeMidNight);

        // 入住打卡
        Optional<HoOrderClockRecord> hoOrderClockInRecordOpt = hoOrderClockRecordList.stream().filter(item -> HotelClockTypeEnum.CLOCK_IN.getCode().equals(item.getType())).findFirst();
        boolean clockedIn = hoOrderClockInRecordOpt.isPresent();
        Optional<HoOrderClockRecord> hoOrderClockOutRecordOpt = hoOrderClockRecordList.stream().filter(item -> HotelClockTypeEnum.CLOCK_OUT.getCode().equals(item.getType())).findFirst();
        boolean clockedOut = hoOrderClockOutRecordOpt.isPresent();


        if (latestCheckOutTimeMidNightNotUp) { //未过实际离店日24点
            if (clockedIn) {
                clockOutDetail.setButtonShow(clockedOut ? Boolean.FALSE : Boolean.TRUE);
                clockOutDetail.setTag(clockedOut ? ClockTipEnum.CLOCKED_TAG.getZh() : null);
                clockOutDetail.setTips(clockedOut ? getClockOutCompletedTips(hoOrderClockOutRecordOpt.get().getClockTime()) : getClockOutTimeTips(latestCheckOutTimeMidNight));
                return clockOutDetail;
            }
            clockOutDetail.setTips(getClockOutBeforeClockInTips(latestCheckOutTimeMidNight));
            clockOutDetail.setButtonShow(Boolean.FALSE);
            return clockOutDetail;
        }

        //已过实际离店日24点
        clockOutDetail.setButtonShow(Boolean.FALSE);

        if (clockedIn) {
            clockOutDetail.setTips(clockedOut ? getClockOutCompletedTips(hoOrderClockOutRecordOpt.get().getClockTime()): getClockOutTimeTips(latestCheckOutTimeMidNight));
            clockOutDetail.setTag(clockedOut ? ClockTipEnum.CLOCKED_TAG.getZh() : ClockTipEnum.EXPIRED_TAG.getZh());
            return clockOutDetail;
        }

        clockOutDetail.setTips(getClockOutBeforeClockInTips(latestCheckOutTimeMidNight));
        clockOutDetail.setTag(ClockTipEnum.EXPIRED_TAG.getZh());
        return clockOutDetail;
    }

    /**
     * 获取最早入住时间
     * 优先获取新字段 若新字段不存在或获取不到相应值则为历史数据
     * 历史数据则取旧字段
     *
     * @param orderInfo 订单信息
     * @return {@link Date }
     */
    private Date getEarliestCheckInDate(HoOrderBo orderInfo) {
        String newestCheckInOutDateStr = orderInfo.getNewestCheckInOutDate();
        boolean historyRecord = StringUtils.isBlank(newestCheckInOutDateStr);
        if (historyRecord) {
            return orderInfo.getActualCheckInTime();
        }

        List<CheckInOutDateInfoBo> checkInOutDateInfoBoList =
                JsonUtils.parseArray(newestCheckInOutDateStr, CheckInOutDateInfoBo.class);
        if (CollectionUtils.isEmpty(checkInOutDateInfoBoList)) {
            return orderInfo.getActualCheckInTime();
        }
        Optional<Date> earliestCheckInDateOpt = checkInOutDateInfoBoList.stream()
                .map(CheckInOutDateInfoBo::getCheckInDate).min(Comparator.comparing(Date::getTime));
        return earliestCheckInDateOpt.orElse(orderInfo.getActualCheckInTime());
    }

    /**
     * 获取最晚退房时间
     * 优先获取新字段 若新字段不存在或获取不到相应值则为历史数据
     * 历史数据则取旧字段
     *
     * @param orderInfo 订单信息
     * @return {@link Date }
     */
    private Date getLatestCheckOutTime(HoOrderBo orderInfo) {
        String newestCheckInOutDateStr = orderInfo.getNewestCheckInOutDate();
        if (StringUtils.isBlank(newestCheckInOutDateStr)) {
            return orderInfo.getActualCheckOutTime();
        }

        List<CheckInOutDateInfoBo> checkInOutDateInfoBoList =
                JsonUtils.parseArray(newestCheckInOutDateStr, CheckInOutDateInfoBo.class);
        if (CollectionUtils.isEmpty(checkInOutDateInfoBoList)) {
            return orderInfo.getActualCheckOutTime();
        }
        Optional<Date> latestCheckOutTimeOpt = checkInOutDateInfoBoList.stream()
                .map(CheckInOutDateInfoBo::getCheckOutDate).max(Comparator.comparing(Date::getTime));
        return latestCheckOutTimeOpt.orElse(orderInfo.getActualCheckOutTime());
    }

    /**
     * 获取最晚退房时间的中午时间
     *
     * @param latestCheckOutTime 最晚退房时间
     * @return {@link Date }
     */
    private Date getLatestCheckOutTimeNoon(Date latestCheckOutTime) {
        Instant instant = latestCheckOutTime.toInstant();
        LocalDateTime latestCheckOutLocalDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        LocalDateTime latestCheckOutLocalDateTimeNoon = latestCheckOutLocalDateTime.with(LocalTime.NOON);
        return Date.from(latestCheckOutLocalDateTimeNoon.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取已完成入住打卡的提示
     *
     * @param clockTime 打卡时间
     * @return {@link String }
     */
    private String getClockInCompletedTips(Date clockTime) {
        return String.format(ClockTipEnum.CLOCK_IN_COMPLETED.getZh(), DateUtil.dateToString(clockTime));

    }

    /**
     * 获取入住打卡的提示
     *
     * @param tipsStartTime 提示开始时间
     * @param tipsEndTime 提示结束时间
     * @return {@link String }
     */
    private String getClockInTimeTips(Date tipsStartTime, Date tipsEndTime) {
        return String.format(ClockTipEnum.CLOCK_IN_TIME.getZh(), DateUtil.dateToYMD(tipsStartTime),
            DateUtil.dateToYMD(tipsEndTime));
    }

    /**
     * 获取打卡下班完成提示
     *
     * @param clockTime 时钟时间
     * @return {@link String }
     */
    private String getClockOutCompletedTips(Date clockTime) {
        return String.format(ClockTipEnum.CLOCK_OUT_COMPLETED.getZh(), DateUtil.dateToString(clockTime));
    }

    /**
     * 获取离店打卡的提示
     *
     * @param tipsTime 提示时间
     * @return {@link String }
     */
    private String getClockOutTimeTips(Date tipsTime) {
        return String.format(ClockTipEnum.CLOCK_OUT_TIME.getZh(), DateUtil.dateToYMD(tipsTime));
    }

    /**
     * 获取先入住打卡再离店打卡提示
     *
     * @param tipsTime 提示时间
     * @return {@link String }
     */
    private String getClockOutBeforeClockInTips(Date tipsTime) {
        return String.format(ClockTipEnum.CLOCK_OUT_BEFORE_CLOCK_IN.getZh(),
            DateUtil.dateToYMD(tipsTime));
    }

    /**
     * 获取协议标签
     */
    public AgreementInfoQueryResp getContent() {
        try {
            JSONResult<AgreementInfoQueryResp> agreementInfo = contentDicitionaryClient.getAgreementInfo();
            if(agreementInfo==null||agreementInfo.getData()==null){
                return null;
            }
            return agreementInfo.getData();
        } catch (Exception exception) {
            log.error("获取协议标签", exception);
            return null;
        }
    }
}
