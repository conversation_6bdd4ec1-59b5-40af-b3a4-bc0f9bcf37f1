package com.corpgovernment.hotel.booking.service;

import com.corpgovernment.api.hotel.booking.orderdetail.request.CancelOrderRequestDto;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderInitVo;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderQueryResponse;
import com.corpgovernment.api.hotel.booking.orderdetail.response.CancelOrderResponseVo;
import com.corpgovernment.api.hotel.booking.orderstatus.response.CancelOrderDetailResponseVo;
import com.corpgovernment.api.hotel.product.model.request.CancelOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.request.CancelOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.response.CancelOrderInitResponse;
import com.corpgovernment.common.enums.FlightCancelTypeEnum;
import com.corpgovernment.hotel.product.external.dto.order.cancel.StandardCancelOrderInquiryResponse;
import com.corpgovernment.hotel.product.service.CancelOrderProductService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CancelOrderService {

	@Autowired
	private CancelOrderProductService cancelOrderProductService;

	public CancelOrderResponseVo cancelOrder(CancelOrderRequestDto cancelOrderRequestDto) {
		CancelOrderRequestBo cancelOrderRequestBo = new CancelOrderRequestBo();
		CancelOrderResponseVo cancelOrderInitVo = new CancelOrderResponseVo();
		cancelOrderRequestBo.setOrderId(cancelOrderRequestDto.getOrderId());
		cancelOrderRequestBo.setApplyCancelFlag(cancelOrderRequestDto.getApplyCancelFlag());
		cancelOrderRequestBo.setCancelReason(cancelOrderRequestDto.getCancelReason());
        cancelOrderRequestBo.setCancelSource(cancelOrderRequestDto.getCancelSource());
		cancelOrderRequestBo.setForceCancel(cancelOrderRequestDto.getForceCancel());
		CancelOrderResponseBo cancelOrder = cancelOrderProductService.cancelOrder(cancelOrderRequestBo);
		if (cancelOrder != null) {
			BeanUtils.copyProperties(cancelOrder, cancelOrderInitVo);
		}
		return cancelOrderInitVo;
	}

	/**
	 * 提前取消订单
	 */
	public CancelOrderResponseVo inAdvanceCancelOrder(CancelOrderRequestDto cancelOrderRequestDto) {
		CancelOrderRequestBo cancelOrderRequestBo = new CancelOrderRequestBo();
		CancelOrderResponseVo cancelOrderInitVo = new CancelOrderResponseVo();
		cancelOrderRequestBo.setOrderId(cancelOrderRequestDto.getOrderId());
        cancelOrderRequestBo.setCancelSource(FlightCancelTypeEnum.A.getCode());
		CancelOrderResponseBo cancelOrder = cancelOrderProductService.cancelOrder(cancelOrderRequestBo);
		if (cancelOrder != null) {
			BeanUtils.copyProperties(cancelOrder, cancelOrderInitVo);
		}
		return cancelOrderInitVo;
	}

    public CancelOrderInitVo cancelOrderInit(CancelOrderRequestDto cancelOrderRequestDto) {
	    CancelOrderRequestBo cancelOrderRequestBo = new CancelOrderRequestBo();
	    CancelOrderInitVo cancelOrderInitVo = new CancelOrderInitVo();
	    cancelOrderRequestBo.setOrderId(cancelOrderRequestDto.getOrderId());
	    CancelOrderInitResponse cancelOrderInit = cancelOrderProductService.cancelOrderInit(cancelOrderRequestBo);
	    if (cancelOrderInit != null) {
		    BeanUtils.copyProperties(cancelOrderInit, cancelOrderInitVo);
	    }
	    return cancelOrderInitVo;
    }

    public CancelOrderQueryResponse cancelOrderQuery(CancelOrderRequestDto cancelOrderRequestDto) {
		CancelOrderRequestBo cancelOrderRequestBo = new CancelOrderRequestBo();
		cancelOrderRequestBo.setOrderId(cancelOrderRequestDto.getOrderId());
        return cancelOrderProductService.cancelOrderQuery(cancelOrderRequestBo);
	}

	public StandardCancelOrderInquiryResponse cancelOrderQueryNew(CancelOrderRequestDto cancelOrderRequestDto) {
		CancelOrderRequestBo cancelOrderRequestBo = new CancelOrderRequestBo();
		cancelOrderRequestBo.setOrderId(cancelOrderRequestDto.getOrderId());
		return cancelOrderProductService.cancelOrderQueryNew(cancelOrderRequestBo);
	}

	public CancelOrderDetailResponseVo cancelOrderDetail(Long orderId) {
		return cancelOrderProductService.cancelOrderDetail(orderId);
	}
}
