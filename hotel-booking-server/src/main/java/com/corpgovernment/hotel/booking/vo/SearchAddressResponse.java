package com.corpgovernment.hotel.booking.vo;

import com.corpgovernment.api.car.vo.subvo.SubAddressVo;
import lombok.Data;

import java.util.List;

@Data
public class SearchAddressResponse {
    private static final long serialVersionUID = 1L;
    private String address;
    private String addressDetail;
    private String latitude;
    private String longitude;
    private String cityName;
    private String cityId;
    private Integer distance;
    private List<SubAddressVo> subAddressList;
    private String mapType;
}
