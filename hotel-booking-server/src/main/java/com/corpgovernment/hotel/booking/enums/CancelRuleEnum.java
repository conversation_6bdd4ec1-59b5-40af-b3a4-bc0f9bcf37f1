package com.corpgovernment.hotel.booking.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 酒店取消规则
 *
 * <AUTHOR>
 */
public enum CancelRuleEnum {
	FREE("FREE", "免费取消", 1),
	TIME_LIMIT("TIME_LIMIT", "限时取消", 2),
	NOT_ALLOWED("NOT_ALLOWED", "不可取消", 8),

	;

	private final String code;
	private final String desc;
	private final Integer type;

	CancelRuleEnum(String code, String desc, Integer type) {
		this.code = code;
		this.desc = desc;
		this.type = type;
	}

	/**
	 * 根据code获取枚举
	 *
	 * @param code 枚举code
	 * @return 取消枚举
	 */
	public static CancelRuleEnum getByCode(String code) {
		if (StringUtils.isBlank(code)) {
			return null;
		}
		return Arrays.stream(CancelRuleEnum.values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
	}

	/**
	 * 根据code获取枚举
	 *
	 * @param type 枚举type
	 * @return 取消枚举
	 */
	public static CancelRuleEnum getByType(Integer type) {
		if (type == null) {
			return null;
		}
		return Arrays.stream(CancelRuleEnum.values()).filter(e -> Objects.equals(e.getType(), type)).findFirst().orElse(null);
	}

	public String getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

	public Integer getType() {
		return type;
	}
}
