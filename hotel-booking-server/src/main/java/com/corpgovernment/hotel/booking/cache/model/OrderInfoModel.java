package com.corpgovernment.hotel.booking.cache.model;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import com.corpgovernment.common.dto.UserCardValidationInfoReqVo;
import com.corpgovernment.common.utils.CardValidateUtil;
import com.corpgovernment.common.utils.Null;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;

import com.corpgovernment.api.approvalsystem.bean.FlowDetail;
import com.corpgovernment.api.approvalsystem.enums.ApprovalFlowFlagEnum;
import com.corpgovernment.api.costcenter.model.CostCenter;
import com.corpgovernment.api.costcenter.model.TempCostCenterVo;
import com.corpgovernment.api.hotel.booking.checkavail.response.CheckAvailResponseVo;
import com.corpgovernment.api.hotel.booking.checkorder.request.BirthVo;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo.ContactsInfo;
import com.corpgovernment.api.hotel.booking.checkorder.request.TelVo;
import com.corpgovernment.api.hotel.booking.hotel.request.BusinessFileRequestVO;
import com.corpgovernment.api.hotel.booking.initpage.response.DeliveryInfoVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo.HotelRoomInfo;
import com.corpgovernment.api.hotel.booking.initpage.response.InvoiceInfoVo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.LadderDeductionDetail;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.LadderDeductionInfo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.Remark;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO.DailyMealInfo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.domain.hoteldetail.model.enums.MapTypeEnum;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.TravelExceedInfoType;
import com.corpgovernment.dto.token.response.HotelOrderTravelStandardGetRespVo;
import com.corpgovernment.dto.travelstandard.response.rule.FloatPriceRuleVO;
import com.corpgovernment.hotel.booking.enums.HotelServiceFeeWayEnum;
import com.corpgovernment.hotel.booking.enums.MixPayTypeEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Slf4j
public class OrderInfoModel {
    /**
     * 订单号
     */
    private Long orderId;
    /**
     * 订单来源
     **/
    private String bookingChannel;
    /**
     * 出行类型，PUB因公、OWN因私
     **/
    private String corpPayType;
    /**
     * 支付方式
     **/
    private String payType;
    /**
     * checkCode
     */
    private String checkCode;
    /**
     * 供应商code
     */
    private String supplierCode;
    /**
     * 供应商corpId
     */
    private String supplierCorpId;
    /**
     * 供应商uid
     */
    private String supplierUid;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商名称
     */
    private String supplierPhone;
    /**
     * 供应商主账户ID
     */
    private String supplierAccountId;
    /**
     * 极其重要，redis中是以wsId做key存储的可订检查上下文信息，创建订单和提交订单时都会使用。
     */
    private String wsId;
    /**
     * 用户id
     */
    private String uid;
    /**
     * 组织id
     */
    private String orgId;
    /**
     * 用户名称
     */
    private String uname;
    /**
     * 部门id
     */
    private String deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 公司Id
     */
    private String corpId;
    /** 预订人公司名称 */
    private String corpName;
    /**
     * 语言类型ZH_CN中文、EN_US英文（默认中文）
     */
    private String language;
    /**
     * 是否需要证件(true:需要; false/null:不需要)
     */
    private Boolean needCertificate;
    /**
     * 支持的证件
     */
    private List<String> supportCertificateTypeList;
    /**
     * 是否需要邮箱(true:需要; false/null:不需要)
     */
    private Boolean needEmail;
    /**
     * 出差申请单号
     */
    private String applyNo;
    /**
     * 行程号
     */
    private Long trafficId;
    /**
     * 配送方式
     */
    private String deliveryType;
    /**
     * 配送费用
     */
    private BigDecimal deliveryPrice;
    /**
     * 预订日期
     */
    private Date orderDate;
    /**
     * 出行人
     */
    private List<PassengerInfo> passengerList;
    /**
     * 房间信息
     */
    private RoomInfo roomInfo;
    /**
     * 联系人信息
     */
    private ContactInfo contactInfo;
    /**
     * 酒店信息
     */
    private HotelInfo hotelInfo;
    /**
     * 每日房价
     */
    private List<RoomDailyInfo> roomDailyInfoList;
    /**
     * 配送信息
     */
    private DeliveryInfo deliveryInfo;
    /**
     * 价格信息
     */
    private PriceInfo priceInfo;
    /**
     * 发票信息
     */
    private InvoiceInfo invoiceInfo;
    /**
     * rc信息
     */
    private RcInfo rcInfo;
    /**
     * 客户备注
     */
    private RemarkInfo remarkInfo;
    /**
     * 选择的key值
     */
    private List<String> remarkKeys;
    /**
     * 取消规则
     */
    private List<OrderCancelRule> orderCancelRule;
    /**
     * 代订人uid
     */
    private String agentUid;
    /**
     * 差旅标准
     */
    private String travelStandard;
    /**
     * 差标金额
     */
    private BigDecimal amountHigh;

    /**
     * 混付情况下个付部分金额
     */
    private BigDecimal pPayAmount;
    /**
     * 混付情况下公司支付部分金额
     */
    private BigDecimal aPayAmount;
    /**
     * 总前收服务费
     */
    private BigDecimal totalServiceCharge;
    /**
     * 服务费策略
     */
    private String serviceChargeStrategy;
    /**
     * 服务费策略值
     */
    private BigDecimal serviceChargeStrategyValue;
    /**
     * 服务费
     */
    // private BigDecimal serviceFee;
    /**
     * 供应商服务费
     */
    private BigDecimal supplierServiceFee;
    /**
     * 公账支付服务费
     */
    private BigDecimal accountPayServiceFee;
    /**
     * 个人支付服务费
     */
    private BigDecimal personalPayServiceFee;
    /**
     * 因公个人支付服务费
     */
    private BigDecimal personalPubPayServiceFee;
    /**
     * 统一支付服务费管控方式
     * 1: 订单
     * 2: 间夜
     */
    private Integer accountPayServiceFeeWay;
    /**
     * 个人支付服务费管控方式
     * 1: 订单
     * 2: 间夜
     */
    private Integer personalPayServiceFeeWay;
    /**
     * 因公个付服务费管控方式
     * 1: 订单
     * 2: 间夜
     */
    private Integer personalPubPayServiceFeeWay;
    /**
     * 服务费获取类型 1从配置读取 2从服务商接口读取
     */
    private Integer serviceFeeOptionType;
    /**
     * 政策执行人id
     */
    private String policyId;
    /**
     * 政策执行人orgId
     */
    private String policyOrgId;

    /**
     * 差标执行人同住政策
     */
    private HotelOrderTravelStandardGetRespVo.CohabitRule cohabitRule;

    /**
     * 执行差标价格上限
     */
    private HotelOrderTravelStandardGetRespVo.AvgPriceRule maxAvgPriceRule;

    /**
     * 执行差标价格下限
     */
    private HotelOrderTravelStandardGetRespVo.AvgPriceRule minAvgPriceRule;

    /**
     * 执行差标星级
     */
    private HotelOrderTravelStandardGetRespVo.StarRule starRule;

    /**
     * 执行差标品牌
     */
    private HotelOrderTravelStandardGetRespVo.BrandRule brandRule;

    /**
     * 差标执行人是否开启浮动差标，命中的浮动差标场景和浮动策略；(浮动差标为空，则说明未命中浮动差标)
     */
    private List<FloatPriceRuleVO> floatPriceRules;

    /**
     * 订单是否超标。 true:超标; false:未超标
     */
    private Boolean overLimit;

    /**
     * 订单超标金额（订单超标+混付）
     * <p>
     * 可用的超标管控方式
     */
    private TravelExceedInfoType travelExceedInfoType;

    /**
     * 是否需要审批
     */
    private Boolean createApproval;

    /**
     * 审批方式
     * @see com.corpgovernment.api.approvalsystem.enums.ApprovalWayEnum
     */
    private String approvalWay;
    /**
     * 审批详情
     */
    private Map<Integer, FlowDetail> flowDetailMap;

    /**
     * 成本中心
     */
    private CostCenter costCenter;

    /**
     * 审批单id
     */
    private String approvalId;

    /**
     * 是否要去除rc，发送变价的情况
     */
    private Boolean rcFlag = false;
    /**
     * 产品id
     */
    private String productId;

    /**
     * 超标管控方式
     */
    private Integer operateType;

    /**
     * 紧急预定支付方式
     */
    private String urgentPayType;

    /**
     * 是否是紧急预订
     */
    private Boolean urgentApply;

    /**
     * 文件列表
     */
    private List<BusinessFileRequestVO> fileList;

    /**
     * 超标个付金额
     */
    private BigDecimal exceedAmount;
    /**
     * 差标Token 这个就是查询房间信息的token
     */
    private String travelStandardToken;
    /**
     * 是否积分
     */
    private Boolean enabledBonusPoint;
    /**
     * 持卡人姓名
     */
    private String memberCardholder;
    /**
     * 会员卡号
     */
    private String memberCardNo;
    /**
     * 是否超标
     */
    private Boolean exceedTravelStandard;
    /**
     * 超标类型
     * standardTravelStandard
     * floatTravelStandard
     */
    private String exceedType;

    /**
     * 同住管理开关是否开启
     * 参考枚举：HotelSwitchEnum.SharedSwitch
     */
    private String sharedManageStatus;
    /**
     * 酒店差标管控规则
     * 参考枚举：HotelManageRulesEnum
     * "sum"："差标相加",
     * "low"："差标就低",
     * "tall"："差标就高"
     */
    private String hotelManageRules;
    /**
     * 合住百分比
     */
    private BigDecimal sharedPercentage;
    /**
     * 酒店差标管控策略
     * 参考枚举：HotelManageStrategyEnum
     * "order"："按订单管控"
     * "room"： "按房间管控"
     */
    private String hotelManageStrategy;
    /**
     * 混付类型
     * @see  com.corpgovernment.hotel.booking.enums.MixPayTypeEnum
     */
    private Integer mixPayType;
    /**
     * 未配置审批流的紧急预订标识
     */
    private Boolean noConfigUrgentApply;

    /**
     * 合住信息
     */
    private ChummageInfo chummageInfo;
    
    /**
     * 房间维度超标信息
     */
    private List<RoomExceedInfo> roomExceedInfoList;
    
    // 海外酒店差标管控是否包含到店另付税费
    private Boolean overseasHotelControlIncludeExtraTax;
    
    // 到店另付税费（间夜）
    private BigDecimal avgExtraTax;
    
    // 酒店价格管控策略
    private PriceControlStrategyEnum priceControlStrategyEnum;
    
    // 到店另付税费（老节点）
    private ExtraTax extraTax;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExtraTax {
        /**
         * 原币种
         */
        private String originCurrency;
        
        /**
         * 原币种到店另付税费
         */
        private BigDecimal originExtraTax;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class RoomExceedInfo {
        
        /**
         * 是否超标
         */
        private Boolean exceed;
        
        /**
         * 超标类型：
         * standardTravelStandard
         * floatTravelStandard
         */
        private String exceedType;
        
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ChummageInfo {

        /**
         * 有合住
         */
        private Boolean haveChummage;

        /**
         * 无合住原因
         */
        private NoChummageReasonCode noChummageReasonCode;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class NoChummageReasonCode {

        private Long id;

        private String code;

        private String name;

        private String remark;

    }

    /**
     * 产线
     */
    private String productType;

    /**
     * 资源模式：supplier（供应商）direct（直连）
     */
    private String resourceMode;

    public static boolean isEmpty(OrderInfoModel orderInfoModel) {
        return orderInfoModel == null || orderInfoModel.equals(new OrderInfoModel());
    }

    public void checkAvail(LocalCheckAvailResponseBo checkAvailResponseBo, HotelInfoModel hotelInfo,
        HotelRoomInfo roomInfo, Map<Integer, FlowDetail> flowDetailMap, CostCenter costCenter,
        InitOrderResponseVo.ServiceFeeInfo serviceFeeInfo) {
        setRoomDailyInfoList(toRoomDailyInfo(checkAvailResponseBo, hotelInfo));
        setCheckCode(checkAvailResponseBo.getCheckCode());
        setRoomInfo(toRoomInfo(hotelInfo, checkAvailResponseBo, roomInfo));
        setHotelInfo(toHotelInfo(hotelInfo, checkAvailResponseBo));
        setPriceInfo(toPriceInfo(checkAvailResponseBo));
        setSupplierCorpId(hotelInfo.getSupplierCorpId());
        setSupplierUid(hotelInfo.getSupplierUid());
        setSupplierCode(hotelInfo.getSupplierCode());
        setSupplierName(hotelInfo.getSupplierName());
        setSupplierPhone(hotelInfo.getSupplierPhone());
        setSupplierAccountId(hotelInfo.getSupplierAccountId());
        setOrderDate(new Date());
        setOrderCancelRule(toOrderCancelRule(checkAvailResponseBo));
        setRemarkInfo(toRemarkInfo(checkAvailResponseBo.getRoomInfo().getRemarkList()));
        setTravelStandard(hotelInfo.getTravelStandard());
        setAmountHigh(hotelInfo.getAmountHigh());
        setPolicyId(hotelInfo.getPolicyId());
        setPolicyOrgId(hotelInfo.getPolicyOrgId());
        setFlowDetailMap(flowDetailMap);
        setCreateApproval(flowDetailMap != null && !flowDetailMap.isEmpty());
        setCostCenter(costCenter);
        setProductId(hotelInfo.getProductId());
        setSupplierServiceFee(roomInfo.getServiceFee());
        if (serviceFeeInfo != null) {
            setServiceFeeOptionType(serviceFeeInfo.getServiceFeeOptionType());
            setAccountPayServiceFee(serviceFeeInfo.getAccountPayServiceFee());
            setPersonalPayServiceFee(serviceFeeInfo.getPersonalPayServiceFee());
            setPersonalPubPayServiceFee(serviceFeeInfo.getPersonalPubPayServiceFee());
            setAccountPayServiceFeeWay(serviceFeeInfo.getAccountPayServiceFeeWay());
            setPersonalPayServiceFeeWay(serviceFeeInfo.getPersonalPayServiceFeeWay());
            setPersonalPubPayServiceFeeWay(serviceFeeInfo.getPersonalPubPayServiceFeeWay());
        }
    }

    public void updateAfterCheckAvail(LocalCheckAvailResponseBo checkAvailResponseBo, HotelInfoModel hotelInfo,
        HotelRoomInfo roomInfo) {
        setRoomDailyInfoList(toRoomDailyInfo(checkAvailResponseBo, hotelInfo));
        setCheckCode(checkAvailResponseBo.getCheckCode());
        setRoomInfo(toRoomInfo(hotelInfo, checkAvailResponseBo, roomInfo));
        setHotelInfo(toHotelInfo(hotelInfo, checkAvailResponseBo));
        setPriceInfo(toPriceInfo(checkAvailResponseBo));
        setOrderCancelRule(toOrderCancelRule(checkAvailResponseBo));
        setRemarkInfo(toRemarkInfo(checkAvailResponseBo.getRoomInfo().getRemarkList()));
    }

    /**
     * 可选备注
     *
     * @param remarkList
     * @return
     */
    private RemarkInfo toRemarkInfo(List<Remark> remarkList) {
        if (CollectionUtils.isEmpty(remarkList)) {
            return null;
        }
        RemarkInfo remarkInfo = new RemarkInfo();
        List<OptionalRemark> optionalRemarkList = remarkList.stream().map(e -> {
            OptionalRemark optionalRemark = new OptionalRemark();
            optionalRemark.setId(e.getId());
            optionalRemark.setKey(e.getKey());
            optionalRemark.setTitle(e.getTitle());
            optionalRemark.setValue(e.getDesc());
            return optionalRemark;
        }).collect(Collectors.toList());
        remarkInfo.setOptionalRemarkList(optionalRemarkList);
        return remarkInfo;
    }

    public void checkAvail(LocalCheckAvailResponseBo checkAvailResponseBo, CheckAvailResponseVo checkAvailResponseVo) {
        setCheckCode(checkAvailResponseBo.getCheckCode());
        setPriceInfo(toPriceInfo(checkAvailResponseBo));
        setOrderCancelRule(toOrderCancelRule(checkAvailResponseBo));
        getRoomInfo().setPolicyDesc(CollectionUtils.isEmpty(checkAvailResponseVo.getCancelDetailList()) ? ""
            : String.join(";", checkAvailResponseVo.getCancelDetailList()));
    }

    private List<OrderCancelRule> toOrderCancelRule(LocalCheckAvailResponseBo checkAvailResponseBo) {
        LocalCheckAvailResponseBo.RoomItem roomInfo = checkAvailResponseBo.getRoomInfo();
        if (roomInfo == null) {
            return new ArrayList<>();
        }
        List<LadderDeductionInfo> ladderDeductionInfoList = roomInfo.getLadderDeductionInfoList();
        if (CollectionUtils.isEmpty(ladderDeductionInfoList)) {
            return new ArrayList<>();
        }
        return ladderDeductionInfoList.stream().map(e -> {
            if (e == null) {
                return null;
            }
            OrderCancelRule orderCancelRule = new OrderCancelRule();
            orderCancelRule.setDeductionType(e.getDeductionType());
            LadderDeductionDetail ladderDeductionInfo = e.getLadderDeductionInfo();
            if (ladderDeductionInfo != null) {
                orderCancelRule.setAmount(ladderDeductionInfo.getAmount());
                orderCancelRule.setDeductionRatio(ladderDeductionInfo.getDeductionRatio());
                orderCancelRule.setEndDeductTime(ladderDeductionInfo.getEndDeductTime());
                orderCancelRule.setStartDeductTime(ladderDeductionInfo.getStartDeductTime());
            }
            return orderCancelRule;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public PriceInfo toPriceInfo(LocalCheckAvailResponseBo checkAvailResponseBo) {
        PriceInfo priceInfo = new PriceInfo();
        LocalCheckAvailResponseBo.RoomItem roomInfo = checkAvailResponseBo.getRoomInfo();
        priceInfo.setAmountCny(roomInfo.getCnyAmount());
        priceInfo.setCustomAmount(roomInfo.getCustomAmount());
        priceInfo.setOriginAmount(roomInfo.getOriginAmount());
        return priceInfo;
    }

    private List<OrderInfoModel.RoomDailyInfo> toRoomDailyInfo(LocalCheckAvailResponseBo checkAvailResponseBo,
        HotelInfoModel hotelInfoModel) {
        List<LocalCheckAvailResponseBo.RoomDailyInfo> roomDailyInfoList = checkAvailResponseBo.getRoomDailyInfoList();
        List<DailyMealInfo> dailyMealInfoList = hotelInfoModel.getDailyMealInfoList();
        int mealSize = dailyMealInfoList.size();
        List<OrderInfoModel.RoomDailyInfo> result = new ArrayList<>();
        for (int i = 0; i < roomDailyInfoList.size(); i++) {
            LocalCheckAvailResponseBo.RoomDailyInfo roomDailyInfo = roomDailyInfoList.get(i);
            OrderInfoModel.RoomDailyInfo target = new OrderInfoModel.RoomDailyInfo();
            if (i < mealSize) {
                DailyMealInfo dailyMealInfo = dailyMealInfoList.get(i);
                target.setBreakfastName(dailyMealInfo.getBreakfastDesc());
                target.setBreakfast(ReUtil.getFirstNumber(dailyMealInfo.getBreakfastDesc()));
            }
            target.setEffectDate(roomDailyInfo.getEffectDate());
            target.setRoomPrice(roomDailyInfo.getSellPrice());
            target.setServiceFeeList(Lists.newArrayList());
            result.add(target);
        }
        return result;
    }

    private OrderInfoModel.HotelInfo toHotelInfo(HotelInfoModel hotelInfoModel,
        LocalCheckAvailResponseBo checkAvailResponseBo) {
        HotelInfo hotelInfo = new HotelInfo();
        hotelInfo.setHotelName(hotelInfoModel.getHotelName());
        hotelInfo.setIsStarLicence(hotelInfoModel.getIsStarLicence());
        hotelInfo.setStar(hotelInfoModel.getStar());
        hotelInfo.setCityId(hotelInfoModel.getCityId());
        hotelInfo.setSupplierCityId(hotelInfoModel.getSupplierCityId());
        hotelInfo.setCityName(hotelInfoModel.getCityName());
        hotelInfo.setLocationId(hotelInfoModel.getLocationId());
        hotelInfo.setLocationName(hotelInfoModel.getLocationName());
        hotelInfo.setZoneId(hotelInfoModel.getZoneId());
        hotelInfo.setZoneName(hotelInfoModel.getZoneName());
        hotelInfo.setLongitude(hotelInfoModel.getLongitude());
        hotelInfo.setLatitude(hotelInfoModel.getLatitude());
        hotelInfo.setAddress(hotelInfoModel.getAddress());
        hotelInfo.setLastArrivalTime(hotelInfoModel.getLastArrivalTime());
        hotelInfo.setEarlyArrivalTime(hotelInfoModel.getEarlyArrivalTime());
        hotelInfo.setLastCancelTime(hotelInfoModel.getLastCancelTime());
        hotelInfo.setHotelId(hotelInfoModel.getHotelId());
        LocalCheckAvailResponseBo.RoomItem roomItem = checkAvailResponseBo.getRoomInfo();
        hotelInfo.setHotelName(hotelInfoModel.getHotelName());
        hotelInfo.setLastCancelTime(checkAvailResponseBo.getRoomInfo().getLastCancelTime());
        hotelInfo.setHotelPhone(hotelInfoModel.getTelephone());
        hotelInfo.setLastArrivalTime(roomItem.getLastArrivalTime());
        hotelInfo.setEarlyArrivalTime(roomItem.getEarlyArrivalTime());
        if (CollectionUtils.isNotEmpty(roomItem.getSpecialTipList())) {
            hotelInfo.setHotelTips(String.join("", roomItem.getSpecialTipList()));
        }
        hotelInfo.setBrandId(hotelInfoModel.getBrandId());
        hotelInfo.setGroupId(hotelInfoModel.getGroupId());
        return hotelInfo;
    }

    private OrderInfoModel.RoomInfo toRoomInfo(HotelInfoModel hotelInfo, LocalCheckAvailResponseBo checkAvailResponseBo,
        InitOrderResponseVo.HotelRoomInfo hotelRoomInfo) {
        RoomInfo roomInfo = new RoomInfo();
        LocalCheckAvailResponseBo.RoomItem roomItem = checkAvailResponseBo.getRoomInfo();
        roomInfo.setBalanceType(hotelInfo.getBalanceType());
        roomInfo.setHotelType(hotelInfo.getHotelType());
        roomInfo.setCheckInDate(hotelInfo.getCheckInDate());
        roomInfo.setCheckOutDate(hotelInfo.getCheckOutDate());
        roomInfo.setRoomId(hotelInfo.getRoomId());
        roomInfo.setBasicRoomId(hotelInfo.getBasicRoomId());
        roomInfo.setRoomName(hotelInfo.getRoomName());
        roomInfo.setQuantity(hotelInfo.getQuantity());
        roomInfo.setPersonCount(roomItem.getGuestPerson());
        roomInfo.setGuestPerson(roomItem.getGuestPerson());
        roomInfo.setBreakfast(hotelInfo.getBreakfast());
        roomInfo.setBreakfastName(hotelInfo.getBreakfastDesc());
        roomInfo.setAmadeus(hotelInfo.getAmadeus());
        roomInfo.setBedType(hotelInfo.getBedType());
        roomInfo.setHotelId(hotelInfo.getHotelId());
        roomInfo.setCityId(hotelInfo.getCityId());
        roomInfo.setSupplierCityId(hotelInfo.getSupplierCityId());
        roomInfo.setLastCancelTime(hotelInfo.getLastCancelTime());
        roomInfo.setEarlyArrivalTime(hotelInfo.getEarlyArrivalTime());
        roomInfo.setLastArrivalTime(hotelInfo.getLastArrivalTime());
        roomInfo.setApplicativeAreaTitle(hotelInfo.getApplicativeAreaTitle());
        roomInfo.setApplicativeAreaDesc(hotelInfo.getApplicativeAreaDesc());
        HotelInfoModel.CancelInfo cancelInfo = hotelInfo.getCancelInfo();
        if (cancelInfo != null) {
            roomInfo.setPolicyType(cancelInfo.getPolicyType());
        }
        roomInfo.setPolicyDesc(String.join(";", hotelRoomInfo.getCancelDetailList()));
        int nextDay = checkAvailResponseBo.getRoomDailyInfoList().size();
        roomInfo.setNextDay(nextDay);
        roomInfo.setRoomName(hotelInfo.getRoomName());
        roomInfo.setBedType(hotelInfo.getBedType());

        roomInfo.setPackageId(hotelInfo.getPackageId());
        roomInfo.setPackageRoom(hotelInfo.getPackageRoom());
        roomInfo.setPrice(hotelInfo.getPrice());
        roomInfo.setPicUrls(hotelInfo.getPicUrls());
        roomInfo.setBasicInfo(hotelInfo.getBasicInfo());
        roomInfo.setName(hotelInfo.getName());
        roomInfo.setBonusPointInfo(convert(roomItem.getBonusPointInfo()));
        roomInfo.setProtocolTag(hotelInfo.getProtocolTag());
        roomInfo.setAdditionalSupplierInfo(roomItem.getAdditionalSupplierInfo());
        roomInfo.setGroupId(hotelInfo.getGroupId());
        HotelInfoModel.ParentRoomInfo parentRoomInfo = hotelInfo.getParentRoomInfo();
        if (parentRoomInfo != null) {
            ParentRoomInfo tmp = new ParentRoomInfo();
            tmp.setNonProtocolMinAvgPrice(parentRoomInfo.getNonProtocolMinAvgPrice());
            tmp.setProtocolMinAvgPrice(parentRoomInfo.getProtocolMinAvgPrice());
            tmp.setNonProtocolMaxAvgPrice(parentRoomInfo.getNonProtocolMaxAvgPrice());
            tmp.setProtocolMinAvgPriceSupplierCode(parentRoomInfo.getProtocolMinAvgPriceSupplierCode());
            roomInfo.setParentRoomInfo(tmp);
        }

        return roomInfo;
    }

    private BonusPointInfo convert(LocalCheckAvailResponseBo.BonusPointInfo bonusPointInfo) {
        if (bonusPointInfo == null) {
            return null;
        }
        BonusPointInfo tmp = new BonusPointInfo();
        tmp.setSupplierCode(bonusPointInfo.getSupplierCode());
        tmp.setGroupId(bonusPointInfo.getGroupId());
        tmp.setGroupName(bonusPointInfo.getGroupName());
        tmp.setBonusPointCode(bonusPointInfo.getBonusPointCode());
        tmp.setBonusPointType(bonusPointInfo.getBonusPointType());
        tmp.setOrderDetailPageRuleDescList(bonusPointInfo.getOrderDetailPageRuleDescList());
        return tmp;
    }

    /**
     * 订单核对时，填充订单数据
     *
     * @param request
     * @param orderInfo
     * @param orderId
     */
    public void checkOrder(CheckOrderRequestVo request, OrderInfoModel orderInfo, Long orderId, Map<String, String> ctripUidMap) {
        setPassengerList(toPassengerList(request.getPassengerInfoList(), request.getPassengerRoomMap(), ctripUidMap));
        checkOrderCommon(request, orderInfo, orderId);
    }

    /**
     * 校验订单信息（扩展）
     *
     * @param request 校验订单请求信息
     * @param orderInfo 订单信息模型
     * @param orderId 订单ID
     * @return 无返回值
     */
    public void checkOrderPlus(CheckOrderRequestVo request, OrderInfoModel orderInfo, Long orderId, Map<String, String> ctripUidMap) {
        log.info("校验订单信息checkOrderPlus,request-before,request:{},orderInfo:{},orderId:{}",
                JsonUtils.toJsonString(request),JsonUtils.toJsonString(orderInfo), orderId);
        // 将请求契约的PassengerInfoList转换为orderInfo中的PassengerInfoList
        List<PassengerInfo>  passengerInfoList = toPassengerListPlus(request.getPassengerInfoList(), request.getPassengerRoomMap(), orderInfo, ctripUidMap);
        log.info("checkOrderPlus.passengerInfoList:{}",JsonUtils.toJsonString(passengerInfoList));
        setPassengerList(passengerInfoList);
        checkOrderCommonPlus(request, orderInfo, orderId);
        log.info("校验订单信息checkOrderPlus,request-after,request:{},orderInfo:{},orderId:{}",
                JsonUtils.toJsonString(request),JsonUtils.toJsonString(orderInfo), orderId);
    }

    /**
     * 检查订单通用信息（扩展）
     *
     * @param request  订单校验请求对象
     * @param orderInfo 订单信息模型
     * @param orderId   订单ID
     * @return 无返回值
     */
    private void checkOrderCommonPlus(CheckOrderRequestVo request, OrderInfoModel orderInfo, Long orderId) {
        log.info("检查订单通用信息checkOrderCommonPlus,request before:{},orderInfo:{},orderId:{}", JsonUtils.toJsonString(request),JsonUtils.toJsonString(orderInfo), orderId);
        setDeliveryInfo(toDeliveryInfo(request, orderInfo));
        setRcInfo(toRcInfo(request.getRcInfo()));
        // 设置是否选择超标原因,前端传送rcInfo的值，就说明选择了rc
        if(ObjectUtil.isNotNull(request.getRcInfo())){
            orderInfo.setRcFlag(true);
        }
        orderInfo.setOrderId(orderId);
        Map<Integer, List<CheckOrderRequestVo.PassengerInfo>> passengerRoomMap = request.getPassengerRoomMap();
        log.info("checkOrderCommonPlus.passengerRoomMap:{}",JsonUtils.toJsonString(passengerRoomMap));
        if(CollectionUtils.isNotEmpty(passengerRoomMap)){
            orderInfo.getRoomInfo().setQuantity(passengerRoomMap.size());// 预订房间数量
        }
        orderInfo.setPPayAmount(request.getPPayAmount());
        orderInfo.setAPayAmount(request.getAPayAmount());
        if(ObjectUtil.isNotNull(request.getPassengerInfoList())){
            orderInfo.getRoomInfo().setPersonCount(request.getPassengerInfoList().size());
        }
        orderInfo.setRemarkKeys(request.getRemarkKeys());
        orderInfo.setFileList(request.getFileList());
        if (null != request.getHotelMemberInfo()) {
            CheckOrderRequestVo.HotelMemberInfo hotelMemberInfo = request.getHotelMemberInfo();
            orderInfo.setMemberCardholder(hotelMemberInfo.getMemberCardholder());
            orderInfo.setMemberCardNo(hotelMemberInfo.getMemberCardNo());
        }
        log.info("检查订单通用信息checkOrderCommonPlus,request after,orderInfo:{}",JsonUtils.toJsonString(orderInfo));
    }

    private void checkOrderCommon(CheckOrderRequestVo request, OrderInfoModel orderInfo, Long orderId) {
        setContactInfo(toContactInfo(request.getContactsInfo()));
        setDeliveryInfo(toDeliveryInfo(request, orderInfo));
        setInvoiceInfo(toInvoiceInfo(request.getInvoiceInfo()));
        String payType = request.getPayInfo().getCode();
        setPayType(payType);
        setRcInfo(toRcInfo(request.getRcInfo()));
        orderInfo.setOrderId(orderId);
        orderInfo.getRoomInfo().setQuantity(request.getRoomQuantity());
        orderInfo.setPPayAmount(request.getPPayAmount());
        orderInfo.setAPayAmount(request.getAPayAmount());
        orderInfo.getRoomInfo().setPersonCount(request.getPassengerInfoList().size());
        RemarkInfo remarkInfo = orderInfo.getRemarkInfo();
        if (remarkInfo != null) {
            remarkInfo.setCustomRemark(request.getTextRemark());
        }
        orderInfo.setRemarkKeys(request.getRemarkKeys());
        orderInfo.getHotelInfo().setLastArrivalTime(request.getLastArrivalTime());
        if (BooleanUtils.isTrue(request.getUrgentApply())) {
            orderInfo.setUrgentApply(request.getUrgentApply());
        }
        orderInfo.setFileList(request.getFileList());
        if (null != request.getHotelMemberInfo()) {
            CheckOrderRequestVo.HotelMemberInfo hotelMemberInfo = request.getHotelMemberInfo();
            orderInfo.setEnabledBonusPoint(BooleanUtils.isTrue(hotelMemberInfo.getEnabledBonusPoint()));
            orderInfo.setMemberCardholder(hotelMemberInfo.getMemberCardholder());
            orderInfo.setMemberCardNo(hotelMemberInfo.getMemberCardNo());
        }
        // 混付标识预处理
        if (StringUtils.equalsIgnoreCase(payType, PayTypeEnum.MIXPAY.getType())) {
            orderInfo.setMixPayType(MixPayTypeEnum.TRAVEL_STANDARD.getCode());
        }
    }

    private RcInfo toRcInfo(CheckOrderRequestVo.RcInfo rcInfo) {
        if (rcInfo == null || this.getRcFlag()) {
            return null;
        }
        if (StringUtils.isEmpty(rcInfo.getReason()) && StringUtils.isEmpty(rcInfo.getReasonNote())) {
            return null;
        }
        RcInfo result = new RcInfo();
        result.setReason(rcInfo.getReason());
        result.setReasonNote(rcInfo.getReasonNote());
        result.setAmountHigh(rcInfo.getAmountHigh());
        return result;
    }


    /**
     * [新页面token流程]将CheckOrderRequestVo.PassengerInfo列表转换为PassengerInfo列表，并添加房间索引信息
     *
     * @param passengerInfoList CheckOrderRequestVo.PassengerInfo列表
     * @param passengerRoomMap  乘客与房间映射关系
     * @return 转换后的PassengerInfo列表
     */
    private List<PassengerInfo> toPassengerListPlus(List<CheckOrderRequestVo.PassengerInfo> passengerInfoList,
                                                Map<Integer, List<CheckOrderRequestVo.PassengerInfo>> passengerRoomMap,
                                                    OrderInfoModel orderInfo,
                                                    Map<String, String> ctripUidMap) {
        log.info("新页面token流程toPassengerListPlus passengerInfoList:{},passengerRoomMap:{},orderInfo:{}",
                JsonUtils.toJsonString(passengerInfoList), JsonUtils.toJsonString(passengerRoomMap), JsonUtils.toJsonString(orderInfo));
        if (CollectionUtils.isEmpty(passengerInfoList)) {
            return new ArrayList<>();
        }

        Map<String, Integer> roomIndexMap = packageRoomIndexMap(passengerRoomMap);
        log.info("checkOrder toPassenger roomIndexMap：{}", JsonUtils.toJsonString(roomIndexMap));

        return passengerInfoList.stream().map(e -> {
            checkSendSms(e);//需发短信的入住人手机号码或者手机号国家码不能为空
            PassengerInfo passengerInfo = new PassengerInfo();
            passengerInfo.setDepartmentName(null);//原逻辑前端传值，现在前端没有传dep；定后下单默认值为空，参考历史代码设置值为null，不会影响现在业务
            passengerInfo.setName(e.getName());
            passengerInfo.setPassport(e.getPassport());
//            passengerInfo.setMobilePhone(e.getMobilePhone());
//            passengerInfo.setCorp(e.getCorp()); 旧的逻辑前端传,新的逻辑根据employeeType进行赋值 0 员工, 1 非员工 2 外部人员
            if (e.getEmployeeType() == 0 || e.getEmployeeType() == 1) {
                passengerInfo.setCorp(true);
            } else if (e.getEmployeeType() == 2) {
                passengerInfo.setCorp(false);
            }
            passengerInfo.setIsSendSms(true);//旧逻辑前端传,新逻辑 前端不传，固定值为true
            passengerInfo.setBirthday(Optional.ofNullable(e.getBirth()).map(BirthVo::getValue).orElse(null));
            passengerInfo.setEmployeeNo(e.getEmployeeNo());
            passengerInfo.setGender(e.getGender());
            passengerInfo.setUid(e.getUid());
            if (CollectionUtils.isNotEmpty(ctripUidMap)) {
                passengerInfo.setCtripUid(ctripUidMap.get(e.getUid()));
            }
            passengerInfo.setNoEmployeeId(e.getNoEmployeeId());
//            passengerInfo.setIsSendSms(e.getIsSendSms());
            passengerInfo.setIsSendSms(true);
            // 增加从新字段取语言
            passengerInfo.setLanguage(Optional.ofNullable(orderInfo).map(OrderInfoModel::getLanguage).orElse("zh"));
            passengerInfo.setOrgId(e.getOrgId());
            passengerInfo.setOrgName(e.getOrgName());
            passengerInfo.setRelationId(e.getRelationId());
            passengerInfo.setRoomNo(1);// 原来就没有赋值，历代代码一个地方写死为1；参考历史代码写死为1
            passengerInfo.setRelationFlag(e.getRelationFlag());
            passengerInfo.setMobilePhone(Optional.ofNullable(e.getTel()).map(TelVo::getValue).orElse(null));
            passengerInfo.setCountryCode(Optional.ofNullable(e.getTel()).map(TelVo::getCountryCode).orElse(null));
            passengerInfo.setCostCenterCode(Optional.ofNullable(e.getCostCenter())
                    .map(CheckOrderRequestVo.CostCenterVo::getCostCenterCode).orElse(null));
            passengerInfo.setCostCenterName(Optional.ofNullable(e.getCostCenter())
                    .map(CheckOrderRequestVo.CostCenterVo::getCostCenterName).orElse(null));
            passengerInfo.setCostCenterId(Optional.ofNullable(e.getCostCenter())
                    .map(CheckOrderRequestVo.CostCenterVo::getCostCenterId).orElse(null));
            passengerInfo.setProjectId(Optional.ofNullable(e.getProjectInfo())
                    .map(CheckOrderRequestVo.ProjectInfo::getProjectId).orElse(null));
            passengerInfo.setProjectCode(Optional.ofNullable(e.getProjectInfo())
                    .map(CheckOrderRequestVo.ProjectInfo::getProjectCode).orElse(null));
            passengerInfo.setProjectName(Optional.ofNullable(e.getProjectInfo())
                    .map(CheckOrderRequestVo.ProjectInfo::getProjectName).orElse(null));
            passengerInfo.setNoSelectProjectDesc(Optional.ofNullable(e.getProjectInfo())
                    .map(CheckOrderRequestVo.ProjectInfo::getNoSelectProjectDesc).orElse(null));
            passengerInfo.setDepartmentName(e.getDep());
            String wbsRemark =
                    Optional.ofNullable(e.getProjectInfo()).map(CheckOrderRequestVo.ProjectInfo::getWbsRemark).orElse(null);
            // 不知道前端到底传到哪个属性里了，所以做了一个判空
            if (StringUtils.isBlank(wbsRemark)) {
                wbsRemark = e.getWbsRemark();
            }
            passengerInfo.setWbsRemark(wbsRemark);
            String costCenterRemark = Optional.ofNullable(e.getCostCenter())
                    .map(CheckOrderRequestVo.CostCenterVo::getCostCenterRemark).orElse(null);
            if (StringUtils.isBlank(costCenterRemark)) {
                costCenterRemark = e.getCostCenterRemark();
            }
            passengerInfo.setCostCenterRemark(costCenterRemark);
            passengerInfo.setEmployeeType(e.getEmployeeType());
            passengerInfo.setSurname(e.getSurname());
            passengerInfo.setGivenname(e.getGivenname());
            passengerInfo.setFullName(e.getFullName());
            passengerInfo.setFullEnName(e.getFullEnName());
            //设置房间索引
            passengerInfo
                    .setRoomIndex(roomIndexMap.get(passengerInfo.getUid() + "_" + passengerInfo.getNoEmployeeId()));
            passengerInfo.setCostCenterVoList(e.getCostCenterVoList());
            passengerInfo.setAccountingUnitCategoryConfigList(buildAccountingUnitList(e.getAccountingUnitCategoryConfigList()));
            // 邮箱
            Boolean needEmail = Optional.ofNullable(orderInfo).map(OrderInfoModel::getNeedEmail).orElse(null);
            if (Boolean.TRUE.equals(needEmail)) {
                passengerInfo.setMail(e.getMail());
                passengerInfo.setEmail(e.getMail());
            }
            // 证件
            Boolean needCertificate = Optional.ofNullable(orderInfo).map(OrderInfoModel::getNeedCertificate).orElse(null);
            log.info("取前端传的第一个证件needCertificate:{}",needCertificate);
            if (Boolean.TRUE.equals(needCertificate) && CollectionUtils.isNotEmpty(e.getCard())) {
                // 取前端传的第一个证件
                e.getCard().stream().filter(item -> item != null && item.getType() != null).findFirst().ifPresent(card -> {
                    passengerInfo.setCardType(String.valueOf(card.getType()));
                    passengerInfo.setCardNo(card.getValue());
                });
            }
            // 校验证件号
            if(StrUtil.isNotBlank(passengerInfo.getCardType()) && StrUtil.isNotBlank(passengerInfo.getCardNo())){
                UserCardValidationInfoReqVo userCardInfoVo = new UserCardValidationInfoReqVo();
                userCardInfoVo.setCardType(Integer.valueOf(passengerInfo.getCardType()));
                userCardInfoVo.setCardNo(passengerInfo.getCardNo());
                log.info("校验证件信息,userCardInfoVo:{},getNationality:{}",JsonUtils.toJsonString(userCardInfoVo),passengerInfo.getNationality());
                CardValidateUtil.validateUserCardNationality(userCardInfoVo,passengerInfo.getNationality());
            }

            return passengerInfo;
        }).collect(Collectors.toList());
    }

    private List<PassengerInfo> toPassengerList(List<CheckOrderRequestVo.PassengerInfo> passengerInfoList,
        Map<Integer, List<CheckOrderRequestVo.PassengerInfo>> passengerRoomMap, Map<String, String> ctripUidMap) {
        if (CollectionUtils.isEmpty(passengerInfoList)) {
            return new ArrayList<>();
        }

        Map<String, Integer> roomIndexMap = packageRoomIndexMap(passengerRoomMap);
        log.info("checkOrder toPassenger roomIndexMap：{}", JsonUtils.toJsonString(roomIndexMap));

        return passengerInfoList.stream().map(e -> {
            checkSendSms(e);//需发短信的入住人手机号码或者手机号国家码不能为空
            PassengerInfo passengerInfo = new PassengerInfo();
            passengerInfo.setName(e.getName());
            passengerInfo.setPassport(e.getPassport());
            passengerInfo.setMobilePhone(e.getMobilePhone());
            passengerInfo.setCorp(e.getCorp());
            passengerInfo.setBirthday(Optional.ofNullable(e.getBirth()).map(BirthVo::getValue).orElse(null));
            passengerInfo.setEmployeeNo(e.getEmployeeNo());
            passengerInfo.setGender(e.getGender());
            passengerInfo.setUid(e.getUid());
            if (ctripUidMap != null) {
                passengerInfo.setCtripUid(ctripUidMap.get(e.getUid()));
            }
            passengerInfo.setNoEmployeeId(e.getNoEmployeeId());
            passengerInfo.setIsSendSms(e.getIsSendSms());
            // 增加从新字段取语言
            passengerInfo.setLanguage(StringUtils.isBlank(e.getLanguage()) ? e.getLang() : e.getLanguage());
            passengerInfo.setOrgId(e.getOrgId());
            passengerInfo.setOrgName(e.getOrgName());
            passengerInfo.setRelationId(e.getRelationId());
            passengerInfo.setRelationFlag(e.getRelationFlag());
            passengerInfo.setMobilePhone(Optional.ofNullable(e.getTel()).map(TelVo::getValue).orElse(null));
            passengerInfo.setCountryCode(Optional.ofNullable(e.getTel()).map(TelVo::getCountryCode).orElse(null));
            passengerInfo.setCostCenterCode(Optional.ofNullable(e.getCostCenter())
                .map(CheckOrderRequestVo.CostCenterVo::getCostCenterCode).orElse(null));
            passengerInfo.setCostCenterName(Optional.ofNullable(e.getCostCenter())
                .map(CheckOrderRequestVo.CostCenterVo::getCostCenterName).orElse(null));
            passengerInfo.setCostCenterId(Optional.ofNullable(e.getCostCenter())
                .map(CheckOrderRequestVo.CostCenterVo::getCostCenterId).orElse(null));
            passengerInfo.setProjectId(Optional.ofNullable(e.getProjectInfo())
                .map(CheckOrderRequestVo.ProjectInfo::getProjectId).orElse(null));
            passengerInfo.setProjectCode(Optional.ofNullable(e.getProjectInfo())
                .map(CheckOrderRequestVo.ProjectInfo::getProjectCode).orElse(null));
            passengerInfo.setProjectName(Optional.ofNullable(e.getProjectInfo())
                .map(CheckOrderRequestVo.ProjectInfo::getProjectName).orElse(null));
            passengerInfo.setNoSelectProjectDesc(Optional.ofNullable(e.getProjectInfo())
                .map(CheckOrderRequestVo.ProjectInfo::getNoSelectProjectDesc).orElse(null));
            passengerInfo.setDepartmentName(e.getDep());
            String wbsRemark =
                Optional.ofNullable(e.getProjectInfo()).map(CheckOrderRequestVo.ProjectInfo::getWbsRemark).orElse(null);
            // 不知道前端到底传到哪个属性里了，所以做了一个判空
            if (StringUtils.isBlank(wbsRemark)) {
                wbsRemark = e.getWbsRemark();
            }
            passengerInfo.setWbsRemark(wbsRemark);
            String costCenterRemark = Optional.ofNullable(e.getCostCenter())
                .map(CheckOrderRequestVo.CostCenterVo::getCostCenterRemark).orElse(null);
            if (StringUtils.isBlank(costCenterRemark)) {
                costCenterRemark = e.getCostCenterRemark();
            }
            passengerInfo.setCostCenterRemark(costCenterRemark);
            passengerInfo.setEmployeeType(e.getEmployeeType());
            passengerInfo.setSurname(e.getSurname());
            passengerInfo.setGivenname(e.getGivenname());
            passengerInfo.setFullName(e.getFullName());
            passengerInfo.setFullEnName(e.getFullEnName());
            passengerInfo
                .setRoomIndex(roomIndexMap.get(passengerInfo.getUid() + "_" + passengerInfo.getNoEmployeeId()));
            passengerInfo.setCostCenterVoList(e.getCostCenterVoList());
            passengerInfo.setAccountingUnitCategoryConfigList(buildAccountingUnitList(e.getAccountingUnitCategoryConfigList()));
            return passengerInfo;
        }).collect(Collectors.toList());
    }

    private List<AccountingUnit> buildAccountingUnitList(List<CheckOrderRequestVo.AccountingUnitVo> accountingUnitVoList) {
        return Optional.ofNullable(accountingUnitVoList)
                .orElse(Collections.emptyList())
                .stream()
                .map(unitVO -> {
                    AccountingUnit unit = new AccountingUnit();
                    BeanUtils.copyProperties(unitVO, unit);
                    return unit;
                })
                .collect(Collectors.toList());
    }

    private Map<String, Integer> packageRoomIndexMap(Map<Integer, List<CheckOrderRequestVo.PassengerInfo>> passengerRoomMap){
        Map<String, Integer> roomIndexMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(passengerRoomMap)) {
            passengerRoomMap.forEach((roomIndex, passengerList) -> {
                for (CheckOrderRequestVo.PassengerInfo passengerInfo : passengerList) {
                    String key = passengerInfo.getUid() + "_" + passengerInfo.getNoEmployeeId();
                    roomIndexMap.put(key, roomIndex);
                }
            });
        }
        return roomIndexMap;
    }

    private void checkSendSms(CheckOrderRequestVo.PassengerInfo passengerInfo) {
        if (passengerInfo.getIsSendSms() != null && passengerInfo.getIsSendSms()) {
            if (StringUtils.isBlank(Optional.ofNullable(passengerInfo.getTel()).map(TelVo::getValue).orElse(null)) ||
                StringUtils
                    .isBlank(Optional.ofNullable(passengerInfo.getTel()).map(TelVo::getCountryCode).orElse(null))) {
                throw new CorpBusinessException(
                    HotelResponseCodeEnum.THE_MOBILE_PHONE_NUMBER_OR_COUNTRY_CODE_OF_THE_GUEST_IS_EMPTY);
            }
        }
    }

    private ContactInfo toContactInfo(ContactsInfo source) {
        if (source == null) {
            return null;
        }
        ContactInfo result = new ContactInfo();
        result.setName(source.getName());
        result.setEmail(source.getEmail());
        result.setMobilePhone(source.getPhone());
        result.setMobilePhoneCountryCode(source.getCountryCode());
        return result;
    }

    private DeliveryInfo toDeliveryInfo(CheckOrderRequestVo requestVo, OrderInfoModel orderInfo) {
        String deliveryType = requestVo.getDeliveryType();
        setDeliveryType(deliveryType);
        setDeliveryPrice(requestVo.getDeliveryPrice());
        // 选择了纸质发票的情况
        if (requestVo.getInvoiceInfo() != null && requestVo.getInvoiceInfo().getInvoiceType() != null
            && requestVo.getInvoiceInfo().getInvoiceType() != 2) {
            DeliveryInfoVo vo = requestVo.getInvoiceInfo().getDeliveryInfo();
            if (vo != null) {
                DeliveryInfo result = new DeliveryInfo();
                result.setKey(vo.getId());
                result.setName(vo.getRecipientName());
                result.setPhone(vo.getRecipientMobile());
                result.setProvinceName(vo.getProvinceName());
                result.setProvinceId(vo.getProvinceCode());
                result.setCityName(vo.getCityName());
                result.setCityId(vo.getCityCode());
                result.setSupplierCityId(orderInfo.getHotelInfo().getSupplierCityId());
                result.setDistrictId(vo.getDistrictCode());
                result.setDistrictName(vo.getDistrictName());
                result.setDeliveryType(vo.getDeliveryType());
                result.setAddress(vo.getAddress());
                return result;
            }
        }
        return null;
    }

    private InvoiceInfo toInvoiceInfo(InvoiceInfoVo source) {
        if (source == null) {
            return null;
        }
        InvoiceInfo invoiceInfo = new InvoiceInfo();
        invoiceInfo.setId(source.getId());
        invoiceInfo.setAccountBank(source.getAccountBank());
        invoiceInfo.setAccountCardNo(source.getAccountCardNo());
        invoiceInfo.setCorporationAddress(source.getCorporationAddress());
        invoiceInfo.setCorporationTel(source.getCorporationTel());
        invoiceInfo.setInvoiceTitle(source.getInvoiceTitle());
        invoiceInfo.setInvoiceTitleType(source.getInvoiceTitleType());
        invoiceInfo.setInvoiceType(source.getInvoiceType());
        invoiceInfo.setInvoiceContent(source.getInvoiceContent());
        invoiceInfo.setTaxpayerNumber(source.getTaxpayerNumber());
        invoiceInfo.setEmail(source.getEmail());
        return invoiceInfo;
    }

    @Data
    public static class RemarkInfo {
        private String customRemark;
        private List<OptionalRemark> optionalRemarkList;
    }

    @Data
    public static class OrderCancelRule {
        private Long orderId;
        private String deductionType;
        private Date startDeductTime;
        private Date endDeductTime;
        private BigDecimal amount;
        private BigDecimal deductionRatio;
    }

    @Data
    public static class OptionalRemark {
        private String id;
        private String key;
        private String title;
        private String value;
    }

    @Data
    public class RcInfo {
        private BigDecimal amountHigh;
        private String reason;
        private String reasonNote;
    }

    @Data
    public static class InvoiceInfo {
        private Long id;
        private String orderId;
        private String accountBank;
        private String accountCardNo;
        private String corporationAddress;
        private String corporationTel;
        private String invoiceTitle;
        private Integer invoiceTitleType;
        private Integer invoiceType;
        private String invoiceContent;
        private String taxpayerNumber;
        private String email;
    }

    @Data
    public static class RoomDailyInfo {
        private String effectDate;
        private BigDecimal roomPrice;
        private Integer breakfast;
        private String breakfastName;
        private List<ServiceFeeInfo> serviceFeeList;

        /**
         * 每日餐食数量
         */
        private Integer meals;
    }

    @Data
    public static class ContactInfo {
        private String confirmType;
        private String name;
        private String email;
        private String mobilePhone;
        private String mobilePhoneCountryCode;
    }

    @Data
    public static class PriceInfo {
        // 总房价
        private BigDecimal amountCny;
        private String originCurrency;
        private BigDecimal originExchange;
        private BigDecimal originAmount;
        private String customCurrency;
        private BigDecimal customExchange;
        private BigDecimal customAmount;
    }

    @Data
    public static class HotelInfo {

        private String hotelName;
        private Boolean isStarLicence;
        private Integer star;
        private String cityId;
        /**
         * 供应商入住城市ID
         */
        private String supplierCityId;
        private String cityName;
        private String countryId;
        private String provinceId;
        private String locationId;
        private String locationName;
        private String zoneId;
        private String zoneName;
        private String longitude;
        private String latitude;
        private String remarks;
        private String hotelTips;
        private String address;
        private String lastArrivalTime;
        private String earlyArrivalTime;
        private String lastCancelTime;
        private String brandName;
        private Boolean hongKongMacaoTaiwan;
        private String hotelPhone;
        private String hotelId;
        /**
         * 品牌Id
         */
        private String brandId;

        /**
         * 集团Id
         */
        private String groupId;
        
        // T增专票 F增普票
        private String vatFlag;

        /**
         * 地图类型  BAI_DU（百度）； GAO_DE（高德）；GOOGLE（谷歌）
         * 高德、谷歌、百度
         * {@link MapTypeEnum}
         */
        private String mapType;

        /**
         * 酒店logo图片
         */
        private String logoPicUrl;
    }

    @Data
    public static class RoomInfo {
        /**
         * 房型的结算类型(FG:现付, PP:预付到携程, PH:预付到酒店，UseFG:现转预房型-预付)
         */
        private String balanceType;
        /**
         * 酒店类型（M：会员酒店，C：协议酒店）
         */
        private String hotelType;
        /**
         * 房型协议标签
         */
        private String protocolTag;
        /**
         * 房型协议标签类型
         */
        private Integer protocolType;
        /**
         * 入住时间
         */
        private String checkInDate;
        /**
         * 离店时间
         */
        private String checkOutDate;
        /**
         * 天数
         */
        private Integer nextDay;
        /**
         * 房型ID
         */
        private String roomId;
        /**
         * 房间名称
         */
        private String roomName;
        /**
         * 预订房间数量
         */
        private Integer quantity;
        /**
         * 入住人数
         */
        private Integer personCount;

        /**
         * 房间可入住人数
         */
        private Integer guestPerson;
        /**
         * 早餐数
         */
        private Integer breakfast;
        /**
         * 早餐描述
         */
        private String breakfastName;
        /**
         * 是否Amadeus酒店
         */
        private Boolean amadeus;
        /**
         * 房型
         */
        private String bedType;
        /**
         * 取消策略
         */
        private Integer policyType;
        /**
         * 取消策略
         */
        private String policyDesc;
        /**
         * 价格描述
         */
        private String applicativeAreaDesc;
        /**
         * 价格标题
         */
        private String applicativeAreaTitle;
        /**
         * 取消修改说明
         */
        private String cancelModifyNote;
        private String hotelId;
        private String cityId;
        /**
         * 供应商入住城市ID
         */
        private String supplierCityId;
        /**
         * 房型最晚取消修改时间
         */
        private String lastCancelTime;
        /**
         * 接口酒店最早到店时间
         */
        private String earlyArrivalTime;
        /**
         * 接口酒店最晚到店时间
         */
        private String lastArrivalTime;

        /**
         * true则视为对应产品包含酒店套餐
         */
        private Boolean packageRoom;

        /**
         * 打包售卖房型打包Id
         */
        private Integer packageId;
        /**
         * 价格
         */
        private BigDecimal price;
        /**
         * 房间图片
         */
        private List<String> picUrls;
        /**
         * 房间基础信息
         */
        private List<String> basicInfo;
        /**
         * 酒店明细名称
         */
        private String name;
        /**
         * 积分信息
         */
        private BonusPointInfo bonusPointInfo;
        /**
         * 供应商下单透传额外信息
         */
        public List<String> additionalSupplierInfo;
        /**
         * 集团id
         */
        private String groupId;
        /**
         * 母房型信息
         */
        private ParentRoomInfo parentRoomInfo;

        /**
         * 母房型id
         */
        private String basicRoomId;

        /**
         * 餐食类型
         */
        private Integer mealType;

        /**
         * 最少入住天数
         */
        private Integer minConsecutiveDays;

        /**
         * 钟点房
         */
        private Boolean hourlyRoom;

        /**
         * 钟点房明细
         */
        private HourRoomDetail hourRoomDetail;

    }

    /**
     * <p>
     * 描述: 钟点房明细
     * </p>
     */
    @Data
    public static class HourRoomDetail {

        /**
         * 用户选择的入住时间 yyyy-MM-dd HH:mm:ss
         */
        private String checkInTime;

        /**
         * 用户选择的离店时间 yyyy-MM-dd HH:mm:ss
         */
        private String checkOutTime;

        /**
         * 最早到店时间，分钟数。如 10:00，该值为 600
         */
        private Integer earliestArriveTime;

        /**
         * 最晚离店时间，分钟数。如 18:00，该值为 1080
         */
        private Integer latestLeaveTime;

        /**
         * 连住时间。如 3小时，该值为3
         */
        private Integer duration;

    }

    /**
     * 母房型信息
     */
    @Data
    public static class ParentRoomInfo {
        /**
         * 所属母房型下最低价非协议房型的房费均价
         */
        private BigDecimal nonProtocolMinAvgPrice;

        /**
         * 所属母房型下最高价非协议房型的房费均价
         */
        private BigDecimal nonProtocolMaxAvgPrice;

        /**
         * 所属母房型下最低价协议房型的房费均价
         */
        private BigDecimal protocolMinAvgPrice;

        /**
         * 所属母房型下最低价协议房型的所属供应商
         */
        private String protocolMinAvgPriceSupplierCode;
    }

    @Data
    public static class BonusPointInfo {
        private String supplierCode;
        private String groupId;
        private String groupName;
        private String bonusPointCode;
        private String bonusPointType;
        private List<String> orderDetailPageRuleDescList;
    }

    @Data
    public static class DeliveryInfo {
        private Long key;
        private String name;
        private String countryCode;
        private String phone;
        private String provinceName;
        private String cityName;
        private String districtName;
        private String address;
        private String postalCode;
        private String provinceId;
        private String cityId;
        /**
         * 供应商入住城市ID
         */
        private String supplierCityId;
        private String districtId;
        private String deliveryType;
    }

    @Data
    public static class ServiceFeeInfo {
        private String effectDate;
        private BigDecimal price;
        private String type;
    }

    @Data
    public static class PassengerInfo {

        private String name;
        private String passport;
        private String mobilePhone;
        private Boolean corp;
        private String birthday;
        private String employeeNo;
        private String nationality;
        private String countryCode;
        private String gender;
        private String uid;
        private String ctripUid;
        private String noEmployeeId;
        private Boolean isSendSms = false;
        private String language;
        private String cardType;
        private String cardNo;
        private String mail;
        private String orgId;
        private String orgName;
        private Date validity;
        private Long relationId;
        private Boolean relationFlag;
        /**
         * 房间号
         **/
        private Integer roomNo;
        /**
         * 成本中心编码code
         **/
        private String costCenterCode;
        /**
         * 成本中心名称
         **/
        private String costCenterName;

        /**
         * 成本中心关联法人机构Code
         */
        private String costCenterLegalEntityCode;

        /**
         * 成本中心关联法人机构名字
         */
        private String costCenterLegalEntityName;

        /**
         * 项目关联法人机构Code
         */
        private String projectLegalEntityCode;

        /**
         * 项目关联法人机构名字
         */
        private String projectLegalEntityName;

        /**
         * 出差申请单号
         **/
        private String tripApplyNo;
        /**
         * 出行计划id
         */
        private Long travelPlanId;
        /**
         * 部门名称
         */
        private String departmentName;

        /**
         * wbs--编号备注
         */
        private String wbsRemark;
        private String costCenterRemark;
        private String projectId;
        private String projectCode;
        private String projectName;
        private String costCenterId;
        private String noSelectProjectDesc;
        /**
         * 成本中心关联法人机构Code
         */
        private String costCenterCorporationCode;

        /**
         * 成本中心关联法人机构名字
         */
        private String costCenterCorporationName;
        private String supplierAccountId;
        /**
         * 项目关联法人机构Code
         */
        private String projectCorporationCode;

        /**
         * 项目关联法人机构名字
         */
        private String projectCorporationName;
        /**
         * 核算单元json,核算单元相关的数据都会拼接到这个json中
         */
        private String accountingUnitJson;
        /**
         * 员工类型
         */
        private Integer employeeType;
        /**
         * 姓
         */
        private String surname;
        /**
         * 名字
         */
        private String givenname;
        /**
         * 完整英文名字
         */
        private String fullEnName;
        /**
         * 完整中文名
         */
        private String fullName;
        /**
         * 入住人名称（新）
         */
        private String travelerName;
        /**
         * 房间索引
         */
        private Integer roomIndex;

        /**
         * 自定义模板多成本中心显示数据
         */
        private List<TempCostCenterVo> costCenterVoList;

        /**
         * 入住人邮箱
         */
        private String email;

        /**
         * 当前选择的核算单元列表
         * @return
         */
        private List<AccountingUnit> accountingUnitCategoryConfigList;

        private Map<String, Object> fieldMap;

        public String getName() {
            if ("en".equals(language) && StringUtils.isNotBlank(passport)) {
                return this.passport;
            }
            if (StringUtils.isBlank(name) && StringUtils.isNotBlank(passport)) {
                return this.passport;
            }
            return this.name;
        }
    }

    @Data
    public static class AccountingUnit {
        /**
         * 核算单元分类编码
         */
        private String categoryCode;
        /**
         * 核算单元分类名称
         */
        private String categoryName;
        /**
         * 必填
         */
        private Boolean required;
        /**
         * 备注
         */
        private String remark;
        /**
         * 核算单元编码
         */
        private String code;
        /**
         * 核算单元名称
         */
        private String name;
        /**
         * 法人机构编码（业务单元）
         */
        private String businessUnitCode;
        /**
         * 法人机构名称（业务单元）
         */
        private String businessUnitName;
    }


    /**
     * 订单总金额
     *
     * @return
     */
    public BigDecimal getTotalAmount() {
        return this.getTotalRoomPrice()
                .add(Null.or(this.getTotalServiceCharge(), BigDecimal.ZERO))
                .add(Optional.ofNullable(this.getDeliveryPrice()).orElse(BigDecimal.ZERO));
    }
    
    /**
     * 订单总金额不包含前收服务费
     *
     * @return
     */
    public BigDecimal getTotalAmountWithoutServiceCharge() {
        return this.getTotalRoomPrice()
                .add(Optional.ofNullable(this.getDeliveryPrice()).orElse(BigDecimal.ZERO));
    }

    /**
     * 总房价
     *
     * @return
     */
    public BigDecimal getTotalRoomPrice() {
        return Optional.ofNullable(this.getPriceInfo()).map(PriceInfo::getAmountCny).orElse(BigDecimal.ZERO);
    }

    /**
     * 混付情况下公司支付金额
     *
     * @return
     */
    public BigDecimal getAPayAmount() {
        if (!PayTypeEnum.MIXPAY.getType().equalsIgnoreCase(this.getPayType())) {
            return null;
        }

        if (StringUtils.isNotBlank(this.getTravelStandardToken()) || Objects.nonNull(this.getExceedAmount())) {
            // 订单总金额 - 个人支付金额
            return this.getTotalAmount().subtract(Optional.ofNullable(this.getExceedAmount()).orElse(BigDecimal.ZERO));
        } else {
            BigDecimal amountHigh = this.getAmountHigh();
            if (null != amountHigh) {
                BigDecimal nextDay = Optional.ofNullable(this.getRoomInfo()).map(RoomInfo::getNextDay)
                    .map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
                BigDecimal roomQuantity = Optional.ofNullable(this.getRoomInfo()).map(RoomInfo::getQuantity)
                    .map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
                // 公付金额 = (差旅标准金额 * 天数 * 房间数) + 服务费
                return amountHigh.multiply(nextDay).multiply(roomQuantity);
            }
        }
        return null;
    }

    /**
     * 混付情况下个人支付金额
     *
     * @return
     */
    public BigDecimal getPPayAmount() {
        if (!PayTypeEnum.MIXPAY.getType().equalsIgnoreCase(this.getPayType())) {
            return null;
        }

        if (StringUtils.isNotBlank(this.getTravelStandardToken()) || Objects.nonNull(this.getExceedAmount())) {
            return Optional.ofNullable(this.getExceedAmount()).orElse(BigDecimal.ZERO);
        } else {
            BigDecimal companyPayAmount = this.getAPayAmount();
            if (null != companyPayAmount) {
                // 个付金额 = 订单总金额 - 公司支付金额
                return this.getTotalAmount().subtract(companyPayAmount);
            }
        }
        return null;
    }

    /**
     * 获取间夜均价
     *
     * @return
     */
    public BigDecimal getRoomNightAvgPrice() {
        // 总价
        BigDecimal roomPrice = this.getTotalRoomPrice();
        if (null == roomPrice || BigDecimal.ZERO.compareTo(roomPrice) == 0) {
            return null;
        }
        // 天数
        BigDecimal nextDay =
            Optional.ofNullable(this.getRoomInfo()).map(RoomInfo::getNextDay).map(BigDecimal::valueOf)
                .orElse(BigDecimal.ZERO);
        // 房间数
        BigDecimal roomQuantity =
            Optional.ofNullable(this.getRoomInfo()).map(RoomInfo::getQuantity).map(BigDecimal::valueOf)
                .orElse(BigDecimal.ZERO);
        if (nextDay.compareTo(BigDecimal.ZERO) <= 0 || roomQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        // 均价
        return roomPrice.divide(nextDay.multiply(roomQuantity), 2, RoundingMode.HALF_UP);
    }

    /**
     * 获取服务费
     *
     * @return
     */
    @Deprecated
    public BigDecimal getTotalServiceFee() {
        Integer serviceFeeOptionType = this.getServiceFeeOptionType();
        if (Objects.isNull(serviceFeeOptionType)) {
            return BigDecimal.ZERO;
        }
        // 1从配置读取
        if (1 == serviceFeeOptionType) {
            OrderInfoModel.RoomInfo roomInfo = this.getRoomInfo();
            // 天数
            BigDecimal nextDay =
                Optional.ofNullable(roomInfo.getNextDay()).map(BigDecimal::valueOf).orElse(BigDecimal.ONE);
            // 房间数
            BigDecimal roomQuantity =
                Optional.ofNullable(roomInfo.getQuantity()).map(BigDecimal::valueOf).orElse(BigDecimal.ONE);
            // 因公
            if (CorpPayTypeEnum.PUB.getType().equals(this.getCorpPayType())) {
                return this.getPubServiceFee(nextDay, roomQuantity);
            } else {
                return this.getOwnServiceFee(nextDay, roomQuantity);
            }
        } else {
            return Optional.ofNullable(this.getSupplierServiceFee()).orElse(BigDecimal.ZERO);
        }
    }

    /**
     * 因公服务费
     *
     * @param nextDay 预定天数
     * @param roomQuantity 预定房间数量
     * @return
     */
    @Deprecated
    private BigDecimal getPubServiceFee(BigDecimal nextDay, BigDecimal roomQuantity) {
        // 统一支付,混付 走因公 服务费
        if (Lists.newArrayList(PayTypeEnum.ACCNT.getType(), PayTypeEnum.MIXPAY.getType())
            .contains(this.getPayType())
            && Objects.nonNull(this.getAccountPayServiceFeeWay()) && this.getAccountPayServiceFeeWay() > 0) {
            if (HotelServiceFeeWayEnum.ORDER.getType().equals(this.getAccountPayServiceFeeWay())) {
                return Optional.ofNullable(this.getAccountPayServiceFee()).orElse(BigDecimal.ZERO);
            } else {
                // 服务费 = 服务费 * 间 * 夜
                BigDecimal accountPayServiceFee =
                    Optional.ofNullable(this.getAccountPayServiceFee()).orElse(BigDecimal.ZERO);
                return accountPayServiceFee.multiply(nextDay).multiply(roomQuantity);
            }
        }
        // 因公个付
        if (PayTypeEnum.PPAY.getType().equals(this.getPayType())
            && Objects.nonNull(this.getPersonalPubPayServiceFeeWay())
            && this.getPersonalPubPayServiceFeeWay() > 0) {
            if (HotelServiceFeeWayEnum.ORDER.getType().equals(this.getPersonalPubPayServiceFeeWay())) {
                return Optional.ofNullable(this.getPersonalPubPayServiceFee()).orElse(BigDecimal.ZERO);
            } else {
                // 服务费 = 服务费 * 间 * 夜
                BigDecimal personalPubPayServiceFee =
                    Optional.ofNullable(this.getPersonalPubPayServiceFee()).orElse(BigDecimal.ZERO);
                return personalPubPayServiceFee.multiply(nextDay).multiply(roomQuantity);
            }
        }
        // 前台现付
        return BigDecimal.ZERO;
    }

    /**
     * 因私服务费
     *
     * @param nextDay 预定天数
     * @param roomQuantity 预定房间数量
     * @return
     */
    @Deprecated
    private BigDecimal getOwnServiceFee(BigDecimal nextDay, BigDecimal roomQuantity) {
        // 因私个付
        if (PayTypeEnum.PPAY.getType().equals(this.getPayType())
            && Objects.nonNull(this.getPersonalPayServiceFeeWay())
            && this.getPersonalPayServiceFeeWay() > 0) {
            if (HotelServiceFeeWayEnum.ORDER.getType().equals(this.getPersonalPayServiceFeeWay())) {
                return Optional.ofNullable(this.getPersonalPayServiceFee()).orElse(BigDecimal.ZERO);
            } else {
                // 服务费 = 服务费 * 间 * 夜
                BigDecimal personalPayServiceFee =
                    Optional.ofNullable(this.getPersonalPayServiceFee()).orElse(BigDecimal.ZERO);
                return personalPayServiceFee.multiply(nextDay).multiply(roomQuantity);
            }
        }
        // 前台现付
        return BigDecimal.ZERO;
    }

    public FlowDetail getFlowDetail() {
        Map<Integer, FlowDetail> flowDetailMap = this.getFlowDetailMap();
        if (flowDetailMap == null) {
            return null;
        }
        if (BooleanUtils.isTrue(this.getUrgentApply())) {
            return flowDetailMap.get(ApprovalFlowFlagEnum.NORMAL.getCode());
        }
        if (this.getOperateType() == null
                || PayTypeEnum.MIXPAY.getType().equalsIgnoreCase(this.getPayType())) {
            return flowDetailMap.get(ApprovalFlowFlagEnum.NORMAL.getCode());
        }
        return flowDetailMap.get(ApprovalFlowFlagEnum.EXCEED.getCode());
    }

}
