package com.corpgovernment.hotel.booking.service.search;

import com.corpgovernment.api.hotel.booking.hotel.request.SearchHotelListRequestVO;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelListRequestBo;
import com.corpgovernment.api.hotel.product.model.request.Tree;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo.HotelListBean;
import com.corpgovernment.api.hotel.product.model.response.SearchHotelListResponseVO;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseRequestVO;
import com.corpgovernment.common.utils.BaseUtils;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.domain.common.gateway.ICommonGateway;
import com.corpgovernment.hotel.booking.bo.SearchHotelListBo;
import com.corpgovernment.hotel.booking.cache.HotelListPageCacheInfo;
import com.corpgovernment.hotel.booking.cache.SupplierHotelModel;
import com.corpgovernment.hotel.booking.enums.SearchHotelSortEnum;
import com.corpgovernment.hotel.product.cache.HotelListCacheManager;
import com.corpgovernment.hotel.product.dataloader.soa.CommonSupplierLoader;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.corpgovernment.mapping.bo.HmHotelMappingResult;
import com.corpgovernment.mapping.mapper.HmHotelMappingResultMapper;
import com.ctrip.corp.obt.generic.constants.GenericConstants;
import com.ctrip.corp.obt.generic.core.context.RequestContext;
import com.ctrip.corp.obt.generic.utils.Conditional;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NewBasicSearchHotelService extends NewSearchHotelProcessAbstractService {
    Id noProductId = Metrics.REGISTRY.createId("hotel.booking.supplier.noproduct");
    @Autowired
    private HotelListCacheManager hotelListCacheManager;
    @Autowired
    private CommonService commonService;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private CommonSupplierLoader commonSupplierLoader;
    @Autowired
    private HmHotelMappingResultMapper hmHotelMappingResultMapper;
    @Autowired
    @Qualifier(value = "queryThreadPoolExecutor")
    private ThreadPoolExecutor queryThreadPoolExecutor;
    @Autowired
    private HotelMappingService hotelMappingService;
    @Autowired
    private HotelSortService hotelSortService;
    @Resource
    private ICommonGateway commonGateway;
    @Autowired
    @Qualifier(value = "queryThreadPoolExecutorV2")
    private ThreadPoolExecutor queryThreadPoolExecutorV2;

    @Override
    protected String type() {
        return "basic";
    }

    @Override
    protected void querySupplierHotel(SearchHotelListBo searchHotelListBo) {

        Map<String, SupplierHotelModel> supplierHotelMap = searchHotelListBo.getCurrentPageCacheInfo().getSupplierInfo();

        try {
            RequestContext.getCurrentContext().addContextParams("supplierCodeList", Null.or(supplierHotelMap, new HashMap<String, SupplierHotelModel>()).keySet().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("数据上报异常", e);
        }

        SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();
        StringJoiner logBuilder = new StringJoiner(System.lineSeparator());

        //查询供应商酒店列表（分页查询）->放入future异步调用
        Map<String, LocalHotelListResponseBo> hotelResponseMap;
        Map<String, Long> supplierTimeoutMap = commonGateway.getSupplierTimeoutMap();
        if (com.ctrip.corp.obt.generic.utils.CollectionUtils.isNotEmpty(supplierTimeoutMap)) {
            hotelResponseMap = searchSupplierHotelList(supplierHotelMap, pageRequest, supplierTimeoutMap);
        } else {
            hotelResponseMap = searchSupplierHotelList(supplierHotelMap, pageRequest);
        }

        //将数据放入Map中 穿透查询上下文
        Map<String, LocalHotelListResponseBo> finalHotelResponseMap = hotelResponseMap;
        supplierHotelMap.forEach((supplierCode, hotelInfo) -> {
            LocalHotelListResponseBo responseBo = finalHotelResponseMap.get(supplierCode);
            if (Objects.isNull(responseBo) || responseBo.getHotelList() == null) {
                return;
            }

            List<LocalHotelListResponseBo.HotelListBean> newHotelListBean = new ArrayList<>();
            List<String> hotelIdList = new ArrayList<>(responseBo.getHotelList().size());
            for (int i = 0; i < responseBo.getHotelList().size(); i++) {
                HotelListBean hotelListBean = responseBo.getHotelList().get(i);
                hotelListBean.setSortIndex((pageRequest.getPageNum() - 1) * pageRequest.getPageSize() + i);

                // 记录出过的酒店id
                hotelIdList.add(hotelListBean.getHotelId());

                newHotelListBean.add(hotelListBean);
            }

            // 之前页出现过的酒店id
            Set<String> showedHotelIdList = hotelInfo.getHotelIdListMap().values().stream().flatMap(List::stream).collect(Collectors.toSet());

            // 当前页出过的重复酒店id
            Set<String> currentPageRepeatHotelIdList = new HashSet<>(hotelIdList);
            currentPageRepeatHotelIdList.retainAll(showedHotelIdList);

            // 移除已经出现过的酒店
            if (!currentPageRepeatHotelIdList.isEmpty()) {
                List<HotelListBean> removedShowedHotelList = newHotelListBean.stream().filter(hotel -> currentPageRepeatHotelIdList.contains(hotel.getHotelId())).collect(Collectors.toList());
                newHotelListBean.removeIf(hotel -> currentPageRepeatHotelIdList.contains(hotel.getHotelId()));
                logBuilder.add(String.format("列表查询供应商酒店：移除已查询出过酒店，supplierCode:%s，hotelList:%s", supplierCode, JsonUtils.toJsonString(removedShowedHotelList)));
            }

            hotelIdList.removeAll(currentPageRepeatHotelIdList);
            hotelInfo.getHotelIdListMap().put(pageRequest.getPageNum(), hotelIdList);
            hotelInfo.getRepeatHotelIdListMap().put(pageRequest.getPageNum(), Lists.newArrayList(currentPageRepeatHotelIdList));

            hotelInfo.setHotelList(newHotelListBean);
            hotelInfo.setTotalCount(responseBo.getTotalCount());
            hotelInfo.setPageSize(responseBo.getPageSize());
            hotelInfo.setCurrentPage(responseBo.getCurrentPage());
            hotelInfo.getLastPageMap().put(pageRequest.getPageNum(), responseBo.getLastPage());
        });
        logBuilder.add("列表查询，最新供应商数据: " + JsonUtils.toJsonString(supplierHotelMap));

        log.info(logBuilder.toString());
    }

    /**
     * 查询供应商酒店列表
     */
    private Map<String, LocalHotelListResponseBo> searchSupplierHotelList(Map<String, SupplierHotelModel> supplierHotelMap, SearchHotelListRequestVO pageRequest) {
        addElkInfoLog("查询供应商酒店入参: %s",  JsonUtils.toJsonString(pageRequest));
        List<Future<LocalHotelListResponseBo>> futures = Lists.newArrayList();
        supplierHotelMap.values().forEach(supplierData -> {
            if (Boolean.TRUE.equals(supplierData.getSupplierShield())) {
                log.info("资源屏蔽 supplierData={}", supplierData);
                return;
            }
            // 上一页已经是最后一页了则直接返回
            boolean isPreLastPage = Optional.ofNullable(supplierData.getLastPageMap().lastEntry()).map(Entry::getValue).orElse(false);
            if (isPreLastPage) {
                return;
            }
            LocalHotelListRequestBo supplierRequest = supplierData.getRequest();
            supplierRequest.setPageNum(pageRequest.getPageNum());
            supplierRequest.setPageSize(pageRequest.getPageSize());
            supplierRequest.setLongitude(pageRequest.getLongitude());
            supplierRequest.setLatitude(pageRequest.getLatitude());
            supplierRequest.setRadius(pageRequest.getRadius());
            supplierRequest.setHotelIdList(Lists.newArrayList());
            this.roomAndNumberOfPeople(pageRequest, supplierRequest);
            // 优惠券变价处理
            Integer roomQuantity = pageRequest.getRoomQuantity();
            Integer guestQuantity = Null.or(pageRequest.getGuestQuantity(), roomQuantity);
            if (roomQuantity != null) {
                log.info("优惠券变价case特殊处理");
                supplierRequest.setRoomQuantity(roomQuantity);
                supplierRequest.setGuestQuantity(guestQuantity);
            }
            addElkInfoLog("查询供应商酒店入参: %s",  JsonUtils.toJsonString(supplierRequest));
            CompletableFuture<LocalHotelListResponseBo> future = CompletableFuture.supplyAsync(() -> commonSupplierLoader.page(supplierRequest, supplierData.getSupplierProduct(), supplierData.getBasicCity(), false), queryThreadPoolExecutor);
            futures.add(future);
        });

        long startTime = System.currentTimeMillis();
        // 供应商code->列表查询结果Map
        Map<String, LocalHotelListResponseBo> hotelResponseMap = Optional.ofNullable(BaseUtils.getFuture("酒店列表查询", hotelApollo.getSupplierTimeOut(), futures, result -> {
                if (result == null) {
                    Metrics.REGISTRY.counter(noProductId.withTags("city", Conditional.ofNullable(pageRequest.getCityCode()).orElse(GenericConstants.UNKNOWN))).increment();
                }
            })).orElse(Collections.emptyList())
            .stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(LocalHotelListResponseBo::getSupplier, Function.identity(), (o, n) -> n));
        addElkInfoLog("查询供应商酒店耗时: %s", System.currentTimeMillis() - startTime);
        return hotelResponseMap;
    }
    
    /**
     * 查询供应商酒店列表
     */
    private Map<String, LocalHotelListResponseBo> searchSupplierHotelList(Map<String, SupplierHotelModel> supplierHotelMap, SearchHotelListRequestVO pageRequest, Map<String, Long> supplierTimeoutMap) {
        addElkInfoLog("查询供应商酒店入参: %s",  JsonUtils.toJsonString(pageRequest));
        List<Future<LocalHotelListResponseBo>> futures = Lists.newArrayList();
        supplierHotelMap.values().forEach(supplierData -> {
            if (Boolean.TRUE.equals(supplierData.getSupplierShield())) {
                log.info("资源屏蔽 supplierData={}", supplierData);
                return;
            }
            // 上一页已经是最后一页了则直接返回
            boolean isPreLastPage = Optional.ofNullable(supplierData.getLastPageMap().lastEntry()).map(Entry::getValue).orElse(false);
            if (isPreLastPage) {
                return;
            }
            LocalHotelListRequestBo supplierRequest = supplierData.getRequest();
            supplierRequest.setPageNum(pageRequest.getPageNum());
            supplierRequest.setPageSize(pageRequest.getPageSize());
            supplierRequest.setLongitude(pageRequest.getLongitude());
            supplierRequest.setLatitude(pageRequest.getLatitude());
            supplierRequest.setRadius(pageRequest.getRadius());
            supplierRequest.setHotelIdList(Lists.newArrayList());
            this.roomAndNumberOfPeople(pageRequest, supplierRequest);
            // 优惠券变价处理
            Integer roomQuantity = pageRequest.getRoomQuantity();
            Integer guestQuantity = Null.or(pageRequest.getGuestQuantity(), roomQuantity);
            if (roomQuantity != null) {
                log.info("优惠券变价case特殊处理");
                supplierRequest.setRoomQuantity(roomQuantity);
                supplierRequest.setGuestQuantity(guestQuantity);
            }
            addElkInfoLog("查询供应商酒店入参: %s",  JsonUtils.toJsonString(supplierRequest));
            CompletableFuture<LocalHotelListResponseBo> future = CompletableFuture.supplyAsync(() -> {
                CompletableFuture<LocalHotelListResponseBo> localHotelListResponseBoCompletableFuture = CompletableFuture.supplyAsync(() -> commonSupplierLoader.page(supplierRequest, supplierData.getSupplierProduct(), supplierData.getBasicCity(), false), queryThreadPoolExecutorV2);
                try {
                    return localHotelListResponseBoCompletableFuture.get(supplierTimeoutMap.getOrDefault(supplierData.getSupplierCode(), 30000L), TimeUnit.MILLISECONDS);
                } catch (Exception e) {
                    log.error("供应商超时 supplierCode={}", supplierData.getSupplierCode(), e);
                    return null;
                }
            }, queryThreadPoolExecutor);
            futures.add(future);
        });
        
        long startTime = System.currentTimeMillis();
        // 供应商code->列表查询结果Map
        Map<String, LocalHotelListResponseBo> hotelResponseMap = Optional.ofNullable(BaseUtils.getFuture("酒店列表查询", hotelApollo.getSupplierTimeOut(), futures, result -> {
                    if (result == null) {
                        Metrics.REGISTRY.counter(noProductId.withTags("city", Conditional.ofNullable(pageRequest.getCityCode()).orElse(GenericConstants.UNKNOWN))).increment();
                    }
                })).orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(LocalHotelListResponseBo::getSupplier, Function.identity(), (o, n) -> n));
        addElkInfoLog("查询供应商酒店耗时: %s", System.currentTimeMillis() - startTime);
        return hotelResponseMap;
    }

    @Override
    protected void hotelMapping(SearchHotelListBo searchHotelListBo) {
        HotelListPageCacheInfo currentPageCacheInfo = searchHotelListBo.getCurrentPageCacheInfo();
        Map<String, SupplierHotelModel> supplierHotelMap = currentPageCacheInfo.getSupplierInfo();
        SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();
        StringJoiner logJoiner = new StringJoiner(System.lineSeparator());

        long startTime = System.currentTimeMillis();

        // 是否启动了酒店匹配
        if (supplierHotelMap != null && supplierHotelMap.size() >= 2) {
            RequestContext.getCurrentContext().addContextParams("openHotelMatch", true);
            RequestContext.getCurrentContext().addContextParams("supplierProductNum", supplierHotelMap.size());
        }
        // 获取酒店mapping信息
        List<HmHotelMappingResult> hotelMappingInfo = hotelMappingService.getHotelMappingInfo(supplierHotelMap);
        logJoiner.add("列表查询，酒店mapping合并");
        logJoiner.add(String.format("hotelMappingResult: %s", JsonUtils.toJsonString(hotelMappingInfo)));

        // 获取需要查询起价的酒店id列表
        Map<String, List<String>> searchMinPriceHotelIdMap = hotelMappingService.getSearchMinPriceHotelMap(supplierHotelMap, hotelMappingInfo);
        logJoiner.add(String.format("WaitConfirmHotelMap: %s", JsonUtils.toJsonString(searchMinPriceHotelIdMap)));

        // 查询酒店起价
        long minPriceStartTime = System.currentTimeMillis();
        Map<String, HotelListBean> minPriceHotelListMap = hotelMappingService.querySupplierMinPrice(searchMinPriceHotelIdMap, supplierHotelMap);
        logJoiner.add(String.format("根据酒店ID获取酒店价格耗时: %s", System.currentTimeMillis() - minPriceStartTime));
        logJoiner.add(String.format("根据酒店ID获取酒店价格结果: %s", JsonUtils.toJsonString(minPriceHotelListMap)));

        // 记录查询起价出过的酒店
        Map<String, List<String>> hotelIdMap = minPriceHotelListMap.values().stream().collect(Collectors.groupingBy(HotelListBean::getSupplier, Collectors.mapping(HotelListBean::getHotelId, Collectors.toList())));
        hotelIdMap.forEach((supplierCode, hotelIdList) -> {
            SupplierHotelModel supplierHotelModel = supplierHotelMap.get(supplierCode);
            List<String> searchedHotelIdList = supplierHotelModel.getHotelIdListMap().getOrDefault(pageRequest.getPageNum(), Lists.newArrayList());
            searchedHotelIdList.addAll(hotelIdList);
            supplierHotelModel.getHotelIdListMap().put(pageRequest.getPageNum(), searchedHotelIdList);
        });

        // 酒店mapping合并
        List<HotelListBean> hotelMappingResultList = hotelMappingService.getHotelMappingResult(supplierHotelMap, minPriceHotelListMap, hotelMappingInfo, pageRequest);
        logJoiner.add(String.format("酒店mapping合并结果: %s", JsonUtils.toJsonString(hotelMappingResultList)));
        logJoiner.add(String.format("酒店mapping耗时: %s", System.currentTimeMillis() - startTime));

        // 计算供应商数量
        hotelMappingResultList.forEach(item -> {
            if (item == null || item.getTree() == null || CollectionUtils.isEmpty(item.getTree().getKvs())) {
                return;
            }
            item.setSupplierNum(item.getTree().getKvs().stream().map(Tree.Kv::getSupplier).collect(Collectors.toSet()).size());
        });
        log.info("计算供应商数量 hotelMappingResultList={}", JsonUtils.toJsonString(hotelMappingResultList));

        currentPageCacheInfo.setUnShowedHotelListInfo(hotelMappingResultList);
        supplierHotelMap.values().forEach(supplierHotel -> supplierHotel.setHotelList(new ArrayList<>()));

        log.info(logJoiner.toString());
    }

    @Override
    protected List<HotelListBean> sortHotelList(SearchHotelListBo searchHotelListBo) {
        HotelListPageCacheInfo prePageCacheInfo = searchHotelListBo.getPrePageCacheInfo();
        HotelListPageCacheInfo currentPageCacheInfo = searchHotelListBo.getCurrentPageCacheInfo();
        Map<String, SupplierHotelModel> supplierHotelMap = currentPageCacheInfo.getSupplierInfo();
        SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();
        StringJoiner logJoiner = new StringJoiner(System.lineSeparator());

        List<HotelListBean> needSortHotels = new ArrayList<>();

        // 获取当前页未出酒店
        needSortHotels.addAll(currentPageCacheInfo.getUnShowedHotelListInfo());
        // 获取上一页未出的酒店
        needSortHotels.addAll(CollectionUtils.emptyIfNull(prePageCacheInfo.getUnShowedHotelListInfo()));

        logJoiner.add("列表查询，酒店排序");
        logJoiner.add("排序前：" + JsonUtils.toJsonString(needSortHotels));

        // 1.查服务商优先级配置
        BaseRequestVO.UserInfo userInfo = pageRequest.getUserInfo();
        List<String> supplierCodes = commonService.getSupplierSort(userInfo.getCorpId(), pageRequest.getCorpPayType());
        log.info("服务商优先级配置 supplierCodes={}", JsonUtils.toJsonString(supplierCodes));
        // 延续排序
        supplierCodes = continueSort(supplierCodes, prePageCacheInfo.getShowedHotelListInfo());
        log.info("延续排序 supplierCodes={}", JsonUtils.toJsonString(supplierCodes));

        if (CollectionUtils.isEmpty(supplierCodes)) {
            supplierCodes.addAll(supplierHotelMap.keySet());
        }

        int sort = Optional.ofNullable(pageRequest.getRecommendSort()).orElse(0);
        if (SearchHotelSortEnum.SEARCH_PRICE_SORT_ASC.getType().compareTo(sort) == 0) {
            logJoiner.add("当前排序规则为--->价格从低到高");
            needSortHotels = hotelSortService.sortByPrice(needSortHotels, supplierCodes, true);

        } else if (SearchHotelSortEnum.SEARCH_PRICE_SORT_DESC.getType().compareTo(sort) == 0) {
            logJoiner.add("当前排序规则为--->价格从高到低");
            needSortHotels = hotelSortService.sortByPrice(needSortHotels, supplierCodes, false);

        } else if (SearchHotelSortEnum.SEARCH_DISTANCE_SORT_ASC.getType().compareTo(sort) == 0) {
            logJoiner.add("当前排序规则为--->距离由近到远");
            needSortHotels = hotelSortService.sortByDistance(needSortHotels, supplierCodes, true);

        } else if (SearchHotelSortEnum.SEARCH_DISTANCE_SORT_DESC.getType().compareTo(sort) == 0) {
            logJoiner.add("当前排序规则为--->距离由远到近");
            needSortHotels = hotelSortService.sortByDistance(needSortHotels, supplierCodes, false);

        } else if (SearchHotelSortEnum.SEARCH_STAR_SORT_ASC.getType().compareTo(sort) == 0) {
            logJoiner.add("当前排序规则为--->星级从低到高");
            needSortHotels = hotelSortService.sortByStar(needSortHotels, supplierCodes, true);

        } else if (SearchHotelSortEnum.SEARCH_STAR_SORT_DESC.getType().compareTo(sort) == 0) {
            logJoiner.add("当前排序规则为--->星级从高到低");
            needSortHotels = hotelSortService.sortByStar(needSortHotels, supplierCodes, false);

        } else {
            logJoiner.add("推荐排序,按服务商分组");
            needSortHotels = sortList(needSortHotels, supplierCodes);
        }

        logJoiner.add("排序后：" + JsonUtils.toJsonString(needSortHotels));
        log.info(logJoiner.toString());
        return needSortHotels;
    }

    private List<String> continueSort(List<String> supplierCodeList, List<HotelListBean> showedHotelListInfo) {
        if (CollectionUtils.isEmpty(supplierCodeList) || CollectionUtils.isEmpty(showedHotelListInfo)) {
            return supplierCodeList;
        }
        HotelListBean hotelListBean = showedHotelListInfo.get(showedHotelListInfo.size() - 1);
        if (hotelListBean == null || StringUtils.isBlank(hotelListBean.getSupplier())) {
            return supplierCodeList;
        }
        String supplier = hotelListBean.getSupplier();
        int index = supplierCodeList.indexOf(supplier);
        if (index == -1) {
            return supplierCodeList;
        }
        List<String> newList = supplierCodeList.subList(index + 1, supplierCodeList.size());
        newList.addAll(supplierCodeList.subList(0, index + 1));
        return newList;
    }

    private List<HotelListBean> sortList(List<HotelListBean> needSortHotels, List<String> supplierCodes) {
        if (CollectionUtils.isEmpty(needSortHotels)) {
            return new ArrayList<>(0);
        }

        Map<String, List<HotelListBean>> hotelListBeanMap = new HashMap<>();
        for (HotelListBean needSortHotel : needSortHotels) {
            if (needSortHotel == null) {
                continue;
            }
            String supplier = needSortHotel.getSupplier();
            List<HotelListBean> tmpList = hotelListBeanMap.getOrDefault(supplier, new ArrayList<>());
            tmpList.add(needSortHotel);
            hotelListBeanMap.put(supplier, tmpList);
        }

        // 各个供应商列表排序
        for (String key : hotelListBeanMap.keySet()) {
            List<HotelListBean> hotelListBeans = hotelListBeanMap.get(key);
            if (CollectionUtils.isEmpty(hotelListBeans)) {
                continue;
            }
            hotelListBeanMap.put(key, hotelSortService.sortBySortIndex(hotelListBeans, supplierCodes));
        }

        // 整体排序
        List<HotelListBean> resultList = new ArrayList<>();
        Set<String> endSupplierCode = new HashSet<>();
        for (int i = 0; i < needSortHotels.size(); i++) {
            if (endSupplierCode.containsAll(supplierCodes)) {
                break;
            }
            for (String supplierCode : supplierCodes) {
                List<HotelListBean> hotelListBeans = hotelListBeanMap.get(supplierCode);
                if (CollectionUtils.isEmpty(hotelListBeans) || i >= hotelListBeans.size()) {
                    endSupplierCode.add(supplierCode);
                    continue;
                }
                resultList.add(hotelListBeans.get(i));
            }
        }
        return resultList;
    }

    protected SearchHotelListResponseVO cutListPage(SearchHotelListBo searchHotelListBo, List<HotelListBean> sortList){
        HotelListPageCacheInfo currentPageCacheInfo = searchHotelListBo.getCurrentPageCacheInfo();
        HotelListPageCacheInfo prePageCacheInfo = searchHotelListBo.getPrePageCacheInfo();
        Map<String, SupplierHotelModel> supplierHotelMap = currentPageCacheInfo.getSupplierInfo();
        SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();

        SearchHotelListResponseVO response = new SearchHotelListResponseVO();
        response.setPageSize(pageRequest.getPageSize());
        response.setCurrentPage(pageRequest.getPageNum());
        response.setTotalCount(supplierHotelMap.values().stream().mapToInt(SupplierHotelModel::getTotalCount).sum());

        // 设置本页返回的酒店
        List<HotelListBean> result = sortList.stream().limit(pageRequest.getPageSize()).collect(Collectors.toList());
        response.setHotelList(result);
        currentPageCacheInfo.setShowedHotelListInfo(result);

        // 设置本页未返回的酒店信息
        List<HotelListBean> unShowedHotelList = new ArrayList<>(sortList.subList(result.size(), sortList.size()));
        currentPageCacheInfo.setUnShowedHotelListInfo(unShowedHotelList);

        // 设置是否最后一页(所有供应商查询都已经查到最后一页并且未展示的酒店列表为空)
        boolean allSupplierLastPage = supplierHotelMap.values().stream()
            .map(SupplierHotelModel::getLastPageMap).map(TreeMap::lastEntry)
            .map(entry -> Optional.ofNullable(entry).map(Entry::getValue).orElse(false))
            .allMatch(BooleanUtils::isTrue);
        response.setLastPage(allSupplierLastPage && unShowedHotelList.isEmpty());
        // 连续两页都没结果也认为为最后一页
        if (pageRequest.getPageNum() > 1 && CollectionUtils.isEmpty(currentPageCacheInfo.getShowedHotelListInfo()) && CollectionUtils.isEmpty(prePageCacheInfo.getShowedHotelListInfo())) {
            response.setLastPage(true);
        }
        return response;
    }

    @Override
    protected void postFilter(SearchHotelListBo searchHotelListBo) {
        HotelListPageCacheInfo currentPageCacheInfo = searchHotelListBo.getCurrentPageCacheInfo();
        SearchHotelListRequestVO pageRequest = searchHotelListBo.getPageRequest();

        List<HotelListBean> hotelList = currentPageCacheInfo.getUnShowedHotelListInfo();
        List<HotelListBean> filteredHotelList = new ArrayList<>();
        // 过滤不在价格筛选范围内的
        Iterator<HotelListBean> iterator = hotelList.iterator();
        while (iterator.hasNext()) {
            HotelListBean hotelInfo = iterator.next();
            if (pageRequest.getMinPrice() != null) {
                // 移除小于最小金额的起价
                hotelInfo.getPrice().removeIf(x -> pageRequest.getMinPrice().compareTo(x.getPrice()) > 0);
            }
            if (pageRequest.getMaxPrice() != null) {
                // 移除超过最大金额的起价
                hotelInfo.getPrice().removeIf(x -> pageRequest.getMaxPrice().compareTo(x.getPrice()) < 0);
            }
            // 所有价格都不符合条件则移除
            if (hotelInfo.getPrice().isEmpty()) {
                iterator.remove();
                filteredHotelList.add(hotelInfo);
            }
        }

        addElkInfoLog("后过滤过滤掉的酒店: %s", JsonUtils.toJsonString(filteredHotelList));
    }

    private void roomAndNumberOfPeople(SearchHotelListRequestVO request, LocalHotelListRequestBo supplierRequest) {
        if (CollectionUtils.isEmpty(request.getRooms())) {
            return;
        }
        supplierRequest.setRoomQuantity(request.getRooms().size());
        AtomicReference<Integer> n = new AtomicReference<>(0);
        request.getRooms().forEach(a -> n.set(n.get() + a.getResidentList().size()));
        supplierRequest.setGuestQuantity(n.get());
    }
}
