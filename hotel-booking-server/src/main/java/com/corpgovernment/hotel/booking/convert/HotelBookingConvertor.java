package com.corpgovernment.hotel.booking.convert;

import com.corpgovernment.api.approvalsystem.bean.ApproveUser;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface HotelBookingConvertor {
    HotelBookingConvertor INSTANCE = Mappers.getMapper(HotelBookingConvertor.class);

    List<ApproveUser> toApproveUserList(List<CheckOrderRequestVo.ManualApproveUser> userList);
}
