package com.corpgovernment.hotel.booking.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.corpgovernment.api.hotel.product.dto.HotelModifyPushRequest;
import com.corpgovernment.api.hotel.product.dto.HotelModifyPushResponse;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.hotel.booking.vo.hotelmodify.*;
import com.corpgovernment.hotel.product.service.HotelModifyPushService;
import com.corpgovernment.hotel.product.service.HotelModifyService;

import io.swagger.annotations.ApiOperation;

/**
 * @author: lilayzzz
 * @since: 2023/12/14
 * @description:
 */
@RestController
@RequestMapping("/modify")
public class HotelModifyController {

    @Autowired
    private HotelModifyService hotelModifyService;
    @Autowired
    private HotelModifyPushService hotelModifyPushService;

    /**
     * 酒店修改（提前离店）
     * 
     * @param request
     * @return
     */
    @PostMapping("/hotelModify")
    @ApiOperation("酒店修改（提前离店）")
    public JSONResult<HotelModifyResponse> hotelModify(@RequestBody HotelModifyRequest request) {
        return JSONResult.success(hotelModifyService.hotelModify(request));
    }

    /**
     * 酒店修改详情查询
     *
     * @param request
     * @return
     */
    @PostMapping("/hotelModifyDetail")
    @ApiOperation("酒店修改详情查询")
    public JSONResult<HotelModifyQueryResponse> hotelModifyDetail(@RequestBody HotelModifyQueryRequest request) {
        return JSONResult.success(hotelModifyService.hotelModifyDetail(request));
    }

    /**
     * 酒店修改单推送
     * 
     * @param request
     * @return
     */
    @PostMapping("hotelModifyPush")
    @ApiOperation("酒店修改单推送")
    public JSONResult<HotelModifyPushResponse> hotelModifyPush(@RequestBody HotelModifyPushRequest request) {
        return JSONResult.success(hotelModifyPushService.process(request));
    }


    /**
     * 可修改房晚查询
     *
     * @param request 请求
     * @return {@link JSONResult }<{@link HotelModifiableRoomNightQueryResponse }>
     */
    @PostMapping("/modifiableRoomNightQuery")
    public JSONResult<HotelModifiableRoomNightQueryResponse>
        modifiableRoomNightQuery(@Valid @RequestBody HotelModifiableRoomNightQueryRequest request) {
        return JSONResult.success(hotelModifyService.modifiableRoomNightQuery(request));
    }

}
