package com.corpgovernment.hotel.booking.controller;

import com.corpgovernment.api.ordercenter.dto.orderdetail.OrderInfoRequest;
import com.corpgovernment.api.ordercenter.dto.orderdetail.OrderInfoResponse;
import com.corpgovernment.api.platform.soa.request.SearchPassengerRequest;
import com.corpgovernment.api.platform.soa.response.SearchPassengerResponse;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.hotel.booking.service.ExternalOrderDetailService;
import com.corpgovernment.hotel.booking.service.OrderDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/hotelSearch")
public class HotelSearchController {

    @Autowired
    private OrderDetailService orderDetailService;

    @Autowired
    private ExternalOrderDetailService externalOrderDetailService;

    @RequestMapping("/queryHotelPassenger")
    public JSONResult<List<SearchPassengerResponse>> queryHotelPassenger(@RequestBody SearchPassengerRequest request) {
        return new JSONResult<>(orderDetailService.queryHotelPassenger(request));
    }

    /**
     * 酒店订单详情
     * 
     * @param request
     * @return
     */
    @PostMapping("/external/queryHotelOrderDetail")
    public JSONResult<OrderInfoResponse> queryHotelOrderDetail(@RequestBody OrderInfoRequest request) {
        return JSONResult.success(externalOrderDetailService.queryHotelOrderDetail(request));
    }
}
