package com.corpgovernment.hotel.booking.service.search;

import cn.hutool.core.lang.UUID;
import com.corpgovernment.api.hotel.booking.hotel.request.SearchHotelListRequestVO;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelListRequestBo;
import com.corpgovernment.api.hotel.product.model.request.Tree;
import com.corpgovernment.api.hotel.product.model.request.Tree.Kv;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo;
import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo.HotelListBean;
import com.corpgovernment.basicdata.utils.CommonUtil;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseRequestVO;
import com.corpgovernment.common.shunt.ShuntConfigDao;
import com.corpgovernment.common.utils.BaseUtils;
import com.corpgovernment.core.dao.entity.db.HotelRelationDo;
import com.corpgovernment.core.dao.mysql.IHotelRelationDao;
import com.corpgovernment.hotel.booking.cache.SupplierHotelModel;
import com.corpgovernment.hotel.product.dataloader.soa.CommonSupplierLoader;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.corpgovernment.mapping.bo.HmHotelMappingResult;
import com.corpgovernment.mapping.mapper.HmHotelMappingResultMapper;
import com.ctrip.corp.obt.generic.core.context.RequestContext;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @since 2023/7/4
 */
@Service
@Slf4j
public class HotelMappingService {
    @Autowired
    private CommonSupplierLoader commonSupplierLoader;
    @Autowired
    private HmHotelMappingResultMapper hmHotelMappingResultMapper;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    @Qualifier(value = "queryThreadPoolExecutor")
    private ThreadPoolExecutor queryThreadPoolExecutor;
    @Autowired
    private ShuntConfigDao shuntConfigDao;
    @Autowired
    private IHotelRelationDao hotelRelationDao;
    @Autowired
    private CommonService commonService;

    /**
     * 获取到酒店mapping的映射信息
     */
    public List<HmHotelMappingResult> getHotelMappingInfo(Map<String, SupplierHotelModel> supplierHotelMap) {
        // 匹配开关
        if (Boolean.TRUE.equals(shuntConfigDao.openFeature("ctripHotelMatch"))) {
            RequestContext.getCurrentContext().addContextParams("switchNewHotelMatch", true);
            log.info("【新酒店匹配策略】提供服务的供应商有：{}",supplierHotelMap.keySet());
            if (supplierHotelMap.size() < 2) {
                return Collections.emptyList();
            }

            List<HotelRelationDo> hotelRelationDoList = new ArrayList<>();
            supplierHotelMap.forEach((supplierCode, hotelListInfo) -> {
                List<String> hotelIdList = hotelListInfo.getHotelList().stream().map(HotelListBean::getHotelId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(hotelIdList)) {
                    if ("ctrip".equals(supplierCode)) {
                        List<HotelRelationDo> forwardRelationList = hotelRelationDao.listForwardMatchedRelation(supplierCode, hotelIdList);
                        if (CollectionUtils.isNotEmpty(forwardRelationList)) {
                            hotelRelationDoList.addAll(forwardRelationList);
                        }
                    } else {
                        List<HotelRelationDo> backwardRelationList = hotelRelationDao.listBackwardMatchedRelation(supplierCode, hotelIdList);
                        if (CollectionUtils.isNotEmpty(backwardRelationList)) {
                            hotelRelationDoList.addAll(backwardRelationList);
                        }
                    }
                }
            });
            log.info("【新酒店匹配策略】hotelRelationDoList={}", hotelRelationDoList);
            // 过滤不提供的供应商关系
            log.info("【新酒店匹配策略】查询到的酒店映射关系：{}", hotelRelationDoList);
            if (CollectionUtils.isEmpty(hotelRelationDoList)) {
                return Collections.emptyList();
            }
            // 一行转多行，用于适配老逻辑
            List<HmHotelMappingResult> results = new ArrayList<>();
            Map<HmHotelMappingResult, Set<HmHotelMappingResult>> hotelRelationMap = new HashMap<>();
            for (HotelRelationDo hotelRelationDo : hotelRelationDoList) {
                if (hotelRelationDo == null || StringUtils.isBlank(hotelRelationDo.getMasterSupplierCode()) || StringUtils.isBlank(hotelRelationDo.getMasterHotelId()) || StringUtils.isBlank(hotelRelationDo.getSubSupplierCode()) || StringUtils.isBlank(hotelRelationDo.getSubHotelId())) {
                    continue;
                }
                HmHotelMappingResult tmp1 = convert(hotelRelationDo.getMasterSupplierCode(), hotelRelationDo.getMasterHotelId());
                HmHotelMappingResult tmp2 = convert(hotelRelationDo.getSubSupplierCode(), hotelRelationDo.getSubHotelId());
                Set<HmHotelMappingResult> tmpSet = hotelRelationMap.getOrDefault(tmp1, new HashSet<>());
                tmpSet.add(tmp2);
                hotelRelationMap.put(tmp1, tmpSet);
            }
            for (HmHotelMappingResult item : hotelRelationMap.keySet()) {
                String hotelKey = UUID.fastUUID().toString();
                Set<HmHotelMappingResult> value = hotelRelationMap.get(item);
                if (CollectionUtils.isEmpty(value)) {
                    continue;
                }
                Set<HmHotelMappingResult> tmpSet = new HashSet<>();
                if (supplierHotelMap.containsKey(item.getSupplierCode())) {
                    item.setHotelKey(hotelKey);
                    tmpSet.add(item);
                }
                for (HmHotelMappingResult tmp: value) {
                    if (supplierHotelMap.containsKey(tmp.getSupplierCode())) {
                        tmp.setHotelKey(hotelKey);
                        tmpSet.add(tmp);
                    }
                }
                if (tmpSet.size() >= 2) {
                    results.addAll(tmpSet);
                }
            }
            results = new ArrayList<>(results.stream().collect(Collectors.toMap(mappingResult -> mappingResult.getSupplierCode() + "_" + mappingResult.getHotelId() + "_" + mappingResult.getHotelKey(), Function.identity(), (m1, m2) -> m1)).values());
            log.info("【新酒店匹配策略】转换后的酒店映射关系：{}", results);
            // 过滤重复的mapping信息
            return results;
        }
        List<HmHotelMappingResult> results = new ArrayList<>();
        supplierHotelMap.forEach((supplierCode, hotelListInfo) -> {
            List<String> allHotelIds = hotelListInfo.getHotelList().stream().map(HotelListBean::getHotelId).collect(Collectors.toList());
            results.addAll(selectHotelMappingResult(allHotelIds, supplierCode));
        });
        // 过滤重复的mapping信息
        return new ArrayList<>(results.stream().collect(Collectors.toMap(mappingResult -> mappingResult.getSupplierCode() + "_" + mappingResult.getHotelId() + "_" + mappingResult.getHotelKey(), Function.identity(), (m1, m2) -> m1)).values());
    }

    private HmHotelMappingResult convert(String supplierCode, String hotelId) {
        HmHotelMappingResult hmHotelMappingResult = new HmHotelMappingResult();
        hmHotelMappingResult.setSupplierCode(supplierCode);
        hmHotelMappingResult.setHotelId(hotelId);
        return hmHotelMappingResult;
    }

    /**
     * 获取到需要查询起价的酒店id列表
     */
    public Map<String, List<String>> getSearchMinPriceHotelMap(Map<String, SupplierHotelModel> supplierHotelMap, List<HmHotelMappingResult> hotelMappingResult) {
        // 酒店供应商+酒店id->酒店mapping关系
        Map<String, HmHotelMappingResult> mappingResultMap = hotelMappingResult.stream().collect(Collectors.toMap(mappingResult -> mappingResult.getSupplierCode() + "_" + mappingResult.getHotelId(), Function.identity(), (m1, m2) -> m1));
        supplierHotelMap.forEach((supplierCode, hotelListInfo) -> {
            for (HotelListBean hotel : hotelListInfo.getHotelList()) {
                // 移除已经有价格的mapping
                mappingResultMap.remove(supplierCode + "_" + hotel.getHotelId());
            }
        });

        return mappingResultMap.values().stream().collect(Collectors.groupingBy(HmHotelMappingResult::getSupplierCode, mapping(HmHotelMappingResult::getHotelId, toList())));
    }

    /**
     * 查询供应商起价
     * 供应商code+供应商id->供应商起价信息
     */
    public Map<String, HotelListBean> querySupplierMinPrice(Map<String, List<String>> searchMinPriceHotelIdMap, Map<String, SupplierHotelModel> supplierHotelMap) {
        //查询供应商酒店列表（分页查询）->放入future异步调用
        List<Future<LocalHotelListResponseBo>> futures = Lists.newArrayList();

        searchMinPriceHotelIdMap.forEach((supplierCode, hotelIds) -> {
            SupplierHotelModel supplierHotelModel = supplierHotelMap.get(supplierCode);
            if (Objects.isNull(supplierHotelModel)) {
                return;
            }

            //根据ID查询只放入基本信息
            LocalHotelListRequestBo supplierRequest = new LocalHotelListRequestBo();
            supplierRequest.setPageNum(1);
            supplierRequest.setPageSize(hotelIds.size());
            supplierRequest.setHotelIdList(hotelIds);
            supplierRequest.setSDate(supplierHotelModel.getRequest().getSDate());
            supplierRequest.setEDate(supplierHotelModel.getRequest().getEDate());
            supplierRequest.setCity(supplierHotelModel.getRequest().getCity());
            supplierRequest.setCityCode(supplierHotelModel.getRequest().getCityCode());
            supplierRequest.setRecommendSort(supplierHotelModel.getRequest().getRecommendSort());
            supplierRequest.setCorpPayType(supplierHotelModel.getRequest().getCorpPayType());
            supplierRequest.setSupplierCityId(supplierHotelModel.getRequest().getSupplierCityId());
            CompletableFuture<LocalHotelListResponseBo> future = CompletableFuture.supplyAsync(() -> commonSupplierLoader.page(supplierRequest, supplierHotelModel.getSupplierProduct(), supplierHotelModel.getBasicCity(), true), queryThreadPoolExecutor);
            futures.add(future);
        });

        // 查询出的供应商code+供应商id->供应商起价信息
        Map<String, LocalHotelListResponseBo.HotelListBean> minPriceHotelListMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(futures)) {

            BaseUtils.getFuture("酒店列表查询", hotelApollo.getSupplierTimeOut(), futures).stream().filter(Objects::nonNull).
                collect(Collectors.toMap(LocalHotelListResponseBo::getSupplier, Function.identity(), (o, n) -> n))
                .forEach((supplierCode, hotelListInfo) -> {
                    if (Objects.isNull(hotelListInfo) ||CollectionUtils.isEmpty(hotelListInfo.getHotelList())) {
                        return;
                    }
                    for (LocalHotelListResponseBo.HotelListBean hotelInfo : hotelListInfo.getHotelList()) {
                        minPriceHotelListMap.put(CommonUtil.getKey(supplierCode, hotelInfo.getKey()), hotelInfo);
                    }
                });
        }
        return minPriceHotelListMap;
    }

    /**
     * 获取酒店mapping结果
     */
    public List<HotelListBean> getHotelMappingResult(Map<String, SupplierHotelModel> supplierHotelMap, Map<String, HotelListBean> minPriceHotelListMap,
                                                     List<HmHotelMappingResult> hotelMappingResultList, SearchHotelListRequestVO pageRequest) {
        Map<String, HmHotelMappingResult> hotelSupplierCodeIdMappingMap = hotelMappingResultList.stream().collect(Collectors.toMap(item -> CommonUtil.getKey(item.getSupplierCode(), item.getHotelId()), Function.identity(), (k1, k2) -> k1));
        Map<String, List<HmHotelMappingResult>> hotelKeyMappingMap = hotelMappingResultList.stream().collect(Collectors.groupingBy(HmHotelMappingResult::getHotelKey));

        // 供应商列表查询结果
        Map<String, HotelListBean> hotelMap = supplierHotelMap.values().stream().map(SupplierHotelModel::getHotelList)
            .flatMap(Collection::stream)
            .collect(Collectors.toMap(hotel -> CommonUtil.getKey(hotel.getSupplier(), hotel.getHotelId()), Function.identity(), (h1, h2) -> h1));

        // 需要mapping的查询结果
        Map<String, HotelListBean> needMappingHotelMap = hotelMap.entrySet().stream()
            .filter(entry -> hotelSupplierCodeIdMappingMap.containsKey(entry.getKey()))
            .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

        // 不需要mapping的查询结果
        Map<String, HotelListBean> notNeedMappingHotelMap = hotelMap.entrySet().stream()
            .filter(entry -> !hotelSupplierCodeIdMappingMap.containsKey(entry.getKey()))
            .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

        // 获取供应商优先级
        List<String> supplierSortList = commonService.getSupplierSort(
                Optional.ofNullable(pageRequest).map(BaseRequestVO::getUserInfo).map(BaseRequestVO.UserInfo::getCorpId).orElse(""),
                Optional.ofNullable(pageRequest).map(SearchHotelListRequestVO::getCorpPayType).orElse(""));
        // mapping合并
        List<HotelListBean> hotelMappingResult = new ArrayList<>();
        // hotelKeyMappingList是同一个酒店 但是supplier_code hotel_id 不同
        hotelMappingResult.addAll(hotelKeyMappingMap.values().stream().map(hotelKeyMappingList -> hotelMappingMerge(hotelKeyMappingList, needMappingHotelMap, minPriceHotelListMap, pageRequest, supplierSortList)).filter(Objects::nonNull).collect(toList()));
        hotelMappingResult.addAll(notNeedMappingHotelMap.values().stream().map(this::convertTree).collect(toList()));

        return hotelMappingResult;
    }

    private List<HmHotelMappingResult> selectHotelMappingResult(List<String> hotelIds, String supplierCode) {
        if (CollectionUtils.isEmpty(hotelIds)) {
            return Collections.emptyList();
        }

        Example example = new Example(HmHotelMappingResult.class);
        example.createCriteria().andIn("hotelId", hotelIds).andEqualTo("supplierCode", supplierCode);
        List<HmHotelMappingResult> hotelMappingResultList = hmHotelMappingResultMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(hotelMappingResultList)) {
            return Collections.emptyList();
        }

        example = new Example(HmHotelMappingResult.class);
        example.createCriteria().andIn("hotelKey", hotelMappingResultList.stream().map(HmHotelMappingResult::getHotelKey).collect(Collectors.toList()));
        hotelMappingResultList = hmHotelMappingResultMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(hotelMappingResultList)) {
            return Collections.emptyList();
        }

        return hotelMappingResultList;
    }

    /**
     * 酒店mapping合并
     *
     * @param hotelKeyMappingList  酒店key对应的mapping信息
     * @param needMappingHotelMap  需要mapping的查询出的酒店
     * @param minPriceHotelListMap 酒店起价信息
     * @param pageRequest          列表查询请求
     */
    private HotelListBean hotelMappingMerge(List<HmHotelMappingResult> hotelKeyMappingList, Map<String, HotelListBean> needMappingHotelMap,
                                            Map<String, HotelListBean> minPriceHotelListMap, SearchHotelListRequestVO pageRequest, List<String> supplierSortList) {
        // 所有需要合并的酒店列表
        List<HotelListBean> hotelListBeans = new ArrayList<>(hotelKeyMappingList.size());
        // 先添加最初查出来的酒店
        hotelKeyMappingList.stream().map(mappingInfo -> needMappingHotelMap.get(CommonUtil.getKey(mappingInfo.getSupplierCode(), mappingInfo.getHotelId()))).filter(Objects::nonNull).forEach(hotelListBeans::add);

        // 后添加需求补起价的酒店
        hotelKeyMappingList.stream().map(mappingInfo -> minPriceHotelListMap.get(CommonUtil.getKey(mappingInfo.getSupplierCode(), mappingInfo.getHotelId()))).filter(Objects::nonNull).forEach(hotelListBeans::add);

        if (hotelListBeans.isEmpty()) {
            return null;
        }
        Tree tree = new Tree();

        tree.setLocalHotelId(String.valueOf(hotelKeyMappingList.get(0).getHotelKey()));
        List<Kv> kvs = hotelListBeans.stream().map(hotle -> {
            Kv kv = new Kv();
            kv.setHotelId(hotle.getHotelId());
            kv.setSupplier(hotle.getSupplier());
            return kv;
        }).collect(toList());
        tree.setKvs(kvs);

        // 用于合并的基础酒店
        HotelListBean baseHotel = hotelListBeans.get(0);
        tree.setBaseSupplier(baseHotel.getSupplier());
        baseHotel.setTree(tree);

        // 按优先级排序
        hotelListBeans = hotelListBeans.stream().sorted(Comparator.comparingInt(hotel -> {
            int index = supplierSortList.indexOf(hotel.getSupplier());
            if (index == -1) {
                return Integer.MAX_VALUE;
            }
            return index;
        })).collect(toList());

        for (HotelListBean hotel : hotelListBeans) {
            List<LocalHotelListResponseBo.HotelListBean.PriceBean> priceBeanList = new ArrayList<>();
            for (LocalHotelListResponseBo.HotelListBean.PriceBean price : hotel.getPrice()) {
                LocalHotelListResponseBo.HotelListBean.PriceBean priceBean = new LocalHotelListResponseBo.HotelListBean.PriceBean();
                if (price.getPrice() == null) {
                    price.setPrice(BigDecimal.ZERO);
                }
                priceBean.setPrice(price.getPrice().setScale(0, BigDecimal.ROUND_HALF_UP));
                priceBean.setName(price.getName());
                priceBeanList.add(priceBean);
            }
            baseHotel.getPrice().addAll(priceBeanList);

            //参数补充假如没有就去取别的供应商的
            //背景：例如美亚缺少评分，地址，将这些参数填充进来

            //评分补充
            if (StringUtils.isNotBlank(baseHotel.getScore())) {
                if (Double.parseDouble(baseHotel.getScore()) <= 0.1) {
                    baseHotel.setScore(hotel.getScore());
                    if (StringUtils.isNotBlank(pageRequest.getStarList())) {
                        if (pageRequest.getStarList().contains(hotel.getStarLevel().getKey())) {
                            baseHotel.setStarLevel(hotel.getStarLevel());
                        }
                    } else {
                        baseHotel.setStarLevel(hotel.getStarLevel());
                    }
                }
            } else {
                baseHotel.setScore(hotel.getScore());
            }
            //距离景点/地铁站距离
            if (StringUtils.isBlank(baseHotel.getAppDistance())) {
                baseHotel.setAppDistance(hotel.getAppDistance());
            }
            //地点名称
            if (StringUtils.isBlank(baseHotel.getAppPlace())) {
                baseHotel.setAppPlace(hotel.getAppPlace());
            }
            //距离公里数
            if (StringUtils.isBlank(baseHotel.getDistance())) {
                baseHotel.setDistance(hotel.getDistance());
            }
            //提示，早餐、停车位、娱乐设施
            if (CollectionUtils.isEmpty(baseHotel.getServiceTips())) {
                baseHotel.setServiceTips(hotel.getServiceTips());
            }
            String tmpProtocolTag = hotel.getProtocolTag();
            String protocolTag = baseHotel.getProtocolTag();
            if (tmpProtocolTag != null && (protocolTag == null || !protocolTag.equals("平台协议价"))) {
                baseHotel.setProtocolTag(hotel.getProtocolTag());
            }
        }

        List<HotelListBean.PriceBean> priceBeanList = baseHotel.getPrice();
        if (CollectionUtils.isNotEmpty(priceBeanList)) {
            baseHotel.setPrice(priceBeanList.stream().distinct().collect(Collectors.toList()));
        }
        return baseHotel;
    }

    public HotelListBean convertTree(LocalHotelListResponseBo.HotelListBean hotelListBean) {
        Tree tree = new Tree();
        List<Tree.Kv> kvs = new ArrayList<>();
        Tree.Kv kv = new Tree.Kv();
        kv.setHotelId(hotelListBean.getKey());
        kv.setSupplier(hotelListBean.getSupplier());
        kvs.add(kv);
        tree.setKvs(kvs);
        tree.setLocalHotelId(CommonUtil.getKey(hotelListBean.getSupplier(), hotelListBean.getKey()));
        tree.setBaseSupplier(hotelListBean.getSupplier());
        for (LocalHotelListResponseBo.HotelListBean.PriceBean price : hotelListBean.getPrice()) {
            price.setPrice(Optional.ofNullable(price.getPrice()).orElse(BigDecimal.ZERO).setScale(0, BigDecimal.ROUND_HALF_UP));
        }
        hotelListBean.setTree(tree);
        return hotelListBean;
    }
}
