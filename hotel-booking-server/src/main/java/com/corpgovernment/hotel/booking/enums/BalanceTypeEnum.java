package com.corpgovernment.hotel.booking.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 酒店支付类型
 *
 * <AUTHOR>
 */
public enum BalanceTypeEnum {
	PP("PP", "在线付"),
	FG("FG", "前台现付"),
	USE_FG("USE_FG","在线付"),//现转预
	;

	private final String code;
	private final String desc;

	BalanceTypeEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 根据code获取枚举
	 *
	 * @param code
	 * @return
	 */
	public static BalanceTypeEnum getByCode(String code) {
		if (StringUtils.isBlank(code)) {
			return null;
		}

		return Arrays.stream(BalanceTypeEnum.values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
	}

	public String getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
}
