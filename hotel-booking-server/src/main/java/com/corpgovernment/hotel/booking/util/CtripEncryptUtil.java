package com.corpgovernment.hotel.booking.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

/**
 * @ClassName: CtripEncryptUtil
 * @description: 携程供应商请求参数封装工具类
 * @author: yssong
 * @date: Created in 10:03 2019/8/23
 * @Version: 1.0
 **/
public class CtripEncryptUtil {

    /**
     * 查询酒店的生产url前缀
     */
//    private static final String searchHotelCityUrl = "Https://sopenservice.ctrip.com/OpenService/ServiceProxy.ashx";

    /**
     * 查询酒店的测试url前缀
     */
    private static final String searchHotelCityUrl = "http://openservice.open.uat.ctripqa.com/openservice/serviceproxy.ashx";


    /**
     * 酒店分销接口通用
     *
     * @param icode 接口对应的icode
     * @return
     */
    public static String encryptCommonHotel(String icode) {

        //测试
        String allianceId = "1";
        String sid = "50";
        String userKey = "123456789";

        //生产
//        String allianceId = "1066305";
//        String sid = "1933293";
//        String userKey = "c1259444a4b74e48a7d4ac3ed9e5b3ee";

        // 随机码, 请求唯一码
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String[] p = new String[]{allianceId, sid, userKey, icode, uuid};
        String data = String.format("aid=%s&sid=%s&userkey=%s&icode=%s&uuid=%s", (Object[]) p);
        String token = getSHA256(data);
        String[] param = new String[]{allianceId, sid, icode, token, uuid};
        String returnRequestParam = String.format("?aid=%s&sid=%s&icode=%s&token=%s&uuid=%s", (Object[]) param);
        return searchHotelCityUrl + returnRequestParam + "&e=r6&mode=1&format=json";
    }

    /**
     *     * 利用java原生的类实现SHA256加密
     *     * @param str 加密后的报文
     *     * @return
     *     
     */
    public static String getSHA256(String str) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodestr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
		return encodestr;
    }

    /**
     *     * 将byte转为16进制
     *     * @param bytes
     *     * @return
     *     
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
		for (byte aByte : bytes) {
			temp = Integer.toHexString(aByte & 0xFF);
			if (temp.length() == 1) {
				//1得到一位的进行补0操作
				stringBuffer.append("0");
			}
			stringBuffer.append(temp);
		}
        return stringBuffer.toString();
    }

}
