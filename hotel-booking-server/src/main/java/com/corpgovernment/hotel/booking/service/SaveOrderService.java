package com.corpgovernment.hotel.booking.service;


import static com.corpgovernment.hotel.product.external.constant.SupplierConstant.ReserveOrder.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import com.corpgovernment.api.hotel.product.model.saveorder.request.OrderSnapshotBookingConfigDataBo;
import com.corpgovernment.api.ordercenter.enums.ProductEnum;
import com.corpgovernment.constants.RiskControlStatusEnum;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelProductSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelProductSnapshotResponse;
import com.corpgovernment.hotel.booking.config.apollo.OrderSnapshotDataConfig;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.applytrip.enums.ApplyTripEmployeeEnum;
import com.corpgovernment.api.applytrip.enums.LineVerifyTypeEnum;
import com.corpgovernment.api.applytrip.metadata.ApplyTripStockOrderStatus;
import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficReturnType;
import com.corpgovernment.api.applytrip.metadata.EApplyTripTrafficType;
import com.corpgovernment.api.applytrip.soa.request.ApplyTripUpdateRemakeRequest;
import com.corpgovernment.api.applytrip.soa.request.UseApplyTripTrafficRequest;
import com.corpgovernment.api.approvalsystem.bean.FlowDetail;
import com.corpgovernment.api.approvalsystem.bean.SubmitterInfo;
import com.corpgovernment.api.approvalsystem.bean.TraverlerInfo;
import com.corpgovernment.api.approvalsystem.enums.ApprovalFlowFlagEnum;
import com.corpgovernment.api.approvalsystem.enums.ApprovalTypeEnum;
import com.corpgovernment.api.approvalsystem.enums.FlowTmplPayTypeEnum;
import com.corpgovernment.api.approvalsystem.service.ApprovalSystemClient;
import com.corpgovernment.api.approvalsystem.service.request.CreateApprovalRequest;
import com.corpgovernment.api.approvalsystem.service.response.CreateApprovalResponse;
import com.corpgovernment.api.car.vo.response.AccountingUnitInfoVo;
import com.corpgovernment.api.costcenter.model.CostCenter;
import com.corpgovernment.api.costcenter.model.TempCostCenterVo;
import com.corpgovernment.api.hotel.booking.core.TravelExceedInfo;
import com.corpgovernment.api.hotel.booking.hotel.request.BusinessFileRequestVO;
import com.corpgovernment.api.hotel.booking.hotel.request.PackageRoomRequestVo;
import com.corpgovernment.api.hotel.booking.saveorder.request.SaveOrderRequestVo;
import com.corpgovernment.api.hotel.booking.saveorder.response.SaveOrderResponseVo;
import com.corpgovernment.api.hotel.product.bo.HotelUpdateOrderStatusRequestBo;
import com.corpgovernment.api.hotel.product.enums.*;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo.ClientInfo;
import com.corpgovernment.api.hotel.product.model.bookorder.request.LocalBookOrderRequestBo.LocalOptionalRemark;
import com.corpgovernment.api.hotel.product.model.bookorder.response.LocalBookOrderResponseBo;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.hotel.product.model.response.PackageRoomResponseVo;
import com.corpgovernment.api.hotel.product.model.saveorder.request.SaveOrderRequestBo;
import com.corpgovernment.api.hotel.product.model.saveorder.request.SaveOrderRequestBo.OrderInfo;
import com.corpgovernment.api.hotel.product.model.updateorder.request.UpdateOrderRequestBo;
import com.corpgovernment.api.order.common.dos.OcApplyTripControlRecord;
import com.corpgovernment.api.order.common.enums.ApplyTripControlOperationTypeEnum;
import com.corpgovernment.api.order.common.enums.ApplyTripControlTypeEnum;
import com.corpgovernment.api.organization.model.passenger.CheckPassengerRequest;
import com.corpgovernment.api.organization.model.passenger.MobilePhoneVo;
import com.corpgovernment.api.organization.model.passenger.PassengerVo;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoRequest;
import com.corpgovernment.api.organization.model.user.employee.EmployeeInfoRequestBo;
import com.corpgovernment.api.organization.model.user.employee.EmployeeInfoResponseBo;
import com.corpgovernment.api.organization.model.user.employee.OrgEmployeeVo;
import com.corpgovernment.api.organization.model.user.nonemployee.OrgNonEmployeeVo;
import com.corpgovernment.api.organization.soa.switchbo.GetPassengerNameRequest;
import com.corpgovernment.api.platform.soa.paymentbill.CreatePaymentBillRequest;
import com.corpgovernment.api.platform.soa.paymentbill.CreatePaymentBillResponse;
import com.corpgovernment.api.supplier.soa.constant.PubOwnEnum;
import com.corpgovernment.api.supplier.vo.MbSupplierInfoVo;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.basic.bo.response.TravelStandardResponseBO;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.constant.LogTagConstants;
import com.corpgovernment.basicdata.bo.HotelBrandBo;
import com.corpgovernment.basicdata.service.impl.HotelBrandDataService;
import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.common.apollo.CommonApollo;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseRequestVO.UserInfo;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.dataloader.CommonOrderCenterDataloader;
import com.corpgovernment.common.dataloader.CommonOrganizationDataloader;
import com.corpgovernment.common.dto.GetEmployeeOpenCardReq;
import com.corpgovernment.common.dto.GetEmployeeOpenCardRsp;
import com.corpgovernment.common.dto.OrderUserInfoRsp;
import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.enums.OrderSourceEnum;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.logging.bo.LogTags;
import com.corpgovernment.common.logging.service.LoggerService;
import com.corpgovernment.common.logging.service.LoggerServiceFactory;
import com.corpgovernment.common.passport.PassportConvertUtil;
import com.corpgovernment.common.passport.bo.PassportConvertBO;
import com.corpgovernment.common.pay.PPayUtil;
import com.corpgovernment.common.pay.PayTokenInfo;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.consolidation.sdk.enums.ApplicationEnum;
import com.corpgovernment.consolidation.sdk.event.OrderCreateExternalEvent;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.constants.TravelStandardOwnerTypeEnum;
import com.corpgovernment.core.domain.gateway.HotelSnapshotGateway;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.model.snapshot.config.BookingConfigModel;
import com.corpgovernment.core.domain.model.snapshot.config.SwitchModel;
import com.corpgovernment.core.domain.model.snapshot.product.ProductSnapshotModel;
import com.corpgovernment.core.service.TravelCheckService;
import com.corpgovernment.dto.config.AllSwitchDTO;
import com.corpgovernment.dto.config.SwitchDTO;
import com.corpgovernment.dto.config.request.GetBookingConfigByTokenRequest;
import com.corpgovernment.dto.config.response.GetBookingConfigByTokenResponse;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.response.TravelStandardTokenResponse;
import com.corpgovernment.hotel.booking.bo.BookingConfig;
import com.corpgovernment.hotel.booking.cache.OrderInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel.InvoiceInfo;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel.PassengerInfo;
import com.corpgovernment.hotel.booking.config.apollo.StandardExceedCheckSwitch;
import com.corpgovernment.hotel.booking.enums.BonusPointTypeEnum;
import com.corpgovernment.hotel.booking.enums.DuplicateCheckModeEnum;
import com.corpgovernment.hotel.booking.metric.MetricService;
import com.corpgovernment.hotel.booking.metric.SaveOrderMetricContext;
import com.corpgovernment.hotel.booking.request.CheckDuplicateBookingRequest;
import com.corpgovernment.hotel.booking.vo.CheckDuplicateBookingResponse;
import com.corpgovernment.hotel.booking.vo.GetSaveOrderResultRequestVO;
import com.corpgovernment.hotel.booking.vo.GetSaveOrderResultResponseVO;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.soa.*;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.external.client.SupplierSoaClient;
import com.corpgovernment.hotel.product.external.converter.CtripSyncOrderCostCenterConverter;
import com.corpgovernment.hotel.product.external.dto.order.book.StandardReserveOrderRequest;
import com.corpgovernment.hotel.product.external.dto.order.book.StandardReserveOrderResponse;
import com.corpgovernment.hotel.product.external.dto.order.synccostcenter.CtripSyncOrderCostCenterRequest;
import com.corpgovernment.hotel.product.external.dto.order.synccostcenter.CtripSyncOrderCostCenterResponse;
import com.corpgovernment.hotel.product.service.CommonSupplierServiceNew;
import com.corpgovernment.hotel.product.service.HotelPushService;
import com.corpgovernment.hotel.product.service.SaveOrderProductService;
import com.corpgovernment.hotel.product.supplier.enums.InvoiceEnum;
import com.corpgovernment.hotel.product.supplier.enums.InvoiceTitleEnum;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.event.EventCenter;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.ObjectUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SaveOrderService extends BaseService {
    private final static LoggerService LOGGER = LoggerServiceFactory.getLoggerService(SaveOrderService.class);

    private final static String TITLE = "save_order";
    /**
     * 下单redis key 前缀
     */
    public final static String CREATE_ORDER_PREFIX = "createOrder_";
    private static final String ATTR_MANAGEMENT_CONTROL = "attr_management_control";
    private static final String ATTR_PRODUCT_CONTROL = "attr_product_control";

    public static final String ATTR_MANAGEMENT_CONTROL_NOT_ENABLED = "3";
    public static final String ATTR_PRODUCT_CONTROL_VALUE = "2";

    @Autowired
    private OrderInfoCacheManager orderInfoCacheManager;
    @Autowired
    private HotelManager hotelManager;
    @Autowired
    private PassengerClientLoader passengerClientLoader;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private OrganizationNonEmployeeClientLoader organizationNonEmployeeClientLoader;
    @Autowired
    private ApprovalSystemClient approvalSystemService;
    @Autowired
    private SaveOrderProductService saveOrderProductService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private SupplierDataClientLoader supplierDataClientLoader;
    @Autowired
    private OrganizationEmployeeClientLoader organizationEmployeeClientLoader;
    @Autowired
    private CommonSupplierServiceNew commonSupplierServiceNew;
    @Autowired
    private PayClientLoader payClientLoader;
    @Autowired
    private HotelPushService hotelPushService;
    @Autowired
    private HotelBrandDataService hotelBrandDataService;
    @Autowired
    @Qualifier(value = "queryThreadPoolExecutor")
    private ThreadPoolExecutor queryThreadPoolExecutor;
    @Autowired
    private CommonOrganizationDataloader commonOrganizationDataloader;
    @Autowired
    private OrderUserClientLoader orderUserClientLoader;
    @Autowired
    private OrganizationPassengerClientLoader organizationPassengerClientLoader;
    @Autowired
    private PackageRoomService packageRoomService;
    @Autowired
    private MetricService metricService;
    @Autowired
    private SupplierSoaClient supplierSoaClient;
    @Autowired
    private EventCenter eventCenter;

    @Autowired
    private StandardExceedCheckSwitch standardExceedCheckSwitch;
    @Autowired
    private CommonOrderCenterDataloader commonOrderCenterDataloader;
    @Autowired
    private CtripSyncOrderCostCenterConverter syncOrderCostCenterConverter;

    @Autowired
    private TravelCheckService travelCheckService;
    @Autowired
    private CheckOrderService checkOrderService;
    @Autowired
    private HotelSnapshotGateway hotelSnapshotGateway;

    @Autowired
    private CoreServiceClient coreServiceClient;
    @Autowired
    private CommonApollo commonApollo;
    @Autowired
    private OrderSnapshotDataConfig orderSnapshotDataConfig;
    @Autowired
    private CoreServiceClientLoader coreServiceClientLoader;


    public SaveOrderResponseVo saveOrder(SaveOrderRequestVo request, SaveOrderMetricContext metricContext) {
        // 校验停留时间
        hotelManager.checkStateTime(false);
        initElkLog();
        addElkInfoLog("入参request:" + JsonUtils.toJsonString(request));
        Long orderId = request.getOrderId();
        UserInfo userInfo = request.getUserInfo();
        // 因为要在catch中使用orderInfo 回退出差申请单，所以将锁获取提到了try外面 保证orderInfo数据不会被修改
        RLock lock = redissonClient.getLock(CREATE_ORDER_PREFIX + orderId);
        boolean locked = lock.tryLock();
        addElkInfoLog("下单获取锁，防止重复下单" + locked);
        if (!locked) {
            addElkInfoLog("，获取订单锁失败,下单结果：失败");
            return SaveOrderResponseVo.failure();
        }
        OrderInfoModel applyTripOrderInfo = null;
        // 是否删除订单
        boolean isDeleteOrder = false;
        // 是否回滚出差申请单
        boolean isRollbackApply = false;
        SaveOrderResponseVo responseVo = new SaveOrderResponseVo();
        try {
            // 该错误码前端会轮播获取订单结果
            saveOrderResult(orderId, HotelResponseCodeEnum.SAVE_ORDER_STATRT_ERROR, responseVo);

            // 获取订单缓存，为空抛出业务异常
            OrderInfoModel orderInfo = Optional
                .ofNullable(
                    orderInfoCacheManager.getOrderInfo(request.getHotelId(), request.getRoomId(), userInfo.getToken()))
                .orElseThrow(
                    () -> new CorpBusinessException(HotelResponseCodeEnum.STAY_TOO_LONG_PLEASE_CHECK_THE_PRICE_AGAIN));
            LOGGER.initTags(getLogTags(orderInfo));
            //判断前端传递的订单号是否和缓存中的订单号一致
            if (!Objects.equals(orderId, orderInfo.getOrderId())) {
                addElkInfoLog("订单号不一致，请刷新页面重新下单，%s %s", orderId, orderInfo.getOrderId());
                throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_CREATE_ERROR);
            }

            addElkInfoLog("酒店下单订单缓存:" + JsonUtils.toJsonString(orderInfo));
            if (OrderInfoModel.isEmpty(orderInfo)) {
                addElkInfoLog("缓存为空，订单下单失败");
                throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_CREATE_ERROR);
            }

            //风控校验
            boolean riskControlCheck = riskControlCheck(orderInfo);
            if (!riskControlCheck) {
                throw new CorpBusinessException(HotelResponseCodeEnum.RISK_CONTROL_CHECK_ERROR);
            }


            //紧急预定判断是否配置审批流
            Map<Integer, FlowDetail> flowDetailMap = orderInfo.getFlowDetailMap();
            if(CorpPayTypeEnum.PUB.getType().equalsIgnoreCase(orderInfo.getCorpPayType()) && Boolean.TRUE.equals(orderInfo.getUrgentApply()) && (CollectionUtils.isEmpty(flowDetailMap) || !flowDetailMap.containsKey(ApprovalFlowFlagEnum.NORMAL.getCode()))){
                // 紧急预定判断是否配置审批流
//            LogSplicingUtils.addLogContext(logContent, "缺少审批流将无法提交订单，仅支持查询酒店信息");
                throw new CorpBusinessException(HotelResponseCodeEnum.NO_APPROVAL_REPEAT_SUBMIT_ERROR);
            }

            metricContext.setSupplierCode(orderInfo.getSupplierCode());

            // 重复下单检查
            checkRepeatBooking(orderInfo);
            // 超标检查
            travelStandardCheck(orderInfo);

            // 校验出差申请单管控
            boolean pass = checkApplicationFormControl(orderInfo);
            if (!pass) {
                log.warn("出差出差申请单管控校验不通过");
                throw new CorpBusinessException(HotelResponseCodeEnum.APPLY_TRIP_FORM_NOT_EXIST);
            }

            applyTripOrderInfo = orderInfo;
            HoOrder order = hoOrderLoader.selectByOrderId(orderId);
            if (order != null) {
                addElkInfoLog("数据库存在该订单号，重复下单,请重新下单");
                throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_REPEAT_ERROR);
            }
            // 1.新增非员工
            // this.addNonEmployee(orderInfo);
            // 2.更新出行人
            //this.checkPassenger(orderInfo);
            // 3.消耗出差申请
            this.useApplyTrip(orderInfo, true);
            isRollbackApply = true;
            // 4.创建审批单
            CreateApprovalResponse approval = this.createApproval(orderInfo);
            if (Objects.nonNull(approval)) {
                orderInfo.setApprovalId(approval.getExternalId());
            }
            // 5.订单落库
            boolean saveOrderResult = saveOrder(orderInfo);
            if (!saveOrderResult) {
                throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_DATA_ERROR);
            }
            isDeleteOrder = true;
            // 该错误码前端会轮播获取订单结果
            saveOrderResult(orderId, HotelResponseCodeEnum.SAVE_ORDER_DOING_ERROR, responseVo);
            // 6.供应商下单
            //LocalBookOrderResponseBo bookResult = this.bookOrder(orderInfo, userInfo);
            StandardReserveOrderResponse bookResult = this.bookOrderNew(orderInfo);
            //保存供应商订单号

            // 同步wbs备注和成本中心备注
            List<ApplyTripUpdateRemakeRequest.ApplyTripUpdateRemake> updateRemakeRequestList = new ArrayList<>();
            orderInfo.getPassengerList().forEach(item -> {
                if (StringUtils.isNotBlank(item.getWbsRemark()) || StringUtils.isNotBlank(item.getCostCenterRemark())) {
                    ApplyTripUpdateRemakeRequest.ApplyTripUpdateRemake updateRemakeRequest =
                        new ApplyTripUpdateRemakeRequest.ApplyTripUpdateRemake(
                            StringUtils.isBlank(item.getUid()) ? item.getNoEmployeeId() : item.getUid(),
                            orderInfo.getApplyNo(), item.getWbsRemark(), item.getCostCenterRemark());
                    updateRemakeRequestList.add(updateRemakeRequest);
                }
            });
            if (CollectionUtils.isNotEmpty(updateRemakeRequestList)) {
                applyTripClientLoader
                    .updateApplyTripWbsAndCostRemake(new ApplyTripUpdateRemakeRequest(updateRemakeRequestList));
            }

            // 下单失败
            if (bookResult == null) {
                addElkInfoLog("供应商下单失败");
                throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_SUPPLIER_ERROR);
            }
            // 下单超时
            //if (bookResult.getTimeout()) {
            //    addElkInfoLog("供应商创单超时");
            //    // 下单超时置本订单为已取消
            //    isDeleteOrder = false;
            //    hoOrderLoader.updateOrderStatus(orderInfo.getOrderId(), null, OrderStatusEnum.CA.getCode());
            //    throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_CREATE_TIMEOUT_ERROR);
            //}
            addElkInfoLog("供应商创单成功：%s", JsonUtils.toJsonString(bookResult));
            hoOrderLoader.updateSupplierOrderId(orderId, bookResult.getOrderID());
            if (StringUtils.isNotBlank(bookResult.getOrderID())) {
                HoOrder updateHoOrder = new HoOrder();
                updateHoOrder.setSupplierOrderId(bookResult.getOrderID());
                updateHoOrder.setOrderId(orderInfo.getOrderId());
                hoOrderLoader.updateByOrderId(updateHoOrder);
                LOGGER.withTag(LogTagConstants.SUPPLIER_ORDER_ID, bookResult.getOrderID());
            }

            String paymentNo = bookResult.getPaymentNo();
            String payType = orderInfo.getPayType();
            // 需要个人支付的支付类型-》个付和混付
            List<String> needPersonPay = Lists.newArrayList(PayTypeEnum.PPAY.getType(), PayTypeEnum.MIXPAY.getType());


            // 携程会反支付流水号，目前其他供应商不会。
            // 1.存在支付单号支付方式为个付或者混付  2.存在个付或者混付且供应商code不为携程  3.存在支付单号且支付方式为前台现付且存在前收服务费
            if ((StringUtils.isNotBlank(paymentNo) && needPersonPay.contains(payType)) ||
                (needPersonPay.contains(payType) && !Objects.equals(orderInfo.getSupplierCode(), "ctrip")) ||
                (StringUtils.isNotBlank(paymentNo) && PayTypeEnum.CASH.getType().equals(payType) && Objects.nonNull(orderInfo.getTotalServiceCharge()) && orderInfo.getTotalServiceCharge().compareTo(BigDecimal.ZERO) > 0)) {
                addElkInfoLog("个人支付部分，创建支付单");
                this.updatePaymentNo(orderInfo.getOrderId(), paymentNo);
                HotelUpdateOrderStatusRequestBo updateRequest = new HotelUpdateOrderStatusRequestBo();
                updateRequest.setOrderId(orderInfo.getOrderId());
                updateRequest.setSupplierOrderId(bookResult.getOrderID());
                updateRequest.setOldOrderStatus(OrderStatusEnum.SI.getCode());
                updateRequest.setNewOrderStatus(OrderStatusEnum.PW.getCode());
                hotelPushService.updateOrderStatus(updateRequest);
                this.createPaymentBill(orderInfo, paymentNo);
            }

            // 7.个人支付跳转数据
            PayTokenInfo payTokenInfo = this.createPayTokenInfo(orderInfo);
            responseVo.setPayTokenInfo(payTokenInfo);

            // 8.保存订单用户信息
            String orgId = orderInfo.getOrgId();

            saveOrderUser(orderId, bookResult.getOrderID(), orderInfo.getUid(),
                orderInfo.getUname(),
                orderInfo.getSupplierUid(), orderInfo.getCorpId());

            // ctrip订单同步成本中心
            if (hotelApollo.isCtrip(orderInfo.getSupplierCode())) {
                syncOrderCostCenter(orderInfo, bookResult.getOrderID());
            }


            // 发布ocs创单事件
            log.info("创单成功，发送ocs创单事件，orderId:{}", orderId);
            eventCenter.post(
                new OrderCreateExternalEvent(ApplicationEnum.HOTEL_DOMESTIC.getCode(),
                    orderId));

            return saveOrderResult(orderId, HotelResponseCodeEnum.SUCCESS_CODE, responseVo);
        } catch (CorpBusinessException corpBusinessException) {
            addElkInfoLog(",业务异常:%s", corpBusinessException.getMsg());
            return saveOrderResult(orderId, HotelResponseCodeEnum.findByCode(corpBusinessException.getResultCode()),
                responseVo);
        } catch (Exception e) {
            LOGGER.error(TITLE, "saveOrder失败", e);
            addElkInfoLog(",产线下单失败"
                + String.format(",%s(%s)", StringUtils.isBlank(e.getMessage()) ? "空指针异常" : e.getMessage(), "-1"));
            return saveOrderResult(orderId, HotelResponseCodeEnum.HOTELINTL_EXCEPTION_ERROR, responseVo);
        } finally {
            if (!String.valueOf(HotelResponseCodeEnum.SUCCESS_CODE.code()).equals(responseVo.getErrorCode())) {
                if (isDeleteOrder) {
                    // 删除订单
                    this.deleteOrder(request.getOrderId());
                }
                if (isRollbackApply) {
                    // 回滚出差申请单
                    this.useApplyTrip(applyTripOrderInfo, false);
                }
            }

            LOGGER.info(TITLE, "国内酒店下单接口日志：{}{}", System.lineSeparator(), this.getElkInfoLog());
            lock.unlock();
            clearElkLog();
            LOGGER.clearTags();
        }
    }

    private boolean riskControlCheck(OrderInfoModel orderInfo) {
        String corpPayType = orderInfo.getCorpPayType();
        if (PubOwnEnum.PUB.name().equals(corpPayType)) {
            log.info("风控校验-因公，直接返回true");
            return true;
        }

        if (StringUtils.isBlank(orderInfo.getTravelStandardToken())) {
            log.info("风控校验-不存在token ，直接返回true");
            return true;
        }

        String moveOrderRiskControlResult =
            coreServiceClientLoader.getMoveOrderRiskControlResult(orderInfo.getTravelStandardToken());
        if (StringUtils.isBlank(moveOrderRiskControlResult)) { // 无响应直接通过
            metricService.metricOrderRiskControl(orderInfo.getOrderId(), moveOrderRiskControlResult);
            return true;
        }
        if (RiskControlStatusEnum.REJECTED.name().equals(moveOrderRiskControlResult)) {
            return false;
        }

        if (RiskControlStatusEnum.IN_PROCESS.name().equals(moveOrderRiskControlResult) ||
            RiskControlStatusEnum.ERROR.name().equals(moveOrderRiskControlResult)) {// 埋点
            metricService.metricOrderRiskControl(orderInfo.getOrderId(), moveOrderRiskControlResult);
        }
        return true;
    }

    /**
     * 差标校验
     */
    private void travelStandardCheck(OrderInfoModel orderInfo) {
        boolean legitimacy = travelStandardLegitimacyCheck(orderInfo);
        if(!legitimacy){
            log.info("++++++校验差旅合法性, 不校验， standardExceedCheckSwitch:{}, ", JsonUtils.toJsonString(standardExceedCheckSwitch));
            metricService.metricsStandardLegitimacyCheck(false); // 埋点
            // 开关判断,是否拦截
            if(standardExceedCheckSwitch.standardValidateCheckFlag()){
                throw new CorpBusinessException(HotelResponseCodeEnum.ILLEGAL_TRAVEL_STANDARD);
            }
        }

        boolean checkExceed = travelExceed(orderInfo);
        if(!checkExceed){
            log.error("++++++++ 超标检查不通过, orderInfo:{}", orderInfo.getTravelStandardToken());
            metricService.metricsOrderCheckStandard(false); // 埋点
            if(standardExceedCheckSwitch.standardExceedCheckFlag()){
                throw new CorpBusinessException(HotelResponseCodeEnum.ORDER_EXCEED_STANDARD);
            }
        }
    }

    /**
     * 校验差旅合法性
     * 如果没有差标，或者不包含阶梯差旅
     * 注意： 进来肯定是因公场景
     */
    private boolean travelStandardLegitimacyCheck(OrderInfoModel orderInfo) {
        log.info("++++++校验差旅合法性, orderInfo:{}, ", JsonUtils.toJsonString(orderInfo));
        try{
            // 非因公
            if (!PubOwnEnum.PUB.name().equals(orderInfo.getCorpPayType())) {
                log.info("++++++校验差旅合法性, 因私，直接返回true, corpPayType:{}", orderInfo.getCorpPayType());
                return true;
            }

            // 租户判断
            if(!standardExceedCheckSwitch.standardValidateCheckTenant().equals(TenantContext.getTenantId())){
                log.info("++++++校验差旅合法性, 租户不匹配， standardExceedCheckSwitch:{}", JsonUtils.toJsonString(standardExceedCheckSwitch));
                return true;
            }

            // 老页面直接返回true
            if(StringUtils.isBlank(orderInfo.getTravelStandardToken())){
                log.info("++++++++ 老页面，没有token，直接返回true");
                return true;
            }

            // 非国内酒店，直接返回true
            if(!BizTypeEnum.HOTEL.name().equals(orderInfo.getProductType())){
                log.info("++++++++ 非国际酒店，直接返回true, productType:{}", orderInfo.getProductType());
                return true;
            }

            // 紧急预定，直接返回true
            BookingConfig bookingConfig = getBookingConfigByToken(orderInfo);
            if(bookingConfig.isUrgentEnable()){
                log.info("++++++++ 紧急预定，直接返回true, bookingConfig:{}", JsonUtils.toJsonString(bookingConfig));
                return true;
            }

            // 申请单不管控
            if(bookingConfig.getAttrManagementControlValues().contains(ATTR_MANAGEMENT_CONTROL_NOT_ENABLED)){
                log.info("++++++++ 申请单不管控，直接返回false, 徐工申请单不管控是个异常场景, bookingConfig:{}", JsonUtils.toJsonString(bookingConfig));
                return false;
            }

            // 通过token 查询差标
            GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
            getTravelStandardByTokenRequest.setTokenList(Arrays.asList(orderInfo.getTravelStandardToken()));
            List<TravelStandardResponseBO> travelStandardResponseList = applyTripClientLoader.getTravelStandardByToken(getTravelStandardByTokenRequest);
            log.info("+++++++ getTravelStandardByToken getTravelStandardByTokenRequest:{},travelStandardResponseList:{}", JsonUtils.toJsonString(getTravelStandardByTokenRequest), JsonUtils.toJsonString(travelStandardResponseList));

            // 没有差标返回false
            if(CollectionUtils.isEmpty(travelStandardResponseList)){
                log.info("+++++++ 没有差标，直接返回false...., token:{}", orderInfo.getTravelStandardToken());
                return false;
            }

            // 只要有一个定制差标，就返回true
            for(TravelStandardResponseBO travelStandardResponseBO : travelStandardResponseList){
                TravelStandardTokenResponse tokenInfo = travelStandardResponseBO.getTravelStandardToken();
                if(tokenInfo  != null && tokenInfo.getOwnerType() != null){
                    if(TravelStandardOwnerTypeEnum.CUSTOMIZED.getCode() == tokenInfo.getOwnerType()){
                        return true;
                    }
                }
            }
            log.info("+++++++ 没有定制差标，直接返回false...., token:{}", orderInfo.getTravelStandardToken());
            return false;

        }catch (Exception e){
            log.error("++++++校验差旅合法性, 异常, orderInfo:{}, ", JsonUtils.toJsonString(orderInfo), e);
            return true;
        }
    }

    /**
     * 背景：@see <a href="https://wiki.corp.ctripcorp.com/pages/viewpage.action?pageId=*********">bug链接</a>
     * 校验出差申请单管控
     * 1.获取配置
     * 2.校验
     *
     * @param orderInfo 订单缓存信息
     * @return 是否通过 true:通过 false:不通过
     */
    private boolean checkApplicationFormControl(OrderInfoModel orderInfo) {
        // 旧数据不校验
        if (StringUtils.isBlank(orderInfo.getTravelStandardToken())) {
            return true;
        }
        // 因私不校验
        if (TravelModeEnum.OWN.getCode().equals(orderInfo.getCorpPayType())) {
            return true;
        }
        try {
            BookingConfig bookingConfig = getBookingConfigByToken(orderInfo);
            // 出差申请管控方式 1 - 严格管控  3 - 不管控, 不管控直接放
            if (bookingConfig.getAttrManagementControlValues().contains(ATTR_MANAGEMENT_CONTROL_NOT_ENABLED)) {
                return true;
            }
            // 如果配置为紧急预订 订单是紧急预订 则通过
            if(bookingConfig.isUrgentEnable()){
                return true;
            }
            if (!bookingConfig.getProductControlValues().contains(ATTR_PRODUCT_CONTROL_VALUE)) {
                return true;
            }
            String applyNo = orderInfo.getApplyNo();
            Long trafficId = orderInfo.getTrafficId();
            return StringUtils.isNotBlank(applyNo) && Objects.nonNull(trafficId);
        } catch (CorpBusinessException e) { // 业务异常 直接抛出
            throw e;
        } catch (Exception e) { // 异常不影响下单 默认通过
            log.error("校验出差申请单管控异常", e);
        }
        return true;
    }


    private BookingConfig getBookingConfigByToken(OrderInfoModel orderInfo) {
        log.info("++++++++++++ getBookingConfigByToken, orderInfo:{}", JsonUtils.toJsonString(orderInfo));
        String token = orderInfo.getTravelStandardToken();
        if(StringUtils.isBlank(token)){
            log.error("getBookingConfigByToken, token is null");
            return null;
        }
        // 请求获取配置
        GetBookingConfigByTokenRequest request = new GetBookingConfigByTokenRequest();
        request.setToken(token);
        JSONResult<GetBookingConfigByTokenResponse> bookingConfigByTokenResponseJSONResult =
                coreServiceClient.getBookingConfigByToken(request);

        // 配置获取异常 抛出异常
        boolean requestConfigFailed = Objects.isNull(bookingConfigByTokenResponseJSONResult)
                || !bookingConfigByTokenResponseJSONResult.isSUCCESS();
        if (requestConfigFailed) {
            throw new CorpBusinessException(HotelResponseCodeEnum.GET_APPLY_TRIP_CONTROL_CONFIG_ERROR);
        }

        GetBookingConfigByTokenResponse bookingConfigByTokenResponse =
                bookingConfigByTokenResponseJSONResult.getData();
        if (Objects.isNull(bookingConfigByTokenResponse)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.GET_APPLY_TRIP_CONTROL_CONFIG_ERROR);
        }

        AllSwitchDTO allSwitch = bookingConfigByTokenResponse.getAllSwitch();
        if (Objects.isNull(allSwitch)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.GET_APPLY_TRIP_CONTROL_CONFIG_ERROR);
        }

        Map<String, SwitchDTO> switchInfoSoaMap = allSwitch.getSwitchInfoSoaMap();
        if (CollectionUtils.isEmpty(switchInfoSoaMap)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.GET_APPLY_TRIP_CONTROL_CONFIG_ERROR);
        }

        SwitchDTO attrManagementControl = switchInfoSoaMap.get(ATTR_MANAGEMENT_CONTROL);
        if (Objects.isNull(attrManagementControl) || StringUtils.isBlank(attrManagementControl.getValue())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.GET_APPLY_TRIP_CONTROL_CONFIG_ERROR);
        }

        BookingConfig bookingConfig = new BookingConfig();

        // 出差申请管控方式 1 - 严格管控  3 - 不管控
        List<String> attrManagementControlValues = JsonUtils.parseArray(attrManagementControl.getValue(), String.class);
        bookingConfig.setAttrManagementControlValues(attrManagementControlValues);

        // 支持紧急预定 且是 紧急预定
        Boolean urgentEnable = allSwitch.getUrgentEnable();
        if (Objects.nonNull(urgentEnable) && urgentEnable) {
            Boolean urgentApply = orderInfo.getUrgentApply();
            if (Objects.nonNull(urgentApply) && urgentApply) {
                bookingConfig.setUrgentEnable(true);
            }
        }


        SwitchDTO attrProductControl = switchInfoSoaMap.get(ATTR_PRODUCT_CONTROL);
        if (Objects.isNull(attrProductControl) || StringUtils.isBlank(attrProductControl.getValue())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.GET_APPLY_TRIP_CONTROL_CONFIG_ERROR);
        }

        //产线管控方式  1-国内机票 2-国内酒店 3-国内火车 4-国内打车 5-国际机票 6-国际酒店 7-国内接送机 8-国内接送站 9-租车
        List<String> productControlValues = JsonUtils.parseArray(attrProductControl.getValue(), String.class);
        bookingConfig.setProductControlValues(productControlValues);
        log.info("+++++++++ getBookingConfigByToken, token:{}, bookingConfig:{}", token, JsonUtils.toJsonString(bookingConfig));
        return bookingConfig;
    }


    /**
     * 检查重复预订
     * 此方法用于检查预定类型为PUB的订单是否重复预订，根据入住日期、离店日期和入住人信息进行检查
     * 如果是其他预定类型，则直接返回不进行检查
     *
     * @param orderInfoModel 订单信息
     */
    private void checkRepeatBooking(OrderInfoModel orderInfoModel) {
        // 如果预定类型不是PUB，则不进行重复预订检查，直接返回
        if (!PubOwnEnum.PUB.name().equals(orderInfoModel.getCorpPayType())) {
            return;
        }
        // 记录检查重复预订的日志
        addElkInfoLog("checkRepeatBooking...");

        CheckDuplicateBookingRequest bookingRequest = new CheckDuplicateBookingRequest();
        // 设置入住日期
        bookingRequest
            .setCheckInDate(DateUtil.stringToDate(orderInfoModel.getRoomInfo().getCheckInDate(), DateUtil.DF_YMD));
        // 设置离店日期
        bookingRequest
            .setCheckOutDate(DateUtil.stringToDate(orderInfoModel.getRoomInfo().getCheckOutDate(), DateUtil.DF_YMD));
        // 设置预定类型为PUB
        bookingRequest.setTravelMode(PubOwnEnum.PUB.name());
        // 转换入住人信息
        bookingRequest.setPassengers(orderInfoModel.getPassengerList().stream().map(p -> {
            CheckDuplicateBookingRequest.Passenger passenger = new CheckDuplicateBookingRequest.Passenger();
            passenger.setUid(p.getUid());
            passenger.setNonEmployeeId(p.getNoEmployeeId());
            Integer employeeType = p.getEmployeeType();
            // 如果员工类型为空，则根据UID进行判断并设置默认值
            if (employeeType == null) {
                employeeType = StringUtils.isNotBlank(p.getUid()) ? ApplyTripEmployeeEnum.EMPLOYEE.getCode()
                    : ApplyTripEmployeeEnum.EXTERNAL_EMPLOYEE.getCode();
            }
            passenger.setEmployeeType(employeeType);
            return passenger;
        }).collect(Collectors.toList()));

        // 调用检查是否重复预订
        CheckDuplicateBookingResponse bookingResponse = checkOrderService.checkDuplicateBookingV3(bookingRequest);
        // 记录检查结果的日志
        addElkInfoLog("checkRepeatBooking bookingResponse: %s", JsonUtils.toJsonString(bookingResponse));
        // 数据异常
        if (bookingResponse == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_GET_REPEAT_ORDER_ERROR);
        }

        // 如果管理端配置重复则不可预定，并且检查结果为重复预订，则抛出异常
        if (DuplicateCheckModeEnum.NO_RESERVATION.getCode().equalsIgnoreCase(bookingResponse.getDuplicateCheckMode())
            && BooleanUtils.isTrue(bookingResponse.isDuplicate())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.SAVE_ORDER_REPEAT_BOOKING);
        }
    }

    /**
     * ctrip订单同步成本中心
     *
     * @param orderInfo
     * @param supplierOrderId
     */
    private void syncOrderCostCenter(OrderInfoModel orderInfo, String supplierOrderId) {
        try {
            OrderUserInfoRsp orderUser = commonOrderCenterDataloader.getOrderUser(orderInfo.getOrderId().toString());
            if (null == orderUser) {
                throw new IllegalArgumentException(String.format("查询订单[%s]下单用户信息失败", orderInfo.getOrderId()));
            }
            CtripSyncOrderCostCenterRequest request = syncOrderCostCenterConverter.toSyncOrderCostCenterRequest(orderInfo, supplierOrderId, orderUser);
            request.setCorpID(orderInfo.getCorpId());
            request.setSupplierCode(orderInfo.getSupplierCode());
            request.setCorpPayType(orderInfo.getCorpPayType());
            if (CollectionUtils.isNotEmpty(request.getOrderList())) {
                CtripSyncOrderCostCenterResponse response = commonSupplierServiceNew.syncOrderCostCenter(request);
                if (!Objects.equals(Boolean.TRUE, response.getStatus().getSuccess()) && CollectionUtils.isNotEmpty(response.getSavedOrderCostCenterResultList())) {
                    response.getSavedOrderCostCenterResultList().forEach(item -> {
                        log.error("syncOrderCostCenter error - orderId[{}], errMsg[{}, {}]", item.getOrderID(), item.getErrorCode(), item.getErrorMsg());
                    });
                }
            }
        } catch (Exception e) {
            log.error("syncOrderCostCenter error:", e);
        }
    }

    /**
     * 保存订单结果
     *
     * @param orderId
     * @param saveOrderCreateError
     * @param saveOrderResponseVo
     * @return
     */
    private SaveOrderResponseVo saveOrderResult(Long orderId, HotelResponseCodeEnum saveOrderCreateError,
        SaveOrderResponseVo saveOrderResponseVo) {
        saveOrderResponseVo.setErrorCode(String.valueOf(saveOrderCreateError.code()));
        saveOrderResponseVo.setErrorMsg(saveOrderCreateError.message());
        orderInfoCacheManager.setSaveOrderResult(orderId, saveOrderResponseVo);
        return saveOrderResponseVo;
    }

    /**
     * 保存订单用户信息
     *
     * @param orderId 订单号
     * @param supplierOrderId 分销订单号
     * @param platUid 用户uid
     * @param userName 用户名称
     * @param supplierUid 分销公司默认配置的uid
     */
    private void saveOrderUser(Long orderId, String supplierOrderId, String platUid,
        String userName, String supplierUid, String corpId) {
        try {
            if (StringUtils.isEmpty(supplierOrderId)) {
                return;
            }
            LOGGER.info(TITLE, "saveorderUser,orderId:{},supplierOrderId:{},platUid:{},userName:{},supplierUid:{}, corpId:{}",
                orderId, supplierOrderId, platUid, userName, supplierUid, corpId);
            // 订单用户关联表
            GetEmployeeOpenCardReq getEmployeeOpenCardReq = new GetEmployeeOpenCardReq();
            getEmployeeOpenCardReq.setUid(platUid);
            getEmployeeOpenCardReq.setCorpId(corpId);
            GetEmployeeOpenCardRsp employeeOpenCardInfo =
                commonOrganizationDataloader.getEmployeeOpenCardInfo(getEmployeeOpenCardReq);
            // 开卡使用绑定的分销uid,未开卡使用默认配置的分销uid
            String finalSupplierUid =
                Optional.ofNullable(employeeOpenCardInfo).map(GetEmployeeOpenCardRsp::getSupplierUid).isPresent()
                    ? employeeOpenCardInfo.getSupplierUid() : supplierUid;
            // 分销和我们自己的订单号都需要存储一份数据
            orderUserClientLoader.save(orderId, platUid, finalSupplierUid, userName);
            orderUserClientLoader.save(Long.parseLong(supplierOrderId), platUid, finalSupplierUid, userName);
        } catch (Exception e) {
            LOGGER.error(TITLE, "saveOrderUser异常", e);
        }
    }

    /**
     * 创建支付单
     *
     * @param orderInfo
     */
    private void createPaymentBill(OrderInfoModel orderInfo, String payNo) {
        addElkInfoLog("进入创支付单流程");
        boolean needPersonPay = Objects.equals(orderInfo.getPayType(), PayTypeEnum.PPAY.getType())
            || Objects.equals(orderInfo.getPayType(), PayTypeEnum.MIXPAY.getType());
        // 前台现付且存在前收服务费
        boolean cashAndHasServiceFee = Objects.equals(orderInfo.getPayType(), PayTypeEnum.CASH.getType()) && Objects.nonNull(orderInfo.getTotalServiceCharge()) && orderInfo.getTotalServiceCharge().compareTo(BigDecimal.ZERO) > 0;
        if (!needPersonPay && !cashAndHasServiceFee) {
            addElkInfoLog("非个付无需传支付单");
            return;
        }
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderInfo.getOrderId());
        CreatePaymentBillRequest createPaymentBillRequest = new CreatePaymentBillRequest();
        createPaymentBillRequest.setOrderId(orderInfo.getOrderId());
        createPaymentBillRequest.setSerialNo(payNo);
        // 混付的情况下创建个人支付部分的支付单
        if (PayTypeEnum.MIXPAY.getType().equalsIgnoreCase(hoOrder.getPaytype())) {
            createPaymentBillRequest.setAmount(hoOrder.getPPayAmount());
            createPaymentBillRequest.setPayType(PayTypeEnum.PPAY.getType());
        } else if (PayTypeEnum.PPAY.getType().equalsIgnoreCase(hoOrder.getPaytype())) {
            createPaymentBillRequest.setAmount(hoOrder.getAmount());
            createPaymentBillRequest.setPayType(orderInfo.getPayType());
        } else {
            createPaymentBillRequest.setAmount(orderInfo.getTotalServiceCharge());
            createPaymentBillRequest.setPayType(PayTypeEnum.PPAY.getType());
        }
        createPaymentBillRequest.setOrderType(OrderTypeEnum.HN.getType());
        createPaymentBillRequest.setRefundFlag(false);
        createPaymentBillRequest.setUid(orderInfo.getUid());
        createPaymentBillRequest.setUserName(orderInfo.getUname());
        createPaymentBillRequest.setCorpPayType(orderInfo.getCorpPayType());
        CreatePaymentBillResponse result = payClientLoader.createPaymentBillRequest(createPaymentBillRequest);
        addElkInfoLog("支付单创建结果：%s", JsonUtils.toJsonString(result));
    }

    /**
     * 删除订单
     *
     * @param orderId
     */
    private void deleteOrder(Long orderId) {
        addElkInfoLog("删除订单");
        saveOrderProductService.updateOrder(UpdateOrderRequestBo.deleted(orderId));
    }

    /**
     * 创建审批流程
     *
     * @param orderInfo
     * @return
     */
    private CreateApprovalResponse createApproval(OrderInfoModel orderInfo) {
        addElkInfoLog("进入审批流程");
        Boolean createApproval = orderInfo.getCreateApproval();
        FlowDetail flowDetail = orderInfo.getFlowDetail();
        String payType = orderInfo.getPayType();
        if (!createApproval || !isRequiredApproval(payType, flowDetail)) {
            addElkInfoLog("为匹配审批流程，不创建审批流程");
            return null;
        }
        if (Objects.isNull(flowDetail)) {
            return null;
        }
        if (flowDetail.getFlowType() == 1) {
            return null;
        }
        CreateApprovalRequest approvalRequest = new CreateApprovalRequest();

        // 获取当前登录人信息
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        OrgEmployeeVo employeeInfo =
            organizationEmployeeClientLoader.findEmployeeInfo(baseUserInfo.getUid(), baseUserInfo.getOrgId());
        if (Objects.isNull(employeeInfo)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_CREATE_THE_APPROVAL_ORDER);
        }
        SubmitterInfo submitter = new SubmitterInfo();
        submitter.setSubmitterId(baseUserInfo.getUid());
        submitter.setSubmitterName(baseUserInfo.getUserName());
        submitter.setSubmitterOrgId(baseUserInfo.getOrgId());
        submitter.setSubmitterOrgName(baseUserInfo.getOrgName());
        submitter.setMobilePhone(employeeInfo.getAreaCode() + " " + employeeInfo.getMobilePhone());
        flowDetail.setSubmitter(submitter);
        flowDetail.setFeedbackPersonTypes(Lists.newArrayList(1, 2));
        flowDetail.setTravelers(orderInfo.getPassengerList().stream().map(e -> {
            TraverlerInfo traverlerInfo = new TraverlerInfo();
            traverlerInfo.setTraverlerId(e.getCorp() ? e.getUid() : e.getNoEmployeeId());
            traverlerInfo.setMobilePhone(e.getCountryCode() + " " + e.getMobilePhone());
            traverlerInfo.setOrgId(e.getOrgId());
            traverlerInfo.setOrgName(e.getOrgName());
            traverlerInfo.setTraverlerName(e.getName());
            traverlerInfo.setEmployee(e.getCorp());
            Optional.ofNullable(orderInfo.getCostCenter()).map(CostCenter::getCostCenterCode)
                .ifPresent(traverlerInfo::setCostCenterId);
            traverlerInfo.setOrgId(e.getOrgId());
            return traverlerInfo;
        }).collect(Collectors.toList()));
        approvalRequest.setFlowDetail(flowDetail);
        approvalRequest.setBusinessType(ApprovalTypeEnum.H.getCode());
        approvalRequest.setBusinessId(String.valueOf(orderInfo.getOrderId()));
        CreateApprovalResponse approval = approvalSystemService.createApproval(approvalRequest);
        if (null == approval || StringUtils.isBlank(approval.getExternalId())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_CREATE_THE_APPROVAL_ORDER);
        }
        return approval;
    }

    /**
     * FlowTmplPayTypeEnum
     * 1.支付方式=不限时，代表无论公司账户还是个人支付的预订单以及机票改签单，都符合条规则，然后按照规则配置执行对应的流程
     * <p>
     * 2.支付方式=公司账户时，代表仅公司账户支付的预订单以及机票改签单， 才符合该规则，然后按照规则配置执行对应的流程
     * <p>
     * 3.支付方式=个人账户时，代表仅个人支付的预订单以及机票改签单，才符合该规则，然后按照规则配置执行对应的流程
     * <p>
     * 若找不到对应支付方式的规则， 则代表该支付方式的预订单/机票改签单，无需审批
     */
    private boolean isRequiredApproval(String paytype, FlowDetail approvalFlowDetail) {
        metricService.metricApprovalFlowDetail(approvalFlowDetail);
        LOGGER.info(TITLE, "判断是否需要审批，支付方式[{}],approvalFlowDetail:{}", paytype,
            JsonUtils.toJsonString(approvalFlowDetail));
        // 审批流为空
        if (approvalFlowDetail == null) {
            return false;
        }
        // 未支付方式为空
        List<String> payTypeEnums = approvalFlowDetail.getPayTypeEnums();
        if (CollectionUtils.isEmpty(payTypeEnums)) {
            return false;
        }
        // 支付方式为不限制时，返回true
        if (payTypeEnums.contains(FlowTmplPayTypeEnum.ALL.getType())) {
            return true;
        }
        // 配置支付方式与订单支付方式一致时，返回true
        if (payTypeEnums.contains(paytype)) {
            return true;
        }
        // 支付类型是个人支付 且 是前台现付单据也需要审批
        if (payTypeEnums.contains(FlowTmplPayTypeEnum.PPAY.getType())
            && PrePayTypeWayEnum.CASH.getType().equals(paytype)) {
            return true;
        }
        return false;
    }

    /**
     * 添加非员工
     */
    private void addNonEmployee(OrderInfoModel orderInfo) {
        addElkInfoLog("进入新增非员工流程");
        String uid = orderInfo.getUid();
        List<PassengerInfo> passengerList = orderInfo.getPassengerList();
        List<PassengerInfo> nonEmployeeList =
            passengerList.stream().filter(e -> e.getNoEmployeeId() != null && e.getNoEmployeeId().startsWith("add_"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nonEmployeeList)) {
            addElkInfoLog("没有非员工，无需添加");
            return;
        }
        for (PassengerInfo nonEmployee : nonEmployeeList) {
            OrgNonEmployeeVo vo = new OrgNonEmployeeVo();
            vo.setName(nonEmployee.getName());
            if (StringUtils.isNotBlank(nonEmployee.getMobilePhone())) {
                vo.setMobilePhone("+86 " + nonEmployee.getMobilePhone());
            }
            vo.setEmployeeUid(uid);
            vo.setSurname(nonEmployee.getSurname());
            vo.setGivenname(nonEmployee.getGivenname());
            vo.setFullName(nonEmployee.getFullName());
            vo.setFullEnName(nonEmployee.getFullEnName());
            Long result = organizationNonEmployeeClientLoader.save(vo);
            nonEmployee.setNoEmployeeId(String.valueOf(result));
        }
    }

    private PayTokenInfo createPayTokenInfo(OrderInfoModel orderInfo) {
        // 前台现付且存在前收服务费
        boolean cashAndHasServiceFee = Objects.equals(orderInfo.getPayType(), PayTypeEnum.CASH.getType()) && Objects.nonNull(orderInfo.getTotalServiceCharge()) && orderInfo.getTotalServiceCharge().compareTo(BigDecimal.ZERO) > 0;
        if (!Objects.equals(PayTypeEnum.PPAY.getType(), orderInfo.getPayType())
            && !Objects.equals(PayTypeEnum.MIXPAY.getType(), orderInfo.getPayType())
            && !cashAndHasServiceFee) {
            return null;
        }
        PayTokenInfo payTokenInfo = new PayTokenInfo();
        payTokenInfo.setOrderId(orderInfo.getOrderId().toString());
        String subject =
            String.format("%s %s", orderInfo.getHotelInfo().getCityName(), orderInfo.getHotelInfo().getHotelName());
        payTokenInfo.setSubject(subject);
        String context = String.format("入住时间：%s 离店时间：%s", orderInfo.getRoomInfo().getCheckInDate(),
            orderInfo.getRoomInfo().getCheckOutDate());
        payTokenInfo.setGoods(context);
        String userNames = orderInfo.getPassengerList().stream().map(OrderInfoModel.PassengerInfo::getName)
            .collect(Collectors.joining("，"));
        payTokenInfo.setUserNames(userNames);
        // 支付订单title
        payTokenInfo.setTitle(subject);
        PayInfoRequest payInfoRequest = new PayInfoRequest();
        payInfoRequest.setTransport("hotel");
        payInfoRequest.setName(orderInfo.getCorpPayType());
        // todo 支付方式修改
        // payInfoRequest.setUId(orderInfo.getUid());
        // //无原单支付方式时，走默认支付方式
        // if (CollectionUtils.isEmpty(payTokenInfo.getPayKeyList())) {
        // PayInfoResponse userPayInfo = switchSoaService.getUserPayInfo(payInfoRequest);
        // if (userPayInfo != null) {
        // payTokenInfo.setPayKeyList(userPayInfo.getType());
        // }
        // }
        return PPayUtil.createPayTokenInfo(payTokenInfo);
    }

    private void updateOrderStatus(UpdateOrderRequestBo requestBo, Long orderId) {
        requestBo.setOrderId(orderId);
        addElkInfoLog("更新订单信息，requestBo：%s", requestBo);
        saveOrderProductService.updateOrder(requestBo);
    }

    /**
     * 供应商创单流程
     *
     * @param orderInfo
     * @param userInfo
     * @return
     */
    private LocalBookOrderResponseBo bookOrder(OrderInfoModel orderInfo, UserInfo userInfo) {
        addElkInfoLog("进入供应商下单流程orderInfo: %s", JsonUtils.toJsonString(orderInfo));

        Integer guestPerson = orderInfo.getRoomInfo().getGuestPerson();
        Integer bookRoomNum = orderInfo.getRoomInfo().getQuantity();
        Integer maxPerson = bookRoomNum * guestPerson;
        if (orderInfo.getPassengerList().size() > maxPerson) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_ORDER, guestPerson);
        }

        LocalBookOrderRequestBo requestBo = new LocalBookOrderRequestBo();
        requestBo.setProductId(orderInfo.getProductId());
        OrderInfoModel.HotelInfo hotelInfo = orderInfo.getHotelInfo();
        boolean hongKongMacaoTaiWan = Optional.ofNullable(hotelInfo.getHongKongMacaoTaiwan()).orElse(false);
        requestBo.setCheckCode(orderInfo.getCheckCode());
        requestBo.setSupplierCorpId(orderInfo.getSupplierCorpId());
        requestBo.setUid(orderInfo.getUid());
        requestBo.setOrgId(orderInfo.getCorpId());
        requestBo.setCorpId(orderInfo.getCorpId());
        requestBo.setSupplierUid(orderInfo.getSupplierUid());
        requestBo.setSupplierCode(orderInfo.getSupplierCode());
        // requestBo.setCorpOrderInfo(new LocalBookOrderRequestBo.CorpOrderInfo(orderInfo.getPayType()));
        requestBo.setClientList(orderInfo.getPassengerList().stream().map(e -> {
            // 获取传递给分销的房客名
            ClientInfo clientInfo = new ClientInfo();
            if(StringUtils.isNotBlank(e.getTravelerName())){
                clientInfo.setName(e.getTravelerName());
            }else{
                clientInfo.setName(getPassengerName(e, hongKongMacaoTaiWan));
            }
            // uid目前只有美亚使用，该字段携程用来处理商旅卡号
            // 后续通知美亚换字段使用 切换完成后去除该字段
            if (!"ctrip".equals(orderInfo.getSupplierCode())) {
                clientInfo.setUid(e.getUid());
                clientInfo.setMobilePhone(e.getMobilePhone());
                clientInfo.setCountryCode(e.getCountryCode());
            } else {
                if (Optional.ofNullable(e.getIsSendSms()).orElse(false)) {
                    clientInfo.setMobilePhone(e.getMobilePhone());
                    clientInfo.setCountryCode(e.getCountryCode());
                }
            }
            clientInfo.setClientUid(e.getUid());
            OrgEmployeeVo employeeInfo = organizationEmployeeClientLoader.findEmployeeInfo(e.getUid(), e.getOrgId());
            if (employeeInfo != null) {
                clientInfo.setCorpId(employeeInfo.getRecentCompanyId());
                clientInfo.setCorpName(employeeInfo.getRecentCompanyName());
                clientInfo.setDeptId(employeeInfo.getOrgId());
                clientInfo.setDeptName(employeeInfo.getOrgName());
            }

            // 入住人是否获得积分
            if (BooleanUtils.isTrue(orderInfo.getEnabledBonusPoint())
                && e.getMobilePhone().equals(orderInfo.getContactInfo().getMobilePhone())) {
                clientInfo.setEarnPoints(true);
                clientInfo.setMobilePhone(e.getMobilePhone());
                clientInfo.setCountryCode(e.getCountryCode());
            }
            clientInfo.setRoomIndex(e.getRoomIndex());
            return clientInfo;
        }).collect(Collectors.toList()));
        requestBo.setBaseInfo(new LocalBookOrderRequestBo.CreateBaseInfo(orderInfo.getWsId()));
        LocalBookOrderRequestBo.ContactInfo contactInfo = new LocalBookOrderRequestBo.ContactInfo();
        OrderInfoModel.ContactInfo orderInfoContactInfo = orderInfo.getContactInfo();
        BeanUtils.copyProperties(orderInfoContactInfo, contactInfo);
        requestBo.setContactInfo(contactInfo);
        OrderInfoModel.PriceInfo orderInfoPriceInfo = orderInfo.getPriceInfo();
        LocalBookOrderRequestBo.PriceInfo priceInfo = new LocalBookOrderRequestBo.PriceInfo();
        BeanUtils.copyProperties(orderInfoPriceInfo, priceInfo);
        List<LocalBookOrderRequestBo.ServiceFeeInfo> serviceFeeInfoList = Lists.newArrayList();
        orderInfo.getRoomDailyInfoList().forEach(e -> {
            List<OrderInfoModel.ServiceFeeInfo> serviceFeeList = e.getServiceFeeList();
            if (CollectionUtils.isNotEmpty(serviceFeeList)) {
                serviceFeeInfoList.addAll(serviceFeeList.stream().map(s -> {
                    LocalBookOrderRequestBo.ServiceFeeInfo serviceFeeInfo =
                        new LocalBookOrderRequestBo.ServiceFeeInfo();
                    serviceFeeInfo.setPrice(s.getPrice());
                    serviceFeeInfo.setEffectDate(s.getEffectDate());
                    serviceFeeInfo.setType(s.getType());
                    return serviceFeeInfo;
                }).collect(Collectors.toList()));
            }
        });
        priceInfo.setServiceFeeList(serviceFeeInfoList);
        // 总价格加上服务费
        priceInfo.setCalculateAmount(orderInfo.getTotalAmount());
        priceInfo.setCalculateServiceFee(orderInfo.getTotalServiceFee());
        requestBo.setPriceInfo(priceInfo);

        OrderInfoModel.RemarkInfo orderInfoRemarkInfo = orderInfo.getRemarkInfo();
        LocalBookOrderRequestBo.RemarkInfo remarkInfo =
            JsonUtils.convert(orderInfoRemarkInfo, LocalBookOrderRequestBo.RemarkInfo.class);
        if (remarkInfo != null && remarkInfo.getOptionalRemarkList() != null) {
            List<String> remarkKeys = orderInfo.getRemarkKeys();
            if (CollectionUtils.isNotEmpty(remarkKeys)) {
                List<LocalOptionalRemark> selectRemarkList = remarkInfo.getOptionalRemarkList().stream()
                    .filter(e -> remarkKeys.contains(e.getKey())).collect(Collectors.toList());
                remarkInfo.setOptionalRemarkList(selectRemarkList);
            } else {
                remarkInfo.setOptionalRemarkList(null);
            }
        }
        requestBo.setRemarkInfo(remarkInfo);
        LocalBookOrderRequestBo.CreateRoomInfo roomInfo = new LocalBookOrderRequestBo.CreateRoomInfo();
        roomInfo.setGuestQuantity(orderInfo.getRoomInfo().getPersonCount());
        roomInfo.setRoomQuantity(orderInfo.getRoomInfo().getQuantity());
        roomInfo.setHotelId(orderInfo.getRoomInfo().getHotelId());
        roomInfo.setRoomId(orderInfo.getRoomInfo().getRoomId());
        roomInfo.setCheckInDate(orderInfo.getRoomInfo().getCheckInDate());
        roomInfo.setCheckOutDate(orderInfo.getRoomInfo().getCheckOutDate());
        roomInfo.setCityId(orderInfo.getRoomInfo().getSupplierCityId());
        requestBo.setRoomInfo(roomInfo);
        LocalBookOrderRequestBo.ExtInfo extInfo = new LocalBookOrderRequestBo.ExtInfo();
        extInfo.setPlatformOrderId(String.valueOf(orderInfo.getOrderId()));
        requestBo.setPlatformSource(StringUtils.isNotBlank(orderInfo.getAgentUid()) ? "Agent" : userInfo.getSource());
        // 用户输入的会员卡号
        if (BooleanUtils.isFalse(orderInfo.getEnabledBonusPoint())
            && StringUtils.isNotBlank(orderInfo.getMemberCardNo())
            && StringUtils.isNotBlank(orderInfo.getMemberCardholder())) {
            extInfo.setMembershipCardNum(orderInfo.getMemberCardNo());
        }
        requestBo.setExtInfo(extInfo);
        requestBo.setCorpPayType(orderInfo.getCorpPayType());
        // 配送信息
        LocalBookOrderRequestBo.DeliveryInfo deliveryInfo = new LocalBookOrderRequestBo.DeliveryInfo();
        if (orderInfo.getDeliveryInfo() != null) {
            OrderInfoModel.DeliveryInfo info = orderInfo.getDeliveryInfo();
            LocalBookOrderRequestBo.DeliveryAddress address = new LocalBookOrderRequestBo.DeliveryAddress();
            address.setAddress(info.getAddress());
            address.setCity(info.getCityName());
            address.setDeliveryFee(orderInfo.getDeliveryPrice());
            address.setDistrict(info.getDistrictName());
            address.setMobilePhone(info.getPhone());
            address.setPostCode(info.getPostalCode());
            address.setProvince(info.getProvinceName());
            address.setReceiverName(info.getName());
            deliveryInfo.setDeliveryAddress(address);
        }
        requestBo.setDeliveryInfo(deliveryInfo);
        // 发票信息
        List<LocalBookOrderRequestBo.InvoiceInfo> invoiceInfoList = Lists.newArrayList();
        InvoiceInfo invoice = orderInfo.getInvoiceInfo();
        if (invoice != null) {
            LocalBookOrderRequestBo.InvoiceInfo invoiceInfo = new LocalBookOrderRequestBo.InvoiceInfo();
            invoiceInfo.setAccountBank(invoice.getAccountBank());
            invoiceInfo.setAccountCardNo(invoice.getAccountCardNo());
            invoiceInfo.setCorporationAddress(invoice.getCorporationAddress());
            invoiceInfo.setCorporationTel(invoice.getCorporationTel());
            invoiceInfo.setInvoiceContent(invoice.getInvoiceContent());
            invoiceInfo.setInvoiceEmail(orderInfo.getContactInfo().getEmail());
            invoiceInfo.setInvoiceTitle(invoice.getInvoiceTitle());
            invoiceInfo.setInvoiceTitleType(
                InvoiceTitleEnum.getByType(invoice.getInvoiceTitleType()).map(InvoiceTitleEnum::getCode)
                    .orElseThrow(
                        () -> new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_INVOICE_TITLE_TYPE)));
            invoiceInfo.setTaxpayerNumber(invoice.getTaxpayerNumber());
            invoiceInfo.setInvoiceType(InvoiceEnum.getByType(invoice.getInvoiceType()).map(InvoiceEnum::getCode)
                .orElseThrow(() -> new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_INVOICE_TYPE)));
            invoiceInfo.setInvoiceEmail(invoice.getEmail());
            invoiceInfoList.add(invoiceInfo);
        }
        requestBo.setInvoiceInfoList(invoiceInfoList);
        LocalBookOrderRequestBo.CorpOrderInfo prepayType = this.getPrepayType(orderInfo);
        if (CollectionUtils.isNotEmpty(orderInfo.getPassengerList())) {
            prepayType.setSendMsg(
                orderInfo.getPassengerList().stream().filter(Objects::nonNull).anyMatch(PassengerInfo::getIsSendSms));
        }
        requestBo.setCorpOrderInfo(prepayType);
        return commonSupplierServiceNew.bookOrder(requestBo);
    }


    private StandardReserveOrderResponse bookOrderNew(OrderInfoModel orderInfo) {
        addElkInfoLog("进入供应商下单流程 订单缓存: %s", JsonUtils.toJsonString(orderInfo));

        Integer guestPerson = orderInfo.getRoomInfo().getGuestPerson();
        Integer bookRoomNum = orderInfo.getRoomInfo().getQuantity();
        Integer maxPerson = bookRoomNum * guestPerson;
        if (orderInfo.getPassengerList().size() > maxPerson) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_ORDER, guestPerson);
        }
        //组装请求供应商标准下单接口参数
        StandardReserveOrderRequest request = assembleStandardReserveOrderRequest(orderInfo);
        return supplierSoaClient.reserveOrder(request);
    }

    /**
     * 组装请求供应商标准下单接口参数
     * @param orderInfo 订单信息
     * @return 标准下单接口参数
     */
    private StandardReserveOrderRequest assembleStandardReserveOrderRequest(OrderInfoModel orderInfo){
        StandardReserveOrderRequest request = new StandardReserveOrderRequest();
        Map<String, Object> additionalInformationMap = new HashMap<>();
        //基础请求信息
        request.setSupplierCode(orderInfo.getSupplierCode());
        request.setCorpPayType(orderInfo.getCorpPayType());
        request.setCompanyCode(orderInfo.getCorpId());
        //基础信息
        StandardReserveOrderRequest.OrderBasicInfo orderBasicInfo = new StandardReserveOrderRequest.OrderBasicInfo();
        orderBasicInfo.setPlatformOrderID(String.valueOf(orderInfo.getOrderId()));
        orderBasicInfo.setBookerUID(orderInfo.getUid());
        request.setOrderBasicInfo(orderBasicInfo);
        //产品价格信息
        request.setProductID(orderInfo.getProductId());
        //房型信息
        StandardReserveOrderRequest.RoomInfo roomInfo = new StandardReserveOrderRequest.RoomInfo();
        roomInfo.setRoomQuantity(orderInfo.getRoomInfo().getQuantity());
        roomInfo.setGuestQuantity(orderInfo.getRoomInfo().getPersonCount());
        roomInfo.setCheckInDate(orderInfo.getRoomInfo().getCheckInDate());
        roomInfo.setCheckOutDate(orderInfo.getRoomInfo().getCheckOutDate());
        request.setRoomInfo(roomInfo);

        OrderInfoModel.HotelInfo hotelInfo = orderInfo.getHotelInfo();
        boolean hongKongMacaoTaiWan = Optional.ofNullable(hotelInfo.getHongKongMacaoTaiwan()).orElse(false);

        //入住人信息
        List<PassengerInfo> passengerList = orderInfo.getPassengerList();
        List<StandardReserveOrderRequest.GuestInfo> guestInfoList = passengerList.stream().map(item -> {
            StandardReserveOrderRequest.GuestInfo guestInfo = new StandardReserveOrderRequest.GuestInfo();
            //程曦平台唯一标识
            String uid = StringUtils.isNotBlank(item.getUid()) ? item.getUid() : item.getNoEmployeeId();
            guestInfo.setUID(uid);
            if (commonApollo.isCtrip(orderInfo.getSupplierCode())) { // 携程则需传递携程uid
                guestInfo.setUID(item.getCtripUid());
            }
            guestInfo.setName(StringUtils.isNotBlank(item.getTravelerName()) ? item.getTravelerName() : getPassengerName(item, hongKongMacaoTaiWan));
            guestInfo.setMobilePhone(item.getMobilePhone());
            guestInfo.setCountryCode(item.getCountryCode());
            additionalInformationMap.put(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_IS_SEND_SMS, guestInfo.getUID()), Optional.ofNullable(item.getIsSendSms()).orElse(false));
            OrgEmployeeVo employeeInfo = organizationEmployeeClientLoader.findEmployeeInfo(item.getUid(), item.getOrgId());
            if (employeeInfo != null) {
                additionalInformationMap.put(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_COMPANY_ID, guestInfo.getUID()), employeeInfo.getRecentCompanyId());
                additionalInformationMap.put(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_COMPANY_NAME, guestInfo.getUID()), employeeInfo.getRecentCompanyName());
                additionalInformationMap.put(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_ORG_ID, guestInfo.getUID()), employeeInfo.getOrgId());
                additionalInformationMap.put(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_ORG_NAME, guestInfo.getUID()), employeeInfo.getOrgName());
            }

            // 入住人是否获得积分
            if (StringUtils.isNotBlank(item.getMobilePhone())) {
                boolean earnPoints = BooleanUtils.isTrue(orderInfo.getEnabledBonusPoint()) && item.getMobilePhone().equals(orderInfo.getContactInfo().getMobilePhone());
                additionalInformationMap.put(String.format(REQUEST_ADDITIONAL_MAP_KEY_PASSENGER_EARN_POINTS, guestInfo.getUID()), earnPoints);
            }

            guestInfo.setProjectCode(item.getProjectCode());
            guestInfo.setCostCenterCode(item.getCostCenterCode());
            guestInfo.setCostCenterName(item.getCostCenterName());
            guestInfo.setCostCenterCorporationCode(item.getCostCenterCorporationCode());
            guestInfo.setCostCenterCorporationName(item.getCostCenterCorporationName());
            guestInfo.setSupplierAccountId(item.getSupplierAccountId());
            guestInfo.setRoomIndex(item.getRoomIndex());
            guestInfo.setEmail(item.getEmail());
            if(CollectionUtils.isNotEmpty(item.getCostCenterVoList())){
                List<TempCostCenterVo> costCenterVoList = item.getCostCenterVoList().stream()
                        .filter(tempCostCenterVo -> StringUtils.isNotBlank(tempCostCenterVo.getCostCenterId()))
                        .map(tempCostCenterVo -> {
                            TempCostCenterVo tempCostCenterVo1 = new TempCostCenterVo();
                            tempCostCenterVo1.setCostCode(tempCostCenterVo.getCostCode());
                            tempCostCenterVo1.setCostName(tempCostCenterVo.getCostName());
                            tempCostCenterVo1.setCostCenterId(tempCostCenterVo.getCostCenterId());
                            tempCostCenterVo1.setCostCenterCode(tempCostCenterVo.getCostCenterCode());
                            tempCostCenterVo1.setCostCenterName(tempCostCenterVo.getCostCenterName());
                            return tempCostCenterVo1;
                        }).collect(Collectors.toList());
                guestInfo.setCostCenterVoList(costCenterVoList);
                guestInfo.setCostCenterInfoList(costCenterVoList);
            }
            guestInfo.setFieldMap(item.getFieldMap());

            Optional.ofNullable(item.getCardType())
                .ifPresent(cardType -> guestInfo.setCardType(Integer.valueOf(cardType)));
            guestInfo.setCardNo(item.getCardNo());

            return guestInfo;
        }).collect(Collectors.toList());
        request.setGuestInfoList(guestInfoList);

        //联系人信息
        StandardReserveOrderRequest.ContactorInfo contactorInfo = new StandardReserveOrderRequest.ContactorInfo();
        contactorInfo.setName(orderInfo.getContactInfo().getName());
        contactorInfo.setMobilePhone(orderInfo.getContactInfo().getMobilePhone());
        contactorInfo.setEmail(orderInfo.getContactInfo().getEmail());
        contactorInfo.setCountryCode(orderInfo.getContactInfo().getMobilePhoneCountryCode());
        request.setContactorInfo(contactorInfo);

        //支付信息
        StandardReserveOrderRequest.PayInfo payInfo = new StandardReserveOrderRequest.PayInfo();
        payInfo.setPayType(orderInfo.getPayType());
        if (orderInfo.getPayType().equalsIgnoreCase(PayTypeEnum.MIXPAY.getType())) {
            List<StandardReserveOrderRequest.MixPayWayInfo> mixPayWayInfoList = new ArrayList<>();
            StandardReserveOrderRequest.MixPayWayInfo aPaymentWay = new StandardReserveOrderRequest.MixPayWayInfo();
            aPaymentWay.setPayAmount(orderInfo.getAPayAmount());
            aPaymentWay.setMixPayWay(PayTypeEnum.ACCNT.getType());
            mixPayWayInfoList.add(aPaymentWay);
            StandardReserveOrderRequest.MixPayWayInfo pPaymentWay = new StandardReserveOrderRequest.MixPayWayInfo();
            pPaymentWay.setPayAmount(orderInfo.getPPayAmount());
            pPaymentWay.setMixPayWay(PayTypeEnum.PPAY.getType());
            mixPayWayInfoList.add(pPaymentWay);
            payInfo.setMixPayWayInfoList(mixPayWayInfoList);
        }
        request.setPayInfo(payInfo);

        //发票信息
        InvoiceInfo orderInfoInvoiceInfo = orderInfo.getInvoiceInfo();
        if (Objects.nonNull(orderInfoInvoiceInfo)) {
            StandardReserveOrderRequest.InvoiceInfo invoiceInfo = new StandardReserveOrderRequest.InvoiceInfo();
            invoiceInfo.setOrderInvoiceTargetType(InvoiceEnum.getByType(orderInfoInvoiceInfo.getInvoiceType()).map(InvoiceEnum::getCode)
                    .orElseThrow(() -> new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_INVOICE_TYPE)));
            invoiceInfo.setInvoiceTitleType(InvoiceTitleEnum.getByType(orderInfoInvoiceInfo.getInvoiceTitleType()).map(InvoiceTitleEnum::getCode)
                    .orElseThrow(() -> new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_INVOICE_TITLE_TYPE)));
            invoiceInfo.setInvoiceTitle(orderInfoInvoiceInfo.getInvoiceTitle());
            invoiceInfo.setTaxpayerNumber(orderInfoInvoiceInfo.getTaxpayerNumber());
            invoiceInfo.setCompanyAddress(orderInfoInvoiceInfo.getCorporationAddress());
            invoiceInfo.setCompanyPhone(orderInfoInvoiceInfo.getCorporationTel());
            invoiceInfo.setCompanyBankName(orderInfoInvoiceInfo.getAccountBank());
            invoiceInfo.setCompanyBankAccount(orderInfoInvoiceInfo.getAccountCardNo());
            invoiceInfo.setEmail(orderInfoInvoiceInfo.getEmail());
            request.setInvoiceInfo(invoiceInfo);
        }
        //发票配送信息
        OrderInfoModel.DeliveryInfo orderInfoDeliveryInfo = orderInfo.getDeliveryInfo();
        if (Objects.nonNull(orderInfoDeliveryInfo)) {
            StandardReserveOrderRequest.InvoiceDeliveryInfo invoiceDeliveryInfo = new StandardReserveOrderRequest.InvoiceDeliveryInfo();
            invoiceDeliveryInfo.setContactName(orderInfoDeliveryInfo.getName());
            invoiceDeliveryInfo.setPostPhone(orderInfoDeliveryInfo.getPhone());
            invoiceDeliveryInfo.setProvince(orderInfoDeliveryInfo.getProvinceName());
            invoiceDeliveryInfo.setCity(orderInfoDeliveryInfo.getCityName());
            invoiceDeliveryInfo.setCanton(orderInfoDeliveryInfo.getDistrictName());
            invoiceDeliveryInfo.setAddress(orderInfoDeliveryInfo.getAddress());
            request.setInvoiceDeliveryInfo(invoiceDeliveryInfo);
        }
        //价格信息
        StandardReserveOrderRequest.PriceInfo priceInfo = new StandardReserveOrderRequest.PriceInfo();
        priceInfo.setTotalAmount(orderInfo.getTotalAmount());
        priceInfo.setServiceFee(orderInfo.getTotalServiceCharge());
        request.setPriceInfo(priceInfo);

        //房型信息
        additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_ROOM_ID, orderInfo.getRoomInfo().getRoomId());
        additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_HOTEL_ID, orderInfo.getRoomInfo().getHotelId());
        additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_CITY_ID, orderInfo.getRoomInfo().getSupplierCityId());

        //备注信息
        OrderInfoModel.RemarkInfo remarkInfo = orderInfo.getRemarkInfo();
        if (remarkInfo != null && CollectionUtils.isNotEmpty(remarkInfo.getOptionalRemarkList())) {
            List<String> remarkKeys = orderInfo.getRemarkKeys();
            if (CollectionUtils.isNotEmpty(remarkKeys)) {
                List<OrderInfoModel.OptionalRemark> selectRemarkList = remarkInfo.getOptionalRemarkList().stream()
                        .filter(e -> remarkKeys.contains(e.getKey())).collect(Collectors.toList());
                remarkInfo.setOptionalRemarkList(selectRemarkList);
            } else {
                remarkInfo.setOptionalRemarkList(null);
            }
            additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_REMARK_INFO, remarkInfo);
        }

        //卡号
        if (BooleanUtils.isFalse(orderInfo.getEnabledBonusPoint())
                && StringUtils.isNotBlank(orderInfo.getMemberCardNo())
                && StringUtils.isNotBlank(orderInfo.getMemberCardholder())) {
            additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_MEMBER_CARD_NO, orderInfo.getMemberCardNo());
        }

        if (CollectionUtils.isNotEmpty(orderInfo.getPassengerList())) {
            additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_SEND_MSG,
                orderInfo.getPassengerList().stream().filter(Objects::nonNull).map(PassengerInfo::getIsSendSms)
                    .filter(Objects::nonNull).anyMatch(Boolean::booleanValue));
        }

        additionalInformationMap.put(REQUEST_ADDITIONAL_MAP_KEY_CORP_NAME,orderInfo.getCorpName());

        request.setAdditionalInformationMap(additionalInformationMap);
        // 供应商下单额外信息
        request.setAdditionalSupplierInfo(Optional.ofNullable(orderInfo.getRoomInfo())
            .map(OrderInfoModel.RoomInfo::getAdditionalSupplierInfo).orElse(Collections.emptyList()));
        return request;
    }

    /**
     * 获取房客姓名
     *
     * @param passengerInfo
     * @param hongKongMacaoTaiWan
     * @return
     */
    private String getPassengerName(PassengerInfo passengerInfo, boolean hongKongMacaoTaiWan) {
        LOGGER.info(TITLE, "房客信息为：{},{}", JsonUtils.toJsonString(passengerInfo), hongKongMacaoTaiWan);
        // 使用护照的情况
        boolean passport = hongKongMacaoTaiWan || "en".equals(passengerInfo.getLanguage());
        // 判断证件类型是否为数字
        if ("T".equals(hotelApollo.getAppProperty("convertPassport", "F")) && passport
            && StringUtils.isNotBlank(passengerInfo.getPassport())) {
            PassportConvertBO passportConvertBO = PassportConvertUtil.convertPassport("",
                "", passengerInfo.getPassport(), 2);
            if (Optional.ofNullable(passportConvertBO).map(PassportConvertBO::getConvertedPassport)
                .filter(StringUtils::isNotBlank)
                .isPresent()) {
                return passportConvertBO.getConvertedPassport();
            }
        }
        return passengerInfo.getName();
    }

    private void updatePaymentNo(Long orderId, String paymentNo) {
        addElkInfoLog("更新支付流水号：%s", paymentNo);
        UpdateOrderRequestBo request = new UpdateOrderRequestBo();
        request.setOrderId(orderId);
        request.setPayNo(paymentNo);
        boolean flag = saveOrderProductService.updateOrder(request);
        addElkInfoLog("更新支付流水号：%s", (flag ? "成功" : "失败"));
    }

    /**
     * 获取prepayType
     *
     * @param orderInfo
     * @return
     */
    private LocalBookOrderRequestBo.CorpOrderInfo getPrepayType(OrderInfoModel orderInfo) {
        LocalBookOrderRequestBo.CorpOrderInfo corpOrderInfo = new LocalBookOrderRequestBo.CorpOrderInfo();
        if (hotelApollo.isCtrip(orderInfo.getSupplierCode())) {
            // 携程CASH传CASH
            corpOrderInfo.setPrepayType(PayTypeEnum.CASH.getType().equals(orderInfo.getPayType())
                ? PayTypeEnum.CashNull.getType() : PrePayTypeWayEnum.getByPayTyp(orderInfo.getPayType()));
        } else {
            // 非携程CASH传PPAY
            corpOrderInfo.setPrepayType(PayTypeEnum.CASH.getType().equals(orderInfo.getPayType())
                ? PayTypeEnum.PPAY.getType() : PrePayTypeWayEnum.getByPayTyp(orderInfo.getPayType()));
        }
        if (orderInfo.getPayType().equalsIgnoreCase(PayTypeEnum.MIXPAY.getType())) {
            List<LocalBookOrderRequestBo.MixPaymentWay> mixPayWayInfo = new ArrayList<>();
            LocalBookOrderRequestBo.MixPaymentWay aPaymentWay = new LocalBookOrderRequestBo.MixPaymentWay();
            aPaymentWay.setPayAmount(orderInfo.getAPayAmount());
            aPaymentWay.setMixPayWay(MixPayWayEnum.ACCNT);
            mixPayWayInfo.add(aPaymentWay);
            LocalBookOrderRequestBo.MixPaymentWay pPaymentWay = new LocalBookOrderRequestBo.MixPaymentWay();
            pPaymentWay.setPayAmount(orderInfo.getPPayAmount());
            pPaymentWay.setMixPayWay(MixPayWayEnum.GUEST);
            mixPayWayInfo.add(pPaymentWay);
            corpOrderInfo.setMixPayWayInfo(mixPayWayInfo);
        }

        return corpOrderInfo;
    }

    /**
     * 订单信息落库
     *
     * @param orderInfo
     * @return
     */
    private boolean saveOrder(OrderInfoModel orderInfo) {
        addElkInfoLog("进入订单信息落库流程");
        SaveOrderRequestBo saveOrderRequestBo = new SaveOrderRequestBo();
        List<PassengerInfo> passengerList = orderInfo.getPassengerList();
        List<EmployeeInfoRequestBo> requestBo = passengerList.stream().filter(e -> StringUtils.isNotBlank(e.getUid()))
            .map(e -> new EmployeeInfoRequestBo(e.getUid(), e.getOrgId())).collect(Collectors.toList());
        requestBo.add(new EmployeeInfoRequestBo(orderInfo.getUid(), orderInfo.getDeptId()));
        List<EmployeeInfoResponseBo> employeeList = organizationEmployeeClientLoader.listEmployeeInfo(requestBo);
        boolean isVip = employeeList.stream().anyMatch(e -> e.getVipLevel() != null && e.getVipLevel() > 0);
        OrderInfo order = toOrderInfo(orderInfo);
        order.setVipOrder(isVip);
        saveOrderRequestBo.setOrderInfo(order);
        saveOrderRequestBo.setDeliveryInfo(toDeliveryInfo(orderInfo));
        // 获取产品快照
        ProductSnapshotModel productSnapshotModel = hotelSnapshotGateway.getHotelProductSnapshot(orderInfo.getTravelStandardToken());

        saveOrderRequestBo.setHotelInfo(toHotelInfo(orderInfo,productSnapshotModel));
        saveOrderRequestBo.setRoomInfo(toRoomInfo(orderInfo,productSnapshotModel));
        // 只有非公司支付的时候才会开具发票
        if (!PayTypeEnum.ACCNT.getType().equals(orderInfo.getPayType().toUpperCase())) {
            saveOrderRequestBo.setInvoiceInfo(toInvoiceInfo(orderInfo));
        }
        saveOrderRequestBo.setHotelLowInfo(toHotelLowInfo(orderInfo));
        List<SaveOrderRequestBo.PassengerInfo> passengerInfos = toPassengerInfo(orderInfo);
        Map<String, EmployeeInfoResponseBo> employeeMap = employeeList.stream()
            .collect(Collectors.toMap(e -> e.getUid() + "_" + e.getOrgId(), e -> e, (e1, e2) -> e1));
        Map<String, String> travelerNameMap = organizationPassengerClientLoader.getTravelerNameMap(data -> {
            List<PassengerInfo> passengerInfoList = (List<PassengerInfo>)data;
            return passengerInfoList.stream().map(passenger -> {
                GetPassengerNameRequest passengerNameRequest = new GetPassengerNameRequest();
                String uid =
                    StringUtils.isNotBlank(passenger.getUid()) ? passenger.getUid()
                        : Objects.nonNull(passenger.getNoEmployeeId()) ? passenger.getNoEmployeeId()
                            : StringUtils.EMPTY;
                passengerNameRequest.setId(uid);
                passengerNameRequest.setEmployeeType(passenger.getEmployeeType());
                passengerNameRequest.setLanguage(passenger.getLanguage());
                return passengerNameRequest;
            }).collect(Collectors.toList());
        }, orderInfo.getPassengerList());

        boolean travelNameSwitch = getTravelerNameEnabled(TenantContext.getTenantId());
        passengerInfos.forEach(p -> {
            String key = p.getUid() + "_" + p.getOrgId();
            p.setVipLevel(Optional.ofNullable(employeeMap.get(key)).map(EmployeeInfoResponseBo::getVipLevel).orElse(0));
            if (travelNameSwitch) {
                String uid = StringUtils.isNotBlank(p.getUid()) ? p.getUid()
                    : Objects.nonNull(p.getNoEmployeeId()) ? String.valueOf(p.getNoEmployeeId()) : StringUtils.EMPTY;
                p.setTravelerName(travelerNameMap.get(uid));
            }
        });
        if (travelNameSwitch){
            orderInfo.getPassengerList().forEach(passenger ->{
                String uid = StringUtils.isNotBlank(passenger.getUid()) ? passenger.getUid()
                        : Objects.nonNull(passenger.getNoEmployeeId()) ? passenger.getNoEmployeeId() : StringUtils.EMPTY;
                passenger.setTravelerName(travelerNameMap.get(uid));
            });
        }
        saveOrderRequestBo.setPassengerInfoList(passengerInfos);
        saveOrderRequestBo.setRoomDailyInfoList(toRoomDailyInfo(orderInfo));
        saveOrderRequestBo.setOrderCancelRule(toOrderCancelRule(orderInfo));
        saveOrderRequestBo.setFileList(toFileList(orderInfo));
        saveOrderRequestBo.setHotelMemberInfo(toHotelMemberInfo(orderInfo));
        saveOrderRequestBo.setChummageInfo(toCustomerInfo(orderInfo));
        saveOrderRequestBo.setOrderSnapshotDataList(getOrderSnapshotDataList(orderInfo));
        // 服务费埋点
        metricService.metricServiceFee(order);
        try {
            saveOrderProductService.saveOrder(saveOrderRequestBo);
            addElkInfoLog("订单信息落库成功");
            return true;
        } catch (Exception e) {
            LOGGER.error(TITLE, "订单信息落库失败", e);
            addElkInfoLog("订单信息落库失败");
        }
        return false;
    }

    private List<SaveOrderRequestBo.OrderSnapshotDataInfo> getOrderSnapshotDataList(OrderInfoModel orderInfo) {
        boolean isTenantTurnOnSaveOrderSnapshotData = orderSnapshotDataConfig.getOrderSnapshotDataGreyTenantId().contains(TenantContext.getTenantId());
        if (!isTenantTurnOnSaveOrderSnapshotData) {
            log.info("++++++++ 租户未开启订单快照数据, tenantId:{}", TenantContext.getTenantId());
            return Collections.emptyList();
        }

        String travelStandardToken = orderInfo.getTravelStandardToken();
        Long orderId = orderInfo.getOrderId();

        List<SaveOrderRequestBo.OrderSnapshotDataInfo> orderSnapshotDataBoList = new ArrayList<>();

        SaveOrderRequestBo.OrderSnapshotDataInfo orderProductInfoSnapshotDataInfo = getOrderProductInfoSnapshotDataInfo(orderId, travelStandardToken);
        Optional.ofNullable(orderProductInfoSnapshotDataInfo).ifPresent(orderSnapshotDataBoList::add);

        SaveOrderRequestBo.OrderSnapshotDataInfo orderBookingConfigInfoSnapshotDataInfo = getOrderBookingConfigInfoSnapshotDataInfo(orderId, travelStandardToken);
        Optional.ofNullable(orderBookingConfigInfoSnapshotDataInfo).ifPresent(orderSnapshotDataBoList::add);
        return orderSnapshotDataBoList;
    }

    private SaveOrderRequestBo.OrderSnapshotDataInfo getOrderProductInfoSnapshotDataInfo(Long orderId, String travelStandardToken) {
        GetHotelProductSnapshotResponse hotelProductSnapshot;
        try {
            GetHotelProductSnapshotRequest request = new GetHotelProductSnapshotRequest();
            request.setToken(travelStandardToken);
            hotelProductSnapshot = coreServiceClientLoader.getHotelProductSnapshot(request);
        } catch (Exception e) {
            log.error("获取酒店快照失败, travelStandardToken:{}", travelStandardToken, e);
            return null;
        }

        if (Objects.isNull(hotelProductSnapshot)) {
            log.error("获取酒店快照失败, travelStandardToken:{}", travelStandardToken);
            return null;
        }
        SaveOrderRequestBo.OrderSnapshotDataInfo productInfoSnapshotData = new SaveOrderRequestBo.OrderSnapshotDataInfo();
        productInfoSnapshotData.setProductType(ProductEnum.HOTEL.getCode());
        productInfoSnapshotData.setBusinessId(String.valueOf(orderId));
        productInfoSnapshotData.setDataScene(OrderSnapshotDataSceneEnum.BOOKING.getCode());
        productInfoSnapshotData.setDataType(OrderSnapshotDataTypeEnum.PRODUCT_INFO.getCode());
        productInfoSnapshotData.setDataContent(JsonUtils.toJsonString(hotelProductSnapshot));
        return productInfoSnapshotData;
    }


    private SaveOrderRequestBo.OrderSnapshotDataInfo getOrderBookingConfigInfoSnapshotDataInfo(Long orderId, String travelStandardToken) {
        BookingConfigModel bookingConfigSnapshot;
        try {
            bookingConfigSnapshot = hotelSnapshotGateway.getBookingConfigSnapshot(travelStandardToken);
        } catch (Exception e) {
            log.error("获取预订配置快照失败, travelStandardToken:{}", travelStandardToken);
            return null;
        }

        if (Objects.isNull(bookingConfigSnapshot)) {
            log.error("获取预订配置快照失败, travelStandardToken:{}", travelStandardToken);
            return null;
        }
        List<SwitchModel> switchInfoSoaList = bookingConfigSnapshot.getAllSwitch().getSwitchInfoSoaList();
        if (CollectionUtils.isEmpty(switchInfoSoaList)) {
            log.error("获取预订配置列表为空, travelStandardToken:{}", travelStandardToken);
            return null;
        }

        List<OrderSnapshotBookingConfigDataBo> orderSnapshotBookingConfigDataBoList = switchInfoSoaList.stream().filter(Objects::nonNull)
                .filter(item -> {
                    List<String> orderSnapshotDataBookingConfigAttrList = orderSnapshotDataConfig.getOrderSnapshotDataBookingConfigAttr();
                    if (CollectionUtils.isEmpty(orderSnapshotDataBookingConfigAttrList)) { //表示全量
                        return true;
                    }
                    return orderSnapshotDataBookingConfigAttrList.contains(item.getSwitchKey());
                }).map(item -> {
                    OrderSnapshotBookingConfigDataBo orderSnapshotBookingConfigDataBo = new OrderSnapshotBookingConfigDataBo();
                    orderSnapshotBookingConfigDataBo.setSwitchKey(item.getSwitchKey());
                    orderSnapshotBookingConfigDataBo.setSwitchName(item.getSwitchName());
                    orderSnapshotBookingConfigDataBo.setValue(item.getValue());
                    return orderSnapshotBookingConfigDataBo;
                }).collect(Collectors.toList());

        SaveOrderRequestBo.OrderSnapshotDataInfo bookingConfigSnapshotData = new SaveOrderRequestBo.OrderSnapshotDataInfo();
        bookingConfigSnapshotData.setProductType(ProductEnum.HOTEL.getCode());
        bookingConfigSnapshotData.setBusinessId(String.valueOf(orderId));
        bookingConfigSnapshotData.setDataScene(OrderSnapshotDataSceneEnum.BOOKING.getCode());
        bookingConfigSnapshotData.setDataType(OrderSnapshotDataTypeEnum.BOOKING_CONFIG.getCode());
        bookingConfigSnapshotData.setDataContent(JsonUtils.toJsonString(orderSnapshotBookingConfigDataBoList));
        return bookingConfigSnapshotData;
    }

    private SaveOrderRequestBo.ChummageInfo toCustomerInfo(OrderInfoModel orderInfo) {
        if(ObjectUtils.notNull(orderInfo.getChummageInfo())){
            SaveOrderRequestBo.ChummageInfo chummageInfo = new SaveOrderRequestBo.ChummageInfo();
            chummageInfo.setHaveChummage(orderInfo.getChummageInfo().getHaveChummage());
            chummageInfo.setOrderId(orderInfo.getOrderId());
            if(ObjectUtils.notNull(orderInfo.getChummageInfo().getNoChummageReasonCode())){
                SaveOrderRequestBo.NoChummageReasonCode noChummageReasonCode = new SaveOrderRequestBo.NoChummageReasonCode();
                noChummageReasonCode.setRemark(orderInfo.getChummageInfo().getNoChummageReasonCode().getRemark());
                noChummageReasonCode.setId(orderInfo.getChummageInfo().getNoChummageReasonCode().getId());
                noChummageReasonCode.setName(orderInfo.getChummageInfo().getNoChummageReasonCode().getName());
                noChummageReasonCode.setCode(orderInfo.getChummageInfo().getNoChummageReasonCode().getCode());
                chummageInfo.setNoChummageReasonCode(noChummageReasonCode);
            }
            return chummageInfo;
        }
        return null;
    }

    private SaveOrderRequestBo.HotelMemberInfo toHotelMemberInfo(OrderInfoModel orderInfo) {
        OrderInfoModel.BonusPointInfo bonusPointInfo = orderInfo.getRoomInfo().getBonusPointInfo();
        if (BooleanUtils.isTrue(orderInfo.getEnabledBonusPoint())
            || (StringUtils.isNotBlank(orderInfo.getMemberCardholder())
                && StringUtils.isNotBlank(orderInfo.getMemberCardNo()))
            || (null != bonusPointInfo
                && BonusPointTypeEnum.OFFLINE.getCode().equalsIgnoreCase(bonusPointInfo.getBonusPointType()))) {
            SaveOrderRequestBo.HotelMemberInfo hotelMemberInfo = new SaveOrderRequestBo.HotelMemberInfo();
            hotelMemberInfo.setEnabledBonusPoint(orderInfo.getEnabledBonusPoint());
            hotelMemberInfo.setMemberCardNo(orderInfo.getMemberCardNo());
            hotelMemberInfo.setMemberCardholder(orderInfo.getMemberCardholder());

            if (null != bonusPointInfo) {
                hotelMemberInfo.setGroupId(bonusPointInfo.getGroupId());
                hotelMemberInfo.setGroupName(bonusPointInfo.getGroupName());
                hotelMemberInfo
                    .setMemberRuleDesc(JsonUtils.toJsonString(bonusPointInfo.getOrderDetailPageRuleDescList()));
                hotelMemberInfo.setBonusPointCode(bonusPointInfo.getBonusPointCode());
                hotelMemberInfo.setBonusPointType(bonusPointInfo.getBonusPointType());
            }
            return hotelMemberInfo;
        }
        return null;
    }


    /**
     * 组装文件数据
     * 
     * @param orderInfo
     * @return
     */
    private List<SaveOrderRequestBo.BusinessFileInfo> toFileList(OrderInfoModel orderInfo) {
        if (CollectionUtils.isEmpty(orderInfo.getFileList())) {
            return null;
        }

        return orderInfo.getFileList().stream().map(this::saveBusinessFileInfo).collect(Collectors.toList());
    }

    /**
     * 组装文件保存对象数据
     * 
     * @param requestVO
     * @return
     */
    private SaveOrderRequestBo.BusinessFileInfo saveBusinessFileInfo(BusinessFileRequestVO requestVO) {
        SaveOrderRequestBo.BusinessFileInfo businessFileInfo = new SaveOrderRequestBo.BusinessFileInfo();
        businessFileInfo.setFileName(requestVO.getFileName());
        businessFileInfo.setFilePath(requestVO.getFilePath());
        businessFileInfo.setFileType(requestVO.getFileType());
        businessFileInfo.setFileSize(requestVO.getFileSize());
        return businessFileInfo;
    }

    /**
     * 处理取消规则
     *
     * @param orderInfo
     * @return
     */
    private List<SaveOrderRequestBo.OrderCancelRule> toOrderCancelRule(OrderInfoModel orderInfo) {
        if (orderInfo.getOrderCancelRule() == null) {
            return new ArrayList<>();
        }
        List<OrderInfoModel.OrderCancelRule> orderCancelRuleList = orderInfo.getOrderCancelRule();
        return orderCancelRuleList.stream().map(e -> {
            SaveOrderRequestBo.OrderCancelRule orderCancelRule = new SaveOrderRequestBo.OrderCancelRule();
            orderCancelRule.setDeductionType(e.getDeductionType());
            orderCancelRule.setStartDeductTime(e.getStartDeductTime());
            orderCancelRule.setEndDeductTime(e.getEndDeductTime());
            orderCancelRule.setAmount(e.getAmount());
            orderCancelRule.setDeductionRatio(e.getDeductionRatio());
            orderCancelRule.setOrderId(orderInfo.getOrderId());
            return orderCancelRule;
        }).collect(Collectors.toList());
    }

    /**
     * 处理每日房间信息
     *
     * @param orderInfo
     * @return
     */
    private List<SaveOrderRequestBo.RoomDailyInfo> toRoomDailyInfo(OrderInfoModel orderInfo) {
        List<OrderInfoModel.RoomDailyInfo> roomDailyInfoList = orderInfo.getRoomDailyInfoList();
        Long orderId = orderInfo.getOrderId();
        return roomDailyInfoList.stream().map(e -> {
            SaveOrderRequestBo.RoomDailyInfo roomDailyInfo = new SaveOrderRequestBo.RoomDailyInfo();
            roomDailyInfo.setOrderId(orderId);
            roomDailyInfo.setEffectDate(DateUtil.stringToDate(e.getEffectDate(), DateUtil.DF_YMD));
            roomDailyInfo.setRoomPrice(e.getRoomPrice());
            roomDailyInfo.setBreakfast(e.getBreakfast());
            roomDailyInfo.setBreakfastName(e.getBreakfastName());
            roomDailyInfo.setMeals(e.getMeals());
            return roomDailyInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 处理入住人信息
     *
     * @param orderInfo
     * @return
     */
    private List<SaveOrderRequestBo.PassengerInfo> toPassengerInfo(OrderInfoModel orderInfo) {
        List<OrderInfoModel.PassengerInfo> passengerList = orderInfo.getPassengerList();
        return passengerList.stream().map(e -> {
            SaveOrderRequestBo.PassengerInfo passengerInfo = new SaveOrderRequestBo.PassengerInfo();
            passengerInfo.setUid(e.getUid());
            passengerInfo.setBirthday(DateUtil.stringToDate(e.getBirthday(), "yyyy-MM-dd"));
            passengerInfo.setGender(e.getGender());
            passengerInfo.setMobilePhone(e.getMobilePhone());
            passengerInfo.setIsSendSms(e.getIsSendSms());
            passengerInfo.setCountryCode(e.getCountryCode());
            passengerInfo.setCostCenterCode(e.getCostCenterCode());
            passengerInfo.setCostCenterName(e.getCostCenterName());
            passengerInfo.setCostCenterId(e.getCostCenterId());
            passengerInfo.setProjectCode(e.getProjectCode());
            passengerInfo.setProjectName(e.getProjectName());
            passengerInfo.setNoSelectProjectDesc(e.getNoSelectProjectDesc());
            passengerInfo.setProjectId(e.getProjectId());
            passengerInfo.setWbsRemark(e.getWbsRemark());
            passengerInfo.setCostCenterRemark(e.getCostCenterRemark());
            passengerInfo.setDepartmentName(null);
            passengerInfo.setOrgId(e.getOrgId());
            passengerInfo.setOrgName(e.getOrgName());
            passengerInfo.setRoomNo(e.getRoomNo());
            passengerInfo.setServicePrice(null);
            passengerInfo.setTripApplyNo(e.getTripApplyNo());
            passengerInfo.setTripTrafficId(e.getTravelPlanId());
            passengerInfo.setOrderId(orderInfo.getOrderId());
            passengerInfo.setPassengerName(e.getName());
            passengerInfo.setDepartmentId(e.getOrgId());
            passengerInfo.setDepartmentName(e.getDepartmentName());
            String noEmployeeId = e.getNoEmployeeId();
            if (StringUtils.isNotBlank(noEmployeeId)) {
                passengerInfo.setNoEmployeeId(Optional.ofNullable(noEmployeeId).map(Long::valueOf).orElse(null));
            }
            if (null == e.getEmployeeType()) {
                if (!e.getCorp() || (StringUtils.isBlank(e.getUid()) && StringUtils.isNotBlank(e.getNoEmployeeId()))) {
                    e.setEmployeeType(ApplyTripEmployeeEnum.EXTERNAL_EMPLOYEE.getCode());
                }
            }
            passengerInfo.setEmployeeType(e.getEmployeeType());
            passengerInfo.setRoomIndex(e.getRoomIndex());

            if (CollectionUtils.isNotEmpty(e.getCostCenterVoList())) {
                passengerInfo.setMultiCostCenterDataJSON(JsonUtils.toJsonString(e.getCostCenterVoList()));
            }

            Optional.ofNullable(e.getCardType())
                    .ifPresent(cardType -> passengerInfo.setCardType(Integer.valueOf(cardType)));
            passengerInfo.setCardNo(e.getCardNo());
            passengerInfo.setEmail(e.getEmail());

            passengerInfo.setCostCenterLegalEntityName(e.getCostCenterCorporationName());
            passengerInfo.setCostCenterLegalEntityCode(e.getCostCenterCorporationCode());
            passengerInfo.setProjectLegalEntityCode(e.getProjectCorporationCode());
            passengerInfo.setProjectLegalEntityName(e.getProjectCorporationName());
            if(CollectionUtils.isNotEmpty(e.getAccountingUnitCategoryConfigList())){
                passengerInfo.setAccountingUnitJson(JsonUtils.toJsonString(e.getAccountingUnitCategoryConfigList().stream().map(this::convertAccountingUnit).collect(Collectors.toList())));
            }
            passengerInfo.setSupplierAccountId(e.getSupplierAccountId());
            passengerInfo.setCtripUid(e.getCtripUid());
            return passengerInfo;
        }).collect(Collectors.toList());
    }

    private AccountingUnitInfoVo convertAccountingUnit(OrderInfoModel.AccountingUnit accountingUnitVo){
        AccountingUnitInfoVo accountingUnitInfoVo = new AccountingUnitInfoVo();
        accountingUnitInfoVo.setAccountingUnitCode(accountingUnitVo.getCode());
        accountingUnitInfoVo.setAccountingUnitName(accountingUnitVo.getName());
        accountingUnitInfoVo.setLegalEntityCode(accountingUnitVo.getBusinessUnitCode());
        accountingUnitInfoVo.setLegalEntityName(accountingUnitVo.getBusinessUnitName());
        accountingUnitInfoVo.setAccountingUnitType(accountingUnitVo.getCategoryName());
        accountingUnitInfoVo.setAccountingUnitTypeCode(accountingUnitVo.getCategoryCode());
        accountingUnitInfoVo.setAccountingUnitRemark(accountingUnitVo.getRemark());
        return accountingUnitInfoVo;
    }

    /**
     * 处理酒店低价信息
     *
     * @param orderInfo
     * @return
     */
    private SaveOrderRequestBo.HotelLowInfo toHotelLowInfo(OrderInfoModel orderInfo) {
        OrderInfoModel.RcInfo rcInfo = orderInfo.getRcInfo();
        if (rcInfo == null) {
            return null;
        }
        SaveOrderRequestBo.HotelLowInfo hotelLowInfo = new SaveOrderRequestBo.HotelLowInfo();
        hotelLowInfo.setAmountHigh(orderInfo.getAmountHigh());
        hotelLowInfo.setOrderId(orderInfo.getOrderId());
        hotelLowInfo.setReason(rcInfo.getReason());
        hotelLowInfo.setReasonNote(rcInfo.getReasonNote());
        return hotelLowInfo;
    }

    /**
     * 处理发票信息
     *
     * @param orderInfo
     * @return
     */
    private SaveOrderRequestBo.InvoiceInfo toInvoiceInfo(OrderInfoModel orderInfo) {
        OrderInfoModel.InvoiceInfo invoiceInfo = orderInfo.getInvoiceInfo();
        if (invoiceInfo == null) {
            return null;
        }
        SaveOrderRequestBo.InvoiceInfo result = new SaveOrderRequestBo.InvoiceInfo();
        result.setId(invoiceInfo.getId());
        result.setAccountBank(invoiceInfo.getAccountBank());
        result.setAccountCardNo(invoiceInfo.getAccountCardNo());
        result.setCorporationAddress(invoiceInfo.getCorporationAddress());
        result.setCorporationTel(invoiceInfo.getCorporationTel());
        result.setInvoiceTitle(invoiceInfo.getInvoiceTitle());
        result.setInvoiceTitleType(invoiceInfo.getInvoiceTitleType());
        result.setInvoiceType(invoiceInfo.getInvoiceType());
        result.setInvoiceContent(invoiceInfo.getInvoiceContent());
        result.setTaxpayerNumber(invoiceInfo.getTaxpayerNumber());
        result.setOrderId(orderInfo.getOrderId());
        result.setUid(orderInfo.getUid());
        result.setEmail(invoiceInfo.getEmail());
        return result;
    }

    /**
     * 处理房间信息
     *
     * @param orderInfo
     * @return
     */
    private SaveOrderRequestBo.RoomInfo toRoomInfo(OrderInfoModel orderInfo,ProductSnapshotModel productSnapshotModel) {
        SaveOrderRequestBo.RoomInfo roomInfo = new SaveOrderRequestBo.RoomInfo();
        OrderInfoModel.RoomInfo orderInfoRoomInfo = orderInfo.getRoomInfo();
        roomInfo.setHotelType(orderInfoRoomInfo.getHotelType());
        roomInfo.setRoomId(orderInfoRoomInfo.getRoomId());
        roomInfo.setRoomName(orderInfoRoomInfo.getRoomName());
        roomInfo.setBreakfast(orderInfoRoomInfo.getBreakfast());
        roomInfo.setBreakfastName(orderInfoRoomInfo.getBreakfastName());
        roomInfo.setNextDay(orderInfoRoomInfo.getNextDay());
        roomInfo.setBedType(orderInfoRoomInfo.getBedType());
        roomInfo.setApplicativeAreaDesc(orderInfoRoomInfo.getApplicativeAreaDesc());
        roomInfo.setApplicativeAreaTitle(orderInfoRoomInfo.getApplicativeAreaTitle());
        roomInfo.setPersonCount(orderInfoRoomInfo.getPersonCount());
        roomInfo.setCancelModifyNote(orderInfoRoomInfo.getCancelModifyNote());
        roomInfo.setCancelPolicyType(orderInfoRoomInfo.getPolicyType());
        roomInfo.setCancelPolicyDesc(orderInfo.getRoomInfo().getPolicyDesc());
        roomInfo.setCheckInDate(DateUtil.stringToDate(orderInfoRoomInfo.getCheckInDate(), DateUtil.DF_YMD));
        roomInfo.setCheckOutDate(DateUtil.stringToDate(orderInfoRoomInfo.getCheckOutDate(), DateUtil.DF_YMD));
        roomInfo.setRoomQuantity(orderInfoRoomInfo.getQuantity());
        roomInfo.setOrderId(orderInfo.getOrderId());
        roomInfo.setBasicRoomId(orderInfoRoomInfo.getBasicRoomId());
        roomInfo.setMealType(orderInfoRoomInfo.getMealType());
        roomInfo.setLastCancelTime(DateUtil.stringToDate(orderInfoRoomInfo.getLastCancelTime(), DateUtil.DF_YMD_HMS));
        roomInfo.setMinConsecutiveDays(orderInfoRoomInfo.getMinConsecutiveDays());

        // 是否套餐
        roomInfo.setPackageRoom(Optional.ofNullable(orderInfoRoomInfo.getPackageRoom()).orElse(Boolean.FALSE));
        // 套餐ID
        roomInfo.setPackageId(Optional.ofNullable(orderInfoRoomInfo.getPackageId()).orElse(0));
        // 房型图片
        roomInfo.setPicUrls(CollectionUtils.isNotEmpty(orderInfoRoomInfo.getPicUrls())
            ? JsonUtils.toJsonString(orderInfoRoomInfo.getPicUrls()) : null);
        // 房型基础数据
        roomInfo.setRoomInfoContext(CollectionUtils.isNotEmpty(orderInfoRoomInfo.getBasicInfo())
            ? JsonUtils.toJsonString(orderInfoRoomInfo.getBasicInfo()) : null);
        if (roomInfo.getPackageRoom() && roomInfo.getPackageId() > 0) {
            // 套餐内容
            List<PackageRoomResponseVo.PackageRoomProductInfo> packageRoomInfo = getPackageRoomInfo(orderInfo);
            roomInfo.setPackageRoomContext(
                CollectionUtils.isNotEmpty(packageRoomInfo) ? JsonUtils.toJsonString(packageRoomInfo) : null);
        }
        OrderInfoModel.ParentRoomInfo parentRoomInfo = orderInfoRoomInfo.getParentRoomInfo();
        if (null != parentRoomInfo) {
            // 所属母房型下最低价非协议房型的房费均价
            roomInfo.setNonProtocolMinAvgPrice(parentRoomInfo.getNonProtocolMinAvgPrice());
            // 所属母房型下最高价非协议房型的房费均价
            roomInfo.setNonProtocolMaxAvgPrice(parentRoomInfo.getNonProtocolMaxAvgPrice());
            // 所属母房型下最低价协议房型的房费均价
            roomInfo.setProtocolMinAvgPrice(parentRoomInfo.getProtocolMinAvgPrice());
            // 所属母房型下最低价协议房型的所属供应商
            roomInfo.setProtocolMinAvgPriceSupplierCode(parentRoomInfo.getProtocolMinAvgPriceSupplierCode());
        }

        //床型信息赋值
        Optional.ofNullable(productSnapshotModel)
                .filter(model -> CollectionUtils.isNotEmpty(model.getBasicRoomInfo()))
                .map(model -> model.getBasicRoomInfo().get(0).getRoomCardList())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(roomCardList -> {
                    roomInfo.setBedDesc(roomCardList.get(0).getRoomBaseInfo().getBedDesc());
                    roomInfo.setBedDetailDesc(roomCardList.get(0).getRoomBaseInfo().getParentBedDesc());
                });
        return roomInfo;
    }

    /**
     * 获取套餐内容
     *
     * @param orderInfo
     * @return
     */
    private List<PackageRoomResponseVo.PackageRoomProductInfo> getPackageRoomInfo(OrderInfoModel orderInfo) {
        PackageRoomRequestVo packageRoomRequestVo = new PackageRoomRequestVo();
        packageRoomRequestVo.setHotelId(orderInfo.getHotelInfo().getHotelId());
        packageRoomRequestVo.setRoomId(orderInfo.getRoomInfo().getRoomId());
        packageRoomRequestVo.setPackageId(orderInfo.getRoomInfo().getPackageId());
        packageRoomRequestVo.setSupplier(orderInfo.getSupplierCode());
        // 获取登录用户信息
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        packageRoomRequestVo.setBaseUserInfo(baseUserInfo);
        PackageRoomResponseVo packageRoomResponseVo = packageRoomService.searchPackageRoom(packageRoomRequestVo);
        if (null != packageRoomResponseVo
            && CollectionUtils.isNotEmpty(packageRoomResponseVo.getPackageProductList())) {
            return packageRoomResponseVo.getPackageProductList();
        }
        return null;
    }

    /**
     * 处理酒店信息
     *
     * @param orderInfo
     * @return
     */
    private SaveOrderRequestBo.HotelInfo toHotelInfo(OrderInfoModel orderInfo,ProductSnapshotModel productSnapshotModel) {
        SaveOrderRequestBo.HotelInfo hotelInfo = new SaveOrderRequestBo.HotelInfo();
        OrderInfoModel.HotelInfo cacheHotelInfo = orderInfo.getHotelInfo();
        hotelInfo.setHotelName(cacheHotelInfo.getHotelName());
        hotelInfo.setIsStarLicence(cacheHotelInfo.getIsStarLicence());
        hotelInfo.setIsGuarantee(cacheHotelInfo.getIsStarLicence());
        hotelInfo.setStar(cacheHotelInfo.getStar());
        hotelInfo.setCityId(cacheHotelInfo.getCityId());
        hotelInfo.setSupplierCityId(cacheHotelInfo.getSupplierCityId());
        hotelInfo.setCityName(cacheHotelInfo.getCityName());
        hotelInfo.setLongitude(cacheHotelInfo.getLongitude());
        hotelInfo.setLatitude(cacheHotelInfo.getLatitude());
        hotelInfo.setHotelTips(cacheHotelInfo.getHotelTips());
        hotelInfo.setHotelPhone(cacheHotelInfo.getHotelPhone());
        hotelInfo.setBrandName(cacheHotelInfo.getBrandName());
        hotelInfo.setAddress(cacheHotelInfo.getAddress());
        hotelInfo.setHotelId(cacheHotelInfo.getHotelId());
        hotelInfo.setIsGuarantee(false);
        hotelInfo.setLastArrivalTime(DateUtil.stringToDate(cacheHotelInfo.getLastArrivalTime(), DateUtil.DF_YMD_HM));
        hotelInfo.setEarlyArrivalTime(DateUtil.stringToDate(cacheHotelInfo.getEarlyArrivalTime(), DateUtil.DF_YMD_HM));
        hotelInfo.setLastCancelTime(DateUtil.stringToDate(cacheHotelInfo.getLastCancelTime(), DateUtil.DF_YMD_HM));
        hotelInfo.setOrderId(orderInfo.getOrderId());
        hotelInfo.setHotelType(orderInfo.getRoomInfo().getHotelType());
        hotelInfo.setSupplierHotelId(orderInfo.getHotelInfo().getHotelId());
        if (StringUtils.isNotBlank(orderInfo.getHotelInfo().getBrandId())) {
            HotelBrandBo hotelBrandBo = hotelBrandDataService.searchHotelBrand(orderInfo.getHotelInfo().getBrandId());
            if (Objects.nonNull(hotelBrandBo)) {
                hotelInfo.setBrandId(hotelBrandBo.getBrandId());
                hotelInfo.setBrandName(
                    StringUtils.isBlank(hotelBrandBo.getBrandName()) ? "其他" : hotelBrandBo.getBrandName());
            }
        }
        List<String> remarkKeys = orderInfo.getRemarkKeys();
        OrderInfoModel.RemarkInfo remarkInfo = orderInfo.getRemarkInfo();
        if (remarkInfo != null) {
            if (CollectionUtils.isNotEmpty(remarkKeys)) {
                List<OrderInfoModel.OptionalRemark> optionalRemarkList = remarkInfo.getOptionalRemarkList();
                if (CollectionUtils.isNotEmpty(optionalRemarkList)) {
                    List<String> remarks = optionalRemarkList.stream().filter(e -> remarkKeys.contains(e.getKey()))
                        .map(OrderInfoModel.OptionalRemark::getValue).collect(Collectors.toList());
                    hotelInfo.setRemarks(String.join(",", remarks));
                }
            }
            if (StringUtils.isNotBlank(remarkInfo.getCustomRemark())) {
                hotelInfo.setRemarks(remarkInfo.getCustomRemark());
            }
        }
        hotelInfo.setProtocolTag(orderInfo.getRoomInfo().getProtocolTag());
        hotelInfo.setProtocolType(orderInfo.getRoomInfo().getProtocolType());
        hotelInfo.setLocationId(cacheHotelInfo.getLocationId());
        hotelInfo.setLocationName(cacheHotelInfo.getLocationName());
        // 集团ID
        hotelInfo.setGroupId(cacheHotelInfo.getGroupId());
        //查询出酒店英文名称
        Optional.ofNullable(productSnapshotModel).map(ProductSnapshotModel::getHotelInfo)
                .ifPresent(h->hotelInfo.setHotelEnName(h.getNameEn()));
        hotelInfo.setVatFlag(cacheHotelInfo.getVatFlag());
        hotelInfo.setLogoPicUrl(cacheHotelInfo.getLogoPicUrl());
        return hotelInfo;
    }

    /**
     * 处理配送信息
     *
     * @param orderInfo
     * @return
     */
    private SaveOrderRequestBo.DeliveryInfo toDeliveryInfo(OrderInfoModel orderInfo) {
        OrderInfoModel.DeliveryInfo deliveryInfo = orderInfo.getDeliveryInfo();
        if (orderInfo.getInvoiceInfo() == null || deliveryInfo == null) {
            return null;
        }
        if (orderInfo.getInvoiceInfo().getInvoiceType() == 2) {
            return null;
        }
        SaveOrderRequestBo.DeliveryInfo result = new SaveOrderRequestBo.DeliveryInfo();
        result.setProvinceId(deliveryInfo.getProvinceId());
        result.setProvinceName(deliveryInfo.getProvinceName());
        result.setCityId(deliveryInfo.getCityId());
        result.setCityName(deliveryInfo.getCityName());
        result.setDistrictId(deliveryInfo.getDistrictId());
        result.setDistrictName(deliveryInfo.getDistrictName());
        result.setAddress(deliveryInfo.getAddress());
        result.setDeliveryType(deliveryInfo.getDeliveryType());
        result.setPostCode(deliveryInfo.getPostalCode());
        result.setId(deliveryInfo.getKey());
        result.setOrderId(orderInfo.getOrderId());
        result.setRecipientMobilePhone(deliveryInfo.getPhone());
        result.setRecipientName(deliveryInfo.getName());
        result.setUid(orderInfo.getUid());
        return result;
    }

    /**
     * 处理订单信息
     *
     * @param orderInfoCache
     * @return
     */
    private SaveOrderRequestBo.OrderInfo toOrderInfo(OrderInfoModel orderInfoCache) {
        SaveOrderRequestBo.OrderInfo orderInfo = new SaveOrderRequestBo.OrderInfo();
        BeanUtils.copyProperties(orderInfoCache, orderInfo);
        orderInfo.setPayType(orderInfoCache.getPayType());
        orderInfo.setCorpId(orderInfoCache.getCorpId());
        orderInfo.setTripApplyNo(orderInfoCache.getApplyNo());
        orderInfo.setTripTrafficId(orderInfoCache.getTrafficId());
        orderInfo.setContactName(orderInfoCache.getContactInfo().getName());
        orderInfo.setContactEmail(orderInfoCache.getContactInfo().getEmail());
        orderInfo.setContactCountryCode(orderInfoCache.getContactInfo().getMobilePhoneCountryCode());
        orderInfo.setContactMobilePhone(orderInfoCache.getContactInfo().getMobilePhone());

        orderInfo.setSource(this.getSource(orderInfoCache));
        orderInfo.setAgentUid(orderInfoCache.getAgentUid());
        orderInfo.setApprovalId(orderInfoCache.getApprovalId());
        orderInfo.setTravelStandard(orderInfoCache.getTravelStandard());
        // 执行差标金额
        if (orderInfoCache.getMaxAvgPriceRule() != null) {
            orderInfo.setTravelStandardAmount(orderInfoCache.getMaxAvgPriceRule().getPrice());
        }
        MbSupplierInfoVo supplierInfo =
            Optional.ofNullable(supplierDataClientLoader.findBySupplierCode(orderInfo.getSupplierCode()))
                .orElse(new MbSupplierInfoVo());
        orderInfo.setSupplierPhone(supplierInfo.getHotLine());
        // 是否超标,0超标
        orderInfo.setRcType(Optional.ofNullable(orderInfoCache.getRcInfo()).map(OrderInfoModel.RcInfo::getReason)
                .map(StringUtils::isNotBlank).orElse(false) ? 0 : 1);
        // 设置出差申请紧急预定
        orderInfo.setUrgentApply(BooleanUtils.isTrue(orderInfoCache.getUrgentApply()));
        // 供应商主账户ID
        orderInfo.setSupplierAccountId(orderInfoCache.getSupplierAccountId());
        // 差标Token
        orderInfo.setTravelStandardToken(orderInfoCache.getTravelStandardToken());
        // 同住管理开关是否开启
        orderInfo.setSharedManageStatus(orderInfoCache.getSharedManageStatus());
        // 酒店差标管控规则
        orderInfo.setHotelManageRules(orderInfoCache.getHotelManageRules());
        // 合住百分比
        orderInfo.setSharedPercentage(orderInfoCache.getSharedPercentage());
        // 酒店差标管控策略
        orderInfo.setHotelManageStrategy(orderInfoCache.getHotelManageStrategy());
        // 服务费
        orderInfo.setServiceFee(orderInfoCache.getTotalServiceCharge());
        // 间夜均价
        orderInfo.setRoomNightAvgPrice(orderInfoCache.getRoomNightAvgPrice());
        // 订单总金额
        orderInfo.setAmount(orderInfoCache.getTotalAmount());
        // 混付情况下公司支付金额
        orderInfo.setAPayAmount(orderInfoCache.getAPayAmount());
        // 混付情况下个人支付金额
        orderInfo.setPPayAmount(orderInfoCache.getPPayAmount());
        // 混付类型
        orderInfo.setMixPayType(orderInfoCache.getMixPayType());
        // 审核方式
        orderInfo.setApprovalWay(orderInfoCache.getApprovalWay());
        // 自定义备注
        orderInfo.setCustomRemark(getCustomRemark(orderInfoCache));
        // 服务费策略
        orderInfo.setServiceFeeStrategy(orderInfoCache.getServiceChargeStrategy());
        orderInfo.setServiceFeeStrategyValue(orderInfoCache.getServiceChargeStrategyValue());

        GetEmployeeOpenCardReq carReq = new GetEmployeeOpenCardReq();
        carReq.setUid(orderInfoCache.getUid());
        carReq.setCorpId(orderInfoCache.getCorpId());

        GetEmployeeOpenCardRsp employeeOpenCardInfo = commonOrganizationDataloader.getEmployeeOpenCardInfo(carReq);
        if (employeeOpenCardInfo != null && StringUtils.isNotBlank(employeeOpenCardInfo.getSupplierUid())) {
            orderInfo.setSupplierUid(employeeOpenCardInfo.getSupplierUid());
        }

        return orderInfo;
    }


    /**
     * 获取自定义备注
     *
     * @param orderInfoCache 订单信息缓存
     * @return {@link String }
     */
    private String getCustomRemark(OrderInfoModel orderInfoCache) {
        String customRemark = "";
        OrderInfoModel.RemarkInfo remarkInfo = orderInfoCache.getRemarkInfo();
        if (Objects.isNull(remarkInfo)) {
            return customRemark;
        }
        // 填写备注
        String filledInRemark = remarkInfo.getCustomRemark();
        if (StringUtils.isNotBlank(filledInRemark)) {
            customRemark = filledInRemark;
        }

        // 选项备注
        List<String> remarkKeys = orderInfoCache.getRemarkKeys();
        List<OrderInfoModel.OptionalRemark> optionalRemarkList = remarkInfo.getOptionalRemarkList();
        boolean hasOptionalRemarkAndUserSelected =
            CollectionUtils.isNotEmpty(remarkKeys) && CollectionUtils.isNotEmpty(optionalRemarkList);
        if (hasOptionalRemarkAndUserSelected) {
            String optionalRemarkStr = optionalRemarkList.stream().filter(e -> remarkKeys.contains(e.getKey()))
                .map(OrderInfoModel.OptionalRemark::getValue).collect(Collectors.joining(StrUtil.COMMA));
            if (StringUtils.isNotBlank(optionalRemarkStr)) {
                customRemark = optionalRemarkStr + StrUtil.SPACE + customRemark;
            }
        }
        // 截取前200个字符
        customRemark = customRemark.substring(0, Math.min(customRemark.length(), 200));
        return customRemark;
    }

    /**
     * 获取source
     *
     * @param orderInfo
     * @return
     */
    private String getSource(OrderInfoModel orderInfo) {
        if (Objects.equals(orderInfo.getBookingChannel(), "PCBOOK")) {
            return OrderSourceEnum.Web.name();
        }
        if (StringUtils.isNotBlank(orderInfo.getAgentUid())) {
            return OrderSourceEnum.Agent.name();
        }
        return orderInfo.getBookingChannel();
    }

    /**
     * 根据出现人和预订单判断是否为vip订单
     *
     * @param orderInfoModel
     * @return
     */
    private boolean isViper(OrderInfoModel orderInfoModel) {
        List<PassengerInfo> passengerList = orderInfoModel.getPassengerList();
        List<EmployeeInfoRequestBo> requestBo = passengerList.stream().filter(e -> StringUtils.isNotBlank(e.getUid()))
            .map(e -> new EmployeeInfoRequestBo(e.getUid(), e.getOrgId())).collect(Collectors.toList());
        requestBo.add(new EmployeeInfoRequestBo(orderInfoModel.getUid(), orderInfoModel.getDeptId()));
        List<EmployeeInfoResponseBo> employeeList = organizationEmployeeClientLoader.listEmployeeInfo(requestBo);
        return employeeList.stream().anyMatch(e -> e.getVipLevel() != null && e.getVipLevel() > 0);
    }

    /**
     * 消耗出差申请 或回滚出差神
     *
     * @param orderInfoModel
     */
    private void useApplyTrip(OrderInfoModel orderInfoModel, boolean isUse) {
        if (orderInfoModel == null) {
            addElkInfoLog("订单信息为空，无需处理出差申请单");
            return;
        }
        addElkInfoLog("进入出差申请单流程");
        // 因公才消耗
        if (!PubOwnEnum.PUB.name().equals(orderInfoModel.getCorpPayType())) {
            addElkInfoLog("因私无需处理出差申请单");
            return;
        }
        if (StringUtils.isBlank(orderInfoModel.getApplyNo())) {
            addElkInfoLog("无出差申请单号，无需处理出差申请单");
            return;
        }
        UseApplyTripTrafficRequest hotelRequest = new UseApplyTripTrafficRequest();
        hotelRequest.setTrafficType(EApplyTripTrafficType.HOTEL.getValue());
        hotelRequest.setReturnType(EApplyTripTrafficReturnType.HOTEL.getValue());
        hotelRequest.setVerifyType(LineVerifyTypeEnum.ALL_TYPE.getCode());
        hotelRequest.setBizOperation(isUse ? ApplyTripStockOrderStatus.SUBMIT.getValue() : ApplyTripStockOrderStatus.SUBMIT_FAILED.getValue());

        hotelRequest.setStartDate(orderInfoModel.getRoomInfo().getCheckInDate());
        hotelRequest.setEndDate(orderInfoModel.getRoomInfo().getCheckOutDate());
        //间夜数=房间数*夜数
        Integer quantity = orderInfoModel.getRoomInfo().getQuantity();
        Integer nightCount = DateUtil.betweenDay(DateUtil.stringToDate(orderInfoModel.getRoomInfo().getCheckInDate(), DateUtil.DF_YMD), DateUtil.stringToDate(orderInfoModel.getRoomInfo().getCheckOutDate(), DateUtil.DF_YMD));
        hotelRequest.setHotelCount(quantity * nightCount);
        hotelRequest.setEndCityCode(String.valueOf(orderInfoModel.getHotelInfo().getCityId()));

        hotelRequest.setOrderId(String.valueOf(orderInfoModel.getOrderId()));
        hotelRequest.setApplyNo(orderInfoModel.getApplyNo());
        hotelRequest.setTrafficId(orderInfoModel.getTrafficId());

        // 因公-混付 = 公司支付金额
        // 因公-公付 = 订单总金额
        // 因公-个付 | 前台现付 = 0
        // 只有公司支付部分需要进行预算管控去消耗出差金额
        if (PayTypeEnum.MIXPAY.getType().equalsIgnoreCase(orderInfoModel.getPayType())) {
            hotelRequest.setAmount(orderInfoModel.getAPayAmount());
        } else if (PayTypeEnum.ACCNT.getType().equalsIgnoreCase(orderInfoModel.getPayType())) {
            hotelRequest.setAmount(orderInfoModel.getTotalAmount());
        } else {
            hotelRequest.setAmount(BigDecimal.ZERO);
        }

        if (orderInfoModel.getPassengerList() != null) {
            List<String> uidList = orderInfoModel.getPassengerList()
                    .stream()
                    .map(x -> StringUtils.isBlank(x.getUid()) ? x.getNoEmployeeId() : x.getUid())
                    .collect(Collectors.toList());
            hotelRequest.setUid(uidList);
        }
        hotelRequest.setBizKey(hotelRequest.getOrderId().concat(StrUtil.COLON).concat(hotelRequest.getBizOperation()));

        addElkInfoLog((isUse ? "消耗" : "还原") + "出差申请request:" + JsonUtils.toJsonString(hotelRequest));
        OcApplyTripControlRecord controlRecord = new OcApplyTripControlRecord();
        if (isUse) {
            controlRecord.setScene(hotelRequest.getBizOperation());
            controlRecord.setOperationType(ApplyTripControlOperationTypeEnum.OCCUPY.getCode());
            controlRecord.setControlType(ApplyTripControlTypeEnum.CONTROL_ALL.getCode());

            boolean result = applyTripClientLoader.updateApplyTripUseStatusAndRecord(hotelRequest, controlRecord);
            if (!result) {
                throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_CONSUME_TRAVEL_APPLICATION);
            }
        } else {
            controlRecord.setScene(hotelRequest.getBizOperation());
            controlRecord.setOperationType(ApplyTripControlOperationTypeEnum.RELEASE.getCode());
            controlRecord.setControlType(ApplyTripControlTypeEnum.CONTROL_ALL.getCode());

            boolean result = applyTripClientLoader.rollbackApplyTripUseStatusAndRecord(hotelRequest, controlRecord);
            if (!result) {
                throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_RESTORE_TRAVEL_APPLICATION);
            }
        }
    }

    /**
     * 出行人检测更新落地
     */
    private void checkPassenger(OrderInfoModel orderInfo) {
        try {
            addElkInfoLog("进入更新出行人流程");
            List<PassengerVo> passengerVoList = new ArrayList<>();
            for (OrderInfoModel.PassengerInfo passenger : orderInfo.getPassengerList()) {
                PassengerVo passengerVo = new PassengerVo();
                PassengerVo.Birth birth = new PassengerVo.Birth();
                birth.setValue(passenger.getBirthday());
                passengerVo.setBirth(birth);
                passengerVo.setGender(passenger.getGender());

                // 不为en 的时候才更新中文名
                if (!"en".equals(passenger.getLanguage())) {
                    passengerVo.setName(passenger.getName());
                }
                passengerVo.setPassport(passenger.getPassport());
                passengerVo.setNationality(passenger.getNationality());
                passengerVo.setCorp(passenger.getCorp());
                passengerVo.setOrgId(passenger.getOrgId());
                passengerVo.setNoEmployeeId(passenger.getNoEmployeeId());
                passengerVo.setUid(passenger.getUid());
                // 手机号
                MobilePhoneVo mobilePhoneVo = new MobilePhoneVo();
                mobilePhoneVo.setCountryCode(passenger.getCountryCode());
                mobilePhoneVo.setValue(passenger.getMobilePhone());
                passengerVo.setTel(mobilePhoneVo);
                passengerVo.setRelationId(passenger.getRelationId());
                passengerVo.setRelationFlag(passenger.getRelationFlag());
                passengerVo.setSurname(passenger.getSurname());
                passengerVo.setGivenname(passenger.getGivenname());
                passengerVo.setFullEnName(passenger.getFullEnName());
                passengerVo.setFullName(passenger.getFullName());
                passengerVoList.add(passengerVo);
            }
            CheckPassengerRequest checkPassengerRequest = new CheckPassengerRequest();
            checkPassengerRequest.setPassengerVos(passengerVoList);
            checkPassengerRequest.setUid(orderInfo.getUid());

            List<PassengerVo> passengerVos = passengerClientLoader.checkPassenger(checkPassengerRequest);
            if (passengerVoList.size() != passengerVos.size()) {
                log.error("出行人信息更新失败");
            }
            // 反写uid 和noEmployeeId
            orderInfo.getPassengerList().forEach(passenger -> passengerVos.forEach(passengerVo -> {
                if ((StringUtils.isNotBlank(passenger.getName()) && passenger.getName().equals(passengerVo.getName()))
                    || (StringUtils.isNotBlank(passenger.getPassport())
                        && passenger.getPassport().equalsIgnoreCase(passengerVo.getPassport()))) {
                    passenger.setUid(passengerVo.getUid());
                    passenger.setNoEmployeeId(passengerVo.getNoEmployeeId());
                }
            }));
            addElkInfoLog("更新入住人信息：%s", JsonUtils.toJsonString(orderInfo.getPassengerList()));
        } catch (Exception e) {
            addElkInfoLog("更新出行人失败：%s", e.getMessage());
        }
    }

    /**
     * 轮询获取下单接口结果
     *
     * @param request
     * @return
     */
    public GetSaveOrderResultResponseVO getSaveOrderResult(GetSaveOrderResultRequestVO request) {
        SaveOrderResponseVo saveOrderResponseVo = orderInfoCacheManager.getSaveOrderResult(request.getOrderId());
        if (Objects.isNull(saveOrderResponseVo)) {
            saveOrderResponseVo = saveOrderResult(request.getOrderId(), HotelResponseCodeEnum.SAVE_ORDER_STATRT_ERROR,
                new SaveOrderResponseVo());
        }
        GetSaveOrderResultResponseVO resultResponseVO = new GetSaveOrderResultResponseVO();
        resultResponseVO.setErrorCode(saveOrderResponseVo.getErrorCode());
        resultResponseVO.setErrorMsg(saveOrderResponseVo.getErrorMsg());
        resultResponseVO.setPayTokenInfo(saveOrderResponseVo.getPayTokenInfo());

        if (String.valueOf(HotelResponseCodeEnum.SAVE_ORDER_STATRT_ERROR.code())
            .equals(saveOrderResponseVo.getErrorCode())
            || String.valueOf(HotelResponseCodeEnum.SAVE_ORDER_DOING_ERROR.code())
                .equals(saveOrderResponseVo.getErrorCode())) {
            resultResponseVO.setNeedPolling(true);
        }

        return resultResponseVO;
    }

    /**
     * 下单接口日志tag
     *
     * @param request
     * @return
     */
    private LogTags getLogTags(OrderInfoModel request) {
        LogTags logTags = new LogTags();
        if (Objects.isNull(request)) {
            return logTags;
        }

        logTags.setOrderId(request.getOrderId());
        logTags.setCorpId(request.getCorpId());
        logTags.setUid(request.getUid());
        logTags.setSupplierCode(request.getSupplierCode());
        logTags.add(LogTagConstants.HOTEL_ID, Optional.ofNullable(request.getHotelInfo())
            .map(OrderInfoModel.HotelInfo::getHotelId).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.ROOM_ID, Optional.ofNullable(request.getRoomInfo())
            .map(OrderInfoModel.RoomInfo::getRoomId).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.POLICY, Optional.ofNullable(request.getPolicyId()).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.APPROVAL_NO,
            Optional.ofNullable(request.getApplyNo()).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.TRAVEL_NO,
            Optional.ofNullable(request.getTrafficId()).map(String::valueOf).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.BOOKING_CHANNEL,
            Optional.ofNullable(request.getBookingChannel()).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.CORP_PAY_TYPE,
            Optional.ofNullable(request.getCorpPayType()).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.RC_TYPE,
            Objects.nonNull(request.getRcInfo()) ? LogTagConstants.SUCCESS : LogTagConstants.FAILE);
        logTags.add(LogTagConstants.AGEN_UID, Optional.ofNullable(request.getAgentUid()).orElse(LogTagConstants.FAILE));

        logTags.add(LogTagConstants.PAY_TYPE, Optional.ofNullable(request.getPayType()).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.DELIVERY_TYPE,
            Optional.ofNullable(request.getDeliveryType()).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.BALANCE_TYPE, Optional.ofNullable(request.getRoomInfo())
            .map(OrderInfoModel.RoomInfo::getBalanceType).orElse(LogTagConstants.FAILE));
        logTags.add(LogTagConstants.HOTEL_TYPE, Optional.ofNullable(request.getRoomInfo())
            .map(OrderInfoModel.RoomInfo::getHotelType).orElse(LogTagConstants.FAILE));
        return logTags;
    }


    /**
     * 超标检查
     */
    private boolean travelExceed(OrderInfoModel orderInfo) {
        try{
            log.info("+++因公，超标检查，TravelStandardToken：[{}]", orderInfo.getTravelStandardToken());
            boolean ret = true;
            // 非因公
            if (!PubOwnEnum.PUB.name().equals(orderInfo.getCorpPayType())) {
                log.info("++++++校验差旅合法性, 因私，直接返回true, corpPayType:{}", orderInfo.getCorpPayType());
                return ret;
            }

            if(StringUtils.isNotBlank(orderInfo.getTravelStandardToken())){
                ret = checkExceedStandardForNewVersion(orderInfo);
            }else{
                ret = checkExceedStandardForOldVersion(orderInfo);
            }
            return ret;
        }catch (Exception e){
            log.error("++++++++ 超标检查出错, 暂时放行, token:{}", orderInfo.getTravelStandardToken(), e);
            return true;
        }
    }

    private boolean checkExceedStandardForNewVersion(OrderInfoModel orderInfo) {
        TravelExceedInfo ret = travelCheckService.verifyTravelStandard(buildTravelCheckReq(orderInfo));
        if(ret.isExceed()){
            Set<String> rejectTypes = ret.getRejectTypes();
            if(CollectionUtils.isNotEmpty(rejectTypes) ){
                // 支持rc && rc传了
                if(rejectTypes.contains(ControlTypeEnum.C.getCode())){
                    log.info("++++--超标,选择rc, rcInfo:[{}]", JsonUtils.toJsonString(orderInfo.getRcInfo()));
                    return true;
                }
                // 支持混付，公付金额 <= 差标金额
                if(rejectTypes.contains(ControlTypeEnum.M.getCode())
                        && ret.getExceedAmount() != null
                        && orderInfo.getPPayAmount().compareTo(ret.getExceedAmount())>=0){ // 个人付款部分 >= 超标部分
                    log.info("++++--超标,选择混付, orderInfo.getAPayAmount:[{}], orderInfo.getAmountHigh:[{}]", orderInfo.getAPayAmount(), orderInfo.getAmountHigh());
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    /**
     * 检查订单是否超标
     * 备注：当前只处理公账支付情况, 随心付暂不处理
     *      true: 未超标
     *      false: 超标
     */
    private boolean checkExceedStandardForOldVersion(OrderInfoModel orderInfo) {
        if(orderInfo == null){
            return false;
        }
        String payType = orderInfo.getPayType();
        if(!PayTypeEnum.ACCNT.getType().equals(payType)){
            return true;
        }

        BigDecimal amountHigh = orderInfo.getAmountHigh();
        if(amountHigh == null || amountHigh.compareTo(BigDecimal.ZERO) == 0){
            return true;
        }

        if(orderInfo.getRcInfo() != null){
            return true;
        }

        BigDecimal averagePrice = getOrderAveragePrice(orderInfo);
        if(averagePrice.compareTo(amountHigh) <= 0){
            return true;
        }
        return false;
    }

    private BigDecimal getOrderAveragePrice(OrderInfoModel orderInfo){
        BigDecimal total = BigDecimal.ZERO;
        List<OrderInfoModel.RoomDailyInfo> roomlist = orderInfo.getRoomDailyInfoList();
        if(roomlist == null || roomlist.size() ==0){
            return total;
        }
        for(OrderInfoModel.RoomDailyInfo roomDetail : roomlist){
            total = total.add(roomDetail.getRoomPrice());
        }
        BigDecimal size = new BigDecimal(roomlist.size() + "");
        BigDecimal averagePrice = total.divide(size, 2, BigDecimal.ROUND_HALF_UP);
        return averagePrice;
    }

    /**
     * 构建差标请求
     */
    private VerifyTravelStandardRequest buildTravelCheckReq(OrderInfoModel orderModel) {
        String hotelId = orderModel.getHotelInfo().getHotelId();
        String roomId = orderModel.getRoomInfo().getRoomId();
        String travelStandardToken = orderModel.getTravelStandardToken();
        VerifyTravelStandardRequest req = new VerifyTravelStandardRequest();
        req.setTravelStandardToken(travelStandardToken);
        req.setBizType(BizTypeEnum.HOTEL.getCode());
        HotelVerifyRequest vr = new HotelVerifyRequest();
        vr.setHotelId(hotelId);
        vr.setResourcesId(hotelId + "#" + roomId);
        vr.setRoomId(roomId);
        // 传入均价验超标
        BigDecimal averageAmount = BigDecimal.ZERO;
        List<OrderInfoModel.RoomDailyInfo> roomDailyInfoList = orderModel.getRoomDailyInfoList();
        if(CollectionUtils.isNotEmpty(roomDailyInfoList)){
            for(OrderInfoModel.RoomDailyInfo roomDailyInfo : roomDailyInfoList){
                averageAmount = averageAmount.add(roomDailyInfo.getRoomPrice());
            }
        }
        averageAmount = averageAmount.divide(new BigDecimal(roomDailyInfoList.size()), RoundingMode.HALF_UP);
        if (Boolean.TRUE.equals(orderModel.getOverseasHotelControlIncludeExtraTax()) && orderModel.getAvgExtraTax() != null) {
            averageAmount = averageAmount.add(orderModel.getAvgExtraTax());
        }
        vr.setPrice(averageAmount);
        vr.setServiceFee(orderModel.getAccountPayServiceFee());
        vr.setStar(orderModel.getHotelInfo().getStar());
        vr.setStarLicence(orderModel.getHotelInfo().getIsStarLicence());
        vr.setBrandId(orderModel.getHotelInfo().getBrandId());
        vr.setOldProcess(true);
        vr.setDailyPriceList(buildDailyPriceList(roomDailyInfoList, orderModel.getOverseasHotelControlIncludeExtraTax(), orderModel.getAvgExtraTax()));
        vr.setPriceControlStrategy(orderModel.getPriceControlStrategyEnum() == null ? PriceControlStrategyEnum.AVG_PRICE.getCode() : orderModel.getPriceControlStrategyEnum().getCode());
        req.setHotelList(Lists.newArrayList(vr));
        String travelStandardMark = travelCheckService.getTravelStandardMark(travelStandardToken);
        Integer levelStep = travelCheckService.parseLevelStep(travelStandardMark);
        vr.setStepLevel(levelStep);
        return req;
    }
    
    private List<HotelVerifyRequest.DailyPrice> buildDailyPriceList(List<OrderInfoModel.RoomDailyInfo> dailyPriceList, Boolean overseasHotelControlIncludeExtraTax, BigDecimal avgExtraTax) {
        if (CollectionUtils.isEmpty(dailyPriceList)) {
            return null;
        }
        
        List<HotelVerifyRequest.DailyPrice> resultList = new ArrayList<>();
        for (OrderInfoModel.RoomDailyInfo dailyPrice : dailyPriceList) {
            if (dailyPrice == null || StringUtils.isBlank(dailyPrice.getEffectDate()) || dailyPrice.getRoomPrice() == null) {
                continue;
            }
            
            BigDecimal price = dailyPrice.getRoomPrice();
            if (Boolean.TRUE.equals(overseasHotelControlIncludeExtraTax) && avgExtraTax != null) {
                price = price.add(avgExtraTax);
            }
            
            resultList.add(HotelVerifyRequest.DailyPrice.builder()
                    .date(dailyPrice.getEffectDate())
                    .price(price).build());
        }
        
        return resultList;
    }

    private Boolean getTravelerNameEnabled(String tenantId) {
        log.info("国内酒店 启用TravelerNam tenantId 为：{}", tenantId);
        String enableTravelerNameStr = hotelApollo.getAppProperty("hotel.travelerName.enabled.new",
                "{\"ctirp\": false}");

        if (StringUtils.isEmpty(enableTravelerNameStr)
                || StringUtils.isEmpty(tenantId)) {
            return false;
        }
        Map<String, Boolean> map = JsonUtils.parseMap(enableTravelerNameStr);
        return map.getOrDefault(tenantId, false);
    }
}
