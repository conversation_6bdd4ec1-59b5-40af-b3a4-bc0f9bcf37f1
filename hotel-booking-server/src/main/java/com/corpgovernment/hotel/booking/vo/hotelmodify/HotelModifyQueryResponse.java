package com.corpgovernment.hotel.booking.vo.hotelmodify;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * @author: lilayzzz
 * @since: 2023/12/14
 * @description:
 */
@Data
public class HotelModifyQueryResponse {

    /**
     * 申请修改单列表
     */
    private List<HotelModifyDetail> applyList;
    /**
     * 可提前离店的日期
     */
    private CanApplyDate canApplyDate;

    @Data
    public static class CanApplyDate{

        /**
         * 时间
         */
        private List<String> canModifyDate;
    }

    @Data
    public static class HotelModifyDetail{
        /**
         * 提前离店申请单Id
         */
        private String applyId;

        /**
         * 申请时间
         */

        private String applyTime;
        /**
         * 取消原因
         */
        private String cancelReason;
        /**
         * 申请状态
         */
        private Integer applyStatus;

        /**
         * 原订单信息
         */
        private OrderInfo originOrderInfo;
        /**
         * 现订单信息
         */
        private OrderInfo nowOrderInfo;
        /**
         * 处理状态明细
         */
        private List<StatusItem> statusItemList;

        /**
         * 退款金额
         */
        private BigDecimal refundAmount;
        /**
         * 原订单信息 新字段 优先以该字段为准
         */
        private List<OrderInfo> originOrderInfoList;

        /**
         * 现订单信息 新字段 优先以该字段为准
         */
        private List<OrderInfo> nowOrderInfoList;
    }


    @Data
    public static class OrderInfo{
        /**
         * 间夜数
         */
        private Integer roomNight;
        /**
         * 入住时间
         */
        private String checkInTime;

        /**
         * 离店时间
         */
        private String checkOutTime;
        /**
         * 房间信息
         */
        private List<roomInfo> roomInfoList;

    }
    @Data
    public static class roomInfo{
        /**
         * 房间信息
         */
        private List<SingleRoomInfo> roomInfoList;
        /**
         * 数量
         */
        private Integer quantity;
        /**
         * 入住日期
         */
        private String date;
    }

    @Data
    public static class SingleRoomInfo{
        /**
         * 入住人id
         */
        Long clientInfoId;
        /**
         * 入住人姓名
         */
        String name;

        /**
         * 房间索引
         */
        private Integer roomIndex;
    }
    @Data
    public static class StatusItem{
        /**
         * 状态：【1：已提交 2：待处理 3：与酒店协调中 4：修改成功 5：修改失败 6：修改取消,7 修改取消中】
         */
        private Integer status;
        /**
         * 处理时间（格式yyyy-MM-dd HH:mm:ss ）
         */
        private String statusTime;
        /**
         * 处理时间（格式yyyy-MM-dd HH:mm:ss ）
         */
        private String statusTimeUTC;
        /**
         * 根据业务流转对状态排序（数字越大的状态，在业务时间线越靠后，如 提交《处理《修改成功； 修改成功 = 修改失败）
         */
        private Integer statusSortIndex;
    }
}
