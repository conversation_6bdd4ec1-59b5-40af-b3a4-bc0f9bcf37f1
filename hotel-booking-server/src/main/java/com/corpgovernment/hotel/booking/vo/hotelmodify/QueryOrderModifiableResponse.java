package com.corpgovernment.hotel.booking.vo.hotelmodify;

import java.util.List;

import lombok.Data;

/**
 * @author: lilayzzz
 * @since: 2023/12/14
 * @description:
 */
@Data
public class QueryOrderModifiableResponse {

    /**
     * 是否可以提前离店，是 T，否 F
     * 与canEarlyDepartureAheadLimit任一个为T，即表示可以申请提前离店
     */
    private String canEarlyDeparture;
    /**
     * 未过最晚时间 是否可以提前离店， 是 T，否 F
     */
    private String canEarlyDepartureAheadLimit;
    /**
     * 不可修改原因编码
     */
    private Integer unmodifiableReasonCode;
    /**
     * 不可修改原因描述
     */
    private String unmodifiableReasonDesc;
    /**
     * 可修改原因列表：供前端select框使用
     */
    private List<ModifyReason> modifyReasonList;
    private ResponseStatus status;

    @Data
    public class ResponseStatus{
        private Boolean success;

        private Integer errorCode;

        private String errorMessage;
    }
    @Data
    public static class ModifyReason{
        /**
         * 修改原因编码：ITINERARY_CHANGE-行程改变 TRANSPORTATION_PROBLEM-交通延誤/取消 BOOKING_ERROR-预定错误 HEALTH_ILLNESS-身体不适 HOTEL_ISSUE-酒店原因
         */
        private String reasonCode;
        /**
         * 修改原因描述
         */
        private String reasonDesc;
        /**
         * 是否可补充原因【用于前端展示扩展框，供用户进一步描述原因】默认true
         */
        private Boolean extensible;

    }

    /**
     * 判断是否提前离店
     * @param type
     * @return
     */
    public Boolean checkModify(String type){
        return "T".equalsIgnoreCase(type);
    }
}
