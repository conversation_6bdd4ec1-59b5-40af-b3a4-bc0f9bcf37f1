package com.corpgovernment.hotel.booking.request;

import com.corpgovernment.api.applytrip.enums.ApplyTripEmployeeEnum;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.hotel.booking.bo.CheckDuplicateBookingBo;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class CheckDuplicateBookingRequest {


    /**
     * 入住人信息
     */
    List<Passenger> passengers;

    /**
     * 最早入住时间
     */
    private Date checkInDate;

    /**
     * 最晚离店时间
     */
    private Date checkOutDate;

    /**
     * 因公因私模式 PUB因公、OWN因私
     */
    private String travelMode;

    public CheckDuplicateBookingBo convertBo(){
        check();
        CheckDuplicateBookingBo bo = new CheckDuplicateBookingBo();
        bo.setCheckInDate(checkInDate);
        bo.setCheckOutDate(checkOutDate);
        if(CollectionUtils.isNotEmpty(passengers)){
            // set uids
            List<String> uids = passengers.stream().filter(item -> !ApplyTripEmployeeEnum.EXTERNAL_EMPLOYEE.getCode().equals(item.getEmployeeType()))
                            .map(Passenger::getUid).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(uids)){
                bo.setUIds(uids);
            }

            // set nonEmployeeIds
            List<String> nonEmployeeIds = passengers.stream().filter(item -> ApplyTripEmployeeEnum.EXTERNAL_EMPLOYEE.getCode().equals(item.getEmployeeType()))
                            .map(Passenger::getNonEmployeeId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(nonEmployeeIds)){
                bo.setNonEmployeeIds(nonEmployeeIds);
            }
        }
        return bo;
    }

    public void check() {
        if(checkInDate == null){
            throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code(), "入住时间不能为空");
        }
        if(checkOutDate == null){
            throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code(), "离店时间不能为空");
        }
        if(CollectionUtils.isEmpty(passengers)){
            throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code(), "passengers is required");
        }
        for(Passenger passenger : passengers){
            passenger.check();
        }
    }


    @Data
    public static class Passenger {

        // 员工类型
        private Integer employeeType;

        // 员工类型时，必填
        private String uid;

        // 非员工主键ID
        private String nonEmployeeId;

        public void check(){
            ApplyTripEmployeeEnum employeeTypeEnum = ApplyTripEmployeeEnum.getByCode(employeeType);
            if(employeeTypeEnum == null){
                throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code(), "illegal employee type, value:" + employeeType);
            }
            // inner employee
            if(employeeTypeEnum != ApplyTripEmployeeEnum.EXTERNAL_EMPLOYEE && StringUtils.isBlank(uid)){
                throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code(), "非外部员工， uid不能为空");
            }
            // external employee
            if(employeeTypeEnum == ApplyTripEmployeeEnum.EXTERNAL_EMPLOYEE && StringUtils.isBlank(nonEmployeeId)){
                throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_PARAM_ERROR.code(), "外部员工， nonEmployeeId不能为空");
            }
        }

    }

}
