package com.corpgovernment.hotel.booking.service;

import cn.hutool.core.lang.Pair;
import com.corpgovernment.api.basic.dto.BasicCityInfoDto;
import com.corpgovernment.api.basic.dto.CityCountyResponse;
import com.corpgovernment.api.basic.dto.CityLocationResponse;
import com.corpgovernment.api.basic.enums.GeographyTypeEnum;
import com.corpgovernment.api.basic.enums.HotelAreaConfigurationEnum;
import com.corpgovernment.api.basic.enums.MsgConfig;
import com.corpgovernment.api.hotel.product.model.response.HotelForAppNewVo;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.vo.AveragePriceSet;
import com.corpgovernment.api.travelstandard.vo.AverageStarSet;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.api.travelstandard.vo.StarCeiling;
import com.corpgovernment.api.travelstandard.vo.StarFloor;
import com.corpgovernment.client.ManagementClientUtil;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.TravelStandardControlTypeEnum;
import com.corpgovernment.converter.converterImpl.PassengerParamV1Converter;
import com.corpgovernment.converter.converterImpl.QueryParamV1Converter;
import com.corpgovernment.converter.factoryImpl.PassengerParamConverterFactoryImpl;
import com.corpgovernment.converter.factoryImpl.QueryParamConverterFactoryImpl;
import com.corpgovernment.converter.model.passenger.PassengerParamModel;
import com.corpgovernment.converter.model.queryparam.QueryParamModel;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.SnapShotDTO;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardRequest;
import com.corpgovernment.dto.travelstandard.request.PassengerGroupDTO;
import com.corpgovernment.dto.travelstandard.request.PassengerRequest;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.CohabitRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.FloatPriceRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.OffPeakSeasonRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.PriceRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.RuleChainVO;
import com.corpgovernment.dto.travelstandard.response.rule.StarRuleVO;
import com.corpgovernment.hotel.booking.bo.HotelRuleBo;
import com.corpgovernment.hotel.product.dataloader.soa.BasicDataClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BookingCoreClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CommonSupplierLoader;
import com.corpgovernment.hotel.product.model.gaode.AroundPlaceRespDto;
import com.corpgovernment.mapping.enums.HotelStar;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/11
 */
@Service
@Slf4j
public class TokenAccessService {

    private final Id countyOrDistrictGetMetricId = Metrics.REGISTRY.createId("country_or_district_get");

    private final Id travelStandardTokenRefreshMetricId = Metrics.REGISTRY.createId("travel_standard_token_refresh");

    @Autowired
    private BasicDataClientLoader basicDataClientLoader;
    @Autowired
    private BookingCoreClientLoader bookingCoreClientLoader;
    @Autowired
    private ManagementClientUtil managementClientUtil;
    @Autowired
    private CommonSupplierLoader commonSupplierLoader;

    private static final long APPLY_TRIP_MOCK_CITY_ID_LONG = -9999;
    private static final String APPLY_TRIP_MOCK_CITY_ID_STRING = "-9999";

    public List<HotelForAppNewVo> getTravelStandardDesc(String travelStandardToken, String cityId) {
        if (StringUtils.isBlank(travelStandardToken)) {
            log.info("接入token失败，差标token为空");
            return null;
        }

        // 查询差标
        TravelStandardResponse travelStandard = getTravelStandard(travelStandardToken);
        log.info("查询差标，travelStandard：{}", JsonUtils.toJsonString(travelStandard));
        if (travelStandard == null || travelStandard.getRuleChain() == null) {
            log.info("接入token失败，查询的差标为空");
            return null;
        }

        // 禁止预定
        Boolean bookable = travelStandard.getRuleChain().getBookable();
        if (Boolean.FALSE.equals(bookable)) {
            log.info("接入token成功，禁止预定");
            HotelForAppNewVo hotelForAppNewVo = new HotelForAppNewVo();
            HotelForAppNewVo.RcDetailVo rcDetailVo = new HotelForAppNewVo.RcDetailVo();
            rcDetailVo.setContent("禁止预定");
            rcDetailVo.setTitle("预定管控");
            hotelForAppNewVo.setPolicyDetail(Collections.singletonList(rcDetailVo));
            return Collections.singletonList(hotelForAppNewVo);
        }

        // 获取所有差标
        List<TravelStandardRuleVO> ruleList = travelStandard.getRuleChain().getRuleList();
        Map<String, HotelRuleBo> hotelRuleMap = new HashMap<>();
        assembleHotelRule(new HotelRuleBo(), hotelRuleMap, ruleList);
        log.info("所有差标，hotelRuleMap：{}", JsonUtils.toJsonString(hotelRuleMap));
        BasicCityInfoDto basicCityInfo = basicDataClientLoader.getBasicCityInfo(cityId);
        if (CollectionUtils.isEmpty(ruleList) || CollectionUtils.isEmpty(hotelRuleMap)) {
            HotelForAppNewVo hotelForAppNewVo = getNoLimitDesc().get(0);
            if (basicCityInfo != null) {
                LinkedList<HotelForAppNewVo.RcDetailVo> policyDetail = new LinkedList<>(hotelForAppNewVo.getPolicyDetail());
                HotelForAppNewVo.RcDetailVo rcDetailVo = new HotelForAppNewVo.RcDetailVo();
                rcDetailVo.setTitle("适用城市");
                rcDetailVo.setContent(basicCityInfo.getCityName());
                policyDetail.addFirst(rcDetailVo);
                hotelForAppNewVo.setPolicyDetail(policyDetail);
            }
            return Collections.singletonList(hotelForAppNewVo);
        }

        // 组装
        LinkedList<HotelForAppNewVo> hotelForAppNewVoList = new LinkedList<>();
        HotelForAppNewVo firstHotelForAppNewVo = null;
        for (HotelRuleBo hotelRuleBo : hotelRuleMap.values()) {
            if (hotelRuleBo == null) {
                continue;
            }
            HotelForAppNewVo hotelForAppNewVo = new HotelForAppNewVo();
            hotelForAppNewVo.setTitle(hotelRuleBo.getCityName());
            List<HotelForAppNewVo.RcDetailVo> rcDetailVoList = new ArrayList<>();

            // 优先级
            BigDecimal maxPrice = null;
            CohabitRuleVO cohabitRuleVO = hotelRuleBo.getCohabitRuleVO();
            OffPeakSeasonRuleVO offPeakSeasonRuleVO = hotelRuleBo.getOffPeakSeasonRuleVO();
            PriceRuleVO priceRuleVO = hotelRuleBo.getPriceRuleVO();
            StarRuleVO starRuleVO = hotelRuleBo.getStarRuleVO();
            List<String> rejectTypeList = new ArrayList<>();
            if (offPeakSeasonRuleVO != null) {
                maxPrice = offPeakSeasonRuleVO.getMaxPrice();
                String[] rejectTypes = offPeakSeasonRuleVO.getRejectTypes();
                if (CollectionUtils.isNotEmpty(rejectTypes)) {
                    rejectTypeList = Arrays.asList(rejectTypes);
                }
            } else if (cohabitRuleVO != null) {
                maxPrice = cohabitRuleVO.getMaxPrice();
                String[] rejectTypes = cohabitRuleVO.getRejectTypes();
                if (CollectionUtils.isNotEmpty(rejectTypes)) {
                    rejectTypeList = Arrays.asList(rejectTypes);
                }
            } else if (priceRuleVO != null) {
                maxPrice = priceRuleVO.getMaxPrice();
                String[] rejectTypes = priceRuleVO.getRejectTypes();
                if (CollectionUtils.isNotEmpty(rejectTypes)) {
                    rejectTypeList = Arrays.asList(rejectTypes);
                }
            }

            // 其他城市特殊处理
            if (StringUtils.equalsIgnoreCase(hotelRuleBo.getCityName(), "其他城市") && StringUtils.equalsIgnoreCase(hotelRuleBo.getCityType(), String.valueOf(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType()))) {
                if (basicCityInfo != null && StringUtils.isNotBlank(basicCityInfo.getCityName())) {
                    hotelRuleBo.setCityName(basicCityInfo.getCityName());
                }
            }

            // 适用城市
            HotelForAppNewVo.RcDetailVo rcDetailVo = new HotelForAppNewVo.RcDetailVo();
            if (StringUtils.isNotBlank(hotelRuleBo.getCityName())) {
                rcDetailVo.setContent(hotelRuleBo.getCityName());
            } else {
                rcDetailVo.setContent(Optional.ofNullable(basicCityInfo).map(BasicCityInfoDto::getCityName).orElse(null));
            }
            rcDetailVo.setTitle("适用城市");
            rcDetailVoList.add(rcDetailVo);
            // 可订价格
            rcDetailVo = new HotelForAppNewVo.RcDetailVo();
            if (maxPrice == null) {
                rcDetailVo.setContent("不限");
            } else {
                rcDetailVo.setContent("¥" + maxPrice.stripTrailingZeros().toPlainString() + "/间夜");
            }
            rcDetailVo.setTitle("可订价格");
            rcDetailVoList.add(rcDetailVo);
            // 可订星级
            rcDetailVo = new HotelForAppNewVo.RcDetailVo();
            if (starRuleVO == null || starRuleVO.getMaxStar() == null) {
                rcDetailVo.setContent("不限");
            } else {
                rcDetailVo.setContent(String.valueOf(starRuleVO.getMaxStar()));
            }
            rcDetailVo.setTitle("可订星级");
            rcDetailVoList.add(rcDetailVo);
            // 超标管控方式
            for (String rejectType : rejectTypeList) {
                TravelStandardControlTypeEnum travelStandardControlTypeEnum = TravelStandardControlTypeEnum.getEnumByCode(rejectType);
                rcDetailVo = new HotelForAppNewVo.RcDetailVo();
                rcDetailVo.setContent(travelStandardControlTypeEnum.getName());
                rcDetailVo.setTitle("超标管控方式");
                rcDetailVoList.add(rcDetailVo);
            }
            hotelForAppNewVo.setPolicyDetail(rcDetailVoList);
            if (StringUtils.equalsIgnoreCase(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType().toString(), hotelRuleBo.getCityType()) && StringUtils.equalsIgnoreCase(cityId, hotelRuleBo.getCityId())) {
                firstHotelForAppNewVo = hotelForAppNewVo;
            } else {
                hotelForAppNewVoList.add(hotelForAppNewVo);
            }
        }
        if (firstHotelForAppNewVo != null) {
            hotelForAppNewVoList.addFirst(firstHotelForAppNewVo);
        }
        return hotelForAppNewVoList;
    }

    public HotelControlVo accessToken(String travelStandardToken) {
        if (StringUtils.isBlank(travelStandardToken)) {
            log.info("接入token失败，差标token为空");
            return null;
        }
        // 查询差标
        TravelStandardResponse travelStandard = getTravelStandard(travelStandardToken);
        log.info("查询差标，travelStandard：{}", JsonUtils.toJsonString(travelStandard));
        if (travelStandard == null) {
            log.info("接入token失败，查询的差标为空");
            return null;
        }
        // 组装数据
        return assembleHotelControl(travelStandard, null);
    }

    public HotelControlVo accessAndRefreshToken(String travelStandardToken, String cityId, Double lat, Double lon) {
        if (StringUtils.isBlank(travelStandardToken)) {
            log.info("接入token失败，差标token为空");
            return null;
        }
        TravelStandardResponse travelStandard;
        // 获取地理信息
        Pair<String, String> districtIdOrCountyIdPair = getDistrictIdOrCountyIdPair(cityId, lat, lon);
        // 刷新差标
        if (districtIdOrCountyIdPair != null) {
            travelStandard = refreshToken(travelStandardToken, cityId, districtIdOrCountyIdPair.getKey(), districtIdOrCountyIdPair.getValue());
        }
        // 兜底查询老差标
        else {
            travelStandard = getTravelStandard(travelStandardToken);
        }
        // 组装数据
        return assembleHotelControl(travelStandard, cityId);
    }

    private List<HotelForAppNewVo> getNoLimitDesc() {
        HotelForAppNewVo hotelForAppNewVo = new HotelForAppNewVo();
        List<HotelForAppNewVo.RcDetailVo> rcDetailVoList = new ArrayList<>();
        HotelForAppNewVo.RcDetailVo rcDetailVo = new HotelForAppNewVo.RcDetailVo();
        rcDetailVo.setContent("不限");
        rcDetailVo.setTitle("可订价格");
        rcDetailVoList.add(rcDetailVo);
        rcDetailVo = new HotelForAppNewVo.RcDetailVo();
        rcDetailVo.setContent("不限");
        rcDetailVo.setTitle("可订星级");
        rcDetailVoList.add(rcDetailVo);
        hotelForAppNewVo.setPolicyDetail(rcDetailVoList);
        return Collections.singletonList(hotelForAppNewVo);
    }

    private TravelStandardResponse getTravelStandard(String travelStandardToken) {
        if (StringUtils.isBlank(travelStandardToken)) {
            return null;
        }
        // 查询差标
        GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
        getTravelStandardByTokenRequest.setTokenList(Collections.singletonList(travelStandardToken));
        List<TravelStandardResponse> travelStandardResponseList = managementClientUtil.getTravelStandardByToken(getTravelStandardByTokenRequest);
        log.info("查询差标结果 travelStandardResponseList={}", JsonUtils.toJsonString(travelStandardResponseList));
        return getFinalTravelStandardResponse(travelStandardResponseList);
    }

    private HotelControlVo assembleHotelControl(TravelStandardResponse travelStandardResponse, String cityId) {
        log.info("组装差标 travelStandardResponse:{} cityId:{}", travelStandardResponse, cityId);
        if (travelStandardResponse == null || travelStandardResponse.getRuleChain() == null) {
            return null;
        }
        HotelControlVo hotelControlVo = new HotelControlVo();

        RuleChainVO ruleChain = travelStandardResponse.getRuleChain();
        List<TravelStandardRuleVO> ruleList = ruleChain.getRuleList();

        // 获取差标开关
        if (Boolean.TRUE.equals(ruleChain.getBookable())) {
            hotelControlVo.setHotelProductSwitch("E");
        } else {
            hotelControlVo.setHotelProductSwitch("N");
            return hotelControlVo;
        }
        if (CollectionUtils.isEmpty(ruleList)) {
            return HotelControlVo.getNotLimitVo();
        }

        // 获取最高优先级的差标
        HotelRuleBo hotelRuleBo = getHotelRuleBoByPriority(ruleList);
        log.info("获取最高优先级的差标，hotelRuleBo：{}", hotelRuleBo);
        if (hotelRuleBo == null) {
            return HotelControlVo.getNotLimitVo();
        }

        hotelControlVo.setCityId(hotelRuleBo.getCityId());
        hotelControlVo.setCityName(hotelRuleBo.getCityName());
        hotelControlVo.setCityType(hotelRuleBo.getCityType());
        // 文案
        if (travelStandardResponse.getTravelStandardToken().getOwnerType() != 3 && StringUtils.isNotBlank(cityId)) {
            if (!StringUtils.equalsIgnoreCase(hotelRuleBo.getCityType(), String.valueOf(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType()))
                    || !StringUtils.equalsIgnoreCase(cityId, hotelRuleBo.getCityId())) {
                hotelControlVo.setChangeDesc("当前酒店将使用【" + hotelRuleBo.getCityName() + "】的差标进行管控");
            }
        }

        // 星级差标
        assembleStarRule(hotelControlVo, hotelRuleBo.getStarRuleVO());
        // 均价差标和淡旺季差标
        assembleAvgPriceRule(hotelControlVo, hotelRuleBo);
        // 浮动差标
        assembleFloatPriceRule(hotelControlVo, hotelRuleBo.getFloatPriceRuleVO());
        return hotelControlVo;
    }

    private void assembleFloatPriceRule(HotelControlVo travelStandardBo, FloatPriceRuleVO floatPriceRuleVO) {
        if (travelStandardBo == null || floatPriceRuleVO == null) {
            return;
        }
        AveragePriceSet floatAveragePriceSet = new AveragePriceSet();
        travelStandardBo.setFloatAveragePriceSet(floatAveragePriceSet);
        if (floatPriceRuleVO.getMaxPrice() != null) {
            floatAveragePriceSet.setPriceCeiling(String.valueOf(floatPriceRuleVO.getMaxPrice()));
            floatAveragePriceSet.setCurrencyCode(floatPriceRuleVO.getCurrency());
            floatAveragePriceSet.setExchangeToken(floatPriceRuleVO.getExchangeToken());
            floatAveragePriceSet.setOriginalAmount(floatPriceRuleVO.getForeignPrice());
        }
        if (CollectionUtils.isNotEmpty(floatPriceRuleVO.getRejectTypes())) {
            travelStandardBo.setFloatControl(getControl(String.join(",", floatPriceRuleVO.getRejectTypes())));
        }
    }

    private void assembleAvgPriceRule(HotelControlVo travelStandardBo, HotelRuleBo hotelRuleBo) {
        if (travelStandardBo == null || hotelRuleBo == null || (hotelRuleBo.getOffPeakSeasonRuleVO() == null && hotelRuleBo.getPriceRuleVO() == null && hotelRuleBo.getCohabitRuleVO() == null)) {
            return;
        }
        AveragePriceSet averagePriceSet = new AveragePriceSet();
        travelStandardBo.setAveragePriceSet(averagePriceSet);
        averagePriceSet.setPriceFloor("0");
        averagePriceSet.setPriceCeiling("0");

        OffPeakSeasonRuleVO offPeakSeasonRuleVO = hotelRuleBo.getOffPeakSeasonRuleVO();
        CohabitRuleVO cohabitRuleVO = hotelRuleBo.getCohabitRuleVO();
        PriceRuleVO priceRuleVO = hotelRuleBo.getPriceRuleVO();
        // 淡旺季-同住-均价
        if (offPeakSeasonRuleVO != null) {
            AveragePriceSet offPeakSeasonSet = new AveragePriceSet();
            travelStandardBo.setOffPeakSeasonSet(offPeakSeasonSet);
            offPeakSeasonSet.setPriceFloor("0");
            offPeakSeasonSet.setPriceCeiling("0");
            if (offPeakSeasonRuleVO.getMaxPrice() != null) {
                averagePriceSet.setPriceCeiling(String.valueOf(offPeakSeasonRuleVO.getMaxPrice()));
                averagePriceSet.setCurrencyCode(offPeakSeasonRuleVO.getCurrency());
                averagePriceSet.setExchangeToken(offPeakSeasonRuleVO.getExchangeToken());
                averagePriceSet.setOriginalAmount(offPeakSeasonRuleVO.getForeignPrice());

                offPeakSeasonSet.setPriceCeiling(String.valueOf(offPeakSeasonRuleVO.getMaxPrice()));
                offPeakSeasonSet.setCurrencyCode(offPeakSeasonRuleVO.getCurrency());
                offPeakSeasonSet.setExchangeToken(offPeakSeasonRuleVO.getExchangeToken());
                offPeakSeasonSet.setOriginalAmount(offPeakSeasonRuleVO.getForeignPrice());
            }

            if (CollectionUtils.isNotEmpty(offPeakSeasonRuleVO.getRejectTypes())) {
                travelStandardBo.setControl(getControl(String.join(",", offPeakSeasonRuleVO.getRejectTypes())));
                travelStandardBo.setFloatControl(getControl(String.join(",", offPeakSeasonRuleVO.getRejectTypes())));
            }
        }
        else if (cohabitRuleVO != null) {
            if (cohabitRuleVO.getMaxPrice() != null) {
                averagePriceSet.setPriceCeiling(String.valueOf(cohabitRuleVO.getMaxPrice()));
                averagePriceSet.setCurrencyCode(cohabitRuleVO.getCurrency());
                averagePriceSet.setExchangeToken(cohabitRuleVO.getExchangeToken());
                averagePriceSet.setOriginalAmount(cohabitRuleVO.getForeignPrice());
            }
            if (CollectionUtils.isNotEmpty(cohabitRuleVO.getRejectTypes())) {
                travelStandardBo.setControl(getControl(String.join(",", cohabitRuleVO.getRejectTypes())));
            }
        }
        else if (priceRuleVO != null) {
            if (priceRuleVO.getMaxPrice() != null) {
                averagePriceSet.setPriceCeiling(String.valueOf(priceRuleVO.getMaxPrice()));
                averagePriceSet.setCurrencyCode(priceRuleVO.getCurrency());
                averagePriceSet.setExchangeToken(priceRuleVO.getExchangeToken());
                averagePriceSet.setOriginalAmount(priceRuleVO.getForeignPrice());
            }
            if (CollectionUtils.isNotEmpty(priceRuleVO.getRejectTypes())) {
                travelStandardBo.setControl(getControl(String.join(",", priceRuleVO.getRejectTypes())));
            }
        }
    }

    private void assembleStarRule(HotelControlVo travelStandardBo, StarRuleVO starRuleVO) {
        if (travelStandardBo == null || starRuleVO == null) {
            return;
        }
        AverageStarSet averageStarSet = new AverageStarSet();
        travelStandardBo.setAverageStarSet(averageStarSet);
        StarCeiling starCeiling = new StarCeiling();
        if (starRuleVO.getMaxStar() != null && starRuleVO.getMaxStar() >= 1 && starRuleVO.getMaxStar() <= 5) {
            starCeiling.setName(HotelStar.getDisplayHotelStar(starRuleVO.getMaxStar()));
            starCeiling.setValue(String.valueOf(starRuleVO.getMaxStar()));
        } else {
            starCeiling.setName("不限");
            starCeiling.setValue("0");
        }
        averageStarSet.setStarCeiling(starCeiling);

        StarFloor starFloor = new StarFloor();
        starFloor.setName("不限");
        starFloor.setValue("0");
        averageStarSet.setStarFloor(starFloor);
    }

    private HotelRuleBo getHotelRuleBoByPriority(List<TravelStandardRuleVO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }
        HotelRuleBo hotelRuleBo = new HotelRuleBo();
        // 判断是否有多个类型的差标
        Map<String, List<TravelStandardRuleVO>> travelStandardRuleVoListMap = ruleList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(this::getCityType));
        long cityTypeCount = travelStandardRuleVoListMap.keySet().stream().filter(StringUtils::isNotBlank).count();

        // 有一个差标
        if (cityTypeCount == 1) {
            assembleHotelRule(hotelRuleBo, new HashMap<>(), ruleList);
        }
        // 如果有多个类型的差标，选地级市差标，其余情况就一个差标
        else if (cityTypeCount > 1) {
            assembleHotelRule(hotelRuleBo, new HashMap<>(), travelStandardRuleVoListMap.get(String.valueOf(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType())));
        }

        if (hotelRuleBo.getCohabitRuleVO() == null && hotelRuleBo.getPriceRuleVO() == null && hotelRuleBo.getOffPeakSeasonRuleVO() == null && hotelRuleBo.getStarRuleVO() == null && hotelRuleBo.getFloatPriceRuleVO() == null) {
            return null;
        }
        return hotelRuleBo;
    }

    private void assembleHotelRule(HotelRuleBo hotelRuleBo, Map<String, HotelRuleBo> hotelRuleBoMap, List<TravelStandardRuleVO> ruleList) {
        if (hotelRuleBo == null || CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        for (TravelStandardRuleVO travelStandardRuleVO : ruleList) {
            if (travelStandardRuleVO == null) {
                continue;
            }
            String name = travelStandardRuleVO.getName();
            if (StringUtils.equalsIgnoreCase(name, "CohabitRule")) {
                CohabitRuleVO tmp = (CohabitRuleVO) travelStandardRuleVO;
                if (StringUtils.isBlank(tmp.getCityType()) || tmp.getCityId() == null) {
                    continue;
                }
                hotelRuleBo.setCohabitRuleVO(tmp);
                hotelRuleBo.setCityId(String.valueOf(tmp.getCityId()));
                hotelRuleBo.setCityType(tmp.getCityType());
                hotelRuleBo.setCityName(tmp.getCityName());

                HotelRuleBo tmpBo = hotelRuleBoMap.getOrDefault(tmp.getCityType()+tmp.getCityId(), new HotelRuleBo());
                tmpBo.setCohabitRuleVO(tmp);
                tmpBo.setCityId(String.valueOf(tmp.getCityId()));
                tmpBo.setCityType(tmp.getCityType());
                tmpBo.setCityName(tmp.getCityName());
                hotelRuleBoMap.put(tmp.getCityType()+tmp.getCityId(), tmpBo);
            } else if (StringUtils.equalsIgnoreCase(name, "PriceRule")) {
                PriceRuleVO tmp = (PriceRuleVO) travelStandardRuleVO;
                if (StringUtils.isBlank(tmp.getCityType()) || tmp.getCityId() == null) {
                    continue;
                }
                hotelRuleBo.setPriceRuleVO(tmp);
                hotelRuleBo.setCityId(String.valueOf(tmp.getCityId()));
                hotelRuleBo.setCityType(tmp.getCityType());
                hotelRuleBo.setCityName(tmp.getCityName());

                HotelRuleBo tmpBo = hotelRuleBoMap.getOrDefault(tmp.getCityType()+tmp.getCityId(), new HotelRuleBo());
                tmpBo.setPriceRuleVO(tmp);
                tmpBo.setCityId(String.valueOf(tmp.getCityId()));
                tmpBo.setCityType(tmp.getCityType());
                tmpBo.setCityName(tmp.getCityName());
                hotelRuleBoMap.put(tmp.getCityType()+tmp.getCityId(), tmpBo);
            } else if (StringUtils.equalsIgnoreCase(name, "OffPeakSeasonRule")) {
                OffPeakSeasonRuleVO tmp = (OffPeakSeasonRuleVO) travelStandardRuleVO;
                if (StringUtils.isBlank(tmp.getCityType()) || tmp.getCityId() == null) {
                    continue;
                }
                hotelRuleBo.setOffPeakSeasonRuleVO(tmp);
                hotelRuleBo.setCityId(String.valueOf(tmp.getCityId()));
                hotelRuleBo.setCityType(tmp.getCityType());
                hotelRuleBo.setCityName(tmp.getCityName());

                HotelRuleBo tmpBo = hotelRuleBoMap.getOrDefault(tmp.getCityType()+tmp.getCityId(), new HotelRuleBo());
                tmpBo.setOffPeakSeasonRuleVO(tmp);
                tmpBo.setCityId(String.valueOf(tmp.getCityId()));
                tmpBo.setCityType(tmp.getCityType());
                tmpBo.setCityName(tmp.getCityName());
                hotelRuleBoMap.put(tmp.getCityType()+tmp.getCityId(), tmpBo);
            } else if (StringUtils.equalsIgnoreCase(name, "StarRule")) {
                StarRuleVO tmp = (StarRuleVO) travelStandardRuleVO;
                if (StringUtils.isBlank(tmp.getCityType()) || tmp.getCityId() == null) {
                    continue;
                }
                hotelRuleBo.setStarRuleVO(tmp);
                hotelRuleBo.setCityId(String.valueOf(tmp.getCityId()));
                hotelRuleBo.setCityType(tmp.getCityType());
                hotelRuleBo.setCityName(tmp.getCityName());

                HotelRuleBo tmpBo = hotelRuleBoMap.getOrDefault(tmp.getCityType()+tmp.getCityId(), new HotelRuleBo());
                tmpBo.setStarRuleVO(tmp);
                tmpBo.setCityId(String.valueOf(tmp.getCityId()));
                tmpBo.setCityType(tmp.getCityType());
                tmpBo.setCityName(tmp.getCityName());
                hotelRuleBoMap.put(tmp.getCityType()+tmp.getCityId(), tmpBo);
            } else if (StringUtils.equalsIgnoreCase(name, "FloatPriceRule")) {
                FloatPriceRuleVO tmp = (FloatPriceRuleVO) travelStandardRuleVO;
                if (StringUtils.isBlank(tmp.getCityType()) || tmp.getCityId() == null) {
                    continue;
                }
                hotelRuleBo.setFloatPriceRuleVO(tmp);
                hotelRuleBo.setCityId(String.valueOf(tmp.getCityId()));
                hotelRuleBo.setCityType(tmp.getCityType());
                hotelRuleBo.setCityName(tmp.getCityName());

                HotelRuleBo tmpBo = hotelRuleBoMap.getOrDefault(tmp.getCityType()+tmp.getCityId(), new HotelRuleBo());
                tmpBo.setFloatPriceRuleVO(tmp);
                tmpBo.setCityId(String.valueOf(tmp.getCityId()));
                tmpBo.setCityType(tmp.getCityType());
                tmpBo.setCityName(tmp.getCityName());
                hotelRuleBoMap.put(tmp.getCityType()+tmp.getCityId(), tmpBo);
            }
        }
    }

    private String getCityType(TravelStandardRuleVO travelStandardRuleVO) {
        if (travelStandardRuleVO == null) {
            return "";
        }
        String name = travelStandardRuleVO.getName();
        if (StringUtils.equalsIgnoreCase(name, "CohabitRule")) {
            CohabitRuleVO tmp = (CohabitRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "PriceRule")) {
            PriceRuleVO tmp = (PriceRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "OffPeakSeasonRule")) {
            OffPeakSeasonRuleVO tmp = (OffPeakSeasonRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "StarRule")) {
            StarRuleVO tmp = (StarRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "FloatPriceRule")) {
            FloatPriceRuleVO tmp = (FloatPriceRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        }
        return "";
    }

    private Integer getControl(String controlType) {
        if (ControlTypeEnum.F.getCode().equals(controlType)) {
            return NumberUtils.INTEGER_ZERO;
        } else if (ControlTypeEnum.C.getCode().equals(controlType)) {
            return NumberUtils.INTEGER_ONE;
        } else if (ControlTypeEnum.A.getCode().equals(controlType)) {
            return NumberUtils.INTEGER_TWO;
        } else if (ControlTypeEnum.M.getCode().equals(controlType)) {
            return 3;
        } else {
            return 4;
        }
    }

    private TravelStandardResponse refreshToken(String travelStandardToken, String cityId, String districtId, String countyCityId) {
        if (StringUtils.isBlank(travelStandardToken) || StringUtils.isBlank(cityId) || (StringUtils.isBlank(districtId) && StringUtils.isBlank(countyCityId))) {
            log.info("无需刷新token");
            return null;
        }
        Metrics.REGISTRY.counter(travelStandardTokenRefreshMetricId).increment();
        // 获取快照
        QuerySnapshotResponseDTO snapshot = bookingCoreClientLoader.getSnapshot(travelStandardToken, Arrays.asList("query_param", "passenger_data"));
        log.info("获取快照 snapshot={}", snapshot);
        if (snapshot == null || CollectionUtils.isEmpty(snapshot.getSnapshotList()) || snapshot.getSnapshotList().size() != 2
                || snapshot.getSnapshotList().stream().filter(Objects::nonNull).noneMatch(snapShotDTO -> StringUtils.equalsIgnoreCase("query_param", snapShotDTO.getDataType()))
                || snapshot.getSnapshotList().stream().filter(Objects::nonNull).noneMatch(snapShotDTO -> StringUtils.equalsIgnoreCase("passenger_data", snapShotDTO.getDataType()))) {
            log.info("快照异常");
            return null;
        }
        List<SnapShotDTO> snapshotList = snapshot.getSnapshotList();
        GetTravelStandardRequest getTravelStandardRequest = new GetTravelStandardRequest();
        getTravelStandardRequest.setHasGeneratedToken(travelStandardToken);
        snapshotList.forEach(snapShotDTO -> {
            if (snapShotDTO == null) {
                return;
            }
            if (StringUtils.equalsIgnoreCase("query_param", snapShotDTO.getDataType())) {
                QueryParamV1Converter converter = QueryParamConverterFactoryImpl.getConverter(snapShotDTO);
                QueryParamModel queryParamModel = converter.convert(snapShotDTO.getSnapshotData());
                if (queryParamModel == null) {
                    return;
                }
                getTravelStandardRequest.setUid(queryParamModel.getPolicyUid());
                getTravelStandardRequest.setOrgId(queryParamModel.getPolicyOrgId());
                getTravelStandardRequest.setEmployeeType(queryParamModel.getPolicyEmployeeType());
                getTravelStandardRequest.setStartDate(cn.hutool.core.date.DateUtil.parseDate(queryParamModel.getStartDate()));
                getTravelStandardRequest.setEndDate(cn.hutool.core.date.DateUtil.parseDate(queryParamModel.getEndDate()));
                getTravelStandardRequest.setBizType(queryParamModel.getProductType());
                getTravelStandardRequest.setTrafficId(queryParamModel.getTravelNo());
            }
            else if (StringUtils.equalsIgnoreCase("passenger_data", snapShotDTO.getDataType())) {
                PassengerParamV1Converter converter = PassengerParamConverterFactoryImpl.getConverter(snapShotDTO);
                PassengerParamModel passengerParamModel = converter.convert(snapShotDTO.getSnapshotData());
                if (passengerParamModel == null || CollectionUtils.isEmpty(passengerParamModel.getPassengerInfoSnapshotMap())) {
                    return;
                }
                List<PassengerGroupDTO> passengerGroupList = new ArrayList<>();
                passengerParamModel.getPassengerInfoSnapshotMap().forEach((key, value) -> {
                    PassengerGroupDTO passengerGroupDTO = new PassengerGroupDTO();
                    passengerGroupDTO.setGroupId(String.valueOf(key));
                    passengerGroupDTO.setPassengerList(value.stream().map(item -> {
                        PassengerRequest passengerRequest = new PassengerRequest();
                        if (2 == item.getEmployeeType()) {
                            passengerRequest.setUid(item.getNoEmployeeId());
                        } else {
                            passengerRequest.setUid(item.getUid());
                        }
                        passengerRequest.setOrgId(item.getOrgId());
                        passengerRequest.setEmployeeType(item.getEmployeeType());
                        return passengerRequest;
                    }).collect(Collectors.toList()));
                    passengerGroupList.add(passengerGroupDTO);
                });
                getTravelStandardRequest.setPassengerGroupList(passengerGroupList);
            }
        });
        getTravelStandardRequest.setCityId(cityId);
        if (StringUtils.isNotBlank(countyCityId)) {
            getTravelStandardRequest.setCountryCityId(countyCityId);
        }
        if (StringUtils.isNotBlank(districtId)) {
            getTravelStandardRequest.setLocationId(districtId);
        }
        List<TravelStandardResponse> travelStandardResponseList = managementClientUtil.getTravelStandardToken(getTravelStandardRequest);
        log.info("刷新token请求 getTravelStandardRequest:{} travelStandardResponseList:{}", JsonUtils.toJsonString(getTravelStandardRequest), JsonUtils.toJsonString(travelStandardResponseList));
        return getFinalTravelStandardResponse(travelStandardResponseList);
    }

    private TravelStandardResponse getFinalTravelStandardResponse(List<TravelStandardResponse> travelStandardResponseList) {
        if (CollectionUtils.isEmpty(travelStandardResponseList)) {
            return null;
        }
        TravelStandardResponse travelStandardResponse = travelStandardResponseList.stream()
                .filter(item -> item.getTravelStandardToken() != null
                        && item.getTravelStandardToken().getOwnerType() != null
                        && item.getTravelStandardToken().getOwnerType() == 4
                        && Boolean.TRUE.equals(item.getRuleStatus()))
                .findFirst().orElse(travelStandardResponseList.get(0));
        if (travelStandardResponse.getTravelStandardToken().getOwnerType() == 3){
            dealApplyTripTravelStandard(travelStandardResponse);
        }
        return travelStandardResponse;
    }

    private void dealApplyTripTravelStandard(TravelStandardResponse travelStandardResponse) {
        if (travelStandardResponse == null || travelStandardResponse.getRuleChain() == null || CollectionUtils.isEmpty(travelStandardResponse.getRuleChain().getRuleList())) {
            return;
        }
        List<TravelStandardRuleVO> ruleList = travelStandardResponse.getRuleChain().getRuleList();
        for (TravelStandardRuleVO travelStandardRuleVO : ruleList) {
            if (StringUtils.equalsIgnoreCase(travelStandardRuleVO.getName(), "PriceRule")) {
                PriceRuleVO priceRuleVO = (PriceRuleVO) travelStandardRuleVO;
                priceRuleVO.setCityType(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType().toString());
                priceRuleVO.setCityId(APPLY_TRIP_MOCK_CITY_ID_LONG);
            } else if (StringUtils.equalsIgnoreCase(travelStandardRuleVO.getName(), "OffPeakSeasonRule")) {
                OffPeakSeasonRuleVO offPeakSeasonRuleVO = (OffPeakSeasonRuleVO) travelStandardRuleVO;
                offPeakSeasonRuleVO.setCityType(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType().toString());
                offPeakSeasonRuleVO.setCityId(APPLY_TRIP_MOCK_CITY_ID_STRING);
            } else if (StringUtils.equalsIgnoreCase(travelStandardRuleVO.getName(), "CohabitRule")) {
                CohabitRuleVO cohabitRuleVO = (CohabitRuleVO) travelStandardRuleVO;
                cohabitRuleVO.setCityType(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType().toString());
                cohabitRuleVO.setCityId(APPLY_TRIP_MOCK_CITY_ID_STRING);
            }
        }
    }

    private Pair<String, String> getDistrictIdOrCountyIdPair(String cityId, Double lat, Double lon) {
        if (lat == null || lon == null || lat == 0 || lon == 0 || lat == -1 || lon == -1) {
            log.info("获取地理信息失败，请求参数为空");
            return null;
        }
        // 获取城市和区县
        AroundPlaceRespDto aroundPlaceRespDto = commonSupplierLoader.aroundPlaceByGaoDe(lat, lon);
        log.info("周边搜索结果 aroundPlaceRespDto={}", aroundPlaceRespDto);
        if (aroundPlaceRespDto == null || CollectionUtils.isEmpty(aroundPlaceRespDto.getPois()) || aroundPlaceRespDto.getPois().get(0) == null) {
            log.info("获取地理信息失败，高德接口返回异常");
            Metrics.REGISTRY.counter(countyOrDistrictGetMetricId.withTag("type", "高德数据异常")).increment();
            return null;
        }
        AroundPlaceRespDto.Poi poi = aroundPlaceRespDto.getPois().get(0);
        String cityName = poi.getCityName();
        String adName = poi.getAdName();
        if (StringUtils.isBlank(cityName) || StringUtils.isBlank(adName)) {
            log.info("获取地理信息失败，高德接口返回异常");
            Metrics.REGISTRY.counter(countyOrDistrictGetMetricId.withTag("type", "高德数据异常")).increment();
            return null;
        }
        log.info("名称转id cityName:{} adName:{}", cityName, adName);
        // 判断是不是行政区
        if (adName.endsWith("区")) {
            List<CityLocationResponse> cityLocationResponses = basicDataClientLoader.searchLocation(cityName, adName);
            log.info("名称转id结果 cityLocationResponses={}", cityLocationResponses);
            if (CollectionUtils.isNotEmpty(cityLocationResponses) && cityLocationResponses.get(0) != null
                    && StringUtils.equalsIgnoreCase(cityLocationResponses.get(0).getCityId(), cityId) && StringUtils.isNotBlank(cityLocationResponses.get(0).getLocationId())) {
                Metrics.REGISTRY.counter(countyOrDistrictGetMetricId.withTag("type", "执行成功")).increment();
                return new Pair<>(cityLocationResponses.get(0).getLocationId(), null);
            }
        } else {
            List<CityCountyResponse> cityCountyResponses = basicDataClientLoader.searchCounty(cityName, adName);
            log.info("名称转id结果 cityCountyResponses={}", cityCountyResponses);
            if (CollectionUtils.isNotEmpty(cityCountyResponses) && cityCountyResponses.get(0) != null
                    && StringUtils.equalsIgnoreCase(cityCountyResponses.get(0).getCityId(), cityId) && StringUtils.isNotBlank(cityCountyResponses.get(0).getCountyId())) {
                Metrics.REGISTRY.counter(countyOrDistrictGetMetricId.withTag("type", "执行成功")).increment();
                return new Pair<>(null, cityCountyResponses.get(0).getCountyId());
            }
        }
        Metrics.REGISTRY.counter(countyOrDistrictGetMetricId.withTag("type", "名称转id失败")).increment();
        log.info("名称转id失败 cityName:{} adName:{}", cityName, adName);
        return null;
    }

}
