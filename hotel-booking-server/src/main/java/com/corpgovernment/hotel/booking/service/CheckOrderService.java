package com.corpgovernment.hotel.booking.service;

import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.GET_TRAVEL_ATTRIBUTE_ERROR;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Nullable;
import javax.annotation.Resource;

import com.corpgovernment.api.applytrip.soa.request.QueryApplyTripPersonRequest;
import com.corpgovernment.api.applytrip.vo.AoApplyTripPersonVo;
import com.corpgovernment.api.riskcontrol.request.SubmitRiskControlCheckRequest;
import com.corpgovernment.client.RiskControlServiceClient;
import com.corpgovernment.common.base.BaseUserInfo;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.corpgovernment.api.organization.enums.OrgAccountingUnitCategoryConfigEnum;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.applytrip.enums.ApplyTripTempEnableEnum;
import com.corpgovernment.api.applytrip.vo.AoApplyTripTempCostVo;
import com.corpgovernment.api.approvalsystem.bean.FlowDetail;
import com.corpgovernment.api.approvalsystem.bean.FlowNodeInfo;
import com.corpgovernment.api.approvalsystem.enums.ApproveUserTypeEnum;
import com.corpgovernment.api.costcenter.model.TempCostCenterVo;
import com.corpgovernment.api.hotel.booking.checkorder.request.BirthVo;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo.ContactsInfo;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo.PassengerInfo;
import com.corpgovernment.api.hotel.booking.checkorder.request.TelVo;
import com.corpgovernment.api.hotel.booking.checkorder.response.CheckOrderResponseVo;
import com.corpgovernment.api.hotel.booking.core.HotelChummageVerifyReqVo;
import com.corpgovernment.api.hotel.booking.core.HotelChummageVerifyRespVo;
import com.corpgovernment.api.hotel.booking.initpage.response.DeliveryInfoVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo.ContactInfo;
import com.corpgovernment.api.hotel.booking.initpage.response.InvoiceInfoVo;
import com.corpgovernment.api.organization.dto.request.business.unit.BusinessUnitReferenceReq;
import com.corpgovernment.api.organization.dto.response.business.unit.BusinessUnitSimpleDto;
import com.corpgovernment.api.organization.model.commonInvoice.CommonInvoiceVo;
import com.corpgovernment.api.organization.model.org.OrgAccountingUnitCategoryConfig;
import com.corpgovernment.api.organization.model.org.OrgConfigInfoVo;
import com.corpgovernment.api.organization.model.org.OrgInfoVo;
import com.corpgovernment.api.organization.model.org.response.GetOrgConfigResponse;
import com.corpgovernment.api.organization.model.passenger.*;
import com.corpgovernment.api.organization.model.switchinfo.SwitchInfoVo;
import com.corpgovernment.api.organization.model.user.employee.OrgEmployeeVo;
import com.corpgovernment.api.train.product.enums.TripTypeEnum;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.impl.HotelBasicDataService;
import com.corpgovernment.common.base.BaseRequestVO.UserInfo;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.dataloader.CommonOrganizationDataloader;
import com.corpgovernment.common.dto.GetEmployeeOpenCardReq;
import com.corpgovernment.common.dto.GetEmployeeOpenCardRsp;
import com.corpgovernment.common.enums.ExceptionCodeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.EmailUtil;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.domain.common.model.enums.ResourceModeEnum;
import com.corpgovernment.core.domain.common.model.enums.SupportCertificateTypeEnum;
import com.corpgovernment.core.domain.common.model.enums.SystemSupplierEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.HotelChummageVerifyResultEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hotelconfig.service.HotelConfigDomainService;
import com.corpgovernment.core.service.impl.HotelMainFlowService;
import com.corpgovernment.hotel.booking.bo.*;
import com.corpgovernment.hotel.booking.cache.OrderInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.booking.convert.HotelBookingConvertor;
import com.corpgovernment.hotel.booking.convert.VerifyPassengerConvert;
import com.corpgovernment.hotel.booking.enums.CropPayTypeEnum;
import com.corpgovernment.hotel.booking.enums.DuplicateCheckModeEnum;
import com.corpgovernment.hotel.booking.enums.MixPayTypeEnum;
import com.corpgovernment.hotel.booking.request.CheckDuplicateBookingRequest;
import com.corpgovernment.hotel.booking.vo.CheckDuplicateBookingResponse;
import com.corpgovernment.hotel.product.dataloader.db.*;
import com.corpgovernment.hotel.product.dataloader.soa.*;
import com.corpgovernment.hotel.product.entity.db.*;
import com.ctrip.corp.obt.generic.security.utils.DesensitizationUtils;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@AllArgsConstructor
public class CheckOrderService extends BaseService {
    public static final Id ORGANIZATION_MANAGE_CHECK_NAMEINFO = Metrics.REGISTRY.createId("organization.manage.check.nameinfo");

    private static final String TAG_CANCELLING = "取消中";
    private static final String TAG_MODIFYING = "修改中";
    private static final String HOTEL_MODIFIED_RECEIPT_DUPLICATE_ORDER_CONTROL_ENABLED = "1";
    @Autowired
    private final OrderInfoCacheManager orderInfoCacheManager;
    @Autowired
    private final HotelManager hotelManager;
    @Autowired
    private final BasicDataClientLoader basicDataClientLoader;
    @Autowired
    private final OrganizationClientLoader organizationClientLoader;

    @Autowired
    private final PassengerClientLoader passengerClientLoader;
    @Autowired
    private HotelBasicDataService hotelBasicDataService;
    @Autowired
    private ApplyTripService applyTripService;

    @Autowired
    private final HoPassengerLoader hoPassengerLoader;

    @Autowired
    private final HoOrderLoader hoOrderLoader;

    @Autowired
    private final HoHotelApplyLoader hoHotelApplyLoader;

    @Autowired
    private final HoHotelApplyDetailLoader hoHotelApplyDetailLoader;

    @Autowired
    private final HoHotelLoader hoHotelLoader;

    @Autowired
    private final HoRoomLoader hoRoomLoader;

    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private CheckOrderPlusService checkOrderPlusService;
    @Autowired
    private OrganizationEmployeeClientLoader organizationEmployeeClientLoader;
    @Autowired
    private CommonInvoiceClientLoader commonInvoiceClientLoader;

    @Resource
    private TravelAttributeService travelAttributeService;
    @Autowired
    private BusinessUnitClientLoader businessUnitClientLoader;

    @Autowired
    private HotelConfigDomainService hotelConfigDomainService;
    @Autowired
    private HotelMainFlowService hotelMainFlowService;
    @Autowired
    private VerifyPassengerConvert verifyPassengerConvert;

    @Autowired
    private HoOrderCancelFormLoader hoOrderCancelFormLoader;
    @Autowired
    private CommonOrganizationDataloader commonOrganizationDataloader;
    @Autowired
    private RiskControlServiceClient riskControlServiceClient;

    public CheckOrderResponseVo checkOrder(CheckOrderRequestVo request) {
        try {
            initElkLog();
            addElkInfoLog("进入订单核对");
            hotelManager.checkStateTime(false);
            UserInfo userInfo = request.getUserInfo();

            // #恢复脱敏数
            recoverDesensitizedData(request);

            // 邮箱校验
            if (!EmailUtil.checkEmailAddress(Optional.of(request).map(CheckOrderRequestVo::getContactsInfo).map(ContactsInfo::getEmail).orElse(null), false) ||
                    (Objects.nonNull(request.getInvoiceInfo()) && !EmailUtil.checkEmailAddress(request.getInvoiceInfo().getEmail(), true))) {
                throw new CorpBusinessException(HotelResponseCodeEnum.CONTRACT_EMAIL_FORMAT_ERROR_TIP);
            }

            // 获取订单大缓存
            OrderInfoModel orderInfo =
                orderInfoCacheManager.getOrderInfo(request.getHotelId(), request.getRoomId(), userInfo.getToken());
            addElkInfoLog("获取订单缓存：" + JsonUtils.toJsonString(orderInfo));
            if (OrderInfoModel.isEmpty(orderInfo) || orderInfo.getRoomInfo() == null) {
                addElkInfoLog("缓存为空，订单下单失败");
                return CheckOrderResponseVo.failure("500", "酒店信息发生变化");
            }

            CheckOrderResponseVo responseVo = new CheckOrderResponseVo();

            // 校验参数
            checkParams(request, userInfo, orderInfo);
            
            // 入住人设置组织名称
            this.setPassengerOrgName(request);
            
            // 入住人信息处理
            handlePassenger(request);
            // 获取携程用户id
            Map<String, String> ctripUidMap = getCtripUid(request.getPassengerInfoList(), orderInfo.getSupplierCode());
            log.info("获取入住人的ctripUid,ctripUidMap:{}", JsonUtils.toJsonString(ctripUidMap));
            
            Long orderId = this.productionOrderId(orderInfo.getUid());
            addElkInfoLog("获取订单号：" + orderId);
            orderInfo.checkOrder(request, orderInfo, orderId, ctripUidMap);
            // 补充法人机构信息
            fillBusinessUnitInfo(orderInfo);

            // 出行人申请单同步拓展字段赋值
            if (TripTypeEnum.PUB.getType().equals(orderInfo.getCorpPayType()) && Objects.nonNull(orderInfo.getApplyNo())
                    && CollectionUtils.isNotEmpty(orderInfo.getPassengerList())) {
                setPassengerApplyTripExtInfo(orderInfo.getPassengerList(), orderInfo.getApplyNo());
            }

            // 预订人公司名称
            orderInfo.setCorpName(UserInfoContext.getContextParams(BaseUserInfo.class).getCorpName());
            
            // 校验出差申请单并更新orderInfo中的超过总金额管控方式、个付
            checkApplyTrip(responseVo, request, orderInfo);

            orderInfoCacheManager.saveOrderInfo(orderInfo, request.getHotelId(), request.getRoomId(),
                userInfo.getToken());
            addElkInfoLog("保存订单缓存：" + JsonUtils.toJsonString(orderInfo));
            // 支付信息
            CheckOrderResponseVo.PayInfo payInfo = new CheckOrderResponseVo.PayInfo();
            payInfo.setTotalAmount(Null.or(orderInfo.getTotalAmount(), BigDecimal.ZERO));
            payInfo.setPersonalAmount(Null.or(orderInfo.getPPayAmount(), BigDecimal.ZERO));
            payInfo.setPublicAmount(Null.or(orderInfo.getAPayAmount(), BigDecimal.ZERO));
            payInfo.setPayType(orderInfo.getPayType());
            responseVo.setPayInfo(payInfo);
            responseVo.setOrderId(orderId);

            // 校验出行人契约verifyPassenger
            try {
                VerifyPassengerRequest verifyPassengerRequest = new VerifyPassengerRequest();
                verifyPassengerRequest.setUid(request.getUserInfo().getUid());
                List<VerifyPassenger> passengerVos = verifyPassengerConvert.convertVerifyPassengerRequest(orderInfo.getPassengerList());
                log.info("checkOrderPlus convertVerifyPassengerRequest passengerVos: {}", JsonUtils.toJsonString(passengerVos));
                verifyPassengerRequest.setPassengerVos(passengerVos);
                VerifyPassengerResponse verifyPassenger = passengerClientLoader.verifyPassenger(verifyPassengerRequest);
                log.info("checkOrderPlus 校验出行人契约verifyPassenger,verifyPassengerRequest:{},verifyPassenger:{}",JsonUtils.toJsonString(verifyPassengerRequest),JsonUtils.toJsonString(verifyPassenger));
                if(ObjectUtil.isNotNull(verifyPassenger) && CollectionUtil.isNotEmpty(verifyPassenger.getErrorsPassengerVos())){
                    Metrics.REGISTRY.counter(ORGANIZATION_MANAGE_CHECK_NAMEINFO
                                    .withTag("result", false)
                                    .withTag("errorCode","product_hotel_verifyPassenger"))
                            .increment();
                    responseVo.setVerifyPassengerResponse(JsonUtils.parse(JsonUtils.toJsonString(verifyPassenger),
                            com.corpgovernment.api.hotel.booking.checkorder.response.VerifyPassengerResponse.class));
                }
            } catch (Exception e) {
                Metrics.REGISTRY.counter(ORGANIZATION_MANAGE_CHECK_NAMEINFO
                                .withTag("result", false)
                                .withTag("errorCode","product_hotel_verifyPassenger"))
                        .increment();
                log.info("校验出行人契约verifyPassenger,e:",e);
            }

            return responseVo;
        } finally {
            log.info("CheckOrderService.checkOrder酒店核对订单提交{} request：{}{}{}{}", System.lineSeparator(),
                    JsonUtils.toJsonString(request), System.lineSeparator(), System.lineSeparator(), this.getElkInfoLog());
            clearElkLog();
        }
    }

    /**
     * 出行人申请单同步拓展字段赋值
     * @param passengerList
     * @param applyNo
     */
    private void setPassengerApplyTripExtInfo(List<OrderInfoModel.PassengerInfo> passengerList, String applyNo) {
        if(StringUtils.isBlank(applyNo) || CollectionUtils.isEmpty(passengerList)){
            return;
        }
        List<QueryApplyTripPersonRequest.OrgUid> orgUidList = new ArrayList<>();
        passengerList.forEach(passenger -> {
            QueryApplyTripPersonRequest.OrgUid orgUid = new QueryApplyTripPersonRequest.OrgUid();
            orgUid.setOrgId(passenger.getOrgId());
            orgUid.setUid(StringUtils.isNotBlank(passenger.getUid()) ? passenger.getUid() : passenger.getNoEmployeeId());
            orgUidList.add(orgUid);
        });
        QueryApplyTripPersonRequest request = new QueryApplyTripPersonRequest();
        request.setApplyNo(applyNo);
        request.setOrgUidList(orgUidList);
        List<AoApplyTripPersonVo> applyTripPersonList = applyTripClientLoader.personListByApplyInfo(request);
        if (CollectionUtils.isEmpty(applyTripPersonList)) {
            return;
        }
        passengerList.forEach(passenger -> {
            String uid = StringUtils.isNotBlank(passenger.getUid()) ? passenger.getUid() : passenger.getNoEmployeeId();
            applyTripPersonList.stream().filter(applyTripPerson ->
                    StringUtils.equals(applyTripPerson.getOrgId(), passenger.getOrgId(),false) &&
                            StringUtils.equals(applyTripPerson.getUid(), uid, false) &&
                            CollectionUtils.isNotEmpty(applyTripPerson.getOtherFieldList())).findFirst().ifPresent(applyTripPerson -> {
                Map<String, Object> fieldMap = CollectionUtils.newHashMap();
                applyTripPerson.getOtherFieldList().forEach(t->{
                    if(StringUtils.isNotBlank(t.getFieldName())) {
                        fieldMap.put(t.getFieldName(), t.getFieldValue());
                    } else {
                        log.info("applyTripPerson.getOtherFieldList() fieldName is null,applyTripPersonList:{}",JsonUtils.toJsonString(applyTripPersonList));
                    }
                });
                passenger.setFieldMap(fieldMap);
            });
        });
    }


    private void handlePassenger(CheckOrderRequestVo request) {
        if (request == null || request.getPassengerInfoList() == null) {
            return;
        }
        
        for (PassengerInfo passengerInfo : request.getPassengerInfoList()) {
            if (passengerInfo == null || StringUtils.isBlank(passengerInfo.getUid())) {
                continue;
            }
            
            // 获取入住人详情
            OrgEmployeeVo employeeInfo = getEmployeeInfo(passengerInfo.getUid(), passengerInfo.getOrgId());
            
            // 填充
            passengerInfo.setCorpId(employeeInfo.getRecentCompanyId());
        }
    }
    
    /**
     * 订单核对,根据token核对
     *
     * @param request 订单核对请求对象
     * @return 订单核对响应对象
     * @throws CorpBusinessException 当邮箱格式校验失败时抛出此异常
     */
    public CheckOrderResponseVo checkOrderPlus(CheckOrderRequestVo request) {
        try {
            log.info("订单核对checkOrderPlus,根据token核对,request:{}", JsonUtils.toJsonString(request));
            hotelManager.checkStateTime(false);
            SubmitRiskControlCheckRequest riskControlCheckRequest = new SubmitRiskControlCheckRequest();
            riskControlCheckRequest.setToken(request.getToken());
            riskControlCheckRequest.setUid(request.getUserInfo().getUid());
            riskControlServiceClient.submitRiskControlCheck(riskControlCheckRequest);
            UserInfo userInfo = request.getUserInfo();
            log.info("订单核对checkOrderPlus,userInfo:{}", JsonUtils.toJsonString(userInfo));
            
            // 合住校验
            HotelChummageVerifyReqVo hotelChummageVerifyReqVo = new HotelChummageVerifyReqVo();
            hotelChummageVerifyReqVo.setToken(request.getToken());
            HotelChummageVerifyRespVo hotelChummageVerifyRespVo = hotelMainFlowService.verifyHotelChummage(hotelChummageVerifyReqVo);
            String hotelChummageVerifyResultCode = Optional.ofNullable(hotelChummageVerifyRespVo).map(HotelChummageVerifyRespVo::getHotelChummageVerifyResultCode).orElse(null);
            if (StringUtils.equalsIgnoreCase(hotelChummageVerifyResultCode, HotelChummageVerifyResultEnum.NEED_CHUMMAGE.getCode()) || StringUtils.equalsIgnoreCase(hotelChummageVerifyResultCode, HotelChummageVerifyResultEnum.NEED_SAME_SEX_CHUMMAGE.getCode())) {
                Boolean canSelectRc = Optional.ofNullable(hotelChummageVerifyRespVo).map(HotelChummageVerifyRespVo::getCanSelectRc).orElse(null);
                if (Boolean.TRUE.equals(canSelectRc) && request.getNoChummageRc() == null) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.FORCE_CHUMMAGE_NO_RC_INFO);
                }
                if (!Boolean.TRUE.equals(canSelectRc)) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.FORCE_CHUMMAGE);
                }
            }
            
            // 把passengerRoomMap中的数据放入passengerInfoList中
            Map<Integer, List<PassengerInfo>> passengerRoomMap = request.getPassengerRoomMap();
            if(CollectionUtils.isNotEmpty(passengerRoomMap)){
                List<PassengerInfo> extractPassengersFromMap = passengerRoomMap.values().stream()
                        .flatMap(List::stream)
                        .collect(Collectors.toList());

                request.setPassengerInfoList(extractPassengersFromMap);
                log.info("After extractPassengersFromMap: {}", JsonUtils.toJsonString(extractPassengersFromMap));
                // #恢复脱敏数
                log.info("恢复脱敏数 before,request:{}",JsonUtils.toJsonString(request));
                recoverDesensitizedDataToken(request);
                log.info("恢复脱敏数 after,request:{}",JsonUtils.toJsonString(request));
            }

            // 邮箱校验
            if (!EmailUtil.checkEmailAddress(Optional.of(request).map(CheckOrderRequestVo::getContactsInfo).map(ContactsInfo::getEmail).orElse(null), false) ||
                    (Objects.nonNull(request.getInvoiceInfo()) && !EmailUtil.checkEmailAddress(request.getInvoiceInfo().getEmail(), true))) {
                log.info("邮箱校验错误.");
                throw new CorpBusinessException(HotelResponseCodeEnum.CONTRACT_EMAIL_FORMAT_ERROR_TIP);
            }

            // 获取订单大缓存
            OrderInfoModel orderInfo = checkOrderPlusService.getOrderInfo(request);
            log.info("获取订单大缓存,orderInfo:{}",JsonUtils.toJsonString(orderInfo));

            CheckOrderResponseVo responseVo = new CheckOrderResponseVo();

            if (OrderInfoModel.isEmpty(orderInfo) || orderInfo.getRoomInfo() == null) {
                return CheckOrderResponseVo.failure("500", "酒店信息发生变化");
            }
            // 校验并赋值用户自选审批人
            checkAndSetManualApproveUser(orderInfo, request);
            //从DB反查人员信息
            List<PassengerInfo>  passengerList = request.getPassengerInfoList();
            if(CollectionUtils.isNotEmpty(passengerList)){
                for (PassengerInfo passenger : passengerList) {
                    OrgEmployeeVo orgEmployeeVoDB = getEmployeeInfo(passenger.getUid(), passenger.getOrgId());
                    buildOrderInfoModelPassengerInfo(passenger,orgEmployeeVoDB);
                }
            }
            log.info("从DB反查人员信息,passengerList:{}",JsonUtils.toJsonString(passengerList));

            // 校验参数
            checkParams(request, userInfo, orderInfo);

            String productType = checkOrderPlusService.getProductType(request); //产线类型
            log.info("产线类型,productType:{}", productType);

            Long orderId = this.productionOrderId(orderInfo.getUid(),productType);
            log.info("获取订单号,orderId:{}", orderId);

            // 旧流程前端没有传orgName,新流程前端已经传了orgName.
            // 后端做兜底:如果前端不传orgName,后端再进行反查补充orgName
            PassengerInfo passengerInfoOrgName = request.getPassengerInfoList().stream().filter(p->StringUtils.isBlank(p.getOrgName())).findFirst().orElse(null);
            if(ObjectUtil.isNotNull(passengerInfoOrgName)){
                log.info("入住人设置组织名称反查DB,passengerInfoOrgName:{}", JsonUtils.toJsonString(passengerInfoOrgName));
                this.setPassengerOrgName(request);
            }
            log.info("入住人设置组织名称,request:{}", JsonUtils.toJsonString(request));
            
            // 处理入住人信息
            handlePassenger(request);
            // 获取入住人的ctripUid
            Map<String, String> ctripUidMap = getCtripUid(request.getPassengerInfoList(), request.getSupplierCode());
            log.info("获取入住人的ctripUid,ctripUidMap:{}", JsonUtils.toJsonString(ctripUidMap));
            // 填充PassengerList信息
            orderInfo.checkOrderPlus(request, orderInfo, orderId, ctripUidMap);
            log.info("订单核对checkOrderPlus,orderInfo:{}", JsonUtils.toJsonString(orderInfo));
            // 补充法人机构信息
            fillBusinessUnitInfo(orderInfo);

            // 校验出差申请单并更新orderInfo中的超过总金额管控方式、个付
            checkApplyTrip(responseVo, request, orderInfo);
            log.info("订单核对checkOrderPlus,orderInfo:{}", JsonUtils.toJsonString(orderInfo));

            // 校验数据
            List<String> checkTipList = Null.or(checkOrderInfoModel(orderInfo), new ArrayList<>(0));
            if (CollectionUtils.isNotEmpty(checkTipList)) {
                throw new CorpBusinessException(HotelResponseCodeEnum.ORDER_INFO_CHECK_FAIL.code(), String.join(",", checkTipList));
            }

            // 出行人申请单同步拓展字段赋值
            if (TripTypeEnum.PUB.getType().equals(orderInfo.getCorpPayType()) && Objects.nonNull(orderInfo.getApplyNo())
                    && CollectionUtils.isNotEmpty(orderInfo.getPassengerList())) {
                setPassengerApplyTripExtInfo(orderInfo.getPassengerList(), orderInfo.getApplyNo());
            }
            // 预订人公司名称
            orderInfo.setCorpName(UserInfoContext.getContextParams(BaseUserInfo.class).getCorpName());

            orderInfoCacheManager.saveOrderInfo(orderInfo, request.getHotelId(), request.getRoomId(),
                    userInfo.getToken());
            log.info("订单核对checkOrderPlus,saveOrderInfo:{}", JsonUtils.toJsonString(orderInfo));
            // 支付信息
            CheckOrderResponseVo.PayInfo payInfo = new CheckOrderResponseVo.PayInfo();
            payInfo.setTotalAmount(Null.or(orderInfo.getTotalAmount(), BigDecimal.ZERO));
            payInfo.setPersonalAmount(Null.or(orderInfo.getPPayAmount(), BigDecimal.ZERO));
            payInfo.setPublicAmount(Null.or(orderInfo.getAPayAmount(), BigDecimal.ZERO));
            payInfo.setPayType(orderInfo.getPayType());
            payInfo.setTotalServiceCharge(orderInfo.getTotalServiceCharge());
            payInfo.setServiceChargeStrategy(orderInfo.getServiceChargeStrategy());
            payInfo.setServiceChargeStrategyValue(orderInfo.getServiceChargeStrategyValue());
            responseVo.setPayInfo(payInfo);
            responseVo.setOrderId(orderId);
            responseVo.setDirectSupplier(StringUtils.equalsIgnoreCase(orderInfo.getResourceMode(), ResourceModeEnum.DIRECT.getCode()));
            log.info("支付信息,orderInfo:{}", JsonUtils.toJsonString(orderInfo));

            // 校验出行人契约verifyPassenger
            try {
                VerifyPassengerRequest verifyPassengerRequest = new VerifyPassengerRequest();
                verifyPassengerRequest.setUid(request.getUserInfo().getUid());
                List<VerifyPassenger> passengerVos = verifyPassengerConvert.convertVerifyPassengerRequest(orderInfo.getPassengerList());
                log.info("checkOrderPlus convertVerifyPassengerRequest passengerVos: {}", JsonUtils.toJsonString(passengerVos));
                verifyPassengerRequest.setPassengerVos(passengerVos);
                VerifyPassengerResponse verifyPassenger = passengerClientLoader.verifyPassenger(verifyPassengerRequest);
                log.info("checkOrderPlus 校验出行人契约verifyPassenger,verifyPassengerRequest:{},verifyPassenger:{}",JsonUtils.toJsonString(verifyPassengerRequest),JsonUtils.toJsonString(verifyPassenger));
                if(ObjectUtil.isNotNull(verifyPassenger) && CollectionUtil.isNotEmpty(verifyPassenger.getErrorsPassengerVos())){
                    Metrics.REGISTRY.counter(ORGANIZATION_MANAGE_CHECK_NAMEINFO
                                    .withTag("result", false)
                                    .withTag("errorCode","product_hotel_verifyPassenger"))
                            .increment();
                    responseVo.setVerifyPassengerResponse(JsonUtils.parse(JsonUtils.toJsonString(verifyPassenger),
                            com.corpgovernment.api.hotel.booking.checkorder.response.VerifyPassengerResponse.class));
                }
            } catch (Exception e) {
                Metrics.REGISTRY.counter(ORGANIZATION_MANAGE_CHECK_NAMEINFO
                                .withTag("result", false)
                                .withTag("errorCode","product_hotel_verifyPassenger"))
                        .increment();
                log.info("校验出行人契约verifyPassenger,e:",e);
            }

            return responseVo;
        } finally {
            log.info("CheckOrderService.checkOrder酒店核对订单提交{} request：{}{}{}{}", System.lineSeparator(),
                    JsonUtils.toJsonString(request), System.lineSeparator(), System.lineSeparator(), this.getElkInfoLog());
            clearElkLog();
        }
    }

    private Map<String, String> getCtripUid(List<PassengerInfo> passengerInfoList, String supplierCode) {
        if (!StringUtils.equalsIgnoreCase(supplierCode, SystemSupplierEnum.CTRIP.getCode()) || passengerInfoList == null) {
            return null;
        }
        
        Map<String, String> ctripUidMap = new HashMap<>();
        for (PassengerInfo passengerInfo : passengerInfoList) {
            if (passengerInfo == null || StringUtils.isBlank(passengerInfo.getUid())) {
                continue;
            }
            
            GetEmployeeOpenCardReq getEmployeeOpenCardReq = new GetEmployeeOpenCardReq();
            getEmployeeOpenCardReq.setUid(passengerInfo.getUid());
            getEmployeeOpenCardReq.setCorpId(passengerInfo.getCorpId());
            GetEmployeeOpenCardRsp employeeOpenCardInfo = commonOrganizationDataloader.getEmployeeOpenCardInfo(getEmployeeOpenCardReq);
            log.info("查询入住人开卡信息 getEmployeeOpenCardReq={} employeeOpenCardInfo={}", JsonUtils.toJsonString(getEmployeeOpenCardReq), JsonUtils.toJsonString(employeeOpenCardInfo));
            if (employeeOpenCardInfo != null && StringUtils.isNotBlank(employeeOpenCardInfo.getSupplierUid())) {
                ctripUidMap.put(passengerInfo.getUid(), employeeOpenCardInfo.getSupplierUid());
            }
        }
        return ctripUidMap;
    }
    
    private List<String> checkOrderInfoModel(OrderInfoModel orderInfoModel) {
        if (orderInfoModel == null) {
            return null;
        }
        
        Set<String> tipSet = new HashSet<>();
        // 入住人信息校验
        List<OrderInfoModel.PassengerInfo> passengerList = orderInfoModel.getPassengerList();
        if (CollectionUtils.isNotEmpty(passengerList)) {
            // 邮箱校验
            if (Boolean.TRUE.equals(orderInfoModel.getNeedEmail())
                    && passengerList.stream().filter(Objects::nonNull).anyMatch(item -> StringUtils.isBlank(item.getMail()))) {
                tipSet.add("请维护入住人的邮箱");
            }

            // 证件校验
            if (Boolean.TRUE.equals(orderInfoModel.getNeedCertificate())
                    && passengerList.stream().filter(Objects::nonNull).anyMatch(item -> StringUtils.isBlank(item.getCardNo()))) {
                tipSet.add(getCertificateCheckTip(orderInfoModel.getSupportCertificateTypeList()));
            }
        }
        
        // 钟点房校验
        String str = checkHourlyRoomInfo(orderInfoModel);
        if (StringUtils.isNotBlank(str)) {
            tipSet.add(str);
        }

//        // 联系人邮箱校验
//        String contractEmail = Optional.ofNullable(orderInfoModel.getContactInfo())
//                .map(OrderInfoModel.ContactInfo::getEmail)
//                .orElse(null);
//        if (BizTypeEnum.HOTEL_INTL.equals(BizTypeEnum.getByCodeOrName(orderInfoModel.getProductType()))
//                && StringUtils.isBlank(contractEmail)) {
//            tipSet.add("请维护联系人邮箱");
//        }
        
        return new ArrayList<>(tipSet);
    }
    
    private String checkHourlyRoomInfo(OrderInfoModel orderInfoModel) {
        if (orderInfoModel == null || orderInfoModel.getRoomInfo() == null || !Boolean.TRUE.equals(orderInfoModel.getRoomInfo().getHourlyRoom())) {
            return null;
        }
        
        OrderInfoModel.HourRoomDetail hourRoomDetail = orderInfoModel.getRoomInfo().getHourRoomDetail();
        if (hourRoomDetail == null || hourRoomDetail.getCheckInTime() == null || hourRoomDetail.getCheckOutTime() == null) {
            return "钟点房信息异常";
        }
        
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        // 将字符串解析为LocalDateTime对象
        LocalDateTime localDateTime = LocalDateTime.parse(hourRoomDetail.getCheckInTime(), formatter);
        if (localDateTime.isBefore(LocalDateTime.now())) {
            return "钟点房选择的入住时段非法";
        }
        
        return null;
    }

    private String getCertificateCheckTip(List<String> supportCertificateTypeList) {
        if (CollectionUtils.isEmpty(supportCertificateTypeList)) {
            return "请维护入住人的证件信息";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("请维护入住人的");
        for (String supportCertificateType : supportCertificateTypeList) {
            SupportCertificateTypeEnum supportCertificateTypeEnum = SupportCertificateTypeEnum.getEnum(supportCertificateType);
            if (supportCertificateTypeEnum != null) {
                sb.append(supportCertificateTypeEnum.getInfo()).append("或");
            }
        }
        if (sb.toString().endsWith("或")) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 根据组织员工数据库对象构建订单信息模型乘客信息
     *
     * @param passengerInfo 订单信息模型乘客信息对象
     * @param orgEmployeeVoDB 组织员工数据库对象
     */
    public void buildOrderInfoModelPassengerInfo(PassengerInfo passengerInfo,OrgEmployeeVo orgEmployeeVoDB){
        if(ObjectUtil.isNull(passengerInfo) || ObjectUtil.isNull(orgEmployeeVoDB)){
            return;
        }
//        passengerInfo.setName(orgEmployeeVoDB.getName());
//        passengerInfo.setPassport();
//        passengerInfo.setMobilePhone(orgEmployeeVoDB.getMobilePhone());
//        passengerInfo.setCorp();
//        passengerInfo.setBirthday(orgEmployeeVoDB.getBirthday());
//        passengerInfo.setEmployeeNo();
//        passengerInfo.setCountryCode();
//        passengerInfo.setGender(orgEmployeeVoDB.getGender());
//        passengerInfo.setUid(orgEmployeeVoDB.getUid());
//        passengerInfo.setNoEmployeeId();
//        passengerInfo.setIsSendSms();
//        passengerInfo.setLanguage(LanguageEnum.ZH_CN.getCode());
//        passengerInfo.setLang(LanguageEnum.ZH_CN.getCode());
//        passengerInfo.setOrgId(orgEmployeeVoDB.getOrgId());
//        passengerInfo.setOrgName(orgEmployeeVoDB.getOrgName());
//        passengerInfo.setRelationId();
//        passengerInfo.setRelationFlag();
//        passengerInfo.setTel();
//        passengerInfo.setCostCenter();
//        passengerInfo.setProjectInfo();
        BirthVo birth = new BirthVo();
        birth.setValue(orgEmployeeVoDB.getBirthday());
        passengerInfo.setBirth(birth);
//        passengerInfo.setDep();
//        passengerInfo.setWbsRemark();
//        passengerInfo.setCostCenterRemark();
//        passengerInfo.setEmployeeType();
//        passengerInfo.setSurname(orgEmployeeVoDB.getSurname());
//        passengerInfo.setGivenname(orgEmployeeVoDB.getGivenname());
//        passengerInfo.setFullEnName(orgEmployeeVoDB.getFullEnName());
//        passengerInfo.setFullName(orgEmployeeVoDB.getFullName());
        passengerInfo.setNationality(orgEmployeeVoDB.getNationality());
//        passengerInfo.setCostCenterVoList();
    }

    /**
     * 查询员工信息
     */
    private OrgEmployeeVo getEmployeeInfo(String uid, String orgId) {
        log.info("查询员工信息,uid:{},orgId:{}", uid, orgId);
        if(StrUtil.isBlank(uid) || StrUtil.isBlank(orgId)){
            return null;
        }
        OrgEmployeeVo employeeInfo = organizationEmployeeClientLoader.findEmployeeInfoByUid(uid, orgId);
        log.info("查询员工信息,uid:{},orgId:{},employeeInfo:{}", uid,orgId,JsonUtils.toJsonString(employeeInfo));
        if (employeeInfo == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_USER_INFORMATION);
        }
        return employeeInfo;
    }

    /**
     * 获取联系人信息
     */
    private InitOrderResponseVo.ContactInfo toContactInfo(OrgEmployeeVo data) {
        if (data == null) {
            return null;
        }
        InitOrderResponseVo.ContactInfo contactInfo = new InitOrderResponseVo.ContactInfo();
        contactInfo.setEmail(data.getEmail());
        contactInfo.setName(data.getName());
        contactInfo.setPhone(data.getMobilePhone());
        contactInfo.setCountryCode(data.getAreaCode());
        return contactInfo;
    }

    private void checkApplyTrip(CheckOrderResponseVo responseVo, CheckOrderRequestVo request, OrderInfoModel orderInfo) {
        log.info("出差申请单校验，responseVo：{}，request：{}，orderInfo：{}", JsonUtils.toJsonString(responseVo), JsonUtils.toJsonString(request), JsonUtils.toJsonString(orderInfo));
        if (responseVo == null || orderInfo == null || orderInfo.getTrafficId() == null || request == null || request.getPayInfo() == null
                || StringUtils.isBlank(request.getPayInfo().getCode()) || !StringUtils.equalsIgnoreCase(CropPayTypeEnum.PUB.getCode(), orderInfo.getCorpPayType())) {
            return;
        }

        // 支付方式
        String code = request.getPayInfo().getCode();
        if (StringUtils.equalsIgnoreCase(PayTypeEnum.CASH.getType(), code) || StringUtils.equalsIgnoreCase(PayTypeEnum.PPAY.getType(), code)) {
            return;
        }

        // 查询出差申请单项
        List<ApplyTripItemBo> applyTripItemList = applyTripService.getApplyTripItemList(orderInfo.getTrafficId(), BizTypeEnum.getByCodeOrName(orderInfo.getProductType()));
        log.info("applyTripItemList={}", JsonUtils.toJsonString(applyTripItemList));
        if (CollectionUtils.isEmpty(applyTripItemList)) {
            return;
        }
        responseVo.setApplyFieldList(applyTripItemList.stream().filter(Objects::nonNull).map(item -> {
            CheckOrderResponseVo.FieldObject fieldObject = new CheckOrderResponseVo.FieldObject();
            fieldObject.setKey(item.getCode());
            fieldObject.setLabel(item.getName());
            fieldObject.setValue(item.getDesc());
            return fieldObject;
        }).collect(Collectors.toList()));

        // 订单总金额
        BigDecimal totalPrice = Null.or(orderInfo.getTotalAmount(), BigDecimal.ZERO);
        // 公账部分金额计算
        BigDecimal totalAmount = null;
        // 总间夜数
        Integer hotelDayCount = 1;
        // 混付
        if (StringUtils.equalsIgnoreCase(PayTypeEnum.MIXPAY.getType(), code)) {
            OrderInfoModel.RoomInfo roomInfo = orderInfo.getRoomInfo();
            long dayNum = LocalDate.parse(roomInfo.getCheckInDate()).until(LocalDate.parse(roomInfo.getCheckOutDate()), ChronoUnit.DAYS);
            Integer roomQuantity = Null.or(request.getRoomQuantity(), 1);
            hotelDayCount =  (int)dayNum * roomQuantity;
            // 公账总金额
            BigDecimal totalAmountHigh = Null.or(orderInfo.getAmountHigh(), BigDecimal.ZERO).multiply(BigDecimal.valueOf(hotelDayCount));
            totalAmount = totalPrice.min(totalAmountHigh);
        }
        // 统一支付
        else if (StringUtils.equalsIgnoreCase(PayTypeEnum.ACCNT.getType(), code)) {
            // 订单总金额
            totalAmount = totalPrice;
        }

        // 校验
        List<ApplyTripItemBo> checkApplyTripItemList = applyTripItemList;
        if (totalAmount != null) {
            checkApplyTripItemList = applyTripService.checkApplyTripItemList(
                    applyTripItemList,
                    ApplyTripControlBo.builder().totalAmount(totalAmount).hotelDayCount(hotelDayCount)
                            .wantCheckItemCodeList(Arrays.asList("hotelAmount","amount", "hotelDayCount")).build());
        }

        // 超标项
        List<ApplyTripItemBo> tmpList = checkApplyTripItemList.stream().filter(item -> item != null && Boolean.TRUE.equals(item.getOverLimit())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmpList)) {
            return;
        }
        responseVo.setExceedApplyFieldList(tmpList.stream().filter(Objects::nonNull).map(item -> {
            CheckOrderResponseVo.FieldObject fieldObject = new CheckOrderResponseVo.FieldObject();
            fieldObject.setKey(item.getCode());
            fieldObject.setLabel(item.getName());
            fieldObject.setValue(item.getOverLimitDesc());
            return fieldObject;
        }).collect(Collectors.toList()));

        // 总金额管控
        ApplyTripItemBo applyTripItemBo = tmpList.stream().filter(item -> "hotelAmount".equalsIgnoreCase(item.getCode()) || "amount".equalsIgnoreCase(item.getCode())).findFirst().orElse(null);
        // 总金额超标
        if (applyTripItemBo != null) {
            // 超过总金额管控方式
            Map<String, SwitchInfoVo> travelAttribute = Null.or(getTravelAttribute(Optional.ofNullable(request.getUserInfo()).map(UserInfo::getCorpId).orElse("")), new HashMap<>(0));
            Integer overTotalAmountControl = Optional.ofNullable(travelAttribute.get("over_total_amount_control")).map(SwitchInfoVo::getNowVal).map(item -> item.get(0)).map(Object::toString).map(Integer::parseInt).orElse(0);
            responseVo.setOverTotalAmountControl(overTotalAmountControl);
            orderInfo.setMixPayType(overTotalAmountControl);
            // 个付重算
            if (overTotalAmountControl == 1) {
                BigDecimal value = (BigDecimal) Null.or(applyTripItemBo.getValue(), BigDecimal.ZERO);
                BigDecimal exceedAmount = totalPrice.subtract(value);
                // 如果出差申请单总金额为0，则不走混付
                if (value.compareTo(BigDecimal.ZERO) == 0) {
                    orderInfo.setPayType(PayTypeEnum.PPAY.getType());
                } else {
                    orderInfo.setExceedAmount(exceedAmount);
                    orderInfo.setPayType(PayTypeEnum.MIXPAY.getType());
                    orderInfo.setMixPayType(MixPayTypeEnum.TRIP_APPLY.getCode());
                }
                responseVo.setPayDesc("以下订单内容不符合出差申请要求，继续预订需个人支付超出部分¥" + exceedAmount.stripTrailingZeros().toPlainString());
            }
        }
    }

    /**
     * 获取差标属性
     */
    private Map<String, SwitchInfoVo> getTravelAttribute(String corpId) {
        if (StringUtils.isBlank(corpId)) {
            return null;
        }
        // 获取差旅属性
        List<SwitchInfoVo> travelAttributeList = organizationClientLoader.getTravelAttribute(corpId);
        if (travelAttributeList == null) {
            throw new CorpBusinessException(GET_TRAVEL_ATTRIBUTE_ERROR);
        }
        Map<String, SwitchInfoVo> switchInfoVoMap = travelAttributeList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SwitchInfoVo::getKey, item -> item, (a, b) -> a));
        log.info("差旅属性={}", switchInfoVoMap);
        return switchInfoVoMap;
    }

    public List<OrderInfoBo> checkDuplicateBooking(CheckDuplicateBookingBo request, String hotelModifiedReceiptDuplicateOrderControl) {
        List<HoPassenger> hoPassengers = hoPassengerLoader.listByPassengerIds(request.getUIds(), request.getNonEmployeeIds());
        if(CollectionUtils.isEmpty(hoPassengers)){
            return Collections.emptyList();
        }

        // map: orderId -> passengerNames
        Map<Long, List<String>> passengerNameMap = parsePassengerNameMap(hoPassengers);
        if (CollectionUtils.isEmpty(passengerNameMap)) {
            return Collections.emptyList();
        }

        // valid order map
        Map<Long, HoOrder> validOrderMap = getValidOrderMap(passengerNameMap.keySet());
        if (CollectionUtils.isEmpty(validOrderMap)) {
            return Collections.emptyList();
        }

        // valid order list
        Set<Long> validOrderIds = validOrderMap.keySet();

        //hotel map: orderId -> hotel
        Map<Long/*orderId*/, HoHotel> hotelMap = getOrderIdToHotelMap(validOrderIds);

        // room map: orderId -> room
        Map<Long, HoRoom> roomMap = getOrderIdToRoomMap(validOrderIds);

        // apply map: orderId -> HoHotelApplyDetail
        Map<Long, HoHotelApplyDetail> hoApplyDetailMap = getHoApplyDetailMap(validOrderIds, hotelModifiedReceiptDuplicateOrderControl);

        // valid order list
        List<OrderInfoBo> validOrderList = getValidOrderInfoList(validOrderMap,  hoApplyDetailMap, hotelMap, roomMap, passengerNameMap);

        // filter
        return getDuplicateOrderList(validOrderList, request.getCheckInDate(), request.getCheckOutDate());
    }

    public CheckDuplicateBookingResponse checkDuplicateBookingV2(CheckDuplicateBookingRequest request) {
        if (request == null) {
            return null;
        }

        // 获取差旅属性
        Map<String, SwitchInfoVo> currenctUserTravelAttributeMap = travelAttributeService.getCurrentUserTravelAttributeMap();

        // 酒店重复预订校验
        String hotelDuplicateOrderControl = travelAttributeService.getHotelDuplicateOrderControl(currenctUserTravelAttributeMap);
        DuplicateCheckModeEnum duplicateCheckModeEnum = DuplicateCheckModeEnum.getEnum(hotelDuplicateOrderControl);
        // 如果是因私仅提醒
        if (StringUtils.equalsIgnoreCase(request.getTravelMode(), TravelModeEnum.OWN.getCode()) || duplicateCheckModeEnum == null) {
            duplicateCheckModeEnum = DuplicateCheckModeEnum.ONLY_REMIND;
        }

        // 酒店修改中单据是否参与重复预订判断
        String hotelModifiedReceiptDuplicateOrderControl = travelAttributeService.getHotelModifiedReceiptDuplicateOrderControl(currenctUserTravelAttributeMap);

        // 重复订单获取
        List<OrderInfoBo> orderInfoBoList = checkDuplicateBooking(request.convertBo(), hotelModifiedReceiptDuplicateOrderControl);

        // 组装返回
        CheckDuplicateBookingResponse checkDuplicateBookingResponse = new CheckDuplicateBookingResponse();
        checkDuplicateBookingResponse.setDuplicateCheckMode(duplicateCheckModeEnum.getCode());
        if (CollectionUtils.isNotEmpty(orderInfoBoList)) {
            checkDuplicateBookingResponse.setOrderInfoList(orderInfoBoList);
            checkDuplicateBookingResponse.setDuplicate(true);
        } else {
            checkDuplicateBookingResponse.setOrderInfoList(new ArrayList<>(0));
            checkDuplicateBookingResponse.setDuplicate(false);
        }
        return checkDuplicateBookingResponse;
    }

    public CheckDuplicateBookingResponse checkDuplicateBookingV3(CheckDuplicateBookingRequest request) {
        CheckDuplicateBookingResponse checkDuplicateBookingResponse = new CheckDuplicateBookingResponse();

        List<OrderInfoBo> duplicateBookingOrderList = getDuplicateBookingOrderList(request.convertBo());
        log.info("重复订单列表：{}", JsonUtils.toJsonString(duplicateBookingOrderList));
        boolean notExistDuplicateBookingOrder = CollectionUtils.isEmpty(duplicateBookingOrderList);
        if (notExistDuplicateBookingOrder) {
            checkDuplicateBookingResponse.setOrderInfoList(Collections.emptyList());
            checkDuplicateBookingResponse.setDuplicate(false);
            return checkDuplicateBookingResponse;
        }

        checkDuplicateBookingResponse.setDuplicate(true);
        checkDuplicateBookingResponse.setOrderInfoList(duplicateBookingOrderList);
        
        //设置标签
        setDuplicateBookingOrderTag(duplicateBookingOrderList);

        // 如果是因私仅提醒
        boolean ownTravelMode = TravelModeEnum.OWN.getCode().equals(request.getTravelMode());
        if (ownTravelMode) {
            checkDuplicateBookingResponse.setDuplicateCheckMode(DuplicateCheckModeEnum.ONLY_REMIND.getCode());
            return checkDuplicateBookingResponse;
        }

        // 获取差旅属性
        Map<String, SwitchInfoVo> currentUserTravelAttributeMap = travelAttributeService.getCurrentUserTravelAttributeMap();

        DuplicateCheckModeEnum duplicateCheckModeEnum = getDuplicateCheckModeEnum(currentUserTravelAttributeMap);
        checkDuplicateBookingResponse.setDuplicateCheckMode(duplicateCheckModeEnum.getCode());

        // 重复订单列表中存在 标签全为 取消中/修改中 的重复订单
        boolean allProcessingTagDuplicateOrder = duplicateBookingOrderList.stream().allMatch(item -> Objects.nonNull(item.getTag()));
        if (!allProcessingTagDuplicateOrder) {
            return checkDuplicateBookingResponse;
        }

        // 酒店取消中/修改中单据是否参与重复预订判断
        Boolean hotelModifiedReceiptDuplicateOrderControlFlag = getHotelModifiedReceiptDuplicateOrderControlFlag(currentUserTravelAttributeMap);
        if (!hotelModifiedReceiptDuplicateOrderControlFlag) {
            return checkDuplicateBookingResponse;
        }

        judgeDuplicateAfterFormProcessed(duplicateBookingOrderList, request.getCheckInDate(), request.getCheckOutDate());
        log.info("判断处理单据后是否与下单重复的重复订单列表：{}", JsonUtils.toJsonString(duplicateBookingOrderList));

        boolean existDuplicateAfterFormProcessed = duplicateBookingOrderList.stream().anyMatch(OrderInfoBo::getDuplicateAfterFormProcessed);
        if (!existDuplicateAfterFormProcessed) {
            checkDuplicateBookingResponse.setDuplicateCheckMode(DuplicateCheckModeEnum.ONLY_REMIND.getCode());
            return checkDuplicateBookingResponse;
        }

        return checkDuplicateBookingResponse;
    }

    /**
     * 获取重复预订订单
     *
     * @param checkDuplicateBookingBo 检查重复预订请求对象
     * @return {@link List }<{@link OrderInfoBo }>
     */
    private List<OrderInfoBo> getDuplicateBookingOrderList(CheckDuplicateBookingBo checkDuplicateBookingBo) {

        List<HoOrder> passengersHistoryActiveOrderList = getPassengersHistoryActiveOrder(checkDuplicateBookingBo);
        if (CollectionUtils.isEmpty(passengersHistoryActiveOrderList)) {
            return Collections.emptyList();
        }

        List<HoOrder> duplicateBookingOrderList = getDuplicateBookingOrderList(passengersHistoryActiveOrderList, checkDuplicateBookingBo.getCheckInDate(), checkDuplicateBookingBo.getCheckOutDate());
        if (CollectionUtils.isEmpty(duplicateBookingOrderList)) {
            return Collections.emptyList();
        }

        //组装重复订单信息
        return getDuplicateBookingOrderInfoList(duplicateBookingOrderList);

    }

    /**
     * 获取乘客历史活跃订单
     * 活跃订单 【订单状态不为已取消，已完成的订单】
     *
     * @param request 请求
     * @return {@link List }<{@link HoOrder }>
     */
    private List<HoOrder> getPassengersHistoryActiveOrder(CheckDuplicateBookingBo request) {
        // 查询入住人的历史订单
        List<HoPassenger> hoPassengerList = hoPassengerLoader.listByPassengerIds(request.getUIds(), request.getNonEmployeeIds());
        boolean passengerNeverHasOrder = CollectionUtils.isEmpty(hoPassengerList);
        if (passengerNeverHasOrder) {
            return Collections.emptyList();
        }
        Set<Long> historyOrderIdSet = hoPassengerList.stream().map(HoPassenger::getOrderId).collect(Collectors.toSet());
        return hoOrderLoader.listValidOrdersByOrderIds(historyOrderIdSet);
    }


    /**
     * 获取重复预订订单列表
     *
     * @param passengersHistoryActiveOrderList 乘客历史活跃订单列表
     * @param checkInDate                      入住日期
     * @param checkOutDate                     退房日期
     * @return {@link List }<{@link HoOrder }>
     */
    private List<HoOrder> getDuplicateBookingOrderList(List<HoOrder> passengersHistoryActiveOrderList, Date checkInDate, Date checkOutDate) {
        return passengersHistoryActiveOrderList.stream().filter(historyActiveOrder -> {
            //订单当前的入离日期是否存在与下单的订单日期重叠
            String newestCheckInOutDate = historyActiveOrder.getNewestCheckInOutDate();
            List<CheckInOutDateInfoBo> checkInOutDateInfoBoList = JsonUtils.parseArray(newestCheckInOutDate, CheckInOutDateInfoBo.class);
            return checkInOutDateInfoBoList.stream().filter(item -> Objects.nonNull(item.getCheckInDate()) && Objects.nonNull(item.getCheckOutDate())).anyMatch(item -> compareDate(checkInDate, item.getCheckOutDate()) < 0 && compareDate(checkOutDate, item.getCheckInDate()) > 0);
        }).collect(Collectors.toList());
    }

    /**
     * 获取重复预订订单信息
     *
     * @param duplicateBookingOrderList 重复预订订单列表
     * @return {@link List }<{@link OrderInfoBo }>
     */
    private List<OrderInfoBo> getDuplicateBookingOrderInfoList(List<HoOrder> duplicateBookingOrderList) {

        Set<Long> duplicateBookingOrderIdSet = duplicateBookingOrderList.stream().map(HoOrder::getOrderId).collect(Collectors.toSet());

        // passengerNames Map: orderId -> passengerNameList
        Map<Long, List<String>> orderIdToPassengerNamesMap = getOrderIdToPassengerNamesMap(duplicateBookingOrderIdSet);

        // hotel map: orderId -> hotel
        Map<Long, HoHotel> orderIdToHotelMap = getOrderIdToHotelMap(duplicateBookingOrderIdSet);

        // room map: orderId -> room
        Map<Long, HoRoom> orderIdToRoomMap = getOrderIdToRoomMap(duplicateBookingOrderIdSet);
        
        // assemble order info
        Map<Long, HoOrder> orderIdToHoOrderMap = duplicateBookingOrderList.stream().collect(Collectors.toMap(HoOrder::getOrderId, Function.identity()));
        List<OrderInfoBo> duplicateBookingOrderInfoList = Lists.newArrayListWithCapacity(orderIdToHoOrderMap.size());
        orderIdToHoOrderMap.forEach((orderId, order) -> {
            List<String> passengers = orderIdToPassengerNamesMap.get(orderId);
            HoHotel hotel = orderIdToHotelMap.get(orderId);
            HoRoom room = orderIdToRoomMap.get(orderId);
            OrderInfoBo orderInfoBo = OrderInfoBo.assembly(order, passengers, hotel, room);
            Optional.ofNullable(orderInfoBo).ifPresent(duplicateBookingOrderInfoList::add);
        });
        return duplicateBookingOrderInfoList;
    }


    /**
     * 获取乘客姓名map
     * key: orderId value: 乘客姓名列表
     *
     * @param duplicateBookingOrderIdSet 重复预订订单 ID 设置
     * @return {@link Map }<{@link Long }, {@link List }<{@link String }>>
     */
    private Map<Long, List<String>> getOrderIdToPassengerNamesMap(Set<Long> duplicateBookingOrderIdSet) {
        List<HoPassenger> hoPassengerList =
            hoPassengerLoader.selectByOrderIds(new ArrayList<>(duplicateBookingOrderIdSet));
        if (CollectionUtils.isEmpty(hoPassengerList)) {
            return Collections.emptyMap();
        }
        return hoPassengerList.stream().collect(Collectors.groupingBy(HoPassenger::getOrderId, Collectors.mapping(HoPassenger::getPassengerName, Collectors.toList())));
    }

    /**
     * 设置重复预订订单标签 (取消中/修改中)
     * 先判定是否存在取消中单据 若无再判定存在修改中单据
     *
     * @param duplicateBookingOrderList 重复预订订单列表
     */
    private void setDuplicateBookingOrderTag(List<OrderInfoBo> duplicateBookingOrderList) {
        Set<Long> duplicateBookingOrderIdSet =
            duplicateBookingOrderList.stream().map(OrderInfoBo::getOrderId).collect(Collectors.toSet());
        List<HoOrderCancelForm> hoOrderCancelFormList =
            hoOrderCancelFormLoader.listBeingCanceledFormByOrderIds(duplicateBookingOrderIdSet);
        Map<Long, HoOrderCancelForm> orderIdToOrderCancelFormMap = hoOrderCancelFormList.stream()
            .collect(Collectors.toMap(HoOrderCancelForm::getOrderId, Function.identity()));
        duplicateBookingOrderList.forEach(orderInfoBo -> {
            HoOrderCancelForm hoOrderCancelForm = orderIdToOrderCancelFormMap.get(orderInfoBo.getOrderId());
            Optional.ofNullable(hoOrderCancelForm).ifPresent(item -> {
                orderInfoBo.setTag(TAG_CANCELLING);
                orderInfoBo.setDuplicateAfterFormProcessed(Boolean.FALSE);
            });
        });

        // 获取差集 没有取消中的单据还要确认是否存在修改中的单据
        Set<Long> notExistCancellingFormOrderIdSet = duplicateBookingOrderList.stream()
            .filter(item -> Objects.isNull(item.getTag())).map(OrderInfoBo::getOrderId).collect(Collectors.toSet());

        List<HoHotelApply> hoHotelApplyList =
            hoHotelApplyLoader.listBeingModifiedByOrderIds(notExistCancellingFormOrderIdSet);
        Map<Long, HoHotelApply> orderIdToHoHotelApplyMap =
            hoHotelApplyList.stream().collect(Collectors.toMap(HoHotelApply::getOrderId, Function.identity()));
        duplicateBookingOrderList.stream().filter(item -> Objects.isNull(item.getTag()))
            .forEach(duplicateBookingOrder -> {
                HoHotelApply hoHotelApply = orderIdToHoHotelApplyMap.get(duplicateBookingOrder.getOrderId());
                Optional.ofNullable(hoHotelApply).ifPresent(item -> duplicateBookingOrder.setTag(TAG_MODIFYING));
            });
    }

    /**
     * 获取重复检查模式枚举
     *
     * @param currentUserTravelAttributeMap 当前用户出行属性
     * @return {@link DuplicateCheckModeEnum }
     */
    private DuplicateCheckModeEnum getDuplicateCheckModeEnum(Map<String, SwitchInfoVo> currentUserTravelAttributeMap) {
        // 酒店重复预订校验
        String hotelDuplicateOrderControl =
                travelAttributeService.getHotelDuplicateOrderControl(currentUserTravelAttributeMap);
        return DuplicateCheckModeEnum.getEnum(hotelDuplicateOrderControl);
    }

    /**
     * 获取酒店修改/取消单据重复订单控制标志 默认false
     *
     * @param currentUserTravelAttributeMap 当前用户出行属性图
     * @return {@link Boolean }
     */
    private Boolean getHotelModifiedReceiptDuplicateOrderControlFlag(Map<String, SwitchInfoVo> currentUserTravelAttributeMap) {
        String hotelModifiedReceiptDuplicateOrderControl =
            travelAttributeService.getHotelModifiedReceiptDuplicateOrderControl(currentUserTravelAttributeMap);
        return StringUtils.isNotBlank(hotelModifiedReceiptDuplicateOrderControl)
            ? HOTEL_MODIFIED_RECEIPT_DUPLICATE_ORDER_CONTROL_ENABLED.equals(hotelModifiedReceiptDuplicateOrderControl) : Boolean.FALSE;
    }

    /**
     * 判断处理单据后是否与下单重复
     *
     * @param duplicateBookingOrderList 重复预订订单列表
     * @param checkInDate               入住日期
     * @param checkOutDate              退房日期
     */
    private void judgeDuplicateAfterFormProcessed(List<OrderInfoBo> duplicateBookingOrderList, Date checkInDate, Date checkOutDate) {

        Set<Long> modifyingOrderIdSet = duplicateBookingOrderList.stream()
                .filter(item -> TAG_MODIFYING.equals(item.getTag()))
                .map(OrderInfoBo::getOrderId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(modifyingOrderIdSet)) {
            return;
        }

        //orderId -> applyId -> applyDetail [AfterRecord]
        List<HoHotelApply> hoHotelApplyList = hoHotelApplyLoader.listBeingModifiedByOrderIds(modifyingOrderIdSet);
        Map<Long, String> orderIdToApplyIdMap = hoHotelApplyList.stream().collect(Collectors.toMap(HoHotelApply::getOrderId, HoHotelApply::getApplyId));
        List<HoHotelApplyDetail> hoHotelApplyDetailList = hoHotelApplyDetailLoader.listAfterRecordByApplyIds(orderIdToApplyIdMap.values());
        Map<String, HoHotelApplyDetail> applyIdToApplyDetailMap = hoHotelApplyDetailList.stream().collect(Collectors.toMap(HoHotelApplyDetail::getApplyId, Function.identity()));

        duplicateBookingOrderList.forEach(item -> {
            if (!modifyingOrderIdSet.contains(item.getOrderId())) {
                item.setDuplicateAfterFormProcessed(Boolean.FALSE);
                return;
            }

            // 获取修改中单据修改成功后入离日期
            String applyId = orderIdToApplyIdMap.get(item.getOrderId());
            HoHotelApplyDetail afterRecord = applyIdToApplyDetailMap.get(applyId);
            String checkInOutDateDetail = afterRecord.getCheckInOutDateDetail();
            List<CheckInOutDateInfoBo> checkInOutDateInfoBoList =
                JsonUtils.parseArray(checkInOutDateDetail, CheckInOutDateInfoBo.class);
            // 修改中单据修改成功后 是否存在与下单的订单日期重叠
            boolean duplicateAfterFormProcessed = checkInOutDateInfoBoList.stream().anyMatch(
                checkInOutDateInfoBo -> compareDate(checkInDate, checkInOutDateInfoBo.getCheckOutDate()) < 0
                    && compareDate(checkOutDate, checkInOutDateInfoBo.getCheckInDate()) > 0);
            item.setDuplicateAfterFormProcessed(duplicateAfterFormProcessed);
        });
    }

    /**
     * 获取交叉记录
     * 过滤条件：
     *      入住时间 < 历史订单的离店时间， checkInDate < item.checkOutDate
     *      离开时间 > 历史订单的入住时间， checkOutDate > item.checkInDate
     */
    private List<OrderInfoBo> getDuplicateOrderList(List<OrderInfoBo> validOrderList, Date checkInDate, Date checkOutDate) {
        if(CollectionUtils.isEmpty(validOrderList)){
            return Collections.emptyList();
        }
        return validOrderList.stream().filter(
                item -> compareDate(checkInDate, item.getCheckOutDate()) < 0 && compareDate(checkOutDate, item.getCheckInDate()) > 0
        ).collect(Collectors.toList());
    }



    /**
     * 封装有效订单
     */
    private List<OrderInfoBo> getValidOrderInfoList(Map<Long, HoOrder> validOrderMap,
                                                    Map<Long, HoHotelApplyDetail> hoApplyDetailMap,
                                                    Map<Long, HoHotel> hotelMap, Map<Long, HoRoom> roomMap,
                                                    Map<Long, List<String>> passengerNameMap) {
        List<OrderInfoBo> bos = Lists.newArrayListWithCapacity(validOrderMap.size());
        for(Map.Entry<Long, HoOrder> entry : validOrderMap.entrySet()){
            Long orderId = entry.getKey();
            HoOrder order = entry.getValue();
            HoHotel hotel = hotelMap.get(orderId);
            HoRoom room = roomMap.get(orderId);
            List<String> passengers = passengerNameMap.get(orderId);
            HoHotelApplyDetail apply = hoApplyDetailMap.get(orderId);
            OrderInfoBo bo;
            if(apply == null){
                bo = OrderInfoBo.assembly(order, passengers, hotel, room);
            }else{
                bo = OrderInfoBo.assembly(order, passengers, hotel, room, apply);
            }
            if(bo == null){
                log.warn("##存在不完整的order，orderId:[{}]\n, order:[{}]\n, hotel:[{}]\n, room:[{}]\n, passengers:[{}],\n apply:[{}]", order.getOrderId(), order, hotel, room, passengers, apply);
            }else{
                bos.add(bo);
            }
        }
        return bos;
    }


    /**
     * get valid order map: key-orderId, value-hoOrder
     * 排除status: 5-已取消、6-已完成
     */
    private Map<Long/*orderId*/, HoOrder> getValidOrderMap(Set<Long> orderIds){
        List<HoOrder> hoOrders = hoOrderLoader.listValidOrdersByOrderIds(orderIds);
        if(CollectionUtils.isEmpty(hoOrders)){
            return Collections.emptyMap();
        }
        return hoOrders.stream().collect(Collectors.toMap(HoOrder::getOrderId, item->item));
    }

    /**
     * 查询map -> hoRoom
     */
    private Map<Long, HoRoom> getOrderIdToRoomMap(Collection<Long> validOrderIds) {
        List<HoRoom> roomList = hoRoomLoader.listByOrderIds(validOrderIds);
        if(CollectionUtils.isEmpty(roomList)){
            return Collections.emptyMap();
        }
        return roomList.stream().collect(Collectors.toMap(HoRoom::getOrderId, item->item));
    }

    /**
     * 查询 map : orderId -> hoHotel
     */
    private Map<Long, HoHotel> getOrderIdToHotelMap(Set<Long> validOrderIds) {
        List<HoHotel> hoHotelList = hoHotelLoader.listByOrderIds(validOrderIds);
        if(CollectionUtils.isEmpty(hoHotelList)){
            return Collections.emptyMap();
        }
        return hoHotelList.stream().collect(Collectors.toMap(HoHotel::getOrderId, item->item));
    }

    /**
     * 根据orderId 获取 getHoApplyDetailMap
     * getHoApplyDetailMap: key-orderId, value:HoHotelApplyDetail
     */
    private Map<Long/*orderId*/, HoHotelApplyDetail> getHoApplyDetailMap(Collection<Long> orderIds, String hotelModifiedReceiptDuplicateOrderControl) {
        Map<Long/*OrderId*/, HoHotelApply> orderModifyApplyMap = getOrderModifyApplyMap(orderIds, hotelModifiedReceiptDuplicateOrderControl);
        if(CollectionUtils.isEmpty(orderModifyApplyMap)){
            return Collections.emptyMap();
        }

        Set<String> applyIds = orderModifyApplyMap.values().stream().map(HoHotelApply::getApplyId).collect(Collectors.toSet());

        Map<String/*applyId*/, HoHotelApplyDetail> applyDetailMap = getorderModifyApplyDetailMap(applyIds);
        if(CollectionUtils.isEmpty(applyDetailMap)){
            return Collections.emptyMap();
        }

        Map<Long, HoHotelApplyDetail> retMap = new HashedMap();
        for(Map.Entry<Long/*orderId*/, HoHotelApply> entry:orderModifyApplyMap.entrySet()){
            Long orderId = entry.getKey();
            HoHotelApply apply = entry.getValue();
            String applyId = apply.getApplyId();
            if(applyDetailMap.containsKey(applyId)){
                retMap.put(orderId, applyDetailMap.get(applyId));
            }
        }

        return retMap;
    }

    /**
     * 根据applyIds查HoHotelApplyDetail
     */
    private Map<String/*applyId*/, HoHotelApplyDetail> getorderModifyApplyDetailMap(Set<String> applyIds) {
        if(CollectionUtils.isEmpty(applyIds)){
            return Collections.emptyMap();
        }

        List<HoHotelApplyDetail> hotelApplyDetailList = hoHotelApplyDetailLoader.listAfterRecordByApplyIds(applyIds);
        if(CollectionUtils.isEmpty(hotelApplyDetailList)){
            return Collections.emptyMap();
        }

        return hotelApplyDetailList.stream().collect(Collectors.toMap(HoHotelApplyDetail::getApplyId, item -> item));
    }

    /**
     * 根据orderId 查询有效的hoHotelApply 记录
     * 有效：status = 4 , success
     * 一个orderId对应多条record, 取最后一条（根据DatachangeLasttime 排序）
     */
    private Map<Long/*orderId*/, HoHotelApply> getOrderModifyApplyMap(Collection<Long> orderIds, String hotelModifiedReceiptDuplicateOrderControl) {
        List<HoHotelApply> hoHotelApplyList;
        if (StringUtils.equalsIgnoreCase(hotelModifiedReceiptDuplicateOrderControl, "1")) {
            hoHotelApplyList = hoHotelApplyLoader.listNoFailByOrderIds(orderIds);
        } else {
            hoHotelApplyList = hoHotelApplyLoader.listSuccessByOrderIds(orderIds);
        }
        if(CollectionUtils.isEmpty(hoHotelApplyList)){
            return Collections.emptyMap();
        }

        Map<Long, List<HoHotelApply>> tempMap = hoHotelApplyList.stream()
                .collect(Collectors.groupingBy(HoHotelApply::getOrderId));

        return tempMap.entrySet().stream().collect(Collectors.toMap(
                entry -> entry.getKey(),
                entry -> {
                    HoHotelApply last = null;
                    for (HoHotelApply apply : entry.getValue()) {
                        if (last == null || last.getDatachangeLasttime().before(apply.getDatachangeLasttime())) {
                            last = apply;
                        }
                    }
                    return last;
                }
        ));
    }

    /**
     * 获取orderId -> passengerNames 的映射关系
     */
    private Map<Long/**orderId**/, List<String>/**PassengerNames**/> parsePassengerNameMap(List<HoPassenger> hoPassengers) {
       if(CollectionUtils.isEmpty(hoPassengers)){
           return Collections.emptyMap();
       }
       Map<Long, List<HoPassenger>> tempMap = hoPassengers.stream().collect(Collectors.groupingBy(HoPassenger::getOrderId));
       return tempMap.entrySet().stream().collect(Collectors.toMap(
               Map.Entry::getKey, // key
           entry -> { // value
               List<HoPassenger> passengers = entry.getValue();
               return passengers.stream().map(HoPassenger::getPassengerName).collect(Collectors.toList());
           }
       ));
    }
    /**
     * 恢复订单请求中的脱敏数据
     *
     * @param request 订单请求对象
     */
    private void recoverDesensitizedDataToken(CheckOrderRequestVo request) {
        // 组装原始数据
        InitOrderResponseVo initOrderBasicResponse = new InitOrderResponseVo();
        OrgEmployeeVo orgEmployeeVo = getEmployeeInfo(request.getUserInfo().getUid(), request.getUserInfo().getOrgId());
        ContactInfo contactInfo = toContactInfo(orgEmployeeVo);
        initOrderBasicResponse.setContactInfo(contactInfo);

        InvoiceInfoVo invoiceInfoCache = toInvoiceInfo(request.getUserInfo());
        initOrderBasicResponse.setInvoiceInfo(invoiceInfoCache);
        log.info("脱敏恢复后的数据invoiceInfoCache:{}", JsonUtils.toJsonString(invoiceInfoCache));

        if (initOrderBasicResponse == null) {
            throw new CorpBusinessException(ExceptionCodeEnum.PageTimeout, "您停留时间过长，请重新查询价格");
        }
        // 进行数据脱敏
        ContactsInfo contactsInfo = request.getContactsInfo();
        if (contactsInfo != null) {
            recoverContactsInfo(contactsInfo, initOrderBasicResponse);
        }
        log.info("脱敏恢复后的数据contactsInfo:{}", JsonUtils.toJsonString(contactsInfo));

        InvoiceInfoVo invoiceInfo = request.getInvoiceInfo();
        if (invoiceInfo != null) {
            recoverInvoiceInfo(invoiceInfo, initOrderBasicResponse);
        }
        log.info("脱敏恢复后的数据invoiceInfo:{}", JsonUtils.toJsonString(invoiceInfo));

        List<PassengerInfo> passengerInfoList = request.getPassengerInfoList();
        log.info("recoverDesensitizedDataToken,passengerInfoList before:{}",JsonUtils.toJsonString(passengerInfoList));
        if (CollectionUtils.isNotEmpty(passengerInfoList)) {
            recoverPassengerList(passengerInfoList);
        }
        log.info("recoverDesensitizedDataToken,passengerInfoList after:{}",JsonUtils.toJsonString(passengerInfoList));
        log.info("脱敏恢复后的数据：{}", JsonUtils.toJsonString(request));
    }

    /**
     * 将用户信息转化为发票信息
     *
     * @param userInfo 用户信息
     * @return 发票信息
     * @throws CorpBusinessException 获取发票信息失败时抛出异常
     */
    private InvoiceInfoVo toInvoiceInfo(UserInfo userInfo) {
        CommonInvoiceVo data = commonInvoiceClientLoader.getDefaultInvoice(userInfo.getUid(), userInfo.getOrgId());
        if (data == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_INVOICE_INFORMATION);
        }
        InvoiceInfoVo vo = new InvoiceInfoVo();
        vo.setId(data.getId());
        vo.setInvoiceTitleType(data.getInvoiceTitleType());
        vo.setInvoiceType(data.getInvoiceType());
        vo.setAccountBank(data.getAccountBank());
        vo.setAccountCardNo(data.getAccountCardNo());
        vo.setCorporationAddress(data.getCorporationAddress());
        vo.setCorporationTel(data.getCorporationTel());
        vo.setInvoiceTitle(data.getInvoiceTitle());
        vo.setTaxpayerNumber(data.getTaxpayerNumber());
        return vo;
    }

    /**
     * 恢复脱敏后数据
     *
     * @param request
     */
    private void recoverDesensitizedData(CheckOrderRequestVo request) {
        InitOrderResponseVo initOrderBasicResponse = orderInfoCacheManager.getInitOrderBasicResponse();
        if (initOrderBasicResponse == null) {
            throw new CorpBusinessException(ExceptionCodeEnum.PageTimeout, "您停留时间过长，请重新查询价格");
        }
        ContactsInfo contactsInfo = request.getContactsInfo();
        if (contactsInfo != null) {
            recoverContactsInfo(contactsInfo, initOrderBasicResponse);
        }
        InvoiceInfoVo invoiceInfo = request.getInvoiceInfo();
        if (invoiceInfo != null) {
            recoverInvoiceInfo(invoiceInfo, initOrderBasicResponse);
        }
        // feign获取passenger数据并恢复
        List<PassengerInfo> passengerInfoList = request.getPassengerInfoList();
        if (CollectionUtils.isNotEmpty(passengerInfoList)) {
            recoverPassengerList(passengerInfoList);
        }
        log.info("脱敏恢复后的数据：{}", JsonUtils.toJsonString(request));
    }

    private void recoverPassengerList(List<PassengerInfo> passengerInfoList) {
        List<PassengerInfo> desensitizedPassengerList =
            passengerInfoList.stream().filter(p -> hasDesensitizedData(p)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(desensitizedPassengerList)) {
            return;
        }
        List<PassengerVo> passengerListRequest =
            desensitizedPassengerList.stream().map(p -> toListPassengerInfoRequest(p)).collect(Collectors.toList());
        List<PassengerVo> originalPassengerVos = passengerClientLoader.listPassengerInfo(passengerListRequest);
        Map<String, com.corpgovernment.api.organization.model.passenger.PassengerVo> originalPassengerMap =
                Optional.ofNullable(originalPassengerVos).map(o -> o.stream()).orElse(Stream.empty())
                        .collect(Collectors.toMap(com.corpgovernment.api.organization.model.passenger.PassengerVo::getUid,
                                Function.identity(), (a, b) -> a));
        Map<String, com.corpgovernment.api.organization.model.passenger.PassengerVo> originalNoEmployeePassengerMap =
                Optional.ofNullable(originalPassengerVos).map(o -> o.stream()).orElse(Stream.empty())
                        .collect(Collectors.toMap(com.corpgovernment.api.organization.model.passenger.PassengerVo::getNoEmployeeId,
                                Function.identity(), (a, b) -> a));
        // feign data
        Map<String, PassengerInfo> employeePassengerMap = desensitizedPassengerList.stream().collect(Collectors.toMap(PassengerInfo::getUid, Function.identity(), (a, b) -> a));
        Map<String, PassengerInfo> nonEmployeePassengerMap = desensitizedPassengerList.stream().collect(Collectors.toMap(PassengerInfo::getNoEmployeeId, Function.identity(), (a, b) -> a));

        // employee
        recoverPassengerFromFeignGeneral(employeePassengerMap, originalPassengerMap);
        // nonEmployee
        recoverPassengerFromFeignGeneral(nonEmployeePassengerMap, originalNoEmployeePassengerMap);
    }

    private void recoverPassengerFromFeignGeneral(Map<String, PassengerInfo> employeePassengerMap, Map<String, PassengerVo> originalPassengerMap) {
        employeePassengerMap.forEach((id, d) -> {
            if (StringUtils.isBlank(id)) {
                return;
            }
            PassengerVo originalPassengerVo =
                    originalPassengerMap.get(id);
            String mobilePhone = d.getMobilePhone();
            if (mobilePhone != null) {
                recoverGeneralDesensitizedData(d, originalPassengerVo, PassengerInfo::getMobilePhone,
                    PassengerInfo::setMobilePhone,
                    p -> Optional.ofNullable(p).map(PassengerVo::getTel).map(MobilePhoneVo::getValue).orElse(null));
            }
            TelVo tel = d.getTel();
            if (tel != null) {
                recoverGeneralDesensitizedData(tel,
                    Optional.ofNullable(originalPassengerVo).map(PassengerVo::getTel).orElse(null), TelVo::getValue,
                    TelVo::setValue, MobilePhoneVo::getValue);
            }

            // 卡信息
            List<CheckOrderRequestVo.Card> cardList = d.getCard();
            if (CollectionUtils.isNotEmpty(cardList)) {
                // 原始信息
                Map<Integer, CardVo> cardMap = new HashMap<>();
                List<CardVo> tmpCardList = Optional.ofNullable(originalPassengerVo).map(PassengerVo::getSource).orElse(new ArrayList<>(0));
                for (CardVo cardVo : tmpCardList) {
                    if (cardVo == null || cardVo.getType() == null) {
                        continue;
                    }
                    cardMap.put(cardVo.getType(), cardVo);
                }

                // 恢复脱敏数据
                for (CheckOrderRequestVo.Card card : cardList) {
                    if (card == null || card.getType() == null) {
                        continue;
                    }

                    String value = card.getValue();
                    if (checkDesensitizedData(value)) {
                        card.setValue(cardMap.getOrDefault(card.getType(), new CardVo()).getValue());
                    }
                }
            }

            // 邮箱
            String mail = d.getMail();
            if (checkDesensitizedData(mail)) {
                d.setMail(Optional.ofNullable(originalPassengerVo).map(PassengerVo::getMail).orElse(null));
            }
        });
    }

    private boolean checkDesensitizedData(String str) {
        return StringUtils.isNotBlank(str) && str.contains("**");
    }

    private boolean hasDesensitizedData(PassengerInfo passengerInfo) {
        boolean hasId = StringUtils.isNotBlank(passengerInfo.getUid()) || StringUtils.isNotBlank(passengerInfo.getNoEmployeeId());
        boolean hasDes = StringUtils.isNotBlank(passengerInfo.getMobilePhone())
                && DesensitizationUtils.isDesensitized(passengerInfo.getMobilePhone())
                || Optional.ofNullable(passengerInfo.getTel()).map(t -> t.getValue())
                    .map(v -> DesensitizationUtils.isDesensitized(v)).orElse(false);
        if (!hasId && hasDes) {
            log.warn("存在脱敏数据未找到原数据, 脱敏数据: {}", com.ctrip.corp.obt.generic.utils.JsonUtils.toJsonString(passengerInfo));
        }
        return hasId && hasDes;
    }

    private PassengerVo toListPassengerInfoRequest(PassengerInfo passengerInfo) {
        PassengerVo passengerVo = new PassengerVo();
        passengerVo.setUid(passengerInfo.getUid());
        passengerVo.setNoEmployeeId(passengerInfo.getNoEmployeeId());
        passengerVo.setOrgId(passengerInfo.getOrgId());
        return passengerVo;
    }

    private void recoverInvoiceInfo(InvoiceInfoVo invoiceInfo, InitOrderResponseVo initOrderBasicResponse) {
        InvoiceInfoVo originalInvoiceInfo = initOrderBasicResponse.getInvoiceInfo();
        recoverGeneralDesensitizedData(invoiceInfo, originalInvoiceInfo, InvoiceInfoVo::getEmail,
            InvoiceInfoVo::setEmail,
            InvoiceInfoVo::getEmail);
        // recover DeliveryInfo
        recoverDeliveryInfoVo(invoiceInfo.getDeliveryInfo(), originalInvoiceInfo);
        recoverDeliveryInfoVo(invoiceInfo.getPriDeliveryInfo(), originalInvoiceInfo);
        recoverDeliveryInfoVo(invoiceInfo.getPubDeliveryInfo(), originalInvoiceInfo);
    }

    private void recoverDeliveryInfoVo(DeliveryInfoVo deliveryInfo, InvoiceInfoVo originalInvoiceInfo) {
        if (deliveryInfo != null) {
            DeliveryInfoVo originalDeliveryInfo =
                Optional.ofNullable(originalInvoiceInfo).map(InvoiceInfoVo::getDeliveryInfo).orElse(null);
            recoverGeneralDesensitizedData(deliveryInfo, originalDeliveryInfo, DeliveryInfoVo::getAddress,
                DeliveryInfoVo::setAddress, DeliveryInfoVo::getAddress);
            recoverGeneralDesensitizedData(deliveryInfo, originalDeliveryInfo, DeliveryInfoVo::getRecipientMobile,
                DeliveryInfoVo::setRecipientMobile, DeliveryInfoVo::getRecipientMobile);
        }
    }

    private void recoverContactsInfo(ContactsInfo contactsInfo, InitOrderResponseVo initOrderBasicResponse) {
        ContactInfo originalContactsInfo = initOrderBasicResponse.getContactInfo();
        recoverGeneralDesensitizedData(contactsInfo, originalContactsInfo, ContactsInfo::getPhone,
            ContactsInfo::setPhone,
            ContactInfo::getPhone);
        recoverGeneralDesensitizedData(contactsInfo, originalContactsInfo, ContactsInfo::getEmail,
            ContactsInfo::setEmail,
            ContactInfo::getEmail);
    }

    /**
     * user数据若为脱敏后数据，则更新为原始的未脱敏数据
     *
     * @param originalData
     * @param userData
     * @param originalGetter
     * @param userGetter
     * @param userSetter
     * @param <T>
     * @param <U>
     */
    private <T, U> void recoverGeneralDesensitizedData(U userData, @Nullable T originalData,
        Function<U, String> userGetter,
        BiConsumer<U, String> userSetter, Function<T, String> originalGetter) {
        if (userData == null || originalGetter == null || userSetter == null || userSetter == null) {
            return;
        }
        String userFieldValue = userGetter.apply(userData);
        if (StringUtils.isNotBlank(userFieldValue) && DesensitizationUtils.isDesensitized(userFieldValue)) {
            userSetter.accept(userData,
                Optional.ofNullable(originalData).map(o -> originalGetter.apply(o)).orElse(null));
        }
    }

    private void checkParams(CheckOrderRequestVo request, UserInfo userInfo, OrderInfoModel orderInfo) {

        if (CollectionUtils.isEmpty(request.getPassengerInfoList())) {
            throw new CorpBusinessException(HotelResponseCodeEnum.PASSENGER_LIST_IS_NULL);
        }

        List<AoApplyTripTempCostVo> tempCostVoList = new ArrayList<>();
        if(TripTypeEnum.PUB.getType().equals(orderInfo.getCorpPayType()) && Objects.isNull(orderInfo.getTrafficId())
                && CollectionUtils.isNotEmpty(request.getPassengerInfoList())) {
            tempCostVoList = applyTripClientLoader.getTripTempCostSoa(request.getUrgentApply(), request.getUserInfo().getCorpId());
        }

        Map<String, GetOrgConfigResponse> orgConfigMap = new HashedMap();
        for (PassengerInfo passenger : request.getPassengerInfoList()) {
            String orgId = passenger.getOrgId();
            String name = StringUtils.isNotBlank(passenger.getName()) ? passenger.getName() : passenger.getPassport();
            if (StringUtils.isBlank(orgId)) {
                orgId = userInfo.getOrgId();
            }

            GetOrgConfigResponse getOrgConfigResponse = orgConfigMap.get(orgId);
            if (Objects.isNull(getOrgConfigResponse)) {
                getOrgConfigResponse = organizationClientLoader.orgConfigGet(orgId);
                orgConfigMap.put(orgId, getOrgConfigResponse);
            }

            // 校验成本中心
            if (TripTypeEnum.PUB.getType().equals(orderInfo.getCorpPayType())
                && getOrgConfigResponse.getOrgConfigInfo().getCostCenterIsVerify()
                    && Objects.isNull(orderInfo.getTrafficId())) {
                if (Objects.isNull(passenger.getCostCenter())) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.COST_CENTER_IS_NULL, name);
                }
                CheckOrderRequestVo.CostCenterVo costCenter = passenger.getCostCenter();
                if (StringUtils.isBlank(costCenter.getCostCenterName())) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.COST_CENTER_NAME_IS_NULL, name);
                }
                if (StringUtils.isBlank(costCenter.getCostCenterId())) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.COST_CENTER_ID_IS_NULL, name);
                }
            }
            // 校验项目号
            if (TripTypeEnum.PUB.getType().equals(orderInfo.getCorpPayType())
                && getOrgConfigResponse.getOrgConfigInfo().getProjectIsVerify()
                    && Objects.isNull(orderInfo.getTrafficId()) ) {

                if (Objects.isNull(passenger.getProjectInfo())) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.PROJECT_IS_NULL, name);
                }

                CheckOrderRequestVo.ProjectInfo projectInfo = passenger.getProjectInfo();
                if (StringUtils.isBlank(projectInfo.getProjectName())
                    && StringUtils.isBlank(projectInfo.getNoSelectProjectDesc())) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.PROJECT_IS_NULL, name);
                }

                if (StringUtils.isNotBlank(projectInfo.getProjectName())
                    && StringUtils.isBlank(projectInfo.getProjectId())) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.PROJECT_ID_IS_NULL, name);
                }
            }

            if(CollectionUtils.isNotEmpty(tempCostVoList)) {
                tempCostVoList.stream().forEach(t->{
                    if(!ApplyTripTempEnableEnum.VERIFY.getCode().equals(t.getTempEnable())) {
                        return;
                    }
                    if(CollectionUtils.isEmpty(passenger.getCostCenterVoList())) {
                        throw new CorpBusinessException(HotelResponseCodeEnum.TEMP_COST_CENTER_IS_NULL, name, t.getCostName());
                    }
                    List<TempCostCenterVo> costCenterList = passenger.getCostCenterVoList().stream()
                            .filter(c-> StringUtils.equalsIgnoreCase(c.getCostCode(), t.getCostCode())
                                    && StringUtils.isNotBlank(c.getCostCenterCode())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(costCenterList)) {
                        throw new CorpBusinessException(HotelResponseCodeEnum.TEMP_COST_CENTER_IS_NULL, name, t.getCostName());
                    }
                });
            }

            // 校验核算单元
            if (TripTypeEnum.PUB.getType().equals(orderInfo.getCorpPayType())) {
                checkAccountingUnit(passenger, getOrgConfigResponse);
            }
        }
    }

	/**
	 * 生成订单号
	 *
	 * @param uid
	 * @return
	 */
	private Long productionOrderId(String uid) {
		// 获取订单号
		Long orderId = basicDataClientLoader.productionOrderId(uid);
		if (orderId == null) {
			throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_ORDER_NUMBER);
		}
		return orderId;
	}

    /**
     * 生成订单号
     *
     * @param uid
     * @return
     */
    private Long productionOrderId(String uid,String productType) {
        // 获取订单号
        Long orderId = basicDataClientLoader.productionOrderId(uid,productType);
        log.info("productionOrderId,uid:{},productType:{},orderId:{}", uid, productType, orderId);
        if (orderId == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_ORDER_NUMBER);
        }
        return orderId;
    }

    private void setPassengerOrgName(CheckOrderRequestVo requestVo) {
        List<PassengerInfo> passengerInfoList = requestVo.getPassengerInfoList();
        // 入住人设置组织名称
        List<String> orgIds = passengerInfoList.stream().map(CheckOrderRequestVo.PassengerInfo::getOrgId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgIds)) {
            return;
        }
        List<OrgInfoVo> orgInfoList = organizationClientLoader.findOrgInfoByOrgIds(orgIds);
        if (CollectionUtils.isEmpty(orgInfoList)) {
            return;
        }
        Map<String, OrgInfoVo> orgInfoMap =
            orgInfoList.stream().collect(Collectors.toMap(OrgInfoVo::getOrgId, e -> e, (e1, e2) -> e1));
        passengerInfoList.forEach(e -> {
            OrgInfoVo orgInfoVo = orgInfoMap.getOrDefault(e.getOrgId(), new OrgInfoVo());
            e.setOrgName(orgInfoVo.getName());
        });
    }


    private int compareDate(Date date1, Date date2){
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate1.compareTo(localDate2);
    }

    private void checkAccountingUnit(PassengerInfo passenger, GetOrgConfigResponse getOrgConfigResponse) {
        List<CheckOrderRequestVo.AccountingUnitVo> accountingUnitLis = passenger.getAccountingUnitCategoryConfigList();
        if (CollectionUtils.isEmpty(accountingUnitLis)){
            return;
        }
        Map<String, CheckOrderRequestVo.AccountingUnitVo> accountingUnitReqMap = accountingUnitLis.stream().collect(Collectors.toMap(p->p.getCategoryCode(), p->p, (a,b)->a));
        // 读取组织配置
        Map<String, OrgAccountingUnitCategoryConfig> categoryConfigMap = Optional.ofNullable(getOrgConfigResponse)
                .map(GetOrgConfigResponse::getOrgConfigInfo)
                .map(OrgConfigInfoVo::getAccountingUnitCategoryConfigList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream().collect(Collectors.toMap(
                        OrgAccountingUnitCategoryConfig::getCategoryCode,
                        Function.identity(),
                        (v1, v2) -> v1))
                )
                .orElse(Collections.emptyMap());

        // 校验&移除无效数据
        accountingUnitLis.removeIf(v -> {
            String categoryCode = v.getCategoryCode();
            if (StringUtils.isBlank(categoryCode)){
                throw new CorpBusinessException(HotelResponseCodeEnum.ACCOUNTING_UNIT_CATEGORY_CODE_NULL, passenger.getName());
            }
            if (StringUtils.isBlank(v.getCategoryName())){
                throw new CorpBusinessException(HotelResponseCodeEnum.ACCOUNTING_UNIT_CATEGORY_NAME_NULL, passenger.getName());
            }

            OrgAccountingUnitCategoryConfig config = categoryConfigMap.get(categoryCode);
            if (config == null){
                log.error("OrgAccountingUnitCategoryConfigNull:{}, passenger:{}", categoryCode, passenger.getName());
            }

            Boolean isRequired = isRequiredAccountingUnit(config, accountingUnitReqMap);
            if (BooleanUtils.isNotTrue(isRequired)
                    && StringUtils.isBlank(v.getCode())
                    && StringUtils.isBlank(v.getName())) {
                log.info("非必填核算单元，不校验核算单元名称和核算单元编码,直接删除，{}", JsonUtils.toJsonString(v));
                return true;
            }
            if (StringUtils.isBlank(v.getName())){
                throw new CorpBusinessException(HotelResponseCodeEnum.ACCOUNTING_UNIT_NAME_NULL, passenger.getName());
            }
            if (StringUtils.isBlank(v.getCode())){
                throw new CorpBusinessException(HotelResponseCodeEnum.ACCOUNTING_UNIT_CODE_NULL, passenger.getName());
            }
            return false;
        });
    }

    private void fillBusinessUnitInfo(OrderInfoModel orderInfo) {
        List<OrderInfoModel.PassengerInfo> passengerList = orderInfo.getPassengerList();
        if (CollectionUtils.isEmpty(passengerList)) {
            return;
        }

        Map<BusinessUnitReferenceReq.Type, Map<String, BusinessUnitSimpleDto>> referencedBusinessUnitMap = initBusinessUnitData(passengerList);

        for (OrderInfoModel.PassengerInfo passenger : passengerList) {
            fillBusinessUnitForPassenger(passenger, referencedBusinessUnitMap);
        }
    }

    private Map<BusinessUnitReferenceReq.Type, Map<String, BusinessUnitSimpleDto>> initBusinessUnitData(List<OrderInfoModel.PassengerInfo> passengerList) {
        // 查询法人数据
        List<BusinessUnitReferenceReq.Reference> referenceList = Lists.newArrayList();
        for (OrderInfoModel.PassengerInfo passenger : passengerList) {
            if (StringUtils.isNotBlank(passenger.getCostCenterId())) {
                BusinessUnitReferenceReq.Reference reference = new BusinessUnitReferenceReq.Reference();
                reference.setType(BusinessUnitReferenceReq.Type.COST_INFO);
                reference.setValue(passenger.getCostCenterId());
                referenceList.add(reference);
            } else if (StringUtils.isNotBlank(passenger.getCostCenterCode())) {
                log.error("成本中心ID为空，成本中心：{}", passenger.getCostCenterId());
            }

            if (StringUtils.isNotBlank(passenger.getProjectId())) {
                BusinessUnitReferenceReq.Reference reference = new BusinessUnitReferenceReq.Reference();
                reference.setType(BusinessUnitReferenceReq.Type.PROJECT);
                reference.setValue(passenger.getProjectId());
                referenceList.add(reference);
            } else if (StringUtils.isNotBlank(passenger.getProjectCode())) {
                log.error("项目ID为空，项目：{}", passenger.getProjectId());
            }

            if (CollectionUtils.isNotEmpty(passenger.getAccountingUnitCategoryConfigList())) {
                for (OrderInfoModel.AccountingUnit accountingUnitVo : passenger.getAccountingUnitCategoryConfigList()) {
                    BusinessUnitReferenceReq.Reference reference = new BusinessUnitReferenceReq.Reference();
                    reference.setType(BusinessUnitReferenceReq.Type.ACCOUNTING_UNIT);
                    reference.setValue(accountingUnitVo.getCode());
                    referenceList.add(reference);
                }
            }
        }

        if (CollectionUtils.isEmpty(referenceList)){
            return Collections.emptyMap();
        }
        referenceList = referenceList.stream().distinct().collect(Collectors.toList());
        return businessUnitClientLoader.getReferencedBusinessUnit(referenceList);
    }

    private void fillBusinessUnitForPassenger(OrderInfoModel.PassengerInfo passenger,
                                              Map<BusinessUnitReferenceReq.Type, Map<String, BusinessUnitSimpleDto>> referencedBusinessUnitMap) {
        // 填充成本中心法人数据
        if (StringUtils.isNotBlank(passenger.getCostCenterId())) {
            Map<String, BusinessUnitSimpleDto> referenceMap = referencedBusinessUnitMap.getOrDefault(BusinessUnitReferenceReq.Type.COST_INFO, Collections.emptyMap());
            BusinessUnitSimpleDto businessUnitSimpleDto = referenceMap.get(passenger.getCostCenterId());
            if (businessUnitSimpleDto != null) {
                passenger.setCostCenterCorporationCode(businessUnitSimpleDto.getCode());
                passenger.setCostCenterCorporationName(businessUnitSimpleDto.getName());
                passenger.setSupplierAccountId(businessUnitSimpleDto.getSupplierAccountId());
            } else {
                log.error("成本中心法人数据不存在，成本中心：{}", passenger.getCostCenterId());
            }
        }

        // 填充项目法人数据
        if (StringUtils.isNotBlank(passenger.getProjectId())) {
            Map<String, BusinessUnitSimpleDto> referenceMap = referencedBusinessUnitMap.getOrDefault(BusinessUnitReferenceReq.Type.PROJECT, Collections.emptyMap());
            BusinessUnitSimpleDto businessUnitSimpleDto = referenceMap.get(passenger.getProjectId());
            if (businessUnitSimpleDto != null) {
                passenger.setProjectCorporationCode(businessUnitSimpleDto.getCode());
                passenger.setProjectCorporationName(businessUnitSimpleDto.getName());
            } else {
                log.error("项目法人数据不存在，项目：{}", passenger.getProjectId());
            }
        }

        // 填充核算的法人数据
        List<OrderInfoModel.AccountingUnit> accountingUnitLis = passenger.getAccountingUnitCategoryConfigList();
        if (CollectionUtils.isNotEmpty(accountingUnitLis)){
            Map<String, BusinessUnitSimpleDto> referenceMap = referencedBusinessUnitMap.getOrDefault(BusinessUnitReferenceReq.Type.ACCOUNTING_UNIT, Collections.emptyMap());
            for (OrderInfoModel.AccountingUnit accountingUnitVo : accountingUnitLis) {
                BusinessUnitSimpleDto businessUnitSimpleDto = referenceMap.get(accountingUnitVo.getCode());
                if (businessUnitSimpleDto!= null){
                    accountingUnitVo.setBusinessUnitCode(businessUnitSimpleDto.getCode());
                    accountingUnitVo.setBusinessUnitName(businessUnitSimpleDto.getName());
                } else {
                    log.error("核算单元法人数据不存在，核算单元：{}", JsonUtils.toJsonString(accountingUnitVo));
                }
            }
        }
    }


    /**
     * 用户自选审批人校验并赋值
     * @param orderInfoModel
     * @param request
     */
    private void checkAndSetManualApproveUser(OrderInfoModel orderInfoModel, CheckOrderRequestVo request){
        FlowDetail flowDetail = orderInfoModel.getFlowDetail();
        if(flowDetail==null){
            return;
        }
        Map<Integer, CheckOrderRequestVo.ManualApproveNode> flowNodeInfoMap = CollectionUtils.isEmpty(request.getManualApproveUserList())?new HashMap<>():request.getManualApproveUserList().stream().collect(Collectors.toMap(p->p.getNodeLevel(), p->p, (a,b)->a));
        // 用户自选审批人
        if(CollectionUtils.isNotEmpty(flowDetail.getFlowNodeInfos())){
            for(FlowNodeInfo nodeInfo:flowDetail.getFlowNodeInfos()){
                // 用户自选节点
                if(Objects.equals(nodeInfo.getApproveUserType(), ApproveUserTypeEnum.MANUAL.getCode())){
                    CheckOrderRequestVo.ManualApproveNode approveNode = flowNodeInfoMap.get(nodeInfo.getNodeLevel());
                    if(approveNode==null) {
                        throw new CorpBusinessException(HotelResponseCodeEnum.APPROVE_USER_NO_SELECTED, nodeInfo.getNodeLevel());
                    }
                    nodeInfo.setApproveUsers(HotelBookingConvertor.INSTANCE.toApproveUserList(approveNode.getUserList()));
                }
            }
        }
    }

    /**
     * 核算单元是否必填
     * @param config
     * @param accountingUnitReqMap
     * @return
     */
    private boolean isRequiredAccountingUnit(OrgAccountingUnitCategoryConfig config, Map<String, CheckOrderRequestVo.AccountingUnitVo> accountingUnitReqMap){
        if(config==null){
            return false;
        }
        if(StringUtils.isNotBlank(config.getRequiredType())){
            // 上级有值时必填
            if(OrgAccountingUnitCategoryConfigEnum.RequiredTypeEnum.PARENT_REQUEST.getCode().equals(config.getRequiredType())){
                // 如果上级有值就返回true找出上级是否有值
                CheckOrderRequestVo.AccountingUnitVo unitVo = accountingUnitReqMap.get(config.getCategoryParentCode());
                return unitVo!=null && StringUtils.isNotBlank(unitVo.getCode());
            } else {
                return OrgAccountingUnitCategoryConfigEnum.RequiredTypeEnum.YES.getCode().equals(config.getRequiredType());
            }
        } else {
            return Boolean.TRUE.equals(config.getRequired());
        }
    }
}
