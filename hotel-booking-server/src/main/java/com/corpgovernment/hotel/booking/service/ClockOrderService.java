package com.corpgovernment.hotel.booking.service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.corpgovernment.api.organization.enums.SwitchEnum;
import com.corpgovernment.api.organization.model.switchinfo.GetSwitchListRequest;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.hotel.product.dataloader.soa.SwitchClientLoader;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.redisson.api.GeoUnit;
import org.redisson.api.RGeo;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.hotel.booking.enums.HotelClockTypeEnum;
import com.corpgovernment.api.hotel.booking.orderdetail.request.ClockOrderRequestVo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.hotel.product.dataloader.db.HoHotelLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderClockRecordLoader;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.entity.db.HoHotel;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.entity.db.HoOrderClockRecord;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.utils.DateUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;

@Service
@Slf4j
public class ClockOrderService extends BaseService {

    private static final String CLOCK_ORDER_LOCK_KEY = "lock:hotel-booking:server:clock:%s:%s";
    private static final String ORDER_HOTEL_GEO_KEY = "cache:hotel-booking:server:order:hotel:%s";
    private static final String REFERENCE_POINT_ORDER_HOTEL = "OrderHotel";
    private static final String TARGET_POINT_ORDER_CLOCK_USER = "OrderClockUser";
    private static final String HOTEL_ENABLE_VISIT_CONTROL_ENABLE = "1";
    //订前说不能枚举 故用常量
    private static final String HOTEL_VISIT_RANGE_CONTROL = "hotel_visit_range_control";
    private static final String HOTEL_VISIT_RANGE_CONTROL_LIMIT = "1";
    private static final String HOTEL_VISIT_RANGE_KILOMETERS = "kvMap";
    private static final String HOTEL_VISIT_RANGE_KILOMETERS_KEY = "range";


    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private HoOrderClockRecordLoader hoOrderClockRecordLoader;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private HoHotelLoader hoHotelLoader;
    @Autowired
    private SwitchClientLoader switchClientLoader;

    public void clock(ClockOrderRequestVo request) {
        try {
            initElkLog();
            addElkInfoLog("酒店入离打卡参数:", JsonUtils.toJsonString(request));
            tryLockAndProcess(request, this::process);
        } finally {
            log.info(getElkInfoLog());
            clearElkLog();
        }
    }


    /**
     * 尝试锁定并处理
     *
     * @param request 请求参数
     * @param consumer  消费者
     */
    private void tryLockAndProcess(ClockOrderRequestVo request, Consumer<ClockOrderRequestVo> consumer) {
        RLock lock = null;
        boolean locked = false;
        try {
            String lockKey = String.format(CLOCK_ORDER_LOCK_KEY, request.getType(), request.getOrderId());
            lock = redissonClient.getLock(lockKey);
            locked = lock.tryLock(0, 10, TimeUnit.SECONDS);
            if (!locked) {
                addElkInfoLog("打卡存在并发请求");
                throw new CorpBusinessException(HotelResponseCodeEnum.CLOCK_CONCURRENT_ERROR);
            }
            consumer.accept(request);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new CorpBusinessException(HotelResponseCodeEnum.REDIS_LOCK_EXCEPTION);
        } finally {
            if (Objects.nonNull(lock) && locked) {
                lock.unlock();
            }
        }
    }

    /**
     * 处理
     *
     * @param request 要求
     */
    private void process(ClockOrderRequestVo request) {
        Long orderId = request.getOrderId();
        Integer clockType = request.getType();
        HoOrder hoOrder = hoOrderLoader.selectByOrderId(orderId);
        if (Objects.isNull(hoOrder)) {
            addElkInfoLog("该订单不存在");
            throw new CorpBusinessException(HotelResponseCodeEnum.ORDER_IS_NULL);
        }

        //获取配置
        Map<String, Object> clockSwitchMap = getClockSwitchMap();
        if (!getClockEnable(clockSwitchMap)) {
            addElkInfoLog("酒店打卡功能未启用");
            throw new CorpBusinessException(HotelResponseCodeEnum.CLOCK_NOT_ENABLE);
        }

        HotelClockTypeEnum requestHotelClockTypeEnum = HotelClockTypeEnum.getByCode(clockType);

        // 判断是否打过卡
        boolean isClocked = judgeIsClocked(orderId, requestHotelClockTypeEnum);
        if (isClocked) {
            addElkInfoLog("该订单打卡成功,打卡类型:", requestHotelClockTypeEnum.getDesc());
            throw new CorpBusinessException(HotelResponseCodeEnum.ALREADY_CLOCK_ERROR);
        }

        //未进行入住打卡不可进行离店打卡
        boolean clockOut = HotelClockTypeEnum.CLOCK_OUT.equals(requestHotelClockTypeEnum);
        if (clockOut) {
            HoOrderClockRecord hoOrderClockRecord = hoOrderClockRecordLoader.selectByOrderIdAndClockType(orderId, HotelClockTypeEnum.CLOCK_IN);
            if (Objects.isNull(hoOrderClockRecord)) {
                addElkInfoLog("未进行入住打卡，不可进行离店打卡");
                throw new CorpBusinessException(HotelResponseCodeEnum.CLOCK_OUT_NEED_CLOCK_IN);
            }
        }

        // 时间范围内
        boolean inClockTime = judgeInClockTime(hoOrder, requestHotelClockTypeEnum);
        if (!inClockTime) {
            addElkInfoLog("打卡时间不在时间范围内");
            throw new CorpBusinessException(HotelResponseCodeEnum.CLOCK_NOT_IN_TIME);
        }

        //地理位置范围内
        if (getClockRangeLimit(clockSwitchMap)) {
            Integer clockRangeKilometersLimit = getClockRangeKilometersLimit(clockSwitchMap);
            boolean inClockRange = judgeInClockRange(request, hoOrder.getActualCheckOutTime(), clockRangeKilometersLimit);
            if (!inClockRange) {
                addElkInfoLog("打卡位置不在规定位置范围内");
                throw new CorpBusinessException(HotelResponseCodeEnum.CLOCK_NOT_IN_RANGE, clockRangeKilometersLimit);
            }
        }
        //保存记录
        saveClockRecord(request);
    }

    /**
     * 判断是否已打卡
     *
     * @param orderId            订单号
     * @param hotelClockTypeEnum 酒店时钟类型枚举
     * @return boolean
     */
    private boolean judgeIsClocked(Long orderId, HotelClockTypeEnum hotelClockTypeEnum) {
        HoOrderClockRecord hoOrderClockRecord = hoOrderClockRecordLoader.selectByOrderIdAndClockType(orderId, hotelClockTypeEnum);
        return Objects.nonNull(hoOrderClockRecord);
    }

    /**
     * 判断是否在打卡时间内
     *
     * @param hoOrder 订单
     * @param clockTypeEnum 打卡类型枚举
     */
    private boolean judgeInClockTime(HoOrder hoOrder, HotelClockTypeEnum clockTypeEnum) {
        Date now = new Date();
        Date actualCheckInTime = hoOrder.getActualCheckInTime();
        Date actualCheckOutTime = hoOrder.getActualCheckOutTime();
        boolean clockIn = HotelClockTypeEnum.CLOCK_IN.equals(clockTypeEnum);
        if (clockIn) { // 入住打卡: 入住日0点 < 当前时间 < 实际离店日12点
            return now.after(actualCheckInTime) && now.before(getActualCheckOutTimeNoon(actualCheckOutTime));
        }
        // 离店打卡: 离店打卡 < 实际离店日24点
        Date actualCheckOutTimeMidNight = DateUtils.getDayEnd(actualCheckOutTime);
        return now.before(actualCheckOutTimeMidNight);
    }

    /**
     * 判断是否在打卡范围内
     * 酒店不存在经纬度则为true
     *
     * @param request                   订单号
     * @param clockRangeKilometersLimit
     * @return boolean 在范围内为true 否则为false
     */
    private boolean judgeInClockRange(ClockOrderRequestVo request, Date actualCheckOutTime, Integer clockRangeKilometersLimit) {
        Long orderId = request.getOrderId();
        String userLongitude = request.getLongitude();
        String userLatitude = request.getLatitude();

        HoHotel hoHotel = hoHotelLoader.selectByOrderId(orderId);
        String hotelLatitude = hoHotel.getLatitude();
        String hotelLongitude = hoHotel.getLongitude();

        boolean hotelHasNoGeoCoordinates = StringUtils.isBlank(hotelLatitude) || StringUtils.isBlank(hotelLongitude);
        if (hotelHasNoGeoCoordinates) { // 不存在酒店地理坐标直接允许打卡
            return true;
        }
        // 酒店存在地理坐标 需判定用户坐标是否在经纬度内
        String geoKey = String.format(ORDER_HOTEL_GEO_KEY, orderId);
        RGeo<String> geo = redissonClient.getGeo(geoKey);
        //实际离店时间24点为坐标key的过期时间
        geo.expireAt(DateUtils.getDayEnd(actualCheckOutTime));

        geo.add(Double.parseDouble(hotelLongitude), Double.parseDouble(hotelLatitude), REFERENCE_POINT_ORDER_HOTEL);
        geo.add(Double.parseDouble(userLongitude), Double.parseDouble(userLatitude), TARGET_POINT_ORDER_CLOCK_USER);

        // 用户是否在酒店的配置公里范围内
        Double distance = geo.dist(REFERENCE_POINT_ORDER_HOTEL, TARGET_POINT_ORDER_CLOCK_USER, GeoUnit.KILOMETERS);
        return Objects.nonNull(distance) && distance <= clockRangeKilometersLimit;
    }

    /**
     * 获取实际退房时间的中午时间
     *
     * @param actualCheckOutTime 实际退房时间
     * @return {@link Date }
     */
    private Date getActualCheckOutTimeNoon(Date actualCheckOutTime) {
        Instant instant = actualCheckOutTime.toInstant();
        LocalDateTime actualCheckOutLocalDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        LocalDateTime actualCheckOutLocalDateTimeNoon = actualCheckOutLocalDateTime.with(LocalTime.NOON);
        return Date.from(actualCheckOutLocalDateTimeNoon.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 保存打卡记录
     *
     * @param request 要求
     */
    private void saveClockRecord(ClockOrderRequestVo request) {
        HoOrderClockRecord record = new HoOrderClockRecord();
        record.setOrderId(request.getOrderId());
        record.setType(request.getType());
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        record.setClockUid(baseUserInfo.getUid());
        record.setClockTime(new Date());
        record.setLatitude(request.getLatitude());
        record.setLongitude(request.getLongitude());
        hoOrderClockRecordLoader.insertSelective(record);
    }

    /**
     * 启用酒店打卡功能
     *
     * @return boolean
     */
    @Nullable
    private Map<String, Object> getClockSwitchMap() {
        GetSwitchListRequest request = new GetSwitchListRequest();
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        request.setOrgId(baseUserInfo.getOrgId());
        request.setUId(baseUserInfo.getUid());
        request.setSwitchKey(SwitchEnum.HOTEL_ENABLE_VISIT_CONTROL.getKey());
        return switchClientLoader.getSwitchValueMap(request);
    }

    /**
     * 获取打卡是否启用
     *
     * @param switchValueMap 开关值图
     * @return boolean
     */
    private boolean getClockEnable(Map<String, Object> switchValueMap) {
        if (CollectionUtils.isEmpty(switchValueMap)) { //获取配置失败 按照配置默认值走 即启用
            return true;
        }
        Object switchValue = switchValueMap.get(SwitchEnum.HOTEL_ENABLE_VISIT_CONTROL.getKey());
        if (Objects.isNull(switchValue)) { //获取配置失败 按照配置默认值走 即启用
            return true;
        }
        List<String> switchValueList = JsonUtils.parseArray(switchValue.toString(), String.class);
        return switchValueList.contains(HOTEL_ENABLE_VISIT_CONTROL_ENABLE);
    }

    /**
     * 获取打卡范围是否限制
     *
     * @param switchValueMap 开关值图
     * @return boolean
     */
    private boolean getClockRangeLimit(Map<String, Object> switchValueMap) {
        if (CollectionUtils.isEmpty(switchValueMap)) { //获取配置失败 按照配置默认值走 即启用
            return true;
        }
        Object switchValue = switchValueMap.get(HOTEL_VISIT_RANGE_CONTROL);
        if (Objects.isNull(switchValue)) { //获取配置失败 按照配置默认值走 即启用
            return true;
        }
        List<String> switchValueList = JsonUtils.parseArray(switchValue.toString(), String.class);
        return switchValueList.contains(HOTEL_VISIT_RANGE_CONTROL_LIMIT);
    }

    /**
     * 获取打卡范围公里限制
     *
     * @param switchValueMap 开关值图
     * @return {@link Integer }
     */
    private Integer getClockRangeKilometersLimit(Map<String, Object> switchValueMap) {
        if (CollectionUtils.isEmpty(switchValueMap)) { // 获取配置失败 按照配置默认值走 即5km
            return 5;
        }
        Object visitRangeKilometersValue = switchValueMap.get(HOTEL_VISIT_RANGE_KILOMETERS);
        if (Objects.isNull(visitRangeKilometersValue)) {
            return 5;
        }
        JSONObject jsonObject = JSONUtil.parseObj(visitRangeKilometersValue);
        Object hotelVisitRangeKilometersVale = jsonObject.get(HOTEL_VISIT_RANGE_KILOMETERS_KEY);
        return Optional.ofNullable(hotelVisitRangeKilometersVale).map(String::valueOf).map(Integer::parseInt).orElse(5);
    }
}
