package com.corpgovernment.hotel.booking.vo.hotelmodify;

import java.util.List;

import lombok.Data;

/**
 * @author: lilayzzz
 * @since: 2023/12/14
 * @description:
 */
@Data
public class HotelModifyRequest {

    /**
     * 订单号
     */
    private Long  orderId;
    /**
     * reasonCode 修改原因编码
     */
    private String reasonCode;
    /**
     * reasonCode 修改原因内容
     */
    private String reasonContent;
    /**
     * 公司id
     */
    private ModifyInfo modifyInfo;
    /**
     * 提前离店修改类型
     * 1：申请提前离店
     * 2：提前离店
     */
    private Integer modifyType;

    /**
     * 强制修改
     */
    private Boolean forceModify = false;

    @Data
    public static class ModifyInfo{
        /**
         * 入住时间 format:yyyy-MM-dd HH:mm:ss
         */
        private String checkInTime;
        /**
         * 离店时间 format:yyyy-MM-dd HH:mm:ss
         */
        private String checkOutTime;
        /**
         * 保留的间夜信息列表
         */
        private List<RoomNightInfo> reservedRoomNightList;

        /**
         * 入离明细
         */
        private List<CheckInOutDetailInfo> checkInOutDetailList;

    }

    @Data
    public static class RoomNightInfo{
        /**
         * 日期 format：yyyy-MM-dd
         */
        private String date;
        /**
         * 房间数
         */
        private String quantity;

        /**
         * 入住人信息列表
         */
        private List<ClientInfo> clientInfoList;

    }
    @Data
    public static class ClientInfo{
        /**
         * 入住人ID
         */
        private Long clientInfoId;
        /**
         * 房间索引
         */
        private Integer roomIndex;
        /**
         * 入住人姓名
         */
        private Integer name;
    }
    
    @Data
    public static class CheckInOutDetailInfo {

        /**
         * 入住日期
         */
        private String checkInDate;

        /**
         * 退房日期
         */
        private String checkOutDate;
    }



}
