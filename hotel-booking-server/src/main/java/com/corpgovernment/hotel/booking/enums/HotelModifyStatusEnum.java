package com.corpgovernment.hotel.booking.enums;

/**
 * @author: lilayzzz
 * @since: 2023/12/11
 * @description:
 */
public enum HotelModifyStatusEnum {

    PLATFORM_SUBMITTED(0, "差旅平台已提交"),
    SUBMITTED(1, "已提交"),
    PENDING(2, "待处理"),
    COORDINATION(3, "与酒店协调中"),
    SUCCESS(4, "修改成功"),
    FAILED(5, "修改失败"),
    CANCELED(6, "修改取消"),
    CANCELING(7, "修改取消中"),
    ;

    private Integer code;
    private String desc;

    HotelModifyStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
