package com.corpgovernment.hotel.booking.bo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CheckDuplicateBookingBo {


    /**
     * 入住人信息
     * 内部员工（正式员工）： uid
     */
    List<String> uIds;

    /**
     * 外部员工： noEmployeeId
     */
    List<String> nonEmployeeIds;

    /**
     * 最早入住时间
     */
    private Date checkInDate;

    /**
     * 最晚离店时间
     */
    private Date checkOutDate;

}
