package com.corpgovernment.hotel.booking.service;

import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.ExceptionCodeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.hotel.booking.cache.model.HotelInfoModel;
import com.corpgovernment.hotel.booking.enums.LadderDeductionTypeEnum;
import com.corpgovernment.redis.cache.UserCacheManager;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class HotelManager {
    @Autowired
    private UserCacheManager userCacheManager;
    @Autowired
    private HotelApollo hotelApollo;

    /**
     * 停留时间key
     */
    private static final String STATETIME_KEY = "stateTime_key";

    /**
     * 停留时间校验
     *
     * @param isInit
     */
    public void checkStateTime(boolean isInit) {
        if (isInit) {
            userCacheManager.setUserCache(STATETIME_KEY, new Date(), hotelApollo.getBookTimeout());
        } else if (userCacheManager.getUserCache(STATETIME_KEY, Date.class) == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.STAY_TOO_LONG_PLEASE_CHECK_THE_PRICE_AGAIN);
        }
    }

    /**
     * 限时取消获取取消提示
     */
    public List<String> getCancelDetailList(List<HotelInfoModel.LadderDeduction> ladderDeductionList) {
        List<String> cancelDetailList = Lists.newArrayList();
        for (HotelInfoModel.LadderDeduction deduction : ladderDeductionList) {
            if (deduction == null) {
                continue;
            }
            LadderDeductionTypeEnum ladderDeductionTypeEnum = LadderDeductionTypeEnum.getByCode(deduction.getDeductionType());
            if (ladderDeductionTypeEnum == null) {
                continue;
            }
            switch (ladderDeductionTypeEnum) {
                case FREE:
                    cancelDetailList.add(getYMmDdHhMmString(deduction.getEndTime()) + "之前可以免费取消");
                    break;
                case LADDER:
                    cancelDetailList.add(getYMmDdHhMmString(deduction.getStartTime()) + "~" +
                            getYMmDdHhMmString(deduction.getEndTime()) + "收取" + deduction.getAmount() +"元取消费");
                    break;
                case CANNOT_CANCEL:
                    cancelDetailList.add(getYMmDdHhMmString(deduction.getStartTime()) + "之后不可取消");
                    break;
                default:
                    break;
            }
        }
        return cancelDetailList;
    }

    public String getYMmDdHhMmString(String dateString) {
        return DateUtil.dateToString(DateUtil.stringToDate(dateString, DateUtil.DF_YMD_HMS), DateUtil.DF_YMD_HM);
    }


}
