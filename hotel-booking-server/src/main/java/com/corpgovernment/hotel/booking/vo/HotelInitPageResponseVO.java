package com.corpgovernment.hotel.booking.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class HotelInitPageResponseVO {
    /**
     * 供应商信息
     */
    private SupplierInfoVO supplierInfo;
    /**
     * 是否是统一支付
     */
    private Boolean isAccnt;
    /**
     * 最早入住时间
     */
    private String checkInDate;
    /**
     * 最晚离店时间
     */
    private String checkOutDate;
    /**
     * 城市信息
     */
    private List<CityVO> cityInfoList;
    /**
     * 不可预定时间
     */
    private List<String> disabledDateList;
	/**
	 * 支持的支付方式
	 */
	private List<String> payCodes;
    /**
     * 是否能改变地址
     */
	private Boolean canChange;
    private ContactInfoVO contactInfo;
}
