package com.corpgovernment.hotel.booking.vo;

import java.util.List;
import java.util.Objects;

import com.corpgovernment.api.costcenter.model.TempCostCenterVo;
import com.ctrip.corp.obt.generic.security.annotation.SecurityField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("出行人")
public class PassengerInfoVO {
    /**
     * 名称
     */
    private String name;
    /**
     * 英文名称
     */
    private String passport;
    /**
     * 电话
     */
    @SecurityField(securityType = "des_mobile_phone")
    private String mobilePhone;
    /**
     * 国家码
     */
    private String countryCode;
    /**
     * 员工id
     */
    private String uid;
    private String orgId;
    /**
     * 非员工id
     */
    private String noEmployeeId;
    /**
     * 中英文
     */
    private String lang;
    /**
     * 是否为员工
     */
    private Boolean corp;

    private String gender;

    @ApiModelProperty("成本中心")
    private CostCenterVO costCenter;

    @ApiModelProperty("项目号")
    private ProjectInfoVO projectInfo;
    /**
     * 员工类型
     */
    private Integer employeeType;

    /**
     * 自定义模板多成本中心显示数据
     */
    private List<TempCostCenterVo> costCenterVoList;

    /**
     * 核算单元数据
     * @return
     */
    private List<AccountingUnit>  accountingUnitCategoryConfigList;

    public String getPassengerName() {
        return Objects.equals("en", lang) ? passport : name;
    }


    @Data
    public static class AccountingUnit {
        /**
         * 核算单元分类编码
         */
        private String categoryCode;
        /**
         * 核算单元分类名称
         */
        private String categoryName;
        /**
         * 必填
         */
        private Boolean required;
        /**
         * 备注
         */
        private String remark;
        /**
         * 核算单元编码
         */
        private String code;
        /**
         * 核算单元名称
         */
        private String name;
        /**
         * 法人机构编码（业务单元）
         */
        private String businessUnitCode;
        /**
         * 法人机构名称（业务单元）
         */
        private String businessUnitName;
    }
}
