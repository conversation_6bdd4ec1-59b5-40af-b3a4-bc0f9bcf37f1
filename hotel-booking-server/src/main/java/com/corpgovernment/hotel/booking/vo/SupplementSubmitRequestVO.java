package com.corpgovernment.hotel.booking.vo;

import com.corpgovernment.common.base.BaseRequestVO;
import com.ctrip.corp.obt.generic.security.annotation.SecurityEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SupplementSubmitRequestVO extends BaseRequestVO {

    private HotelInfoVO hotelInfo;
    @SecurityEntity
    private List<PassengerInfoVO> passengerList;
    private String payCode;
    /**
     * 发票id列表
     */
    private List<Long> invoiceIds;
    /**
     * 来源
     */
    private String source;
    /**
     * 供应商补录选择的账户
     */
    private String account;

    /**
     * 出差申请单号
     */
    private String tripApplyNo;


    /**
     * 配送费
     */
    private BigDecimal deliverFee;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 手续费
     */
    private BigDecimal commissionFee;

    /**
     * 赔付费
     */
    private BigDecimal compensationFee;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    private String bookingOrgId;

    /**
     * 后收服务费
     */
    private BigDecimal postServiceFee;
}
