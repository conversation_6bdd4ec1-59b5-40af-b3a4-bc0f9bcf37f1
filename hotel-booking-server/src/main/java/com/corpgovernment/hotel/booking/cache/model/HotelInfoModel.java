package com.corpgovernment.hotel.booking.cache.model;

import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO.DailyMealInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class HotelInfoModel {

    private Integer index;
    /**
     * 房型的结算类型(FG:现付, PP:预付到携程, PH:预付到酒店，UseFG:现转预房型-预付)
     */
    private String balanceType;
    /**
     * 	是否走浮动
     */
    private Integer floatControl;
    /**
     * 酒店类型（M：会员酒店，C：协议酒店）
     */
    private String hotelType;
    /**
     * 房型协议标签
     */
    private String protocolTag;
    /**
     * 房型名称
     */
    private String bedType;
    /**
     * 入住时间
	 */
	private String checkInDate;
	/**
	 * 离店时间
	 */
	private String checkOutDate;
	/**
	 * 房型ID
	 */
	private String roomId;
    /**
     * 物理房型ID
     */
    private String basicRoomId;
	/**
	 * 房型名称
	 */
	private String roomName;

	private String originRoomId;
	/**
	 * 预订房间数量
	 */
	private Integer quantity;
	/**
	 * 可住人数
	 */
	private Integer guestPerson;
    /**
     * 早餐数
     */
    private Integer breakfast;
	/**
	 * 早餐数
	 */
	private String breakfastDesc;
    /**
     * 入住酒店ID
     */
    private String hotelId;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 行政区id
     */
    private String locationId;
    /**
     * 酒店电话
     */
    private String telephone;
    /**
     * 入住城市ID
     */
    private String cityId;
    /**
     * 供应商城市id
     */
    private String supplierCityId;
    /**
     * 城市名
     */
    private String cityName;
    /**
     * 外滩地区
     */
    private String zoneId;
    /**
     * 外滩地区
     */
    private String zoneName;
    /**
     * 位置信息
     */
    private String locationName;
    /**
     * 最晚到店时间
     */
    private String lastArrivalTime;
    /**
     * 最早到店时间
     */
    private String earlyArrivalTime;
    /**
     * 最晚取消时间
     */
    private String lastCancelTime;
    /**
     * 是否Amadeus酒店
     */
    private Boolean amadeus;
    /**
     * 酒店地址
     */
    private String address;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 酒店图片
     */
    private String pic;
    /**
     * 无烟说明
     */
    private String smoke;
    /**
     * 其他说明
     */
    private String otherDescription;
    /**
     * 每日房价
     */
    private List<RoomDailyInfo> roomDailyInfoList;
    /**
     * 取消策略
     */
    private CancelInfo cancelInfo;
    /**
     * 展示价格
     */
    private BigDecimal averagePrice;
    /**
     * 供应商简称
     */
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商uid
     */
    private String supplierUid;
    /**
     * 供应商corpId
     */
    private String supplierCorpId;
    /**
     * 供应商电话
     */
    private String supplierPhone;
    /**
     * 供应商主账户ID
     */
    private String supplierAccountId;
    /**
     * 阶梯扣款策略
     */
    private List<LadderDeduction> ladderDeductionList;
    /**
     * 差旅标准
     */
    private String travelStandard;
    /**
     * 差标金额
     */
    private BigDecimal amountHigh;
    /**
     * 政策执行人id
	 */
	private String policyId;
	/**
	 * 政策执行人orgId
	 */
	private String policyOrgId;
	/**
	 * 酒店房间是否超标
	 */
	private boolean exceedStandard;
	/**
	 * 产品id
	 */
	private String productId;
	/**
	 * 酒店星级
	 */
	private Integer star;
	/**
	 * 是否挂牌星级
	 */
	private Boolean isStarLicence;
	/**
	 * 每日餐食
	 */
	private List<DailyMealInfo> dailyMealInfoList;
	/**
	 * 适用人群描述
	 */
	private String applicativeAreaDesc;
	/**
	 * 适用人群标题
	 */
	private String applicativeAreaTitle;

    /**
     * 品牌Id
     */
    private String brandId;
    /**
     * 集团Id
     */
    private String groupId;

    private String roomKey;

    /**
     * true则视为对应产品包含酒店套餐
     */
    private Boolean packageRoom;

    /**
     * 打包售卖房型打包Id
     */
    private Integer packageId;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 房间图片
     */
    private List<String> picUrls;
    /**
     * 房间基础信息
     */
    private List<String> basicInfo;
    /**
     * 酒店明细名称
     */
    private String name;

    /**
     * 紧急预定
     */
    private Boolean urgentApply;

    /**
     * 母房型信息
     */
    private ParentRoomInfo parentRoomInfo;

    /**
     * 母房型信息
     */
    @Data
    public static class ParentRoomInfo {
        /**
         * 所属母房型下最低价非协议房型的房费均价
         */
        private BigDecimal nonProtocolMinAvgPrice;

        /**
         * 所属母房型下最高价非协议房型的房费均价
         */
        private BigDecimal nonProtocolMaxAvgPrice;

        /**
         * 所属母房型下最低价协议房型的房费均价
         */
        private BigDecimal protocolMinAvgPrice;

        /**
         * 所属母房型下最低价协议房型的所属供应商
         */
        private String protocolMinAvgPriceSupplierCode;
    }

	@Data
	public static class LadderDeduction {
		private String startTime;
		private String endTime;
		private BigDecimal deductionRatio;
		private Integer advanceHour;
		private Boolean antiCut;
		private BigDecimal amount;
        /**
         * 扣款类型
         */
        private String deductionType;
    }

    @Data
    public static class RoomDailyInfo {

        private String hotelId;
        private String roomId;
        private String effectDate;
        private BigDecimal roomPrice;
    }

    @Data
    public static class CancelInfo {
        /**
         * * 0: None 无效值,未知
         * * 1: FreeCancelation 免费取消
         * * 2: LimitedCancelation 限时取消
         * * 4:OverTimeGuaranteeLimitedCancelation 超时担保,有限制取消
         * * 8: CanNotCancelation 不能取消
         * * 1和2表示免费取消
         */
        private Integer policyType;
    }

    public static boolean isEmpty(HotelInfoModel hotelInfoModel) {
        if (hotelInfoModel == null) {
            return true;
        }
		return hotelInfoModel.equals(new HotelInfoModel());
	}
}
