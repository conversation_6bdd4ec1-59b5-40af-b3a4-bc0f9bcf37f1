package com.corpgovernment.hotel.booking.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.hotel.product.model.request.LocalHotelDetailRequestBo;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO;
import com.corpgovernment.api.supplier.bo.suppliercompany.InitResponseBo;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.basic.bo.response.TravelStandardResponseBO;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.convert.TravelStandardConvert;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.constants.TravelStandardOwnerTypeEnum;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.CohabitRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.PriceRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.RuleChainVO;
import com.corpgovernment.hotel.booking.bo.CohabitStandardBo;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierCompanyClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.TravelStandardClientLoader;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/1/30
 */
@Service
@Slf4j
public class TravelStandardService {
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private SupplierCompanyClientLoader supplierCompanyClientLoader;
    @Autowired
    private TravelStandardClientLoader travelStandardClientLoader;

    /**
     * 根据同住token获取同住差标
     * @param travelStandardToken
     * @return
     */
    public HotelControlVo getHotelControlVoByToken(String travelStandardToken){
        GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
        getTravelStandardByTokenRequest.setTokenList(Arrays.asList(travelStandardToken));
        List<TravelStandardResponseBO> travelStandardResponseList = applyTripClientLoader.getTravelStandardByToken(getTravelStandardByTokenRequest);
        TravelStandardResponseBO travelStandardResponseBO =
                travelStandardResponseList.stream().filter(t->t.getTravelStandardToken().getOwnerType() == TravelStandardOwnerTypeEnum.REQUEST.getCode())
                        .findFirst().orElse(null);
        HotelControlVo hotelControlVo = null;
        log.info("根据同住token获取同住差标,travelStandardResponseBO:{}", JsonUtils.toJsonString(travelStandardResponseBO));
        // 处理同住模式
        if(ObjectUtil.isNotNull(travelStandardResponseBO) && ObjectUtil.isNotNull(travelStandardResponseBO.getRuleChainBO()) &&
            ObjectUtil.isNotNull(travelStandardResponseBO.getRuleChainBO().getCohabitRuleVO())){
            log.info("根据同住token获取同住差标,travelStandardResponseBO:{}", JsonUtils.toJsonString(travelStandardResponseBO));
            hotelControlVo =  TravelStandardConvert.travelStandardResponseShareWithBOConver(travelStandardResponseBO);
        } else{
            log.info("根据同住token获取非同住差标,travelStandardResponseBO:{}", JsonUtils.toJsonString(travelStandardResponseBO));
            // 如果为null,就是没有开启同住模式
            hotelControlVo = TravelStandardConvert.travelStandardResponseBOConver(travelStandardResponseBO);
        }
        log.info("根据同住token获取同住差标,hotelControlVo:{}", JsonUtils.toJsonString(hotelControlVo));
        return hotelControlVo;
    }

    /**
     * 根据LocalHotelDetailRequestBo和HotelDetailResponseVO生成包含是否超标的Map
     *
     * @param requestBo LocalHotelDetailRequestBo对象
     * @param responseVo HotelDetailResponseVO对象
     * @return 包含是否超标的Map，key为资源ID，value为是否超标（0：不超标，1：超标且不可预订，2：超标条件可预订）
     */
    public Map<String, String> getTravelStandardMap(LocalHotelDetailRequestBo requestBo, HotelDetailResponseVO responseVo) {
        Map<String, String> responsesMap = null;
        try {
            if(CollectionUtil.isEmpty(responseVo.getRooms())){
                return null;
            }
            // 获取服务费
            InitResponseBo.SupplierCompanyBo  serviceFee = new InitResponseBo.SupplierCompanyBo();
            // 判断是否需要服务费,如果房间包括meiya的房间才查询服务费
            boolean serviceFeeFlag = responseVo.getRooms().stream()
                    .flatMap(room -> Optional.ofNullable(room.getChildRooms()).orElse(Collections.emptyList()).stream())
                    .anyMatch(childRoom -> "meiya".equalsIgnoreCase(childRoom.getSupplierCode()));
            if (serviceFeeFlag) {
                InitResponseBo initResponseBo = supplierCompanyClientLoader.searchSupplierConfig(requestBo.getBaseUserInfo().getCorpId(), "meiya");
                if (initResponseBo == null || initResponseBo.getSupplierCompany() == null) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_SERVICE_CHARGE);
                }
                serviceFee = initResponseBo.getSupplierCompany();
            }
            VerifyTravelStandardRequest verifyRequest = createVerifyRequest(requestBo, responseVo, serviceFee, responseVo.getRooms());
            // verifyResult 0 通过（不超标），1 超标且不可预订，2 超标条件可预订
            List<ResourcesVerifyResponse> responses = applyTripClientLoader.verifyTravelStandard(verifyRequest);
            // 是否超标Map
            responsesMap = responses.stream()
                    .filter(r -> ObjectUtil.isNotEmpty(r.getExceed())) // 过滤掉exceed为空的对象
                    .collect(Collectors.toMap(
                            ResourcesVerifyResponse::getResourcesId,
                            r->String.valueOf(r.getExceed()),
                            (oldValue, newValue) -> oldValue
                    ));
        } catch (CorpBusinessException e) {
            log.error("根据LocalHotelDetailRequestBo和HotelDetailResponseVO生成包含是否超标的Map,e:",e);
        }
        return responsesMap;
    }

    private VerifyTravelStandardRequest createVerifyRequest(LocalHotelDetailRequestBo requestBo, HotelDetailResponseVO hotelDetailResponse,
                                                            InitResponseBo.SupplierCompanyBo serviceFee, List<HotelDetailResponseVO.Room> rooms) {
        VerifyTravelStandardRequest request = new VerifyTravelStandardRequest();

        request.setTravelStandardToken(requestBo.getTravelStandardToken());
        request.setBizType("3"); // 国内酒店

        List<HotelVerifyRequest> hotelList = new ArrayList<>();
        for (HotelDetailResponseVO.Room room : rooms) {
            for (HotelDetailResponseVO.ChildRoom childRoom : room.getChildRooms()) {
                hotelList.add(createHotelVerifyRequest(childRoom, serviceFee, hotelDetailResponse.getIntro()));
            }
        }
        request.setHotelList(hotelList);
        return request;
    }
    private HotelVerifyRequest createHotelVerifyRequest(HotelDetailResponseVO.ChildRoom childRoom, InitResponseBo.SupplierCompanyBo serviceFee, HotelDetailResponseVO.Intro intro) {
        HotelVerifyRequest request = new HotelVerifyRequest();
        request.setResourcesId(childRoom.getRoomKey()); // roomkey作为资源id
        request.setPrice(childRoom.getPrice());
        if(childRoom.getSupplierCode().equals("meiya")){
            if("C".equalsIgnoreCase(childRoom.getRoomType())){ // C 为协议酒店,取协议酒店服务费
                request.setServiceFee(serviceFee.getPersonalPubServiceFee());
            }else {
                request.setServiceFee(serviceFee.getAccountPubOtherServiceFee());
            }
        }
        if (intro != null) {
            request.setStarLicence(intro.getStarLicence());
            request.setStar(StringUtils.isNotBlank(intro.getLevel()) ? Integer.valueOf(intro.getLevel()) : null);
        }
        request.setOldProcess(true);
        return request;
    }



    public CohabitStandardBo cohabitStandardContentList(String travelStandardToken) {
        try {
            if (StringUtils.isBlank(travelStandardToken)) {
                return null;
            }
            GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
            getTravelStandardByTokenRequest.setTokenList(Lists.newArrayList(travelStandardToken));
            getTravelStandardByTokenRequest.setReturnSubToken(Boolean.TRUE);
            List<TravelStandardResponse> travelStandardResponseList =
                travelStandardClientLoader.getTravelStandardByToken(getTravelStandardByTokenRequest);
            if (CollectionUtils.isEmpty(travelStandardResponseList)) {
                return null;
            }

            TravelStandardResponse travelStandardResponse = travelStandardResponseList.stream()
                .filter(t -> t.getTravelStandardToken().getOwnerType() == TravelStandardOwnerTypeEnum.REQUEST.getCode())
                .findFirst().orElse(null);

            if (Objects.isNull(travelStandardResponse)) {
                return null;
            }

            // ControllerType = room 按房间管控
            Optional<TravelStandardRuleVO> anyCohabitRuleVO =
                travelStandardResponse.getRuleChain().getRuleList().stream().filter(CohabitRuleVO.class::isInstance)
                    .filter(e -> "room".equalsIgnoreCase(((CohabitRuleVO)e).getControllerType())).findAny();
            if (!anyCohabitRuleVO.isPresent()) {
                return null;
            }

            List<TravelStandardResponse> groupTravelStandardList = travelStandardResponseList.stream()
                .filter(
                    t -> t.getTravelStandardToken().getOwnerType() == TravelStandardOwnerTypeEnum.USER_GROUP.getCode())
                .collect(Collectors.toList());

            CohabitStandardBo cohabitStandardBo = new CohabitStandardBo();
            if (CollectionUtils.isNotEmpty(anyCohabitRuleVO.get().getRejectTypes())){
                cohabitStandardBo
                        .setRejectTypes(Arrays.stream(anyCohabitRuleVO.get().getRejectTypes()).collect(Collectors.toList()));
            }
            List<CohabitStandardBo.RoomInfo> roomInfoList = Lists.newArrayList();
            groupTravelStandardList.forEach(travel -> {
                RuleChainVO ruleChain = travel.getRuleChain();
                if (null == ruleChain) {
                    return;
                }
                List<TravelStandardRuleVO> ruleList = ruleChain.getRuleList();
                if (CollectionUtils.isEmpty(ruleList)) {
                    // 不限
                    if (BooleanUtils.isTrue(travel.getRuleStatus()) && BooleanUtils.isTrue(ruleChain.getBookable())) {
                        CohabitStandardBo.RoomInfo roomInfo = new CohabitStandardBo.RoomInfo();
                        roomInfo.setRoomIndex(travel.getTravelStandardToken().getOwnerId());
                        roomInfo.setAmount(BigDecimal.ZERO);
                        roomInfoList.add(roomInfo);
                    }
                    return;
                }
                Optional<TravelStandardRuleVO> firstCohabitRuleVO =
                    ruleList.stream().filter(CohabitRuleVO.class::isInstance).findFirst();
                if (firstCohabitRuleVO.isPresent()) {
                    TravelStandardRuleVO travelStandardRuleVO = firstCohabitRuleVO.get();
                    CohabitRuleVO cohabitRuleVO = (CohabitRuleVO)travelStandardRuleVO;
                    CohabitStandardBo.RoomInfo roomInfo = new CohabitStandardBo.RoomInfo();
                    roomInfo.setRoomIndex(travel.getTravelStandardToken().getOwnerId());
                    roomInfo.setAmount(cohabitRuleVO.getMaxPrice());
                    roomInfoList.add(roomInfo);
                } else {
                    Optional<TravelStandardRuleVO> firstPriceRuleVO =
                        ruleList.stream().filter(PriceRuleVO.class::isInstance).findFirst();
                    if (firstPriceRuleVO.isPresent()) {
                        PriceRuleVO priceRuleVO = (PriceRuleVO)firstPriceRuleVO.get();
                        CohabitStandardBo.RoomInfo roomInfo = new CohabitStandardBo.RoomInfo();
                        roomInfo.setRoomIndex(travel.getTravelStandardToken().getOwnerId());
                        roomInfo.setAmount(priceRuleVO.getMaxPrice());
                        roomInfoList.add(roomInfo);
                    }
                }
            });
            cohabitStandardBo.setRoomInfoList(roomInfoList);
            return cohabitStandardBo;
        } catch (Exception e) {
            log.error("同住差标组装异常", e);
        }
        return null;
    }

    /**
     * 获取差标集合
     *
     * @param travelStandardToken 旅行标准代币
     * @return {@link List }<{@link TravelStandardResponse }>
     */
    public List<TravelStandardResponse> getTravelStandardResponseList(String travelStandardToken) {
        GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
        getTravelStandardByTokenRequest.setTokenList(Lists.newArrayList(travelStandardToken));
        getTravelStandardByTokenRequest.setReturnSubToken(Boolean.TRUE);
        return travelStandardClientLoader.getTravelStandardByToken(getTravelStandardByTokenRequest);
    }
}
