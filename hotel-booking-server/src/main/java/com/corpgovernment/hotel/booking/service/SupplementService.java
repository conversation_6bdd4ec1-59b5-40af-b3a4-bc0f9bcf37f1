package com.corpgovernment.hotel.booking.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Nullable;

import com.corpgovernment.api.car.vo.response.AccountingUnitInfoVo;
import com.corpgovernment.api.hotel.booking.checkorder.request.CheckOrderRequestVo;
import com.corpgovernment.api.hotel.product.model.orderdetail.request.SupplierOrderDetailRequest;
import com.corpgovernment.api.order.common.supplement.CommonSupplementService;
import com.corpgovernment.api.ordercenter.dto.response.HotelOrderInfo;
import com.corpgovernment.api.ordercenter.dto.supplement.CommonOrderSupplement;
import com.corpgovernment.api.ordercenter.dto.supplement.CommonOrderSupplement.AccountingUnitInfo;
import com.corpgovernment.api.ordercenter.event.OrderSupplementEvent;
import com.corpgovernment.api.organization.dto.request.business.unit.BusinessUnitReferenceReq;
import com.corpgovernment.api.organization.dto.response.business.unit.BusinessUnitSimpleDto;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.consolidation.sdk.enums.ApplicationEnum;
import com.corpgovernment.consolidation.sdk.event.OrderCreateExternalEvent;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.booking.service.supplement.SupplementOrderService;
import com.corpgovernment.hotel.product.dataloader.soa.BusinessUnitClientLoader;
import com.corpgovernment.hotel.product.entity.db.HoOrder;
import com.corpgovernment.hotel.product.external.client.SupplierSoaClient;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailRequest;
import com.corpgovernment.hotel.product.external.dto.order.detail.StandardOrderDetailResponse;
import com.corpgovernment.hotel.product.model.ctrip.orderdetail.response.OrderDetailResponse;
import com.corpgovernment.hotel.product.service.HotelPushService;
import com.ctrip.corp.obt.api.eventcenter.enums.BusinessTypeEnum;
import com.ctrip.corp.obt.api.eventcenter.eventmodel.OrderEvent;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.event.EventCenter;
import com.ctrip.corp.obt.generic.utils.EnvironmentHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.applytrip.enums.ApplyTripEmployeeEnum;
import com.corpgovernment.api.basic.enums.SiteEnum;
import com.corpgovernment.api.basic.vo.OrderIdProducerRequestVo;
import com.corpgovernment.api.hotel.product.model.enums.OrderStatusEnum;
import com.corpgovernment.api.hotel.product.model.saveorder.request.SaveOrderRequestBo;
import com.corpgovernment.api.ordercenter.dto.request.AddOrderDetailLogRequest;
import com.corpgovernment.api.ordercenter.enums.OperationTitleEnums;
import com.corpgovernment.api.ordercenter.enums.OrderTypeEnums;
import com.corpgovernment.api.organization.model.enums.ExpressAddressProductTypeEnum;
import com.corpgovernment.api.organization.model.passenger.CheckPassengerRequest;
import com.corpgovernment.api.organization.model.passenger.MobilePhoneVo;
import com.corpgovernment.api.organization.model.passenger.PassengerVo;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoRequest;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoResponse;
import com.corpgovernment.api.organization.model.user.employee.OrgEmployeeVo;
import com.corpgovernment.api.organization.model.user.nonemployee.OrgNonEmployeeVo;
import com.corpgovernment.api.organization.soa.switchbo.GetPassengerNameRequest;
import com.corpgovernment.api.platform.bo.CreateDirectRefundOrderRequest;
import com.corpgovernment.api.platform.soa.paymentbill.CreatePaymentBillRequest;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierCompanyBo;
import com.corpgovernment.api.supplier.vo.MbSupplierInfoVo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseRequestVO.UserInfo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.DeliveryTypeEnum;
import com.corpgovernment.common.enums.ExpenseTypeEnum;
import com.corpgovernment.common.enums.OrderSourceEnum;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.hotel.booking.cache.OrderInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.model.SupplementOrderInfoModel;
import com.corpgovernment.hotel.booking.enums.BreakfastEnum;
import com.corpgovernment.hotel.booking.vo.ContactInfoVO;
import com.corpgovernment.hotel.booking.vo.CostCenterVO;
import com.corpgovernment.hotel.booking.vo.HotelInfoVO;
import com.corpgovernment.hotel.booking.vo.HotelInitPageResponseVO;
import com.corpgovernment.hotel.booking.vo.HotelSubmitResponseVO;
import com.corpgovernment.hotel.booking.vo.PassengerInfoVO;
import com.corpgovernment.hotel.booking.vo.ProjectInfoVO;
import com.corpgovernment.hotel.booking.vo.SupplementInitRequestVO;
import com.corpgovernment.hotel.booking.vo.SupplementSubmitRequestVO;
import com.corpgovernment.hotel.booking.vo.SupplierInfoVO;
import com.corpgovernment.hotel.product.dataloader.db.HoOrderLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BasicDataClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CommonOrderClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.OrganizationEmployeeClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.OrganizationNonEmployeeClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.OrganizationPassengerClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PassengerClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.PayClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierCompanyClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierDataClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SwitchClientLoader;
import com.corpgovernment.hotel.product.service.SaveOrderProductService;
import com.ctrip.corp.obt.api.eventcenter.enums.BusinessTypeEnum;
import com.ctrip.corp.obt.api.eventcenter.eventmodel.OrderEvent;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.event.EventCenter;
import com.ctrip.corp.obt.generic.security.utils.DesensitizationUtils;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.EnvironmentHolder;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SupplementService {

    private static final String HOTEL_DOMESTIC = "HotelDomestic";


    @Autowired
    private OrganizationEmployeeClientLoader organizationEmployeeClientLoader;
    @Autowired
    private OrderInfoCacheManager orderInfoCacheManager;
    @Autowired
    private BasicDataClientLoader basicDataClientLoader;
    @Autowired
    private SupplierCompanyClientLoader supplierCompanyClientLoader;
    @Autowired
    private SwitchClientLoader switchClientLoader;
    @Autowired
    private OrganizationNonEmployeeClientLoader organizationNonEmployeeClientLoader;
    @Autowired
    private PassengerClientLoader passengerClientLoader;
    @Autowired
    private PayClientLoader payClientLoader;
    @Autowired
    private HoOrderLoader hoOrderLoader;
    @Autowired
    private CommonOrderClientLoader commonOrderClientLoader;
    @Autowired
    private SaveOrderProductService saveOrderProductService;
    @Autowired
    private SupplierDataClientLoader supplierDataClientLoader;
    @Autowired
    @Qualifier(value = "basicThreadPoolExecutor")
    private ThreadPoolExecutor basicThreadPoolExecutor;
    @Autowired
    @Qualifier(value = "logThreadPoolExecutor")
    private ThreadPoolExecutor logThreadPoolExecutor;
    @Autowired
    private OrganizationPassengerClientLoader organizationPassengerClientLoader;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private EventCenter eventCenter;
    @Autowired
    private SupplementOrderService supplementOrderService;
    @Autowired
    private BusinessUnitClientLoader businessUnitClientLoader;

    @Autowired
    private SupplierSoaClient supplierSoaClient;
    @Autowired
    private HotelPushService hotelPushService;
    @Autowired
    private CommonSupplementService commonSupplementService;

    /**
     * 初始化补录
     */
    public HotelInitPageResponseVO init(SupplementInitRequestVO request) {
        // 补录预订人
        OrgEmployeeVo orgEmployee = this.getOrgEmployeeInfo(request.getBookUid(), request.getBookOrgId());
        // 供应商信息
        SupplierInfoVO supplierInfo = this.getSupplierInfo(request, orgEmployee);
        // 支付信息
        List<String> payCodes = this.toPayInfo(request);
        // 初始化补录缓存并保存
        SupplementOrderInfoModel orderInfo = initOrderInfo(request, orgEmployee);
        HotelInitPageResponseVO responseVo = HotelInitPageResponseVO.builder()
            .supplierInfo(supplierInfo)
            .payCodes(payCodes).build();
        orderInfo.setCityInfoList(responseVo.getCityInfoList());
        orderInfoCacheManager.saveSupplementOrderInfo(orderInfo);
        ContactInfoVO contactInfo = new ContactInfoVO();
        contactInfo.setContactCountryCode(orgEmployee.getAreaCode());
        contactInfo.setContactPhone(orgEmployee.getMobilePhone());
        contactInfo.setContactName(orgEmployee.getName());
        // 获取是否是出差申请
        boolean canChange = Boolean.FALSE;
        responseVo.setCanChange(canChange);
        responseVo.setContactInfo(contactInfo);
        // 返回
        return responseVo;
    }

    /**
     * 根据入住时间和离店时间获取有效的入住日期
     */
    private static List<String> getEffectDateList(String checkInTime, String checkOutTime) {
        List<String> list = Lists.newArrayList();
        LocalDate checkInDate = LocalDate.parse(checkInTime);
        LocalDate checkOutDate = LocalDate.parse(checkOutTime);
        long distance = ChronoUnit.DAYS.between(checkInDate, checkOutDate);
        Stream.iterate(checkInDate, d -> d.plusDays(1)).limit(distance).forEach(f -> list.add(f.toString()));
        return list;
    }

    /**
     * 提交补录
     */
    public HotelSubmitResponseVO submit(SupplementSubmitRequestVO request) {
        // 获取补录缓存
        Long orderId = null;
        boolean failure = false;
        UserInfo userInfo = request.getUserInfo();
        try {
            // 恢复脱敏数据
            recoverDesensitizedData(request);

            SupplementOrderInfoModel orderInfo = orderInfoCacheManager.getSupplementOrderInfo();
            log.info("补录订单缓存：{}", orderInfo);
            if (orderInfo == null || orderInfo.isEmpty()) {
                throw new CorpBusinessException(HotelResponseCodeEnum.REFILL_ORDER_CACHE_EXPIRED);
            }
            if (orderInfo.getIsOrdered()) {
                throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_RECORD_REPEATED_RECORD);
            }
            HotelInfoVO hotelInfo = request.getHotelInfo();
            boolean cancelFlag =
                hotelInfo.getCancelPrice() != null && hotelInfo.getCancelPrice().compareTo(BigDecimal.ZERO) > 0;
            if (cancelFlag) {
                BigDecimal amount = hotelInfo.getPrice().multiply(BigDecimal.valueOf(hotelInfo.getNum()))
                    .multiply(BigDecimal.valueOf(hotelInfo.getDays()));
                if (amount.compareTo(hotelInfo.getCancelPrice()) < 0) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.CANCELLATION_AMOUNT_LESS_THAN_TOTAL_AMOUNT);
                }
            }

            List<PassengerInfoVO> passengerList = request.getPassengerList();
            checkPassenger(orderInfo.getUid(), passengerList,request.getBookingOrgId());
            // 生成订单号
            orderId = getOrderId(orderInfo.getUid());
            orderInfo.setOrderId(orderId);
            // 补录落库
            boolean flag = saveOrder(orderInfo, request, orderId);
            if (!flag) {
                // 订单落库失败
                failure = true;
            }
            // 创建支付信息
            boolean createSuccess = createPayBill(request, orderInfo);
            // 下单失败或者创建支付单失败都需要回滚
            failure = failure || !createSuccess;
            if (failure) {
                int count = hoOrderLoader.deleteByOrderId(orderId);
                if (count <= 0) {
                    log.error("删除订单失败，订单号：{}", orderId);
                }
                throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_RECORD_ORDER);
            }
            orderInfo.setIsOrdered(true);

            // 补录成功处理
            supplementOrderService.supplementSuccessHandler(orderId);

            return HotelSubmitResponseVO.create(orderId);
        } catch (CorpBusinessException e) {
            log.error("补录订单出现错误", e);
            throw e;
        } finally {
            String content = "";
            if (failure) {
                content = "补录失败，订单号：" + orderId;
            } else {
                content = "补录成功，订单号：" + orderId;
            }
            this.addOperationLog(String.valueOf(orderId), userInfo.getUid(), userInfo.getUsername(),
                OperationTitleEnums.SUPPLEMENT_ORDER.getDesc(), content);
        }
        // 返回

    }




    /**
     * 恢复脱敏后数据
     *
     * @param request
     */
    private void recoverDesensitizedData(SupplementSubmitRequestVO request) {
        // feign获取passenger数据并恢复
        List<PassengerInfoVO> passengerInfoList = request.getPassengerList();
        if (CollectionUtils.isNotEmpty(passengerInfoList)) {
            recoverPassengerList(passengerInfoList);
        }
        log.info("脱敏恢复后的数据：{}", JsonUtils.toJsonString(request));
    }

    private void recoverPassengerList(List<PassengerInfoVO> passengerInfoList) {
        List<PassengerInfoVO> desensitizedPassengerList =
            passengerInfoList.stream().filter(p -> hasDesensitizedData(p)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(desensitizedPassengerList)) {
            return;
        }
        List<PassengerVo> passengerListRequest =
            desensitizedPassengerList.stream().map(p -> toListPassengerInfoRequest(p)).collect(Collectors.toList());
        List<PassengerVo> originalPassengerVos = passengerClientLoader.listPassengerInfo(passengerListRequest);
        Map<String, com.corpgovernment.api.organization.model.passenger.PassengerVo> originalPassengerMap =
            Optional.ofNullable(originalPassengerVos).map(o -> o.stream()).orElse(Stream.empty())
                .collect(Collectors.toMap(com.corpgovernment.api.organization.model.passenger.PassengerVo::getUid,
                    Function.identity(), (a, b) -> a));
        Map<String, com.corpgovernment.api.organization.model.passenger.PassengerVo> originalNoEmployeePassengerMap =
            Optional.ofNullable(originalPassengerVos).map(o -> o.stream()).orElse(Stream.empty())
                .collect(
                    Collectors.toMap(com.corpgovernment.api.organization.model.passenger.PassengerVo::getNoEmployeeId,
                        Function.identity(), (a, b) -> a));
        // feign data
        Map<String, PassengerInfoVO> employeePassengerMap = desensitizedPassengerList.stream()
            .collect(Collectors.toMap(PassengerInfoVO::getUid, Function.identity(), (a, b) -> a));
        Map<String, PassengerInfoVO> nonEmployeePassengerMap = desensitizedPassengerList.stream()
            .collect(Collectors.toMap(PassengerInfoVO::getNoEmployeeId, Function.identity(), (a, b) -> a));

        // employee
        recoverPassengerFromFeignGeneral(employeePassengerMap, originalPassengerMap);
        // nonEmployee
        recoverPassengerFromFeignGeneral(nonEmployeePassengerMap, originalNoEmployeePassengerMap);
    }

    private void recoverPassengerFromFeignGeneral(Map<String, PassengerInfoVO> employeePassengerMap,
        Map<String, PassengerVo> originalPassengerMap) {
        employeePassengerMap.forEach((id, d) -> {
            if (StringUtils.isBlank(id)) {
                return;
            }
            PassengerVo originalPassengerVo =
                originalPassengerMap.get(id);
            String mobilePhone = d.getMobilePhone();
            if (mobilePhone != null) {
                recoverGeneralDesensitizedData(d, originalPassengerVo, PassengerInfoVO::getMobilePhone,
                    PassengerInfoVO::setMobilePhone,
                    p -> Optional.ofNullable(p).map(PassengerVo::getTel).map(MobilePhoneVo::getValue).orElse(null));
            }
        });
    }

    /**
     * user数据若为脱敏后数据，则更新为原始的未脱敏数据
     *
     * @param originalData
     * @param userData
     * @param originalGetter
     * @param userGetter
     * @param userSetter
     * @param <T>
     * @param <U>
     */
    private <T, U> void recoverGeneralDesensitizedData(U userData, @Nullable T originalData,
        Function<U, String> userGetter,
        BiConsumer<U, String> userSetter, Function<T, String> originalGetter) {
        if (userData == null || originalGetter == null || userSetter == null || userSetter == null) {
            return;
        }
        String userFieldValue = userGetter.apply(userData);
        if (StringUtils.isNotBlank(userFieldValue) && DesensitizationUtils.isDesensitized(userFieldValue)) {
            userSetter.accept(userData,
                Optional.ofNullable(originalData).map(o -> originalGetter.apply(o)).orElse(null));
        }
    }

    private PassengerVo toListPassengerInfoRequest(PassengerInfoVO passengerInfo) {
        PassengerVo passengerVo = new PassengerVo();
        passengerVo.setUid(passengerInfo.getUid());
        passengerVo.setNoEmployeeId(passengerInfo.getNoEmployeeId());
        passengerVo.setOrgId(passengerInfo.getOrgId());
        return passengerVo;
    }

    private boolean hasDesensitizedData(PassengerInfoVO passengerInfoVO) {
        boolean hasId = StringUtils.isNotBlank(passengerInfoVO.getUid())
            || StringUtils.isNotBlank(passengerInfoVO.getNoEmployeeId());
        boolean hasDes = StringUtils.isNotBlank(passengerInfoVO.getMobilePhone())
            && DesensitizationUtils.isDesensitized(passengerInfoVO.getMobilePhone());
        if (!hasId && hasDes) {
            log.warn("存在脱敏数据未找到原数据, 脱敏数据: {}",
                com.ctrip.corp.obt.generic.utils.JsonUtils.toJsonString(passengerInfoVO));
        }
        return hasId && hasDes;
    }

    /**
     * 添加操作日志
     *
     * @param orderId
     * @param uid
     * @param desc
     */
    protected void addOperationLog(String orderId, String uid, String userName, String title, String desc) {
        if (StringUtils.isBlank(uid) || StringUtils.isBlank(orderId) || StringUtils.isBlank(desc)) {
            log.warn(String.format("记录日志参数不允许为空,uid:%s, orderId:%s, operationTitle:%s, operationDetails:%s", uid,
                orderId, desc, desc));
            return;
        }
        try {
            logThreadPoolExecutor.execute(() -> {
                try {
                    log.info("开始记录订单日志");
                    AddOrderDetailLogRequest logRequest = new AddOrderDetailLogRequest();
                    logRequest.setOrderType(OrderTypeEnums.HOTEL.getCode());
                    logRequest.setUserName(userName);
                    logRequest.setUid(uid);
                    logRequest.setOrderId(orderId);
                    logRequest.setOperationTitle(title);
                    logRequest.setOperationDetails(desc);
                    log.info("对账单日志入参" + JsonUtils.toJsonString(logRequest));
                    commonOrderClientLoader.addLog(logRequest);
                } catch (Exception e) {
                    log.warn("异步记录日志出错", e);
                }
            });
        } catch (Exception e) {
            log.warn("异步记录日志出错", e);
        }
    }

    /**
     * 补录酒店支付单
     *
     * @param requestVo
     * @param orderInfo
     * @return
     */
    private boolean createPayBill(SupplementSubmitRequestVO requestVo, SupplementOrderInfoModel orderInfo) {
        CreatePaymentBillRequest createPaymentBillRequest = new CreatePaymentBillRequest();
        HotelInfoVO hotelInfo = requestVo.getHotelInfo();
        BigDecimal amount = hotelInfo.getPrice().multiply(BigDecimal.valueOf(hotelInfo.getNum()))
            .multiply(BigDecimal.valueOf(hotelInfo.getDays()));

        BigDecimal serviceFee = Optional.ofNullable(requestVo.getServiceFee()).orElse(BigDecimal.ZERO);
        amount = amount.add(serviceFee);

        createPaymentBillRequest.setAmount(amount);
        createPaymentBillRequest.setOrderId(orderInfo.getOrderId());
        createPaymentBillRequest.setRefundFlag(false);
        createPaymentBillRequest.setRemark("");
        createPaymentBillRequest.setUid(orderInfo.getUid());
        createPaymentBillRequest.setUserName(orderInfo.getUname());
        createPaymentBillRequest.setOrderType(OrderTypeEnum.HN.getType());
        createPaymentBillRequest.setCorpPayType(ExpenseTypeEnum.PUB.getCode());
        createPaymentBillRequest.setPayType(requestVo.getPayCode());
        if (PayTypeEnum.PPAY.getType().equals(requestVo.getPayCode())) {
            createPaymentBillRequest.setPayChannel(PayTypeEnum.PPAY.getType());
        }
        Long billId = payClientLoader.supplementPaymentBill(createPaymentBillRequest);
        if (billId == null) {
            log.info("创建支付单失败!");
            return false;
        }

        //补录订单创建退款单
        if (createRefundBill(requestVo)) {
            // todo 创建退款单记录
            CreateDirectRefundOrderRequest refundRequest = new CreateDirectRefundOrderRequest();
            BigDecimal refundAmount = amount.subtract(feeAmount(hotelInfo.getCancelPrice()));

            // 退款金额增加赔付费
            refundAmount.add(feeAmount(requestVo.getCommissionFee()));

            refundRequest.setAmount(refundAmount);
            refundRequest.setBillId(billId);
            refundRequest.setUid(orderInfo.getUid());
            refundRequest.setUserName(orderInfo.getUname());
            refundRequest.setRealRefundAmount(refundAmount);
            Long refundBillId = payClientLoader.createDirectRefundOrder(refundRequest);
            if (refundBillId == null) {
                log.info("创建支付单失败!");
            }
        }
        return true;
    }

    private static BigDecimal feeAmount (BigDecimal bigDecimal) {
        return Objects.nonNull(bigDecimal) ? bigDecimal : BigDecimal.ZERO;
    }

    /**
     * 是否需要创建退款单
     * 历史逻辑为 判断取消费为空且取消费大于0
     * 新增 如果包含赔付费则创建退款单
     * @param requestVo
     * @return
     */
    private static boolean createRefundBill(SupplementSubmitRequestVO requestVo) {
        if (requestVo.getHotelInfo().getCancelPrice() != null &&
                requestVo.getHotelInfo().getCancelPrice().compareTo(BigDecimal.ZERO) > 0) {
            return Boolean.TRUE;
        }

        if (requestVo.getCommissionFee() != null &&
                requestVo.getCommissionFee().compareTo(BigDecimal.ZERO) > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 获取公司支付设置
     */
    private List<String> toPayInfo(SupplementInitRequestVO request) {
        // 获取支付方式
        PayInfoRequest payInfoRequest = new PayInfoRequest();
        payInfoRequest.setName(ExpenseTypeEnum.PUB.getCode());
        payInfoRequest.setTransport(ExpressAddressProductTypeEnum.HOTEL.getProductType());
        payInfoRequest.setOrgId(request.getBookOrgId());
        List<PayInfoResponse> userPayInfoList = switchClientLoader.getUserPayInfo(payInfoRequest);
        if (CollectionUtils.isEmpty(userPayInfoList)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_PAY_INFORMATION);
        }
        return userPayInfoList.stream().map(PayInfoResponse::getCode).distinct().collect(Collectors.toList());
    }

    /**
     * 订单入库
     */
    private boolean saveOrder(SupplementOrderInfoModel orderInfo, SupplementSubmitRequestVO request, Long orderId) {
        try {

            OrderDetailResponse.HotelOrderInfo supplierOrderInfo = getSupplierOrderInfo(request);

            SaveOrderRequestBo saveOrderRequestBo = new SaveOrderRequestBo();
            saveOrderRequestBo.setOrderInfo(toOrderInfo(orderInfo, request, orderId, supplierOrderInfo));
            saveOrderRequestBo.setHotelInfo(toHotelInfo(orderId, request, orderInfo));
            saveOrderRequestBo.setRoomInfo(toRoomInfo(orderId, request));
            saveOrderRequestBo.setRoomDailyInfoList(toRoomDailyInfo(orderId, request));

            List<SaveOrderRequestBo.PassengerInfo> passengerInfo = toPassengerInfo(orderId, orderInfo, request);
            fillBusinessUnitInfo(passengerInfo);
            saveOrderRequestBo.setPassengerInfoList(passengerInfo);
            log.info("保存订单request:" + JsonUtils.toJsonString(saveOrderRequestBo));
            saveOrderProductService.saveOrder(saveOrderRequestBo);
            return true;
        } catch (Exception ex) {
            log.error("补录落库失败:" + ex.getMessage(), ex);
            return false;
        }
    }


    private void fillBusinessUnitInfo(List<SaveOrderRequestBo.PassengerInfo> passengerInfo) {
        if (CollectionUtils.isEmpty(passengerInfo)) {
            return;
        }

        Map<BusinessUnitReferenceReq.Type, Map<String, BusinessUnitSimpleDto>> referencedBusinessUnitMap =
            initBusinessUnitData(passengerInfo);

        for (SaveOrderRequestBo.PassengerInfo passenger : passengerInfo) {
            fillBusinessUnitForPassenger(passenger, referencedBusinessUnitMap);
        }
    }

    private Map<BusinessUnitReferenceReq.Type, Map<String, BusinessUnitSimpleDto>>
        initBusinessUnitData(List<SaveOrderRequestBo.PassengerInfo> passengerList) {
        // 查询法人数据
        List<BusinessUnitReferenceReq.Reference> referenceList = Lists.newArrayList();
        for (SaveOrderRequestBo.PassengerInfo passenger : passengerList) {
            if (StringUtils.isNotBlank(passenger.getCostCenterId())) {
                BusinessUnitReferenceReq.Reference reference = new BusinessUnitReferenceReq.Reference();
                reference.setType(BusinessUnitReferenceReq.Type.COST_INFO);
                reference.setValue(passenger.getCostCenterId());
                referenceList.add(reference);
            } else if (StringUtils.isNotBlank(passenger.getCostCenterCode())) {
                log.error("成本中心ID为空，成本中心：{}", passenger.getCostCenterId());
            }

            if (StringUtils.isNotBlank(passenger.getProjectId())) {
                BusinessUnitReferenceReq.Reference reference = new BusinessUnitReferenceReq.Reference();
                reference.setType(BusinessUnitReferenceReq.Type.PROJECT);
                reference.setValue(passenger.getProjectId());
                referenceList.add(reference);
            } else if (StringUtils.isNotBlank(passenger.getProjectCode())) {
                log.error("项目ID为空，项目：{}", passenger.getProjectId());
            }
        }

        if (CollectionUtils.isEmpty(referenceList)) {
            return Collections.emptyMap();
        }
        referenceList = referenceList.stream().distinct().collect(Collectors.toList());
        return businessUnitClientLoader.getReferencedBusinessUnit(referenceList);
    }

    private void fillBusinessUnitForPassenger(SaveOrderRequestBo.PassengerInfo passenger,
        Map<BusinessUnitReferenceReq.Type, Map<String, BusinessUnitSimpleDto>> referencedBusinessUnitMap) {
        // 填充成本中心法人数据
        if (StringUtils.isNotBlank(passenger.getCostCenterId())) {
            Map<String, BusinessUnitSimpleDto> referenceMap =
                referencedBusinessUnitMap.getOrDefault(BusinessUnitReferenceReq.Type.COST_INFO, Collections.emptyMap());
            BusinessUnitSimpleDto businessUnitSimpleDto = referenceMap.get(passenger.getCostCenterId());
            if (businessUnitSimpleDto != null) {
                passenger.setCostCenterLegalEntityCode(businessUnitSimpleDto.getCode());
                passenger.setCostCenterLegalEntityName(businessUnitSimpleDto.getName());
                passenger.setSupplierAccountId(businessUnitSimpleDto.getSupplierAccountId());
            } else {
                log.error("成本中心法人数据不存在，成本中心：{}", passenger.getCostCenterId());
            }
        }

        // 填充项目法人数据
        if (StringUtils.isNotBlank(passenger.getProjectId())) {
            Map<String, BusinessUnitSimpleDto> referenceMap =
                referencedBusinessUnitMap.getOrDefault(BusinessUnitReferenceReq.Type.PROJECT, Collections.emptyMap());
            BusinessUnitSimpleDto businessUnitSimpleDto = referenceMap.get(passenger.getProjectId());
            if (businessUnitSimpleDto != null) {
                passenger.setProjectLegalEntityCode(businessUnitSimpleDto.getCode());
                passenger.setProjectLegalEntityName(businessUnitSimpleDto.getName());
            } else {
                log.error("项目法人数据不存在，项目：{}", passenger.getProjectId());
            }
        }
    }

    private OrderDetailResponse.HotelOrderInfo getSupplierOrderInfo(SupplementSubmitRequestVO request) {
        SupplierOrderDetailRequest orderDetailRequest = new SupplierOrderDetailRequest();
        orderDetailRequest.setSupplierOrderId(request.getHotelInfo().getSupplierOrderId());
        orderDetailRequest.setSupplierCode(request.getHotelInfo().getSupplierCode());
        orderDetailRequest.setCompanyCode(request.getUserInfo().getCorpId());
        OrderDetailResponse.HotelOrderInfo hotelOrderInfo = null;
        try {
            hotelOrderInfo = hotelPushService.getHotelOrderInfo(orderDetailRequest);
        } catch (Exception e) {
            log.error("获取供应商订单详情失败:" + e.getMessage(), e);
        }
        return hotelOrderInfo;
    }

    /**
     * 出行人检测更新落地
     */

    private void checkPassenger(String uid, List<PassengerInfoVO> passengerList,String bookingOrgId) {
        passengerList.forEach(e -> {
            String noEmployeeId = e.getNoEmployeeId();
            if (StringUtils.isNotBlank(noEmployeeId) && noEmployeeId.startsWith("add_")) {
                OrgNonEmployeeVo vo = new OrgNonEmployeeVo();
                vo.setName(e.getName());
                if (StringUtils.isNotBlank(e.getMobilePhone())) {
                    vo.setMobilePhone(e.getCountryCode() + e.getMobilePhone());
                }
                vo.setEmployeeUid(uid);
                Long id = organizationNonEmployeeClientLoader.save(vo);
                log.info("新增非员工结果：" + id);
                if (id != null) {
                    e.setNoEmployeeId(String.valueOf(id));
                }
            }

            //校验核算单元
            if(CollectionUtils.isNotEmpty(e.getAccountingUnitCategoryConfigList())) {
                String orgId = e.getOrgId();
                if(StringUtils.isEmpty(orgId)){
                    orgId=bookingOrgId;
                }

                List<AccountingUnitInfo> accountingUnitInfos = e.getAccountingUnitCategoryConfigList().stream()
                        .map(accountingUnit -> {
                            AccountingUnitInfo accountingUnitInfo = new AccountingUnitInfo();
                            accountingUnitInfo.setAccountingUnitName(accountingUnit.getName());
                            accountingUnitInfo.setAccountingUnitCode(accountingUnit.getCode());
                            accountingUnitInfo.setAccountingUnitType(accountingUnit.getCategoryName());
                            accountingUnitInfo.setAccountingUnitTypeCode(accountingUnit.getCategoryCode());
                            return accountingUnitInfo;
                        }).collect(Collectors.toList());
                String error = commonSupplementService.checkAccountingUnit(orgId, accountingUnitInfos);
                if(StringUtils.isNotBlank(error)){
                    throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_RECORD_ORDER,error);
                }
            }
        });
        CompletableFuture.runAsync(() -> {
            try {
                List<PassengerVo> passengerVoList = new ArrayList<>();
                for (PassengerInfoVO passenger : passengerList) {
                    PassengerVo passengerVo = new PassengerVo();
                    passengerVo.setName(passenger.getName());
                    passengerVo.setPassport(passenger.getPassport());
                    passengerVo.setCorp(passenger.getCorp());
                    passengerVo.setNoEmployeeId(passenger.getNoEmployeeId());
                    passengerVo.setUid(passenger.getUid());
                    MobilePhoneVo mobilePhoneVo = new MobilePhoneVo();
                    mobilePhoneVo.setCountryCode(passenger.getCountryCode());
                    mobilePhoneVo.setValue(passenger.getMobilePhone());
                    passengerVo.setTel(mobilePhoneVo);
                    passengerVoList.add(passengerVo);
                }
                CheckPassengerRequest checkPassengerRequest = new CheckPassengerRequest();
                checkPassengerRequest.setPassengerVos(passengerVoList);
                checkPassengerRequest.setUid(uid);
                log.info("更新入住人信息：{}", checkPassengerRequest);
                passengerClientLoader.checkPassenger(checkPassengerRequest);
            } catch (Exception e) {
                log.error("更新出行人失败：" + e.getMessage());
            }
        }, basicThreadPoolExecutor);
    }

    /**
     * 生成每日房价信息
     */
    private List<SaveOrderRequestBo.RoomDailyInfo> toRoomDailyInfo(Long orderId, SupplementSubmitRequestVO requestVo) {
        HotelInfoVO hotelInfo = requestVo.getHotelInfo();
        String checkInTime =
            DateUtil.dateToString(DateUtil.stringToDate(hotelInfo.getCheckInDate(), DateUtil.DF_YMD), DateUtil.DF_YMD);
        String checkOutTime =
            DateUtil.dateToString(DateUtil.stringToDate(hotelInfo.getCheckOutDate(), DateUtil.DF_YMD), DateUtil.DF_YMD);
        List<String> effectDateList = getEffectDateList(checkInTime, checkOutTime);
        return effectDateList.stream().map(e -> {
            SaveOrderRequestBo.RoomDailyInfo roomDailyInfo = new SaveOrderRequestBo.RoomDailyInfo();
            roomDailyInfo.setEffectDate(DateUtil.stringToDate(e, DateUtil.DF_YMD));
            roomDailyInfo.setOrderId(orderId);
            roomDailyInfo.setBreakfast(hotelInfo.getBreakfast());
            roomDailyInfo.setRoomPrice(hotelInfo.getPrice());
            roomDailyInfo.setBreakfastName(BreakfastEnum.getDescByType(String.valueOf(hotelInfo.getBreakfast())));
            return roomDailyInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 生成房间信息
     */
    private SaveOrderRequestBo.RoomInfo toRoomInfo(Long orderId, SupplementSubmitRequestVO requestVo) {
        SaveOrderRequestBo.RoomInfo roomInfo = new SaveOrderRequestBo.RoomInfo();
        HotelInfoVO hotelInfo = requestVo.getHotelInfo();
        roomInfo.setOrderId(orderId);
        roomInfo.setRoomName(hotelInfo.getRoomName());
        roomInfo.setBreakfast(hotelInfo.getBreakfast());
        roomInfo.setBreakfastName(BreakfastEnum.getDescByType(String.valueOf(hotelInfo.getBreakfast())));
        roomInfo.setBedType(hotelInfo.getBedName());
        roomInfo.setCheckInDate(DateUtil.stringToDate(hotelInfo.getCheckInDate(), DateUtil.DF_YMD));
        roomInfo.setCheckOutDate(DateUtil.stringToDate(hotelInfo.getCheckOutDate(), DateUtil.DF_YMD));
        roomInfo.setNextDay(hotelInfo.getDays());
        roomInfo.setPersonCount(-1);
        roomInfo.setRoomQuantity(hotelInfo.getNum());
        return roomInfo;
    }

    /**
     * 生成酒店信息
     */
    private SaveOrderRequestBo.HotelInfo toHotelInfo(Long orderId, SupplementSubmitRequestVO request,
        SupplementOrderInfoModel orderInfo) {
        SaveOrderRequestBo.HotelInfo hotel = new SaveOrderRequestBo.HotelInfo();
        HotelInfoVO hotelInfo = request.getHotelInfo();
        hotel.setHotelName(hotelInfo.getHotelName());
        hotel.setHotelPhone(hotelInfo.getHotelPhone());
        hotel.setAddress(hotelInfo.getHotelAddress());
        hotel.setCityName(hotelInfo.getCityName());
        hotel.setCityId(String.valueOf(hotelInfo.getCityId()));
        hotel.setOrderId(orderId);
        hotel.setStar(3);
        hotel.setIsStarLicence(false);
        hotel.setLatitude("");
        hotel.setLongitude("");
        //补录增加酒店类型和vat类型
        hotel.setHotelType(hotelInfo.getHotelType());
        hotel.setVatFlag(hotelInfo.getVatFlag());
        return hotel;
    }

    /**
     * 生成入住人信息
     */
    private List<SaveOrderRequestBo.PassengerInfo> toPassengerInfo(Long orderId, SupplementOrderInfoModel orderInfo,
        SupplementSubmitRequestVO requestVo) {
        List<PassengerInfoVO> passengerList = requestVo.getPassengerList();

        Map<String, String> travelerNameMap = organizationPassengerClientLoader.getTravelerNameMap(data -> {
            List<PassengerInfoVO> passengerInfoList = (List<PassengerInfoVO>)data;
            return passengerInfoList.stream().map(passenger -> {
                GetPassengerNameRequest passengerNameRequest = new GetPassengerNameRequest();
                String uid =
                    StringUtils.isNotBlank(passenger.getUid()) ? passenger.getUid()
                        : Objects.nonNull(passenger.getNoEmployeeId()) ? passenger.getNoEmployeeId()
                            : StringUtils.EMPTY;
                passengerNameRequest.setId(uid);
                passengerNameRequest.setEmployeeType(StringUtils.isNotBlank(passenger.getUid())
                    ? ApplyTripEmployeeEnum.EMPLOYEE.getCode() : ApplyTripEmployeeEnum.EXTERNAL_EMPLOYEE.getCode());
                passengerNameRequest.setLanguage(passenger.getLang());
                return passengerNameRequest;
            }).collect(Collectors.toList());
        }, requestVo.getPassengerList());
        return passengerList.stream().map(e -> {
            SaveOrderRequestBo.PassengerInfo passengerInfo = new SaveOrderRequestBo.PassengerInfo();
            passengerInfo.setOrderId(orderId);
            passengerInfo.setPassengerName(e.getPassengerName());
            String uid =
                StringUtils.isNotBlank(e.getUid()) ? e.getUid()
                    : Objects.nonNull(e.getNoEmployeeId()) ? e.getNoEmployeeId() : StringUtils.EMPTY;
            passengerInfo.setTravelerName(travelerNameMap.get(uid));
            passengerInfo.setUid(e.getUid());
            passengerInfo.setGender(e.getGender());
            passengerInfo.setMobilePhone(e.getMobilePhone());
            passengerInfo.setIsSendSms(false);
            passengerInfo.setCountryCode(e.getCountryCode());
            CostCenterVO costCenter = e.getCostCenter();
            if (costCenter != null) {
                passengerInfo.setCostCenterId(costCenter.getCostCenterId());
                passengerInfo.setCostCenterCode(costCenter.getCostCenterCode());
                passengerInfo.setCostCenterName(costCenter.getCostCenterName());
                passengerInfo.setCostCenterRemark(costCenter.getCostCenterRemark());
            }
            ProjectInfoVO projectInfo = e.getProjectInfo();
            if (projectInfo != null) {
                passengerInfo.setProjectId(projectInfo.getProjectId());
                passengerInfo.setProjectCode(projectInfo.getProjectCode());
                passengerInfo.setProjectName(projectInfo.getProjectName());
                passengerInfo.setWbsRemark(projectInfo.getWbsRemark());
                passengerInfo.setNoSelectProjectDesc(projectInfo.getNoSelectProjectDesc());
            }

            passengerInfo.setOrgId(e.getOrgId());
            // todo 补录先写死1
            passengerInfo.setRoomNo(1);
            String noEmployeeId = e.getNoEmployeeId();
            Optional.ofNullable(noEmployeeId).ifPresent(id -> passengerInfo.setNoEmployeeId(Long.valueOf(id)));
            passengerInfo.setEmployeeType(e.getEmployeeType());

            if (CollectionUtils.isNotEmpty(e.getCostCenterVoList())) {
                passengerInfo.setMultiCostCenterDataJSON(JsonUtils.toJsonString(e.getCostCenterVoList()));
            }
            if(CollectionUtils.isNotEmpty(e.getAccountingUnitCategoryConfigList())){
                List<AccountingUnitInfoVo> accountingUnitInfoVos = e.getAccountingUnitCategoryConfigList().stream().map(this::convertAccountingUnit).collect(Collectors.toList());
                passengerInfo.setAccountingUnitJson(JsonUtils.toJsonString(accountingUnitInfoVos));
            }

            return passengerInfo;
        }).collect(Collectors.toList());
    }

    private AccountingUnitInfoVo convertAccountingUnit(PassengerInfoVO.AccountingUnit accountingUnit){
        AccountingUnitInfoVo accountingUnitInfoVo = new AccountingUnitInfoVo();
        accountingUnitInfoVo.setAccountingUnitCode(accountingUnit.getCode());
        accountingUnitInfoVo.setAccountingUnitName(accountingUnit.getName());
        accountingUnitInfoVo.setLegalEntityCode(accountingUnit.getBusinessUnitCode());
        accountingUnitInfoVo.setLegalEntityName(accountingUnit.getBusinessUnitName());
        accountingUnitInfoVo.setAccountingUnitType(accountingUnit.getCategoryName());
        accountingUnitInfoVo.setAccountingUnitTypeCode(accountingUnit.getCategoryCode());
        accountingUnitInfoVo.setAccountingUnitRemark(accountingUnit.getRemark());
        return accountingUnitInfoVo;
    }

    /**
     * 获取联系人信息失败
     */
    private OrgEmployeeVo getOrgEmployeeInfo(String uid, String orgId) {
        OrgEmployeeVo employeeInfo = organizationEmployeeClientLoader.findEmployeeInfoByUid(uid, orgId);
        if (employeeInfo == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUBSCRIBER_INFORMATION);
        }
        return employeeInfo;
    }

    /**
     * 初始化订单缓存
     */
    private SupplementOrderInfoModel initOrderInfo(SupplementInitRequestVO requestVo, OrgEmployeeVo orgEmployee) {
        SupplementOrderInfoModel orderInfo = new SupplementOrderInfoModel();
        orderInfo.setTripApplyNo(requestVo.getTripApplyNo());
        orderInfo.setTripTrafficId(requestVo.getTripTrafficId());
        orderInfo.setUid(orgEmployee.getUid());
        orderInfo.setCorpId(orgEmployee.getRecentCompanyId());
        String deptId =
            Objects.equals(orgEmployee.getRecentCompanyId(), orgEmployee.getOrgId()) ? null : orgEmployee.getOrgId();
        orderInfo.setDeptId(deptId);
        orderInfo.setUname(orgEmployee.getName());
        orderInfo.setContactEmail(orgEmployee.getEmail());
        String mobilePhone = orgEmployee.getMobilePhone();
        if (StringUtils.isNotBlank(mobilePhone)) {
            String[] arr = mobilePhone.split(" ");
            if (arr[0].contains("+")) {
                orderInfo.setContactCountryCode(arr[0]);
            }
            if (arr.length > 1) {
                orderInfo.setContactMobilePhone(arr[1]);
            }
        }
        orderInfo.setContactName(orgEmployee.getName());
        orderInfo.setCorpId(orgEmployee.getRecentCompanyId());
        orderInfo.setDeptId(orgEmployee.getOrgId());
        return orderInfo;
    }

    /**
     * 获取服务商信息
     */
    private SupplierInfoVO getSupplierInfo(SupplementInitRequestVO request, OrgEmployeeVo orgEmployee) {
        SupplierCompanyBo supplierCompany = supplierCompanyClientLoader
            .findSupplierCompany(orgEmployee.getRecentCompanyId(), request.getSupplierCode(), 3);
        if (supplierCompany == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUPPLIER_INFORMATION);
        }
        MbSupplierInfoVo supplierInfo = supplierDataClientLoader.findBySupplierCode(supplierCompany.getSupplierCode());
        if (supplierInfo == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUPPLIER_INFORMATION);
        }
        return SupplierInfoVO.create(supplierInfo.getSupplierName(), supplierCompany.getSupplierCode());
    }

    /**
     * 获取订单号
     */
    private Long getOrderId(String uid) {
        OrderIdProducerRequestVo orderIdProducerRequestVo = new OrderIdProducerRequestVo();
        orderIdProducerRequestVo.setUid(uid);
        orderIdProducerRequestVo.setSiteEnum(SiteEnum.Hotel);
        Long orderId = basicDataClientLoader.productionOrderId(uid);
        if (orderId == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_ORDER_NUMBER);
        }
        return orderId;
    }

    /**
     * 生成订单信息
     */
    private SaveOrderRequestBo.OrderInfo toOrderInfo(SupplementOrderInfoModel orderInfo,
        SupplementSubmitRequestVO requestVo, Long orderId, OrderDetailResponse.HotelOrderInfo supplierOrderInfo) {
        SaveOrderRequestBo.OrderInfo order = new SaveOrderRequestBo.OrderInfo();
        HotelInfoVO hotelInfo = requestVo.getHotelInfo();
        order.setOrderDate(new Date());
        order.setTripApplyNo(orderInfo.getTripApplyNo());
        order.setTripTrafficId(orderInfo.getTripTrafficId());
        order.setOrderId(orderId);
        order.setSupplierOrderId(hotelInfo.getSupplierOrderId());
        order.setSupplierCode(hotelInfo.getSupplierCode());
        MbSupplierInfoVo supplierInfoVo = supplierDataClientLoader.findBySupplierCode(hotelInfo.getSupplierCode());
        order.setSupplierName(Optional.ofNullable(supplierInfoVo).map(MbSupplierInfoVo::getSupplierName).orElse(StringUtils.EMPTY));
        BigDecimal amount = hotelInfo.getPrice().multiply(BigDecimal.valueOf(hotelInfo.getNum()))
            .multiply(BigDecimal.valueOf(hotelInfo.getDays()));
        BigDecimal serviceFee = Optional.ofNullable(requestVo.getServiceFee()).orElse(BigDecimal.ZERO);
        amount = amount.add(serviceFee);
        if (Objects.nonNull(requestVo.getDeliverFee())) {
            amount = amount.add(requestVo.getDeliverFee());
        }
        order.setAmount(amount);
        order.setPayType(requestVo.getPayCode());
        // 是为取消订单
        boolean cancelFlag =
                hotelInfo.getCancelPrice() != null && hotelInfo.getCancelPrice().compareTo(BigDecimal.ZERO) > 0;
        order.setOrderStatus(cancelFlag ? OrderStatusEnum.CA.getCode() : OrderStatusEnum.ED.getCode());
        order.setDeliveryType(DeliveryTypeEnum.PJN.getCode());
        order.setCorpPayType(ExpenseTypeEnum.PUB.getCode());
        order.setCorpId(orderInfo.getCorpId());
        saveSupplierInfo(requestVo, orderInfo, order);
        if (StringUtils.isEmpty(hotelInfo.getContactName())) {
            order.setContactName(orderInfo.getContactName());
            order.setContactMobilePhone(orderInfo.getContactMobilePhone());
            order.setContactCountryCode(orderInfo.getContactCountryCode());
            order.setContactEmail(orderInfo.getContactEmail());
        } else {
            order.setContactName(hotelInfo.getContactName());
            order.setContactMobilePhone(hotelInfo.getContactPhone());
            order.setContactCountryCode(hotelInfo.getContactCountryCode());
        }
        order.setUid(orderInfo.getUid());
        order.setUname(orderInfo.getUname());
        order.setDeptId(orderInfo.getDeptId());
        if (Objects.equals(requestVo.getSource(), "SUPPLIER")) {
            order.setSource(OrderSourceEnum.Supplement.name());
        } else {
            order.setSource(OrderSourceEnum.Supplement.name());
        }
        if (PayTypeEnum.PPAY.getType().equals(order.getPayType())
            && (OrderSourceEnum.Supplement.name().equals(order.getSource())
                || OrderSourceEnum.Supplier.name().equals(order.getSource()))) {
            order.setPayChannel(PayTypeEnum.PPAY.getType());
        }
        //补录增加出差申请单号和各类费用
        order.setTripApplyNo(requestVo.getTripApplyNo());
        order.setServiceFee(requestVo.getServiceFee());
        order.setDeliveryPrice(requestVo.getDeliverFee());
        order.setPostServiceFee(requestVo.getPostServiceFee());
        order.setCancelFee(requestVo.getHotelInfo().getCancelPrice());
        order.setCommissionFee(requestVo.getCommissionFee());
        order.setCompensationFee(requestVo.getCompensationFee());
        order.setOtherFee(requestVo.getOtherFee());

        Optional<OrderDetailResponse.ClientInfoEntity> clientInfo = Optional.ofNullable(supplierOrderInfo)
                .map(OrderDetailResponse.HotelOrderInfo::getClientInfo)
                .orElse(Collections.emptyList()).stream().findFirst();

        order.setSettlementPersonAmt(Optional.ofNullable(supplierOrderInfo).map(OrderDetailResponse.HotelOrderInfo::getSettlementPersonAmt).orElse(null));
        order.setSettlementACCNTAmt(Optional.ofNullable(supplierOrderInfo).map(OrderDetailResponse.HotelOrderInfo::getSettlementACCNTAmt).orElse(null));
        order.setActualCheckInTime(clientInfo.map(OrderDetailResponse.ClientInfoEntity::getActualCheckInTime).orElse(null));
        order.setActualDepartureTime(clientInfo.map(OrderDetailResponse.ClientInfoEntity::getActualDepartureTime).orElse(null));
        order.setAPayAmount(Optional.ofNullable(supplierOrderInfo).map(OrderDetailResponse.HotelOrderInfo::getSettlementPersonAmt).orElse(null));
        order.setPPayAmount(Optional.ofNullable(supplierOrderInfo).map(OrderDetailResponse.HotelOrderInfo::getSettlementACCNTAmt).orElse(null));
        return order;
    }

    private void saveSupplierInfo(SupplementSubmitRequestVO req, SupplementOrderInfoModel orderInfo,
        SaveOrderRequestBo.OrderInfo order) {
        if (StringUtils.isNotBlank(req.getHotelInfo().getSupplierCode())) {
            SupplierCompanyBo response = supplierCompanyClientLoader.findSupplierCompany(orderInfo.getCorpId(),
                req.getHotelInfo().getSupplierCode(), 1);
            order.setSupplierCorpId(response.getSupplierCorpId());
            order.setSupplierUid(response.getSupplierUid());
        }
    }

}
