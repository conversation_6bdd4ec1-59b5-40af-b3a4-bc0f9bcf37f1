package com.corpgovernment.hotel.booking.service;

import static com.corpgovernment.hotel.booking.enums.BalanceTypeEnum.FG;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.applytrip.apply.sync.StandardAmountSyncRequest;
import com.corpgovernment.api.applytrip.soa.response.QueryApplyTripStandardResponse;
import com.corpgovernment.api.approvalsystem.bean.ApproveUser;
import com.corpgovernment.api.approvalsystem.bean.FlowDetail;
import com.corpgovernment.api.approvalsystem.bean.FlowNodeInfo;
import com.corpgovernment.api.approvalsystem.bean.TraverlerInfo;
import com.corpgovernment.api.approvalsystem.enums.ApprovalEnum;
import com.corpgovernment.api.approvalsystem.enums.ApprovalFlowFlagEnum;
import com.corpgovernment.api.approvalsystem.enums.ApprovalWayEnum;
import com.corpgovernment.api.approvalsystem.enums.UrgentEnum;
import com.corpgovernment.api.approvalsystem.service.ApprovalSystemClient;
import com.corpgovernment.api.approvalsystem.service.request.GetFlowTmplRequest;
import com.corpgovernment.api.approvalsystem.service.response.GetFlowTmplResponse;
import com.corpgovernment.api.costcenter.model.CostCenter;
import com.corpgovernment.api.hotel.booking.initpage.request.InitOrderRequestVo;
import com.corpgovernment.api.hotel.booking.initpage.response.DeliveryInfoVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo.HotelRoomInfo;
import com.corpgovernment.api.hotel.booking.initpage.response.InitOrderResponseVo.ServiceFeeInfo;
import com.corpgovernment.api.hotel.booking.initpage.response.InvoiceInfoVo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.LadderDeductionInfo;
import com.corpgovernment.api.organization.enums.SwitchFieldKeyEnum;
import com.corpgovernment.api.organization.model.commonInvoice.CommonInvoiceVo;
import com.corpgovernment.api.organization.model.expressaddress.ExpressAddressVo;
import com.corpgovernment.api.organization.model.switchinfo.GetAllSwitchResponse;
import com.corpgovernment.api.organization.model.switchinfo.GetSwitchListRequest;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoRequest;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoResponse;
import com.corpgovernment.api.organization.model.user.employee.OrgEmployeeVo;
import com.corpgovernment.api.supplier.bo.suppliercompany.InitResponseBo;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierCompanyBo;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.enums.HotelSwitchEnum;
import com.corpgovernment.api.travelstandard.vo.HotelControlVo;
import com.corpgovernment.api.travelstandard.vo.HotelPriceControl;
import com.corpgovernment.api.travelstandard.vo.HotelTravelStandardManageVo;
import com.corpgovernment.basic.bo.response.RuleChainBO;
import com.corpgovernment.basic.bo.response.TravelStandardResponseBO;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.BaseRequestVO.UserInfo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.CancelPolicyEnum;
import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.enums.DeliveryTypeEnum;
import com.corpgovernment.common.enums.ExpenseTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.BaseUtils;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.response.rule.CohabitRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.OffPeakSeasonRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.PriceRuleVO;
import com.corpgovernment.hotel.booking.bo.BookingCheckResultBo;
import com.corpgovernment.hotel.booking.bo.VerifyTravelStandardBo;
import com.corpgovernment.hotel.booking.cache.HotelInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.OrderInfoCacheManager;
import com.corpgovernment.hotel.booking.cache.model.HotelInfoModel;
import com.corpgovernment.hotel.booking.cache.model.OrderInfoModel;
import com.corpgovernment.hotel.booking.enums.ApprovalOperateTypeEnum;
import com.corpgovernment.hotel.booking.enums.CropPayTypeEnum;
import com.corpgovernment.hotel.booking.enums.HotelServiceFeeSwitchEnum;
import com.corpgovernment.hotel.booking.enums.HotelServiceFeeWayEnum;
import com.corpgovernment.hotel.booking.enums.RoomTypeEnum;
import com.corpgovernment.hotel.booking.enums.TravelUnLimitedTypeEnum;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.BasicDataClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CommonInvoiceClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.CostCenterClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.ExpressAddressClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.OrganizationEmployeeClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierCompanyClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SwitchClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.TravelStandardPostClientLoader;
import com.corpgovernment.hotel.product.service.CommonSupplierServiceNew;
import com.corpgovernment.hotel.product.supplier.enums.InvoiceEnum;
import com.ctrip.corp.obt.generic.constants.GenericConstants;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.Conditional;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import com.google.common.collect.Lists;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class InitOrderService {

    @Autowired
    private OrderInfoCacheManager orderInfoCacheManager;
    @Autowired
    private HotelInfoCacheManager hotelInfoCacheManager;
    @Autowired
    private HotelManager hotelManager;
    @Autowired
    private ExpressAddressClientLoader expressAddressClientLoader;
    @Autowired
    private CommonInvoiceClientLoader commonInvoiceClientLoader;
    @Autowired
    private SupplierCompanyClientLoader supplierCompanyClientLoader;
    @Autowired
    private OrganizationEmployeeClientLoader organizationEmployeeClientLoader;
    @Autowired
    private BasicDataClientLoader basicDataClientLoader;
    @Autowired
    private ApprovalSystemClient approvalSystemClient;
    @Autowired
    private CostCenterClientLoader costCenterClientLoader;
    @Autowired
    private CommonSupplierServiceNew commonSupplierServiceNew;
    @Autowired
    private SwitchClientLoader switchClientLoader;
    @Autowired
    private TravelStandardPostClientLoader travelStandardPostClientLoader;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    @Qualifier("queryThreadPoolExecutor")
    private ThreadPoolExecutor queryThreadPoolExecutor;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private ChangePriceService changePriceService;
    @Autowired
    private TravelStandardService travelStandardService;

    /**
     * 支付提示
     */
    private static final String PAY_INFO_TIPS = "请于{0}分钟内完成支付，以免耽误您的行程。 ";
    /**
     * 变价提示
     */
    private static final String CHANGE_PRICE_TIPS = "抱歉，酒店价格发生变动，单间总价由￥{0}变为￥{1}";
    /**
     * 禁止预定
     */
    private static final String CHANGE_PRICE_FORBIDDEN_TIPS = "抱歉，酒店价格发生变动，单间总价由￥{0}变为￥{1}，已违反公司差旅标准";

    Id fillPageId = Metrics.REGISTRY.createId("hotel.booking.fillPage.initOrder");

    /**
     * 填写页初始化接口
     *
     * @param requestVo
     * @return
     */
    public InitOrderResponseVo initOrder(InitOrderRequestVo requestVo) {
        log.info("进入订单填写页接口,requestVo:{}" , JsonUtils.toJsonString(requestVo));
        if(CorpPayTypeEnum.OWN.getType().equals(requestVo.getExpenseType())){
            requestVo.setTravelStandardToken(null);
        }
        log.info("进入订单填写页接口,requestVo:{}" , JsonUtils.toJsonString(requestVo));
        //校验停留时间
        hotelManager.checkStateTime(false);
        // 初始化订单缓存
        OrderInfoModel orderInfo = this.initOrderInfo(requestVo);


        UserInfo userInfo = requestVo.getUserInfo();
        // 获取酒店房间信息缓存
        HotelInfoModel hotelInfo = hotelInfoCacheManager.getHotelInfo(requestVo.getHotelId(), requestVo.getRoomKey(), userInfo.getToken());
        // 校验是否可以紧急预订
        if(requestVo.getUrgentApply()!=null){
            orderInfo.setUrgentApply(requestVo.getUrgentApply());
        }
        checkUrgentEnable(userInfo.getCorpId(), userInfo.getUid(),  orderInfo);

        log.info("酒店缓存json" + JsonUtils.toJsonString(hotelInfo));
        if (HotelInfoModel.isEmpty(hotelInfo)) {
            return InitOrderResponseVo.failure("1", "获取酒店缓存失败");
        }
        CompletableFuture<OrgEmployeeVo> employeeFuture = CompletableFuture.supplyAsync(() -> this.getEmployeeInfo(userInfo.getUid(), userInfo.getOrgId()), queryThreadPoolExecutor);
        // 可订查询
        LocalCheckAvailResponseBo checkAvailResponseBo = commonSupplierServiceNew.checkAvail(toCheckAvailRequest(hotelInfo, orderInfo));
        log.info("可订查询结果：" + JsonUtils.toJsonString(checkAvailResponseBo));

        if (Objects.isNull(checkAvailResponseBo) || StringUtils.isNotBlank(checkAvailResponseBo.getFailedReason()) ||
                Objects.isNull(checkAvailResponseBo.getRoomInfo()) || CollectionUtils.isEmpty(checkAvailResponseBo.getRoomDailyInfoList())){
            return InitOrderResponseVo.failure(Optional.ofNullable(checkAvailResponseBo).map(LocalCheckAvailResponseBo::getCheckCode).orElse("1"), Optional.ofNullable(checkAvailResponseBo).
                    map(LocalCheckAvailResponseBo::getFailedReason).orElse("该房型暂时不可预订，请稍后再试或预订其他房型"));
        }

        CompletableFuture<InitResponseBo> supplierConfigFuture = CompletableFuture.supplyAsync(() -> supplierCompanyClientLoader.searchSupplierConfig(userInfo.getCorpId(), hotelInfo.getSupplierCode()), queryThreadPoolExecutor);

        //1、填写页可订变价时，若变动前/后价格都未命中“RC原因/禁止预订” 均价差标（命中淡旺季时，读取淡旺季均价），则弹框提醒“抱歉，酒店价格发生变动，价格由￥xx变动￥xx”，点击“重新选择”，返回酒店详情页并重新获取该酒店价格，点击“ 继续预订” 更新价继续预订。
        //2、填写页可订变价时，若变动前价格已命中“RC原因” 均价差标（命中淡旺季时，读取淡旺季均价），变动后价格命中，则弹框提醒“抱歉，酒店价格发生变动，价格由￥xx变动￥xx”，点击“重新选择”，返回酒店详情页并重新获取该酒店价格，点击“ 继续预订” 更新价继续预订。若价格变低未命中时，废除已选RC，相关数据不落库。
        //3、填写页可订变价时，若变动前价格都未命中“RC原因/禁止预订” 均价差标（命中淡旺季时，读取淡旺季均价），变动后价格命中，则弹框提醒“抱歉，酒店价格发生变动，价格由￥xx变动￥xx，已违反公司差旅标准”，点击“重新选择”，返回酒店详情页并重新获取该酒店价格。
        //4、变价规则：酒店详情接口返回的每日房价的总价 不等于 酒店可订接口返回的每日房价的总价
        //原价总额
        BigDecimal originalTotalPrice = BigDecimal.ZERO;
        for (HotelInfoModel.RoomDailyInfo roomDailyInfo : hotelInfo.getRoomDailyInfoList()) {
            originalTotalPrice = originalTotalPrice.add(roomDailyInfo.getRoomPrice());
        }
        //现价
        BigDecimal presentTotalPrice = BigDecimal.ZERO;
        for (LocalCheckAvailResponseBo.RoomDailyInfo roomDailyInfo : checkAvailResponseBo.getRoomDailyInfoList()) {
            presentTotalPrice = presentTotalPrice.add(roomDailyInfo.getSellPrice());
        }

        // 获取酒店信息
        InitOrderResponseVo.HotelRoomInfo hotelRoomInfo = toHotelRoomInfo(hotelInfo, checkAvailResponseBo);
        log.info("酒店房间信息json" + JsonUtils.toJsonString(hotelRoomInfo));
        String lastArrivalTime = hotelRoomInfo.getLastArrivalTime();
        String earlyArrivalTime = hotelRoomInfo.getEarlyArrivalTime();
        if (StringUtils.isNotBlank(earlyArrivalTime) && StringUtils.isNotBlank(lastArrivalTime) && lastArrivalTime.compareTo(earlyArrivalTime) < 0) {
            return InitOrderResponseVo.failure("2", "最早到店时间晚于最晚到店时间,不可预订");
        }
        // 获取成本中心
        String policyOrgId = StringUtils.isNotBlank(hotelInfo.getPolicyOrgId()) ? hotelInfo.getPolicyOrgId() : userInfo.getOrgId();
        List<CostCenter> costCenters = costCenterClientLoader.getCostCenter(policyOrgId);
        CostCenter costCenter = null;
        if (CollectionUtils.isNotEmpty(costCenters)){
            costCenter = costCenters.get(0);
        }
        // 获取审批信息
        Map<Integer, FlowDetail> flowDetailMap = toApprovalFlow(requestVo, hotelInfo, orderInfo);
        Integer approvalWay = getApprovalWay(flowDetailMap);
        orderInfo.setApprovalWay(approvalWay.toString());
        // 获取服务费信息
        ServiceFeeInfo serviceFeeInfo = Optional.ofNullable(BaseUtils.getFuture("获取服务商配置", supplierConfigFuture))
            .map(InitResponseBo::getSupplierCompany)
            .map(supplierConfig -> buildServiceFee(supplierConfig, requestVo.getExpenseType(), hotelRoomInfo))
            .orElse(null);
        //调用差标接口校验是否超标
        VerifyTravelStandardBo verifyTravelStandardBo = new VerifyTravelStandardBo();
        if (StrUtil.isNotBlank(requestVo.getTravelStandardToken())) {
            verifyTravelStandardBo = changePriceService.verifyTravelStandard(serviceFeeInfo, requestVo, hotelInfo);
            // 写入差标Token
            orderInfo.setTravelStandardToken(requestVo.getTravelStandardToken());
            // 写入差标个付金额
            orderInfo.setExceedAmount(
                Objects.nonNull(verifyTravelStandardBo) ? verifyTravelStandardBo.getExceedAmount() : BigDecimal.ZERO);
        }
        log.info("调用差标接口校验是否超标,serviceFeeInfo:{},requestVo:{},verifyTravelStandard:{}",
                JSONUtil.toJsonStr(serviceFeeInfo),JSONUtil.toJsonStr(requestVo),JsonUtils.toJsonString(verifyTravelStandardBo));
        BookingCheckResultBo checkResultBo = changePriceService.handledChangePrice(verifyTravelStandardBo.isVerifyTravelStandard(),requestVo.getExpenseType(), orderInfo, userInfo, hotelInfo, checkAvailResponseBo, originalTotalPrice, presentTotalPrice);
        // 补完并保存订单大缓存
        orderInfo.checkAvail(checkAvailResponseBo, hotelInfo, hotelRoomInfo, flowDetailMap, costCenter,serviceFeeInfo);
        
        log.info("存入订单缓存的审批流：{}, approvalWay:{}", JsonUtils.toJsonString(orderInfo.getFlowDetailMap()), approvalWay);
        orderInfoCacheManager.saveOrderInfo(orderInfo, hotelInfo.getHotelId(), hotelInfo.getRoomId(), userInfo.getToken());

        InitOrderResponseVo result = toResponse(requestVo, hotelInfo, checkAvailResponseBo, hotelRoomInfo, flowDetailMap, serviceFeeInfo, orderInfo,checkResultBo);
        // 设置超标金额(个人支付金额)
        result.setExceedAmount(ObjectUtil.isNotNull(verifyTravelStandardBo.getExceedAmount()) ?
                verifyTravelStandardBo.getExceedAmount():null);
        log.info("initOrder response" + JsonUtils.toJsonString(result));
        OrgEmployeeVo employee = BaseUtils.getFuture("查询员工信息", employeeFuture);
        result.setContactInfo(toContactInfo(employee));
        setInitMetric(result);
        if(CorpPayTypeEnum.PUB.getType().equalsIgnoreCase(requestVo.getExpenseType()) && Boolean.TRUE.equals(requestVo.getUrgentApply()) && !flowDetailMap.containsKey(ApprovalFlowFlagEnum.NORMAL.getCode())){
            result.setNoConfigUrgentApply(Boolean.TRUE);
        }
        // 保存订单初始化基本信息，用于恢复脱敏数据
        orderInfoCacheManager.saveInitOrderBasicResponse(result);

        return result;
    }

    private Integer getApprovalWay(Map<Integer, FlowDetail> flowDetailMap) {

        if (MapUtil.isEmpty(flowDetailMap)) {
            return ApprovalWayEnum.NO_APPROVAL.getCode();
        }

        if (MapUtil.isNotEmpty(flowDetailMap)) {
            FlowDetail flowDetail = flowDetailMap.get(ApprovalFlowFlagEnum.EXCEED.getCode());
            if(flowDetail == null){
                flowDetail = flowDetailMap.get(ApprovalFlowFlagEnum.NORMAL.getCode());
            }
            if(flowDetail == null){
                return ApprovalWayEnum.NO_APPROVAL.getCode();
            }
            if (flowDetail.getApprovalWay()!=null) {
                return flowDetail.getApprovalWay();
            }
        }
        return ApprovalWayEnum.PRESET_APPROVAL.getCode();
    }

    /**
     * 构建服务费信息
     */
    private ServiceFeeInfo buildServiceFee(InitResponseBo.SupplierCompanyBo supplier, String expenseType, HotelRoomInfo hotelRoomInfo) {
        //1：从配置读取 2：从供应商读取
        Integer type = supplier.getServiceFeeOptionType();
        if (type == null) {
            return null;
        }
        ServiceFeeInfo serviceFeeInfo = new ServiceFeeInfo();
        serviceFeeInfo.setServiceFeeOptionType(type);
        if (type == 1) {
            if (RoomTypeEnum.C.getType().equals(hotelRoomInfo.getHotelType())){
                if (CropPayTypeEnum.PUB.getCode().equals(expenseType)){
                    this.getContractPubServiceFee(serviceFeeInfo, supplier);
                } else if (CropPayTypeEnum.OWN.getCode().equals(expenseType)){
                    this.getContractOwnServiceFee(serviceFeeInfo, supplier);
                }
            } else {
                if (CropPayTypeEnum.PUB.getCode().equals(expenseType)){
                    this.getMemberPubServiceFee(serviceFeeInfo, supplier);
                } else if (CropPayTypeEnum.OWN.getCode().equals(expenseType)){
                    this.getMemberOwnServiceFee(serviceFeeInfo, supplier);
                }
            }
        }
        if (type == 2) {
            serviceFeeInfo.setAccountPayServiceFee(hotelRoomInfo.getServiceFee());
            serviceFeeInfo.setPersonalPayServiceFee(hotelRoomInfo.getServiceFee());
            serviceFeeInfo.setPersonalPubPayServiceFee(hotelRoomInfo.getServiceFee());
            serviceFeeInfo.setAccountPayServiceFeeWay(HotelServiceFeeWayEnum.ORDER.getType());
            serviceFeeInfo.setPersonalPayServiceFeeWay(HotelServiceFeeWayEnum.ORDER.getType());
            serviceFeeInfo.setPersonalPubPayServiceFeeWay(HotelServiceFeeWayEnum.ORDER.getType());
        }
        return serviceFeeInfo;
    }


    /**
     * 会员房型
     * 因公服务费
     * @param serviceFeeInfo
     * @param supplier
     */
    private void getMemberPubServiceFee(ServiceFeeInfo serviceFeeInfo, InitResponseBo.SupplierCompanyBo supplier){
        //统一支付 服务费
        if (HotelServiceFeeSwitchEnum.before.getType().equals(supplier.getAccountPubOtherSwitch())){
            serviceFeeInfo.setAccountPayServiceFee(supplier.getAccountPubOtherServiceFee());
        } else {
            serviceFeeInfo.setAccountPayServiceFee(BigDecimal.ZERO);
        }
        serviceFeeInfo.setAccountPayServiceFeeWay(supplier.getAccountPubServiceFeeWay());

        //因公个付服务费
        if (HotelServiceFeeSwitchEnum.before.getType().equals(supplier.getPersonalPubOtherSwitch())){
            serviceFeeInfo.setPersonalPubPayServiceFee(supplier.getPersonalPubOtherServiceFee());
        } else {
            serviceFeeInfo.setPersonalPubPayServiceFee(BigDecimal.ZERO);
        }
        serviceFeeInfo.setPersonalPubPayServiceFeeWay(supplier.getPriServiceFeeWay());
    }

    /**
     * 会员房型
     * 个人支付服务费
     * @param serviceFeeInfo
     * @param supplier
     */
    private void getMemberOwnServiceFee(ServiceFeeInfo serviceFeeInfo, InitResponseBo.SupplierCompanyBo supplier){
        //个付服务费
        if (HotelServiceFeeSwitchEnum.before.getType().equals(supplier.getPriOtherSwitch())){
            serviceFeeInfo.setPersonalPayServiceFee(supplier.getPriOtherServiceFee());
        } else {
            serviceFeeInfo.setPersonalPayServiceFee(BigDecimal.ZERO);
        }
        serviceFeeInfo.setPersonalPayServiceFeeWay(supplier.getPriServiceFeeWay());
    }

    /**
     * 协议房型
     * 因公服务费
     * @param serviceFeeInfo
     * @param supplier
     */
    private void getContractPubServiceFee(ServiceFeeInfo serviceFeeInfo, InitResponseBo.SupplierCompanyBo supplier){
        //统一支付 服务费
        if (HotelServiceFeeSwitchEnum.before.getType().equals(supplier.getAccountPubContractSwitch())){
            serviceFeeInfo.setAccountPayServiceFee(supplier.getAccountPubContractServiceFee());
        } else {
            serviceFeeInfo.setAccountPayServiceFee(BigDecimal.ZERO);
        }
        serviceFeeInfo.setAccountPayServiceFeeWay(supplier.getAccountPubServiceFeeWay());

        //因公个付服务费
        if (HotelServiceFeeSwitchEnum.before.getType().equals(supplier.getPersonalPubContractSwitch())){
            serviceFeeInfo.setPersonalPubPayServiceFee(supplier.getPersonalPubContractServiceFee());
        } else {
            serviceFeeInfo.setPersonalPubPayServiceFee(BigDecimal.ZERO);
        }
        serviceFeeInfo.setPersonalPubPayServiceFeeWay(supplier.getPriServiceFeeWay());
    }

    /**
     * 协议房型
     * 个人支付服务费
     * @param serviceFeeInfo
     * @param supplier
     */
    private void getContractOwnServiceFee(ServiceFeeInfo serviceFeeInfo, InitResponseBo.SupplierCompanyBo supplier){
        //个付服务费
        if (HotelServiceFeeSwitchEnum.before.getType().equals(supplier.getPriContractSwitch())){
            serviceFeeInfo.setPersonalPayServiceFee(supplier.getPriContractServiceFee());
        } else {
            serviceFeeInfo.setPersonalPayServiceFee(BigDecimal.ZERO);
        }
        serviceFeeInfo.setPersonalPayServiceFeeWay(supplier.getPriServiceFeeWay());
    }

    private InitOrderResponseVo toResponse(InitOrderRequestVo requestVo,
                                           HotelInfoModel hotelInfo,
                                           LocalCheckAvailResponseBo checkAvailResponseBo,
                                           HotelRoomInfo hotelRoomInfo,
                                           Map<Integer, FlowDetail> flowDetailMap,
                                           ServiceFeeInfo serviceFeeInfo,
                                           OrderInfoModel orderInfo, BookingCheckResultBo checkResultBo) {
        InitOrderResponseVo responseVo = new InitOrderResponseVo();
        UserInfo userInfo = requestVo.getUserInfo();
        // 获取默认发票信息
        InvoiceInfoVo invoiceInfo = toInvoiceInfo(userInfo);
        log.info("默认发票信息json" + JsonUtils.toJsonString(invoiceInfo));
        InitOrderResponseVo.PayInfo payInfo = toPayInfo(requestVo, hotelInfo, orderInfo);
        log.info("支付信息json" + JsonUtils.toJsonString(payInfo));
        int busPriType = Objects.equals(requestVo.getExpenseType(), ExpenseTypeEnum.PUB.getCode()) ? 1 : 2;
        SupplierCompanyBo supplierCompanyBo = supplierCompanyClientLoader.findSupplierCompany(userInfo.getCorpId(), hotelInfo.getSupplierCode(), busPriType);
        log.info("查询企业订购供应商信息：response:[{}]", supplierCompanyBo);
        if (supplierCompanyBo == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SERVICE_CHARGE);
        }
        // 获取支付信息
        if (!Objects.equals(FG.getCode(), hotelInfo.getBalanceType())) {
            // 获取配送信息
            DeliveryInfoVo deliveryInfo = toDeliveryInfo(requestVo);
            Optional.ofNullable(deliveryInfo).ifPresent(invoiceInfo::setDeliveryInfo);
            log.info("配送信息json" + JsonUtils.toJsonString(deliveryInfo));
            // 获取配送费
            // 配送费和配送方式冗余到配送信息对象里
            Optional.ofNullable(supplierCompanyBo).ifPresent(e -> {
                responseVo.setDeliveryPrice(e.getDeliveryFee());
                responseVo.setDeliveryType(DeliveryTypeEnum.PJS.getCode());
                log.info("配送费" + JsonUtils.toJsonString(e.getDeliveryFee()));
            });
        }
        LocalCheckAvailResponseBo.RoomItem roomInfo = checkAvailResponseBo.getRoomInfo();
        responseVo.setInvoiceInfo(invoiceInfo);
        responseVo.setPayInfo(payInfo);
        responseVo.setHotelInfo(hotelRoomInfo);
        responseVo.setRoomTypeNote(toRoomTypeNote(checkAvailResponseBo));
        responseVo.setRoomDailyInfoList(toRoomDailyInfo(checkAvailResponseBo, hotelInfo));
        responseVo.setMinBookingRoomNum(roomInfo.getMinBookingRoomNum());
        responseVo.setMaxBookingRoomNum(roomInfo.getMaxBookingRoomNum());
        responseVo.setReceiveTextRemark(roomInfo.getReceiveTextRemark());
        responseVo.setAvailableVatInvoiceType(roomInfo.getAvailableVatInvoiceType());
        responseVo.setHongKongMacaoTaiWan(false);
        responseVo.setServiceFee(hotelRoomInfo.getServiceFee());
        setApprovalInfo(responseVo, requestVo, flowDetailMap);
        responseVo.setInvoiceTypeArr(hotelApollo.getInvoiceType());
        responseVo.setAmountHigh(hotelInfo.getAmountHigh());
        responseVo.setServiceFeeInfo(serviceFeeInfo);

        responseVo.setIsHitRC(Null.or(checkResultBo, BookingCheckResultBo::getHitRc));
        responseVo.setErrorCode(Null.or(checkResultBo, t-> String.valueOf(t.getErrorCode())));
        responseVo.setErrorMsg(Null.or(checkResultBo, BookingCheckResultBo::getErrorMsg));
        // 发票类型
        List<InitOrderResponseVo.InvoiceTypeInfo> invoiceTypeInfoList = new ArrayList<>();
        List<String> supportInvoiceTypeList = roomInfo.getSupportInvoiceTypeList();
        if (CollectionUtils.isNotEmpty(supportInvoiceTypeList)) {
            for (String code : supportInvoiceTypeList) {
                InvoiceEnum invoiceEnum = InvoiceEnum.get(code);
                if (invoiceEnum == null) {
                    continue;
                }
                InitOrderResponseVo.InvoiceTypeInfo invoiceTypeInfo = new InitOrderResponseVo.InvoiceTypeInfo();
                invoiceTypeInfo.setCode(invoiceEnum.getCode());
                invoiceTypeInfo.setDesc(invoiceEnum.getDesc());
                invoiceTypeInfo.setType(invoiceEnum.getType());
                invoiceTypeInfoList.add(invoiceTypeInfo);
            }
        }
        responseVo.setInvoiceTypeInfoList(invoiceTypeInfoList);
        responseVo.setNoConfigUrgentApply(Boolean.FALSE);
        return responseVo;
    }


    private void setApprovalInfo(InitOrderResponseVo responseVo, InitOrderRequestVo requestVo, Map<Integer, FlowDetail> flowDetailMap){
        if (flowDetailMap == null || flowDetailMap.isEmpty()) {
            return ;
        }
        FlowDetail flowDetail = flowDetailMap.get(ApprovalFlowFlagEnum.NORMAL.getCode());
        FlowDetail flowDetail2 = flowDetailMap.get(ApprovalFlowFlagEnum.EXCEED.getCode());

        responseVo.setApprovalFlowInfo(toApprovalInfo(requestVo, flowDetail));
        responseVo.setApprovalFlowInfoExceed(toApprovalInfo(requestVo, flowDetail2));

    }

    private InitOrderResponseVo.ApprovalInfoResponse toApprovalInfo(InitOrderRequestVo requestVo, FlowDetail flowDetail) {
        if (flowDetail == null) {
            return null;
        }
        InitOrderResponseVo.ApprovalInfoResponse approvalInfo = new InitOrderResponseVo.ApprovalInfoResponse();
        List<List<String>> approvalList = flowDetail.getFlowNodeInfos().stream()
                .map(node -> node.getApproveUsers().stream().map(ApproveUser::getName).collect(Collectors.toList()))
                .collect(Collectors.toList());
        approvalInfo.setApprovalList(approvalList);
        approvalInfo.setExceedStandard(Optional.ofNullable(flowDetail.getRcType()).orElse(1) == 0);
        approvalInfo.setExceedStandardInfo(requestVo.getExceedStandardInfo());
        approvalInfo.setPayTypeEnums(flowDetail.getPayTypeEnums());
        approvalInfo.setFlowType(flowDetail.getFlowType());
        //设置approveNodeList
        approvalInfo.setFlowTmplId(flowDetail.getFlowTmplId());
        approvalInfo.setApproveNodeList(new ArrayList<>());
        for (FlowNodeInfo info : flowDetail.getFlowNodeInfos()) {
            InitOrderResponseVo.ApproveNode node = new  InitOrderResponseVo.ApproveNode();
            approvalInfo.getApproveNodeList().add(node);
            node.setApproveUserNames(new ArrayList<>());
            node.setApproveUserType(info.getApproveUserType());
            node.setNodeLevel(info.getNodeLevel());
            node.setApproveUserType(info.getApproveUserType());
            node.setManualSelectType(info.getManualSelectType());
            InitOrderResponseVo.NodeApproveUser nodeApproveUser = new InitOrderResponseVo.NodeApproveUser();
            node.setNodeApproveUser(nodeApproveUser);
            nodeApproveUser.setManualApprovalType(info.getManualApprovalType());
            nodeApproveUser.setApproveUserList(new ArrayList<>());
            //审批人
            for (com.corpgovernment.api.approvalsystem.bean.ApproveUser user : info.getApproveUsers()) {
                InitOrderResponseVo.ApproveUser person = new InitOrderResponseVo.ApproveUser();
                person.setCode(user.getUid());
                person.setName(user.getName());
                person.setOrgId(user.getOrgId());
                person.setOrgName(user.getOrgName());
                nodeApproveUser.getApproveUserList().add(person);
                node.getApproveUserNames().add(person.getName());
            }
        }
        return approvalInfo;
    }

    /**
     * 获取审批信息
     */
    private Map<Integer, FlowDetail> toApprovalFlow(InitOrderRequestVo requestVo, HotelInfoModel hotelInfo, OrderInfoModel orderInfo) {
        //因私不需要审批
        if (Objects.equals(requestVo.getExpenseType(), ExpenseTypeEnum.OWN.getCode())) {
            return null;
        }
        Map<Integer, FlowDetail> result = new HashMap<>();
        UserInfo userInfo = requestVo.getUserInfo();
        String policyOrgId = StringUtils.isNotBlank(hotelInfo.getPolicyOrgId()) ? hotelInfo.getPolicyOrgId() : userInfo.getOrgId();
        String policyId = StringUtils.isNotBlank(hotelInfo.getPolicyId()) ? hotelInfo.getPolicyId() : userInfo.getUid();

        TraverlerInfo traveler = new TraverlerInfo();
        traveler.setOrgId(policyOrgId);
        traveler.setTraverlerId(policyId);
        traveler.setTraverlerName(userInfo.getUsername());

        GetFlowTmplRequest request = new GetFlowTmplRequest();
        request.setApprovalEnum(ApprovalEnum.ON);
        request.setTravelers(Lists.newArrayList(traveler));
        request.setTrafficTypes(Lists.newArrayList(6));
        request.setFlag(ApprovalFlowFlagEnum.NORMAL.getCode());
        boolean needNormal = requestVo.getOperateType()==null
                || ApprovalOperateTypeEnum.MIX_PAY.getCode().equals(requestVo.getOperateType())
                || ApprovalOperateTypeEnum.CHOOSE_MIX.getCode().equals(requestVo.getOperateType());
        log.info("审批流查询：hotelInfo.getPolicyOrgId={}，hotelInfo.getPolicyId={}, userInfo.getOrgId={}, userInfo.getUid={}，GetFlowTmplRequest={}",
                hotelInfo.getPolicyOrgId(), hotelInfo.getPolicyId(), userInfo.getOrgId(), userInfo.getUid(), JsonUtils.toJsonString(request));

        // 紧急预订只走紧急预订流程
        if(Boolean.TRUE.equals(requestVo.getUrgentApply()) || Boolean.TRUE.equals(hotelInfo.getUrgentApply())){
            log.info("紧急预定审批流程查询");
            request.setUrgentEnum(UrgentEnum.YES);
            GetFlowTmplResponse flowImpl = getFlowImpl(request);
            FlowDetail flowDetail = Optional.ofNullable(flowImpl.getFlowDetailMap()).map(e -> e.get(genFlowKey(policyId, policyOrgId))).orElse(null);
            if(flowDetail==null){
//                throw new CorpBusinessException(HotelResponseCodeEnum.URGENT_APPLY_APPROVAL_NOT_SETUP);
                log.warn("无可用的紧急预订审批流");
            }else{
                result.put(ApprovalFlowFlagEnum.NORMAL.getCode(), flowDetail);
            }
            log.info("紧急预定审批流程查询结果：{}", JsonUtils.toJsonString(result));
            return result;
        }

        // 不超标、包含混付的都返回普通审批流程
        if(needNormal) {
            GetFlowTmplResponse flowImpl = getFlowImpl(request);
            result.put(ApprovalFlowFlagEnum.NORMAL.getCode(), Optional.ofNullable(flowImpl.getFlowDetailMap()).map(e -> e.get(genFlowKey(policyId, policyOrgId))).orElse(null));
        }

        boolean exceedTravelStandard = requestVo.getOperateType()!=null
                && (ApprovalOperateTypeEnum.CHOOSE.getCode().equals(requestVo.getOperateType())
                || ApprovalOperateTypeEnum.CHOOSE_MIX.getCode().equals(requestVo.getOperateType()));
        /*
		  未超标 -》获取审批信息
		       1.无-》无需审批
		       2.有-》返回审批信息
		 */
        if (!exceedTravelStandard) {
            return result;
        }
        /*
		  超标-》获取超标配置
		       1.禁止预定-》报错禁止预定
		       2.选择rc -》查询普通审批
		           1.有-》返回普通审批
		           2.无-》返回null
		       3.超标审批-》查询超标审批信息
		           1.有-》返回审批信息
		           2.无-》报错禁止预定
		 */
        String hotelPriceRcSet = "";
        QueryApplyTripStandardResponse hotelApplyTripStandard = applyTripClientLoader.getHotelApplyTripStandard(requestVo.getTrafficId());
        if (Objects.isNull(hotelApplyTripStandard)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.APPLY_CONTROL_IS_NULL);
        }
        if (BooleanUtils.isTrue(hotelApplyTripStandard.getEnable())) {
            if (!TravelUnLimitedTypeEnum.isUnLimitAMOUNT(hotelApplyTripStandard.getUnLimitedType())) {
                StandardAmountSyncRequest amount = hotelApplyTripStandard.getStandardAmount();
                hotelPriceRcSet = amount.getControlType();
            }
        } else {
            HotelTravelStandardManageVo hotelTravelStandard = getHotelTravelStandardManageVo(requestVo, policyId, policyOrgId);
            hotelPriceRcSet = Optional.ofNullable(hotelTravelStandard).map(HotelTravelStandardManageVo::getHotelPriceControl).map(HotelPriceControl::getHotelPriceRcSet)
                    .orElseThrow(() -> new CorpBusinessException(HotelResponseCodeEnum.OVERSTANDARD_CONFIGURATION_INFORMATION_IS_EMPTY));
            log.info("配置的超标管控方式:{}", hotelPriceRcSet);
            if (Objects.nonNull(hotelInfo.getFloatControl())) {
                if (StringUtils.isNotBlank(hotelTravelStandard.getHotelPriceControl().getFloatControlType())) {
                    hotelPriceRcSet = hotelTravelStandard.getHotelPriceControl().getFloatControlType();
                }
            }
            HotelPriceControl priceControl = hotelTravelStandard.getHotelPriceControl();
            if(priceControl!=null){
                orderInfo.setSharedManageStatus(priceControl.getSharedManageStatus());
                orderInfo.setSharedPercentage(priceControl.getSharedPercentage());
                orderInfo.setHotelManageRules(priceControl.getHotelManageRules());
                orderInfo.setHotelManageStrategy(priceControl.getHotelManageStrategy());
            }
        }
        if (Objects.equals(ControlTypeEnum.F.getCode(), hotelPriceRcSet)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.OVERSTANDARD_FORBID_RESERVATION);
        }

        // 超标后混付，走普通审批
        if (hotelPriceRcSet.contains(ControlTypeEnum.M.getCode())
                && !hotelPriceRcSet.contains(ControlTypeEnum.C.getCode())
                && !hotelPriceRcSet.contains(ControlTypeEnum.A.getCode())) {
            return result;
        }
        // 超标后除了混付外，走超标审批
        request.setFlag(ApprovalFlowFlagEnum.EXCEED.getCode());
        FlowDetail flowDetail = Optional.ofNullable(getFlowImpl(request).getFlowDetailMap()).map(e -> e.get(genFlowKey(policyId, policyOrgId))).orElse(null);
        result.put(ApprovalFlowFlagEnum.EXCEED.getCode(), flowDetail);
        // 不需要返回普通审批时，全部返回超标的审批流程
        if(!needNormal && exceedTravelStandard){
            result.put(ApprovalFlowFlagEnum.NORMAL.getCode(), flowDetail);
        }
        return result;
    }

    /**
     * 获取差标信息
     *
     * @param requestVo
     * @param policyId
     * @param policyOrgId
     * @return
     */
    private HotelTravelStandardManageVo getHotelTravelStandardManageVo(InitOrderRequestVo requestVo, String policyId, String policyOrgId) {
        HotelTravelStandardManageVo hotelTravelStandard = null;
        // 优先使用差标token
        if (StrUtil.isNotBlank(requestVo.getTravelStandardToken())) {
            hotelTravelStandard = getHotelTravelStandardByToken(requestVo.getTravelStandardToken());
        } else {
            hotelTravelStandard = getHotelTravelStandardByPolicy(policyId, policyOrgId);
        }
        return hotelTravelStandard;
    }

    /**
     * 根据差标token获取差标信息
     *
     * @param travelStandardToken 差标token
     * @return
     */
    private HotelTravelStandardManageVo getHotelTravelStandardByToken(String travelStandardToken) {
        GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
        getTravelStandardByTokenRequest.setTokenList(Arrays.asList(travelStandardToken));
        List<TravelStandardResponseBO> travelStandardResponseList = applyTripClientLoader.getTravelStandardByToken(getTravelStandardByTokenRequest);
        if (CollectionUtils.isNotEmpty(travelStandardResponseList)) {
            TravelStandardResponseBO travelStandardResponse = travelStandardResponseList.get(0);
            if (travelStandardResponse != null && travelStandardResponse.getRuleChainBO() != null) {
                HotelTravelStandardManageVo hotelTravelStandard = new HotelTravelStandardManageVo();
                HotelPriceControl hotelPriceControl = new HotelPriceControl();
                RuleChainBO ruleChainBO = travelStandardResponse.getRuleChainBO();
                OffPeakSeasonRuleVO offPeakSeasonRuleVO = ruleChainBO.getOffPeakSeasonRuleVO();
                CohabitRuleVO cohabitRuleVO = ruleChainBO.getCohabitRuleVO();
                PriceRuleVO priceRuleVO = ruleChainBO.getPriceRuleVO();
                String hotelPriceRcSetResult = getHotelPriceRcSetResult(offPeakSeasonRuleVO, cohabitRuleVO, priceRuleVO);
                hotelPriceControl.setHotelPriceRcSet(hotelPriceRcSetResult);
                hotelTravelStandard.setHotelPriceControl(hotelPriceControl);
                if(cohabitRuleVO!=null){
                    hotelPriceControl.setSharedManageStatus(HotelSwitchEnum.SharedSwitch.E.getCode());
                    hotelPriceControl.setHotelManageStrategy(cohabitRuleVO.getControllerType());
                    hotelPriceControl.setHotelManageRules(cohabitRuleVO.getCalculateType());
                    hotelPriceControl.setSharedPercentage(cohabitRuleVO.getCalculateRatio());
                }
                return hotelTravelStandard;
            }
        }
        return null;
    }

    /**
     * 根据政策ID和组织ID获取差标信息
     *
     * @param policyId  政策ID
     * @param policyOrgId 组织ID
     * @return
     */
    private HotelTravelStandardManageVo getHotelTravelStandardByPolicy(String policyId, String policyOrgId) {
        return travelStandardPostClientLoader.getHotelTravelStandard(policyId, policyOrgId);
    }

    /**
     * 获取酒店价格控制结果
     *
     * @param offPeakSeasonRuleVO 淡季规则
     * @param cohabitRuleVO 同住规则
     * @param priceRuleVO 价格规则
     * @return
     */
    private String getHotelPriceRcSetResult(OffPeakSeasonRuleVO offPeakSeasonRuleVO, CohabitRuleVO cohabitRuleVO, PriceRuleVO priceRuleVO) {
        String hotelPriceRcSetResult = "";
        if (ObjectUtil.isNotEmpty(offPeakSeasonRuleVO)) {
            hotelPriceRcSetResult = JsonUtils.toJsonString(offPeakSeasonRuleVO.getRejectTypes());
        } else if (ObjectUtil.isNotEmpty(cohabitRuleVO)) {
            hotelPriceRcSetResult = JsonUtils.toJsonString(cohabitRuleVO.getRejectTypes());
        } else if (ObjectUtil.isNotEmpty(priceRuleVO)) {
            hotelPriceRcSetResult = JsonUtils.toJsonString(priceRuleVO.getRejectTypes());
        }
        return hotelPriceRcSetResult;
    }


    /**
     * 生成审批key
     */
    private String genFlowKey(String uid, String orgId) {
        return String.format("%s♀%s", orgId, uid);
    }

    /**
     * 获取审批配置
     */
    private GetFlowTmplResponse getFlowImpl(GetFlowTmplRequest request) {
        log.info("查询审批信息：request:[{}]", JsonUtils.toJsonString(request));
        GetFlowTmplResponse flowTmpl = approvalSystemClient.getFlowTmpl(request);
        log.info("查询审批信息：response:[{}]", JsonUtils.toJsonString(flowTmpl));
        if (flowTmpl == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_APPROVAL_INFORMATION);
        }
        if (CollectionUtils.isNotEmpty(flowTmpl.getFlowDetailMap())){
            flowTmpl.getFlowDetailMap().forEach((key, flowTmplInfo) ->{
                if (flowTmplInfo.getPayTypeEnums().contains(PayTypeEnum.ACCNT.getType())){
                    flowTmplInfo.getPayTypeEnums().add(PayTypeEnum.MIXPAY.getType());
                }
            });
        }

        return flowTmpl;
    }

    /**
     * 初始化订单缓存
     */
    private OrderInfoModel initOrderInfo(InitOrderRequestVo requestVo) {
        OrderInfoModel orderInfo = new OrderInfoModel();
        UserInfo userInfo = requestVo.getUserInfo();
        orderInfo.setUid(userInfo.getUid());
        orderInfo.setOrgId(userInfo.getOrgId());
        orderInfo.setDeptId(userInfo.getOrgId());
        orderInfo.setDeptName(userInfo.getOrgName());
        orderInfo.setCorpId(userInfo.getCorpId());
        orderInfo.setUname(userInfo.getUsername());
        orderInfo.setCorpPayType(requestVo.getExpenseType());
        orderInfo.setBookingChannel(userInfo.getSource());
        orderInfo.setAgentUid(requestVo.getAgentUid());
        orderInfo.setApplyNo(requestVo.getApplyNo());
        orderInfo.setTrafficId(requestVo.getTrafficId());
        orderInfo.setOperateType(requestVo.getOperateType());
        orderInfo.setUrgentApply(requestVo.getUrgentApply());
        orderInfo.setExceedTravelStandard(requestVo.isExceedTravelStandard());
        return orderInfo;
    }

    /**
     * 获取支付信息
     */
    private InitOrderResponseVo.PayInfo toPayInfo(InitOrderRequestVo requestVo, HotelInfoModel hotelInfo, OrderInfoModel orderInfo) {
        InitOrderResponseVo.PayInfo payInfo = new InitOrderResponseVo.PayInfo();
        Long payTimeMinute = getPayTimeMinute();
        payInfo.setPayInfoTip(MessageFormat.format(PAY_INFO_TIPS, payTimeMinute));
        String expenseType = requestVo.getExpenseType();
        if (Objects.equals(FG.getCode(), hotelInfo.getBalanceType())) {
            payInfo.setPayCodeList(Lists.newArrayList(PayTypeEnum.CASH.getType()));
            return filterUrgentPayType(payInfo, orderInfo);
        }
        if (Objects.equals(ExpenseTypeEnum.OWN.getCode(), expenseType)) {
            payInfo.setPayCodeList(Lists.newArrayList(PayTypeEnum.PPAY.getType()));
        } else {
            payInfo.setPayCodeList(toPayCode(requestVo.getUserInfo().getOrgId(), requestVo.getExpenseType()));
            if (StringUtils.isNotBlank(hotelInfo.getTravelStandard()) && !Objects.equals("null", hotelInfo.getTravelStandard())) {
                String travelStandard = hotelInfo.getTravelStandard();
                HotelControlVo hotelControlVo = JsonUtils.parse(travelStandard, HotelControlVo.class);
                //0：禁止预订。1：选择原因。2：预订触发审批。3：混付，4：混合和选择原因,null:没有差标
                //支持混付的情况下给混付标识
                if (hotelControlVo.getControl() != null && (hotelControlVo.getControl() == 3 || hotelControlVo.getControl() == 4)) {
                    payInfo.getPayCodeList().add(PayTypeEnum.MIXPAY.getType());
                }
            }

        }
        return filterUrgentPayType(payInfo, orderInfo);
    }

    /**
     * 获取全局支付时间（分）
     */
    private Long getPayTimeMinute() {
        Integer payTime = basicDataClientLoader.getPayTimeLimit();
        // 分钟
        if (!BaseUtils.isNotNull(payTime)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_GLOBAL_PAY_TIME);
        }
        return Long.valueOf(payTime);
    }

    /**
     * 获取联系人信息
     */
    private InitOrderResponseVo.ContactInfo toContactInfo(OrgEmployeeVo data) {
        if (data == null) {
            return null;
        }
        InitOrderResponseVo.ContactInfo contactInfo = new InitOrderResponseVo.ContactInfo();
        contactInfo.setEmail(data.getEmail());
        contactInfo.setName(data.getName());
        contactInfo.setPhone(data.getMobilePhone());
        contactInfo.setCountryCode(data.getAreaCode());
        return contactInfo;
    }

    /**
     * 获取每日房价
     */
    private List<InitOrderResponseVo.RoomDailyInfo> toRoomDailyInfo(LocalCheckAvailResponseBo checkAvailResponseBo, HotelInfoModel hotelInfo) {
        List<LocalCheckAvailResponseBo.RoomDailyInfo> roomDailyInfoList = checkAvailResponseBo.getRoomDailyInfoList();
        String breakfastDesc = hotelInfo.getBreakfastDesc();
        return roomDailyInfoList.stream().map(e -> {
            InitOrderResponseVo.RoomDailyInfo roomDailyInfo = new InitOrderResponseVo.RoomDailyInfo();
            roomDailyInfo.setEffectDate(e.getEffectDate());
            roomDailyInfo.setRoomPrice(e.getSellPrice());
            roomDailyInfo.setBreakfastName(breakfastDesc);
            return roomDailyInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取房间备注
     */
    private InitOrderResponseVo.RoomTypeNote toRoomTypeNote(LocalCheckAvailResponseBo checkAvailResponseBo) {
        InitOrderResponseVo.RoomTypeNote roomTypeNote = new InitOrderResponseVo.RoomTypeNote();
        List<LocalCheckAvailResponseBo.Remark> remarkList = checkAvailResponseBo.getRoomInfo().getRemarkList();
        if (CollectionUtils.isEmpty(remarkList)) {
            roomTypeNote.setRoomTypeShow(false);
            return null;
        }
        roomTypeNote.setRoomTypeShow(true);
        String unique = remarkList.get(0).getUnique();
        roomTypeNote.setCheckType(Objects.equals("T", unique) ? 0 : 1);
        List<InitOrderResponseVo.RoomType> roomTypeList = remarkList.stream().map(e -> {
            InitOrderResponseVo.RoomType roomType = new InitOrderResponseVo.RoomType();
            roomType.setKey(e.getKey());
            roomType.setValue(e.getDesc());
            return roomType;
        }).collect(Collectors.toList());
        roomTypeNote.setRoomTypeList(roomTypeList);
        return roomTypeNote;
    }

    /**
     * 封装可订请求
     */
    private LocalCheckAvailRequestBo toCheckAvailRequest(HotelInfoModel hotelInfo, OrderInfoModel orderInfo) {
        LocalCheckAvailRequestBo requestBo = new LocalCheckAvailRequestBo();
        requestBo.setBaseInfo(toBaseInfo(hotelInfo, orderInfo));
        requestBo.setRoomInfo(toCheckRoomInfo(hotelInfo));
        requestBo.setUid(orderInfo.getUid());
        requestBo.setOrgId(orderInfo.getCorpId());
        requestBo.setCorpPayType(orderInfo.getCorpPayType());
        requestBo.setCorpId(orderInfo.getCorpId());
        return requestBo;
    }

    private LocalCheckAvailRequestBo.CheckRoomInfo toCheckRoomInfo(HotelInfoModel hotelInfo) {
        LocalCheckAvailRequestBo.CheckRoomInfo checkRoomInfo = new LocalCheckAvailRequestBo.CheckRoomInfo();
        BeanUtils.copyProperties(hotelInfo, checkRoomInfo);
        checkRoomInfo.setGuestPerson(1);
        checkRoomInfo.setQuantity(1);
        checkRoomInfo.setRoomId(hotelInfo.getRoomId());
        return checkRoomInfo;
    }

    private LocalCheckAvailRequestBo.CheckBaseInfo toBaseInfo(HotelInfoModel hotelInfo, OrderInfoModel orderInfo) {
        LocalCheckAvailRequestBo.CheckBaseInfo checkBaseInfo = new LocalCheckAvailRequestBo.CheckBaseInfo();
        checkBaseInfo.setLanguage(orderInfo.getLanguage());
        checkBaseInfo.setSupplierCorpId(hotelInfo.getSupplierCorpId());
        checkBaseInfo.setSupplierUid(hotelInfo.getSupplierUid());
        checkBaseInfo.setSupplierCode(hotelInfo.getSupplierCode());
        return checkBaseInfo;
    }

    public HotelRoomInfo toHotelRoomInfo(HotelInfoModel hotelInfo, LocalCheckAvailResponseBo checkAvailResponseBo) {
        HotelRoomInfo hotelRoomInfo = new HotelRoomInfo();
        LocalCheckAvailResponseBo.RoomItem roomItem = checkAvailResponseBo.getRoomInfo();
        hotelRoomInfo.setHotelType(hotelInfo.getHotelType());
        hotelRoomInfo.setLongitude(hotelInfo.getLongitude());
        hotelRoomInfo.setLatitude(hotelInfo.getLatitude());
        hotelRoomInfo.setCheckInDate(hotelInfo.getCheckInDate());
        hotelRoomInfo.setCheckOutDate(hotelInfo.getCheckOutDate());
        hotelRoomInfo.setRoomName(hotelInfo.getRoomName());
        hotelRoomInfo.setBedType(hotelInfo.getBedType());
        hotelRoomInfo.setPic(hotelInfo.getPic());
        hotelRoomInfo.setAddress(hotelInfo.getAddress());
        hotelRoomInfo.setPersonCount(hotelInfo.getGuestPerson());
        hotelRoomInfo.setBreakfast(hotelInfo.getBreakfast());
        hotelRoomInfo.setBreakfastDesc(hotelInfo.getBreakfastDesc());
        hotelRoomInfo.setHotelName(hotelInfo.getHotelName());
        hotelRoomInfo.setCityName(hotelInfo.getCityName());
        hotelRoomInfo.setLastArrivalTime(hotelInfo.getLastArrivalTime());
        hotelRoomInfo.setEarlyArrivalTime(hotelInfo.getEarlyArrivalTime());
        hotelRoomInfo.setLastCancelTime(hotelInfo.getLastCancelTime());
        hotelRoomInfo.setSmoke(hotelInfo.getSmoke());
        hotelRoomInfo.setApplicativeAreaTitle(hotelInfo.getApplicativeAreaTitle());
        hotelRoomInfo.setApplicativeAreaDesc(hotelInfo.getApplicativeAreaDesc());
        List<LocalCheckAvailResponseBo.RoomDailyInfo> roomDailyInfoList = checkAvailResponseBo.getRoomDailyInfoList();
        String lastCancelTime = roomItem.getLastCancelTime();
        hotelRoomInfo.setEarlyArrivalTime(roomItem.getEarlyArrivalTime());
        hotelRoomInfo.setLastArrivalTime(roomItem.getLastArrivalTime());
        if (StringUtils.isNotBlank(lastCancelTime)) {
            hotelRoomInfo.setLastCancelTime(DateUtil.dateToYMDHM_CN(DateUtil.stringToDate(lastCancelTime, DateUtil.DF_YMD_HMS)));
        }
        hotelRoomInfo.setMaxQuantity(roomItem.getMaxBookingRoomNum());
        hotelRoomInfo.setStayNight(roomDailyInfoList.size());

        // 封装酒店取消策略和取消类型
        setCancalDetailContent(hotelInfo, hotelRoomInfo, roomItem, lastCancelTime);

        hotelRoomInfo.setPersonCount(roomItem.getGuestPerson());
        hotelRoomInfo.setHotelPhone(hotelInfo.getTelephone());
        hotelRoomInfo.setNotifyList(roomItem.getSpecialTipList());
        hotelRoomInfo.setHotelTips(new ArrayList<>());
        hotelRoomInfo.setBedType(hotelInfo.getBedType());
        hotelRoomInfo.setPic(hotelInfo.getPic());
        hotelRoomInfo.setServiceFee(checkAvailResponseBo.getServiceFee());
        hotelRoomInfo.setBrandId(hotelInfo.getBrandId());
        hotelRoomInfo.setGroupId(hotelInfo.getGroupId());
        hotelRoomInfo.setProtocolTag(hotelInfo.getProtocolTag());
        return hotelRoomInfo;
    }

    /**
     * 封装酒店取消策略和取消类型，类型根据取消类型显示，取消类型不存在显示阶梯取消，
     *      * 可订查询存在阶梯策略根据可订显示，不存在根据订单明细记录的阶梯策略展示，也不存在根据取消类型显示
     * @param hotelInfo
     * @param hotelRoomInfo
     * @param roomItem
     * @param lastCancelTime
     */
    private void setCancalDetailContent(HotelInfoModel hotelInfo, HotelRoomInfo hotelRoomInfo, LocalCheckAvailResponseBo.RoomItem roomItem, String lastCancelTime) {
        Integer policyType = hotelInfo.getCancelInfo().getPolicyType();
        hotelRoomInfo.setPolicyType(policyType);
        hotelRoomInfo.setCancelDetailList(CancelPolicyEnum.getCancelDetail(policyType, lastCancelTime));
        List<LadderDeductionInfo> ladderDeductionInfoList = roomItem.getLadderDeductionInfoList();
        if (CollectionUtils.isNotEmpty(ladderDeductionInfoList)) {
            hotelRoomInfo.setCancelDetailList(LocalCheckAvailResponseBo.getCancelDetailList(ladderDeductionInfoList));
            if (policyType == null) {
                hotelRoomInfo.setPolicyType(2);
                hotelInfo.getCancelInfo().setPolicyType(2);
            }
        } else {
            List<HotelInfoModel.LadderDeduction> ladderDeductionList = hotelInfo.getLadderDeductionList();
            if (CollectionUtils.isNotEmpty(ladderDeductionList)) {
                List<String> cancelDetailList = hotelManager.getCancelDetailList(ladderDeductionList);
                if(CollectionUtils.isNotEmpty(cancelDetailList)) {
                    hotelRoomInfo.setCancelDetailList(cancelDetailList);
                }
                if (policyType == null) {
                    hotelRoomInfo.setPolicyType(2);
                    hotelInfo.getCancelInfo().setPolicyType(2);
                }
            }
        }
    }

    /**
     * 获取配送信息
     */
    private DeliveryInfoVo toDeliveryInfo(InitOrderRequestVo requestVo) {
        //获取个人的配送地址信息
        UserInfo userInfo = requestVo.getUserInfo();
        List<ExpressAddressVo> expressAddressVoList = expressAddressClientLoader.getExpressAddress(userInfo.getUid(), userInfo.getOrgId(), requestVo.getExpenseType());
        if (CollectionUtils.isEmpty(expressAddressVoList)) {
            return null;
        }
        ExpressAddressVo expressAddress = expressAddressVoList.get(0);
        DeliveryInfoVo deliveryInfo = new DeliveryInfoVo();
        deliveryInfo.setAddress(expressAddress.getAddress());
        deliveryInfo.setCityCode(expressAddress.getCity());
        deliveryInfo.setDistrictCode(expressAddress.getDistrict());
        deliveryInfo.setProvinceCode(expressAddress.getProvince());
        deliveryInfo.setCityName(expressAddress.getCityName());
        deliveryInfo.setDistrictName(expressAddress.getDistrictName());
        deliveryInfo.setProvinceName(expressAddress.getProvinceName());
        deliveryInfo.setId(expressAddress.getId());
        deliveryInfo.setRecipientName(expressAddress.getRecipientName());
        deliveryInfo.setRecipientMobile(expressAddress.getRecipientMobilePhone());
        deliveryInfo.setDeliveryType(expressAddress.getDeliveryType());
        return deliveryInfo;
    }

    /**
     * 获取发票信息
     */
    private InvoiceInfoVo toInvoiceInfo(UserInfo userInfo) {
        CommonInvoiceVo data = commonInvoiceClientLoader.getDefaultInvoice(userInfo.getUid(), userInfo.getOrgId());
        if (data == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_INVOICE_INFORMATION);
        }
        InvoiceInfoVo vo = new InvoiceInfoVo();
        vo.setId(data.getId());
        vo.setInvoiceTitleType(data.getInvoiceTitleType());
        vo.setInvoiceType(data.getInvoiceType());
        vo.setAccountBank(data.getAccountBank());
        vo.setAccountCardNo(data.getAccountCardNo());
        vo.setCorporationAddress(data.getCorporationAddress());
        vo.setCorporationTel(data.getCorporationTel());
        vo.setInvoiceTitle(data.getInvoiceTitle());
        vo.setTaxpayerNumber(data.getTaxpayerNumber());
        return vo;
    }

    /**
     * 获取支付方式
     */
    public List<String> toPayCode(String orgId, String expenseType) {
        //获取支付方式
        PayInfoRequest request = new PayInfoRequest();
        request.setTransport("hotel");
        request.setOrgId(orgId);
        request.setName(expenseType);
        List<PayInfoResponse> payInfoList = switchClientLoader.getUserPayInfo(request);
        if (CollectionUtils.isEmpty(payInfoList)) {
            return Lists.newArrayList(PayTypeEnum.PPAY.getType());
        }
        return payInfoList.stream().map(PayInfoResponse::getCode).filter(StringUtils::isNotBlank).distinct().sorted(String::compareTo).collect(Collectors.toList());
    }

    /**
     * 查询员工信息
     */
    private OrgEmployeeVo getEmployeeInfo(String uid, String orgId) {
        OrgEmployeeVo employeeInfo = organizationEmployeeClientLoader.findEmployeeInfoByUid(uid, orgId);
        if (employeeInfo == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_USER_INFORMATION);
        }
        return employeeInfo;
    }

    private void setInitMetric(InitOrderResponseVo response){
        if(response == null){
            return;
        }
        Metrics.REGISTRY
                .counter(fillPageId.withTags("errorCode",
                        Conditional.ofNullable(response.getErrorCode()).orElse(GenericConstants.UNKNOWN)))
                .increment();
    }


    /**
     * 校验是否支付紧急预订
     * @param corpId
     * @param uid
     * @param orderInfo
     */
    private void checkUrgentEnable(String corpId, String uid, OrderInfoModel orderInfo ){
        if(orderInfo.getUrgentApply()==null || Boolean.FALSE.equals(orderInfo.getUrgentApply())){
            return;
        }
        // 校验是否支持紧急预订
        GetSwitchListRequest switchRequest = new GetSwitchListRequest();
        switchRequest.setOrgId(corpId);
        switchRequest.setUId(uid);
        GetAllSwitchResponse switchResponse = switchClientLoader.allSwitch(switchRequest);
        if(switchResponse==null || Boolean.FALSE.equals(switchResponse.getUrgentEnable())){
            throw new CorpBusinessException(HotelResponseCodeEnum.URGENT_APPLY_DISABLE);
        }
        orderInfo.setUrgentPayType(switchResponse.getUrgentPayType());
    }

    /**
     * 紧急预定支付方式
     * @param payInfo
     * @return
     */
    private InitOrderResponseVo.PayInfo filterUrgentPayType(InitOrderResponseVo.PayInfo payInfo, OrderInfoModel orderInfo){
        if(!Boolean.TRUE.equals(orderInfo.getUrgentApply())
                || ExpenseTypeEnum.OWN.getCode().equalsIgnoreCase(orderInfo.getCorpPayType())
                || CollectionUtils.isEmpty(payInfo.getPayCodeList())
                || SwitchFieldKeyEnum.URGENT_PAY_TYPE_ALL.getKey().equalsIgnoreCase(orderInfo.getUrgentPayType())){
            return payInfo;
        }
        Iterator<String> payTypes = payInfo.getPayCodeList().iterator();
        while (payTypes.hasNext()){
            if(!payTypes.next().equalsIgnoreCase(orderInfo.getUrgentPayType())){
                payTypes.remove();
            }
        }
        return payInfo;
    }
}
