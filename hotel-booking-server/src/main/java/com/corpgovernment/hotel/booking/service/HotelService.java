package com.corpgovernment.hotel.booking.service;

import com.corpgovernment.api.approvalsystem.service.ApprovalSystemClient;
import com.corpgovernment.api.hotel.booking.hotel.request.HotelDetailRequestVO;
import com.corpgovernment.api.hotel.booking.hotel.request.SearchHotelListRequestVO;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelDetailRequestBo;
import com.corpgovernment.api.hotel.product.model.request.LocalHotelListRequestBo;
import com.corpgovernment.api.hotel.product.model.request.Tree;
import com.corpgovernment.api.hotel.product.model.response.HotelDetailResponseVO;
import com.corpgovernment.api.hotel.product.model.response.SearchHotelListResponseVO;
import com.corpgovernment.api.hotel.product.vo.HotelCityVo;
import com.corpgovernment.api.hotel.product.vo.HotelFilterListVo;
import com.corpgovernment.api.hotel.product.vo.HotelFilterRequestVo;
import com.corpgovernment.api.hotel.product.vo.HotelFilterchildrenDataVo;
import com.corpgovernment.api.hotel.product.vo.HotelInitRequestVo;
import com.corpgovernment.api.hotel.product.vo.HotelRequestVo;
import com.corpgovernment.api.hotel.product.vo.HotelSearchRequestVo;
import com.corpgovernment.api.hotel.product.vo.SearchCityVo;
import com.corpgovernment.api.organization.soa.IOrganizationClient;
import com.corpgovernment.basic.bo.response.HotelIndexVo;
import com.corpgovernment.basic.constant.FilterConstant;
import com.corpgovernment.basic.impl.HotelIndexService;
import com.corpgovernment.basic.service.IHotelBasicDataService;
import com.corpgovernment.common.base.BaseRequestVO.UserInfo;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.hotel.booking.vo.CheckHotelCityRequestVO;
import com.corpgovernment.hotel.booking.vo.CheckHotelCityResponseVO;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.corpgovernment.hotel.product.service.HoHotelService;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: HotelService
 * @description: TODO
 * @author: zdwang
 * @date: Created in 16:54 2019/9/6
 * @Version: 1.0
 **/
@Slf4j
@Service
@AllArgsConstructor
public class HotelService {
    private final HotelManager hotelManager;
    private final HoHotelService hotelService;
    private final HotelIndexService hotelIndexService;
    private final IHotelBasicDataService hotelBasicDataService;
	private final ApprovalSystemClient approvalSystemClient;
	private final IOrganizationClient organizationClient;
	/**
	 * 酒店列表请求
	 */
	public SearchHotelListResponseVO searchList(@Valid SearchHotelListRequestVO requestVo, BaseUserInfo userInfo) {
		// 检查停留时间
		hotelManager.checkStateTime(true);
		LocalHotelListRequestBo requestBo = this.convert(requestVo);
		requestBo.setUid(userInfo.getUid());
		requestBo.setOrgId(userInfo.getOrgId());
		requestBo.setBaseCorpId(userInfo.getCorpId());
		requestBo.setToken(userInfo.getToken());
		return hotelService.page(requestBo);
	}

	/**
	 * vo转bo
	 *
	 * @param requestVo
	 * @return
	 */
	private LocalHotelListRequestBo convert(SearchHotelListRequestVO requestVo) {
		LocalHotelListRequestBo requestBo = new LocalHotelListRequestBo();
		requestBo.setMinPrice(requestVo.getMinPrice());
		requestBo.setMaxPrice(requestVo.getMaxPrice());
		requestBo.setTag(requestVo.getTag());
		requestBo.setKey(requestVo.getKey());
		requestBo.setTitle(requestVo.getTitle());
		requestBo.setBusiness(requestVo.getBusiness());
		requestBo.setDistrict(requestVo.getDistrict());
		requestBo.setSubway(requestVo.getSubway());
		requestBo.setCatagory(requestVo.getCatagory());
		requestBo.setRecommendSort(requestVo.getRecommendSort());
		requestBo.setLandmark(requestVo.getLandmark());
		requestBo.setStarList(requestVo.getStarList());
		requestBo.setCorpPayType(requestVo.getCorpPayType());
		requestBo.setRcpolicy(requestVo.getRcpolicy());
		requestBo.setProtatal(requestVo.getProtatal());
		requestBo.setBreakfast(requestVo.getBreakfast());
		requestBo.setCheckImmediate(requestVo.getCheckImmediate());
		requestBo.setFreeCancel(requestVo.getFreeCancel());
		requestBo.setSDate(requestVo.getSDate());
		requestBo.setEDate(requestVo.getEDate());
		requestBo.setCity(requestVo.getCity());
		requestBo.setCityCode(requestVo.getCityCode());
		requestBo.setPageNum(requestVo.getPageNum());
		requestBo.setPageSize(requestVo.getPageSize());
		return requestBo;
	}

	/**
	 * 酒店详情请求
	 */
	public HotelDetailResponseVO detail(HotelDetailRequestVO requestVo, BaseUserInfo baseUserInfo) {
		LocalHotelDetailRequestBo requestBo = JsonUtils.convert(requestVo, LocalHotelDetailRequestBo.class);
		requestBo.setUid(baseUserInfo.getUid());
		requestBo.setOrgId(baseUserInfo.getOrgId());
		requestBo.setBaseCorpId(baseUserInfo.getCorpId());
		requestBo.setToken(baseUserInfo.getToken());
		requestBo.setBaseUserInfo(baseUserInfo);
		requestBo.setTrafficId(requestVo.getTrafficId());
		return hotelService.detail(requestBo, baseUserInfo);
	}

	/**
	 * 酒店详情请求
	 */
	public CheckHotelCityResponseVO checkHotelCity(CheckHotelCityRequestVO request,BaseUserInfo baseUserInfo) {
		return hotelService.checkHotelCity(request);
	}

	/**
	 * 获取筛选项信息
	 */
	public JSONResult<List<HotelFilterListVo>> hotelFilterListByCity(HotelRequestVo request) {
		UserInfo userInfo = request.getUserInfo();
		List<HotelFilterListVo> result = hotelBasicDataService.hotelFilterListByCity(request.getCity(), userInfo.getCorpId(), false);
		return JSONResult.success(result);
	}

	/**
	 * 模糊搜索酒店城市
	 */
	public JSONResult<List<HotelCityVo>> searchHotelCity(SearchCityVo vo) {
		List<HotelCityVo> hotelCityVos = hotelBasicDataService.searchHotelCityBySearchKey(vo.getKey(), vo.getCountryId(),vo.getDomestic());
		return JSONResult.success(hotelCityVos);
    }

    /**
     * 酒店首页初始化
     */
    public JSONResult<HotelIndexVo> init(HotelInitRequestVo hotelInitRequestVo, BaseUserInfo baseUserInfo) {
        hotelInitRequestVo.setUId(baseUserInfo.getUid());
        hotelInitRequestVo.setCorpId(baseUserInfo.getCorpId());
        hotelInitRequestVo.setOrgId(baseUserInfo.getOrgId());
        HotelIndexVo result = hotelIndexService.hotelIndex(baseUserInfo);
		result.setAddApplyTripEnable(getAddApplyTripEnable(baseUserInfo.getOrgId()));
        return JSONResult.success(result);
    }

	private boolean getAddApplyTripEnable(String orgId) {
		try{
			JSONResult<Boolean> config = organizationClient.getAddApplyTripEnableConfig(orgId);
			if(config.isSUCCESS()){
				return config.getData() != null && config.getData();
			}
		}catch (Exception e){
			log.error("------++++ 获取是否显示新增出差申请单按钮异常， 补偿false，不影响业务", e);

		}
		return false;
	}

	/**
     * 酒店过滤
     */
    public JSONResult hotelFilter(HotelFilterRequestVo request) {
		UserInfo userInfo = request.getUserInfo();
		List<HotelFilterListVo> hotelFilter = hotelIndexService.hotelFilter(request.getCityId(), userInfo.getCorpId());
        // 去除地铁站类型数据
        if (CollectionUtils.isNotEmpty(hotelFilter)) {
            hotelFilter.removeIf(
                hotelFilterListVo -> StringUtils.equalsIgnoreCase(FilterConstant.METRO, hotelFilterListVo.getTitle()));
        }
        Map json = new HashMap();
        json.put("filterList", hotelFilter);
        return JSONResult.success(json);
    }

    /**
     * 模糊搜索
     */
    public JSONResult searchLocation(HotelSearchRequestVo hotelSearchRequestVo) {
        List list = hotelIndexService.searchLocation(hotelSearchRequestVo);
        Map json = new HashMap<>();
        json.put("filterResult", list);
        return JSONResult.success(json);
    }

}