package com.corpgovernment.hotel.booking.enums;

/**
 * <p>
 * 描述: 取消单据状态
 * </p>
 * <p>
 * 创建时间: 2024-11-06
 * </p>
 *
 */
public enum CancelFormStatusEnum {

    PLATFORM_SUBMITTED(0, "差旅平台已提交"),
    SUBMITTED(1, "已提交"),
    COORDINATING(2, "协调中"),
    HANDLE_SUCCESS(3, "处理成功"),
    HANDLE_FAILED(4, "处理失败"),
    HANDLE_REVOCATION(5, "处理撤销"),
    ;

    private final Integer code;

    private final String desc;

    CancelFormStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CancelFormStatusEnum getByCode(Integer code) {
        for (CancelFormStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
