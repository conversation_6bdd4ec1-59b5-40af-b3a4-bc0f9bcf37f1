package com.corpgovernment.hotel.booking.vo;

import lombok.Data;

import java.util.List;

/**
 * 查询地址响应对象结果：服务商契约
 */
@Data
public class SearchAddressSupplierResponse {
    private ResponseStatus ResponseStatus;
    private Status status;
    private List<AddressList> addressList;

    @Data
    public class ResponseStatus {

        private String Timestamp;
        private String Ack;
        private List<String> Errors;
    }

    @Data
    public class Status {

        private boolean success;
        private int errorCode;
        private String message;
    }

    @Data
    public class AddressList {

        private String address;
        private String addressDetail;
        private String latitude;
        private String longitude;
        private List<SubAddressList> subAddressList;


        @Data
        public class SubAddressList {

            private String address;
            private String shortName;
            private String latitude;
            private String longitude;
        }
    }



}