package com.corpgovernment.hotel.booking.cache;

import com.corpgovernment.api.hotel.product.model.response.LocalHotelListResponseBo.HotelListBean;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 酒店缓存信息
 *
 * <AUTHOR>
 * @since 2023/7/4
 */
@Data
public class HotelListPageCacheInfo {
    /**
     * 已经展示的酒店列表信息(已mapping完成)
     */
    private List<HotelListBean> showedHotelListInfo;

    /**
     * 多余出来没有展示的酒店列表信息(已mapping完成)
     */
    private List<HotelListBean> unShowedHotelListInfo;

    /**
     * 供应商信息 supplierCode->供应商酒店列表相关信息
     */
    private Map<String, SupplierHotelModel> supplierInfo;
}
