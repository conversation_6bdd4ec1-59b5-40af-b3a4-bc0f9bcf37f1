package com.corpgovernment.hotel.booking.controller;


import com.corpgovernment.api.ordercenter.dto.supplement.HotelOrderSupplementInfo;
import com.corpgovernment.api.ordercenter.dto.supplement.OrderSupplementInfoResponse;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.utils.ObjectStringTrimUtils;
import com.corpgovernment.hotel.booking.metric.MetricService;
import com.corpgovernment.hotel.booking.service.SupplementService;
import com.corpgovernment.hotel.booking.service.supplement.SupplementOrderService;
import com.corpgovernment.hotel.booking.vo.*;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/supplement")
@Slf4j
public class HotelSupplementController {

	@Autowired
	private SupplementService supplementService;
	@Autowired
	private MetricService metricService;

	@Autowired
	private SupplementOrderService supplementOrderService;
	/**
	 * 补录初始化
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping("/init")
	public JSONResult<HotelInitPageResponseVO> init(@RequestBody @Valid SupplementInitRequestVO request, BaseUserInfo baseUserInfo) {
		request.setBaseUserInfo(baseUserInfo);
		return JSONResult.success(supplementService.init(request));
	}

	/**
	 * 补录提交
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping("/submit")
    public JSONResult<HotelSubmitResponseVO> submit(@RequestBody @Valid SupplementSubmitRequestVO request,
        BaseUserInfo baseUserInfo) {
        ObjectStringTrimUtils.allFieldRemoveWarp(request);

        request.setBaseUserInfo(baseUserInfo);
        request.setSource(baseUserInfo.getSource());
        return JSONResult.success(metricService.metricOrderSupplement(
            Optional.ofNullable(request.getHotelInfo()).map(HotelInfoVO::getSupplierCode).orElse(StringUtils.EMPTY),
            () -> supplementService.submit(request)));
    }


	/**
	 * 订单补录-数据保存
	 *
	 * @param request
	 * @return
	 */
	@PostMapping("/supplementOrderSoa")
	public JSONResult<OrderSupplementInfoResponse> supplementOrderSoa(@RequestBody List<HotelOrderSupplementInfo> request){
		return JSONResult.success(supplementOrderService.saveOrder(request));
	}

}
