package com.corpgovernment.basic.impl;

import com.corpgovernment.api.advertisement.enums.AdsChannelEnum;
import com.corpgovernment.api.advertisement.enums.AdsLocationlEnum;
import com.corpgovernment.api.advertisement.vo.AdsInfoVo;
import com.corpgovernment.api.applytrip.soa.response.ApplyTripTrafficVerifyResponse;
import com.corpgovernment.api.applytrip.traffic.request.TripTrafficRequest;
import com.corpgovernment.api.applytrip.traffic.response.HotelTripResponse;
import com.corpgovernment.api.approvalsystem.service.ApprovalSystemClient;
import com.corpgovernment.api.approvalsystem.service.request.GetFlowTmplRequest;
import com.corpgovernment.api.basic.dto.BasicCityInfoDto;
import com.corpgovernment.api.basic.request.BasicCityListRequest;
import com.corpgovernment.api.basic.response.BasicIntegratedCityResponse;
import com.corpgovernment.api.basic.soa.BasicDataClient;
import com.corpgovernment.api.hotel.product.vo.HotelFilterListVo;
import com.corpgovernment.api.hotel.product.vo.HotelSearchRequestVo;
import com.corpgovernment.api.organization.enums.SwitchEnum;
import com.corpgovernment.api.organization.model.switchinfo.GetAllSwitchResponse;
import com.corpgovernment.api.organization.model.switchinfo.GetSwitchListRequest;
import com.corpgovernment.api.supplier.bo.suppliercompany.ListPubOwnRequestBo;
import com.corpgovernment.api.supplier.soa.constant.ProductTypeEnum;
import com.corpgovernment.api.travelstandard.vo.MbTravelstandHotelVo;
import com.corpgovernment.basic.bo.response.DestinationInfoListResponseBo;
import com.corpgovernment.basic.bo.response.HotelCityInfoVo;
import com.corpgovernment.basic.bo.response.HotelIndexVo;
import com.corpgovernment.basic.bo.response.HotelSearchResponseBo;
import com.corpgovernment.basic.bo.response.OtherCityDestinationListResponseBo;
import com.corpgovernment.basic.constant.DestinationTypeEnum;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.handle.HotelBasicDataHandler;
import com.corpgovernment.basic.service.IHotelIndexService;
import com.corpgovernment.basicdata.bo.HotelCityBo;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.OrderTypeControlEnum;
import com.corpgovernment.common.oversea.OverseaCityUtils;
import com.corpgovernment.common.utils.PreBookingMetricUtils;
import com.corpgovernment.hotel.product.dataloader.soa.AdvertisementClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.ApplyTripClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SupplierCompanyClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.SwitchClientLoader;
import com.corpgovernment.hotel.product.dataloader.soa.TravelStandardPostClientLoader;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import lombok.extern.slf4j.Slf4j;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.corpgovernment.basic.handle.HotelBasicDataHandler.DOMESTIC_TYPE;
import static com.corpgovernment.basic.utils.ApplyTripTrafficUtil.checkApplyTripCityLimited;

/**
 * @ClassName: HotelIndexService
 * @description: TODO
 * @author: yssong
 * @date: Created in 18:34 2019/8/27
 * @Version: 1.0
 **/
@Slf4j
@Service
public class HotelIndexService implements IHotelIndexService {

	@Autowired
	private HotelBasicDataService hotelBasicDataService;
	@Autowired
	private AdvertisementClientLoader advertisementClientLoader;
	@Autowired
	private ApplyTripClientLoader applyTripClientLoader;
	@Autowired
	private SwitchClientLoader switchClientLoader;
	@Autowired
	private SupplierCompanyClientLoader supplierCompanyClientLoader;
	@Autowired
	private TravelStandardPostClientLoader travelStandardPostClientLoader;
    @Autowired
    private ApprovalSystemClient approvalSystemService;
    @Autowired
    private HotelBasicDataHandler hotelBasicDataHandler;
    @Autowired
    private BasicDataClient basicDataClient;

    public static final String HOTEL = "hotel";
    /**
     * 首页埋点
     */
    private Id indexMetricId = Metrics.REGISTRY.createId("hotel.booking.index");
    @Override
    public HotelIndexVo hotelIndex(BaseUserInfo baseUserInfo) {
		HotelIndexVo hotelIndexVo = new HotelIndexVo();
		//因公因私
		ListPubOwnRequestBo request = new ListPubOwnRequestBo();
		request.setCompanyCode(baseUserInfo.getCorpId());
		request.setProductType(ProductTypeEnum.hotel.name());
		List<String> corpPayTypeList = supplierCompanyClientLoader.listPubOwnType(request);
		hotelIndexVo.setCorpPayTypeList(corpPayTypeList);

		//广告位
		List<AdsInfoVo> adsData = advertisementClientLoader.getAdsListByChannelAndLocation(AdsChannelEnum.APP.getCode(), AdsLocationlEnum.HOTEL.getCode());
		hotelIndexVo.setAdvList(adsData);

		//酒店差标开关
        MbTravelstandHotelVo hotelSwitch = travelStandardPostClientLoader.getHotelSwitch(baseUserInfo.getUid(), baseUserInfo.getOrgId());
        log.info("同住开关查询结果:{}", hotelIndexVo);
        if (hotelSwitch == null) {
            hotelIndexVo.setLwStatus(false);
        } else {
            hotelIndexVo.setLwStatus(
                    Objects.equals("E", hotelSwitch.getSharedManageStatus()) ? true : false
            );
        }

        //出差申请模式
		GetSwitchListRequest getSwitchListRequest = new GetSwitchListRequest();
		getSwitchListRequest.setUId(baseUserInfo.getUid());
		getSwitchListRequest.setOrgId(baseUserInfo.getCorpId());
		getSwitchListRequest.setSwitchKey(SwitchEnum.APPLICATIONCONTROL.getKey());
		GetAllSwitchResponse allSwitchResponse = switchClientLoader.allSwitch(getSwitchListRequest);
		if (null == allSwitchResponse) {
			throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_QUERY_THE_MANAGETMENT_MODE);
		}
		log.info("查询管控结果数据:{}", JsonUtils.toJsonString(allSwitchResponse));
		boolean isApplication = !"3".equals(allSwitchResponse.getApplicationControlValue()) &&
                allSwitchResponse.getSwitchInfoSoaMap().get(SwitchEnum.APPLICATION_TYPE_CONTROL.getKey()).getValue().contains(OrderTypeControlEnum.HOTEL.getCode()) ? true : false;
        log.info("管控结果:isApplication={}, urgentEnable={}", isApplication, hotelIndexVo.getUrgentEnable());

		if (isApplication) {
            hotelIndexVo.setUrgentEnable(allSwitchResponse.getUrgentEnable());
            if(Boolean.TRUE.equals(hotelIndexVo.getUrgentEnable())){
                hotelIndexVo.setUrgentApprovalFlow(checkUrgentApprovalFlow());
            }
			TripTrafficRequest trafficRequest = new TripTrafficRequest();
			trafficRequest.setTrafficType(3);
			trafficRequest.setUid(baseUserInfo.getUid());
			trafficRequest.setOrgId(baseUserInfo.getOrgId());
			List<HotelTripResponse> hotelTripResponseList = applyTripClientLoader.listInitHotelTripTraffics(trafficRequest);
			hotelIndexVo.setApplicationData(hotelTripResponseList);
		}
		hotelIndexVo.setIsApplication(isApplication);
        hotelIndexVo.setPolicyRange(allSwitchResponse.getPolicyRange());
        Metrics.REGISTRY.counter(indexMetricId).increment();
		return hotelIndexVo;
	}

	@Override
    public List<HotelFilterListVo> hotelFilter(String city, String corpId) {
        return hotelBasicDataService.hotelFilterListByCityUseNewData(city, false);
	}

	@Override
	public List<Map<String, List<Object>>> searchLocation(HotelSearchRequestVo requestVo) {
        boolean applyCityControl = false;
        if (Objects.nonNull(requestVo.getTrafficId())){
            BasicCityListRequest request = new BasicCityListRequest();
            request.setCityIdList(Collections.singletonList(requestVo.getCityid()));
            JSONResult<BasicIntegratedCityResponse> cityResponse = basicDataClient.listBasicCityInfoByIds(request);
            BasicCityInfoDto cityInfoDto = Optional.ofNullable(cityResponse).map(JSONResult::getData).map(BasicIntegratedCityResponse::getCityInfoList).orElse(new ArrayList<>()).stream().findFirst().orElse(new BasicCityInfoDto());
            boolean domesticTag = Objects.equals(cityInfoDto.getAreaType(), DOMESTIC_TYPE);
            //有出差申请单走出差申请单
            ApplyTripTrafficVerifyResponse trafficVerifyResponse = hotelBasicDataService.getApplyTripTrafficVerify(requestVo.getTrafficId());
            applyCityControl = checkApplyTripCityLimited(trafficVerifyResponse, domesticTag);
        }

        //获取城市信息
		HotelCityBo cityInfo = hotelBasicDataService.getCityInfo(requestVo.getCityid());
		if (cityInfo == null) {
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.CITY_SELECTED_ERROR.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.PLEASE_SELECT_A_NEW_CITY);
        }
        HotelCityInfoVo hotelCityInfoVo = new HotelCityInfoVo();
        hotelCityInfoVo.setId(cityInfo.getCityId());
        hotelCityInfoVo.setProvince(cityInfo.getProvinceName());
        hotelCityInfoVo.setCountry(cityInfo.getCountryName());
        hotelCityInfoVo.setName(cityInfo.getCityName());

        //获取搜索结果集
        HotelSearchResponseBo search = hotelBasicDataService.getSearchInfo(requestVo);

        List<Map<String, List<Object>>> destinationResult = new ArrayList<>();
        //动态Map
        Map<String, List<Object>> map1 = new LinkedHashMap<>();
        Map<String, List<Object>> map2 = new LinkedHashMap<>();

        List<DestinationInfoListResponseBo> destinationInfoList = search.getDestinationInfoList();
        List<OtherCityDestinationListResponseBo> otherCityDestinationList = hotelBasicDataHandler.filterOverseaOrDomesticHotelByCityIdOld(search.getOtherCityDestinationList(), requestVo.getCityid());

        if (CollectionUtils.isNotEmpty(destinationInfoList)) {
            for (DestinationInfoListResponseBo destinationInfoListResponseBo : destinationInfoList) {
                StringBuilder buffer = new StringBuilder();
                String lat = StringUtils.isBlank(destinationInfoListResponseBo.getCoordinateInfo().getGLat()) ? "*" : destinationInfoListResponseBo.getCoordinateInfo().getGLat();
                String lon = StringUtils.isBlank(destinationInfoListResponseBo.getCoordinateInfo().getGLon()) ? "*" : destinationInfoListResponseBo.getCoordinateInfo().getGLon();
                buffer.append(destinationInfoListResponseBo.getDestinationId()).append("&")
                        .append(destinationInfoListResponseBo.getDestinationType()).append("&")
                        .append(lon).append("&")
                        .append(lat);
                String key = buffer.toString();

                List<Object> value = new ArrayList<>();
                value.add(destinationInfoListResponseBo.getKeywordName());
                value.add(DestinationTypeEnum.getValueByCode(destinationInfoListResponseBo.getDestinationType()));
                value.add(destinationInfoListResponseBo.getDestinationType());
                value.add(hotelCityInfoVo);
                map1.put(key, value);
            }
        }

        if (!applyCityControl) {
            if (destinationResult.size() < 30) {
                if (CollectionUtils.isNotEmpty(otherCityDestinationList)) {
                    for (OtherCityDestinationListResponseBo otherCityDestinationListResponseBo : otherCityDestinationList) {
                        StringBuilder buffer = new StringBuilder();
                        String lat = StringUtils.isBlank(otherCityDestinationListResponseBo.getCoordinateInfo().getGLat()) ? "*" : otherCityDestinationListResponseBo.getCoordinateInfo().getGLat();
                        String lon = StringUtils.isBlank(otherCityDestinationListResponseBo.getCoordinateInfo().getGLon()) ? "*" : otherCityDestinationListResponseBo.getCoordinateInfo().getGLon();
                        buffer.append(otherCityDestinationListResponseBo.getDestinationId()).append("&")
                                .append(otherCityDestinationListResponseBo.getDestinationType()).append("&")
                                .append(lon).append("&")
                                .append(lat);
                        String key = buffer.toString();

                        List<Object> value = new ArrayList<>();
                        value.add(otherCityDestinationListResponseBo.getDestinationName());
                        value.add(DestinationTypeEnum.getValueByCode(otherCityDestinationListResponseBo.getDestinationType()));
                        value.add(otherCityDestinationListResponseBo.getDestinationType());

                        HotelCityInfoVo cityInfoVo = new HotelCityInfoVo();
                        cityInfoVo.setId(otherCityDestinationListResponseBo.getCityId());
                        cityInfoVo.setProvince(otherCityDestinationListResponseBo.getProvinceName());
                        cityInfoVo.setCountry(otherCityDestinationListResponseBo.getCountryName());
                        cityInfoVo.setName(otherCityDestinationListResponseBo.getCityName());
                        value.add(cityInfoVo);
                        map2.put(key, value);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(map1) && CollectionUtils.isEmpty(map2) || StringUtils.isBlank(requestVo.getKeyword())) {
            return null;
        } else {
            if (CollectionUtils.isNotEmpty(map1)) {
                destinationResult.add(map1);
            } else {
                destinationResult.add(null);
            }

            if (CollectionUtils.isNotEmpty(map2)) {
                destinationResult.add(map2);
            } else {
                destinationResult.add(null);
            }

            destinationResult.add(null);
        }

        return destinationResult;
    }

    /**
     * 校验是否有紧急预订的审批流
     * @return
     */
    private Boolean checkUrgentApprovalFlow(){
        try {
            GetFlowTmplRequest request = new GetFlowTmplRequest();
            JSONResult<Boolean> result = approvalSystemService.checkUrgentApproval(request);
            log.info("查询是否有紧急预订的审批流：request={}, result={}", JsonUtils.toJsonString(request), JsonUtils.toJsonString(result));
            if( result!=null && result.getData()!=null){
                return result.getData();
            }
        } catch (Exception e){
            log.info("校验是否有紧急预订审批流程异常", e);
        }
        return false;
    }
}
