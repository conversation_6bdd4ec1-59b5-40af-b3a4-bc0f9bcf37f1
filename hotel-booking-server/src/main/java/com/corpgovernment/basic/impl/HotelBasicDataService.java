package com.corpgovernment.basic.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.corpgovernment.api.applytrip.apply.sync.StandardAmountSyncRequest;
import com.corpgovernment.api.applytrip.soa.response.ApplyTripTrafficVerifyResponse;
import com.corpgovernment.api.applytrip.soa.response.QueryApplyTripStandardResponse;
import com.corpgovernment.api.applytrip.vo.AoApplyTripStockVo;
import com.corpgovernment.api.basic.bo.SearchBasicDataRequestBo;
import com.corpgovernment.api.basic.request.FuzzySearchHotelCityRequest;
import com.corpgovernment.api.basic.response.HotelCityInfoFuzzySearchResponse;
import com.corpgovernment.api.hotel.product.model.request.InitPageRequestVO;
import com.corpgovernment.api.hotel.product.model.response.HotelConditionResponse;
import com.corpgovernment.api.hotel.product.model.response.HotelForAppNewVo;
import com.corpgovernment.api.hotel.product.vo.*;
import com.corpgovernment.api.organization.bo.LandmarkBO;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoRequest;
import com.corpgovernment.api.organization.model.switchinfo.PayInfoResponse;
import com.corpgovernment.api.organization.model.user.employee.OrgEmployeeVo;
import com.corpgovernment.api.organization.soa.resident.GetAllDifferentialPriceRequest;
import com.corpgovernment.api.supplier.soa.constant.ProductTypeEnum;
import com.corpgovernment.api.supplier.soa.response.MbSupplierProductResponse;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.api.travelstandard.bo.HotelTravelForAppRequestBO;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.soa.TravelStandardPostClient;
import com.corpgovernment.api.travelstandard.vo.*;
import com.corpgovernment.api.travelstandard.vo.request.GetHotelDetailRequest;
import com.corpgovernment.basic.bo.request.HotelSearchRequestBo;
import com.corpgovernment.basic.bo.response.HotelSearchResponseBo;
import com.corpgovernment.basic.constant.FilterCodeConstant;
import com.corpgovernment.basic.constant.FilterConstant;
import com.corpgovernment.basic.constant.HotelFilterBrandEnum;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.convert.HotelQueryConvert;
import com.corpgovernment.basic.service.IHotelBasicDataService;
import com.corpgovernment.basicdata.bo.*;
import com.corpgovernment.basicdata.entity.db.BdHpHotelAreaSupplierEntity;
import com.corpgovernment.basicdata.mapper.HpHotelAreaSupplierMapper;
import com.corpgovernment.common.apollo.CommonApollo;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.AbstractBaseService;
import com.corpgovernment.common.base.BaseRequestVO.UserInfo;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.handler.ICacheHandler;
import com.corpgovernment.common.utils.*;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.controller.vo.HotelCityFuzzySearchInfoReqVo;
import com.corpgovernment.core.domain.hotelconfig.model.entity.DynamicFilter;
import com.corpgovernment.core.domain.hotelconfig.model.entity.DynamicFilterRequest;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hotelconfig.service.IDynamicFilterDomainService;
import com.corpgovernment.core.service.IHotelDataService;
import com.corpgovernment.hotel.booking.enums.TravelUnLimitedTypeEnum;
import com.corpgovernment.hotel.booking.service.TokenAccessService;
import com.corpgovernment.hotel.booking.service.TravelStandardService;
import com.corpgovernment.hotel.product.dataloader.soa.*;
import com.corpgovernment.hotel.product.dto.GeographyInfoDTO;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.DateUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @ClassName: hotelBasicDataService
 * @description: 酒店基础信息
 * @author: yssong
 * @date: Created in 10:10 2019/8/16
 * @Version: 1.0
 **/
@Slf4j
@Service
public class HotelBasicDataService extends AbstractBaseService implements IHotelBasicDataService {

    private final static String[] STAR_NAMES = {"不限", "一星级", "二星级", "三星级", "四星级", "五星级", "六星级"};
    /**
     * 过滤条件-品牌类型种类
     */
    private final static Set<String> FILTER_BRANDS = Arrays.stream(HotelFilterBrandEnum.values()).map(HotelFilterBrandEnum::getBelongName).collect(Collectors.toSet());
    @Autowired
    private TravelStandardPostClient travelStandardPostService;
    @Autowired
    private SwitchClientLoader switchClientLoader;
    @Autowired
    private HotelApollo hotelApollo;
    @Autowired
    private CommonService commonService;
    @Autowired
    private BasicDataClientLoader basicDataClientLoader;
    @Autowired
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    @Autowired
    private OrganizationEmployeeClientLoader organizationEmployeeClientLoader;
    @Autowired
    private TravelStandardPostClientLoader travelStandardPostClientLoader;
    @Autowired
    private OrganizationClientLoader organizationClientLoader;
    @Autowired
    private HotelQueryConvert hotelQueryConvert;
    @Autowired
    private HpHotelAreaSupplierMapper hotelAreaSupplierMapper;
    @Autowired
    @Qualifier(value = "queryThreadPoolExecutor")
    private ThreadPoolExecutor queryThreadPoolExecutor;
    @Autowired
    private ApplyTripClientLoader applyTripClientLoader;
    @Autowired
    private ICacheHandler cacheHandler;
    @Autowired
    private TravelStandardService travelStandardService;
    @Autowired
    private TokenAccessService tokenAccessService;
    @Autowired
    private IHotelDataService iHotelDataService;
    @Autowired
    private CommonApollo commonApollo;
    @Resource
    private IDynamicFilterDomainService dynamicFilterDomainService;
    
    private List<String> hongKongMacauTaiwanProvinceIdList  = Arrays.asList("32", "33", "53");
    private final static String HOTEL_INTL = "HOTEL_INTL";
    private final static String HOTEL = "HOTEL";

    @Override
    public Boolean allHotelCity() {
        //获取酒店城市数据
        basicDataClientLoader.searchHotelCityByCountryId("1");
        return true;
    }

    /**
     * 获取模糊搜索信息
     */
    public HotelSearchResponseBo getSearchInfo(HotelSearchRequestVo requestVo) {
        String supplierCode = hotelApollo.getSearchLocationSupplierCode();
        String supplierCorpId = hotelApollo.getSearchLocationSupplierCorpId();
        if (StringUtils.isBlank(supplierCode) || StringUtils.isBlank(supplierCorpId)) {
            log.warn("模糊搜索服务商未配置");
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.FUZZY_SEARCH_SUPPLIER_IS_NOT_CONFIGURED.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.FUZZY_SEARCH_SUPPLIER_IS_NOT_CONFIGURED);
        }
        MbSupplierProductResponse supplierProduct = commonService.getSupplierInfo(supplierCode, ProductTypeEnum.hotel.name(), hotelOperatorTypeConfig.getSearchLocation());
        if (supplierProduct == null) {
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.GET_SUPPLIER_URL_ERROR.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.FAILED_OBTAIN_SUPPLIER_URL);
        }
        HotelCitySupplierBo supplierCity = commonService.getCitySupplier(requestVo.getCityid(), supplierProduct.getSupplierCode());
        if (supplierCity == null) {
            PreBookingMetricUtils.saveHttpRequest(HotelResponseCodeEnum.GET_SUPPLIER_CITY_INFO_ERROR.code());
            throw new CorpBusinessException(HotelResponseCodeEnum.GET_SUPPLIER_CITY_INFO_ERROR);
        }

        HotelSearchRequestBo request = new HotelSearchRequestBo();
        request.setCityId(supplierCity.getSupplierCityId());
        request.setKeyWord(requestVo.getKeyword());
        request.setCountryId(supplierCity.getSupplierCountryId());
        request.setCorpID(supplierCorpId);
        HotelSearchResponseBo responseBo;
        try {
            String httpResponse;
            if (hotelApollo.isCtrip(supplierCode)) {
                httpResponse = HttpUtils.doPostJSON(supplierCode, "模糊搜索查询", supplierProduct.getProductUrl(), JsonUtils.toJsonString(request));
                responseBo = JsonUtils.parse(httpResponse, HotelSearchResponseBo.class);
                if (responseBo == null || responseBo.getResponseStatus() == null) {
                    log.warn("搜索查询失败，参数:{}", request);
                }
                if (!("Success").equals(responseBo.getResponseStatus().getAck())) {
                    log.warn("搜索查询失败，参数:{}", request);
                }
            } else {
                httpResponse = HttpUtils.doPostJSONUseSign(supplierCode, "模糊搜索查询", supplierProduct.getProductUrl(), JsonUtils.toJsonString(request), supplierProduct.getUserKey());
                responseBo = JsonUtils.parse(httpResponse, HotelSearchResponseBo.class);
                if (!responseBo.isSuccess()) {
                    log.warn("搜索查询失败，参数:{}", request);
                }
            }
            return responseBo;
        } catch (Exception e) {
            log.warn("搜索查询失败", e);
        }
        return null;
    }

    /**
     * 获取城市信息
     */
    public HotelCityBo getCityInfo(String cityId) {
        List<HotelCityBo> hotelCityList = basicDataClientLoader.searchHotelCity(new SearchBasicDataRequestBo().setCityId(cityId));
        if (CollectionUtils.isEmpty(hotelCityList)) {
            log.warn("城市行政区信息查询失败，参数:{}", cityId);
            return null;
        }
        return hotelCityList.get(0);
    }

    public BdHpHotelAreaSupplierEntity selectSupplierArea(String areaId, String supplierCode) {
        BdHpHotelAreaSupplierEntity supplierEntity = new BdHpHotelAreaSupplierEntity();
        supplierEntity.setAreaId(areaId);
        supplierEntity.setSupplierCode(supplierCode);
        List<BdHpHotelAreaSupplierEntity> select = hotelAreaSupplierMapper.select(supplierEntity);
        if (CollectionUtils.isNotEmpty(select) && select.size() > 0) {
            return select.get(0);
        }
        return null;
    }

    @Override
    public List<HotelFilterListVo> hotelFilterListByCity(String city, String corpId, Boolean isList) {
        log.info("city:{},isList：{}", city, isList);
        if (StringUtils.isBlank(city)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_CITY_IS_EMPTY);
        }
        //返回数据
        List<HotelFilterListVo> data = new ArrayList<>(4);
        //地标过滤
        // landmarkFilterByCity(city, corpId, isList, data);
        //行政区过滤
        areaFilterByCity(city, isList, data);
        //商业区过滤
        zoneFilterByCity(city, isList, data);
        //地铁线过滤
        metroFilterByCity(city, isList, data);
        //品牌过滤
        brandFilterByCity(city, isList, data);
        return data;
    }

    /**
     * 使用新的数据源请求
     * 
     * <AUTHOR> 2024/12/23
     */
    @Override
    public List<HotelFilterListVo> hotelFilterListByCityUseNewData(String city, boolean isList) {
        if (StringUtils.isBlank(city)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.REQUEST_PARAM_NULL, "cityId");
        }
        // 使用新的数据源请求来源
        GeographyInfoDTO geographyInfoDTO = basicDataClientLoader.listGeographyInfoByCityIds(Lists.newArrayList(city));
        // 封装返回数据
        return convertHotelFilterListByCityUseNewData(isList, geographyInfoDTO);
    }

    private List<HotelFilterListVo> convertHotelFilterListByCityUseNewData(boolean isList,
        GeographyInfoDTO geographyInfoDTO) {
        List<HotelFilterListVo> data = Lists.newArrayList();
        // 行政区过滤
        if (CollectionUtil.isNotEmpty(geographyInfoDTO.getAreaBoList())) {
            data.add(getHotelAreaFilterListVo(isList, geographyInfoDTO.getAreaBoList()));
        }
        // 商业区过滤
        if (CollectionUtil.isNotEmpty(geographyInfoDTO.getZoneBoList())) {
            data.add(getHotelZoneFilterListVo(isList, geographyInfoDTO.getZoneBoList()));
        }
        // 地铁线过滤
        if (CollectionUtil.isNotEmpty(geographyInfoDTO.getMetroBoList())) {
            data.add(getHotelMetroFilterListVo(isList, geographyInfoDTO.getMetroBoList()));
        }
        // 品牌过滤
        if (CollectionUtil.isNotEmpty(geographyInfoDTO.getBrandBoList())) {
            data.add(getHotelBrandFilterListVo(isList, geographyInfoDTO.getBrandBoList()));
        }
        return data;
    }

    private void landmarkFilterByCity(String cityId, String corpId, Boolean isList, List<HotelFilterListVo> data) {
        List<LandmarkBO> landmarkList = organizationClientLoader.searchOrgLandmark(cityId, corpId);
        if (CollectionUtils.isEmpty(landmarkList)) {
            log.info("没有查到可用的公司地标，参数cityId:{}，corpId:{}", cityId, corpId);
            return;
        }
        HotelFilterListVo filterLandmark = new HotelFilterListVo();
        filterLandmark.setTitle(FilterConstant.COMPANY_LANDMARK);
        filterLandmark.setKey(FilterCodeConstant.COMPANY_LANDMARK);
        List<HotelFilterchildrenListVo> filterLocationChildrenList = new ArrayList<>(landmarkList.size());
        //热门数据处理
        List<HotelFilterchildrenDataVo> hotLocationList = new ArrayList<>(landmarkList.size());
        if (isList) {
            for (LandmarkBO landmark : landmarkList) {
                HotelFilterchildrenListVo childrenList = new HotelFilterchildrenListVo();
                StringBuilder buffer = new StringBuilder();
                String key = buffer.append("*&")
                        .append("LANDMARK").append("&").append(landmark.getLongitude()).append("&").append(landmark.getLatitude()).toString();
                childrenList.setKey(key);
                childrenList.setTitle(landmark.getLandmarkName());
                filterLocationChildrenList.add(childrenList);
            }
            filterLandmark.setMore(filterLocationChildrenList);
        } else {
            for (LandmarkBO landmark : landmarkList) {
                HotelFilterchildrenDataVo childData = new HotelFilterchildrenDataVo();
                StringBuilder buffer = new StringBuilder();
                String key = buffer.append("*&")
                        .append("LANDMARK").append("&").append(landmark.getLongitude()).append("&").append(landmark.getLatitude()).toString();
                childData.setKey(key);
                childData.setTitle(landmark.getLandmarkName());
                hotLocationList.add(childData);
            }
            filterLandmark.setChildren(hotLocationList);
        }
        filterLandmark.setExclusion(true);
        filterLandmark.setType(false);
        data.add(filterLandmark);
    }

    /**
     * 行政区过滤
     */
    public void areaFilterByCity(String cityId, Boolean isList, List<HotelFilterListVo> data) {
        List<HotelAreaBo> hotelAreaBos = basicDataClientLoader.searchHotelArea(new SearchBasicDataRequestBo().setCityId(cityId));
        if (CollectionUtils.isEmpty(hotelAreaBos)) {
            log.warn("城市行政区信息查询失败，参数:{}", cityId);
            return;
        }
        HotelFilterListVo filterLocationList = getHotelAreaFilterListVo(isList, hotelAreaBos);
        data.add(filterLocationList);
    }

    private static HotelFilterListVo getHotelAreaFilterListVo(Boolean isList, List<HotelAreaBo> hotelAreaBos) {
        HotelFilterListVo filterLocationList = new HotelFilterListVo();
        filterLocationList.setTitle(FilterConstant.LOCATION);
        filterLocationList.setKey(FilterCodeConstant.LOCATION);
        List<HotelFilterchildrenListVo> filterLocationChildrenList = new ArrayList<>(hotelAreaBos.size());
        //热门数据处理
        List<HotelFilterchildrenDataVo> hotLocationList = new ArrayList<>(hotelAreaBos.size());
        if (isList) {
            for (HotelAreaBo hotelAreaBo : hotelAreaBos) {
                HotelFilterchildrenListVo hotelFilterchildrenListVo = new HotelFilterchildrenListVo();
                StringBuilder buffer = new StringBuilder();
                String key = buffer.append(hotelAreaBo.getAreaId()).append("&")
                        .append("LOCATION").append("&").append("*").append("&").append("*").toString();
                hotelFilterchildrenListVo.setKey(key);
                hotelFilterchildrenListVo.setTitle(hotelAreaBo.getAreaName());
                filterLocationChildrenList.add(hotelFilterchildrenListVo);
            }
            filterLocationList.setMore(filterLocationChildrenList);
        } else {
            for (HotelAreaBo hotelAreaBo : hotelAreaBos) {
                HotelFilterchildrenDataVo hotelFilterchildrenListVo = new HotelFilterchildrenDataVo();
                StringBuilder buffer = new StringBuilder();
                String key = buffer.append(hotelAreaBo.getAreaId()).append("&")
                        .append("LOCATION").append("&").append("*").append("&").append("*").toString();
                hotelFilterchildrenListVo.setKey(key);
                hotelFilterchildrenListVo.setTitle(hotelAreaBo.getAreaName());
                hotLocationList.add(hotelFilterchildrenListVo);
            }
            filterLocationList.setChildren(hotLocationList);
        }
        return filterLocationList;
    }

    /**
     * 商业区
     */
    public void zoneFilterByCity(String cityId, Boolean isList, List<HotelFilterListVo> data) {
        List<HotelZoneBo> zoneData = basicDataClientLoader.searchHotelZone(new SearchBasicDataRequestBo().setCityId(cityId));
        if (CollectionUtils.isEmpty(zoneData)) {
            log.warn("城市行政区信息查询失败，参数:{}", cityId);
            return;
        }
        HotelFilterListVo filterZoneList = getHotelZoneFilterListVo(isList, zoneData);
        data.add(filterZoneList);
    }

    private static HotelFilterListVo getHotelZoneFilterListVo(Boolean isList, List<HotelZoneBo> zoneData) {
        HotelFilterListVo filterZoneList = new HotelFilterListVo();
        filterZoneList.setTitle(FilterConstant.ZONE);
        filterZoneList.setKey(FilterCodeConstant.ZONE);
        List<HotelFilterchildrenListVo> filterZoneChildrenList = new ArrayList<>(zoneData.size());
        List<HotelFilterchildrenDataVo> hotZoneList = new ArrayList<>(zoneData.size());
        if (isList) {
            for (HotelZoneBo zoneVo : zoneData) {
                HotelFilterchildrenListVo hotelFilterchildrenListVo = new HotelFilterchildrenListVo();
                StringBuilder buffer = new StringBuilder();
                String lon = zoneVo.getGdLon() != null ? zoneVo.getGdLon() : "*";
                String lat = zoneVo.getGdLat() != null ? zoneVo.getGdLat() : "*";
                String key = buffer.append(zoneVo.getZoneId()).append("&")
                        .append("ZONE").append("&")
                        .append(lon).append("&")
                        .append(lat).toString();
                hotelFilterchildrenListVo.setKey(key);
                hotelFilterchildrenListVo.setTitle(zoneVo.getZoneName());
                filterZoneChildrenList.add(hotelFilterchildrenListVo);
            }
            filterZoneList.setMore(filterZoneChildrenList);
        } else {
            //热门数据处理
            for (HotelZoneBo zoneVo : zoneData) {
                HotelFilterchildrenDataVo hotelFilterchildrenListVo = new HotelFilterchildrenDataVo();
                StringBuilder buffer = new StringBuilder();
                String lon = zoneVo.getGdLon() != null ? zoneVo.getGdLon() : "*";
                String lat = zoneVo.getGdLat() != null ? zoneVo.getGdLat() : "*";
                String key = buffer.append(zoneVo.getZoneId()).append("&")
                        .append("ZONE").append("&")
                        .append(lon).append("&")
                        .append(lat).toString();
                hotelFilterchildrenListVo.setKey(key);
                hotelFilterchildrenListVo.setTitle(zoneVo.getZoneName());
                hotZoneList.add(hotelFilterchildrenListVo);
            }
            filterZoneList.setChildren(hotZoneList);
        }
        return filterZoneList;
    }

    /**
     * 地铁线
     */
    public void metroFilterByCity(String cityId, Boolean isList, List<HotelFilterListVo> data) {
        List<HotelMetroBo> metroData = basicDataClientLoader.searchHotelMetro(new SearchBasicDataRequestBo().setCityId(cityId));
        if (CollectionUtils.isEmpty(metroData)) {
            log.warn("城市行政区信息查询失败，参数:{}", cityId);
            return;
        }
        HotelFilterListVo filterMetroList = getHotelMetroFilterListVo(isList, metroData);
        data.add(filterMetroList);
    }

    private static HotelFilterListVo getHotelMetroFilterListVo(Boolean isList, List<HotelMetroBo> metroData) {
        int metroCount = 0;
        //热门数据
        List<HotelFilterchildrenDataVo> hotMetroData = new ArrayList<>(16);
        HotelFilterListVo filterMetroList = new HotelFilterListVo();
        filterMetroList.setTitle(FilterConstant.METRO);
        filterMetroList.setKey(FilterCodeConstant.METRO);
        List<HotelFilterchildrenListVo> filterMetroChildrenList = new ArrayList<>(metroData.size());
        for (HotelMetroBo metroLineDictResponseBo : metroData) {
            HotelFilterchildrenListVo hotelFilterchildrenListVo = new HotelFilterchildrenListVo();
            //地铁线
            hotelFilterchildrenListVo.setKey(metroLineDictResponseBo.getMetroLineId());
            hotelFilterchildrenListVo.setTitle(metroLineDictResponseBo.getMetroLineName());
            filterMetroChildrenList.add(hotelFilterchildrenListVo);
            //地铁站
            List<HotelMetroStationBo> metroStationResponseBos = metroLineDictResponseBo.getStationList();
            List<HotelFilterchildrenDataVo> filterchildrenDataList = new ArrayList<>(metroStationResponseBos.size());
            if (CollectionUtils.isNotEmpty(metroStationResponseBos)) {
                for (HotelMetroStationBo stationVo : metroStationResponseBos) {
                    HotelFilterchildrenDataVo hotelFilterchildrenDataVo = new HotelFilterchildrenDataVo();
                    StringBuilder buffer = new StringBuilder();
                    String lon = stationVo.getGdLon() != null ? stationVo.getGdLon() : "*";
                    String lat = stationVo.getGdLat() != null ? stationVo.getGdLat() : "*";
                    String key = buffer.append(stationVo.getMetroStationId()).append("&")
                            .append("METRO_STATION").append("&")
                            .append(lon).append("&")
                            .append(lat).toString();
                    hotelFilterchildrenDataVo.setKey(key);
                    hotelFilterchildrenDataVo.setTitle(stationVo.getMetroStationName());
                    filterchildrenDataList.add(hotelFilterchildrenDataVo);
                    hotelFilterchildrenListVo.setChildren(filterchildrenDataList);
                    if (hotMetroData.size() < 16) {
                        hotMetroData.add(hotelFilterchildrenDataVo);
                    }
                    metroCount++;
                }

            }

        }
        //特殊数据处理
        if (isList) {
            filterMetroList.setMore(filterMetroChildrenList);
        } else {
            filterMetroList.setChildren(hotMetroData);
            if (metroCount > 16) {
                filterMetroList.setMore(filterMetroChildrenList);
            }
        }
        return filterMetroList;
    }

    /**
     * 品牌过滤
     */
    public void brandFilterByCity(String cityId, Boolean isList, List<HotelFilterListVo> data) {
        //类型为1表示只取品牌
        List<HotelBrandBo> brandData = basicDataClientLoader.searchHotelBrand(new SearchBasicDataRequestBo().setCityId(cityId).setBrandType("1"));
        if (CollectionUtils.isEmpty(brandData)) {
            log.warn("城市行政区信息查询失败，参数:{}", cityId);
            return;
        }
        HotelFilterListVo filterBrandList = getHotelBrandFilterListVo(isList, brandData);
        data.add(filterBrandList);
    }

    private HotelFilterListVo getHotelBrandFilterListVo(Boolean isList, List<HotelBrandBo> brandData) {
        HotelFilterListVo filterBrandList = new HotelFilterListVo();
        filterBrandList.setTitle(FilterConstant.BRAND);
        filterBrandList.setKey(FilterCodeConstant.BRAND);

        // 酒店品牌数据列表
        List<HotelBrandBo> hotelBrandList = brandData.stream()
                .filter(brand -> StringUtils.isNotBlank(brand.getBrandName()))
                .filter(brand -> StringUtils.isBlank(brand.getBelongtoName()) || FILTER_BRANDS.contains(brand.getBelongtoName()))
                .collect(Collectors.toList());

        // 酒店品牌数据列表-按品牌类型名名分类
        Map<String, List<HotelBrandBo>> hotelBrandDataMap = hotelBrandList.stream()
                .filter(item -> item != null && StringUtils.isNotBlank(item.getBelongtoName()))
                .collect(Collectors.groupingBy(HotelBrandBo::getBelongtoName));

        // 构建要输出的品牌列表
        List<HotelFilterchildrenListVo> filterBrandChildrenList = Arrays.stream(HotelFilterBrandEnum.values())
                .map(brand -> {
                    // 按分类拿到对应的品牌列表
                    String brandBelongName = brand.getBelongName();
                    List<HotelFilterchildrenDataVo> brandDataList = hotelBrandDataMap.getOrDefault(brandBelongName, new ArrayList<>())
                            .stream().map(this::buildHotelBrandData)
                            .collect(Collectors.toList());

                    // 其他品牌类型，需要把hotelBrandDataMap中品牌类型名为空的品牌类型也加到其他品牌类型中
                    if (brand == HotelFilterBrandEnum.OTHER) {
                        List<HotelFilterchildrenDataVo> noBelongNameBrand = hotelBrandDataMap.entrySet().stream()
                                .filter(entry -> StringUtils.isBlank(entry.getKey()))
                                .map(Entry::getValue).flatMap(Collection::stream)
                                .map(this::buildHotelBrandData)
                                .collect(Collectors.toList());
                        brandDataList.addAll(noBelongNameBrand);
                    }

                    // 构建返回vo
                    HotelFilterchildrenListVo hotelFilterchildrenListVo = new HotelFilterchildrenListVo();
                    hotelFilterchildrenListVo.setKey(brand.getKey());
                    hotelFilterchildrenListVo.setTitle(brandBelongName);
                    hotelFilterchildrenListVo.setChildren(brandDataList);
                    return hotelFilterchildrenListVo;

                }).collect(Collectors.toList());

        if (isList) {
            filterBrandList.setMore(filterBrandChildrenList);
        } else {
            //热门数据处理
            List<HotelFilterchildrenDataVo> hotData = hotelBrandList.stream().limit(16)
                    .map(this::buildHotelBrandData)
                    .collect(Collectors.toList());

            filterBrandList.setChildren(hotData);
            if (hotelBrandList.size() > 16) {
                filterBrandList.setMore(filterBrandChildrenList);
            }
        }
        filterBrandList.setExclusion(false);
        filterBrandList.setType(true);
        return filterBrandList;
    }

    /**
     * 构建酒店品牌信息
     */
    private HotelFilterchildrenDataVo buildHotelBrandData(HotelBrandBo hotelBrandItemResponseBo) {
        HotelFilterchildrenDataVo hotelFilterchildrenDataVo = new HotelFilterchildrenDataVo();
        String key = hotelBrandItemResponseBo.getBrandId() + "&" +
                "HOTEL_BRAND" + "&" + "*" + "&" + "*";
        hotelFilterchildrenDataVo.setKey(key);
        hotelFilterchildrenDataVo.setTitle(hotelBrandItemResponseBo.getBrandName());
        return hotelFilterchildrenDataVo;
    }

    @Override
    public List<HotelCityVo> searchHotelCityBySearchKey(String key, String countryId, Boolean demotic) {
        if (StringUtils.isBlank(key)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_CITY_IS_EMPTY);
        }
        log.info("searchHotelCityBySearchKey key:{}",key);
        List<HotelCityBo> hotelCityList = null;
        Boolean hmtCityDisplay = commonApollo.getHMTCityDisplay();
        //灰度开启走新接口逻辑 灰度关闭走老接口逻辑
        if (hmtCityDisplay && demotic != null && !demotic) {
            HotelCityFuzzySearchInfoReqVo searchInfoRequest = new HotelCityFuzzySearchInfoReqVo();
            searchInfoRequest.setSearchStr(key);
            searchInfoRequest.setDomestic(false);
            searchInfoRequest.setProductType(HOTEL_INTL);
            HotelCityInfoFuzzySearchResponse response = iHotelDataService.hotelCityInfoFromDistribution(searchInfoRequest);
            hotelCityList = basicDataClientLoader.fuzzySearchHotelCityWithoutCountryId(response);
        } else if (hmtCityDisplay && demotic != null && demotic) {
            FuzzySearchHotelCityRequest request = new FuzzySearchHotelCityRequest();
            request.setCountryId(countryId);
            request.setKey(key);
            request.setProductType(HOTEL);
            hotelCityList = iHotelDataService.fuzzySearchHotelCity(request);
        } else if (!hmtCityDisplay && demotic != null && !demotic) {
            FuzzySearchHotelCityRequest request = new FuzzySearchHotelCityRequest();
            request.setCountryId(countryId);
            request.setKey(key);
            request.setProductType(BizTypeEnum.HOTEL.getCode());
            hotelCityList = basicDataClientLoader.fuzzySearchHotelCityWithoutCountryId(request);
            if (hmtCityDisplay){
                hotelCityList = hotelCityList.stream()
                        .filter(item -> !hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId()))
                        .collect(Collectors.toList());
            }
        } else if (!hmtCityDisplay && demotic != null && demotic) {
            FuzzySearchHotelCityRequest request = new FuzzySearchHotelCityRequest();
            request.setCountryId(countryId);
            request.setKey(key);
            request.setProductType(BizTypeEnum.HOTEL.getCode());
            hotelCityList = basicDataClientLoader.fuzzySearchHotelCity(request);
            if (!hmtCityDisplay){
                hotelCityList = hotelCityList.stream()
                        .filter(item -> !hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId()))
                        .collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(hotelCityList)) {
            return new ArrayList<>();
        }
        return hotelCityList.stream().map(res->{
            HotelCityVo cityVo = new HotelCityVo();
            cityVo.setCity(res.getCityId());
            cityVo.setCityName(res.getCityName());
            cityVo.setCityEnName(res.getCityEnName());
            cityVo.setProvinceName(res.getProvinceName());
            cityVo.setCountryName(res.getCountryName());
            cityVo.setCityId(res.getCityId());
            return cityVo;
        }).collect(Collectors.toList());
    }

    /**
     * 模糊查询 计算每个城市的热度分数，排序
     */
    private HotelCityVo setCityScore(HotelCityVo infoVo, String citySearchKey, String searchKey) {
        double score = 0.0;
        for (String key : citySearchKey.split("\\|")) {
            int searchIndex = key.indexOf(searchKey);
            if (searchIndex != -1) {
                if (key.equals(searchKey)) {
                    score = 1000;
                    break;
                }
                score += 100 - searchIndex;
                BigDecimal bigDecimal = new BigDecimal(searchKey.length()).divide(new BigDecimal(key.length()), 3, BigDecimal.ROUND_UP);
                score += bigDecimal.doubleValue();
            }
        }
        infoVo.setSearchScore(score);
        return infoVo;
    }

    @Override
    public HotelConditionResponse conditions(InitPageRequestVO initPageRequest) {
        HotelConditionResponse conditionResponse = new HotelConditionResponse();
        // 1.价格星级筛选
        CompletableFuture<HotelControlVo> future1 = CompletableFuture.supplyAsync(() -> {
            return getHotelControlVo(initPageRequest);
        }, queryThreadPoolExecutor).handle((controlVo, throwable) -> {
            if (throwable != null) {
                return null;
            } else {
                return controlVo;
            }
        });
        // 2.更多筛选
        CompletableFuture<List<HotelFilterListVo>> future2 = CompletableFuture.supplyAsync(() -> {
            return getHotelFilters(initPageRequest);
        }, queryThreadPoolExecutor).handle((vos, throwable) -> {
            if (throwable != null) {
                return null;
            } else {
                return vos;
            }
        });
        // 3.差标说明
        CompletableFuture<List<HotelForAppVo>> future3 = CompletableFuture.supplyAsync(() -> {
            return getHotelForAppVo(initPageRequest);
        }, queryThreadPoolExecutor).handle((rcDetailVos, throwable) -> {
            if (throwable != null) {
                return null;
            } else {
                return rcDetailVos;
            }
        });
        // 4.支付方式
        CompletableFuture<List<PayInfoResponse>> future4 = CompletableFuture.supplyAsync(() -> {
            return getPayInfo(initPageRequest);
        }, queryThreadPoolExecutor).handle((response, throwable) -> {
            if (throwable != null) {
                return null;
            } else {
                return response;
            }
        });
        // 5.用户随心订
        CompletableFuture<Boolean> future5 = CompletableFuture.supplyAsync(() -> {
            return getPayTips(initPageRequest);
        }, queryThreadPoolExecutor).handle((response, throwable) -> {
            if (throwable != null) {
                return null;
            } else {
                return response;
            }
        });

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(future1, future2, future3, future4);
        try {
            combinedFuture.get();
            conditionResponse.setFilterList(future2.get());
            conditionResponse.setPriceLevel(hotelQueryConvert.convertFromBO(future1.get()));
            conditionResponse.setRcList(hotelQueryConvert.convertFromBO(future3.get()));
            conditionResponse.setPayType(Optional.ofNullable(future4.get()).orElse(new ArrayList<>()).stream()
                .map(PayInfoResponse::getCode).collect(Collectors.toList()));
            conditionResponse.setIsReadPayTips(future5.get());
            log.info("future1:{}",JsonUtils.toJsonString(future1.get()));
            log.info("future2:{}",JsonUtils.toJsonString(future2.get()));
            log.info("future3:{}",JsonUtils.toJsonString(future3.get()));
            log.info("future4:{}",JsonUtils.toJsonString(future4.get()));
            log.info("future5:{}",JsonUtils.toJsonString(future5.get()));
            //获取酒店同住差标
            if (initPageRequest.getUserInfo() != null) {
                MbTravelstandHotelVo hotelSwitch = travelStandardPostClientLoader.getHotelSwitch(initPageRequest.getUserInfo().getUid(), initPageRequest.getUserInfo().getOrgId());
                log.info("hotelSwitch:{}",JsonUtils.toJsonString(hotelSwitch));
                if (hotelSwitch == null) {
                    conditionResponse.setLwStatus(false);
                } else {
                    conditionResponse.setLwStatus(Objects.equals("E", hotelSwitch.getSharedManageStatus()));
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        HotelConditionResponse hotelConditionResponse =buildConditionResponse(conditionResponse);
        // 差标替换
        String travelStandardToken = initPageRequest.getTravelStandardToken();
        if (StringUtils.isNotBlank(travelStandardToken)) {
            List<HotelForAppNewVo> hotelForAppNewVoList = tokenAccessService.getTravelStandardDesc(travelStandardToken, initPageRequest.getCityCode());
            if (CollectionUtils.isNotEmpty(hotelForAppNewVoList)) {
                List<com.corpgovernment.api.hotel.product.model.response.HotelForAppVo> tmpList = hotelForAppNewVoList.stream().filter(Objects::nonNull).map(item -> {
                    com.corpgovernment.api.hotel.product.model.response.HotelForAppVo hotelForAppVo = new com.corpgovernment.api.hotel.product.model.response.HotelForAppVo();
                    hotelForAppVo.setTitle(item.getTitle());
                    List<HotelForAppNewVo.RcDetailVo> policyDetail = item.getPolicyDetail();
                    if (CollectionUtils.isNotEmpty(policyDetail)) {
                        hotelForAppVo.setRcDetail(policyDetail.stream().filter(Objects::nonNull).map(a -> {
                            com.corpgovernment.api.hotel.product.model.response.RcDetailVo rcDetailVo = new com.corpgovernment.api.hotel.product.model.response.RcDetailVo();
                            rcDetailVo.setContent(a.getContent());
                            rcDetailVo.setTitle(a.getTitle());
                            return rcDetailVo;
                        }).collect(Collectors.toList()));
                    }
                    return hotelForAppVo;
                }).collect(Collectors.toList());
                hotelConditionResponse.setRcList(tmpList);
            }
        }
        // 钟点房筛选项
        DynamicFilter dynamicFilter = queryDynamicFilter(initPageRequest);
        hotelConditionResponse.setShowHourlyRoomFilter(Optional.ofNullable(dynamicFilter)
                .map(DynamicFilter::getShowHourlyRoomFilter)
                .orElse(null));
        log.info("hotelConditionResponse,hotelConditionResponse:{}",JsonUtils.toJsonString(hotelConditionResponse));
        return hotelConditionResponse;
    }
    
    private DynamicFilter queryDynamicFilter(InitPageRequestVO initPageRequest) {
        if (initPageRequest == null) {
            return null;
        }
        
        DynamicFilterRequest dynamicFilterRequest = new DynamicFilterRequest();
        dynamicFilterRequest.setBizTypeEnum(BizTypeEnum.HOTEL);
        TravelModeEnum travelModeEnum = TravelModeEnum.getEnum(initPageRequest.getCorpPayType());
        dynamicFilterRequest.setTravelModeEnum(travelModeEnum);
        dynamicFilterRequest.setToken(initPageRequest.getTravelStandardToken());
        return dynamicFilterDomainService.queryDynamicFilter(dynamicFilterRequest);
    }

    /**
     * 用从token获取的价格替换
     * @param conditionResponse
     * @return
     */
    public HotelConditionResponse buildConditionResponse(HotelConditionResponse conditionResponse){
        log.info("conditionResponse:{}",JsonUtils.toJsonString(conditionResponse));
        com.corpgovernment.api.hotel.product.model.response.HotelControlVo  priceLevel = conditionResponse.getPriceLevel();
        if(ObjectUtil.isEmpty(priceLevel)){
            return conditionResponse;
        }
        com.corpgovernment.api.hotel.product.model.response.AveragePriceSet averagePriceSet = priceLevel.getAveragePriceSet();
        if(ObjectUtil.isEmpty(averagePriceSet)){
            return conditionResponse;
        }
        String priceCeiling = averagePriceSet.getPriceCeiling();
        if(StrUtil.isBlank(priceCeiling)){
            return conditionResponse;
        }
        List<com.corpgovernment.api.hotel.product.model.response.HotelForAppVo> rcList = conditionResponse.getRcList();
        if(CollectionUtil.isEmpty(rcList)){
            return conditionResponse;
        }
        com.corpgovernment.api.hotel.product.model.response.HotelForAppVo hotelForAppVo = rcList.get(0);
        List<com.corpgovernment.api.hotel.product.model.response.RcDetailVo> rcDetail = hotelForAppVo.getRcDetail();
        if(CollectionUtil.isEmpty(rcDetail)){
            return conditionResponse;
        }
        for (com.corpgovernment.api.hotel.product.model.response.RcDetailVo rcDetailVo : rcDetail) {
            if(rcDetailVo.getTitle().equals("可订价格")){
                rcDetailVo.setContent(StrUtil.format("¥{}/间夜及以下",priceCeiling));
            }
        }
        return conditionResponse;
    }


    @Override
    public List<HotelForAppNewVo> getHotelTravelStandard(InitPageRequestVO request) {
        QueryApplyTripStandardResponse hotelApplyTripStandard = applyTripClientLoader.getHotelApplyTripStandard(request.getTrafficId());
        if (Objects.isNull(hotelApplyTripStandard)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.APPLY_CONTROL_IS_NULL);
        }
        if (BooleanUtils.isTrue(hotelApplyTripStandard.getEnable())) {
            return convert(buildHotelTravelStandardShowByApply(hotelApplyTripStandard));
        }
        List<HotelForAppVo> hotelForAppVo = this.getHotelForAppVo(request);
        return convert(hotelForAppVo);
    }

    private List<HotelForAppNewVo> convert(List<HotelForAppVo> hotelForAppVo) {
        List<HotelForAppNewVo> result = new ArrayList<>();
        for (HotelForAppVo hotel:hotelForAppVo){
            HotelForAppNewVo appNewVo = new HotelForAppNewVo();
            appNewVo.setTitle(hotel.getTitle());
            appNewVo.setPolicyDetail(JsonUtils.convert(hotel.getRcDetail(), List.class));
            result.add(appNewVo);
        }
        return result;
    }

    private List<HotelForAppVo> buildHotelTravelStandardShowByApply(QueryApplyTripStandardResponse hotelApplyTripStandard) {
        List<HotelForAppVo> voList = new ArrayList<>();
        HotelForAppVo vo = buildNoHotelTravelStandardDetail();
        voList.add(vo);
        if (TravelUnLimitedTypeEnum.isUnLimitAMOUNT(hotelApplyTripStandard.getUnLimitedType())) {
            vo.getRcDetail().add(buildHotelTravelDetail("可订价格：", "不限", "不管控"));
        } else {
            StandardAmountSyncRequest amount = hotelApplyTripStandard.getStandardAmount();
            String name = ControlTypeEnum.getEnumByCode(amount.getControlType()).getName();
            vo.getRcDetail().add(buildHotelTravelDetail("可订价格：", name, "￥" + amount.getPriceUpperLimit() + "/间夜"));
            if (StringUtils.isNotBlank(name)) {
                HotelForAppVo hotelForAppVo = new HotelForAppVo();
                hotelForAppVo.setRcDetail(Arrays.asList(buildHotelTravelDetail("超标管控方式：", name, name)));
                voList.add(hotelForAppVo);
            }
        }
        return voList;
    }

    private HotelForAppVo buildNoHotelTravelStandardDetail() {
        HotelForAppVo vo = new HotelForAppVo();
        List<RcDetailVo> rcDetailVos = new ArrayList<>();
        rcDetailVos.add(buildHotelTravelDetail("适用城市：", "不管控", "不限"));
        rcDetailVos.add(buildHotelTravelDetail("可订星级：", "不管控", "不限"));
        vo.setRcDetail(rcDetailVos);
        return vo;
    }



    private RcDetailVo buildHotelTravelDetail(String title, String controlMode, String content) {
        RcDetailVo rcDetailVo = new RcDetailVo();
        rcDetailVo.setTitle(title);
        rcDetailVo.setControlMode(controlMode);
        rcDetailVo.setContent(content);
        return rcDetailVo;
    }


    /**
     * 酒店随心订提示
     *
     * @param initPageRequest
     * @return
     */
    private Boolean getPayTips(InitPageRequestVO initPageRequest) {
        UserInfo userInfo = initPageRequest.getUserInfo();
        OrgEmployeeVo employeeInfo = organizationEmployeeClientLoader.findEmployeeInfoByUid(userInfo.getUid(), userInfo.getOrgId());
        return employeeInfo.getIsReadPayTips();
    }

    private List<PayInfoResponse> getPayInfo(InitPageRequestVO initPageRequest) {
        PayInfoRequest request = new PayInfoRequest();
        request.setOrgId(initPageRequest.getUserInfo().getOrgId());
        request.setTransport("hotel");
        request.setName(initPageRequest.getCorpPayType());
        return switchClientLoader.getUserPayInfo(request);
    }

    private List<HotelForAppVo> getHotelForAppVo(InitPageRequestVO initPageRequest) {
        QueryApplyTripStandardResponse hotelApplyTripStandard = applyTripClientLoader.getHotelApplyTripStandard(initPageRequest.getTrafficId());
        if (Objects.isNull(hotelApplyTripStandard)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.APPLY_CONTROL_IS_NULL);
        }
        if (BooleanUtils.isTrue(hotelApplyTripStandard.getEnable())) {
            return getHotelForAppVoToApply(hotelApplyTripStandard);
        }
        HotelTravelForAppRequestBO hotelTravelForAppRequest = new HotelTravelForAppRequestBO();
        UserInfo userInfo = initPageRequest.getUserInfo();
        String policyId = StringUtils.isBlank(initPageRequest.getPolicyId()) ? userInfo.getUid() : initPageRequest.getPolicyId();
        hotelTravelForAppRequest.setUid(policyId);
        String policyOrgId = StringUtils.isBlank(initPageRequest.getPolicyOrgId()) ? userInfo.getOrgId() : initPageRequest.getPolicyOrgId();
        hotelTravelForAppRequest.setOrgId(policyOrgId);
        hotelTravelForAppRequest.setCityCode(initPageRequest.getCityCode());
        hotelTravelForAppRequest.setCityName(initPageRequest.getCity());
        hotelTravelForAppRequest.setPostId(initPageRequest.getPostId());
        hotelTravelForAppRequest.setProductType(ProductTypeEnum.hotel.name());
        hotelTravelForAppRequest.setCheckInTime(StringUtils.isBlank(initPageRequest.getSDate())?null: DateUtils.parse(initPageRequest.getSDate(), DateUtils.DATE_FORMAT));
        hotelTravelForAppRequest.setCheckOutTime(StringUtils.isBlank(initPageRequest.getEDate())?null: DateUtils.parse(initPageRequest.getEDate(), DateUtils.DATE_FORMAT));
        List<HotelForAppVo> hotelTravelForApp = travelStandardPostClientLoader.getHotelTravelForApp(hotelTravelForAppRequest);
        // 如果产线开关未开，则直接进行管控
        if (CollectionUtils.isNotEmpty(hotelTravelForApp) && "N".equals(hotelTravelForApp.get(0).getHotelProductSwitch())){
            return hotelTravelForApp;
        }
        if(ObjectUtil.isEmpty(initPageRequest.getTravelStandardToken())) {
            return hotelTravelForApp;
        }

        // 开启同住继续处理同住流程
        return multiplayerLogic(initPageRequest, hotelTravelForApp);
    }

    private List<HotelForAppVo> getHotelForAppVoToApply(QueryApplyTripStandardResponse hotelApplyTripStandard) {
        return buildHotelTravelStandardShowByApply(hotelApplyTripStandard);
    }


    /**
     * 处理同住逻辑
     *
     * @param initPageRequest
     * @param hotelTravelForApp
     * @return
     */
    private List<HotelForAppVo> multiplayerLogic(InitPageRequestVO initPageRequest, List<HotelForAppVo> hotelTravelForApp) {
        HotelControlVo allDifferentialPrice = null;
        String travelStandardToken = initPageRequest.getTravelStandardToken();
        if(StrUtil.isNotBlank(travelStandardToken)){
            allDifferentialPrice = travelStandardService.getHotelControlVoByToken(travelStandardToken);
        } else {
            GetAllDifferentialPriceRequest getAllDifferentialPriceRequest = new GetAllDifferentialPriceRequest();
            getAllDifferentialPriceRequest.setCityId(initPageRequest.getCityCode());
            getAllDifferentialPriceRequest.setEDate(initPageRequest.getEDate());
            getAllDifferentialPriceRequest.setSDate(initPageRequest.getSDate());
            getAllDifferentialPriceRequest.setPolicy(initPageRequest.getPolicyId());
            getAllDifferentialPriceRequest.setPolicyOrgId(initPageRequest.getPolicyOrgId());
            getAllDifferentialPriceRequest.setHotelControl(new HotelControlVo());


            List<GetAllDifferentialPriceRequest.Room> rooms = new ArrayList<>();

            if(CollectionUtils.isNotEmpty(initPageRequest.getRooms())){
                for (InitPageRequestVO.Room room : initPageRequest.getRooms()) {
                    rooms.add(new GetAllDifferentialPriceRequest.Room(room.getRoomNumber(), ListUtils.copyList(room.getResidentList(), GetAllDifferentialPriceRequest.Resident.class)));
                }
            }

            getAllDifferentialPriceRequest.setRooms(rooms);

            allDifferentialPrice = organizationClientLoader.getAllDifferentialPrice(getAllDifferentialPriceRequest, ObjectUtils.copyProperties(initPageRequest.getUserInfo(), BaseUserInfo.class));

        }
        log.info("查询到同住差标为:{}", JsonUtils.toJsonString(allDifferentialPrice));
        // 如果没有token,并且同住差标为null,说明是旧页面+没有token,则直接返回
        if(ObjectUtil.isEmpty(allDifferentialPrice) && StringUtils.isBlank(travelStandardToken)){
            return hotelTravelForApp;
        }
        HotelForAppVo hotelForAppVo = hotelTravelForApp.stream().collect(Collectors.toList()).get(0);
        List<RcDetailVo> rcDetailVos = Lists.newArrayList();
        for (RcDetailVo rcDetailVo : hotelForAppVo.getRcDetail()) {

            if (rcDetailVo.getTitle().equals("可订价格")) {
                if (allDifferentialPrice==null || ((allDifferentialPrice.getAveragePriceSet()==null
                        || allDifferentialPrice.getAveragePriceSet().getPriceCeiling()==null
                        || Double.valueOf(allDifferentialPrice.getAveragePriceSet().getPriceCeiling()) == 0D) &&
                        (allDifferentialPrice.getOffPeakSeasonSet()==null
                                || allDifferentialPrice.getOffPeakSeasonSet().getPriceCeiling()==null
                                || Double.valueOf(allDifferentialPrice.getOffPeakSeasonSet().getPriceCeiling()) == 0D))) {
                    rcDetailVo.setContent("不限");
                } else {
                    if(ObjectUtil.isNotNull(allDifferentialPrice.getOffPeakSeasonSet())){ // 优先取淡旺季的价格
                        rcDetailVo.setContent("￥" +allDifferentialPrice.getOffPeakSeasonSet().getPriceCeiling() + "/间夜");
                    }else{
                        rcDetailVo.setContent("￥" +allDifferentialPrice.getAveragePriceSet().getPriceCeiling() + "/间夜");
                    }
                }
                rcDetailVos.add(1, rcDetailVo);
            } else if (rcDetailVo.getTitle().equals("可订星级")) {
                if (allDifferentialPrice==null || allDifferentialPrice.getAveragePriceSet()==null  || ObjectUtil.isNull(allDifferentialPrice.getAverageStarSet())
                        || allDifferentialPrice.getAverageStarSet().getStarCeiling() == null || Objects.equals("0", allDifferentialPrice.getAverageStarSet().getStarCeiling().getValue())
                        || StringUtils.isBlank(allDifferentialPrice.getAverageStarSet().getStarCeiling().getValue())) {
                    rcDetailVo.setContent(STAR_NAMES[0]);
                } else {
                    rcDetailVo.setContent(STAR_NAMES[Integer.valueOf(allDifferentialPrice.getAverageStarSet().getStarCeiling().getValue())] + "及以下");
                }
                rcDetailVos.add(2, rcDetailVo);
            } else {
                rcDetailVos.add(0, rcDetailVo);
            }

        }
        hotelForAppVo.setRcDetail(rcDetailVos);
        return hotelTravelForApp;
    }

    private List<HotelFilterListVo> getHotelFilters(InitPageRequestVO initPageRequest) {
        return hotelFilterListByCityUseNewData(initPageRequest.getCityCode(), true);
    }

    private HotelControlVo getHotelControlVo(InitPageRequestVO initPageRequest) {
        String travelStandardToken = initPageRequest.getTravelStandardToken();
        // 如果差标不为空就从差标差标中获取控制信息
        if(StrUtil.isNotBlank(travelStandardToken)){
            return travelStandardService.getHotelControlVoByToken(travelStandardToken);
        }

        QueryApplyTripStandardResponse hotelApplyTripStandard = applyTripClientLoader.getHotelApplyTripStandard(initPageRequest.getTrafficId());
        if (Objects.isNull(hotelApplyTripStandard)) {
            throw new CorpBusinessException(HotelResponseCodeEnum.APPLY_CONTROL_IS_NULL);
        }
        if (BooleanUtils.isTrue(hotelApplyTripStandard.getEnable())) {
            return getHotelControlByApply(hotelApplyTripStandard);
        }
        JSONResult<HotelControlVo> hotelDetail;
        GetHotelDetailRequest request = new GetHotelDetailRequest();
        request.setStartDate(DateUtil.stringToDate(initPageRequest.getSDate(), DateUtil.DF_YMD));
        request.setEndDate(DateUtil.stringToDate(initPageRequest.getEDate(), DateUtil.DF_YMD));
        request.setCityCode(initPageRequest.getCityCode());
        if (StringUtils.isNotBlank(initPageRequest.getPolicyId())) {
            request.setUid(initPageRequest.getPolicyId());
            request.setOrgId(initPageRequest.getPolicyOrgId());
            hotelDetail = travelStandardPostService.getHotelDetail(request);
        } else {
            request.setUid(initPageRequest.getUserInfo().getUid());
            request.setOrgId(initPageRequest.getUserInfo().getOrgId());
            hotelDetail = travelStandardPostService.getHotelDetail(request);
        }
        if (hotelDetail == null || !hotelDetail.isSUCCESS()) {
            throw new CorpBusinessException(HotelResponseCodeEnum.EXCEPTION_QUERY_HOTEL_DETAILS,hotelDetail.getMsg());
        }
        return hotelDetail.getData();
    }

    private HotelControlVo getHotelControlByApply(QueryApplyTripStandardResponse hotelApplyTripStandar) {
        if (TravelUnLimitedTypeEnum.isUnLimitAMOUNT(hotelApplyTripStandar.getUnLimitedType())) {
            return HotelControlVo.getNotLimitVo();
        }
        StandardAmountSyncRequest amount = hotelApplyTripStandar.getStandardAmount();
        HotelControlVo limitVo = HotelControlVo.getNotLimitVo();
        AveragePriceSet averagePriceSet = new AveragePriceSet();
        averagePriceSet.setPriceCeiling(amount.getPriceUpperLimit().toString());
        averagePriceSet.setPriceFloor("0");
        limitVo.setAveragePriceSet(averagePriceSet);
        limitVo.setControl(ControlTypeEnum.getEnumByCode(amount.getControlType()).getValue());
        return limitVo;
    }

    /**
     * 求申请单校验信息
     * @param trafficId
     * @return
     */
    public ApplyTripTrafficVerifyResponse getApplyTripTrafficVerify(Long trafficId){
        String key = "verifyInfo:"+trafficId;
        ApplyTripTrafficVerifyResponse response = cacheHandler.getBigValue(key, ApplyTripTrafficVerifyResponse.class);
        if (response==null){
            response = applyTripClientLoader.getApplyTrafficVerify(trafficId);
            cacheHandler.setValue(key, response);
            return response;
        }

        // 库存实时查
        AoApplyTripStockVo applyTripStockVo = applyTripClientLoader.getApplyTrafficStock(trafficId);
        response.setApplyTripStock(applyTripStockVo);
        return response;
    }

    /**
     * 入离时间显示
     * @param startDate
     * @param returnDate
     * @return
     */
    public String getDateMassage(String startDate, String returnDate) {
        if(StringUtils.isBlank(startDate) &&  StringUtils.isBlank(returnDate)) {
          return "入离日期不限";
        }
        String start = "入住日期不限";
        if(StringUtils.isNotBlank(startDate)) {
            start = DateUtil.dateToString(DateUtil.stringToDate(startDate, DateUtil.DF_YMD), DateUtil.DF_YMD_DOT);
        }
        String endDate = "离店日期不限";
        if(StringUtils.isNotBlank(returnDate)) {
            endDate = DateUtil.dateToString(DateUtil.stringToDate(returnDate, DateUtil.DF_YMD), DateUtil.DF_YMD_DOT);
        }
        return start + "-" + endDate;
    }


}
