package com.corpgovernment.basic.constant;

import com.ctrip.corp.obt.generic.exception.ResponseStatus;

public enum HotelResponseCodeEnum implements ResponseStatus {
    UN("未知", 99999),
    ERROR_PARAM("%s", 99998),
    SUCCESS_CODE("成功", 0),
    USER_NOT_LOGGER("用户未登录", 52000),
    COST_CENTER_IS_NULL("出行人：[%s] 成本中心为空", 52001),
    COST_CENTER_NAME_IS_NULL("出行人：[%s] 成本中心名称为空", 52002),
    COST_CENTER_ID_IS_NULL("出行人：[%s] 成本中心ID为空", 52003),
    PROJECT_IS_NULL("出行人：[%s] 未填写项目号,如不需要选择项目请填写不选择项目原因", 52004),
    PROJECT_ID_IS_NULL("出行人：[%s] 缺少项目号ID", 52005),
    PASSENGER_LIST_IS_NULL("出行人信息不能为空", 52006),
    APPLY_CONTROL_IS_NULL("出差申请单差标查询失败", 52007),
    CTRIP_TICKET_GET_FAIL("获取携程ticket失败", 52008),
    ORDER_DETAIL_IS_NULL("酒店订单详情为空", 52009),
    ORDER_ID_IS_NULL("订单号不能为空", 52010),
    ORDER_IS_NULL("该订单不存在", 52011),
    SYSTEM_ERROR("请求失败,请稍后重试", 52012),
    QUERY_CITY_IS_EMPTY("查询城市为空", 52013),
    THE_MOBILE_PHONE_NUMBER_OR_COUNTRY_CODE_OF_THE_GUEST_IS_EMPTY("需发短信的入住人手机号码或者手机号国家码不能为空", 52014),
    GUEST_INFORMATION_IS_EMPTY("入住人信息为空", 52015),
    DAILY_ROOM_RATE_IS_EMPTY("每日房型价格为空", 52016),
    THE_ROOM_TYPE_LIST_IS_EMPTY("房型列表为空", 52017),
    CHECK_IN_TIME_IS_EMPTY("入住时间不能为空", 52018),
    CHECK_OUT_TIME_IS_EMPTY("离店时间不能为空", 52019),
    NEED_TO_BE_RESERVED_ROOM_CAN_NOT_BE_EMPTY("需要保留的房间不能为空", 52020),
    OVERSTANDARD_CONFIGURATION_INFORMATION_IS_EMPTY("超标配置信息为空", 52021),
    SUPPLIER_ORDER_DETAILS_ARE_EMPTY("服务商订单详情为空", 52022),
    SUPPLIER_ORDER_DOES_NOT_EXIST("供应商订单不存在", 52023),
    EXCEPTION_INSERT_INVOICE("插入发票异常", 52024),
    EXCEPTION_QUERY_HOTEL_DETAILS("%s", 52025),
    EXCEPTION_QUERY_ORDER_DETAILS("%s", 52026),
    FAILED_MEIYA_CREATE_ORDER("创单失败，失败原因为：%s", 52027),
    FAILED_OBTAIN_HOTEL_CITY("获取酒店城市失败", 52028),
    FAILED_QUERY_THE_MANAGETMENT_MODE("查询管控方式失败", 52029),
    FAILED_OBTAIN_SUPPLIER_URL("获取供应商URL失败", 52030),
    FAILED_OBTAIN_SUPPLIER_INFORMATION("获取供应商信息失败", 52031),
    FAILED_CALL_SUPPLIER_INTERFACE("平台订单已取消，调用供应商取消接口失败", 52032),
    FAILED_OBTAIN_PAY_INFORMATION("获取支付信息失败", 52033),
    FAILED_OBTAIN_PAYMENT_ORDER_STATUS("支付获取订单状态失败", 52034),
    FAILED_RECORD_ORDER("补录订单失败", 52035),
    FAILED_RECORD_REPEATED_RECORD("重复补录，补录失败", 52036),
    FAILED_OBTAIN_SUBSCRIBER_INFORMATION("获取预订人信息失败", 52037),
    FAILED_OBTAIN_ORDER_NUMBER("获取订单号失败", 52038),
    FAILED_OBTAIN_REQUEST_MODIFICATION_ORDER_DETAILS("获取申请修改订单详情失败", 52039),
    FAILED_OBTAIN_SERVICE_CHARGE("获取服务费失败", 52040),
    FAILED_OBTAIN_GLOBAL_PAY_TIME("获取全局支付时间失败", 52041),
    FAILED_OBTAIN_USER_INFORMATION("获取用户信息失败", 52042),
    FAILED_ORDER("下单失败，入住人数大于最大可入住人数", 52043),
    FAILED_OBTAIN_INVOICE_INFORMATION("获取发票信息失败", 52044),
    FAILED_OBTAIN_INVOICE_TITLE_TYPE("获取发票标题类型失败", 52045),
    FAILED_OBTAIN_INVOICE_TYPE("获取发票类型失败", 52046),
    FAILED_CREATE_THE_APPROVAL_ORDER("创建审批单失败", 52047),
    FAILED_START_APPROVAL("开始审批失败", 52048),
    FAILED_OBTAIN_APPROVAL_INFORMATION("获取审批信息失败", 52049),
    FAILED_CONSUME_TRAVEL_APPLICATION("消耗出差申请失败", 52050),
    FAILED_RESTORE_TRAVEL_APPLICATION("还原出差申请失败", 52051),
    QUERY_HOTEL_STATIC_DATA_DO_NOT_FIND_DATA_SOURCE("查询酒店静态数据列表未找到数据源配置", 52052),
    PLEASE_SELECT_A_NEW_CITY("请重新选择城市", 52053),
    REFILL_ORDER_CACHE_EXPIRED("补录订单缓存已过期", 52054),
    CANCELLATION_AMOUNT_LESS_THAN_TOTAL_AMOUNT("取消金额不能大于总金额", 52055),
    HOTEL_BOOKING_REQUEST_FOR_CHECK_OUT_CAN_NOT_BE_FOUND("申请提起离店的酒店订单未找到", 52056),
    CHECK_IN_TIME_IS_SAME_AS_CHECK_OUT_TIME("入住时间不能和离店时间为同一天", 52057),
    CHECK_IN_TIME_CAN_NOT_BE_LONGER_THAN_CHECK_OUT_TIME("入住时间不能大于离店时间", 52058),
    FREQUENT_OPERATION_PLEASE_RE_CHECK_THE_HOTEL_LIST("操作频繁，请重新查询酒店列表", 52059),
    OVERSTANDARD_FORBID_RESERVATION("超标禁止预定", 52060),
    THE_RESERVATION_IS_OVERSTANDARD_AND_LACK_AN_APPROVER("您的预订超过标准，缺少审批人，请联系管理员配置", 52061),
    THE_CURRENT_HOTEL_TEMPORARILY_NO_BOOKING_ROOM("查询服务商详情接口超时", 52062),
    URBAN_BASIC_DATA_IS_MISSING("该城市基础数据缺失，请联系管理员处理", 52063),
    STAY_TOO_LONG_PLEASE_CHECK_THE_PRICE_AGAIN("您停留时间过长，请重新查询价格", 52064),
    GET_SUPPLIER_CONFIG_METHOD_EXCEPTION("searchHotelProcessAbstractService.getSupplierCofig方法异常：%s", 52065),
    NEW_GET_SUPPLIER_CONFIG_METHOD_EXCEPTION("newSearchHotelProcessAbstractService.getSupplierCofig方法异常：%s", 52066),
    DO_NOT_MEET_TRAVEL_CRITERIA("不符合差旅标准", 52067),
    QUERY_SUPPLIER_DATA_FAIL("查询供应商基础信息异常", 52068),
    QUERY_PARAM_ERROR("请求参数错误", 52069),
    CITY_SELECTED_ERROR("城市选择错误", 52070),
    FUZZY_SEARCH_SUPPLIER_IS_NOT_CONFIGURED("模糊搜索服务商未配置", 52071),
    GET_SUPPLIER_URL_ERROR("获取供应商URL异常", 52072),
    GET_SUPPLIER_CITY_INFO_ERROR("获取供应商城市信息异常", 52073),
    ENTERPRISE_SUBSCRIPTION_SERVICE_EXPIRES("企业订购业务过期", 52074),
    SAVE_ORDER_STATRT_ERROR("开始下单请稍等...", 52075),
    SAVE_ORDER_DOING_ERROR("正在下单请稍等...", 52076),
    SAVE_ORDER_CREATE_ERROR("下单失败，请稍后再试", 52077),
    NO_APPROVAL_REPEAT_SUBMIT_ERROR("缺少审批流将无法提交订单，仅支持查询酒店信息", 52132),
    SAVE_ORDER_REPEAT_ERROR("重复提交订单，下单失败", 52078),
    SAVE_ORDER_DATA_ERROR("保存下单数据失败，请稍后再试", 52079),
    SAVE_ORDER_CREATE_TIMEOUT_ERROR("下单超时，请稍后再试", 52080),
    SAVE_ORDER_SUPPLIER_ERROR("供应商创单失败，请稍后再试", 52081),
    SEND_MESSAGE_PARAM_ERROR("发送消息事件参数非法", 52082),
    MODIFY_APPLY_DONT_EXISTS("修改单申请不存在", 52083),
    APPLY_MODIFY_STATUS_PUSH_FAILED("酒店修改状态推送处理失败", 52084),
    OPTIONAL_CITY_ERROR("所选城市存在问题", 52085),
    GET_TIME_ZONE_ERROR("获取时区失败", 52086),
    CITY_ID_IS_NULL("查询城市不能为空", 52087),
    CTRIP_SEARCH_KEYWORD_URL_IS_NULL("关键词搜索URL为空", 52088),
    CTRIP_SEARCH_KEYWORD_FAIL("供应商请求失败", 52089),
    FUZZY_SEARCH_ERROR("模糊搜索发生异常",59407),

    HOTELINTL_EXCEPTION_ERROR("服务器繁忙，请稍后再试", 52999),
    URGENT_APPLY_APPROVAL_NOT_SETUP("无可用的审批流，不可选择紧急预订", 52082),
    URGENT_APPLY_DISABLE("紧急预订功能未开放，请联系管理员", 52083),
    HOTEL_CANCEL_FAILED("订单取消失败，请稍后重试或联系客服", 52084),
    HOTEL_CANCEL_NEED_SUPPLIER_CONFIRM("订单已提交取消申请,待供应商端确认", 52085),
    HOTEL_CHECK_AVAIL_PRICE_EMPTY("供应商变价信息", 52086),
    FAILED_SERVICE_CHARGE("获取服务费失败", 52086),
    FAILED_CHECK_HOTEL_ROOM("校验酒店房间是否超标失败", 52087),
    FAILED_CHECK_HOTEL_ROOM_FEGIN("调用fegin校验酒店房间超标接口异常", 52088),
    SAVE_ORDER_PARAMS_ERROR("下单参数错误", 52089),
    GET_TRAVEL_ATTRIBUTE_ERROR("获取差旅配置失败", 52090),
    RPC_ERROR("远程调用异常", 52091),
    LOGIN_USER_PHONE_IS_ERROR("登陆人手机信息异常", 52092),
    HOTEL_CARD_ERROR("请输入正确会员卡号", 52093),
    HOTEL_CARD_IS_EXIST("用户会员卡信息已存在", 52094),
    GET_MASTER_GROUP_ID_ERROR("获取主groupId异常", 52095),
    CANCEL_QUERY_FAILED("订单取消问询失败,请稍后重试", 52096),
    CANCEL_DETAIL_FAILED("订单取消详情失败,请稍后重试", 52097),
    ORDER_CONFIRM_FAILED("订单确认失败,请稍后重试", 52098),
    QUERY_ORDER_MODIFICATION_FAILED("订单修改问询失败,请稍后重试", 52099),
    CREATE_ORDER_MODIFICATION_FAILED("订单修改失败，请稍后重试或联系客服", 52100),
    ORDER_MODIFICATION_DETAILS_FAILED("查询订单修改详情失败,请稍后重试", 52101),
    QUERY_ORDER_FAILED_BY_SUPPLIER_ORDER_ID("供应商单号查询订单信息失败", 52102),
    ORDER_EXCEED_STANDARD("当前支付方式不支持，请重新选择", 52103),

    ILLEGAL_TRAVEL_STANDARD("当前差标不合法", 521031),
    TOKEN_ACCESS_ERROR("接入token失败", 52104),

    HOTEL_DETAIL_GET_ERROR("酒店详情获取异常", 52104),
    STAY_TIME_CHECK_FAIL("停留时间校验未通过", 52105),
    SUPPLIER_PRODUCT_GET_ERROR("供应商产品获取异常", 52106),
    TRAVEL_STANDARD_GET_ERROR("差标获取异常", 52107),
    PRICE_CONTROL_TAKE_EFFECT("价格管控生效", 52108),
    STAR_CONTROL_TAKE_EFFECT("星级管控生效", 52109),
    TRAVEL_STANDARD_VERIFY_ERROR("差标校验失败", 52110),
    APPLY_TRIP_GET_ERROR("出差申请单获取异常", 52111),

    BIZ_TYPE_ENUM_GET_ERROR("产线获取异常", 52112),
    SHIELD_STAR_CONTROL_GET_ERROR("屏蔽星级管控获取异常", 52113),
    HOTEL_LIST_SORT_RULE_CONTROL_GET_ERROR("酒店列表排序规则管控获取异常", 52114),
    SUPPLIER_CONFIG_GET_ERROR("供应商配置获取异常", 52115),
    AVG_PRICE_CONTROL_GET_ERROR("间夜均价管控获取异常", 52116),
    HOTEL_ROOM_SORT_RULE_CONTROL_GET_ERROR("酒店房型排序规则管控获取异常", 52117),
    TRAVEL_APPLICATION_GET_ERROR("出差申请获取异常", 52118),
    SIGNAL_TRAVEL_STANDARD_GET_ERROR("单差标获取异常", 52119),
    MULTI_TRAVEL_STANDARD_GET_ERROR("多差标获取异常", 52120),
    BOOK_TRAVEL_STANDARD_IS_FORBID("预订差标被禁止", 52122),
    CONTROL_TAKE_EFFECT("差标与其他筛选条件冲突，请更换条件再试！", 52123),
    HOTEL_LIST_META_DATA_GET_ERROR("酒店列表元数据获取异常", 52124),
    HOTEL_CUSTOM_SERVICE_GET_ERROR("酒店定制服务获取异常", 52125),
    TRAVEL_CONFIG_GET_ERROR("差旅配置获取异常", 52126),
    CLOCK_NOT_IN_TIME("不在打卡时间范围内",52127),
    CLOCK_NOT_IN_RANGE("当前位置不在酒店%skm范围内，不可进行打卡",52128),
    CLOCK_CONCURRENT_ERROR("打卡失败，请稍后再试", 52129),
    ALREADY_CLOCK_ERROR("已打卡成功，请勿重试", 52130),
    CLOCK_OUT_NEED_CLOCK_IN("未进行入住打卡，不可进行离店打卡", 52131),
    CLOCK_NOT_ENABLE("酒店打卡功能未启用", 52132),

    REDIS_LOCK_FAILED("获取锁失败", 52097),

    REDIS_LOCK_EXCEPTION("获取锁异常", 52098),
    CONTRACT_EMAIL_FORMAT_ERROR_TIP("邮箱格式有误，请重新输入", 52099),
    TEMP_COST_CENTER_IS_NULL("出行人：[%s] [%s]为空", 52100),

    REQUEST_PARAM_NULL("请求参数[%s]不能为空", 52100),
    GET_FEEINFO_SNAPSHOT_EXCEPTION("通过快照获取酒店费用信息接口异常", 52101),
    GET_HOTELPRODUCT_SNAPSHOT_EXCEPTION("根据快照获取酒店信息接口异常", 52102),
    GET_SNAPSHOT_EXCEPTION("根据快照获取酒店信息接口异常", 52103),
    BUILD_ORDERINFO_EXCEPTION("构建订单信息模型对象都不能为空", 52104),
    PASSENGER_EMAIL_IS_NULL("请维护入住人的邮箱", 52101),
    PASSENGER_PASSPORT_IS_NULL("请维护入住人的证件号", 52102),
    PAYMENTTYPE_CAN_NOT_BE_EMPTY("支付方式必须为ONLINE/CASH", 52103),
    SAVE_ORDER_REPEAT_BOOKING("存在重复订单", 52140),
    SAVE_ORDER_GET_REPEAT_ORDER_ERROR("重复订单获取失败", 52141),
    
    CONTROL_NON_INTERSECTION("差标无交集，请更换条件再试！", 52142),

    FORCE_CHUMMAGE_NO_RC_INFO("强制合住没有RC信息", 52143),
    FORCE_CHUMMAGE("强制合住", 52144),
    APPLY_TRIP_FORM_NOT_EXIST("不存在出差申请单", 52145),
    ACCOUNTING_UNIT_CATEGORY_CODE_NULL("出行人:[%s] 核算单元数据有误， 核算单元分类编码不能为空", 52146),
    ACCOUNTING_UNIT_CATEGORY_CODE_NOT_EXIST("出行人:[%s] 核算单元数据有误， 核算单元分类编码不存在或无效", 52147),
    ACCOUNTING_UNIT_CATEGORY_NAME_NULL("出行人:[%s] 核算单元数据有误， 核算单元分类名称不能为空", 52148),
    ACCOUNTING_UNIT_CODE_NULL("出行人:[%s] 核算单元数据有误， 核算单元编码不能为空", 52149),
    ACCOUNTING_UNIT_CODE_NOT_EXIST("出行人:[%s] 核算单元数据有误， 核算单元编码不存在或在无效状态:[%s]", 52150),
    ACCOUNTING_UNIT_NAME_NULL("出行人:[%s] 核算单元数据有误， 核算单元名称不能为空", 52151),
    ACCOUNTING_UNIT_CATEGORY_REQUIRED_ERROR("出行人:[%s] 核算单元数据有误， []必填", 52152),

    MODIFIABLE_ROOM_NIGHT_QUERY_FAILED("订单可修改间夜查询失败,请稍后重试", 52200),
    NOT_SUPPORT_OPERATION("不支持的操作", 52201),

    ORDER_INFO_CHECK_FAIL("订单信息校验失败", 52153),
    GET_APPLY_TRIP_CONTROL_CONFIG_ERROR("出差申请单管控配置获取失败，请联系管理员检查配置", 52154),
    APPROVE_USER_NO_SELECTED("审批节点[%s]未选择审批人", 52153),
    FUZZY_SEARCH_DESTINATION_CONFIG_ERROR("模糊搜索目的地配置有误", 52155),

    RISK_CONTROL_CHECK_ERROR("您的账号存在异常风险，暂时无法预订，请联系公司差旅负责人处理", 53000),


    NO_PERMISSION_ACCESS_RESOURCE("代订权限校验不通过", 52154),


    ;

    private String message;
    private int code;

    HotelResponseCodeEnum(String message, int code) {
        this.message = message;
        this.code = code;
    }

    @Override
    public int code() {
        return code;
    }

    @Override
    public String message() {
        return message;
    }

    public static HotelResponseCodeEnum findByCode(int code) {
        for (HotelResponseCodeEnum item : HotelResponseCodeEnum.values()) {
            if (item.code() == code) {
                return item;
            }
        }
        return UN;
    }
}