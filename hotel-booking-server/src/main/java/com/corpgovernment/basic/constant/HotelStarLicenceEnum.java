package com.corpgovernment.basic.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 酒店是星级/钻级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum HotelStarLicenceEnum {
    YES(1, true, "T", "星级"),
    NO(0, false,  "F", "钻级"),
    ;
    // 标准code
    private int code;
    // 供应商接口的值
    private boolean licence;

    private String flag;
    // 标签
    private String label;

    private static Map<String, HotelStarLicenceEnum> map = new HashMap<>();
    static {
        for(HotelStarLicenceEnum licenceEnum: HotelStarLicenceEnum.values()){
            map.put("code:"+licenceEnum.getCode(), licenceEnum);
            map.put("licence:"+licenceEnum.isLicence(), licenceEnum);
            map.put("licence:"+licenceEnum.getFlag(), licenceEnum);
        }
    }

    public static HotelStarLicenceEnum getByCode(Integer code){
        if(code==null){
            return null;
        }
        return map.get("code:"+code);
    }

    public static HotelStarLicenceEnum getByLicence(Object licence){
        if(licence==null){
            return HotelStarLicenceEnum.NO;
        }
        HotelStarLicenceEnum licenceEnum = map.get("licence:"+licence);
        return licenceEnum==null? HotelStarLicenceEnum.NO:licenceEnum;
    }

}
