package com.corpgovernment.basic.constant;

import lombok.Getter;

/**
 * 酒店品牌筛选类型枚举
 *
 * <AUTHOR>
 * @since 2023/6/5
 */
public enum HotelFilterBrandEnum {
    HIGH("1", "高端连锁"),
    MID("2", "中端连锁"),
    FAST("3", "快捷连锁"),
    OTHER("4", "其他品牌"),
    ;
    @Getter
    private final String key;
    @Getter
    private final String belongName;

    HotelFilterBrandEnum(String key, String belongName) {
        this.key = key;
        this.belongName = belongName;
    }
}
