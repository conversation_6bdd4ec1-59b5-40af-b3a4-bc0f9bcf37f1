package com.corpgovernment.basic.controller;

import com.corpgovernment.api.basic.vo.SearchCityVo;
import com.corpgovernment.api.hotel.product.model.request.InitPageRequestVO;
import com.corpgovernment.api.hotel.product.model.response.HotelConditionResponse;
import com.corpgovernment.api.hotel.product.vo.HotelRequestVo;
import com.corpgovernment.basic.service.IHotelBasicDataService;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: HotelBasicDataController
 * @description: 酒店基础数据
 * @author: yssong
 * @date: Created in 10:03 2019/8/16
 * @Version: 1.0
 **/

@RestController
@RequestMapping(value = "/hotelBasicDataSoa")
@Slf4j
public class HotelBasicDataController {

    @Autowired
    private IHotelBasicDataService hotelBasicDataService;

    /**
     * 酒店所有城市
     */
    @RequestMapping("/allHotelCity")
    public JSONResult allHotelCity() {
        return JSONResult.success(hotelBasicDataService.allHotelCity());
    }

	/**
	 * 酒店全量过滤列表
	 */
	@RequestMapping("/filterList")
	public JSONResult hotelFilterListByCity(@RequestBody HotelRequestVo request, BaseUserInfo userInfo) {
		request.setBaseUserInfo(userInfo);
		return JSONResult.success(hotelBasicDataService.hotelFilterListByCity(request.getCity(), userInfo.getCorpId(), false));
	}

    /**
     * 模糊查询酒店城市信息
     */
    @RequestMapping("searchHotelCity")
    public JSONResult searchHotelCity(@RequestBody SearchCityVo vo) {
        return JSONResult.success(hotelBasicDataService.searchHotelCityBySearchKey(vo.getKey(), vo.getCountryId(),vo.getDomestic()));
    }

    /**
     * 条件筛选
     */
    @RequestMapping("/conditions")
    public JSONResult<HotelConditionResponse> conditions(@RequestBody InitPageRequestVO request) {
		return JSONResult.success(hotelBasicDataService.conditions(request));
    }

}
