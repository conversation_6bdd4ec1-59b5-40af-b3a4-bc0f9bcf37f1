package com.corpgovernment.basic.bo.response;

import lombok.Data;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: HotelSearchResponseBo
 * @description: TODO
 * @author: yssong
 * @date: Created in 14:28 2019/9/10
 * @Version: 1.0
 **/
@Data
public class HotelSearchResponseBo implements Serializable {
    /**
     * 错误码（0或空表示正确）
     */
    private String errorCode;
    /**
     * 结果描述
     */
    private String message;
    /**
     * 当前城市查询结果
     */
    private List<DestinationInfoListResponseBo> destinationInfoList;

    /**
     * 其他城市查询结果
     */
    private List<OtherCityDestinationListResponseBo> otherCityDestinationList;

    private CtripResponseStatus responseStatus;

    /**
     * 非携程判断成功
     */
    public Boolean isSuccess(){
        return StringUtils.isBlank(errorCode) || "0".equals(errorCode);
    }
}
