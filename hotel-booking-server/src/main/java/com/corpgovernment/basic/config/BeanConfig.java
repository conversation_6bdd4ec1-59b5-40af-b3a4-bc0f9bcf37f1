package com.corpgovernment.basic.config;

import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.common.apollo.HotelApollo;
import com.ctrip.corp.obt.generic.threadpool.core.spring.EnableDynamicTp;
import com.ctrip.corp.obt.generic.threadpool.core.support.DynamicTp;
import com.ctrip.corp.obt.generic.threadpool.core.support.ThreadPoolBuilder;
import com.ctrip.corp.obt.generic.threadpool.core.support.task.wrapper.TaskWrappers;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.ctrip.corp.obt.generic.threadpool.common.em.QueueTypeEnum.ARRAY_BLOCKING_QUEUE;

@Configuration
@EnableAsync
@Slf4j
@EnableDynamicTp
public class BeanConfig {
    @Autowired
    private HotelApollo hotelApollo;

    /**
     * 获取供应商操作类型配置
     *
     * @return
     */
    @Bean
    public HotelOperatorTypeConfig hotelOperatorTypeConfig() {
        HotelOperatorTypeConfig hotelOperatorType = JsonUtils.parse(hotelApollo.getHotelOperatorType(), HotelOperatorTypeConfig.class);
        log.info("注入的酒店点操作类型：{}", JsonUtils.toJsonString(hotelOperatorType));
        return hotelOperatorType;
    }

    /**
     * 数据加载线程池
     */
    @DynamicTp("queryThreadPoolExecutor")
    @Bean(name = "queryThreadPoolExecutor")
    public ThreadPoolExecutor queryThreadPoolExecutor() {
        int corePoolSize = 20;
        int threadQueueSize = 200;
        return ThreadPoolBuilder.newBuilder()
            .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
            .corePoolSize(corePoolSize)
            .maximumPoolSize(corePoolSize * 2)
            .keepAliveTime(1800L)
            .timeUnit(TimeUnit.SECONDS)
            .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
            .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
            .build();
    }
    
    /**
     * 数据加载线程池
     */
    @DynamicTp("queryThreadPoolExecutorV2")
    @Bean(name = "queryThreadPoolExecutorV2")
    public ThreadPoolExecutor queryThreadPoolExecutorV2() {
        int corePoolSize = 20;
        int threadQueueSize = 200;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(corePoolSize * 2)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    /**
     * 基础线程池
     *
     * @return
     */
    @DynamicTp("basicThreadPoolExecutor")
    @Bean(name = "basicThreadPoolExecutor")
    public ThreadPoolExecutor basicThreadPoolExecutor() {
        int corePoolSize = 15;
        int threadQueueSize = 200;
        return ThreadPoolBuilder.newBuilder()
            .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
            .corePoolSize(corePoolSize)
            .maximumPoolSize(corePoolSize * 2)
            .keepAliveTime(1800L)
            .timeUnit(TimeUnit.SECONDS)
            .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
            .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
            .build();
    }

    /**
     * 酒店Mapping线程池
     *
     * @return
     */
    @DynamicTp("hotelMappingThreadPoolExecutor")
    @Bean(name = "hotelMappingThreadPoolExecutor")
    public ThreadPoolExecutor hotelMappingThreadPoolExecutor() {
        int corePoolSize = 5;
        int threadQueueSize = 200;
        return ThreadPoolBuilder.newBuilder()
            .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
            .corePoolSize(corePoolSize)
            .maximumPoolSize(corePoolSize * 2)
            .keepAliveTime(1800L)
            .timeUnit(TimeUnit.SECONDS)
            .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
            .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
            .build();
    }

    /**
     * 日志处理线程池
     *
     * @return
     */
    @DynamicTp("logThreadPoolExecutor")
    @Bean(name = "logThreadPoolExecutor")
    public ThreadPoolExecutor logThreadPoolExecutor() {
        int corePoolSize = 20;
        return ThreadPoolBuilder.newBuilder()
            .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
            .corePoolSize(corePoolSize)
            .maximumPoolSize(corePoolSize * 2)
            .keepAliveTime(1800L)
            .timeUnit(TimeUnit.SECONDS)
            .workQueue(ARRAY_BLOCKING_QUEUE.getName(), 20)
            .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
            .build();
    }
}
