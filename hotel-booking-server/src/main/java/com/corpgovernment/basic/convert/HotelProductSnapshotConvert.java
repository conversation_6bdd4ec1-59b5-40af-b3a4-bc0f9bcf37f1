package com.corpgovernment.basic.convert;

import cn.hutool.core.util.ObjectUtil;
import com.corpgovernment.api.hotel.booking.core.HourlyRoomInfoVo;
import com.corpgovernment.api.hotel.booking.core.MemberTag;
import com.corpgovernment.api.hotel.booking.core.QueryHotelDetailRespVo;
import com.corpgovernment.dto.snapshot.dto.hotel.*;
import com.corpgovernment.dto.snapshot.dto.hotel.request.SaveHotelProductSnapshotRequest;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.EnumUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: wangzs
 * @Date: 2025/3/6
 * @Description:
 */
@Component
@Slf4j
public class HotelProductSnapshotConvert {

    public static final Integer MAX_COUNT = 99999;
    private static final Map<String, MemberTagFlag> SORT_MEMBER_TAG_FLAG_MAP =
            Stream.of(EnumUtils.getEnumMap(MemberLevelTagEnum.class),
                            EnumUtils.getEnumMap(CXLevelTagEnum.class),
                            EnumUtils.getEnumMap(MemberChangeTagEnum.class))
                    .flatMap(map -> map.entrySet().stream())
                    .reduce(new LinkedHashMap<>(),
                            (map, entry) -> {
                                if (!map.containsKey(entry.getKey())) {
                                    map.put(entry.getKey(), entry.getValue());
                                }
                                return map;
                            },
                            (map1, map2) -> {
                                map1.putAll(map2);
                                return map1;
                            });
    /**
     * 将酒店详细信息转换为保存酒店产品快照请求对象
     *
     * @param hotelDetail 酒店详细信息对象
     * @param token       访问令牌
     * @return 保存酒店产品快照请求对象
     */
    public SaveHotelProductSnapshotRequest saveHotelProductSnapshotRequestConvert(QueryHotelDetailRespVo hotelDetail, String token){
        log.info("saveHotelProductSnapshotRequestConvert, hotelDetail:{}", JsonUtils.toJsonString(hotelDetail));
        if(ObjectUtil.isNull(hotelDetail) || StringUtils.isBlank(token)){
            return null;
        }
        SaveHotelProductSnapshotRequest hotelProduct = new SaveHotelProductSnapshotRequest();
        hotelProduct.setToken(token);
        hotelProduct.setHotelInfo(hotelInfoSnapshotConvert(hotelDetail.getHotelBaseInfo()));
        hotelProduct.setBasicRoomInfo(basicRoomInfoSnapshotConvert(hotelDetail.getBasicRoomCardList()));
        hotelProduct.setGlobalInfo(globalInfoSnapshotConvert(hotelDetail.getGlobalInfo()));
        hotelProduct.setReservationNoticeList(reservationNoticeListSnapshotConvert(hotelDetail.getReservationNoticeList()));
        log.info("saveHotelProductSnapshotRequestConvert, hotelProduct:{}", JsonUtils.toJsonString(hotelProduct));
        return hotelProduct;
    }
    /**
     * 将查询酒店详细信息响应中的预订须知列表转换为预订须知类型列表
     *
     * @param reservationNoticeList 查询酒店详细信息响应中的预订须知列表
     * @return 预订须知类型列表
     */
    private static List<ReservationNoticeType> reservationNoticeListSnapshotConvert(List<QueryHotelDetailRespVo.ReservationNotice> reservationNoticeList) {
        if(CollectionUtils.isEmpty(reservationNoticeList)){
            return Collections.emptyList();
        }
        List<ReservationNoticeType> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(reservationNoticeList)){
            return list;
        }
        reservationNoticeList.forEach(e ->{
            ReservationNoticeType vo = new ReservationNoticeType();
            vo.setTitle(e.getTitle());
            vo.setDesc(e.getDesc());
            list.add(vo);
        });
        return list;
    }
    /**
     * 将查询酒店详细信息响应中的全局信息转换为全局信息类型
     *
     * @param globalInfo 查询酒店详细信息响应中的全局信息
     * @return 全局信息类型
     */
    private static GlobalInfoType globalInfoSnapshotConvert(QueryHotelDetailRespVo.GlobalInfo globalInfo) {
        GlobalInfoType info = new GlobalInfoType();
        if (Objects.isNull(globalInfo)){
            return info;
        }
        info.setHotelBonusPointDescList(globalInfo.getHotelBonusPointDescList());
        info.setAvgPriceControl(avgPriceControlSnapshotConvert(globalInfo.getAvgPriceControl()));
        info.setCorpShortName(globalInfo.getCorpShortName());
        info.setPaymentMethodList(globalInfo.getPaymentMethodList());
        info.setBookable(globalInfo.getBookable());
        return info;
    }
    /**
     * 将查询酒店详细信息响应中的平均价格控制信息转换为平均价格控制类型
     *
     * @param avgPriceControl 查询酒店详细信息响应中的平均价格控制信息
     * @return 平均价格控制类型，如果avgPriceControl为null则返回空的平均价格控制类型
     */
    private static AvgPriceControlType avgPriceControlSnapshotConvert(QueryHotelDetailRespVo.AvgPriceControl avgPriceControl) {
        AvgPriceControlType info = new AvgPriceControlType();
        if (Objects.isNull(avgPriceControl)){
            return info;
        }
        info.setCnyMaxPrice(avgPriceControl.getCnyMaxPrice());
        info.setForeignCurrency(avgPriceControl.getForeignCurrency());
        info.setForeignMaxPrice(avgPriceControl.getForeignMaxPrice());
        return info;
    }
    /**
     * 将查询酒店详细信息响应中的基础房型卡片列表转换为基础房型信息数据传输对象列表
     *
     * @param basicRoomCardList 查询酒店详细信息响应中的基础房型卡片列表
     * @return 基础房型信息数据传输对象列表
     */
    private static List<BasicRoomInfoDTO> basicRoomInfoSnapshotConvert(List<QueryHotelDetailRespVo.BasicRoomCard> basicRoomCardList) {
        List<BasicRoomInfoDTO> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(basicRoomCardList)){
            return list;
        }
        basicRoomCardList.forEach(e ->{
            BasicRoomInfoDTO info = new BasicRoomInfoDTO();
            info.setVirtualBasicRoomId(e.getVirtualBasicRoomId());
            info.setPictureList(e.getPictureList());
            info.setName(e.getName());
            info.setBedDesc(e.getBedDesc());
            info.setAreaDesc(e.getAreaDesc());
            info.setFloorDesc(e.getFloorDesc());
            info.setMinAvgPriceIncludeTax(e.getMinAvgPriceIncludeTax());
            info.setCanReserve(e.getCanReserve());
            info.setProtocolType(e.getProtocolType());
            info.setRoomCardList(roomCardListSnapshotConvert(e.getRoomCardList()));
            list.add(info);
        });
        return list;
    }

    /**
     * 将查询酒店详细信息响应中的房型卡片列表转换为房型信息数据传输对象列表
     *
     * @param roomCardList 查询酒店详细信息响应中的房型卡片列表
     * @return 房型信息数据传输对象列表
     */
    private static List<RoomInfoDTO> roomCardListSnapshotConvert(List<QueryHotelDetailRespVo.RoomCard> roomCardList) {
        List<RoomInfoDTO> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(roomCardList)){
            return list;
        }
        roomCardList.forEach(e ->{
            list.add(roomCardSnapshotConvert(e));
        });
        return list;
    }

    /**
     * 将查询酒店详细信息响应中的房型卡片转换为房型信息数据传输对象
     *
     * @param card 查询酒店详细信息响应中的房型卡片
     * @return 房型信息数据传输对象
     */
    private static RoomInfoDTO roomCardSnapshotConvert(QueryHotelDetailRespVo.RoomCard card) {
        if(ObjectUtil.isNull(card)){
            return null;
        }
        RoomInfoDTO roomInfoDTO = new RoomInfoDTO();
        roomInfoDTO.setCityId(card.getCityId());
        roomInfoDTO.setDistrictId(card.getDistrictId());
        roomInfoDTO.setGroupId(card.getGroupId());
        roomInfoDTO.setHotelId(card.getHotelId());
        roomInfoDTO.setBasicRoomId(card.getBasicRoomId());
        roomInfoDTO.setVirtualBasicRoomId(card.getVirtualBasicRoomId());
        roomInfoDTO.setRoomId(card.getRoomId());
        roomInfoDTO.setBrandId(card.getBrandId());
        roomInfoDTO.setProductId(card.getProductId());
        roomInfoDTO.setSupplierCode(card.getSupplierCode());
        roomInfoDTO.setSupplierName(card.getSupplierName());
        roomInfoDTO.setDirectSupplier(card.getDirectSupplier());
        roomInfoDTO.setCanReserve(card.getCanReserve());
        roomInfoDTO.setRoomBaseInfo(roomBaseInfoSnapshotConvert(card.getRoomBaseInfo()));
        roomInfoDTO.setFacilityGroupList(facilityGroupListSnapshotConvert(card.getFacilityGroupList()));
        roomInfoDTO.setRoomPackage(roomPackageSnapshotConvert(card.getRoomPackage()));
        roomInfoDTO.setRoomPolicyService(roomPolicyServiceSnapshotConvert(card.getRoomPolicyService()));
        roomInfoDTO.setRoomPrice(roomPriceSnapshotConvert(card.getRoomPrice()));
        roomInfoDTO.setMemberTagList(pickMemberTagList(card.getAllRoomTagList(),card.getGroupName()));
        roomInfoDTO.setOverLimitInfo(overLimitInfoSnapshotConvert(card.getOverLimitInfo()));
        roomInfoDTO.setPersonPriceType(convertPersonPrice(card.getPersonPrice()));
        roomInfoDTO.setHourlyRoomInfo(convertHourlyRoomInfo(card.getHourlyRoomInfo()));
        return roomInfoDTO;
    }
    
    private static HourlyRoomInfoDTO convertHourlyRoomInfo(HourlyRoomInfoVo hourlyRoomInfo) {
        if (hourlyRoomInfo == null) {
            return null;
        }
        HourlyRoomInfoDTO hourlyRoomInfoDTO = new HourlyRoomInfoDTO();
        hourlyRoomInfoDTO.setHourlyRoom(hourlyRoomInfo.getHourlyRoom());
        hourlyRoomInfoDTO.setHourlyRoomTip(hourlyRoomInfo.getHourlyRoomTip());
        hourlyRoomInfoDTO.setDurationHour(hourlyRoomInfo.getDurationHour());
        hourlyRoomInfoDTO.setIntervalStartMinute(hourlyRoomInfo.getIntervalStartMinute());
        hourlyRoomInfoDTO.setIntervalEndMinute(hourlyRoomInfo.getIntervalEndMinute());
        return hourlyRoomInfoDTO;
    }
    
    /**
     * 从所有酒店标签列表中挑选出最多MAX_COUNT个符合条件的标签，并转换为会员标签信息数据传输对象列表
     *
     * @param allHotelTagList     所有酒店标签列表
     * @param fillDescContents    填充描述内容的参数数组
     * @return 会员标签信息数据传输对象列表
     */
    public static List<MemberTagInfoDTO> pickMemberTagList(List<MemberTag> allHotelTagList,
                                                          String... fillDescContents) {
        return pickMemberTagList(allHotelTagList, MAX_COUNT, fillDescContents);
    }
    /**
     * 从所有标签列表中挑选出符合条件的标签，并转换为会员标签信息数据传输对象列表
     *
     * @param allTagList      所有标签列表
     * @param count           需要挑选的标签数量
     * @param fillDescContents 填充描述内容的参数数组
     * @return 会员标签信息数据传输对象列表
     */
    public static List<MemberTagInfoDTO> pickMemberTagList(List<MemberTag> allTagList,
                                                          int count,
                                                          String... fillDescContents) {
        if (CollectionUtils.isEmpty(allTagList)) {
            return Collections.emptyList();
        }
        return allTagList.stream()
                .filter(tagBo -> StringUtils.isNotBlank(tagBo.getTagCode()))
                .filter(tagBo -> SORT_MEMBER_TAG_FLAG_MAP.containsKey(tagBo.getTagCode()))
                .map(tagBo -> {
                    MemberTagFlag tag = SORT_MEMBER_TAG_FLAG_MAP.get(tagBo.getTagCode());
                    MemberTagInfoDTO tagInfo = new MemberTagInfoDTO();
                    tagInfo.setTagCode(tag.getCode());
                    tagInfo.setTagDesc(tag.getDesc(fillDescContents));
                    tagInfo.setTagName(tag.getName());
                    return tagInfo;
                })
                .limit(count)
                .collect(Collectors.toList());

    }

    /**
     * 将查询酒店详细信息响应中的成人价格转换为成人价格类型
     *
     * @param personPriceBO 查询酒店详细信息响应中的成人价格对象
     * @return 成人价格类型，如果personPriceBO为null则返回null
     */
    private static PersonPriceType convertPersonPrice(QueryHotelDetailRespVo.PersonPrice personPriceBO) {
        if(ObjectUtil.isNull(personPriceBO)){
            return null;
        }
        PersonPriceType res = new PersonPriceType();
        res.setAdult(personPriceBO.getAdult());
        res.setRateId(personPriceBO.getRateId());
        return res;
    }
    /**
     * 将查询酒店详细信息响应中的超限信息转换为超限信息数据传输对象
     *
     * @param overLimitInfo 查询酒店详细信息响应中的超限信息
     * @return 超限信息数据传输对象，如果overLimitInfo为null则返回null
     */
    private static OverLimitInfoDTO overLimitInfoSnapshotConvert(QueryHotelDetailRespVo.OverLimitInfo overLimitInfo) {
        if (overLimitInfo == null) {
            return null;
        }

        return OverLimitInfoDTO.builder()
                .overLimitModeList(overLimitInfo.getOverLimitModeList())
                .overLimitRuleNameList(overLimitInfo.getOverLimitRuleNameList()).build();
    }

    /**
     * 将查询酒店详细信息响应中的房型价格转换为房型价格类型
     *
     * @param roomPrice 查询酒店详细信息响应中的房型价格
     * @return 房型价格类型
     */
    private static RoomPriceType roomPriceSnapshotConvert(QueryHotelDetailRespVo.RoomPrice roomPrice) {
        if(ObjectUtil.isNull(roomPrice)){
            return null;
        }
        RoomPriceType info = new RoomPriceType();
        info.setRoomQuantity(roomPrice.getRoomQuantity());
        info.setDayQuantity(roomPrice.getDayQuantity());
        info.setDailyRateList(dailyRateListSnapshotConvert(roomPrice.getDailyRateList()));
        info.setTotalPriceIncludeTax(roomPrice.getTotalPriceIncludeTax());
        info.setTotalRoomTax(roomPrice.getTotalRoomTax());
        info.setForeignTotalRoomTax(roomPrice.getForeignTotalRoomTax());
        info.setTotalExtraTax(roomPrice.getTotalExtraTax());
        info.setForeignTotalExtraTax(roomPrice.getForeignTotalExtraTax());
        info.setAvgPriceIncludeTax(roomPrice.getAvgPriceIncludeTax());
        info.setAvgExtraTax(roomPrice.getAvgExtraTax());
        info.setForeignCurrency(roomPrice.getForeignCurrency());
        info.setTotalExtraTaxDetailList(taxDetailListSnapshotConvert(roomPrice.getTotalExtraTaxDetailList()));
        info.setTotalRoomTaxDetailList(taxDetailListSnapshotConvert(roomPrice.getTotalRoomTaxDetailList()));
        info.setTotalServiceCharge(roomPrice.getTotalServiceCharge());
        info.setServiceChargeStrategy(roomPrice.getServiceChargeStrategy());
        info.setServiceChargeStrategyValue(roomPrice.getServiceChargeStrategyValue());
        return info;
    }

    /**
     * 将查询酒店详细信息响应中的税费详情列表转换为税费价格类型列表
     *
     * @param totalExtraTaxDetailList 查询酒店详细信息响应中的税费详情列表
     * @return 税费价格类型列表
     */
    private static List<TaxPriceType> taxDetailListSnapshotConvert(List<QueryHotelDetailRespVo.Price> totalExtraTaxDetailList) {
        List<TaxPriceType> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(totalExtraTaxDetailList)){
            return list;
        }
        totalExtraTaxDetailList.forEach(e ->{
            TaxPriceType info = new TaxPriceType();
            info.setName(e.getName());
            info.setForeignPrice(e.getForeignPrice());
            info.setPrice(e.getPrice());
            list.add(info);
        });
        return list;
    }

    /**
     * 将查询酒店详细信息响应中的每日房价列表转换为每日房价列表
     *
     * @param dailyRateList 查询酒店详细信息响应中的每日房价列表
     * @return 每日房价列表
     */
    private static List<DailyRoomPrice> dailyRateListSnapshotConvert(List<QueryHotelDetailRespVo.DailyRate> dailyRateList) {
        List<DailyRoomPrice> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(dailyRateList)){
            return list;
        }
        dailyRateList.forEach(e ->{
            DailyRoomPrice info = new DailyRoomPrice();
            info.setDate(e.getDate());
            info.setAvgPriceIncludeTax(e.getAvgPriceIncludeTax());
            list.add(info);
        });
        return list;
    }

    /**
     * 将查询酒店详细信息响应中的房型政策服务转换为房型政策服务数据传输对象
     *
     * @param roomPolicyService 查询酒店详细信息响应中的房型政策服务
     * @return 房型政策服务数据传输对象，如果roomPolicyService为null则返回空的房型政策服务数据传输对象
     */
    private static RoomPolicyServiceDTO roomPolicyServiceSnapshotConvert(QueryHotelDetailRespVo.RoomPolicyService roomPolicyService) {
        RoomPolicyServiceDTO info = new RoomPolicyServiceDTO();
        if (Objects.isNull(roomPolicyService)){
            return info;
        }
        info.setApplicativeArea(applicativeAreaSnapshotConvert(roomPolicyService.getApplicativeArea()));
        info.setCancelPolicy(cancelPolicySnapshotConvert(roomPolicyService.getCancelPolicy()));
        info.setJustifyConfirm(roomPolicyService.getJustifyConfirm());
        info.setHotelBonusPoint(roomPolicyService.getHotelBonusPoint());
        info.setSupportSpecialInvoice(roomPolicyService.getSupportSpecialInvoice());
        info.setProtocolType(roomPolicyService.getProtocolType());
        info.setBreakfastCount(roomPolicyService.getBreakfastCount());
        info.setPaymentMethod(paymentMethodSnapshotConvert(roomPolicyService.getPaymentMethod()));
        info.setSpecialNotes(roomPolicyService.getSpecialNotes());
        return info;
    }
    /**
     * 将查询酒店详细信息响应中的支付方式转换为支付方式类型
     *
     * @param paymentMethod 查询酒店详细信息响应中的支付方式
     * @return 支付方式类型，如果paymentMethod为null则返回空的支付方式类型
     */
    private static PaymentMethodType paymentMethodSnapshotConvert(QueryHotelDetailRespVo.PaymentMethod paymentMethod) {
        PaymentMethodType info = new PaymentMethodType();
        if (Objects.isNull(paymentMethod)){
            return info;
        }
        info.setType(paymentMethod.getType());
        info.setName(paymentMethod.getName());
        return info;
    }


    /**
     * 将查询酒店详细信息响应中的取消政策转换为取消政策类型
     *
     * @param cancelPolicy 查询酒店详细信息响应中的取消政策
     * @return 取消政策类型，如果cancelPolicy为null则返回空的取消政策类型
     */
    private static CancelPolicyType cancelPolicySnapshotConvert(QueryHotelDetailRespVo.CancelPolicy cancelPolicy) {
        CancelPolicyType info = new CancelPolicyType();
        if (Objects.isNull(cancelPolicy)){
            return info;
        }
        info.setCancelRuleType(cancelPolicy.getCancelRuleType());
        info.setEndFreeCancelTime(cancelPolicy.getEndFreeCancelTime());
        info.setStepCancelPolicyList(stepCancelPolicyListSnapshotConvert(cancelPolicy.getStepCancelPolicyList()));
        return info;
    }

    /**
     * 将查询酒店详细信息响应中的分步取消政策列表转换为分步取消政策类型列表
     *
     * @param stepCancelPolicyList 查询酒店详细信息响应中的分步取消政策列表
     * @return 分步取消政策类型列表
     */
    private static List<StepCancelPolicyType> stepCancelPolicyListSnapshotConvert(List<QueryHotelDetailRespVo.StepCancelPolicy> stepCancelPolicyList) {
        List<StepCancelPolicyType> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(stepCancelPolicyList)){
            return list;
        }
        stepCancelPolicyList.forEach(e ->{
            StepCancelPolicyType info = new StepCancelPolicyType();
            info.setCancelRuleType(e.getCancelRuleType());
            info.setStartTime(e.getStartTime());
            info.setEndTime(e.getEndTime());
            info.setPrice(e.getPrice());
            list.add(info);
        });
        return list;
    }

    /**
     * 将查询酒店详细信息响应中的应用区域信息转换为应用区域类型
     *
     * @param applicativeArea 查询酒店详细信息响应中的应用区域信息
     * @return 应用区域类型，如果applicativeArea为null则返回空的应用区域类型
     */
    private static ApplicativeAreaType applicativeAreaSnapshotConvert(QueryHotelDetailRespVo.ApplicativeArea applicativeArea) {
        ApplicativeAreaType info = new ApplicativeAreaType();
        if (Objects.isNull(applicativeArea)){
            return info;
        }
        info.setName(applicativeArea.getName());
        info.setDesc(applicativeArea.getDesc());
        return info;
    }

    /**
     * 将查询酒店详细信息响应中的房型套餐转换为房型套餐类型
     *
     * @param roomPackage 查询酒店详细信息响应中的房型套餐
     * @return 房型套餐类型，如果roomPackage为null则返回null
     */
    private static RoomPackageType roomPackageSnapshotConvert(QueryHotelDetailRespVo.RoomPackage roomPackage) {
        RoomPackageType info = new RoomPackageType();
        if (Objects.isNull(roomPackage)){
            return null;
        }
        info.setPackageId(roomPackage.getPackageId());
        info.setPackageProductList(packageProductListSnapshotConvert(roomPackage.getPackageProductList()));
        return info;
    }

    /**
     * 将查询酒店详细信息响应中的套餐产品列表转换为套餐产品类型列表
     *
     * @param packageProductList 查询酒店详细信息响应中的套餐产品列表
     * @return 套餐产品类型列表
     */
    private static List<PackageProductType> packageProductListSnapshotConvert(List<QueryHotelDetailRespVo.PackageProduct> packageProductList) {
        List<PackageProductType> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(packageProductList)){
            return list;
        }
        packageProductList.forEach(e ->{
            list.add(packageProductSnapshotConvert(e));
        });
        return list;
    }
    /**
     * 将查询酒店详细信息响应中的套餐产品转换为套餐产品类型
     *
     * @param packageProduct 查询酒店详细信息响应中的套餐产品
     * @return 套餐产品类型
     */
    private static PackageProductType packageProductSnapshotConvert(QueryHotelDetailRespVo.PackageProduct packageProduct) {
        if(ObjectUtil.isNull(packageProduct)){
            return null;
        }
        PackageProductType info = new PackageProductType();
        info.setIconNameList(packageProduct.getIconNameList());
        info.setPackageName(packageProduct.getPackageName());
        info.setMealDescList(packageProduct.getMealDescList());
        info.setMaxGuestDesc(packageProduct.getMaxGuestDesc());
        info.setBookRuleDesc(packageProduct.getBookRuleDesc());
        info.setTelephoneList(packageProduct.getTelephoneList());
        info.setReceptionTimeDescList(packageProduct.getReceptionTimeDescList());
        info.setSpecialDesc(packageProduct.getSpecialDesc());
        return info;
    }

    /**
     * 将查询酒店详细信息响应中的设施组列表转换为设施组类型列表
     *
     * @param facilityGroupList 查询酒店详细信息响应中的设施组列表
     * @return 设施组类型列表
     */
    private static List<FacilityGroupType> facilityGroupListSnapshotConvert(List<QueryHotelDetailRespVo.FacilityGroup> facilityGroupList) {
        List<FacilityGroupType> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(facilityGroupList)){
            return list;
        }
        facilityGroupList.forEach(e ->{
            FacilityGroupType info = new FacilityGroupType();
            info.setName(e.getName());
            info.setFacilityList(facilityListSnapshotConvert(e.getFacilityList()));
            list.add(info);
        });
        return list;
    }
    /**
     * 将查询酒店详细信息响应中的设施列表转换为设施类型列表
     *
     * @param facilityList 查询酒店详细信息响应中的设施列表
     * @return 设施类型列表
     */
    private static List<FacilityType> facilityListSnapshotConvert(List<QueryHotelDetailRespVo.Facility> facilityList) {
        List<FacilityType> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(facilityList)){
            return list;
        }
        facilityList.forEach(e ->{
            FacilityType info = new FacilityType();
            info.setName(e.getName());
            info.setCharge(e.getCharge());
            list.add(info);
        });
        return list;
    }

    /**
     * 将查询酒店详细信息响应中的房间基本信息转换为房间基本信息类型
     *
     * @param roomBaseInfo 查询酒店详细信息响应中的房间基本信息
     * @return 房间基本信息类型
     */
    private static RoomBaseInfoType roomBaseInfoSnapshotConvert(QueryHotelDetailRespVo.RoomBaseInfo roomBaseInfo) {
        if(ObjectUtil.isNull(roomBaseInfo)){
            return null;
        }
        RoomBaseInfoType roomBaseInfoType = new RoomBaseInfoType();
        roomBaseInfoType.setFloorDesc(roomBaseInfo.getFloorDesc());
        roomBaseInfoType.setWindowDesc(roomBaseInfo.getWindowDesc());
        roomBaseInfoType.setSmokeDesc(roomBaseInfo.getSmokeDesc());
        roomBaseInfoType.setMaxGuestNum(roomBaseInfo.getMaxGuestNum());
        roomBaseInfoType.setBedDesc(roomBaseInfo.getBedDesc());
        roomBaseInfoType.setWifiDesc(roomBaseInfo.getWifiDesc());
        roomBaseInfoType.setAreaDesc(roomBaseInfo.getAreaDesc());
        roomBaseInfoType.setParentBedDesc(roomBaseInfo.getParentBedDesc());
        return roomBaseInfoType;
    }

    /**
     * 将查询酒店详细信息响应中的酒店基本信息转换为酒店基本信息数据传输对象
     *
     * @param hotelBaseInfo 查询酒店详细信息响应中的酒店基本信息
     * @return 酒店基本信息数据传输对象
     */
    private static HotelBaseInfoDTO hotelInfoSnapshotConvert(QueryHotelDetailRespVo.HotelBaseInfo hotelBaseInfo) {
        HotelBaseInfoDTO info = new HotelBaseInfoDTO();
        if (Objects.isNull(hotelBaseInfo)){
            return info;
        }
        info.setName(hotelBaseInfo.getName());
        info.setNameEn(hotelBaseInfo.getNameEn());
        info.setAddress(hotelBaseInfo.getAddress());
        info.setLon(hotelBaseInfo.getLon());
        info.setLat(hotelBaseInfo.getLat());
        info.setLogoUrl(hotelBaseInfo.getLogoUrl());
        info.setStar(hotelBaseInfo.getStar());
        info.setStarLicence(hotelBaseInfo.getStarLicence());
        info.setLevelName(hotelBaseInfo.getLevelName());
        info.setReviewScore(hotelBaseInfo.getReviewScore());
        info.setGroupName(hotelBaseInfo.getGroupName());
        info.setTelephone(hotelBaseInfo.getTelephone());
        info.setDistrictName(hotelBaseInfo.getDistrictName());
        info.setCityName(hotelBaseInfo.getCityName());
        info.setOpenDateDesc(hotelBaseInfo.getOpenDateDesc());
        info.setRenovationDateDesc(hotelBaseInfo.getRenovationDateDesc());
        info.setFacilityList(hotelBaseInfo.getFacilityList());
        info.setVideoList(videoListSnapshotConvert(hotelBaseInfo.getVideoList()));
        info.setPictureList(pictureListSnapshotConvert(hotelBaseInfo.getPictureList()));
        info.setMapInfoList(mapInfoListConvert(hotelBaseInfo.getMapInfoList()));
        info.setSupplierStarInfo(buildSupplierStar(hotelBaseInfo.getSupplierStarInfo()));
        return info;
    }
    /**
     * 构建供应商星级信息映射
     *
     * @param supplierStarInfo 查询酒店详细信息响应中的供应商星级信息映射
     * @return 供应商星级信息映射
     */
    private static Map<String, SupplierStarDTO> buildSupplierStar(Map<String, QueryHotelDetailRespVo.SupplierStarDTO> supplierStarInfo) {
        if (MapUtils.isEmpty(supplierStarInfo)) {
            return new HashMap<>(0);
        }
        Map<String, SupplierStarDTO> supplierStarMap = new HashMap<>();
        supplierStarInfo.forEach((k, v) -> {
            SupplierStarDTO supplierStarDto = new SupplierStarDTO();
            supplierStarDto.setStar(v.getStar());
            supplierStarDto.setStarLicence(v.getStarLicence());
            supplierStarMap.put(k, supplierStarDto);
        });
        return supplierStarMap;
    }
    /**
     * 将查询酒店详细信息响应中的地图信息列表转换为地图信息数据传输对象列表
     *
     * @param mapInfoList 查询酒店详细信息响应中的地图信息列表
     * @return 地图信息数据传输对象列表
     */
    private static List<MapInfoDTO> mapInfoListConvert(List<QueryHotelDetailRespVo.MapInfo> mapInfoList) {
        if (CollectionUtils.isEmpty(mapInfoList)) {
            return null;
        }

        List<MapInfoDTO> resultList = new ArrayList<>();
        for (QueryHotelDetailRespVo.MapInfo mapInfo : mapInfoList) {
            if (mapInfo == null) {
                continue;
            }
            MapInfoDTO mapInfoDTO = new MapInfoDTO();
            mapInfoDTO.setLat(mapInfo.getLat());
            mapInfoDTO.setLon(mapInfo.getLon());
            mapInfoDTO.setMapType(mapInfo.getMapType());
            resultList.add(mapInfoDTO);
        }
        return resultList;
    }
    /**
     * 将查询酒店详细信息响应中的图片列表转换为图片类型列表
     *
     * @param pictureList 查询酒店详细信息响应中的图片列表
     * @return 图片类型列表
     */
    private static List<PictureType> pictureListSnapshotConvert(List<QueryHotelDetailRespVo.Picture> pictureList) {
        List<PictureType> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(pictureList)){
            return list;
        }
        pictureList.forEach(e ->{
            PictureType info = new PictureType();
            info.setType(e.getType());
            info.setUrl(e.getUrl());
            list.add(info);
        });
        return list;
    }
    /**
     * 将查询酒店详细信息响应中的视频列表转换为视频类型列表
     *
     * @param videoList 查询酒店详细信息响应中的视频列表
     * @return 视频类型列表
     */
    private static List<VideoType> videoListSnapshotConvert(List<QueryHotelDetailRespVo.Video> videoList) {
        List<VideoType> list = CollectionUtils.newArrayList();
        if (CollectionUtils.isEmpty(videoList)){
            return list;
        }
        videoList.forEach(e ->{
            VideoType info = new VideoType();
            info.setUrl(e.getUrl());
            info.setCoverPictureUrl(e.getCoverPictureUrl());
            list.add(info);
        });
        return list;
    }
}
