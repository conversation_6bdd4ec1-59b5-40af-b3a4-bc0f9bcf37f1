package com.corpgovernment.basic.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.api.travelstandard.vo.*;
import com.corpgovernment.api.travelstandard.vo.response.AverageBrandSet;
import com.corpgovernment.basic.bo.response.RuleChainBO;
import com.corpgovernment.basic.bo.response.TravelStandardResponseBO;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.*;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 差标转换工具类
 *
 * <AUTHOR>
 * @description: TODO
 * @date 2024/1/25
 */
@Slf4j
public class TravelStandardConvert {

    public static List<com.corpgovernment.api.travelstandard.vo.ExceedReasonVO> convertExceedReasonVO(List<com.corpgovernment.dto.travelstandard.response.ExceedReasonVO> exceedReasonVOList){
        List<com.corpgovernment.api.travelstandard.vo.ExceedReasonVO> exceedReasonVOlist = new ArrayList<>();
        if(CollectionUtil.isEmpty(exceedReasonVOList)){
            return exceedReasonVOlist;
        }
        exceedReasonVOlist = JsonUtils.parseArray(JsonUtils.toJsonString(exceedReasonVOList),
                com.corpgovernment.api.travelstandard.vo.ExceedReasonVO.class);
        return exceedReasonVOlist;
    }

    /**
     * 处理同住
     * @param travelStandardResponseBO
     * @return
     */
    public static HotelControlVo travelStandardResponseShareWithBOConver(TravelStandardResponseBO travelStandardResponseBO) {
        log.info("处理同住,travelStandardResponseBO:{}",JsonUtils.toJsonString(travelStandardResponseBO));
        StarRuleVO starRuleVO = travelStandardResponseBO.getRuleChainBO().getStarRuleVO();
        CohabitRuleVO cohabitRuleVO = travelStandardResponseBO.getRuleChainBO().getCohabitRuleVO();
        OffPeakSeasonRuleVO offPeakSeasonRuleVO = travelStandardResponseBO.getRuleChainBO().getOffPeakSeasonRuleVO();
        PriceRuleVO priceRuleVO = travelStandardResponseBO.getRuleChainBO().getPriceRuleVO();
        BrandRuleVO brandRuleVO = travelStandardResponseBO.getRuleChainBO().getBrandRuleVO();

        // 拒绝策略
        // 【差标接口返回超标管控方式】
        //  F("F", "禁止预订", 0),
        //  C("C", "选择原因后继续预订", 1),
        //  M("M", "支持混付", 3),
        String[] rejectTypes = null;
        HotelControlVo hotelControlVo = new HotelControlVo();
        hotelControlVo.setExceedReasonList(convertExceedReasonVO(travelStandardResponseBO.getRuleChainBO().getExceedReasonList()));
        //  淡旺季、均价、同住取值规则
        //  开了同住：取淡旺季的价格，按照同住计算规则计算差标 = 同住差标 > 取均价；
        //  没有同住：取淡旺季价格 >  取均价；
        if(ObjectUtil.isNotNull(cohabitRuleVO)){
            if(ObjectUtil.isNotNull(cohabitRuleVO.getMaxPrice())){
                hotelControlVo.setOffPeakSeasonSet(createAveragePriceSet(cohabitRuleVO.getMaxPrice().toString(), "0"));
            }
            if(ObjectUtil.isNotNull(cohabitRuleVO.getMaxFloatPrice())){
                hotelControlVo.setFloatAveragePriceSet(createAveragePriceSet(cohabitRuleVO.getMaxFloatPrice().toString(), "0"));
            }
            rejectTypes = cohabitRuleVO.getRejectTypes();
        }else if(ObjectUtil.isNotNull(offPeakSeasonRuleVO) && ObjectUtil.isNotNull(offPeakSeasonRuleVO.getMaxPrice())){
            hotelControlVo.setOffPeakSeasonSet(createAveragePriceSet(offPeakSeasonRuleVO.getMaxPrice().toString(), "0"));
            rejectTypes = offPeakSeasonRuleVO.getRejectTypes();
        }else if(ObjectUtil.isNotNull(priceRuleVO) && ObjectUtil.isNotNull(priceRuleVO.getMaxPrice())){
            hotelControlVo.setOffPeakSeasonSet(createAveragePriceSet(priceRuleVO.getMaxPrice().toString(), "0"));
            rejectTypes = priceRuleVO.getRejectTypes();
        }
        if(ObjectUtil.isNotNull(starRuleVO)){
            hotelControlVo.setAverageStarSet(createAverageStarSet(starRuleVO));
        }
        if (ObjectUtil.isNotNull(brandRuleVO)) {
            hotelControlVo.setBrandSet(createAverageBrandSet(brandRuleVO));
        }
        if(ArrayUtil.isNotEmpty(rejectTypes)){
            if(ArrayUtil.contains(rejectTypes, ControlTypeEnum.M.getCode())){
                hotelControlVo.setControl(ControlTypeEnum.M.getValue());
            }else if(ArrayUtil.contains(rejectTypes, ControlTypeEnum.F.getCode())){
                hotelControlVo.setControl(ControlTypeEnum.F.getValue());
            }else if(ArrayUtil.contains(rejectTypes, ControlTypeEnum.C.getCode())){
                hotelControlVo.setControl(ControlTypeEnum.C.getValue());
            }
        }
        log.info("travelStandardResponseShareWithBOConver,hotelControlVo:{}", JsonUtils.toJsonString(hotelControlVo));
        return hotelControlVo;
    }

    /**
     * 处理非同住
     * @param travelStandardResponseBO
     * @return
     */
    public static HotelControlVo travelStandardResponseBOConver(TravelStandardResponseBO travelStandardResponseBO) {
        log.info("处理非同住,travelStandardResponseBO:{}",JsonUtils.toJsonString(travelStandardResponseBO));
        if(ObjectUtil.isNull(travelStandardResponseBO)){
            return null;
        }
        StarRuleVO starRuleVO = null;
        PriceRuleVO priceRuleVO = null;
        OffPeakSeasonRuleVO offPeakSeasonRuleVO = null;
        if(ObjectUtil.isNotNull(travelStandardResponseBO.getRuleChainBO())){
            starRuleVO = travelStandardResponseBO.getRuleChainBO().getStarRuleVO();
            priceRuleVO = travelStandardResponseBO.getRuleChainBO().getPriceRuleVO();
            offPeakSeasonRuleVO = travelStandardResponseBO.getRuleChainBO().getOffPeakSeasonRuleVO();
        }

        // 拒绝策略
        // 【差标接口返回超标管控方式】
        //  F("F", "禁止预订", 0),
        //  C("C", "选择原因后继续预订", 1),
        //  M("M", "支持混付", 3),
        String[] rejectTypes = null;
        HotelControlVo hotelControlVo = new HotelControlVo();
        hotelControlVo.setExceedReasonList(convertExceedReasonVO(travelStandardResponseBO.getRuleChainBO().getExceedReasonList()));

        //  淡旺季、均价、同住取值规则
        //  开了同住：取淡旺季的价格，按照同住计算规则计算差标 = 同住差标 > 取均价；
        //  没有同住：取淡旺季价格 >  取均价；
        if(ObjectUtil.isNotNull(offPeakSeasonRuleVO) && ObjectUtil.isNotNull(offPeakSeasonRuleVO.getMaxPrice())){
            hotelControlVo.setOffPeakSeasonSet(createAveragePriceSet(offPeakSeasonRuleVO.getMaxPrice().toString(), "0"));
            rejectTypes = offPeakSeasonRuleVO.getRejectTypes();
        } else if(ObjectUtil.isNotNull(priceRuleVO)){
            if(ObjectUtil.isNotNull(priceRuleVO.getMaxPrice())){
                hotelControlVo.setOffPeakSeasonSet(createAveragePriceSet(priceRuleVO.getMaxPrice().toString(), "0"));
            }
            if(ObjectUtil.isNotNull(priceRuleVO.getMinPrice())){
                hotelControlVo.setFloatAveragePriceSet(createAveragePriceSet(priceRuleVO.getMinPrice().toString(), "0"));
            }
            rejectTypes = priceRuleVO.getRejectTypes();
        }
        if(ObjectUtil.isNotNull(starRuleVO)){
            hotelControlVo.setAverageStarSet(createAverageStarSet(starRuleVO));
        }

        if(ArrayUtil.isNotEmpty(rejectTypes)){
            if(ArrayUtil.contains(rejectTypes, ControlTypeEnum.M.getCode())){
                hotelControlVo.setControl(ControlTypeEnum.M.getValue());
            }else if(ArrayUtil.contains(rejectTypes, ControlTypeEnum.F.getCode())){
                hotelControlVo.setControl(ControlTypeEnum.F.getValue());
            }else if(ArrayUtil.contains(rejectTypes, ControlTypeEnum.C.getCode())){
                hotelControlVo.setControl(ControlTypeEnum.C.getValue());
            }
        }
        log.info("travelStandardResponseBOConver,hotelControlVo:{}", JsonUtils.toJsonString(hotelControlVo));
        return hotelControlVo;
    }

    private static AveragePriceSet createAveragePriceSet(String priceCeiling, String priceFloor) {
        AveragePriceSet averagePriceSet = new AveragePriceSet();
        averagePriceSet.setPriceCeiling(priceCeiling);
        averagePriceSet.setPriceFloor(priceFloor);
        return averagePriceSet;
    }

    private static AverageBrandSet createAverageBrandSet(BrandRuleVO brandRuleVO) {
        AverageBrandSet res = new AverageBrandSet();
        if (null == brandRuleVO || CollectionUtils.isEmpty(brandRuleVO.getBrandList())) {
            return res;
        }
        List<BrandInfo> brandInfoList = brandRuleVO.getBrandList().stream().map(brand -> {
            BrandInfo brandInfo = new BrandInfo();
            brandInfo.setBrandId(brand.getBrandId());
            brandInfo.setBrandName(brand.getBrandName());
            return brandInfo;
        }).collect(Collectors.toList());
        res.setBrandInfoList(brandInfoList);
        return res;
    }
    private static AverageStarSet createAverageStarSet(StarRuleVO starRuleVO) {
        AverageStarSet averageStarSet = new AverageStarSet();
        if(ObjectUtil.isNotNull(starRuleVO)){
            StarCeiling starCeiling = new StarCeiling();
            starCeiling.setValue(Optional.ofNullable(starRuleVO.getMaxStar()).map(String::valueOf).orElse(""));
            averageStarSet.setStarCeiling(starCeiling);
        }
        return averageStarSet;
    }

    public static List<TravelStandardResponseBO> convert(List<TravelStandardResponse> travelStandardResponseList) {
        List<TravelStandardResponseBO> travelStandardResponseBOList = new ArrayList<>();
        for (TravelStandardResponse travelStandardResponse : travelStandardResponseList) {
            TravelStandardResponseBO travelStandardResponseBO = new TravelStandardResponseBO();
            travelStandardResponseBO.setTravelStandardToken(travelStandardResponse.getTravelStandardToken());
            RuleChainBO ruleChainBO = new RuleChainBO();
            List<TravelStandardRuleVO> travelStandardRuleVOList = travelStandardResponse.getRuleChain().getRuleList();
            if(CollectionUtil.isNotEmpty(travelStandardRuleVOList)){
                for (TravelStandardRuleVO travelStandardRuleVO : travelStandardRuleVOList) {
                    travelStandardRuleVOConvert(travelStandardRuleVO, ruleChainBO);
                }
            }
            travelStandardResponseBO.setRuleChainBO(ruleChainBO);
            travelStandardResponseBOList.add(travelStandardResponseBO);
        }
        return travelStandardResponseBOList;
    }

    private static void travelStandardRuleVOConvert(TravelStandardRuleVO travelStandardRuleVO, RuleChainBO ruleChainBO) {
        if (travelStandardRuleVO instanceof StarRuleVO) {
            StarRuleVO rule = new StarRuleVO();
            BeanUtil.copyProperties(travelStandardRuleVO, rule);
            ruleChainBO.setStarRuleVO(rule);
        } else if (travelStandardRuleVO instanceof OffPeakSeasonRuleVO) {
            OffPeakSeasonRuleVO rule = new OffPeakSeasonRuleVO();
            BeanUtil.copyProperties(travelStandardRuleVO, rule);
            ruleChainBO.setOffPeakSeasonRuleVO(rule);
        } else if (travelStandardRuleVO instanceof CohabitRuleVO) {
            CohabitRuleVO rule = new CohabitRuleVO();
            BeanUtil.copyProperties(travelStandardRuleVO, rule);
            ruleChainBO.setCohabitRuleVO(rule);
        } else if (travelStandardRuleVO instanceof PriceRuleVO) {
            PriceRuleVO rule = new PriceRuleVO();
            BeanUtil.copyProperties(travelStandardRuleVO, rule);
            ruleChainBO.setPriceRuleVO(rule);
        } else if (travelStandardRuleVO instanceof FloatPriceRuleVO) {
            FloatPriceRuleVO rule = new FloatPriceRuleVO();
            BeanUtil.copyProperties(travelStandardRuleVO, rule);
            ruleChainBO.setFloatPriceRuleVO(rule);
        } else if (travelStandardRuleVO instanceof BrandRuleVO) {
            BrandRuleVO rule = new BrandRuleVO();
            BeanUtil.copyProperties(travelStandardRuleVO, rule);
            ruleChainBO.setBrandRuleVO(rule);
        }
        ruleChainBO.setExceedReasonList(travelStandardRuleVO.getExceedReasonList());
    }
}
