package com.corpgovernment.swagger;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Bean
    public Docket docket() {
        // 构建并配置 Docket 对象
        return new Docket(DocumentationType.SWAGGER_2)
                // 用来创建该 API 的基本信息，展示在文档的页面中
                .apiInfo(apiInfo())
                .enable(true)
                .useDefaultResponseMessages(false)
                // 返回 ApiSelectorBuilder
                .select()
                //包下的类，才生成接口文档
                .apis(RequestHandlerSelectors.basePackage("com.corpgovernment"))
                // 接口路径过滤方案
                .paths(PathSelectors.any())
                .build();

    }

    /**
     * 构建基本配置信息
      */
    public ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("酒店预定服务管理")
                .description("酒店预定服务接口文档")
                .version("v1.0")
                .contact(new Contact("携程程曦", null, null))
                .build();
    }

}

