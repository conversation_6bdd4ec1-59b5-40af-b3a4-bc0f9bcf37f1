package com.corpgovernment;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.corpgovernment.common.annotation.EnablePermissionValidate;
import com.github.pagehelper.PageHelper;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableMBeanExport;
import org.springframework.jmx.support.RegistrationPolicy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.*;


@EnableDiscoveryClient
@EnableFeignClients(value = "com.corpgovernment")
@EnableMethodCache(basePackages = "com.corpgovernment")
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class,scanBasePackages = {"com.ctrip.corp.obt.falconcache","com.alicp.jetcache.anno","com.corpgovernment"})
@EnableTransactionManagement
@EnableScheduling
@EnablePermissionValidate
@EnableMBeanExport(registration = RegistrationPolicy.IGNORE_EXISTING)
@ServletComponentScan
@MapperScan(basePackages = "com.corpgovernment.**.mapper")
public class HotelBookingApplication {
    public static void main(String[] args) {
        SpringApplication.run(HotelBookingApplication.class, args);
    }

    @Bean
    public PageHelper pageHelper() {
        PageHelper pageHelper = new PageHelper();
        Properties properties = new Properties();
        properties.setProperty("offsetAsPageNum", "true");
        properties.setProperty("rowBoundsWithCount", "true");
        properties.setProperty("reasonable", "true");
        //配置mysql数据库的方言
        properties.setProperty("dialect", "mysql");
        pageHelper.setProperties(properties);
        return pageHelper;
    }
}
