package com.corpgovernment.core.service.custom;

import com.corpgovernment.api.hotel.booking.core.QueryHotelListReqVo;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPage;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControl;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-25 11:54
 */
public interface IHotelCustomService {

    /**
     * 获取差标控制列表
     * @param hotelPageRequest 酒店页请求
     * @param travelStandard 差标
     * @param travelConfig 差旅配置
     * @param travelStandardFilter 符合差标过滤
     * @return 差标控制列表
     */
    List<TravelControl> getTravelControlList(HotelPageRequest hotelPageRequest, TravelStandard travelStandard, TravelConfig travelConfig, Boolean travelStandardFilter);

    /**
     * 查询酒店列表
     * @param requestParam 请求参数
     * @param travelControlList 差标控制列表
     * @param travelConfig 差旅配置
     * @return 酒店列表
     */
    HotelPage queryHotelList(QueryHotelListReqVo requestParam, List<TravelControl> travelControlList, TravelConfig travelConfig);

    /**
     * 特殊处理酒店详情
     * @param hotelDetail 酒店详情
     * @param prioritySupplierCodeList 优先供应商列表
     * @param hotelRoomSortRule 酒店房间排序规则
     */
    void specialHandleHotelDetail(HotelDetail hotelDetail, List<String> prioritySupplierCodeList, Integer hotelRoomSortRule);

    /**
     * 获取酒店定制枚举
     * @return 定制枚举
     */
    HotelCustomEnum hotelCustomEnum();

}
