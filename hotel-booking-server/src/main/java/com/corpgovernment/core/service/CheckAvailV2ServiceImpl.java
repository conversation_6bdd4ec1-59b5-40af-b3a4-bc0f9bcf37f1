package com.corpgovernment.core.service;

import com.corpgovernment.api.applytrip.soa.response.ApplyTripTrafficVerifyResponse;
import com.corpgovernment.api.hotel.booking.checkavail.request.CheckAvailRequestVo;
import com.corpgovernment.api.hotel.booking.checkavail.request.CheckAvailV2RequestVo;
import com.corpgovernment.api.hotel.booking.checkavail.response.CheckAvailResponseVo;
import com.corpgovernment.api.hotel.booking.checkavail.response.CheckAvailV2ResponseVo;
import com.corpgovernment.api.hotel.booking.core.HourlyRoomInfoVo;
import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.RoomDailyInfo;
import com.corpgovernment.api.hotel.product.model.checkavail.response.LocalCheckAvailResponseBo.RoomItem;
import com.corpgovernment.common.base.BaseService;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.CancelPolicyEnum;
import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.common.enums.ExpenseTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.converter.model.queryparam.QueryParamModel;
import com.corpgovernment.core.domain.common.model.enums.SupportCertificateTypeEnum;
import com.corpgovernment.core.dao.openfeign.impl.HotelCoreOpenFeignDao;
import com.corpgovernment.core.domain.common.model.enums.TravelAttributeEnum;
import com.corpgovernment.core.domain.gateway.HotelSnapshotGateway;
import com.corpgovernment.core.domain.gateway.impl.ProductSnapshotConverter;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HourlyRoomInfo;
import com.corpgovernment.core.domain.model.SupplierCheckAvailResultModel;
import com.corpgovernment.core.domain.model.snapshot.config.*;
import com.corpgovernment.core.domain.model.snapshot.fee.BonusPointInfoModel;
import com.corpgovernment.core.domain.model.snapshot.fee.PriceInfoModel;
import com.corpgovernment.core.domain.model.snapshot.fee.*;
import com.corpgovernment.core.domain.model.snapshot.product.*;
import com.corpgovernment.core.service.bo.ChangePriceResultBo;
import com.corpgovernment.core.service.bo.DailyRoomChangePriceBo;
import com.corpgovernment.dto.config.ServiceFeeDTO;
import com.corpgovernment.dto.snapshot.PriceInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.ChangeInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.HotelFeeInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.TaxFeeType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.TaxInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelFeeSnapshotResponse;
import com.corpgovernment.hotel.booking.bo.BookingCheckResultBo;
import com.corpgovernment.hotel.booking.cache.model.HotelInfoModel;
import com.corpgovernment.hotel.booking.config.apollo.CheckAvailConfig;
import com.corpgovernment.hotel.booking.service.HotelManager;
import com.corpgovernment.hotel.product.model.ctrip.v2.CheckAvailV2ResponseType;
import com.corpgovernment.hotel.product.service.SupplierCheckAvailService;
import com.corpgovernment.util.AmountPrecisionUtil;
import com.corpgovernment.util.BizTypeContextUtil;
import com.ctrip.corp.obt.generic.constants.GenericConstants;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.Conditional;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.REQUEST_PARAM_NULL;

/**
 * 酒店可定查询（新）
 * CheckAvailService后续不再使用，前端只调该服务
 */
@Service
@Slf4j
public class CheckAvailV2ServiceImpl extends BaseService {


	@Autowired
	private HotelManager hotelManager;
	@Autowired
	private SupplierCheckAvailService supplierCheckAvailService;
	@Autowired
	private HotelSnapshotGateway hotelSnapshotGateway;
	@Autowired
	private CheckAvailConfig checkAvailConfig;

	private static final Integer CAN_BOOKING= 1 ;
	private static final Integer CAN_NOT_BOOKING = 2 ;
	private static final Integer CHANGE_PRICE = 3 ;
	Id checkAvailId = Metrics.REGISTRY.createId("hotel.booking.checkAvailV2");
	private static final String PRICE_UP = "供应商该房型房费每间夜上调约￥%s，请重新选择房型进行预订";
	private static final String PRICE_DOWN = "供应商该房型房费每间夜下调约￥%s，请重新选择房型进行预订";
    private static final String CHANGE_CANCEL_POLICY_DESC = "当前房型取消政策已发生变化，请注意查看！";
    private static final String CHANGE_EXTRA_TAX_DESC_V1 = "供应商该房型到店另付税/费变更为￥%s，是否继续预订？";
    private static final String CHANGE_EXTRA_TAX_DESC_V2 = "此房型需到店另付税/费变更为￥%s，费用以实际到店支付为准，可能会影响订单超标判断和实际报销金额，是否继续预订？";
    public CheckAvailV2ResponseVo checkAvail(CheckAvailV2RequestVo request,BaseUserInfo baseUserInfo) {
		try {
			// 校验请求参数
			checkRequestParam(request);
			initElkLog();
			addElkInfoLog("酒店可定查询");
			// 校验停留时间
			hotelManager.checkStateTime(false);
			// 获取产品快照
			ProductSnapshotModel hotelInfo = hotelSnapshotGateway.getHotelProductSnapshot(request.getToken(), request.getProductId(),
					 request.getSupplierCode(),request.getHotelId(),request.getRoomId(),request.getPaymentType());
			// 获取费用快照
			GetHotelFeeSnapshotResponse hotelFeeSnapshot = null;
			
			BookingConfigModel bookingConfig = hotelSnapshotGateway.getBookingConfigSnapshot(request.getToken());
			QueryParamModel queryParam = hotelSnapshotGateway.getQueryParam(request.getToken());
			HotelQueryContextModel hotelQueryContextModel = hotelSnapshotGateway.getHotelContextSnapshot(request.getToken());
			BizTypeContextUtil.saveBizTypeToContext(Optional.ofNullable(queryParam)
					.map(QueryParamModel::getProductType)
					.map(BizTypeEnum::getByCodeOrName)
					.orElse(null));
			addElkInfoLog("酒店缓存checkAvail" + JsonUtils.toJsonString(hotelInfo));

			// 可订查询
			LocalCheckAvailRequestBo requestBo = toCheckAvailRequest(hotelInfo, request, bookingConfig,hotelQueryContextModel,baseUserInfo);
			SupplierCheckAvailResultModel checkAvailResponse = supplierCheckAvailService.checkAvail(requestBo, BizTypeEnum.getByCodeOrName(queryParam.getProductType()));
			addElkInfoLog("可订查询结果：" + JsonUtils.toJsonString(checkAvailResponse));
			HotelFeeInfoModel hotelFeeInfoModel1 =
					buildHotelFeeSnapshot(checkAvailResponse,
							bookingConfig,
							hotelQueryContextModel,
							hotelInfo,
							queryParam,
							request.getSupplierCode(),
							request.getCorpPayType());
			log.info("hotelFeeInfoModel1:{}", hotelFeeInfoModel1);
			Boolean dailyOrTotal = dailyOrTotal(bookingConfig);
			CheckAvailV2ResponseVo checkAvailV2ResponseVo = Null.or(buildPriceChangeResponse(checkAvailResponse,hotelFeeSnapshot, hotelInfo, request.getInvokeSource(), dailyOrTotal,request.getSupplierCode()), new CheckAvailV2ResponseVo());

			// 可订异常
			if (checkAvailV2ResponseVo.getCode() == null || Objects.equals(checkAvailV2ResponseVo.getCode(), CAN_NOT_BOOKING)
					) {
				checkAvailV2ResponseVo.setCheckAvailFailInfo(CheckAvailV2ResponseVo.CheckAvailFailInfo.builder()
						.failCheckAvail(true)
						.failCheckAvailDesc(checkAvailV2ResponseVo.getMsg()).build());
			} else {
				checkAvailV2ResponseVo.setExtraTaxChangeInfo(buildExtraTaxChangeInfo(hotelInfo, hotelFeeSnapshot, hotelFeeInfoModel1, bookingConfig, hotelQueryContextModel, queryParam));
				checkAvailV2ResponseVo.setCancelPolicyChangeInfo(buildCancelPolicyChangeInfo(checkAvailResponse));
				checkAvailV2ResponseVo.setPriceChangeInfo(buildPriceChangeInfo(checkAvailV2ResponseVo));
				checkAvailV2ResponseVo.setNeedRefreshHotelDetail(Optional.ofNullable(checkAvailResponse).map(SupplierCheckAvailResultModel::getChangeCancelPolicy).orElse(false)
						|| Optional.ofNullable(checkAvailV2ResponseVo.getExtraTaxChangeInfo()).map(CheckAvailV2ResponseVo.ExtraTaxChangeInfo::getChangeExtraTax).orElse(false)
						|| Optional.ofNullable(checkAvailV2ResponseVo.getPriceChangeInfo()).map(CheckAvailV2ResponseVo.PriceChangeInfo::getChangePrice).orElse(false)
						|| Optional.ofNullable(checkAvailResponse).map(SupplierCheckAvailResultModel::getHourlyRoomInfo).map(HourlyRoomInfo::getHourlyRoom).orElse(false));
			}
			
			// 费用快照额外填充
			hotelFeeInfoModel1.setChangeInfo(ChangeInfoModel.builder()
					.failCheckAvail(Optional.ofNullable(checkAvailV2ResponseVo.getCheckAvailFailInfo()).map(CheckAvailV2ResponseVo.CheckAvailFailInfo::getFailCheckAvail).orElse(false))
					.changePrice(Optional.ofNullable(checkAvailV2ResponseVo.getPriceChangeInfo()).map(CheckAvailV2ResponseVo.PriceChangeInfo::getChangePrice).orElse(false))
					.changeExtraTax(Optional.ofNullable(checkAvailV2ResponseVo.getExtraTaxChangeInfo()).map(CheckAvailV2ResponseVo.ExtraTaxChangeInfo::getChangeExtraTax).orElse(false))
					.changeCancelPolicy(Optional.ofNullable(checkAvailV2ResponseVo.getCancelPolicyChangeInfo()).map(CheckAvailV2ResponseVo.CancelPolicyChangeInfo::getChangeCancelPolicy).orElse(false)).build());
			
			// 上一次超标结果
			hotelFeeInfoModel1.setLastOverLimit(Optional.ofNullable(hotelInfo)
					.map(ProductSnapshotModel::getBasicRoomInfo)
					.map(item -> item.get(0))
					.map(BasicRoomInfoModel::getRoomCardList)
					.map(item -> item.get(0))
					.map(RoomInfoModel::getOverLimitInfo)
					.map(OverLimitInfoModel::getOverLimitRuleNameList)
					.map(CollectionUtils::isNotEmpty).orElse(false));
			
			// 保存费用快照
			log.info("最终的费用快照 hotelFeeInfoModel={}", JsonUtils.toJsonString(hotelFeeInfoModel1));
			hotelSnapshotGateway.saveHotelFeeSnapshot(request.getToken(),hotelFeeInfoModel1);
			
			// 其他响应参数填充
			checkAvailV2ResponseVo.setNeedEmail(Optional.ofNullable(hotelFeeInfoModel1.getBookingRules())
					.map(HotelFeeInfoModel.BookingRules::getBillingGuestInfo)
					.map(HotelFeeInfoModel.BillingGuestInfo::getNeedEmail)
					.orElse(false));
			checkAvailV2ResponseVo.setNeedCertificate(Optional.ofNullable(hotelFeeInfoModel1.getBookingRules())
					.map(HotelFeeInfoModel.BookingRules::getCertificateInfo)
					.map(HotelFeeInfoModel.CertificateInfo::getNeedCertificate)
					.orElse(false));
			checkAvailV2ResponseVo.setSupportCertificateType(Optional.ofNullable(hotelFeeInfoModel1.getBookingRules())
					.map(HotelFeeInfoModel.BookingRules::getCertificateInfo)
					.map(HotelFeeInfoModel.CertificateInfo::getSupportCertificateType)
					.orElse(null));
			checkAvailV2ResponseVo.setSupportCertificateTypeList(Optional.ofNullable(hotelFeeInfoModel1.getBookingRules())
					.map(HotelFeeInfoModel.BookingRules::getCertificateInfo)
					.map(HotelFeeInfoModel.CertificateInfo::getSupportCertificateTypeList)
					.orElse(null));
			checkAvailV2ResponseVo.setHourlyRoomInfo(convertHourlyRoomInfo(Optional.ofNullable(checkAvailResponse)
					.map(SupplierCheckAvailResultModel::getHourlyRoomInfo)
					.orElse(null)));
			
			return checkAvailV2ResponseVo;
		} finally {
			log.info("CheckAvailService.checkAvail酒店可定查询提交{} request：{}{}{}{}", System.lineSeparator(), JsonUtils.toJsonString(request), System.lineSeparator(), System.lineSeparator(), this.getElkInfoLog());
			clearElkLog();
		}

    }
	
	private HourlyRoomInfoVo convertHourlyRoomInfo(HourlyRoomInfo hourlyRoomInfo) {
		if (hourlyRoomInfo == null || !Boolean.TRUE.equals(hourlyRoomInfo.getHourlyRoom())) {
			return null;
		}
		
		HourlyRoomInfoVo hourlyRoomInfo1 = new HourlyRoomInfoVo();
		hourlyRoomInfo1.setHourlyRoom(hourlyRoomInfo.getHourlyRoom());
		hourlyRoomInfo1.setDurationHour(hourlyRoomInfo.getDurationHour());
		hourlyRoomInfo1.setIntervalStartMinute(hourlyRoomInfo.getIntervalStartMinute());
		hourlyRoomInfo1.setIntervalEndMinute(hourlyRoomInfo.getIntervalEndMinute());
		hourlyRoomInfo1.setHourlyRoomDesc(hourlyRoomInfo.getHourlyRoomDesc());
		hourlyRoomInfo1.setAvailableTimeSlots(hourlyRoomInfo.getAvailableTimeSlots());
		hourlyRoomInfo1.setAvailablePeriodsDesc(hourlyRoomInfo.getAvailablePeriodsDesc());
		hourlyRoomInfo1.setHourlyRoomTip(hourlyRoomInfo.getHourlyRoomTip());
		return hourlyRoomInfo1;
	}

	/**
	 *
	 * @return
	 */
	private Boolean dailyOrTotal(BookingConfigModel bookingConfigModel){
		if (bookingConfigModel == null
				|| bookingConfigModel.getAllSwitch() == null
				|| CollectionUtils.isEmpty(bookingConfigModel.getAllSwitch().getSwitchInfoSoaMap())){
			return Boolean.FALSE;
		}
		SwitchModel switchModel = bookingConfigModel.getAllSwitch().getSwitchInfoSoaMap().get("hotel_price_control_strategy");
		if (switchModel == null || StringUtils.isBlank(switchModel.getValue())) {
			return Boolean.FALSE;
		}
		List<Integer> valueList = JsonUtils.parseArray(switchModel.getValue(), Integer.class);
		return !valueList.contains(1);


	}
	
	private CheckAvailV2ResponseVo.PriceChangeInfo buildPriceChangeInfo(CheckAvailV2ResponseVo checkAvailV2ResponseVo) {
		if (checkAvailV2ResponseVo != null && Objects.equals(checkAvailV2ResponseVo.getCode(), CHANGE_PRICE)) {
			return CheckAvailV2ResponseVo.PriceChangeInfo.builder()
					.changePrice(true)
					.changePriceDesc(checkAvailV2ResponseVo.getMsg())
					.build();
		}
		
		return CheckAvailV2ResponseVo.PriceChangeInfo.builder()
				.changePrice(false).build();
	}
	
	private CheckAvailV2ResponseVo.CancelPolicyChangeInfo buildCancelPolicyChangeInfo(SupplierCheckAvailResultModel checkAvailResponse) {
		Boolean changeCancelPolicy = Optional.ofNullable(checkAvailResponse).map(SupplierCheckAvailResultModel::getChangeCancelPolicy).orElse(null);
		if (Boolean.TRUE.equals(changeCancelPolicy)) {
			// 先不做取消政策提醒
			return CheckAvailV2ResponseVo.CancelPolicyChangeInfo.builder()
					.changeCancelPolicy(false)
					.changeCancelPolicyDesc(CHANGE_CANCEL_POLICY_DESC)
					.build();
		}
		
		return CheckAvailV2ResponseVo.CancelPolicyChangeInfo.builder()
				.changeCancelPolicy(false).build();
	}
	
	private CheckAvailV2ResponseVo.ExtraTaxChangeInfo buildExtraTaxChangeInfo(ProductSnapshotModel hotelInfo,
																			  GetHotelFeeSnapshotResponse hotelFeeSnapshot,
																			  HotelFeeInfoModel hotelFeeInfoModel1,
																			  BookingConfigModel bookingConfig,
																			  HotelQueryContextModel hotelQueryContextModel,
																			  QueryParamModel queryParam) {
		log.info("hotelInfo={} hotelFeeInfoModel1={}", JsonUtils.toJsonString(hotelInfo), JsonUtils.toJsonString(hotelFeeInfoModel1));
		// 获取旧到店另付税费总价
		BigDecimal oldTotalExtraTax = Null.or(getOldTotalExtraTax(hotelInfo), BigDecimal.ZERO);
		// 获取新到店另付税费总价
		BigDecimal newTotalExtraTax = Null.or(getTotalExtraTax(ProductSnapshotConverter.INSTANCE.convert(hotelFeeInfoModel1)), BigDecimal.ZERO);
		log.info("oldTotalExtraTax={} newTotalExtraTax={}", oldTotalExtraTax, newTotalExtraTax);
		
		// 到店另付税费变动
		if (oldTotalExtraTax.compareTo(newTotalExtraTax) != 0) {
			return CheckAvailV2ResponseVo.ExtraTaxChangeInfo.builder()
					.changeExtraTax(true)
					.changeExtraTaxDesc(getChangeExtraTaxDesc(newTotalExtraTax, bookingConfig, hotelQueryContextModel, queryParam)).build();
		}
		
		return CheckAvailV2ResponseVo.ExtraTaxChangeInfo.builder()
				.changeExtraTax(false).build();
	}
	
	private String getChangeExtraTaxDesc(BigDecimal totalExtraTax,
										 BookingConfigModel bookingConfig,
										 HotelQueryContextModel hotelQueryContextModel,
										 QueryParamModel queryParam) {
		BizTypeEnum bizTypeEnum = Optional.ofNullable(queryParam)
				.map(QueryParamModel::getProductType)
				.map(BizTypeEnum::getByCodeOrName)
				.orElse(null);
		Boolean overseasHotelControlIncludeExtraTax = bookingConfig == null ? Boolean.FALSE : getOverseasHotelControlIncludeExtraTax(bookingConfig.getAllSwitch(), bizTypeEnum);
		String corpPayType = Optional.ofNullable(hotelQueryContextModel)
				.map(HotelQueryContextModel::getCorpPayType)
				.orElse(null);
		
		if (Boolean.TRUE.equals(overseasHotelControlIncludeExtraTax) && StringUtils.equalsIgnoreCase(corpPayType, CorpPayTypeEnum.PUB.getType())) {
			return StringUtils.format(CHANGE_EXTRA_TAX_DESC_V2, AmountPrecisionUtil.toStringHalfEven(totalExtraTax, 2));
		}
		
		return StringUtils.format(CHANGE_EXTRA_TAX_DESC_V1, AmountPrecisionUtil.toStringHalfEven(totalExtraTax, 2));
	}
	
	private Boolean getOverseasHotelControlIncludeExtraTax(AllSwitchModel allSwitch, BizTypeEnum bizTypeEnum) {
		if (!Objects.equals(BizTypeEnum.HOTEL_INTL, bizTypeEnum)) {
			return Boolean.FALSE;
		}
		List<Integer> switchValue = getSwitchValue(allSwitch, TravelAttributeEnum.OVERSEAS_HOTEL_CONTROL_INCLUDE_EXTRA_TAX.getCode());
		return CollectionUtils.isNotEmpty(switchValue) && switchValue.get(0) != null && switchValue.get(0) == 1;
	}
	
	private List<Integer> getSwitchValue(AllSwitchModel allSwitchDto, String switchName) {
		if (allSwitchDto == null || CollectionUtils.isEmpty(allSwitchDto.getSwitchInfoSoaMap())) {
			return null;
		}
		
		SwitchModel switchDto = allSwitchDto.getSwitchInfoSoaMap().get(switchName);
		if (switchDto == null || StringUtils.isBlank(switchDto.getValue())) {
			return null;
		}
		return JsonUtils.parseArray(switchDto.getValue(), Integer.class);
	}
	
	private CheckAvailV2ResponseVo buildPriceChangeResponse(SupplierCheckAvailResultModel checkAvailResponse,
															GetHotelFeeSnapshotResponse hotelFeeSnapshot,
															ProductSnapshotModel hotelInfo,String invokeSource, Boolean dailyOrTotal, String supplierCode) {
		if (Objects.isNull(checkAvailResponse)){
			return buildResponse(CAN_NOT_BOOKING, "该房型暂时不可预订，请稍后再试或预订其他房型", null, supplierCode);
		}
		if("2".equals(checkAvailResponse.getCheckCode())){
			if(StringUtils.isBlank(checkAvailResponse.getFailedReason())){
				return buildResponse(CAN_NOT_BOOKING, "该房型暂时不可预订，请稍后再试或预订其他房型", null,supplierCode);
			}
			return buildResponse(CAN_NOT_BOOKING, checkAvailResponse.getFailedReason(), null,supplierCode);
		}
		if (Objects.isNull(checkAvailResponse) || StringUtils.isNotBlank(checkAvailResponse.getFailedReason())  ||
				Objects.isNull(checkAvailResponse.getRoomInfo()) || CollectionUtils.isEmpty(checkAvailResponse.getRoomDailyInfoList())){
			return buildResponse(CAN_NOT_BOOKING, "该房型暂时不可预订，请稍后再试或预订其他房型", null,supplierCode);
		}
		ChangePriceResultBo checkResult = this.changePrice(hotelInfo,
				hotelFeeSnapshot,
				checkAvailResponse,
				invokeSource,
				dailyOrTotal);
		if (checkResult.getChangePrice()){
			return buildResponse(CHANGE_PRICE, null, checkResult, supplierCode);
		}else {
			return buildResponse(CAN_BOOKING, null, checkResult, supplierCode);
		}
	}
	
	private BigDecimal getTotalExtraTax(HotelFeeInfoDTO hotelFeeInfoDTO) {
		List<TaxFeeType> taxFeeTypeList = Optional.ofNullable(hotelFeeInfoDTO)
				.map(HotelFeeInfoDTO::getTaxInfo)
				.map(TaxInfoType::getTaxFeeInfoList).orElse(null);
		if (CollectionUtils.isEmpty(taxFeeTypeList)) {
			return null;
		}
		
		BigDecimal totalExtraTax = BigDecimal.ZERO;
		for (TaxFeeType taxFeeType : taxFeeTypeList) {
			if (taxFeeType == null || !Boolean.FALSE.equals(taxFeeType.getIncludeInTotalPrice()) || taxFeeType.getSellPrice() == null || taxFeeType.getSellPrice().getPrice() == null) {
				continue;
			}
			totalExtraTax = totalExtraTax.add(taxFeeType.getSellPrice().getPrice());
		}
		return totalExtraTax;
	}
	
	private BigDecimal getOldTotalExtraTax(ProductSnapshotModel hotelInfo) {
		// 没有费用快照取产品快照
		return Optional.ofNullable(hotelInfo)
				.map(ProductSnapshotModel::getBasicRoomInfo)
				.map(item -> item.get(0))
				.map(BasicRoomInfoModel::getRoomCardList)
				.map(item -> item.get(0))
				.map(RoomInfoModel::getRoomPrice)
				.map(RoomPriceInfo::getTotalExtraTax).orElse(null);
	}
	
	private void checkRequestParam(CheckAvailV2RequestVo request) {
		if (request == null) {
			throw new CorpBusinessException(REQUEST_PARAM_NULL, "request");
		}
		if (StringUtils.isBlank(request.getToken())) {
			throw new CorpBusinessException(REQUEST_PARAM_NULL, "token");
		}
		if (StringUtils.isBlank(request.getRoomId())) {
			throw new CorpBusinessException(REQUEST_PARAM_NULL, "roomId");
		}
		if (StringUtils.isBlank(request.getHotelId())) {
			throw new CorpBusinessException(REQUEST_PARAM_NULL, "hotelId");
		}

	}
	private HotelFeeInfoModel buildHotelFeeSnapshot(SupplierCheckAvailResultModel localCheckAvailResponseBo, BookingConfigModel bookingConfig,
													HotelQueryContextModel hotelQueryContextModel, ProductSnapshotModel hotelInfo, QueryParamModel queryParam, String supplierCode, String corpPaytype){

		HotelFeeInfoModel hotelFeeInfoModel = new HotelFeeInfoModel();
		hotelFeeInfoModel.setRoomDailyPriceTypeList(buildDailyRoomInfo(localCheckAvailResponseBo.getRoomDailyInfoList()));
		hotelFeeInfoModel.setCancelPenalties(buildCancelPenaltyModel(localCheckAvailResponseBo));
		hotelFeeInfoModel.setTaxInfo(buildTaxInfoModel(localCheckAvailResponseBo,bookingConfig,hotelQueryContextModel));
		hotelFeeInfoModel.setBonusPointInfo(buildBonusPointInfoModel(localCheckAvailResponseBo));
		hotelFeeInfoModel.setInvoiceInfo(buildInvoiceInfoModel(localCheckAvailResponseBo));
		hotelFeeInfoModel.setPayTypeList(buildPayTypeList(localCheckAvailResponseBo,bookingConfig));
		hotelFeeInfoModel.setBookingRules(getBookingRules(localCheckAvailResponseBo, queryParam));
		hotelFeeInfoModel.setHotelLimitInformationType(buildHotelLimitInformationType(localCheckAvailResponseBo));
		hotelFeeInfoModel.setTimeInformationType(buildTimeInformationType(localCheckAvailResponseBo));
		hotelFeeInfoModel.setAdditionalSupplierInfo(Optional.ofNullable(localCheckAvailResponseBo.getRoomInfo())
				.map(SupplierCheckAvailResultModel.RoomItem::getAdditionalSupplierInfo)
				.orElse(null));
		hotelFeeInfoModel.setRemarkInfo(buildRemarkInfo(localCheckAvailResponseBo));
		hotelFeeInfoModel.setLadderDeductionInfoList(buildLadderDeductionInfoModelList(localCheckAvailResponseBo));
		hotelFeeInfoModel.setUrgentBooking(Null.or(queryParam, QueryParamModel::getUrgentApply));
		hotelFeeInfoModel.setUrgentPayType(bookingConfig.getUrgentPayType());
		hotelFeeInfoModel.setSupplierTotalPrice(buildSupplierTotalPrice(localCheckAvailResponseBo));
		hotelFeeInfoModel.setMealTypeEnum(Optional.ofNullable(localCheckAvailResponseBo)
				.map(SupplierCheckAvailResultModel::getRoomInfo)
				.map(SupplierCheckAvailResultModel.RoomItem::getMealTypeEnum)
				.orElse(null));
		hotelFeeInfoModel.setServiceChargeInfoList(convertServiceChargeInfoList(localCheckAvailResponseBo.getServiceChargeInfoList()));
		return hotelFeeInfoModel;
	}
	
	private List<HotelFeeInfoModel.ServiceChargeInfo> convertServiceChargeInfoList(List<SupplierCheckAvailResultModel.ServiceChargeInfo> serviceChargeInfoList) {
		if (CollectionUtils.isEmpty(serviceChargeInfoList)) {
			return null;
		}
		
		return serviceChargeInfoList.stream()
				.map(this::convertServiceChargeInfo)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
	}
	
	private HotelFeeInfoModel.ServiceChargeInfo convertServiceChargeInfo(SupplierCheckAvailResultModel.ServiceChargeInfo item) {
		if (item == null) {
			return null;
		}
		
		return  HotelFeeInfoModel.ServiceChargeInfo.builder()
				.payTypeEnum(item.getPayTypeEnum())
				.totalServiceCharge(item.getTotalServiceCharge())
				.serviceChargeStrategyEnum(item.getServiceChargeStrategyEnum())
				.serviceChargeStrategyValue(item.getServiceChargeStrategyValue())
				.avgServiceCharge(item.getAvgServiceCharge())
				.build();
	}
	
	private PriceInfoModel buildSupplierTotalPrice(SupplierCheckAvailResultModel localCheckAvailResponseBo) {
		BigDecimal sellPrice = Optional.ofNullable(localCheckAvailResponseBo)
				.map(SupplierCheckAvailResultModel::getRoomInfo)
				.map(SupplierCheckAvailResultModel.RoomItem::getCnyAmount).orElse(null);
		if (sellPrice == null) {
			return null;
		}
		
		PriceInfoModel priceInfoModel = new PriceInfoModel();
		priceInfoModel.setCurrency("CNY");
		priceInfoModel.setPrice(sellPrice);
		return priceInfoModel;
	}
	private CancelPenaltyModel buildCancelPenaltyModel(SupplierCheckAvailResultModel localCheckAvailResponseBo){
		if (localCheckAvailResponseBo == null ||
				localCheckAvailResponseBo.getRoomInfo() == null ||
				localCheckAvailResponseBo.getRoomInfo().getCancelPenalties() == null){
			return null;
		}
		CancelPenaltyModel cancelPenaltyModel= new CancelPenaltyModel();
		cancelPenaltyModel.setFreeCancelPolicySceneType(localCheckAvailResponseBo.getRoomInfo().getCancelPenalties().getFreeCancelPolicySceneType());
		cancelPenaltyModel.setPolicyType(localCheckAvailResponseBo.getRoomInfo().getCancelPenalties().getPolicyType());
		return cancelPenaltyModel;
	}
	
	private RemarkInfoModel buildRemarkInfo(SupplierCheckAvailResultModel localCheckAvailResponseBo){
		if (localCheckAvailResponseBo == null || localCheckAvailResponseBo.getRoomInfo() == null){
			return null;
		}
		RemarkInfoModel  remarkInfoModel = new RemarkInfoModel();
		remarkInfoModel.setReceiveTextRemark(localCheckAvailResponseBo.getRoomInfo().getReceiveTextRemark());
		List<SupplierCheckAvailResultModel.Remark> remarkList = localCheckAvailResponseBo.getRoomInfo().getRemarkList();
		if (CollectionUtils.isNotEmpty(remarkList)){
			remarkInfoModel.setRemarkList(remarkList.stream().map(t->{
				RemarkModel remarkModel = new RemarkModel();
				remarkModel.setId(t.getId());
				remarkModel.setDesc(t.getDesc());
				remarkModel.setTitle(t.getTitle());
				remarkModel.setNeedUserValue(t.getNeedUserValue());
				remarkModel.setKey(t.getKey());
				remarkModel.setDefaultOption(t.getDefaultOption());
				return remarkModel;
			}).collect(Collectors.toList()));
		}
		return remarkInfoModel;
	}

	private TimeInformationModel buildTimeInformationType(SupplierCheckAvailResultModel localCheckAvailResponseBo){
		if (localCheckAvailResponseBo == null || localCheckAvailResponseBo.getRoomInfo() == null){
			return null;
		}
		TimeInformationModel  timeInformationModel = new TimeInformationModel();
		timeInformationModel.setLastArrivalTime(localCheckAvailResponseBo.getRoomInfo().getLastArrivalTime());
		timeInformationModel.setEarlyArrivalTime(localCheckAvailResponseBo.getRoomInfo().getEarlyArrivalTime());
		timeInformationModel.setLastCancelTime(localCheckAvailResponseBo.getRoomInfo().getLastCancelTime());
		timeInformationModel.setHoldTime(localCheckAvailResponseBo.getRoomInfo().getHoldTime());
		return timeInformationModel;
	}

	private List<LadderDeductionInfoModel> buildLadderDeductionInfoModelList(SupplierCheckAvailResultModel localCheckAvailResponseBo){
		if (localCheckAvailResponseBo == null ||
				localCheckAvailResponseBo.getRoomInfo() == null ||
				CollectionUtils.isEmpty(localCheckAvailResponseBo.getRoomInfo().getLadderDeductionInfoList())){
			return null;
		}
		return localCheckAvailResponseBo.getRoomInfo().getLadderDeductionInfoList().stream().map(t->{
			LadderDeductionInfoModel ladderDeductionInfoModel = new LadderDeductionInfoModel();
			ladderDeductionInfoModel.setDeductionType(t.getDeductionType());
			LadderDeductionDetailModel ladderDeductionDetailModel = new LadderDeductionDetailModel();
			ladderDeductionDetailModel.setDeductionRatio(t.getLadderDeductionInfo().getDeductionRatio());
			ladderDeductionDetailModel.setAmount(t.getLadderDeductionInfo().getAmount());
			ladderDeductionDetailModel.setCurrency(t.getLadderDeductionInfo().getCurrency());
			ladderDeductionDetailModel.setOriginalAmount(t.getLadderDeductionInfo().getOriginalAmount());
			ladderDeductionDetailModel.setOriginalCurrency(t.getLadderDeductionInfo().getOriginalCurrency());
			ladderDeductionDetailModel.setStartDeductTime(t.getLadderDeductionInfo().getStartDeductTime());
			ladderDeductionDetailModel.setEndDeductTime(t.getLadderDeductionInfo().getEndDeductTime());
			ladderDeductionInfoModel.setLadderDeductionInfo(ladderDeductionDetailModel);
			return ladderDeductionInfoModel;
		}).collect(Collectors.toList());
	}

	private HotelLimitInformationModel buildHotelLimitInformationType(SupplierCheckAvailResultModel localCheckAvailResponseBo){
		if (localCheckAvailResponseBo == null || localCheckAvailResponseBo.getRoomInfo() == null){
			return null;
		}
		HotelLimitInformationModel  hotelLimitInformationModel = new HotelLimitInformationModel();
		hotelLimitInformationModel.setMinBookingRoomNum(localCheckAvailResponseBo.getRoomInfo().getMinBookingRoomNum());
		hotelLimitInformationModel.setMaxBookingRoomNum(localCheckAvailResponseBo.getRoomInfo().getMaxBookingRoomNum());
		hotelLimitInformationModel.setGuestPerson(localCheckAvailResponseBo.getRoomInfo().getGuestPerson());
		return hotelLimitInformationModel;
	}
	private HotelFeeInfoModel.BookingRules getBookingRules(SupplierCheckAvailResultModel response, QueryParamModel queryParam) {
		if (response.getBookingRules() == null) {
			return null;
		}
		HotelFeeInfoModel.BookingRules bookingRules = new HotelFeeInfoModel.BookingRules();
		HotelFeeInfoModel.BillingGuestInfo billingGuestInfo = new HotelFeeInfoModel.BillingGuestInfo();
		billingGuestInfo.setGuestsNameLanguages(Null.or(response.getBookingRules().getBillingGuestInfo(),t->t.getGuestsNameLanguages()));
		billingGuestInfo.setNeedEmail(getNeedEmail(response, queryParam));
		bookingRules.setBillingGuestInfo(billingGuestInfo);
		bookingRules.setCertificateInfo(buildCertificateInfo(response));
		bookingRules.setMinLOS(response.getBookingRules().getMinLOS());
		return bookingRules;
	}
	
	private Boolean getNeedEmail(SupplierCheckAvailResultModel response, QueryParamModel queryParam) {
		return Optional.ofNullable(response)
				.map(SupplierCheckAvailResultModel::getBookingRules)
				.map(SupplierCheckAvailResultModel.BookingRules::getBillingGuestInfo)
				.map(SupplierCheckAvailResultModel.BillingGuestInfo::getNeedEmail)
				.orElse(false);
	}
	
	private HotelFeeInfoModel.CertificateInfo buildCertificateInfo(SupplierCheckAvailResultModel response) {
		List<String> supportCertificateTypeList = Optional.ofNullable(response)
				.map(SupplierCheckAvailResultModel::getRoomInfo)
				.map(SupplierCheckAvailResultModel.RoomItem::getSpecialOfferRoomInfo)
				.map(SupplierCheckAvailResultModel.SpecialOfferRoomInfo::getSupportCertificateType)
				.orElse(null);
		if (CollectionUtils.isNotEmpty(supportCertificateTypeList)) {
			supportCertificateTypeList = supportCertificateTypeList.stream()
					.filter(Objects::nonNull)
					.map(SupportCertificateTypeEnum::getEnum)
					.filter(Objects::nonNull)
					.map(SupportCertificateTypeEnum::getNumCode)
					.collect(Collectors.toList());
			return HotelFeeInfoModel.CertificateInfo.builder()
					.needCertificate(true)
					.supportCertificateType(supportCertificateTypeList.get(0))
					.supportCertificateTypeList(supportCertificateTypeList).build();
		}
		
		Boolean needCertificate = Optional.ofNullable(response)
				.map(SupplierCheckAvailResultModel::getBookingRules)
				.map(SupplierCheckAvailResultModel.BookingRules::getCertificateInfo)
				.map(SupplierCheckAvailResultModel.CertificateInfo::getNeedCertificate)
				.orElse(null);
		String supportCertificateType = Optional.ofNullable(response)
				.map(SupplierCheckAvailResultModel::getBookingRules)
				.map(SupplierCheckAvailResultModel.BookingRules::getCertificateInfo)
				.map(SupplierCheckAvailResultModel.CertificateInfo::getSupportCertificateType)
				.orElse(null);
		return HotelFeeInfoModel.CertificateInfo.builder()
				.needCertificate(Boolean.TRUE.equals(needCertificate) && StringUtils.isNotBlank(supportCertificateType))
				.supportCertificateType(supportCertificateType)
				.supportCertificateTypeList(Optional.ofNullable(supportCertificateType)
						.map(item -> item.split(","))
						.map(Arrays::asList)
						.orElse(null)).build();
	}
	
	
	private List<PayTypeInfoModel> buildPayTypeList(SupplierCheckAvailResultModel localCheckAvailResponseBo,BookingConfigModel bookingConfig){
		if (localCheckAvailResponseBo == null){
			return null;
		}
		return bookingConfig.getPayInfoList();

	}
	private InvoiceInfoModel buildInvoiceInfoModel(SupplierCheckAvailResultModel localCheckAvailResponseBo){
		if (localCheckAvailResponseBo == null){
			return null;
		}
		InvoiceInfoModel invoiceInfoModel = new InvoiceInfoModel();
		if (localCheckAvailResponseBo.getRoomInfo() != null){
			invoiceInfoModel.setInvoiceType(localCheckAvailResponseBo.getRoomInfo().getSupportInvoiceTypeList());
		}
		return invoiceInfoModel;
	}

	private BonusPointInfoModel buildBonusPointInfoModel(SupplierCheckAvailResultModel localCheckAvailResponseBo){
		if(localCheckAvailResponseBo == null || localCheckAvailResponseBo.getRoomInfo() == null || localCheckAvailResponseBo.getRoomInfo().getBonusPointInfo() == null){
			return null;
		}
		SupplierCheckAvailResultModel.BonusPointInfo bonusPointInfo = localCheckAvailResponseBo.getRoomInfo().getBonusPointInfo();
		BonusPointInfoModel tmp = new BonusPointInfoModel();
		tmp.setGroupId(bonusPointInfo.getGroupId());
		tmp.setGroupName(bonusPointInfo.getGroupName());
		tmp.setBonusPointType(bonusPointInfo.getBonusPointType());
		tmp.setFillPageRuleDescList(bonusPointInfo.getFillPageRuleDescList());
		tmp.setOrderDetailPageRuleDescList(bonusPointInfo.getOrderDetailPageRuleDescList());
		return tmp;
	}

	private TaxInfoModel buildTaxInfoModel(SupplierCheckAvailResultModel localCheckAvailResponseBo, BookingConfigModel bookingConfig, HotelQueryContextModel hotelQueryContextModel) {
		if (localCheckAvailResponseBo.getTaxInfo() == null){
			return null;
		}
		TaxInfoModel taxInfoModel = new TaxInfoModel();
		SupplierCheckAvailResultModel.TaxInfo taxInfo =localCheckAvailResponseBo.getTaxInfo();
		taxInfoModel.setExcludeTaxFeeDesc(taxInfo.getExcludeTaxFeeDesc());
		taxInfoModel.setIncludeTaxFeeDesc(taxInfo.getIncludeTaxFeeDesc());
		if (CollectionUtils.isNotEmpty(taxInfo.getTaxFeeInfoList())){
			List<TaxFeeModel> taxFeeModels = taxInfo.getTaxFeeInfoList().stream().map(t->{
				TaxFeeModel taxFeeInfoModel = new TaxFeeModel();
				taxFeeInfoModel.setTaxId(t.getTaxId());
				PriceInfoModel priceInfoModel = new PriceInfoModel();
				priceInfoModel.setCurrency(Null.or(t.getSellPrice(),o->o.getCurrency()));
				priceInfoModel.setPrice(Null.or(t.getSellPrice(),o->o.getPrice()));
				taxFeeInfoModel.setSellPrice(priceInfoModel);

				PriceInfoModel priceInfoModel1 = new PriceInfoModel();
				priceInfoModel1.setCurrency(Null.or(t.getOriginalPrice(),o->o.getCurrency()));
				priceInfoModel1.setPrice(Null.or(t.getOriginalPrice(),o->o.getPrice()));
				taxFeeInfoModel.setOriginalPrice(priceInfoModel1);
				taxFeeInfoModel.setPercentage(t.getPercentage());
				taxFeeInfoModel.setTaxTypeName(t.getTaxTypeName());
				taxFeeInfoModel.setChargeMode(t.getChargeMode());
				taxFeeInfoModel.setTaxFeeCalculateType(t.getTaxFeeCalculateType());
				taxFeeInfoModel.setIncludeInTotalPrice(t.getIncludeInTotalPrice());
				return taxFeeInfoModel;
			}).collect(Collectors.toList());
			taxInfoModel.setTaxFeeInfoList(taxFeeModels);
		}

		return taxInfoModel;
	}

	private List<RoomDailyPriceModel> buildDailyRoomInfo(List<SupplierCheckAvailResultModel.RoomDailyInfo> dailyRoomPriceModelList){
		if (CollectionUtils.isEmpty(dailyRoomPriceModelList)){
			return Collections.emptyList();
		}
		return dailyRoomPriceModelList.stream().map(t->{
			RoomDailyPriceModel roomDailyPriceModel = new RoomDailyPriceModel();
			roomDailyPriceModel.setEffectDate(t.getEffectDate());
			PriceInfoModel priceInfoModel = new PriceInfoModel();
			priceInfoModel.setPrice(t.getSellPrice());
			priceInfoModel.setCurrency(t.getCurrency());
			roomDailyPriceModel.setSellPrice(priceInfoModel);
			roomDailyPriceModel.setMealCount(t.getMeals());
			return roomDailyPriceModel;
		}).collect(Collectors.toList());
	}

	/**
	 * 是否变价判断逻辑
	 * dailyOrTotal 看每日房价变化还是总价变化
	 * @return
	 */
	private ChangePriceResultBo changePrice(ProductSnapshotModel hotelInfo, GetHotelFeeSnapshotResponse hotelFeeSnapshot,
								SupplierCheckAvailResultModel checkAvailResponseBo,String invokeSource, Boolean dailyOrTotal) {
		// 供应商是否变价
		boolean isSupplierChange = checkAvailResponseBo.getChangePrice() != null && checkAvailResponseBo.getChangePrice();
		// 供应商没变价直接返回未变价
		if (!isSupplierChange){
			return new ChangePriceResultBo(false);
		}
		// 判断总价情况下，总价是否变化
		if (!dailyOrTotal){
			return isTotalPriceChange(hotelInfo, checkAvailResponseBo);
		} else {
			// 判断每日均价情况下，筛选出房价变化的日期
			return isDailyPriceChange(hotelInfo, checkAvailResponseBo);
		}
	}

	/**
	 * 总价是否有变化，以及总价的变化金额
	 * @param hotelInfo
	 * @param checkAvailResponseBo
	 * @return
	 */
	private ChangePriceResultBo isTotalPriceChange(ProductSnapshotModel hotelInfo, SupplierCheckAvailResultModel checkAvailResponseBo){
		BigDecimal originalTotalPrice = BigDecimal.ZERO;
		RoomPriceInfo roomPriceInfo = Conditional.ofEmptyAble(hotelInfo.getBasicRoomInfo()).map(t->t.get(0)).map(BasicRoomInfoModel::getRoomCardList).map(t->t.get(0)).map(RoomInfoModel::getRoomPrice).orElse(null);

		for (DailyRoomPriceModel roomDailyInfo : roomPriceInfo.getDailyRateList()) {
			originalTotalPrice = originalTotalPrice.add(roomDailyInfo.getAvgPriceIncludeTax().getPrice());
		}
		// 现价
		BigDecimal presentTotalPrice = BigDecimal.ZERO;
		for (SupplierCheckAvailResultModel.RoomDailyInfo roomDailyInfo : checkAvailResponseBo.getRoomDailyInfoList()) {
			presentTotalPrice = presentTotalPrice.add(roomDailyInfo.getSellPrice());
		}
		// 原总价现总价相等 认为未变价
		if (presentTotalPrice.compareTo(originalTotalPrice) == 0){
			return new ChangePriceResultBo(false);
		}
		BigDecimal price =  presentTotalPrice.subtract(originalTotalPrice);
		BigDecimal changeAveragePrice = price.divide(BigDecimal.valueOf(checkAvailResponseBo.getRoomDailyInfoList().size()), 0, RoundingMode.CEILING);
		ChangePriceResultBo changePriceResultBo = new ChangePriceResultBo();
		changePriceResultBo.setChangePrice(true);
		changePriceResultBo.setPrice(changeAveragePrice);
		return changePriceResultBo;
	}

	/**
	 * 每日房价是否有变化，以及变化的日期
	 * @param hotelInfo
	 * @param checkAvailResponseBo
	 * @return
	 */
	private ChangePriceResultBo isDailyPriceChange(ProductSnapshotModel hotelInfo, SupplierCheckAvailResultModel checkAvailResponseBo){
		ChangePriceResultBo changePriceResultBo = new ChangePriceResultBo();
		changePriceResultBo.setChangePrice(true);
		RoomPriceInfo roomPriceInfo = Conditional.ofEmptyAble(hotelInfo.getBasicRoomInfo()).map(t->t.get(0)).map(BasicRoomInfoModel::getRoomCardList).map(t->t.get(0)).map(RoomInfoModel::getRoomPrice).orElse(null);

		Map<String, BigDecimal> dailyPriceMap = roomPriceInfo.getDailyRateList().stream()
				.filter(t->t.getAvgPriceIncludeTax() != null && t.getAvgPriceIncludeTax().getPrice() != null)
				.collect(Collectors.toMap(DailyRoomPriceModel::getDate, t -> t.getAvgPriceIncludeTax().getPrice()));
		if (CollectionUtils.isNotEmpty(checkAvailResponseBo.getChangePriceDetailList())){
			List<DailyRoomChangePriceBo> dailyRoomChangePriceBoList = new ArrayList<>();
			checkAvailResponseBo.getChangePriceDetailList().forEach(nowPrice -> {
				BigDecimal datePrice = dailyPriceMap.get(nowPrice.getDate());
				if (datePrice != null) {
					// 取出变价的日期的房价
					if (datePrice.compareTo(nowPrice.getPrice()) != 0){
						DailyRoomChangePriceBo dailyRoomPriceModel = new DailyRoomChangePriceBo();
						dailyRoomPriceModel.setDate(nowPrice.getDate());
						dailyRoomPriceModel.setChangePrice(nowPrice.getPrice().subtract(datePrice));
						dailyRoomChangePriceBoList.add(dailyRoomPriceModel);
					}
				}
			});
			changePriceResultBo.setDailyRoomChangePriceList(dailyRoomChangePriceBoList);
		}
		if (CollectionUtils.isEmpty(changePriceResultBo.getDailyRoomChangePriceList())){
			changePriceResultBo.setChangePrice(false);
		}
		return changePriceResultBo;
	}


	private CheckAvailV2ResponseVo buildResponse(Integer code, String reason, ChangePriceResultBo changePrice,String supplierCode) {
		CheckAvailV2ResponseVo response = new CheckAvailV2ResponseVo();
		response.setCode(code);
		if (StringUtils.isNotBlank(reason)){
			response.setMsg(reason);
			return response;
		}
		if (changePrice != null){
			if (changePrice.getChangePrice()){
				// 总价变价
				if ( changePrice.getPrice() != null){
					if (changePrice.getPrice().compareTo(BigDecimal.ZERO) > 0){
						response.setMsg(String.format(PRICE_UP, AmountPrecisionUtil.showAndToStringByBizTypeContext(changePrice.getPrice())));
					}else {
						response.setMsg(String.format(PRICE_DOWN, AmountPrecisionUtil.showAndToStringByBizTypeContext(changePrice.getPrice().abs())));
					}
				}
				// 每日房价变价文案拼接
				if (CollectionUtils.isNotEmpty(changePrice.getDailyRoomChangePriceList())){
					response.setMsg(generateBookingPrompt(changePrice.getDailyRoomChangePriceList(),code,supplierCode));
				}
			}
		}
		if (Objects.equals(code, CHANGE_PRICE) &&
				!checkAvailConfig.getCanBookingSupplierCode().contains(supplierCode)){
			response.setCode(CAN_NOT_BOOKING);
		}
		return response;
	}

	private  String generateBookingPrompt(List<DailyRoomChangePriceBo> priceChangeList,Integer code,String supplierCode) {
		StringBuilder sb = new StringBuilder("供应商该房型房费");

		for (int i = 0; i < priceChangeList.size(); i++) {
			DailyRoomChangePriceBo bo = priceChangeList.get(i);
			String changeDirection = bo.getChangePrice().compareTo(BigDecimal.ZERO) < 0? "下调" : "上调";
			sb.append(bo.getDate()).append(" ").append(changeDirection).append("约¥").append(AmountPrecisionUtil.showAndToStringByBizTypeContext(bo.getChangePrice().abs())).append(i < priceChangeList.size() - 1? "、" : "");
		}
		// 途美等变价降级不可订文案改为请重新选择房型
		if (Objects.equals(code, CHANGE_PRICE) &&
				!checkAvailConfig.getCanBookingSupplierCode().contains(supplierCode)){
			sb.append("，请重新选择房型");
		} else {
			sb.append("，是否继续预订？");
		}



		return sb.toString();
	}



	private CheckAvailResponseVo.FieldObject checkAllAmount(CheckAvailRequestVo request, HotelInfoModel hotelInfo,
															ApplyTripTrafficVerifyResponse trafficVerifyResponse, List<CheckAvailResponseVo.FieldObject> exceedApplyFieldList) {
		BigDecimal balanceAmount = BigDecimal.ZERO;
		if(trafficVerifyResponse.getApplyTripTraffic().getAmount() != null
				&& trafficVerifyResponse.getApplyTripStock() != null) {
			balanceAmount = trafficVerifyResponse.getApplyTripTraffic().getAmount()
					.subtract(trafficVerifyResponse.getApplyTripStock().getUsedAmount());
		}
		BigDecimal allAmount = BigDecimal.ZERO;
		// 计算间隔天数
		Long sum = LocalDate.parse(hotelInfo.getCheckInDate()).until(LocalDate.parse(hotelInfo.getCheckOutDate()), ChronoUnit.DAYS);
		log.info("申请单间隔天数{},房间数据{}", sum, request.getRoomQuantity());
		// 多房间多晚相乘
		if (request.getRoomQuantity() != null) {
			sum = sum * request.getRoomQuantity();
		}
		// 混付
		if(StringUtil.equals(request.getPayInfoCode(), PayTypeEnum.MIXPAY.getType())) {
			// 公付金额
			allAmount = hotelInfo.getAmountHigh().multiply(BigDecimal.valueOf(sum));
		} else {
			// 总金额
			allAmount = hotelInfo.getPrice().multiply(BigDecimal.valueOf(sum));
		}
		if(allAmount.compareTo(balanceAmount) > 0) {
			// 超标
			CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo
					.FieldObject("amount", "总金额", "￥" + allAmount.stripTrailingZeros().toPlainString());
			exceedApplyFieldList.add(fieldObject);
		}

		CheckAvailResponseVo.FieldObject fieldObject = new CheckAvailResponseVo.FieldObject();
		fieldObject.setKey("amount");
		fieldObject.setLabel("可用金额");
		fieldObject.setValue("￥"+balanceAmount.stripTrailingZeros().toPlainString()+"(总金额￥"
				+ trafficVerifyResponse.getApplyTripTraffic().getAmount().stripTrailingZeros().toPlainString() + ")");
		return fieldObject;
	}

	private CheckAvailResponseVo toCheckAvailResponse(LocalCheckAvailResponseBo checkAvailResponse,
													  HotelInfoModel hotelInfo, CheckAvailResponseVo responseVo, BookingCheckResultBo checkResult) {
		responseVo.setErrorMsg(Null.or(checkResult, BookingCheckResultBo::getErrorMsg));
		responseVo.setHitRc(Null.or(checkResult, BookingCheckResultBo::getHitRc));
		responseVo.setErrorCode(Null.or(checkResult, t->String.valueOf(t.getErrorCode())));
		RoomItem roomInfo = checkAvailResponse.getRoomInfo();
		List<RoomDailyInfo> roomDailyInfoList = checkAvailResponse.getRoomDailyInfoList();
		List<CheckAvailResponseVo.RoomDailyInfo> roomDailyInfos = roomDailyInfoList.stream().map(e -> {
			CheckAvailResponseVo.RoomDailyInfo roomDailyInfo = new CheckAvailResponseVo.RoomDailyInfo();
			roomDailyInfo.setEffectDate(e.getEffectDate());
			roomDailyInfo.setRoomPrice(e.getSellPrice());
			return roomDailyInfo;
		}).collect(Collectors.toList());
		responseVo.setRoomDailyInfoList(roomDailyInfos);
		responseVo.setMinBookingRoomNum(Optional.ofNullable(roomInfo.getMinBookingRoomNum()).orElse(0));
		responseVo.setMaxBookingRoomNum(Optional.ofNullable(roomInfo.getMaxBookingRoomNum()).orElse(0));
		responseVo.setGuestPerson(Optional.ofNullable(roomInfo.getGuestPerson()).orElse(0));
		responseVo.setLastArrivalTime(roomInfo.getLastArrivalTime());
		responseVo.setEarlyArrivalTime(roomInfo.getEarlyArrivalTime());
		List<String> specialTipList = roomInfo.getSpecialTipList();
		responseVo.setNotifyList(specialTipList);
		// 酒店积分
		responseVo.setBonusPointInfo(convert(roomInfo.getBonusPointInfo()));

		Integer policyType = hotelInfo.getCancelInfo().getPolicyType();
		addElkInfoLog(
				"显示取消规则: %s  , 结果: %s",
				policyType, CancelPolicyEnum.getCancelDetail(policyType, roomInfo.getLastCancelTime())
		);
		// 先取酒店详情取消规则
		responseVo.setCancelDetailList(CancelPolicyEnum.getCancelDetail(policyType, roomInfo.getLastCancelTime()));
		// 再取可定查询取消规则
		if (CollectionUtils.isNotEmpty(Optional.ofNullable(roomInfo.getLadderDeductionInfoList()).map(LocalCheckAvailResponseBo::getCancelDetailList).orElse(new ArrayList<>(0)))) {
			responseVo.setCancelDetailList(LocalCheckAvailResponseBo.getCancelDetailList(roomInfo.getLadderDeductionInfoList()));
		} else {
			if(CollectionUtils.isEmpty(hotelInfo.getLadderDeductionList())) {
				return responseVo;
			}
			// 酒店缓存中的取消策略
			List<String> cancelDetailList = hotelManager.getCancelDetailList(hotelInfo.getLadderDeductionList());
			if(CollectionUtils.isNotEmpty(cancelDetailList)) {
				responseVo.setCancelDetailList(cancelDetailList);
			}
		}
		return responseVo;
	}

	private CheckAvailResponseVo.BonusPointInfo convert(LocalCheckAvailResponseBo.BonusPointInfo bonusPointInfo) {
		if (bonusPointInfo == null) {
			return null;
		}
		CheckAvailResponseVo.BonusPointInfo tmp = new CheckAvailResponseVo.BonusPointInfo();
		tmp.setGroupId(bonusPointInfo.getGroupId());
		tmp.setGroupName(bonusPointInfo.getGroupName());
		tmp.setBonusPointType(bonusPointInfo.getBonusPointType());
		tmp.setBonusPointDescList(bonusPointInfo.getFillPageRuleDescList());
		return tmp;
	}

    private LocalCheckAvailRequestBo toCheckAvailRequest(ProductSnapshotModel hotelInfo, CheckAvailV2RequestVo request, BookingConfigModel bookingConfig, HotelQueryContextModel hotelQueryContextModel,BaseUserInfo baseUserInfo) {
        LocalCheckAvailRequestBo requestBo = new LocalCheckAvailRequestBo();
        requestBo.setBaseInfo(toBaseInfo(hotelInfo,bookingConfig,request.getSupplierCode()));
        requestBo.setRoomInfo(toCheckRoomInfo(hotelInfo, hotelQueryContextModel));
		requestBo.getRoomInfo().setQuantity(hotelQueryContextModel.getRoomNum());
		requestBo.getRoomInfo().setGuestPerson(hotelQueryContextModel.getPersonNum());
		requestBo.setCorpPayType(StringUtils.isNotBlank(request.getCorpPayType()) ? request.getCorpPayType() : ExpenseTypeEnum.OWN.getCode());
		requestBo.setOrgId(baseUserInfo.getOrgId());
		requestBo.setCorpId(baseUserInfo.getCorpId());
		requestBo.setUid(baseUserInfo.getUid());
		// 因公因私标识
		requestBo.getRoomInfo().setFeeType(ExpenseTypeEnum.OWN.getCode().equals(hotelQueryContextModel.getCorpPayType()) ? "P" : "C");
        requestBo.setGroupId(hotelInfo.getBasicRoomInfo().get(0).getRoomCardList().get(0).getGroupId());
        return requestBo;
    }

    private LocalCheckAvailRequestBo.CheckBaseInfo toBaseInfo(ProductSnapshotModel hotelInfo, BookingConfigModel bookingConfig,String supplierCode){
		SupplierConfigModel supplierConfigModel = bookingConfig.getSupplierConfigList().stream().filter(t->t.getSupplierCode().equals(supplierCode)).findFirst().orElseThrow(CorpBusinessException::new);
        LocalCheckAvailRequestBo.CheckBaseInfo checkBaseInfo = new LocalCheckAvailRequestBo.CheckBaseInfo();
        checkBaseInfo.setSupplierCorpId(supplierConfigModel.getSupplierCorpId());
        checkBaseInfo.setSupplierUid(supplierConfigModel.getSupplierUid());
        checkBaseInfo.setSupplierCode(hotelInfo.getBasicRoomInfo().get(0).getRoomCardList().get(0).getSupplierCode());
        return checkBaseInfo;
    }

    private LocalCheckAvailRequestBo.CheckRoomInfo toCheckRoomInfo(ProductSnapshotModel snapshotModel,HotelQueryContextModel hotelQueryContextModel) {
		RoomInfoModel roomInfoModel = snapshotModel.getBasicRoomInfo().get(0).getRoomCardList().get(0);
        LocalCheckAvailRequestBo.CheckRoomInfo checkRoomInfo = new LocalCheckAvailRequestBo.CheckRoomInfo();
        checkRoomInfo.setCheckInDate(hotelQueryContextModel.getCheckInDate());
        checkRoomInfo.setCheckOutDate(hotelQueryContextModel.getCheckOutDate());
        checkRoomInfo.setProductId(roomInfoModel.getProductId());
        checkRoomInfo.setQuantity(hotelQueryContextModel.getRoomNum());
        checkRoomInfo.setGuestPerson(hotelQueryContextModel.getPersonNum());
		    checkRoomInfo.setRoomId(roomInfoModel.getRoomId());
        if (null != roomInfoModel.getPersonPriceType()) {
            PersonPriceInfo personPriceType = roomInfoModel.getPersonPriceType();
            checkRoomInfo.setAdult(personPriceType.getAdult());
            checkRoomInfo.setRateId(personPriceType.getRateId());
        }
        return checkRoomInfo;
    }

	private void setMetric(CheckAvailResponseVo response){
		if(response == null){
			return;
		}
		Metrics.REGISTRY.counter(checkAvailId.withTags("errorCode",
						Conditional.ofNullable(response.getErrorCode()).orElse(GenericConstants.UNKNOWN)))
				.increment();
	}
}
