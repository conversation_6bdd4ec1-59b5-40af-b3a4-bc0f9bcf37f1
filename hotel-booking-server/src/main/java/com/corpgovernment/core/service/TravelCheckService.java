package com.corpgovernment.core.service;

import com.corpgovernment.api.hotel.booking.core.TravelExceedInfo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.converter.model.context.HotelContextModel;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.dto.management.response.TravelStandardRuleVerifyResultVO;
import com.corpgovernment.dto.snapshot.SnapshotQtyCmd;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.SnapShotDTO;
import com.corpgovernment.dto.travelstandard.response.ExceedReasonVO;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 差标校验
 */
@Slf4j
@Service
public class TravelCheckService {

    @Autowired
    private CoreServiceClient coreServiceClient;

    /**
     * 超标校验
     * roomInfo
     */
    public TravelExceedInfo verifyTravelStandard( VerifyTravelStandardRequest req) {
        log.info("+++++超标校验, verifyTravelStandard,request:{}", JsonUtils.toJsonString(req));
        JSONResult<List<ResourcesVerifyResponse>> response = coreServiceClient.verifyTravelStandard(req);
        log.info("+++++超标校验, verifyTravelStandard,response:{}", JsonUtils.toJsonString(response));
        if (response != null && response.isSUCCESS()) {
            return buildTravelExceedInfo(response.getData().get(0)); // 因为传入roomId, 默认只有一个资源结果
        } else {
            throw new CorpBusinessException(response.getStatus(), response.getMsg());
        }
    }


    public String getTravelStandardMark(String token) {
        log.info("+++++查询TravelStandardMark， token:{}", token);
        if(StringUtils.isBlank(token)){
            return null;
        }
        SnapshotQtyCmd cmd = new SnapshotQtyCmd();
        cmd.setToken(token);
        cmd.setDataTypeList(Lists.newArrayList( CollectionUtils.newArrayList("hotel_context")));
        JSONResult<QuerySnapshotResponseDTO> ret = coreServiceClient.getSnapshot(cmd);
        log.info("+++++查询TravelStandardMark， ret:{}", JsonUtils.toJsonString(ret));
        if(!ret.isSUCCESS()){
            return null;
        }
        QuerySnapshotResponseDTO data = ret.getData();
        if(data == null){
            return null;
        }
        List<SnapShotDTO> snapshotList = data.getSnapshotList();
        if(CollectionUtils.isEmpty(snapshotList)){
            return null;
        }
        SnapShotDTO hotelContextSnapshot = snapshotList.get(0);
        if(hotelContextSnapshot == null){
            return null;
        }
        String snapshotData = hotelContextSnapshot.getSnapshotData();
        if(StringUtils.isBlank(snapshotData)){
            return null;
        }
        HotelContextModel model = JsonUtils.parse(snapshotData, HotelContextModel.class);
        if(model == null){
            return null;
        }
        return model.getTravelStandardMark();
    }

    public Integer parseLevelStep(String travelStandardMark) {
        if(StringUtils.isBlank(travelStandardMark)){
            return null;
        }
        try{
            return Integer.parseInt(travelStandardMark);
        }catch (NumberFormatException e){
            return null;
        }
    }


    /**
     * 构建超标结果
     */
    private TravelExceedInfo buildTravelExceedInfo(ResourcesVerifyResponse verifyResponse) {
        TravelExceedInfo ret = new TravelExceedInfo();
        // 空判
        if (verifyResponse == null) {
            log.warn("++++++超标检查结果为空，自己返回true");
            ret.setExceed(false);
            return ret;
        }
        // set exceed
        boolean exceed = verifyResponse.getExceed()!= null && verifyResponse.getExceed()> 0; // 是否超标, 0 通过（不超标）, 1 超基础差标, 2 超浮动差标
        ret.setExceed(exceed);

        if (exceed) {
            // 超标金额
            BigDecimal exceedAmount = verifyResponse.getExceedAmount();
            ret.setExceedAmount(exceedAmount);
            ret.setExceedType(verifyResponse.getExceed() == 2 ? "floatTravelStandard" : "standardTravelStandard");
            // 管控方式, 如 ControlTypeEnum.M (随心付/支持混付)
            Set<String> rejectTypes = verifyResponse.getRejectTypes();
            if (CollectionUtils.isEmpty(rejectTypes)) {
                log.error("+++管控方式(如 ControlTypeEnum.M)为空, verifyResponse:{}", verifyResponse);
                throw new CorpBusinessException(HotelResponseCodeEnum.UN.code(), "差标校验结果出错, 没有可用的拒绝策略");
            }
            ret.setRejectTypes(rejectTypes);
            // set exceed items & rc list
            setRcList(verifyResponse, ret);
            // 判断是否是同住房间管控
            ret.setRoomExceedInfoList(buildRoomExceedInfoList(verifyResponse.getRuleVerifyResultList()));
        }
        return ret;
    }
    
    private List<TravelExceedInfo.RoomExceedInfo> buildRoomExceedInfoList(List<TravelStandardRuleVerifyResultVO> ruleVerifyResultList) {
        if (ruleVerifyResultList == null) {
            return null;
        }
        
        List<TravelStandardRuleVerifyResultVO> cohabitRuleList = ruleVerifyResultList.stream()
                .filter(item -> item != null
                        && item.getRule() != null
                        && StringUtils.equalsIgnoreCase(item.getRule().getName(), "CohabitRule"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cohabitRuleList)) {
            return null;
        }
        List<TravelExceedInfo.RoomExceedInfo> roomExceedInfoList = new ArrayList<>();
        for (TravelStandardRuleVerifyResultVO item : cohabitRuleList) {
            TravelExceedInfo.RoomExceedInfo roomExceedInfo = new TravelExceedInfo.RoomExceedInfo();
            roomExceedInfo.setExceed(item.getExceed() != null && item.getExceed() > 0);
            if (item.getExceed() != null && (item.getExceed() == 2 || item.getExceed() == 5)) {
                roomExceedInfo.setExceedType("floatTravelStandard");
            } else if (item.getExceed() != null && (item.getExceed() == 1 || item.getExceed() == 3)) {
                roomExceedInfo.setExceedType("standardTravelStandard");
            }
            roomExceedInfoList.add(roomExceedInfo);
        }
        return roomExceedInfoList;
    }


    private void setRcList(ResourcesVerifyResponse verifyResponse, TravelExceedInfo ret) {
        List<TravelStandardRuleVerifyResultVO> itemList = verifyResponse.getRuleVerifyResultList();
        if (CollectionUtils.isNotEmpty(itemList)) {
            List<String> itemDescList = new ArrayList<>();
            List<TravelExceedInfo.ExceedReason> rcList = new ArrayList<>();
            List<String> exceedResultDescList = new ArrayList<>();
            for (TravelStandardRuleVerifyResultVO item : itemList) {
                if (item.getExceed() > 0 && item.getRule() != null) {
                    TravelStandardRuleVO rule = item.getRule();
                    String itemDesc = rule.getName();
                    // set item
                    itemDescList.add(itemDesc);
                    // set rc list
                    List<ExceedReasonVO> itemRcList = rule.getExceedReasonList();
                    if (CollectionUtils.isNotEmpty(itemRcList)) {
                        List<TravelExceedInfo.ExceedReason> exceedRcList = getExceedReasons(itemRcList);
                        rcList.addAll(exceedRcList);
                    }
                    // 管控原因
                    String resultDesc = item.getResultDesc();
                    if (StringUtils.isNotBlank(resultDesc)) {
                        exceedResultDescList.add(resultDesc);
                    }
                }
            }
            ret.setExceedItemList(itemDescList);
            ret.setReasonList(rcList);
            ret.setExceedResultDescList(exceedResultDescList);
        }
    }

    private List<TravelExceedInfo.ExceedReason> getExceedReasons(List<ExceedReasonVO> exceedReasonList) {
        return exceedReasonList.stream().map(
                exceedRc -> {
                    TravelExceedInfo.ExceedReason exceedReason = new TravelExceedInfo.ExceedReason();
                    exceedReason.setId(exceedRc.getId());
                    exceedReason.setName(exceedRc.getName());
                    return exceedReason;
                }
        ).collect(Collectors.toList());
    }






}
