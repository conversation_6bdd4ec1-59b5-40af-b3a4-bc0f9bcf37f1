package com.corpgovernment.core.service.custom.factory;

import com.corpgovernment.api.hotel.booking.core.QueryHotelListReqVo;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.common.service.IHotelIndicatorDomainService;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelApplication;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hotelconfig.service.IHotelConfigDomainService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPage;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControl;
import com.corpgovernment.core.service.custom.IHotelCustomService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-25 12:29
 */
@Service
@Slf4j
public class HotelCustomServiceFactory {

    @Resource
    private IHotelIndicatorDomainService hotelIndicatorDomainService;

    private final Map<HotelCustomEnum, IHotelCustomService> map = new HashMap<>();

    public IHotelCustomService getHotelCustomService(HotelCustomEnum hotelCustomEnum) {
        if (hotelCustomEnum == null) {
            return null;
        }
        return map.get(hotelCustomEnum);
    }

    public HotelCustomEnum getHotelCustomEnum(TravelStandard travelStandard) {
        if (travelStandard != null && Boolean.TRUE.equals(checkLatLonTravelApplication(travelStandard.getTravelApplication()))) {
            return HotelCustomEnum.XU_GONG;
        }
        return HotelCustomEnum.DEFAULT;
    }

    @BusinessBehaviorMonitor
    public List<TravelControl> getTravelControlList(HotelCustomEnum hotelCustomEnum,
                                                    HotelPageRequest hotelPageRequest,
                                                    TravelStandard travelStandard,
                                                    TravelConfig travelConfig,
                                                    Boolean travelStandardFilter) {
        try {
            IHotelCustomService hotelCustomService = getHotelCustomService(hotelCustomEnum);
            if (hotelCustomService == null) {
                return null;
            }
            return hotelCustomService.getTravelControlList(hotelPageRequest, travelStandard, travelConfig, travelStandardFilter);
        } finally {
            hotelIndicatorDomainService.getTravelControlList();
        }
    }

    @BusinessBehaviorMonitor
    public HotelPage queryHotelList(HotelCustomEnum hotelCustomEnum, QueryHotelListReqVo requestParam, List<TravelControl> travelControlList, TravelConfig travelConfig) {
        try {
            IHotelCustomService hotelCustomService = getHotelCustomService(hotelCustomEnum);
            if (hotelCustomService == null) {
                return null;
            }
            return hotelCustomService.queryHotelList(requestParam, travelControlList, travelConfig);
        } finally {
            hotelIndicatorDomainService.queryHotelList();
        }
    }

    @BusinessBehaviorMonitor
    public void specialHandleHotelDetail(HotelCustomEnum hotelCustomEnum, HotelDetail hotelDetail, List<String> prioritySupplierCodeList, Integer hotelRoomSortRule) {
        try {
            IHotelCustomService hotelCustomService = getHotelCustomService(hotelCustomEnum);
            if (hotelCustomService == null) {
                return;
            }
            hotelCustomService.specialHandleHotelDetail(hotelDetail, prioritySupplierCodeList, hotelRoomSortRule);
        } finally {
           hotelIndicatorDomainService.specialHandleHotelDetail();
        }
    }

    @Autowired
    public void setMap(List<IHotelCustomService> hotelCustomServiceList) {
        if (CollectionUtils.isEmpty(hotelCustomServiceList)) {
            return;
        }

        for (IHotelCustomService hotelCustomService : hotelCustomServiceList) {
            if (hotelCustomService == null) {
                continue;
            }
            map.put(hotelCustomService.hotelCustomEnum(), hotelCustomService);
        }
    }

    private Boolean checkLatLonTravelApplication(TravelApplication travelApplication) {
        return travelApplication != null
                && travelApplication.getBookLon() != null
                && travelApplication.getBookLat() != null
                && StringUtils.isNotBlank(travelApplication.getBookAddress());
    }

}
