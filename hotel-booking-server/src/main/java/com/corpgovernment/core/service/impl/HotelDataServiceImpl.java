package com.corpgovernment.core.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.corpgovernment.api.basic.request.FuzzyHotelCityFromDistributionRequest;
import com.corpgovernment.api.basic.request.FuzzySearchHotelCityRequest;
import com.corpgovernment.api.basic.response.FuzzyHotelCityFromDistributionResponse;
import com.corpgovernment.api.basic.response.FuzzyHotelCityFromDistributionResponse.DestinationInfoListDTO;
import com.corpgovernment.api.basic.response.HotelCityFuzzySearchVo;
import com.corpgovernment.api.basic.response.HotelCityInfoFuzzySearchResponse;
import com.corpgovernment.basic.bo.response.TicketResponse;
import com.corpgovernment.basic.constant.AccessTypeEnum;
import com.corpgovernment.basic.constant.GetTicketVersionEnum;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.basic.convert.FuzzyHotelCityConvert;
import com.corpgovernment.basicdata.bo.HotelCityBo;
import com.corpgovernment.basicdata.utils.StringUtil;
import com.corpgovernment.common.apollo.BasicManageApollo;
import com.corpgovernment.common.apollo.CommonApollo;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.ExceptionCodeEnum;
import com.corpgovernment.common.handler.ICacheHandler;
import com.corpgovernment.common.utils.HttpUtils;
import com.corpgovernment.core.controller.vo.HotelCityFromDistributionReqVo;
import com.corpgovernment.core.controller.vo.HotelCityFromDistributionRespVo;
import com.corpgovernment.core.controller.vo.HotelCityFuzzySearchInfoReqVo;
import com.corpgovernment.core.dao.apollo.impl.HotelCoreApolloDao;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.hotelconfig.gateway.IHotelConfigGateway;
import com.corpgovernment.core.domain.hotelconfig.gateway.ISupplierProductGateway;
import com.corpgovernment.core.service.IHotelDataService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.corpgovernment.basic.constant.DestinationTypeEnum.HOTEL;
import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.CTRIP_SEARCH_KEYWORD_FAIL;
import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.CTRIP_SEARCH_KEYWORD_URL_IS_NULL;
import static com.corpgovernment.basicdata.service.impl.CtripSupplierAuthServiceImpl.EXPIRE_SECOND;
import static com.corpgovernment.basicdata.service.impl.CtripSupplierAuthServiceImpl.TICKET_CACHE_KEY_PREFIX;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HotelDataServiceImpl implements IHotelDataService {
    @Autowired
    private ICacheHandler cacheHandler;

    @Autowired
    private BasicManageApollo basicManageApollo;

    @Autowired
    private FuzzyHotelCityConvert fuzzyHotelCityConvert;

    @Resource
    private HotelCoreApolloDao hotelCoreApolloDao;

    @Autowired
    private CommonApollo commonApollo;
    
    @Resource
    private ISupplierProductGateway supplierProductGateway;

    private static List<String> hongKongMacauTaiwanProvinceIdList  = Arrays.asList("32", "33", "53");

    private final static String HOTEL_INTL = "HOTEL_INTL";
    private final static String HOTEL = "HOTEL";
    private final static String DOMESTIC = "DOMESTIC";
    private final static String OVERSEA = "OVERSEA";
    private final static String ALL = "ALL";

    /**
     * 根据分销渠道模糊查询酒店所在城市信息
     *
     * @param request 请求参数，包含查询条件等
     * @return 返回模糊查询酒店所在城市信息的响应对象
     */
    @Override
    public FuzzyHotelCityFromDistributionResponse fuzzyHotelCityFromDistribution(FuzzyHotelCityFromDistributionRequest request){
        log.info("fuzzyHotelCityFromDistribution request:{}", JsonUtils.toJsonString(request));
        SupplierProduct supplierProduct = supplierProductGateway.queryKeyWordSupplierProduct(request.getProductType());
        if (supplierProduct == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.FUZZY_SEARCH_DESTINATION_CONFIG_ERROR);
        }
        HotelCityFromDistributionReqVo hotelCityFromDistributionRequest = getHotelCityFromDistributionRequest(request, supplierProduct);
        log.info("fuzzyHotelCityFromDistribution request:{},hotelCityFromDistributionRequest:{}",
                JsonUtils.toJsonString(request), JsonUtils.toJsonString(hotelCityFromDistributionRequest));
        HotelCityFromDistributionRespVo hotelCityFromDistributionResponse = fuzzyHotelCityFromDistributionSoa(hotelCityFromDistributionRequest, supplierProduct);
        log.info("fuzzyHotelCityFromDistribution,hotelCityFromDistributionRequest:{}, hotelCityFromDistributionResponse:{}",
                JsonUtils.toJsonString(hotelCityFromDistributionRequest), JsonUtils.toJsonString(hotelCityFromDistributionResponse));
        HotelCityFromDistributionRespVo.ErrorInfoDTO errorInfo = hotelCityFromDistributionResponse.getErrorInfo();
        if(ObjectUtil.isNotNull(errorInfo)){
            log.info("hotelCityFromDistributionResponse.errorInfo:{}", JsonUtils.toJsonString(errorInfo));
            throw new CorpBusinessException(ExceptionCodeEnum.Alert,
                    errorInfo.getErrorCode()+":"+errorInfo.getErrorDesc());
        }
        log.info("fuzzyHotelCityFromDistribution,hotelCityFromDistributionRequest:{}, hotelCityFromDistributionResponse:{}",
                JsonUtils.toJsonString(hotelCityFromDistributionRequest),JsonUtils.toJsonString(hotelCityFromDistributionResponse));
        FuzzyHotelCityFromDistributionResponse fuzzyHotelCityFromDistributionResponse = fuzzyHotelCityConvert.convertFuzzyHotelCityFromDistributionResponse(hotelCityFromDistributionResponse);
        log.info("fuzzyHotelCityFromDistribution fuzzyHotelCityFromDistributionResponse:{}", JsonUtils.toJsonString(fuzzyHotelCityFromDistributionResponse));
        return fuzzyHotelCityFromDistributionResponse;
    }

    @Override
    public HotelCityInfoFuzzySearchResponse hotelCityInfoFromDistribution(HotelCityFuzzySearchInfoReqVo request) {
        HotelCityInfoFuzzySearchResponse response = new HotelCityInfoFuzzySearchResponse();
        log.info("fuzzyHotelCityFromDistribution request:{}", JsonUtils.toJsonString(request));
        //参数校验
        if (!checkHotelCityFuzzySearchParams(request)) {
            log.info("酒店城市模糊搜,参数校验失败:{}", request);
            return response;
        }
        //模糊搜索
        try {
            log.info("开始模糊搜索！");
            FuzzyHotelCityFromDistributionRequest cityFromDistributionRequest = new FuzzyHotelCityFromDistributionRequest();
            cityFromDistributionRequest.setKeyword(request.getSearchStr());
            cityFromDistributionRequest.setProductType(request.getProductType());
            cityFromDistributionRequest.setCorpPayType(request.getCorpPayType());
            cityFromDistributionRequest.setOnlyGeoData(true);
            log.info("fuzzyHotelCityFromDistribution cityFromDistributionRequest:{}", JsonUtils.toJsonString(cityFromDistributionRequest));
            List<DestinationInfoListDTO> destinationInfoList = fuzzyHotelCityFromDistribution(cityFromDistributionRequest).getDestinationInfoList();
            log.info("fuzzyHotelCityFromDistribution destinationInfoList:{}", JsonUtils.toJsonString(destinationInfoList));
            List<HotelCityFuzzySearchVo> targetList = new ArrayList<>();
            for (DestinationInfoListDTO dto : destinationInfoList) {
                log.info("fuzzyHotelCityFromDistribution dto:{}", JsonUtils.toJsonString(dto));
                HotelCityFuzzySearchVo hotelCityFuzzySearchVo = new HotelCityFuzzySearchVo();
                hotelCityFuzzySearchVo.setCityId(dto.getCityId().toString());
                hotelCityFuzzySearchVo.setCityName(dto.getCityName());
                hotelCityFuzzySearchVo.setCityEnName(dto.getDestinationEnName());
//                hotelCityFuzzySearchVo.setParentCityId(dto.getParentCityList().get(0).getCityId().toString());
                hotelCityFuzzySearchVo.setProvinceId(String.valueOf(dto.getProvinceId()));
                hotelCityFuzzySearchVo.setProvinceName(dto.getProvinceName());
                hotelCityFuzzySearchVo.setCountryId(dto.getCountryId().toString());
                hotelCityFuzzySearchVo.setCountryName(dto.getCountryName());
                targetList.add(hotelCityFuzzySearchVo);
            }
            Boolean hmtCityDisplay = commonApollo.getHMTCityDisplay();
            log.info("fuzzyHotelCityFromDistribution targetList:{}", JsonUtils.toJsonString(targetList));
            log.info("fuzzyHotelCityFromDistribution hmtCityDisplay:{}", JsonUtils.toJsonString(hmtCityDisplay));
            // 不是酒店的场景海外恒输出港澳台
            if (HOTEL.equalsIgnoreCase(request.getProductType())){
                // 酒店场景港澳台，国内展示则海外不展示
                if (!hmtCityDisplay &&HOTEL.equalsIgnoreCase(request.getProductType())) {
                    targetList = targetList.stream()
                            .filter(item -> !hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId()))
                            .collect(Collectors.toList());
                } else if (hmtCityDisplay && !HOTEL.equalsIgnoreCase(request.getProductType())) {
                    targetList = targetList.stream()
                            .filter(item -> !hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId()))
                            .collect(Collectors.toList());
                }
            }
            log.info("fuzzyHotelCityFromDistribution targetLists:{}", JsonUtils.toJsonString(targetList));
            response.setFuzzySearchCityInfo(targetList);
            log.info("模糊搜索结束！");
        } catch (Exception e) {
            log.error("模糊搜索发生错误", e);
        }
        return response;
    }

    @Override
    public List<HotelCityBo> fuzzySearchHotelCity(FuzzySearchHotelCityRequest request) {
        HotelCityFuzzySearchInfoReqVo searchInfoRequest = new HotelCityFuzzySearchInfoReqVo();
        searchInfoRequest.setSearchStr(request.getKey());
        searchInfoRequest.setDomestic(Objects.isNull(request.getCountryId()) || Objects.equals(request.getCountryId(), "1"));
        searchInfoRequest.setProductType(request.getProductType());
        HotelCityInfoFuzzySearchResponse searchResponse = fuzzyHotelCityFromDistribution(searchInfoRequest);
        log.info("fuzzySearchHotelCity,searchResponse:{},searchInfoRequest:{}",JsonUtils.toJsonString(searchResponse),JsonUtils.toJsonString(searchInfoRequest));
        List<HotelCityFuzzySearchVo> fuzzySearchCityInfo = Optional.ofNullable(searchResponse).map(HotelCityInfoFuzzySearchResponse::getFuzzySearchCityInfo).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(fuzzySearchCityInfo)){
            return Collections.emptyList();
        }
        fuzzySearchCityInfo = fuzzySearchCityInfo.stream().filter(city -> (StringUtils.isBlank(city.getAreaId()))).collect(Collectors.toList());
        List<HotelCityBo> hotelCityList = new ArrayList<>();
        for (HotelCityFuzzySearchVo searchVo : fuzzySearchCityInfo) {
            HotelCityBo cityBo = new HotelCityBo();
            cityBo.setCityId(searchVo.getCityId());
            cityBo.setCityCode(searchVo.getCityCode());
            cityBo.setCityName(searchVo.getCityName());
            cityBo.setCityEnName(searchVo.getCityEnName());
            cityBo.setProvinceName(searchVo.getProvinceName());
            cityBo.setProvinceId(searchVo.getProvinceId());
            cityBo.setCountryName(searchVo.getCountryName());
            hotelCityList.add(cityBo);
        }

        return hotelCityList;
    }

    /**
     * 获取酒店城市模糊搜索数据
     */
    public HotelCityInfoFuzzySearchResponse fuzzyHotelCityFromDistribution(HotelCityFuzzySearchInfoReqVo request){
        HotelCityInfoFuzzySearchResponse response = new HotelCityInfoFuzzySearchResponse();
        log.info("fuzzyHotelCityFromDistribution request:{}", JsonUtils.toJsonString(request));
        //参数校验
        if (!checkHotelCityFuzzySearchParams(request)) {
            log.info("酒店城市模糊搜,参数校验失败:{}", request);
            return response;
        }
        //模糊搜索
        try {
            log.info("开始模糊搜索！");
            FuzzyHotelCityFromDistributionRequest cityFromDistributionRequest = new FuzzyHotelCityFromDistributionRequest();
            cityFromDistributionRequest.setKeyword(request.getSearchStr());
            cityFromDistributionRequest.setProductType(request.getProductType());
            cityFromDistributionRequest.setCorpPayType(request.getCorpPayType());
            cityFromDistributionRequest.setOnlyGeoData(true);
            log.info("fuzzyHotelCityFromDistribution cityFromDistributionRequest:{}", JsonUtils.toJsonString(cityFromDistributionRequest));
            List<DestinationInfoListDTO> destinationInfoListDTOList = fuzzyHotelCityFromDistribution(cityFromDistributionRequest).getDestinationInfoList();
            log.info("fuzzyHotelCityFromDistribution destinationInfoListDTOList:{}", JsonUtils.toJsonString(destinationInfoListDTOList));
            List<DestinationInfoListDTO> destinationInfoList = destinationInfoListDTOList.stream()
                    .filter(dto -> !"COUNTRY".equals(dto.getResultType()))
                    .collect(Collectors.toList());
            log.info("fuzzyHotelCityFromDistribution destinationInfoList:{}", JsonUtils.toJsonString(destinationInfoList));
            List<HotelCityFuzzySearchVo> targetList = new ArrayList<>();
            for (DestinationInfoListDTO dto : destinationInfoList) {
                log.info("fuzzyHotelCityFromDistribution dto:{}", JsonUtils.toJsonString(dto));
                HotelCityFuzzySearchVo hotelCityFuzzySearchVo = new HotelCityFuzzySearchVo();
                hotelCityFuzzySearchVo.setCityId(dto.getCityId().toString());
                hotelCityFuzzySearchVo.setCityName(dto.getCityName());
                hotelCityFuzzySearchVo.setCityEnName(dto.getDestinationEnName());
//                hotelCityFuzzySearchVo.setParentCityId(dto.getParentCityList().get(0).getCityId().toString());
                hotelCityFuzzySearchVo.setProvinceId(String.valueOf(dto.getProvinceId()));
                hotelCityFuzzySearchVo.setProvinceName(dto.getProvinceName());
                hotelCityFuzzySearchVo.setCountryId(dto.getCountryId().toString());
                hotelCityFuzzySearchVo.setCountryName(dto.getCountryName());
                targetList.add(hotelCityFuzzySearchVo);
            }
            Boolean hmtCityDisplay = commonApollo.getHMTCityDisplay();
            log.info("fuzzyHotelCityFromDistribution targetList:{}", JsonUtils.toJsonString(targetList));
            log.info("fuzzyHotelCityFromDistribution hmtCityDisplay:{}", JsonUtils.toJsonString(hmtCityDisplay));
            // 不是酒店的场景海外恒输出港澳台
            if (HOTEL.equalsIgnoreCase(request.getProductType())){
                // 酒店场景港澳台，国内展示则海外不展示
                if (!hmtCityDisplay &&HOTEL.equalsIgnoreCase(request.getProductType())) {
                    targetList = targetList.stream()
                            .filter(item -> !hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId()))
                            .collect(Collectors.toList());
                } else if (hmtCityDisplay && !HOTEL.equalsIgnoreCase(request.getProductType())) {
                    targetList = targetList.stream()
                            .filter(item -> !hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId()))
                            .collect(Collectors.toList());
                }
            }
            log.info("fuzzyHotelCityFromDistribution targetLists:{}", JsonUtils.toJsonString(targetList));
            response.setFuzzySearchCityInfo(targetList);
            log.info("模糊搜索结束！");
        } catch (Exception e) {
            log.error("模糊搜索发生错误", e);
        }
        return response;
    }

    /**
     * 参数校验
     */
    private Boolean checkHotelCityFuzzySearchParams(HotelCityFuzzySearchInfoReqVo request) {
        //参数检验
        if (Objects.isNull(request) || StringUtils.isBlank(request.getSearchStr()) || Objects.isNull(request.getDomestic())) {
            return false;
        }
        request.setSearchStr(request.getSearchStr().trim());
        return true;
    }

    /**
     * 根据模糊请求获取酒店城市分布请求
     *
     * @param request 模糊酒店城市分布请求
     * @return 酒店城市分布请求
     */
    private  HotelCityFromDistributionReqVo getHotelCityFromDistributionRequest(FuzzyHotelCityFromDistributionRequest request, SupplierProduct supplierProduct) {
        log.info("根据模糊请求获取酒店城市分布请求,request:{}", JsonUtils.toJsonString(request));
        HotelCityFromDistributionReqVo hotelCityFromDistributionRequest = new HotelCityFromDistributionReqVo();
        String supplierTicketStr = hotelCoreApolloDao.getSupplierTicketJson();
        log.info("根据模糊请求获取酒店城市分布请求supplierTicketStr:{}", supplierTicketStr);
        log.info("根据模糊请求获取酒店城市分布请求,getAppKeyByCorpID:{}", JsonUtils.toJsonString(getAppKeyByCorpID()));
        hotelCityFromDistributionRequest.setAuth(getAppKeyByCorpID(), ticket(true));
        hotelCityFromDistributionRequest.setCorpId(supplierProduct.getCorpId());
        HotelCityFromDistributionReqVo.SearchEngineBaseInfoDTO searchEngineBaseInfoDTO = new HotelCityFromDistributionReqVo.SearchEngineBaseInfoDTO();
        // 没有携程uid就可以不传
//        BaseUserInfo userInfo = request.getUserInfo();
//        if(ObjectUtil.isNotNull(userInfo)){
//            searchEngineBaseInfoDTO.setUid(userInfo.getUid());
//            hotelCityFromDistributionRequest.setCorpId(userInfo.getCorpId());
//        }
        searchEngineBaseInfoDTO.setLanguageType("CN");
        searchEngineBaseInfoDTO.setLocale("zh-CN");
        searchEngineBaseInfoDTO.setRequestFrom("CORP_GDS"); // 固定值
        HotelCityFromDistributionReqVo.SearchEngineBaseInfoDTO.DebugInfoDTO debugInfoDTO = new HotelCityFromDistributionReqVo.SearchEngineBaseInfoDTO.DebugInfoDTO();
        debugInfoDTO.setTraceLogId(IdUtil.fastUUID());
        searchEngineBaseInfoDTO.setDebugInfo(debugInfoDTO);
        hotelCityFromDistributionRequest.setSearchEngineBaseInfo(searchEngineBaseInfoDTO);
        setKeyword(request, hotelCityFromDistributionRequest);
        String searchRange = "";
        if(HOTEL.equalsIgnoreCase(request.getProductType())&&Objects.isNull(request.getSearchRange())){ // 国内酒店
            searchRange = DOMESTIC;
        }else if(HOTEL_INTL.equalsIgnoreCase(request.getProductType())&&Objects.isNull(request.getSearchRange())){ // 国际酒店
            searchRange = OVERSEA;
        }else { // 出差申请单
            searchRange = ALL;
        }
        hotelCityFromDistributionRequest.setSearchRange(searchRange); // ALL 全部；DOMESTIC 国内(含港澳台)；OVERSEA 海外
        if (Boolean.TRUE.equals(request.getOnlyGeoData())){
            hotelCityFromDistributionRequest.setOnlyGeoData(true); // 只输出地理信息（只返回国家、省份、城市等信息，不返回酒店、品牌等其他信息）
        }else{
            hotelCityFromDistributionRequest.setOnlyGeoData(false);
        }
        log.info("根据模糊请求获取酒店城市分布请求,hotelCityFromDistributionRequest:{}", JsonUtils.toJsonString(hotelCityFromDistributionRequest));
        return hotelCityFromDistributionRequest;
    }

    /**
     * 获取AppKey
     *
     * @return
     */
    protected String getAppKeyByCorpID() {
        return getAppKeyByCorpID(StringUtils.EMPTY, StringUtils.EMPTY);
    }

    /**
     * 根据公司ID获取AppKey
     *
     * @param corpid
     * @param supplierCode
     * @return
     */
    private String getAppKeyByCorpID(String corpid, String supplierCode) {
        String supplierTicketStr = hotelCoreApolloDao.getSupplierTicketJson();
        log.info("supplierTicketStr:{}", supplierTicketStr);
        if (StringUtils.isEmpty(supplierTicketStr)) {
            return StringUtils.EMPTY;
        }
        JsonNode jsonNode = JsonUtils.getJsonNode(supplierTicketStr);
        log.info("jsonNode:{}", jsonNode);
        if (jsonNode == null) {
            return StringUtils.EMPTY;
        }
        String appKey = jsonNode.findPath(String.join(StringUtil.SPLITSTRING, corpid, supplierCode, "key").toLowerCase()).asText();
        appKey = StringUtils.isEmpty(appKey) ? jsonNode.findPath("defaultKey").asText() : appKey;

        return appKey;
    }

    /**
     * 获取ticket
     *
     * @return
     */
    @Override
    public String ticket(boolean isCache) {
        return ticketByCorpID(StringUtils.EMPTY, StringUtils.EMPTY, isCache);
    }

    @Override
    public String ticketByCorpID(String corpId, String supplierCode, boolean isCache) {
        return ticketByVersion(corpId, supplierCode, isCache, GetTicketVersionEnum.V1.getCode());
    }

    /**
     * 获取ticket
     *
     * @return
     */
    @Override
    public String ticketByVersion(String corpid, String supplierCode, boolean isCache, Integer version) {
        String ticket = "";
        if (isCache) {
            Object var1 = cacheHandler.getValue(String.join(StringUtil.SPLITSTRING, TICKET_CACHE_KEY_PREFIX, corpid, supplierCode));
            if (!ObjectUtils.isEmpty(var1)) {
                return var1.toString();
            }
        }

        String supplierTicketStr = basicManageApollo.getSupplierTicketJson();
        if (StringUtils.isEmpty(supplierTicketStr)) {
            return ticket;
        }
        JsonNode jsonNode = JsonUtils.getJsonNode(supplierTicketStr);
        if (jsonNode == null) {
            return ticket;
        }
        String appKey = getAppKeyByCorpID(corpid, supplierCode);
        String appSecurity = jsonNode.findPath(String.join(StringUtil.SPLITSTRING, corpid, supplierCode, "security").toLowerCase()).asText();
        appSecurity = StringUtils.isEmpty(appSecurity) ? jsonNode.findPath("defaultSecurity").asText() : appSecurity;

        // 验签入参组装
        Map requestParam = new HashMap();
        requestParam.put("appKey", appKey);
        requestParam.put("appSecurity", appSecurity);

        // url 版本获取控制
        String url = null;
        if (GetTicketVersionEnum.V1.getCode().equals(version)) {
            url = jsonNode.findPath(String.join(StringUtil.SPLITSTRING, supplierCode, "url").toLowerCase()).asText();
            url = StringUtils.isEmpty(url) ? jsonNode.findPath("defaultUrl").asText() : url;
        } else {
            // v2 版本url切换，同时，新增字段
            url = jsonNode.findPath("defaultV2Url").asText();
            requestParam.put("accessType", AccessTypeEnum.TIME_LIMITED.getCode());
        }

        log.info("appKey：{}、appSecurity:{}、url:{}", appKey, appSecurity, url);
        if (StringUtils.isEmpty(appKey) || StringUtils.isEmpty(appSecurity) || StringUtils.isEmpty(url)) {
            return ticket;
        }

        String response = "";
        try {
            response = HttpUtils.doPostJSON(supplierCode, "getTicket", url, JsonUtils.toJsonString(requestParam));
        } catch (IOException e) {
            log.warn("获取携程ticket失败,e:{}", e);
            return ticket;
        }
        log.warn("获取携程ticket信息,response:{}", response);
        TicketResponse ticketResponse = JsonUtils.parse(response, TicketResponse.class);
        if (ticketResponse == null || ticketResponse.getStatus() == null) {
            log.warn("获取携程ticket失败，参数:{}", requestParam);
            return ticket;
        }
        if (ticketResponse.getStatus().getErrorCode() != 0) {
            log.warn("获取携程ticket失败，参数:{}", requestParam);
            return ticket;
        }
        ticket = ticketResponse.getTicket();
        if (StringUtils.isBlank(ticket)) {
            log.warn("获取携程ticket失败，参数:{}", requestParam);
            return ticket;
        }
        log.info("获取ticket成功，ticket：{}", ticket);
        cacheHandler.setValue(String.join(StringUtil.SPLITSTRING, TICKET_CACHE_KEY_PREFIX, corpid, supplierCode), ticket, EXPIRE_SECOND);
        return ticket;
    }

    /**
     * 根据分销渠道模糊查询酒店所在城市信息Soa
     *
     * @param request 请求参数，包含查询条件等
     * @return 返回模糊查询酒店所在城市信息的响应对象
     */
    public HotelCityFromDistributionRespVo fuzzyHotelCityFromDistributionSoa(HotelCityFromDistributionReqVo request, SupplierProduct supplierProduct){
        log.info("fuzzyHotelCityFromDistributionSoa request:{}", JsonUtils.toJsonString(request));
        String response = "";
        String param = JsonUtils.toJsonString(request);
//        String ctripSearchKeywordUrl = hotelCoreApolloDao.getCtripSearchKeywordUrl();
//        log.info("fuzzyHotelCityFromDistributionSoa ctripSearchKeywordUrl:{}", JsonUtils.toJsonString(ctripSearchKeywordUrl));
//        if (ctripSearchKeywordUrl == null) {
//            throw new CorpBusinessException(CTRIP_SEARCH_KEYWORD_URL_IS_NULL);
//        }
        try {
            response = HttpUtils.doPostJSON("ctrip", "目的地模糊查询", supplierProduct.getUrl(), param);
            log.info("目的地模糊查询,param:{}, response:{}", param, response);
        } catch (IOException e) {
            log.error("目的地模糊查询,e:", e);
        }
        HotelCityFromDistributionRespVo hotelCityFromDistributionResponse = JsonUtils.parse(response, HotelCityFromDistributionRespVo.class);
        log.info("fuzzyHotelCityFromDistributionSoa response:{}", JsonUtils.toJsonString(hotelCityFromDistributionResponse));
        return hotelCityFromDistributionResponse;
    }

    /**
     * 设置关键词
     *
     * @param request                模糊酒店城市分布请求
     * @param hotelCityFromDistributionRequest 酒店城市分布请求
     */
    private static void setKeyword(FuzzyHotelCityFromDistributionRequest request, HotelCityFromDistributionReqVo hotelCityFromDistributionRequest) {
        // 空安全处理逻辑
        String originKeyword = request.getKeyword();
        String encodedValue = Optional.ofNullable(originKeyword)
                .filter(StringUtils::isNotBlank)  // 过滤空值和纯空格
                .map(k -> {
                    try {
                        // 进行 URL 编码并替换+为%20（更符合 URL 规范）
                        return URLEncoder.encode(k, StandardCharsets.UTF_8.name())
                                .replace("+", "%20");
                    } catch (Exception e) {
                        return "";  // 编码失败返回空字符串
                    }
                })
                .orElse("");  // 原值为空时返回空字符串
        hotelCityFromDistributionRequest.setKeyword(encodedValue); // keyword必须传值
        log.info("setKeyword,原始值originKeyword:{},URL编码encodedValue:{}",originKeyword, encodedValue);
    }
}
