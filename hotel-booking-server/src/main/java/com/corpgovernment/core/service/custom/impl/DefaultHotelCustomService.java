package com.corpgovernment.core.service.custom.impl;

import com.corpgovernment.api.hotel.booking.core.QueryHotelListReqVo;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.domain.common.model.entity.HotelListMetaData;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.hotelconfig.model.entity.SignalTravelStandard;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelCard;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPage;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelSupplier;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControl;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControlRequest;
import com.corpgovernment.core.domain.hotelpage.service.HotelPageDomainService;
import com.corpgovernment.core.service.custom.IHotelCustomService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.Md5Utils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-25 11:59
 */
@Service
@Slf4j
public class DefaultHotelCustomService implements IHotelCustomService {

    @Resource
    private HotelPageDomainService hotelPageDomainService;

    @Override
    public List<TravelControl> getTravelControlList(HotelPageRequest hotelPageRequest, TravelStandard travelStandard, TravelConfig travelConfig, Boolean travelStandardFilter) {
        Optional<SignalTravelStandard> optionalSignalTravelStandard = Optional.ofNullable(travelStandard).map(TravelStandard::getSignalTravelStandard);
        Optional<SignalTravelStandard.AvgPriceTravelStandard> optionalAvgPriceTravelStandard = optionalSignalTravelStandard.map(SignalTravelStandard::getAvgPriceTravelStandard);

        if (Boolean.TRUE.equals(travelStandardFilter)) {
            // 差旅控制
            return  hotelPageDomainService.controlRequest(hotelPageRequest, Collections.singletonList(TravelControlRequest.builder()
                    .priority(1)
                    .canBook(Objects.equals(Optional.ofNullable(travelConfig).map(TravelConfig::getTravelModeEnum).orElse(null), TravelModeEnum.PUB)
                            ? optionalSignalTravelStandard.map(SignalTravelStandard::getCanBook).orElse(null) : Boolean.TRUE)
                    .minPrice(optionalAvgPriceTravelStandard.map(SignalTravelStandard.AvgPriceTravelStandard::getCnyMinPrice).orElse(null))
                    .maxPrice(optionalAvgPriceTravelStandard.map(SignalTravelStandard.AvgPriceTravelStandard::getCnyMaxPrice).orElse(null))
                    .shieldStarList(Optional.ofNullable(travelConfig).map(TravelConfig::getShieldStarList).orElse(null))
                    .brandIdList(optionalSignalTravelStandard.map(SignalTravelStandard::getBrandList).orElse(new ArrayList<>(0)).stream()
                            .filter(Objects::nonNull)
                            .map(SignalTravelStandard.Brand::getBrandId).collect(Collectors.toList()))
                    .starList(optionalSignalTravelStandard.map(SignalTravelStandard::getStarList).orElse(null)).build()));
        }

        return hotelPageDomainService.controlRequest(hotelPageRequest, Collections.singletonList(TravelControlRequest.builder()
                .priority(1)
                .canBook(Boolean.TRUE)
                .shieldStarList(Optional.ofNullable(travelConfig).map(TravelConfig::getShieldStarList).orElse(null)).build()));
    }

    @Override
    public HotelPage queryHotelList(QueryHotelListReqVo requestParam, List<TravelControl> travelControlList, TravelConfig travelConfig) {
        if (CollectionUtils.isEmpty(travelControlList) || travelControlList.get(0) == null || travelControlList.get(0).getHotelPageRequest() == null
                || CollectionUtils.isEmpty(travelControlList.get(0).getHotelPageRequest().getSupplierProductList())
                || CollectionUtils.isNotEmpty(travelControlList.get(0).getControlledList()) || requestParam == null || requestParam.getPageIndex() == null) {
            return null;
        }

        HotelPageRequest hotelPageRequest = travelControlList.get(0).getHotelPageRequest();
        int pageIndex = requestParam.getPageIndex();

        // 元数据缓存key
        String hotelListMetaDataKey = buildHotelListMetaDataKey(requestParam);
        // 获取元数据缓存
        HotelListMetaData hotelListMetaData = hotelPageDomainService.getHotelListMetaData(hotelListMetaDataKey, pageIndex);
        log.info("酒店列表元数据 {}", JsonUtils.toJsonString(hotelListMetaData));

        HotelPage hotelPage;
        if (hotelListMetaData == null || (pageIndex != 1 && (hotelListMetaData.getStartPageIndexMap() == null ||
                hotelPageRequest.getSupplierProductList().stream().map(supplierProduct -> supplierProduct.getSupplierCode() + pageIndex)
                        .anyMatch(item -> !hotelListMetaData.getStartPageIndexMap().containsKey(item))))) {
            throw new CorpBusinessException(HotelResponseCodeEnum.HOTEL_LIST_META_DATA_GET_ERROR);
        }

        // 访问酒店页管理模块
        hotelPage = callHotelPageByCacheMode(hotelListMetaData, hotelPageRequest, pageIndex, travelConfig);
        // 有缓存模式塞缓存
        hotelPageDomainService.setHotelListMetaData(hotelListMetaDataKey, hotelListMetaData);

        return hotelPage;
    }

    @Override
    public void specialHandleHotelDetail(HotelDetail hotelDetail, List<String> prioritySupplierCodeList, Integer hotelRoomSortRule) {
    }

    @Override
    public HotelCustomEnum hotelCustomEnum() {
        return HotelCustomEnum.DEFAULT;
    }

    /**
     * 调用酒店页服务
     * @param hotelListMetaData 酒店列表元数据
     * @param hotelPageRequest 酒店页请求
     * @param pageIndex 页码
     * @param travelConfig 差旅配置
     * @return 酒店列表
     */
    private HotelPage callHotelPageByCacheMode(HotelListMetaData hotelListMetaData, HotelPageRequest hotelPageRequest, Integer pageIndex, TravelConfig travelConfig) {
        if (hotelListMetaData == null || hotelPageRequest == null || pageIndex == null || travelConfig == null || hotelPageRequest.getPageSize() == null) {
            HotelPage hotelPage = new HotelPage();
            hotelPage.setHotelCardList(new ArrayList<>());
            return hotelPage;
        }

        int pageSize = hotelPageRequest.getPageSize();

        // 填充酒店卡片列表
        getHotelCardListMap(hotelListMetaData, hotelPageRequest, pageIndex);

        // 协议优先标志
        boolean protocolPriority = Optional.ofNullable(travelConfig.getHotelListSortRule()).map(item -> item == 1).orElse(false);

        // 聚合供应商卡片
        List<HotelCard> hotelCardList = hotelPageDomainService.aggregationHotelCardListMap(
                hotelListMetaData.getHotelCardListMap(),
                hotelPageRequest.getHotelSort(),
                protocolPriority,
                travelConfig.getPrioritySupplierCodeList(),
                hotelListMetaData.getPrePageLastHotelSupplierCode());
        if (CollectionUtils.isEmpty(hotelCardList)) {
            HotelPage hotelPage = new HotelPage();
            hotelPage.setLastPage(true);
            hotelPage.setHotelCardList(new ArrayList<>(0));
            return hotelPage;
        }

        // 截取
        Map<String, List<HotelCard>> updateHotelCardListMap = new HashMap<>();
        List<HotelCard> resultList = hotelCardList.subList(0, Math.min(hotelCardList.size(), Math.min(hotelCardList.size(), pageSize)));

        // 剩余部分转成updateHotelCardListMap
        if (hotelCardList.size() > pageSize) {
            for (HotelCard hotelCard : hotelCardList.subList(pageSize, hotelCardList.size())) {
                String supplierCode = Optional.ofNullable(hotelCard).map(HotelCard::getSupplierList).map(item -> item.get(0)).map(HotelSupplier::getSupplierCode).orElse(null);
                if (StringUtils.isBlank(supplierCode)) {
                    continue;
                }
                List<HotelCard> tmpList = updateHotelCardListMap.getOrDefault(supplierCode, new ArrayList<>());
                tmpList.add(hotelCard);
                updateHotelCardListMap.put(supplierCode, tmpList);
            }
            for (String supplierCode : updateHotelCardListMap.keySet()) {
                List<HotelCard> tmpList = updateHotelCardListMap.get(supplierCode);
                if (CollectionUtils.isEmpty(tmpList)) {
                    continue;
                }
                updateHotelCardListMap.put(
                        supplierCode,
                        tmpList.stream().sorted(Comparator.comparing(item -> Null.or(item.getDefaultSortNum(), Integer.MAX_VALUE))).collect(Collectors.toList()));
            }
        }

        // 刷新缓存
        hotelListMetaData.setHotelCardListMap(updateHotelCardListMap);
        if (!resultList.isEmpty()) {
            hotelListMetaData.setPrePageLastHotelSupplierCode(
                    Optional.ofNullable(resultList.get(resultList.size()-1)).map(HotelCard::getSupplierList).map(item -> item.get(0)).map(HotelSupplier::getSupplierCode).orElse(null));
        }

        HotelPage hotelPage = new HotelPage();
        hotelPage.setHotelCardList(resultList);
        hotelPage.setLastPage(hotelListMetaData.getLastPageMap().values().stream().allMatch(Boolean.TRUE::equals) && updateHotelCardListMap.values().stream().allMatch(CollectionUtils::isEmpty));
        return hotelPage;
    }

    /**
     * 获取酒店卡片列表map
     * @param hotelListMetaData 酒店列表元数据
     * @param hotelPageRequest 酒店页请求
     * @param pageIndex 页码
     */
    private void getHotelCardListMap(HotelListMetaData hotelListMetaData, HotelPageRequest hotelPageRequest, Integer pageIndex) {
        if (hotelListMetaData == null || hotelPageRequest == null || pageIndex == null || hotelPageRequest.getPageSize() == null) {
            return;
        }

        int pageSize = hotelPageRequest.getPageSize();

        // 已出现酒店set
        Set<String> showedHotelIdSet = Null.or(hotelListMetaData.getShowedHotelIdSet(), new HashSet<>(0));
        // 最后一页标记
        Map<String, Boolean> lastPageMap = Null.or(hotelListMetaData.getLastPageMap(), new HashMap<>(0));
        // 开始页获取
        Map<String, Integer> startPageIndexMap = Null.or(hotelListMetaData.getStartPageIndexMap(), new HashMap<>(0));
        // 酒店卡片列表
        Map<String, List<HotelCard>> hotelCardListMap = Null.or(hotelListMetaData.getHotelCardListMap(), new HashMap<>(0));

        // 调用记录
        Map<Integer, Map<String, HotelPage>> indexHotelPageMap = new HashMap<>();

        // 开始调用
        for (SupplierProduct supplierProduct : hotelPageRequest.getSupplierProductList()) {
            String supplierCode = supplierProduct.getSupplierCode();
            // 获取之前的酒店缓存
            List<HotelCard> tmpLinkedList = hotelCardListMap.getOrDefault(supplierCode, new ArrayList<>());
            // 获取扫描位置
            int startPageIndex = startPageIndexMap.getOrDefault(supplierCode + pageIndex, 1);

            // 直接跳出条件
            if ((pageIndex != 1 && Boolean.TRUE.equals(lastPageMap.get(supplierCode))) || tmpLinkedList.size() >= pageSize) {
                int tmp = pageIndex + 1;
                startPageIndexMap.put(supplierCode + tmp, startPageIndex);
                continue;
            }

            boolean lastPage = false;
            // 最多循环调用5次，防止死循环
            int i = 0;
            int empty = -100;
            for (; i < 3; i++) {
                // 调用记录
                Map<String, HotelPage> hotelPageMap = indexHotelPageMap.get(startPageIndex);
                if (hotelPageMap == null) {
                    hotelPageMap = hotelPageDomainService.getHotelPage(hotelPageRequest, startPageIndex, true);
                    indexHotelPageMap.put(startPageIndex, hotelPageMap);
                }
                log.info("酒店页数据 i={} startPageIndex={} hotelPageMap={}", i, startPageIndex, hotelPageMap);
                if (hotelPageMap == null || hotelPageMap.get(supplierCode) == null || hotelPageMap.get(supplierCode).getHotelCardList() == null) {
                    break;
                }
                List<HotelCard> tmpList = hotelPageMap.get(supplierCode).getHotelCardList();
                int cnt = 0;
                for (HotelCard item : tmpList) {
                    cnt++;
                    List<String> list = item.getSupplierList().stream().map(a -> a.getSupplierCode() + a.getHotelId()).collect(Collectors.toList());
                    // 是否已经出现
                    if (list.stream().anyMatch(showedHotelIdSet::contains)) {
                        showedHotelIdSet.addAll(list);
                        continue;
                    }
                    tmpLinkedList.add(item);
                    showedHotelIdSet.addAll(list);
                    // 如果满了，直接跳出
                    if (tmpLinkedList.size() >= pageSize) {
                        break;
                    }
                }
                // 全部消耗完，并且是最后一页
                if (cnt == tmpList.size() && Boolean.TRUE.equals(hotelPageMap.get(supplierCode).getLastPage())) {
                    lastPage = true;
                    break;
                }
                // 满了
                if (tmpLinkedList.size() >= pageSize) {
                    if (cnt == tmpList.size()) {
                        startPageIndex++;
                    }
                    break;
                }
                // 兜底，如果连续两次返回都是空列表，就默认为最后一页
                if (CollectionUtils.isEmpty(tmpList)) {
                    if (empty + 1 == i) {
                        lastPage = true;
                        break;
                    }
                    empty = i;
                }
                startPageIndex++;
            }
            int tmp = pageIndex + 1;
            startPageIndexMap.put(supplierCode + tmp, startPageIndex);
            lastPageMap.put(supplierCode, lastPage);
            hotelCardListMap.put(supplierCode, tmpLinkedList);
        }

        // 刷新缓存
        hotelListMetaData.setHotelCardListMap(hotelCardListMap);
        hotelListMetaData.setLastPageMap(lastPageMap);
        hotelListMetaData.setStartPageIndexMap(startPageIndexMap);
        hotelListMetaData.setShowedHotelIdSet(showedHotelIdSet);
    }

    /**
     * 构建酒店列表元数据key
     * @param requestParam 请求参数
     * @return 酒店列表元数据key
     */
    private String buildHotelListMetaDataKey(QueryHotelListReqVo requestParam) {
        QueryHotelListReqVo key = JsonUtils.parse(JsonUtils.toJsonString(requestParam), QueryHotelListReqVo.class);
        key.setPageIndex(null);
        return HotelCoreConstant.RedisKey.HOTEL_LIST_META_DATA + HotelCoreConstant.COLON + Md5Utils.md5Hex(key.toString());
    }

}
