package com.corpgovernment.core.service.bo;

import com.corpgovernment.common.enums.ExpenseTypeEnum;
import com.corpgovernment.core.constant.PaymentMethodType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.ServiceChargeInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class RoomInfo {
    // 快照token
    private String token;

    // productID
    private String productId;

    // hotelID
    private String hotelId;

    // roomID
    private String roomId;

    // 订单金额
    private BigDecimal orderAmount;

    // 房费金额
    private BigDecimal roomsAmount;

    // 前收服务费拓展字段=================
    private Map<String/*payType*/, ServiceChargeInfoDTO> serviceChargeInfoMap;
    // ================================

    // 星级
    private Integer star;
    /**
     * 是否挂牌酒店
     */
    private Boolean starLicence;
    // 酒店房型支持的支付方式：在线付/前台到付
    // @com.corpgovernment.core.constant.PaymentMethodType
    private PaymentMethodType paymentMethodType;

    /**
     * 间夜数
     */
    private Integer roomDayCount;

    /**
     * 品牌ID
     */
    private String brandId;


    private String travelStandardMark;

    // 支持公账支付
    private boolean supportACCNT;

    // 支持个人支付
    private boolean supportPPAY;

    // 因公/因私
    ExpenseTypeEnum expensiveType;
    
    // 是直连酒店
    private Boolean directSupplier;
    
    /**
     * 海外酒店差标管控是否包含到店另付税费
     */
    private Boolean overseasHotelControlIncludeExtraTax;
    
    /**
     * 到店另付税费
     */
    private BigDecimal avgExtraTax;
    
    private BigDecimal totalExtraTax;
    
    private Integer roomNum;
    
    private String priceControlStrategy;
    
    private List<DailyPrice> dailyPriceList;
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DailyPrice {
        
        private BigDecimal price;
        
        private String date;
        
    }

}
