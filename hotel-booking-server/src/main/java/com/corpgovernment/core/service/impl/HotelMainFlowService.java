package com.corpgovernment.core.service.impl;

import com.corpgovernment.api.hotel.booking.core.DistrictTravelStandardDetermineReqVo;
import com.corpgovernment.api.hotel.booking.core.DistrictTravelStandardDetermineRespVo;
import com.corpgovernment.api.hotel.booking.core.QueryDynamicFilterReqVo;
import com.corpgovernment.api.hotel.booking.core.QueryDynamicFilterRespVo;
import com.corpgovernment.api.hotel.booking.core.QueryHotelDetailRespVo;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.domain.common.model.entity.CityInfo;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.api.hotel.booking.core.HotelChummageVerifyReqVo;
import com.corpgovernment.api.hotel.booking.core.HotelChummageVerifyRespVo;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.controller.vo.IndicatorGenerateReqVo;
import com.corpgovernment.core.controller.vo.IndicatorGenerateRespVo;
import com.corpgovernment.core.domain.common.gateway.IHotelIndicatorGateway;
import com.corpgovernment.core.domain.common.model.entity.HotelDetailIndicator;
import com.corpgovernment.core.domain.common.model.enums.HotelForceChummageEnum;
import com.corpgovernment.core.domain.common.service.HotelIndicatorDomainService;
import com.corpgovernment.core.domain.hotelconfig.model.entity.DynamicFilter;
import com.corpgovernment.core.domain.hotelconfig.model.entity.DynamicFilterRequest;
import com.corpgovernment.core.domain.hotelconfig.model.entity.Guest;
import com.corpgovernment.core.domain.hotelconfig.model.entity.HotelRoomCheckInInfo;
import com.corpgovernment.core.domain.hotelconfig.model.entity.RcInfo;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.enums.EmployeeTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.GenderEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.HotelChummageVerifyResultEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.RcTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hotelconfig.service.IDynamicFilterDomainService;
import com.corpgovernment.core.domain.hotelconfig.service.IHotelConfigDomainService;
import com.corpgovernment.core.service.IHotelMainFlowService;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @create 2024-10-09 13:36
 * @create 2024-09-12 17:21
 */
@Service
@Slf4j
public class HotelMainFlowService implements IHotelMainFlowService {
    
    @Resource
    private IHotelConfigDomainService hotelConfigDomainService;
    
    @Resource
    private ICommonDomainService commonDomainService;
    
    @Resource
    private IHotelIndicatorGateway hotelIndicatorGateway;
    @Autowired
    private HotelIndicatorDomainService hotelIndicatorDomainService;
    
    @Resource
    private IDynamicFilterDomainService dynamicFilterDomainService;
    
    @Override
    @BusinessBehaviorMonitor
    public HotelChummageVerifyRespVo verifyHotelChummage(HotelChummageVerifyReqVo hotelChummageVerifyReqVo) {
        if (hotelChummageVerifyReqVo == null) {
            return null;
        }
        
        // 获取配置
        TravelConfig travelConfig = hotelConfigDomainService.getTravelConfig(hotelChummageVerifyReqVo.getToken(), null);
        if (travelConfig == null) {
            return null;
        }
        
        // 校验合住
        HotelChummageVerifyResultEnum hotelChummageVerifyResultEnum = hotelConfigDomainService.verifyHotelChummage(
                travelConfig.getHotelForceChummageEnum(),
                travelConfig.getHotelForceSameSexChummage(),
                buildHotelRoomCheckInInfoList(
                        travelConfig.getHotelRoomCheckInInfoList(),
                        hotelChummageVerifyReqVo.getPassengerMap()));
        
        return HotelChummageVerifyRespVo.builder()
                .hotelChummageVerifyResultCode(hotelChummageVerifyResultEnum == null ? null : hotelChummageVerifyResultEnum.getCode())
                .canSelectRc(Objects.equals(travelConfig.getHotelForceChummageEnum(), HotelForceChummageEnum.NO_CHUMMAGE_NEED_SELECT_RC)
                        && (Objects.equals(hotelChummageVerifyResultEnum, HotelChummageVerifyResultEnum.NEED_CHUMMAGE)
                        || Objects.equals(hotelChummageVerifyResultEnum, HotelChummageVerifyResultEnum.NEED_SAME_SEX_CHUMMAGE)))
                .noChummageDesc(hotelConfigDomainService.getNoChummageDesc(hotelChummageVerifyResultEnum, travelConfig.getHotelForceChummageEnum()))
                .noChummageRcList(buildNoChummageRcList(hotelChummageVerifyResultEnum, travelConfig.getHotelForceChummageEnum(), travelConfig.getRcInfoList())).build();
    }
    
    @Override
    public IndicatorGenerateRespVo generateIndicator(IndicatorGenerateReqVo indicatorGenerateReqVo) {
        if (indicatorGenerateReqVo == null) {
            return null;
        }
        List<HotelDetailIndicator> xugongHotelDetailIndicatorList = hotelIndicatorGateway.getXugongHotelDetailIndicatorList(indicatorGenerateReqVo.getStartTime(), indicatorGenerateReqVo.getEndTime());
//        log.info("指标分析数据 xugongHotelDetailIndicatorList={}", JsonUtils.toJsonString(xugongHotelDetailIndicatorList));
        if (CollectionUtils.isEmpty(xugongHotelDetailIndicatorList)) {
            return null;
        }
        for (HotelDetailIndicator hotelDetailIndicator : xugongHotelDetailIndicatorList) {
            hotelIndicatorDomainService.computeHotelDetailIndicator(hotelDetailIndicator);
        }
//        log.info("指标分析结果 xugongHotelDetailIndicatorList={}", JsonUtils.toJsonString(xugongHotelDetailIndicatorList));
        HotelDetailIndicator hotelDetailIndicator = hotelIndicatorDomainService.aggregateHotelDetailIndicator(xugongHotelDetailIndicatorList);
        log.info("指标聚合结果 hotelDetailIndicator={}", JsonUtils.toJsonString(hotelDetailIndicator));
        return buildIndicatorGenerateRespVo(hotelDetailIndicator);
    }
    
    @Override
    public QueryDynamicFilterRespVo queryDynamicFilter(QueryDynamicFilterReqVo requestParam) {
        // 构建请求
        DynamicFilterRequest dynamicFilterRequest = new DynamicFilterRequest();
        dynamicFilterRequest.setToken(requestParam.getToken());
        BizTypeEnum bizTypeEnum = BizTypeEnum.getByCodeOrName(requestParam.getBizType());
        dynamicFilterRequest.setBizTypeEnum(bizTypeEnum);
        TravelModeEnum travelModeEnum = TravelModeEnum.getEnum(requestParam.getTravelMode());
        dynamicFilterRequest.setTravelModeEnum(travelModeEnum);
        // 查询动态筛选项
        DynamicFilter dynamicFilter = dynamicFilterDomainService.queryDynamicFilter(dynamicFilterRequest);
        return toQueryDynamicFilterRespVo(dynamicFilter);
    }
    
    private QueryDynamicFilterRespVo toQueryDynamicFilterRespVo(DynamicFilter dynamicFilter) {
        if (dynamicFilter == null) {
            return null;
        }
        
        QueryDynamicFilterRespVo queryDynamicFilterRespVo = new QueryDynamicFilterRespVo();
        queryDynamicFilterRespVo.setShowHourlyRoomFilter(dynamicFilter.getShowHourlyRoomFilter());
        return queryDynamicFilterRespVo;
    }
    
    private IndicatorGenerateRespVo buildIndicatorGenerateRespVo(HotelDetailIndicator hotelDetailIndicator) {
        if (hotelDetailIndicator == null || hotelDetailIndicator.getIndicatorComputeResult() == null) {
            return null;
        }
        
        return IndicatorGenerateRespVo.builder()
                .xuGongIndicatorList(Collections.singletonList(IndicatorGenerateRespVo.Indicator.builder()
                        .indicatorName("徐工定制详情页无结果指标")
                        .indicatorValue(Optional.ofNullable(hotelDetailIndicator.getRequestCount()).map(String::valueOf).orElse(null))
                        .childIndicatorList(buildXugongHotelDetailNoResultIndicator(hotelDetailIndicator.getIndicatorComputeResult())).build())).build();
    }
    
    private List<IndicatorGenerateRespVo.Indicator> buildXugongHotelDetailNoResultIndicator(HotelDetailIndicator.IndicatorComputeResult indicatorComputeResult) {
        if (indicatorComputeResult == null) {
            return null;
        }

        return Arrays.asList(
                IndicatorGenerateRespVo.Indicator.builder()
                        .indicatorName("供应商无资源")
                        .indicatorValue(Optional.ofNullable(indicatorComputeResult.getSupplierNoResourceCount())
                                .map(String::valueOf)
                                .orElse(null))
                        .build(),
                IndicatorGenerateRespVo.Indicator.builder()
                        .indicatorName("供应商所有房型产品不可订")
                        .indicatorValue(Optional.ofNullable(indicatorComputeResult.getAllCanNotReserveCount())
                                .map(String::valueOf)
                                .orElse(null))
                        .childIndicatorList(buildSupplierCanNotReserveRateIndicator(indicatorComputeResult.getSupplierCanNotReserveRoomRateMap()))
                        .build(),
                IndicatorGenerateRespVo.Indicator.builder()
                        .indicatorName("供应商所有可订房型产品被管控")
                        .indicatorValue(Optional.ofNullable(indicatorComputeResult.getAllOverLimitCount())
                                .map(String::valueOf)
                                .orElse(null))
                        .childIndicatorList(buildAllOverLimitChildIndicator(indicatorComputeResult))
                        .build());
    }
    
    private List<IndicatorGenerateRespVo.Indicator> buildSupplierCanNotReserveRateIndicator(Map<String, Double> supplierCanNotReserveRoomRateMap) {
        if (CollectionUtils.isEmpty(supplierCanNotReserveRoomRateMap)) {
            return null;
        }
        
        List<IndicatorGenerateRespVo.Indicator> indicatorList = new ArrayList<>();
        supplierCanNotReserveRoomRateMap.forEach((supplierCode, rate) -> indicatorList.add(IndicatorGenerateRespVo.Indicator.builder()
                .indicatorName(String.format("供应商%s房型产品不可订占比", supplierCode))
                .indicatorValue(Optional.ofNullable(rate)
                        .map(String::valueOf)
                        .orElse(null))
                .build()));
        return indicatorList;
    }
    
    private List<IndicatorGenerateRespVo.Indicator> buildAllOverLimitChildIndicator(HotelDetailIndicator.IndicatorComputeResult indicatorComputeResult) {
        if (indicatorComputeResult == null) {
            return null;
        }
        
        Integer overLimitRoomCount = indicatorComputeResult.getOverLimitRoomCount();
        HotelDetailIndicator.OverLimitIndicatorComputeResult overLimitIndicatorComputeResult = Null.or(indicatorComputeResult.getOverLimitIndicatorComputeResult(), new HotelDetailIndicator.OverLimitIndicatorComputeResult());
        
        return Arrays.asList(
                IndicatorGenerateRespVo.Indicator.builder()
                        .indicatorName("被管控房型产品数量")
                        .indicatorValue(Optional.ofNullable(overLimitRoomCount)
                                .map(String::valueOf)
                                .orElse(null))
                        .childIndicatorList(Arrays.asList(IndicatorGenerateRespVo.Indicator.builder()
                                        .indicatorName("仅被含早管控房型产品数量")
                                        .indicatorValue(Optional.ofNullable(overLimitIndicatorComputeResult.getOnlyBreakfastOverLimitRoomCount())
                                                .map(String::valueOf)
                                                .orElse(null))
                                        .build(),
                                IndicatorGenerateRespVo.Indicator.builder()
                                        .indicatorName("仅被房型管控房型产品数量")
                                        .indicatorValue(Optional.ofNullable(overLimitIndicatorComputeResult.getOnlyBedTypeOverLimitRoomCount())
                                                .map(String::valueOf)
                                                .orElse(null))
                                        .build(),
                                IndicatorGenerateRespVo.Indicator.builder()
                                        .indicatorName("仅被价格管控房型产品数量")
                                        .indicatorValue(Optional.ofNullable(overLimitIndicatorComputeResult.getOnlyPriceOverLimitRoomCount())
                                                .map(String::valueOf)
                                                .orElse(null))
                                        .build(),
                                IndicatorGenerateRespVo.Indicator.builder()
                                        .indicatorName("被含早+房型管控房型产品数量")
                                        .indicatorValue(Optional.ofNullable(overLimitIndicatorComputeResult.getBreakfastAndBedTypeAndPriceOverLimitRoomCount())
                                                .map(String::valueOf)
                                                .orElse(null))
                                        .build(),
                                IndicatorGenerateRespVo.Indicator.builder()
                                        .indicatorName("被含早+价格管控房型产品数量")
                                        .indicatorValue(Optional.ofNullable(overLimitIndicatorComputeResult.getBreakfastAndPriceOverLimitRoomCount())
                                                .map(String::valueOf)
                                                .orElse(null))
                                        .build(),
                                IndicatorGenerateRespVo.Indicator.builder()
                                        .indicatorName("被房型+价格管控房型产品数量")
                                        .indicatorValue(Optional.ofNullable(overLimitIndicatorComputeResult.getBedTypeAndPriceOverLimitRoomCount())
                                                .map(String::valueOf)
                                                .orElse(null))
                                        .build(),
                                IndicatorGenerateRespVo.Indicator.builder()
                                        .indicatorName("被含早+房型+价格管控房型产品数量")
                                        .indicatorValue(Optional.ofNullable(overLimitIndicatorComputeResult.getBreakfastAndBedTypeAndPriceOverLimitRoomCount())
                                                .map(String::valueOf)
                                                .orElse(null))
                                        .build()))
                        .build(),
                IndicatorGenerateRespVo.Indicator.builder()
                        .indicatorName("供应商房型产品被管控占比")
                        .childIndicatorList(buildSupplierOverLimitRateIndicator(indicatorComputeResult.getSupplierOverLimitRoomRateMap()))
                        .build()
                );
    }
    
    private List<IndicatorGenerateRespVo.Indicator> buildSupplierOverLimitRateIndicator(Map<String, Double> supplierOverLimitRoomRateMap) {
        if (CollectionUtils.isEmpty(supplierOverLimitRoomRateMap)) {
            return null;
        }
        
        List<IndicatorGenerateRespVo.Indicator> indicatorList = new ArrayList<>();
        supplierOverLimitRoomRateMap.forEach((supplierCode, rate) -> indicatorList.add(IndicatorGenerateRespVo.Indicator.builder()
                .indicatorName(String.format("供应商%s房型产品被管控占比", supplierCode))
                .indicatorValue(Optional.ofNullable(rate)
                        .map(String::valueOf)
                        .orElse(null))
                .build()));
        return indicatorList;
    }
    
    private List<HotelRoomCheckInInfo> buildHotelRoomCheckInInfoList(List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList,
                                                                     Map<Integer, List<HotelChummageVerifyReqVo.PassengerInfo>> passengerMap) {
        if (CollectionUtils.isNotEmpty(passengerMap)) {
            return buildHotelRoomCheckInInfoList(passengerMap);
        }
        return hotelRoomCheckInInfoList;
    }
    
    private List<HotelRoomCheckInInfo> buildHotelRoomCheckInInfoList(Map<Integer, List<HotelChummageVerifyReqVo.PassengerInfo>> passengerMap) {
        if (CollectionUtils.isEmpty(passengerMap)) {
            return null;
        }
        
        List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList = new ArrayList<>();
        passengerMap.forEach((key, value) -> {
            List<Guest> guestList = buildGuestList(value);
            
            if (CollectionUtils.isEmpty(guestList)) {
                return;
            }
            
            hotelRoomCheckInInfoList.add(HotelRoomCheckInInfo.builder()
                    .roomIndex(key)
                    .guestList(guestList).build());
        });
        
        return hotelRoomCheckInInfoList;
    }
    
    private List<Guest> buildGuestList(List<HotelChummageVerifyReqVo.PassengerInfo> value) {
        if (CollectionUtils.isEmpty(value)) {
            return null;
        }
        
        List<Guest> guestList = new ArrayList<>();
        for (HotelChummageVerifyReqVo.PassengerInfo passengerInfo : value) {
            if (passengerInfo == null) {
                continue;
            }
            
            EmployeeTypeEnum employeeTypeEnum = EmployeeTypeEnum.getEnum(passengerInfo.getEmployeeType() == null ? null : passengerInfo.getEmployeeType().toString());
            
            guestList.add(Guest.builder()
                    .uid(Objects.equals(employeeTypeEnum, EmployeeTypeEnum.EXTERNAL_EMPLOYEE) ? passengerInfo.getNoEmployeeId() : passengerInfo.getUid())
                    .employeeTypeEnum(employeeTypeEnum)
                    .orgId(passengerInfo.getOrgId())
                    .genderEnum(GenderEnum.getEnum(passengerInfo.getGender())).build());
        }
        return guestList;
    }
    
    private List<HotelChummageVerifyRespVo.RcInfo> buildNoChummageRcList(HotelChummageVerifyResultEnum hotelChummageVerifyResultEnum, HotelForceChummageEnum hotelForceChummageEnum, List<RcInfo> rcInfoList) {
        // 校验通过
        if (hotelChummageVerifyResultEnum == null || hotelChummageVerifyResultEnum == HotelChummageVerifyResultEnum.VERIFY_PASS || CollectionUtils.isEmpty(rcInfoList)) {
            return null;
        }
        
        // 配置选择rc
        if (!Objects.equals(hotelForceChummageEnum, HotelForceChummageEnum.NO_CHUMMAGE_NEED_SELECT_RC)) {
            return null;
        }
        
        List<HotelChummageVerifyRespVo.RcInfo> resultList = new ArrayList<>();
        for (RcInfo rcInfo : rcInfoList) {
            if (rcInfo == null || rcInfo.getRcTypeEnumList() == null || !rcInfo.getRcTypeEnumList().contains(RcTypeEnum.NOT_CO_LIVING)) {
                continue;
            }
            resultList.add(HotelChummageVerifyRespVo.RcInfo.builder()
                    .id(rcInfo.getId())
                    .code(rcInfo.getCode())
                    .name(rcInfo.getName())
                    .remarkConfigCode(rcInfo.getRemarkConfigEnum().getCode()).build());
        }
        return resultList;
    }
    
    @Override
    public DistrictTravelStandardDetermineRespVo determineDistrictTravelStandard(DistrictTravelStandardDetermineReqVo requestParam) {
        // 获取酒店模式
        HotelCustomEnum hotelCustomEnum = hotelConfigDomainService.getHotelCustomEnum(requestParam.getToken());
        // 徐工定制无区县差标
        if (Objects.equals(hotelCustomEnum, HotelCustomEnum.XU_GONG)) {
            log.info("徐工定制无区县差标");
            return DistrictTravelStandardDetermineRespVo.builder()
                    .travelStandardChange(false).build();
        }
        
        // 获取城市信息
        CityInfo cityInfo = commonDomainService.getCityInfo(requestParam.getHotelLon(), requestParam.getHotelLat());
        if (cityInfo == null) {
            return null;
        }
        
        // 确定差标
        String travelStandardCityName = hotelConfigDomainService.determineDistrictTravelStandard(
                requestParam.getToken(),
                requestParam.getCityId(),
                cityInfo.getDistrictId(),
                cityInfo.getCountyId());
        
        if (StringUtils.isNotBlank(travelStandardCityName)) {
            return DistrictTravelStandardDetermineRespVo.builder()
                    .travelStandardChange(true)
                    .travelStandardChangeDesc("当前酒店将使用【" + travelStandardCityName + "】的差标进行管控")
                    .districtId(cityInfo.getDistrictId())
                    .countyId(cityInfo.getCountyId()).build();
        } else {
            return DistrictTravelStandardDetermineRespVo.builder()
                    .travelStandardChange(false).build();
        }
    }
    
}
