package com.corpgovernment.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.corpgovernment.api.hotel.booking.core.HourlyRoomInfoVo;
import com.corpgovernment.basic.convert.HotelProductSnapshotConvert;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HourlyRoomInfo;
import com.corpgovernment.core.domain.openapi.service.SnapshotGateway;
import com.corpgovernment.dto.snapshot.dto.hotel.request.SaveHotelProductSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.response.SaveHotelProductSnapshotResponse;
import com.corpgovernment.orgsdk.client.ContentDicitionaryClient;
import com.corpgovernment.orgsdk.response.AgreementInfoQueryResp;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.hotel.booking.core.MemberTag;
import com.corpgovernment.api.hotel.booking.core.QueryHotelDetailReqVo;
import com.corpgovernment.api.hotel.booking.core.QueryHotelDetailRespVo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.domain.common.model.entity.MapInfo;
import com.corpgovernment.core.domain.common.model.entity.OverLimitReasonCode;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.common.service.IHotelIndicatorDomainService;
import com.corpgovernment.core.domain.hotelconfig.model.entity.SignalTravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelApplication;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hotelconfig.service.IHotelConfigDomainService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.ApplicativeArea;
import com.corpgovernment.core.domain.hoteldetail.model.entity.ApplyTripOverLimitReminder;
import com.corpgovernment.core.domain.hoteldetail.model.entity.ArrivalAndDeparturePolicy;
import com.corpgovernment.core.domain.hoteldetail.model.entity.BasicRoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.CancelPolicy;
import com.corpgovernment.core.domain.hoteldetail.model.entity.ChargingPile;
import com.corpgovernment.core.domain.hoteldetail.model.entity.ChildAndAddBedPolicy;
import com.corpgovernment.core.domain.hoteldetail.model.entity.City;
import com.corpgovernment.core.domain.hoteldetail.model.entity.DailyRate;
import com.corpgovernment.core.domain.hoteldetail.model.entity.District;
import com.corpgovernment.core.domain.hoteldetail.model.entity.Facility;
import com.corpgovernment.core.domain.hoteldetail.model.entity.FacilityGroup;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelBaseInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetailFilter;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetailRequest;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelFacility;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelPolicyService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.Item;
import com.corpgovernment.core.domain.hoteldetail.model.entity.MealInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.MealPolicy;
import com.corpgovernment.core.domain.hoteldetail.model.entity.MemberTagInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.NearBy;
import com.corpgovernment.core.domain.hoteldetail.model.entity.NearByGroup;
import com.corpgovernment.core.domain.hoteldetail.model.entity.OverLimitInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.PackageProduct;
import com.corpgovernment.core.domain.hoteldetail.model.entity.ParkingLot;
import com.corpgovernment.core.domain.hoteldetail.model.entity.PaymentTool;
import com.corpgovernment.core.domain.hoteldetail.model.entity.PersonPrice;
import com.corpgovernment.core.domain.hoteldetail.model.entity.Picture;
import com.corpgovernment.core.domain.hoteldetail.model.entity.ReservationNotice;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomBaseInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomControl;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPackage;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPolicyService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPrice;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomSupplier;
import com.corpgovernment.core.domain.hoteldetail.model.entity.StepCancelPolicy;
import com.corpgovernment.core.domain.hoteldetail.model.entity.SupplierStarInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.Video;
import com.corpgovernment.core.domain.hoteldetail.model.entity.Zone;
import com.corpgovernment.core.domain.hoteldetail.model.enums.BalanceTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.MapTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.MealTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.NearByGroupTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.WindowEnum;
import com.corpgovernment.core.domain.hoteldetail.service.IHotelDetailDomainService;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelSupplier;
import com.corpgovernment.core.service.IHotelDetailService;
import com.corpgovernment.core.service.custom.factory.HotelCustomServiceFactory;
import com.corpgovernment.hotel.product.dataloader.db.MsBaseCtripCorpBrandLoader;
import com.corpgovernment.hotel.product.entity.db.MsBaseCtripCorpBrand;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.Conditional;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @date 2024/5/17
 */
@Service
@Slf4j
public class HotelDetailService implements IHotelDetailService {

    @Resource
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;

    @Resource
    private IHotelConfigDomainService hotelConfigDomainService;

    @Resource
    private ICommonDomainService commonDomainService;

    @Resource
    private IHotelDetailDomainService hotelDetailDomainService;

    @Resource
    private HotelCustomServiceFactory hotelCustomServiceFactory;

    @Resource
    private IHotelIndicatorDomainService hotelIndicatorDomainService;

    @Resource
    private MsBaseCtripCorpBrandLoader msBaseCtripCorpBrandLoader;

    @Autowired
    private ContentDicitionaryClient contentDicitionaryClient;

    public static final String AGREEMENT_TAG_ENABLE = "1";

    @Resource
    private HotelProductSnapshotConvert hotelProductSnapshotConvert;
    @Resource
    private SnapshotGateway snapshotGateway;
    @Override
    @BusinessBehaviorMonitor
    public QueryHotelDetailRespVo queryHotelDetail(QueryHotelDetailReqVo requestParam) {
        QueryHotelDetailRespVo response = null;
        HotelCustomEnum hotelCustomEnum = null;
        try {
            // 参数校验
            if (CollectionUtils.isEmpty(requestParam.getHotelSupplierList()) || StringUtils.isBlank(requestParam.getToken()) || StringUtils.isBlank(requestParam.getTravelMode())
                    || requestParam.getGuestQuantity() == null || requestParam.getGuestQuantity() <= 0 || requestParam.getRoomQuantity() == null || requestParam.getRoomQuantity() <= 0
                    || StringUtils.isBlank(requestParam.getCheckInDate()) || StringUtils.isBlank(requestParam.getCheckOutDate())) {
                throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_PARAM_ERROR);
            }

            // 停留时间校验
            if (StringUtils.isBlank(commonDomainService.getStayTime())) {
                throw new CorpBusinessException(HotelResponseCodeEnum.STAY_TIME_CHECK_FAIL);
            }

            // 获取差旅配置
            TravelConfig travelConfig = getTravelConfig(requestParam.getToken(), requestParam.getTravelMode(), requestParam.getProductType());

            // 获取差旅标准
            TravelStandard travelStandard = getTravelStandard(requestParam.getToken(), requestParam.getTravelMode());

            // 获取供应商产品列表
            List<SupplierProduct> supplierProductList = hotelConfigDomainService.getSupplierProductList(travelConfig, hotelOperatorTypeConfig.getGetDomesticHotelDetail(), requestParam.getHasHourlyRoom());
            if (CollectionUtils.isEmpty(supplierProductList)) {
                throw new CorpBusinessException(HotelResponseCodeEnum.SUPPLIER_CONFIG_GET_ERROR);
            }

            // 获取全量酒店详情
            List<String> prioritySupplierCodeList = supplierProductList.stream().filter(Objects::nonNull).map(SupplierProduct::getSupplierCode).collect(Collectors.toList());
            HotelDetailRequest hotelDetailRequest = HotelDetailRequest.builder().token(requestParam.getToken())
                    .hotelSupplierList(requestParam.getHotelSupplierList().stream().filter(Objects::nonNull).map(item -> HotelSupplier.builder()
                            .supplierCode(item.getSupplierCode())
                            .hotelId(item.getHotelId()).build()).collect(Collectors.toList()))
                    .checkInDate(requestParam.getCheckInDate())
                    .checkOutDate(requestParam.getCheckOutDate())
                    .guestQuantity(requestParam.getGuestQuantity())
                    .roomQuantity(requestParam.getRoomQuantity())
                    .abroad(BizTypeEnum.HOTEL_INTL.equals(travelConfig.getBizTypeEnum()))
                    .hotelDetailSupplierProductList(supplierProductList)
                    .roomPackageDetailSupplierProductList(hotelConfigDomainService.getSupplierProductList(travelConfig, hotelOperatorTypeConfig.getSearchPackageRoom(), requestParam.getHasHourlyRoom()))
                    .prioritySupplierCodeList(prioritySupplierCodeList)
                    .hotelRoomSortRule(travelConfig.getHotelRoomSortRule())
                    .baseSupplierCode(Conditional.ofEmptyAble(requestParam.getHotelSupplierList())
                            .map(list -> list.stream()
                                    .map(QueryHotelDetailReqVo.HotelSupplier::getSupplierCode)
                                    .findFirst().orElse(null))
                            .get())
                    .resourcePriceIncludeServiceCharge(travelConfig.extractFilterWithServiceCharge())
                    .travelMode(travelConfig.getTravelModeEnum())
                    .hasHourlyRoom(requestParam.getHasHourlyRoom())
                    .build();
            HotelDetail hotelDetail = hotelDetailDomainService.getHotelDetail(hotelDetailRequest, requestParam.getUseCache());
            if (hotelDetail == null) {
                throw new CorpBusinessException(HotelResponseCodeEnum.HOTEL_DETAIL_GET_ERROR);
            }

            // 处理酒店积分
            hotelDetailDomainService.processHotelBonusPoint(hotelDetail);
            
            if (TravelModeEnum.PUB.name().equals(requestParam.getTravelMode())) {
                // 校验差标
                Boolean success = hotelDetailDomainService.verifyTravelStandard(
                        hotelDetail,
                        requestParam.getToken(),
                        requestParam.getTravelStandardMark(),
                        travelConfig.getBizTypeEnum(),
                        commonDomainService.getRoomNightNum(requestParam.getCheckInDate(), requestParam.getCheckOutDate(), requestParam.getRoomQuantity()),
                        hotelConfigDomainService.getPaymentMethodList(travelStandard, travelConfig),
                        travelConfig.getOverseasHotelControlIncludeExtraTax(),
                        travelConfig.getPriceControlStrategyEnum());
                if (!Boolean.TRUE.equals(success)) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.TRAVEL_STANDARD_VERIFY_ERROR);
                }
                log.info("差标校验后 hotelDetail={}", JsonUtils.toJsonString(hotelDetail));
                
                // 出差申请单预算管控
                hotelDetail = hotelDetailDomainService.verifyApplyTrip(
                        hotelDetail,
                        Optional.ofNullable(travelStandard)
                                .map(TravelStandard::getTravelApplication)
                                .map(TravelApplication::getTravelId)
                                .orElse(null),
                        requestParam.getProductType());
            }

            // 处理酒店详情
            hotelDetail = hotelDetailDomainService.handleRoomControl(hotelDetail,
                    travelConfig.getOverseasHotelControlIncludeExtraTax());

            QueryHotelDetailRespVo queryHotelDetailProductSnapshot = assembleResponse(hotelDetail, travelConfig, travelStandard);
            log.info("组装产品快照后 queryHotelDetailProductSnapshot:{}", JsonUtils.toJsonString(queryHotelDetailProductSnapshot));
            // 保存产品快照
            SaveHotelProductSnapshotRequest saveHotelProductSnapshotRequest = hotelProductSnapshotConvert.saveHotelProductSnapshotRequestConvert(queryHotelDetailProductSnapshot,requestParam.getToken());
            log.info("保存产品快照 saveHotelProductSnapshotRequest:{}", JsonUtils.toJsonString(saveHotelProductSnapshotRequest));
            SaveHotelProductSnapshotResponse saveHotelProductSnapshotResponse = snapshotGateway.saveHotelProductSnapshot(saveHotelProductSnapshotRequest);
            log.info("保存产品快照后 saveHotelProductSnapshotRequest:{},saveHotelProductSnapshotResponse:{}",
                    JsonUtils.toJsonString(saveHotelProductSnapshotRequest),JsonUtils.toJsonString(saveHotelProductSnapshotResponse));

            // 筛选
            hotelDetailDomainService.filterRoom(hotelDetail, HotelDetailFilter.builder()
                    .travelStandardFilter(requestParam.getTravelStandardFilter())
                    .protocolRoomFilter(requestParam.getProtocolRoomFilter())
                    .hasBreakfastFilter(requestParam.getHasBreakfastFilter())
                    .justifyConfirmFilter(requestParam.getJustifyConfirmFilter())
                    .freeCancelFilter(requestParam.getFreeCancelFilter())
                    .onlinePayFilter(requestParam.getOnlinePayFilter())
                    .hotelBonusPointFilter(requestParam.getHotelBonusPointFilter())
                    .supplierCodeFilter(requestParam.getSupplierCodeFilter())
                    .fullAmountOnlinePayFilter(requestParam.getFullAmountOnlinePayFilter())
                    .hourlyRoomFilter(requestParam.getHasHourlyRoom())
                    .build());
            log.info("筛选后 hotelDetail={}", JsonUtils.toJsonString(hotelDetail));

            // 获取定制枚举
            hotelCustomEnum = hotelCustomServiceFactory.getHotelCustomEnum(travelStandard);
            log.info("定制模式 hotelCustomEnum={}", JsonUtils.toJsonString(hotelCustomEnum));

            // 特殊处理
            hotelCustomServiceFactory.specialHandleHotelDetail(hotelCustomEnum, hotelDetail, prioritySupplierCodeList, travelConfig.getHotelRoomSortRule());
            log.info("定制模式处理后 hotelDetail={}", JsonUtils.toJsonString(hotelDetail));

            // 浏览过记录
            commonDomainService.setViewedHotel(getHotelIdList(requestParam.getHotelSupplierList()));

            response = assembleResponse(hotelDetail, travelConfig, travelStandard);
            return response;
        } finally {
            hotelIndicatorDomainService.queryHotelDetail(requestParam, response, hotelCustomEnum);
        }
    }

    /**
     * 获取酒店ID列表
     * @param hotelSupplierList 酒店供应商列表
     * @return 酒店ID列表
     */
    private List<String> getHotelIdList(List<QueryHotelDetailReqVo.HotelSupplier> hotelSupplierList) {
        if (CollectionUtils.isEmpty(hotelSupplierList)) {
            return null;
        }

        return hotelSupplierList.stream()
                .filter(item -> item != null && item.getHotelId() != null && item.getSupplierCode() != null)
                .map(item -> item.getSupplierCode() + item.getHotelId()).distinct().collect(Collectors.toList());
    }

    /**
     * 获取差标
     * @param token token
     * @param travelMode 差旅模式
     * @return 差标
     */
    private TravelStandard getTravelStandard(String token, String travelMode) {
        if (!TravelModeEnum.PUB.getCode().equals(travelMode)) {
            return null;
        }

        // 获取差标
        TravelStandard travelStandard = hotelConfigDomainService.getTravelStandard(token);
        if (travelStandard == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.TRAVEL_STANDARD_GET_ERROR);
        }

        // 出差申请单
        TravelApplication travelApplication = travelStandard.getTravelApplication();
        if (travelStandard.getTravelApplication() == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.TRAVEL_APPLICATION_GET_ERROR);
        }

        // 如果有出差申请单
        if (StringUtils.isNotBlank(travelApplication.getTravelId())) {
            // 出差申请单是否含有经纬度
            if (Boolean.TRUE.equals(hotelConfigDomainService.checkLatLonTravelApplication(travelApplication))) {
                // 多差标
                if (travelStandard.getMultiTravelStandard() == null) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.MULTI_TRAVEL_STANDARD_GET_ERROR);
                }
            }
            else {
                // 单差标
                if (travelStandard.getSignalTravelStandard() == null) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.SIGNAL_TRAVEL_STANDARD_GET_ERROR);
                }
            }
        }
        else {
            // 单差标
            if (travelStandard.getSignalTravelStandard() == null) {
                throw new CorpBusinessException(HotelResponseCodeEnum.SIGNAL_TRAVEL_STANDARD_GET_ERROR);
            }
        }

        return travelStandard;
    }

    /**
     * 获取差旅配置
     * @param token token
     * @param travelMode 差旅模式
     * @param productType 产线
     * @return 差旅配置
     */
    private TravelConfig getTravelConfig(String token, String travelMode, String productType) {
        // 获取差旅配置
        TravelConfig travelConfig = hotelConfigDomainService.getTravelConfig(
                token,
                travelMode,
                productType,
                Arrays.asList(hotelOperatorTypeConfig.getGetDomesticHotelDetail(), hotelOperatorTypeConfig.getSearchPackageRoom()));

        // 差旅配置为空
        if (travelConfig == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.TRAVEL_CONFIG_GET_ERROR);
        }

        // 差线类型为空
        BizTypeEnum bizTypeEnum = travelConfig.getBizTypeEnum();
        if (bizTypeEnum == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.BIZ_TYPE_ENUM_GET_ERROR);
        }

        // 供应商配置为空
        Map<String, List<SupplierProduct>> supplierProductListMap = travelConfig.getSupplierProductListMap();
        if (CollectionUtils.isEmpty(travelConfig.getPrioritySupplierCodeList()) || CollectionUtils.isEmpty(supplierProductListMap)
                || CollectionUtils.isEmpty(supplierProductListMap.get(hotelOperatorTypeConfig.getGetDomesticHotelDetail()))) {
            throw new CorpBusinessException(HotelResponseCodeEnum.SUPPLIER_CONFIG_GET_ERROR);
        }

        return travelConfig;
    }

    /**
     * 组装返回
     * @param hotelDetail 酒店详情
     * @param travelConfig 差旅配置
     * @param travelStandard 差标
     * @return 返回
     */
    private QueryHotelDetailRespVo assembleResponse(HotelDetail hotelDetail, TravelConfig travelConfig, TravelStandard travelStandard) {
        log.info("组装返回参数, hotelDetail:{},travelConfig:{},travelStandard:{}", JsonUtils.toJsonString(hotelDetail),JsonUtils.toJsonString(travelConfig),JsonUtils.toJsonString(travelStandard));
        if (hotelDetail == null) {
            return null;
        }
        QueryHotelDetailRespVo queryHotelDetailRespVo = new QueryHotelDetailRespVo();
        // 组装globalInfo
        assembleGlobalInfo(queryHotelDetailRespVo, hotelDetail, travelConfig, travelStandard);
        // 组装酒店数据
        assembleHotelResponse(queryHotelDetailRespVo, hotelDetail);
        // 组装房型数据
        assembleRoomResponse(queryHotelDetailRespVo, hotelDetail);
        log.info("组装返回参数, queryHotelDetailRespVo:{}", JsonUtils.toJsonString(queryHotelDetailRespVo));
        return queryHotelDetailRespVo;
    }

    /**
     * 组装globalInfo
     * @param queryHotelDetailRespVo 返回
     * @param hotelDetail 酒店详情
     * @param travelConfig 差旅配置
     * @param travelStandard 差标
     */
    private void assembleGlobalInfo(QueryHotelDetailRespVo queryHotelDetailRespVo, HotelDetail hotelDetail, TravelConfig travelConfig, TravelStandard travelStandard) {
        log.info("组装globalInfo, queryHotelDetailRespVo:{},hotelDetail:{},travelConfig:{},travelStandard:{}",
                JsonUtils.toJsonString(queryHotelDetailRespVo),JsonUtils.toJsonString(hotelDetail),JsonUtils.toJsonString(travelConfig),JsonUtils.toJsonString(travelStandard));
        if (queryHotelDetailRespVo == null) {
            return;
        }
        QueryHotelDetailRespVo.GlobalInfo tmpGlobalInfo = new QueryHotelDetailRespVo.GlobalInfo();

        tmpGlobalInfo.setHotelBonusPointDescList(hotelDetail.getHotelBonusPointDescList());

        // 支付方式
        List<String> paymentMethodList = hotelConfigDomainService.getPaymentMethodList(travelStandard, travelConfig);
        tmpGlobalInfo.setPaymentMethodList(paymentMethodList);
        // 公司简称
        AgreementInfoQueryResp content = getContent();
        if (StringUtils.isNotBlank(content.getTwoContent())&&content.getTwoSwitch().equals(AGREEMENT_TAG_ENABLE)&&content!=null){
            tmpGlobalInfo.setCorpBaShortName(content.getTwoContent());
        }
        if (StringUtils.isNotBlank(content.getThreeContent())&&content.getThreeSwitch().equals(AGREEMENT_TAG_ENABLE)&&content!=null){
            tmpGlobalInfo.setCorpShortName(content.getThreeContent());
        }
        // 海外酒店差标管控是否包含到店另付税费
        tmpGlobalInfo.setOverseasHotelControlIncludeExtraTax(travelConfig.getOverseasHotelControlIncludeExtraTax());

        if (travelStandard != null && travelStandard.getSignalTravelStandard() != null) {
            SignalTravelStandard signalTravelStandard = travelStandard.getSignalTravelStandard();

            // 均价差标
            SignalTravelStandard.AvgPriceTravelStandard avgPriceTravelStandard = signalTravelStandard.getAvgPriceTravelStandard();
            if (avgPriceTravelStandard != null) {
                QueryHotelDetailRespVo.AvgPriceControl tmpAvgPriceControl = new QueryHotelDetailRespVo.AvgPriceControl();
                tmpAvgPriceControl.setCnyMaxPrice(avgPriceTravelStandard.getCnyMaxPrice());
                tmpAvgPriceControl.setForeignCurrency(avgPriceTravelStandard.getForeignCurrency());
                tmpAvgPriceControl.setForeignMaxPrice(avgPriceTravelStandard.getForeignMaxPrice());
                tmpAvgPriceControl.setCnyMinPrice(avgPriceTravelStandard.getCnyMinPrice());
                tmpAvgPriceControl.setForeignMinPrice(avgPriceTravelStandard.getForeignMinPrice());
                tmpGlobalInfo.setAvgPriceControl(tmpAvgPriceControl);
            }

            // 星级差标
            QueryHotelDetailRespVo.StarControl starControl = new QueryHotelDetailRespVo.StarControl();
            List<Integer> starList = signalTravelStandard.getStarList();
            if (CollectionUtils.isNotEmpty(starList)) {
                starList = starList.stream().filter(item -> item != null && item != 1).collect(Collectors.toList());
            }
            starControl.setStarList(starList);
            tmpGlobalInfo.setStarControl(starControl);

            // 品牌差标
            QueryHotelDetailRespVo.BrandControl brandControl = new QueryHotelDetailRespVo.BrandControl();
            List<QueryHotelDetailRespVo.Brand> tmpBrandList = new ArrayList<>();
            List<SignalTravelStandard.Brand> brandList = signalTravelStandard.getBrandList();
            if (CollectionUtils.isNotEmpty(brandList)) {
                for (SignalTravelStandard.Brand brand : brandList) {
                    if (brand == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.Brand tmpBrand = new QueryHotelDetailRespVo.Brand();
                    tmpBrand.setBrandId(brand.getBrandId());
                    tmpBrand.setBrandName(brand.getBrandName());
                    tmpBrandList.add(tmpBrand);
                }
            }
            brandControl.setBrandList(tmpBrandList);
            tmpGlobalInfo.setBrandControl(brandControl);

            // 预定配置
            tmpGlobalInfo.setBookable(signalTravelStandard.getCanBook());

            // 超标管控方式
            tmpGlobalInfo.setOverLimitModeList(commonDomainService.getOverLimitModeList(signalTravelStandard.getOverLimitModeList(), paymentMethodList));

            List<OverLimitReasonCode> overLimitReasonCodeList = signalTravelStandard.getOverLimitReasonCodeList();
            if (CollectionUtils.isNotEmpty(overLimitReasonCodeList)) {
                List<QueryHotelDetailRespVo.OverLimitReasonCode> tmpOverLimitReasonCodeList = new ArrayList<>();
                for (OverLimitReasonCode overLimitReasonCode : overLimitReasonCodeList) {
                    if (overLimitReasonCode == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.OverLimitReasonCode tmpOverLimitReasonCode = new QueryHotelDetailRespVo.OverLimitReasonCode();
                    tmpOverLimitReasonCode.setId(overLimitReasonCode.getId());
                    tmpOverLimitReasonCode.setName(overLimitReasonCode.getName());
                    tmpOverLimitReasonCodeList.add(tmpOverLimitReasonCode);
                }
                if (CollectionUtils.isNotEmpty(tmpOverLimitReasonCodeList)) {
                    tmpGlobalInfo.setOverLimitReasonCodeList(tmpOverLimitReasonCodeList);
                }
            }
        }
        log.info("组装globalInfo, tmpGlobalInfo:{}", JsonUtils.toJsonString(tmpGlobalInfo));
        queryHotelDetailRespVo.setGlobalInfo(tmpGlobalInfo);
    }

    private Map<String, String> searchGroupBaseInfo(HotelDetail hotelDetail) {
        List<String> allRoomGroupIdList = hotelDetail.getBasicRoomCardList().stream()
                .filter(basicRoomCard -> basicRoomCard != null
                        && CollectionUtils.isNotEmpty(basicRoomCard.getRoomCardList()))
                .map(BasicRoomCard::getRoomCardList)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(RoomCard::getGroupId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        return msBaseCtripCorpBrandLoader.queryGroupInfoByGroupIdList(allRoomGroupIdList)
                .stream()
                .collect(Collectors.toMap(MsBaseCtripCorpBrand::getBrandId,
                        MsBaseCtripCorpBrand::getBrandName,
                        (a, b) -> a));

    }

    /**
     * 组装酒店数据
     * @param queryHotelDetailRespVo 返回
     * @param hotelDetail 酒店详情
     */
    private void assembleRoomResponse(QueryHotelDetailRespVo queryHotelDetailRespVo, HotelDetail hotelDetail) {
        log.info("assembleRoomResponse start,queryHotelDetailRespVo:{},hotelDetail:{}",JsonUtils.toJsonString(queryHotelDetailRespVo),JsonUtils.toJsonString(hotelDetail));
        if (queryHotelDetailRespVo == null || hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return;
        }
        // 当前所有房型的集团基本信息；key：集团id；value：集团名称
        Map<String, String> groupInfoMap = searchGroupBaseInfo(hotelDetail);
        log.info("assembleRoomResponse groupInfoMap:{}", JsonUtils.toJsonString(groupInfoMap));

        List<QueryHotelDetailRespVo.BasicRoomCard> tmpBasicRoomCardList = new ArrayList<>();
        for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
            if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                continue;
            }
            QueryHotelDetailRespVo.BasicRoomCard tmpBasicRoomCard = new QueryHotelDetailRespVo.BasicRoomCard();
            tmpBasicRoomCard.setVirtualBasicRoomId(basicRoomCard.getVirtualBasicRoomId());
            tmpBasicRoomCard.setPictureList(basicRoomCard.getPictureList());
            tmpBasicRoomCard.setName(basicRoomCard.getName());
            tmpBasicRoomCard.setBedDesc(basicRoomCard.getBedDesc());
            tmpBasicRoomCard.setAreaDesc(basicRoomCard.getAreaDesc());
            tmpBasicRoomCard.setFloorDesc(basicRoomCard.getFloorDesc());
            tmpBasicRoomCard.setWindowDesc(Optional.ofNullable(basicRoomCard.getWindowEnum()).map(WindowEnum::getDesc).orElse(null));
            if (basicRoomCard.getMinRoomPrice() != null && basicRoomCard.getMinRoomPrice().getAvgPriceIncludeTax() != null && basicRoomCard.getMinRoomPrice().getAvgPriceIncludeTax().getCustomPrice() != null) {
                tmpBasicRoomCard.setMinAvgPriceIncludeTax(basicRoomCard.getMinRoomPrice().getAvgPriceIncludeTax().getCustomPrice());
            }
            tmpBasicRoomCard.setMinRoomPrice(assembleRoomPrice(basicRoomCard.getMinRoomPrice()));
            tmpBasicRoomCard.setCanReserve(basicRoomCard.getCanReserve());
            tmpBasicRoomCard.setProtocolType(basicRoomCard.getProtocolType());

            List<QueryHotelDetailRespVo.RoomCard> roomCardList = new ArrayList<>();
            for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                if (roomCard == null) {
                    continue;
                }
                QueryHotelDetailRespVo.RoomCard tmpRoomCard = new QueryHotelDetailRespVo.RoomCard();
                tmpRoomCard.setCityId(Optional.ofNullable(roomCard.getCity()).map(City::getCityId).orElse(null));
                tmpRoomCard.setDistrictId(Optional.ofNullable(roomCard.getDistrict()).map(District::getDistrictId).orElse(null));
                tmpRoomCard.setZoneList(Null.or(roomCard.getZoneList(), new ArrayList<Zone>(0)).stream().filter(Objects::nonNull).map(item -> {
                    QueryHotelDetailRespVo.Zone tmpZone = new QueryHotelDetailRespVo.Zone();
                    tmpZone.setZoneId(item.getZoneId());
                    tmpZone.setZoneName(item.getZoneName());
                    return tmpZone;
                }).collect(Collectors.toList()));
                tmpRoomCard.setBrandId(roomCard.getBrandId());
                tmpRoomCard.setGroupId(roomCard.getGroupId());
                tmpRoomCard.setGroupName(groupInfoMap.get(roomCard.getGroupId()));
                tmpRoomCard.setHotelId(roomCard.getHotelId());
                tmpRoomCard.setBasicRoomId(roomCard.getBasicRoomId());
                tmpRoomCard.setVirtualBasicRoomId(roomCard.getVirtualBasicRoomId());
                tmpRoomCard.setRoomId(roomCard.getRoomId());
                tmpRoomCard.setProductId(roomCard.getProductId());
                tmpRoomCard.setSupplierCode(roomCard.getSupplierCode());
                tmpRoomCard.setSupplierName(roomCard.getSupplierName());
                tmpRoomCard.setDirectSupplier(roomCard.getDirectSupplier());
                tmpRoomCard.setCanReserve(roomCard.getCanReserve());

                OverLimitInfo overLimitInfo = roomCard.getOverLimitInfo();
                if (overLimitInfo != null && CollectionUtils.isNotEmpty(overLimitInfo.getOverLimitRuleNameList())) {
                    QueryHotelDetailRespVo.OverLimitInfo tmpOverLimitInfo = new QueryHotelDetailRespVo.OverLimitInfo();
                    tmpOverLimitInfo.setOverLimitRuleNameList(overLimitInfo.getOverLimitRuleNameList());
                    tmpOverLimitInfo.setOverLimitModeList(overLimitInfo.getOverLimitModeList());
                    tmpRoomCard.setOverLimitInfo(tmpOverLimitInfo);
                }

                RoomBaseInfo roomBaseInfo = roomCard.getRoomBaseInfo();
                if (roomBaseInfo != null) {
                    QueryHotelDetailRespVo.RoomBaseInfo tmpRoomBaseInfo = new QueryHotelDetailRespVo.RoomBaseInfo();
                    tmpRoomBaseInfo.setFloorDesc(roomBaseInfo.getFloorDesc());
                    tmpRoomBaseInfo.setWindowDesc(Optional.ofNullable(roomBaseInfo.getWindowEnum()).map(WindowEnum::getDesc).orElse(null));
                    tmpRoomBaseInfo.setSmokeDesc(roomBaseInfo.getSmokeDesc());
                    tmpRoomBaseInfo.setMaxGuestNum(roomBaseInfo.getMaxGuestNum());
                    tmpRoomBaseInfo.setBedDesc(roomBaseInfo.getBedDesc());
                    tmpRoomBaseInfo.setWifiDesc(roomBaseInfo.getWifiDesc());
                    tmpRoomBaseInfo.setAreaDesc(roomBaseInfo.getAreaDesc());
                    tmpRoomBaseInfo.setParentBedDesc(roomBaseInfo.getParentBedDesc());
                    tmpRoomCard.setRoomBaseInfo(tmpRoomBaseInfo);
                }

                List<FacilityGroup> facilityGroupList = roomCard.getFacilityGroupList();
                if (CollectionUtils.isNotEmpty(facilityGroupList)) {
                    List<QueryHotelDetailRespVo.FacilityGroup> tmpFacilityGroupList = new ArrayList<>();
                    for (FacilityGroup facilityGroup : facilityGroupList) {
                        if (facilityGroup == null || CollectionUtils.isEmpty(facilityGroup.getFacilityList())) {
                            continue;
                        }
                        QueryHotelDetailRespVo.FacilityGroup tmpFacilityGroup = new QueryHotelDetailRespVo.FacilityGroup();
                        tmpFacilityGroup.setName(facilityGroup.getName());
                        List<QueryHotelDetailRespVo.Facility> tmpFacilityList = new ArrayList<>();
                        for (Facility facility : facilityGroup.getFacilityList()) {
                            if (facility == null) {
                                continue;
                            }
                            QueryHotelDetailRespVo.Facility tmpFacility = new QueryHotelDetailRespVo.Facility();
                            tmpFacility.setName(facility.getName());
                            tmpFacility.setCharge(facility.getCharge());
                            tmpFacilityList.add(tmpFacility);
                        }
                        if (CollectionUtils.isNotEmpty(tmpFacilityList)) {
                            tmpFacilityGroup.setFacilityList(tmpFacilityList);
                            tmpFacilityGroupList.add(tmpFacilityGroup);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(tmpFacilityGroupList)) {
                        tmpRoomCard.setFacilityGroupList(tmpFacilityGroupList);
                    }
                }

                RoomPackage roomPackage = roomCard.getRoomPackage();
                if (roomPackage != null && CollectionUtils.isNotEmpty(roomPackage.getPackageProductList())) {
                    QueryHotelDetailRespVo.RoomPackage tmpRoomPackage = new QueryHotelDetailRespVo.RoomPackage();
                    tmpRoomPackage.setPackageId(roomPackage.getPackageId());
                    List<QueryHotelDetailRespVo.PackageProduct> tmpPackageProductList = new ArrayList<>();
                    for (PackageProduct packageProduct : roomPackage.getPackageProductList()) {
                        if (packageProduct == null) {
                            continue;
                        }
                        QueryHotelDetailRespVo.PackageProduct tmpPackageProduct = new QueryHotelDetailRespVo.PackageProduct();
                        tmpPackageProduct.setIconNameList(packageProduct.getIconNameList());
                        tmpPackageProduct.setPackageName(packageProduct.getPackageName());
                        tmpPackageProduct.setMealDescList(packageProduct.getMealDescList());
                        tmpPackageProduct.setMaxGuestDesc(packageProduct.getMaxGuestDesc());
                        tmpPackageProduct.setBookRuleDesc(packageProduct.getBookRuleDesc());
                        tmpPackageProduct.setTelephoneList(packageProduct.getTelephoneList());
                        tmpPackageProduct.setReceptionTimeDescList(packageProduct.getReceptionTimeDescList());
                        tmpPackageProduct.setSpecialDesc(packageProduct.getSpecialDesc());
                        tmpPackageProductList.add(tmpPackageProduct);
                    }
                    if (CollectionUtils.isEmpty(tmpPackageProductList)) {
                        continue;
                    }
                    tmpRoomPackage.setPackageProductList(tmpPackageProductList);
                    tmpRoomCard.setRoomPackage(tmpRoomPackage);
                }

                RoomPolicyService roomPolicyService = roomCard.getRoomPolicyService();
                if (roomPolicyService != null) {
                    QueryHotelDetailRespVo.RoomPolicyService tmpRoomPolicyService = new QueryHotelDetailRespVo.RoomPolicyService();

                    ApplicativeArea applicativeArea = roomPolicyService.getApplicativeArea();
                    if (applicativeArea != null) {
                        QueryHotelDetailRespVo.ApplicativeArea tmpApplicativeArea = new QueryHotelDetailRespVo.ApplicativeArea();
                        tmpApplicativeArea.setName(applicativeArea.getName());
                        tmpApplicativeArea.setDesc(applicativeArea.getDesc());
                        tmpRoomPolicyService.setApplicativeArea(tmpApplicativeArea);
                    }

                    CancelPolicy cancelPolicy = roomPolicyService.getCancelPolicy();
                    if (cancelPolicy != null) {
                        QueryHotelDetailRespVo.CancelPolicy tmpCancelPolicy = new QueryHotelDetailRespVo.CancelPolicy();
                        tmpCancelPolicy.setCancelRuleType(cancelPolicy.getCancelRuleEnum() == null ? null : cancelPolicy.getCancelRuleEnum().getCode());
                        tmpCancelPolicy.setEndFreeCancelTime(cancelPolicy.getEndFreeCancelTime());
                        tmpCancelPolicy.setStepCancelPolicyList(buildStepCancelPolicyList(cancelPolicy.getStepCancelPolicyList()));
                        tmpRoomPolicyService.setCancelPolicy(tmpCancelPolicy);
                    }

                    tmpRoomPolicyService.setJustifyConfirm(roomPolicyService.getJustifyConfirm());
                    tmpRoomPolicyService.setHotelBonusPoint(roomPolicyService.getHotelBonusPoint());
                    tmpRoomPolicyService.setSupportSpecialInvoice(roomPolicyService.getSupportSpecialInvoice());
                    tmpRoomPolicyService.setProtocolType(roomCard.getProtocolType());
                    tmpRoomPolicyService.setBreakfastCount(roomPolicyService.getBreakfastCount());
                    tmpRoomPolicyService.setMealType(Optional.ofNullable(roomPolicyService.getMealTypeEnum())
                            .map(MealTypeEnum::getCodeNum)
                            .orElse(null));
                    tmpRoomPolicyService.setMealInfoList(buildMealInfoList(roomPolicyService.getMealInfoList()));

                    BalanceTypeEnum balanceTypeEnum = roomCard.getBalanceTypeEnum();
                    if (balanceTypeEnum != null) {
                        QueryHotelDetailRespVo.PaymentMethod tmpPaymentMethod = new QueryHotelDetailRespVo.PaymentMethod();
                        tmpPaymentMethod.setType(balanceTypeEnum.getCode());
                        tmpPaymentMethod.setName(balanceTypeEnum.getDesc());
                        tmpRoomPolicyService.setPaymentMethod(tmpPaymentMethod);
                    }
                    
                    tmpRoomPolicyService.setSpecialNotes(roomPolicyService.getSpecialNotes());

                    tmpRoomCard.setRoomPolicyService(tmpRoomPolicyService);
                }

                tmpRoomCard.setRoomPrice(assembleRoomPrice(roomCard.getRoomPrice()));
                
                // 出差申请单超标提醒
                ApplyTripOverLimitReminder applyTripOverLimitReminder = roomCard.getApplyTripOverLimitReminder();
                if (applyTripOverLimitReminder != null) {
                    tmpRoomCard.setApplyTripOverLimitReminder(QueryHotelDetailRespVo.ApplyTripOverLimitReminder.builder()
                            .applyTripItemList(buildItemList(applyTripOverLimitReminder.getApplyTripItemList()))
                            .overLimitItemList(buildItemList(applyTripOverLimitReminder.getOverLimitItemList())).build());
                }
                
                // 房间控制
                RoomControl roomControl = roomCard.getRoomControl();
                if (roomControl != null) {
                    tmpRoomCard.setRoomControl(QueryHotelDetailRespVo.RoomControl.builder()
                            .reminderApplyTripOverLimit(roomControl.getReminderApplyTripOverLimit())
                            .showExtraTaxTip(roomControl.getShowExtraTaxTip()).build());
                }

                tmpRoomCard.setPersonPrice(convertPersonPrice(roomCard.getPersonPrice()));


                // 房型所有会员标签
                tmpRoomCard.setAllRoomTagList(buildRoomTagList(roomCard.getAllRoomTagList()));
                
                tmpRoomCard.setHourlyRoomInfo(convertHourlyRoomInfo(roomCard.getHourlyRoomInfo()));

                roomCardList.add(tmpRoomCard);
            }
            if (CollectionUtils.isEmpty(roomCardList)) {
                continue;
            }
            tmpBasicRoomCard.setRoomCardList(roomCardList);
            tmpBasicRoomCardList.add(tmpBasicRoomCard);
        }
        queryHotelDetailRespVo.setBasicRoomCardList(tmpBasicRoomCardList);
    }
    
    private HourlyRoomInfoVo convertHourlyRoomInfo(HourlyRoomInfo hourlyRoomInfo) {
        if (hourlyRoomInfo == null || !Boolean.TRUE.equals(hourlyRoomInfo.getHourlyRoom())) {
            return null;
        }
        
        HourlyRoomInfoVo hourlyRoomInfo1 = new HourlyRoomInfoVo();
        hourlyRoomInfo1.setHourlyRoom(hourlyRoomInfo.getHourlyRoom());
        hourlyRoomInfo1.setDurationHour(hourlyRoomInfo.getDurationHour());
        hourlyRoomInfo1.setIntervalStartMinute(hourlyRoomInfo.getIntervalStartMinute());
        hourlyRoomInfo1.setIntervalEndMinute(hourlyRoomInfo.getIntervalEndMinute());
        hourlyRoomInfo1.setHourlyRoomDesc(hourlyRoomInfo.getHourlyRoomDesc());
        hourlyRoomInfo1.setAvailableTimeSlots(hourlyRoomInfo.getAvailableTimeSlots());
        hourlyRoomInfo1.setAvailablePeriodsDesc(hourlyRoomInfo.getAvailablePeriodsDesc());
        hourlyRoomInfo1.setHourlyRoomTip(hourlyRoomInfo.getHourlyRoomTip());
        return hourlyRoomInfo1;
    }
    
    private QueryHotelDetailRespVo.RoomPrice assembleRoomPrice(RoomPrice roomPrice) {
        if (roomPrice == null) {
            return null;
        }
        
        QueryHotelDetailRespVo.RoomPrice tmpRoomPrice = new QueryHotelDetailRespVo.RoomPrice();
        tmpRoomPrice.setRoomQuantity(roomPrice.getRoomQuantity());
        tmpRoomPrice.setDayQuantity(roomPrice.getDayQuantity());
        
        List<DailyRate> dailyRateList = roomPrice.getDailyRateList();
        if (CollectionUtils.isNotEmpty(dailyRateList)) {
            List<QueryHotelDetailRespVo.DailyRate> tmpDailyRateList = new ArrayList<>();
            for (DailyRate dailyRate : dailyRateList) {
                if (dailyRate == null || !Price.checkCustomPrice(dailyRate.getAvgPriceIncludeTax())) {
                    continue;
                }
                QueryHotelDetailRespVo.DailyRate tmpDailyRate = new QueryHotelDetailRespVo.DailyRate();
                tmpDailyRate.setDate(dailyRate.getDate());
                tmpDailyRate.setAvgPriceIncludeTax(dailyRate.getAvgPriceIncludeTax().getCustomPrice());
                tmpDailyRateList.add(tmpDailyRate);
            }
            if (CollectionUtils.isNotEmpty(tmpDailyRateList)) {
                tmpRoomPrice.setDailyRateList(tmpDailyRateList);
            }
        }
        
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalRoomTax()))) {
            tmpRoomPrice.setForeignTotalRoomTax(roomPrice.getTotalRoomTax().getOriginPrice());
            tmpRoomPrice.setForeignCurrency(roomPrice.getTotalRoomTax().getOriginCurrency());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalExtraTax()))) {
            tmpRoomPrice.setForeignTotalExtraTax(roomPrice.getTotalExtraTax().getOriginPrice());
            tmpRoomPrice.setForeignCurrency(roomPrice.getTotalExtraTax().getOriginCurrency());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalPriceIncludeTax()))) {
            tmpRoomPrice.setTotalPriceIncludeTax(roomPrice.getTotalPriceIncludeTax().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalRoomTax()))) {
            tmpRoomPrice.setTotalRoomTax(roomPrice.getTotalRoomTax().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalExtraTax()))) {
            tmpRoomPrice.setTotalExtraTax(roomPrice.getTotalExtraTax().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getAvgPriceIncludeTax()))) {
            tmpRoomPrice.setAvgPriceIncludeTax(roomPrice.getAvgPriceIncludeTax().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getAvgExtraTax()))) {
            tmpRoomPrice.setAvgExtraTax(roomPrice.getAvgExtraTax().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalOverLimitPrice()))) {
            tmpRoomPrice.setTotalOverLimitPrice(roomPrice.getTotalOverLimitPrice().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalCorpPayPrice()))) {
            tmpRoomPrice.setTotalCorpPayPrice(roomPrice.getTotalCorpPayPrice().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getAvgPriceExcludeTax()))) {
            tmpRoomPrice.setAvgPriceExcludeTax(roomPrice.getAvgPriceExcludeTax().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalPriceExcludeTax()))) {
            tmpRoomPrice.setTotalPriceExcludeTax(roomPrice.getTotalPriceExcludeTax().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getTotalServiceCharge()))) {
            tmpRoomPrice.setTotalServiceCharge(roomPrice.getTotalServiceCharge().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getAvgServiceCharge()))) {
            tmpRoomPrice.setAvgServiceCharge(roomPrice.getAvgServiceCharge().getCustomPrice());
        }
        ServiceChargeStrategyEnum serviceChargeStrategyEnum = roomPrice.getServiceChargeStrategyEnum();
        if (serviceChargeStrategyEnum != null) {
            tmpRoomPrice.setServiceChargeStrategy(serviceChargeStrategyEnum.getCode());
        }
        tmpRoomPrice.setServiceChargeStrategyValue(roomPrice.getServiceChargeStrategyValue());
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getAvgPriceIncludeTaxAndServiceCharge()))) {
            tmpRoomPrice.setAvgPriceIncludeTaxAndServiceCharge(roomPrice.getAvgPriceIncludeTaxAndServiceCharge().getCustomPrice());
        }
        if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(roomPrice.getAvgPriceIncludeTaxAndServiceCharge()))) {
            tmpRoomPrice.setAvgPriceIncludeTaxAndServiceCharge(roomPrice.getAvgPriceIncludeTaxAndServiceCharge().getCustomPrice());
        }
        tmpRoomPrice.setResourcePriceIncludeServiceCharge(roomPrice.getResourcePriceIncludeServiceCharge());
        
        
        List<Price> totalExtraTaxDetailList = roomPrice.getTotalExtraTaxDetailList();
        if (CollectionUtils.isNotEmpty(totalExtraTaxDetailList)) {
            tmpRoomPrice.setTotalExtraTaxDetailList(totalExtraTaxDetailList.stream().filter(Objects::nonNull).map(item -> {
                QueryHotelDetailRespVo.Price price = new QueryHotelDetailRespVo.Price();
                price.setName(item.getName());
                price.setPrice(item.getCustomPrice());
                price.setForeignPrice(item.getOriginPrice());
                return price;
            }).collect(Collectors.toList()));
        }
        List<Price> totalRoomTaxDetailList = roomPrice.getTotalRoomTaxDetailList();
        if (CollectionUtils.isNotEmpty(totalRoomTaxDetailList)) {
            tmpRoomPrice.setTotalRoomTaxDetailList(totalRoomTaxDetailList.stream().filter(Objects::nonNull).map(item -> {
                QueryHotelDetailRespVo.Price price = new QueryHotelDetailRespVo.Price();
                price.setName(item.getName());
                price.setPrice(item.getCustomPrice());
                price.setForeignPrice(item.getOriginPrice());
                return price;
            }).collect(Collectors.toList()));
        }
        
        return tmpRoomPrice;
    }
    
    
    
    private List<QueryHotelDetailRespVo.MealInfo> buildMealInfoList(List<MealInfo> mealInfoList) {
        if (CollectionUtils.isEmpty(mealInfoList)) {
            return null;
        }
        
        List<QueryHotelDetailRespVo.MealInfo> resultList = new ArrayList<>();
        for (MealInfo mealInfo : mealInfoList) {
            if (mealInfo == null) {
                continue;
            }
            
            QueryHotelDetailRespVo.MealInfo tmp = new QueryHotelDetailRespVo.MealInfo();
            tmp.setMealCount(mealInfo.getMealCount());
            tmp.setEffectDate(mealInfo.getEffectDate());
            resultList.add(tmp);
        }
        return resultList;
    }
    
    private QueryHotelDetailRespVo.PersonPrice convertPersonPrice(PersonPrice personPrice) {
        if (personPrice == null) {
            return null;
        }

        QueryHotelDetailRespVo.PersonPrice res = new QueryHotelDetailRespVo.PersonPrice();
        res.setAdult(personPrice.getAdult());
        res.setRateId(personPrice.getRateId());
        return res;

    }

    private List<MemberTag> buildRoomTagList(List<MemberTagInfo> tagInfoList) {
        if (CollectionUtils.isEmpty(tagInfoList)) {
            return Collections.emptyList();
        }
        return tagInfoList.stream().map(tagInfo -> {
            MemberTag tag = new MemberTag();
            tag.setTagName(tagInfo.getTagName());
            tag.setTagDesc(tagInfo.getTagDesc());
            tag.setTagCode(tagInfo.getTagCode());
            return tag;
        }).collect(Collectors.toList());

    }

    private List<QueryHotelDetailRespVo.Item> buildItemList(List<Item> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        
        List<QueryHotelDetailRespVo.Item> resultList = new ArrayList<>();
        for (Item item : itemList) {
            if (item == null) {
                continue;
            }
            
            resultList.add(QueryHotelDetailRespVo.Item.builder()
                    .key(item.getKey())
                    .label(item.getLabel())
                    .value(item.getValue()).build());
        }
        
        return resultList;
    }

    /**
     * 构建阶梯取消政策列表
     * @param stepCancelPolicyList 阶梯取消政策列表
     * @return 阶梯取消政策列表
     */
    private List<QueryHotelDetailRespVo.StepCancelPolicy> buildStepCancelPolicyList(List<StepCancelPolicy> stepCancelPolicyList) {
        if (CollectionUtils.isEmpty(stepCancelPolicyList)) {
            return null;
        }

        List<QueryHotelDetailRespVo.StepCancelPolicy> resultList = new ArrayList<>();
        for (StepCancelPolicy stepCancelPolicy : stepCancelPolicyList) {
            if (stepCancelPolicy == null) {
                continue;
            }
            QueryHotelDetailRespVo.StepCancelPolicy tmpStepCancelPolicy = new QueryHotelDetailRespVo.StepCancelPolicy();
            tmpStepCancelPolicy.setCancelRuleType(stepCancelPolicy.getCancelRuleEnum() == null ? null : stepCancelPolicy.getCancelRuleEnum().getCode());
            tmpStepCancelPolicy.setStartTime(stepCancelPolicy.getStartTime());
            tmpStepCancelPolicy.setEndTime(stepCancelPolicy.getEndTime());
            tmpStepCancelPolicy.setPrice(stepCancelPolicy.getPrice());
            resultList.add(tmpStepCancelPolicy);
        }

        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }

        return resultList;
    }

    /**
     * 构建酒店信息
     * @param queryHotelDetailRespVo 返回
     * @param hotelDetail 酒店详情
     */
    private void assembleHotelResponse(QueryHotelDetailRespVo queryHotelDetailRespVo, HotelDetail hotelDetail) {
        log.info("构建酒店信息,queryHotelDetailRespVo:{},hotelDetail:{}", JsonUtils.toJsonString(queryHotelDetailRespVo), JsonUtils.toJsonString(hotelDetail));
        if (queryHotelDetailRespVo == null || hotelDetail == null) {
            return;
        }

        HotelBaseInfo hotelBaseInfo = hotelDetail.getHotelBaseInfo();
        if (hotelBaseInfo != null) {
            QueryHotelDetailRespVo.HotelBaseInfo tmpHotelBaseInfo = new QueryHotelDetailRespVo.HotelBaseInfo();
            tmpHotelBaseInfo.setName(hotelBaseInfo.getName());
            tmpHotelBaseInfo.setNameEn(hotelBaseInfo.getNameEn());
            tmpHotelBaseInfo.setAddress(hotelBaseInfo.getAddress());
            tmpHotelBaseInfo.setLon(hotelBaseInfo.getLon());
            tmpHotelBaseInfo.setLat(hotelBaseInfo.getLat());
            tmpHotelBaseInfo.setLogoUrl(hotelBaseInfo.getLogoUrl());
            tmpHotelBaseInfo.setStar(hotelBaseInfo.getStar());
            tmpHotelBaseInfo.setStarLicence(hotelBaseInfo.getStarLicence());
            tmpHotelBaseInfo.setLevelName(hotelBaseInfo.getLevelName());
            tmpHotelBaseInfo.setReviewScore(hotelBaseInfo.getReviewScore());
            tmpHotelBaseInfo.setGroupName(hotelBaseInfo.getGroupName());
            tmpHotelBaseInfo.setTelephone(hotelBaseInfo.getTelephone());
            tmpHotelBaseInfo.setDistrictName(hotelBaseInfo.getDistrictName());
            tmpHotelBaseInfo.setCityName(hotelBaseInfo.getCityName());
            tmpHotelBaseInfo.setOpenDateDesc(hotelBaseInfo.getOpenDateDesc());
            tmpHotelBaseInfo.setRenovationDateDesc(hotelBaseInfo.getRenovationDateDesc());
            tmpHotelBaseInfo.setFacilityList(hotelBaseInfo.getFacilityList());
            tmpHotelBaseInfo.setSupplierStarInfo(buildSupplierStar(hotelBaseInfo.getSupplierStarInfo()));
            List<MapInfo> mapInfoList = hotelBaseInfo.getMapInfoList();
            if (CollectionUtils.isNotEmpty(mapInfoList)) {
                List<QueryHotelDetailRespVo.MapInfo> tmpMapInfoList = new ArrayList<>();
                for (MapInfo mapInfo : mapInfoList) {
                    if (mapInfo == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.MapInfo tmpMapInfo = new QueryHotelDetailRespVo.MapInfo();
                    tmpMapInfo.setLat(mapInfo.getLat());
                    tmpMapInfo.setLon(mapInfo.getLon());
                    tmpMapInfo.setMapType(Optional.ofNullable(mapInfo.getMapType())
                            .map(MapTypeEnum::getCode)
                            .orElse(null));
                    tmpMapInfoList.add(tmpMapInfo);
                }
                if (CollectionUtils.isNotEmpty(tmpMapInfoList)) {
                    tmpHotelBaseInfo.setMapInfoList(tmpMapInfoList);
                }
            }
            
            List<Video> videoList = hotelBaseInfo.getVideoList();
            if (CollectionUtils.isNotEmpty(videoList)) {
                List<QueryHotelDetailRespVo.Video> tmpVideoList = new ArrayList<>();
                for (Video video : videoList) {
                    if (video == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.Video tmpVideo = new QueryHotelDetailRespVo.Video();
                    tmpVideo.setUrl(video.getUrl());
                    tmpVideo.setCoverPictureUrl(video.getCoverPictureUrl());
                    tmpVideoList.add(tmpVideo);
                }
                if (CollectionUtils.isNotEmpty(tmpVideoList)) {
                    tmpHotelBaseInfo.setVideoList(tmpVideoList);
                }
            }
            List<Picture> pictureList = hotelBaseInfo.getPictureList();
            if (CollectionUtils.isNotEmpty(pictureList)) {
                List<QueryHotelDetailRespVo.Picture> tmpPictureList = new ArrayList<>();
                for (Picture picture : pictureList) {
                    if (picture == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.Picture tmpPicture = new QueryHotelDetailRespVo.Picture();
                    tmpPicture.setType(picture.getType());
                    tmpPicture.setUrl(picture.getUrl());
                    tmpPictureList.add(tmpPicture);
                }
                if (CollectionUtils.isNotEmpty(tmpPictureList)) {
                    tmpHotelBaseInfo.setPictureList(tmpPictureList);
                }
            }
            queryHotelDetailRespVo.setHotelBaseInfo(tmpHotelBaseInfo);
        }

        List<RoomSupplier> supplierList = hotelDetail.getSupplierList();
        if (CollectionUtils.isNotEmpty(supplierList)) {
            List<QueryHotelDetailRespVo.Supplier> tmpSupplierList = new ArrayList<>();
            for (RoomSupplier roomSupplier : supplierList) {
                if (roomSupplier == null) {
                    continue;
                }
                QueryHotelDetailRespVo.Supplier tmpSupplier = new QueryHotelDetailRespVo.Supplier();
                tmpSupplier.setSupplierCode(roomSupplier.getSupplierCode());
                tmpSupplier.setSupplierName(roomSupplier.getSupplierName());
                if (roomSupplier.getMinRoomPrice() != null && roomSupplier.getMinRoomPrice().getAvgPriceIncludeTax() != null && roomSupplier.getMinRoomPrice().getAvgPriceIncludeTax().getCustomPrice() != null) {
                    tmpSupplier.setMinAvgPriceIncludeTax(roomSupplier.getMinRoomPrice().getAvgPriceIncludeTax().getCustomPrice());
                }
                tmpSupplier.setMinRoomPrice(assembleRoomPrice(roomSupplier.getMinRoomPrice()));
                tmpSupplierList.add(tmpSupplier);
            }
            queryHotelDetailRespVo.setSupplierList(tmpSupplierList);
        }

        List<ReservationNotice> reservationNoticeList = hotelDetail.getReservationNoticeList();
        if (CollectionUtils.isNotEmpty(reservationNoticeList)) {
            List<QueryHotelDetailRespVo.ReservationNotice> tmpReservationNoticeList = new ArrayList<>();
            for (ReservationNotice reservationNotice : reservationNoticeList) {
                if (reservationNotice == null) {
                    continue;
                }
                QueryHotelDetailRespVo.ReservationNotice tmpReservationNotice = new QueryHotelDetailRespVo.ReservationNotice();
                tmpReservationNotice.setTitle(reservationNotice.getTitle());
                tmpReservationNotice.setDesc(reservationNotice.getDesc());
                tmpReservationNoticeList.add(tmpReservationNotice);
            }
            if (CollectionUtils.isNotEmpty(tmpReservationNoticeList)) {
                queryHotelDetailRespVo.setReservationNoticeList(tmpReservationNoticeList);
            }
        }

        HotelFacility hotelFacility = hotelDetail.getHotelFacility();
        if (hotelFacility != null) {
            QueryHotelDetailRespVo.HotelFacility tmpHotelFacility = new QueryHotelDetailRespVo.HotelFacility();
            List<ParkingLot> parkingLotList = hotelFacility.getParkingLotList();
            if (CollectionUtils.isNotEmpty(parkingLotList)) {
                List<QueryHotelDetailRespVo.ParkingLot> tmpParkingLotList = new ArrayList<>();
                for (ParkingLot parkingLot : parkingLotList) {
                    if (parkingLot == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.ParkingLot tmpParkingLot = new QueryHotelDetailRespVo.ParkingLot();
                    tmpParkingLot.setReservedDesc(parkingLot.getReservedDesc());
                    tmpParkingLot.setLocationDesc(parkingLot.getLocationDesc());
                    tmpParkingLot.setTypeDesc(parkingLot.getTypeDesc());
                    tmpParkingLot.setChargeableDesc(parkingLot.getChargeableDesc());
                    tmpParkingLotList.add(tmpParkingLot);
                }
                if (CollectionUtils.isNotEmpty(tmpParkingLotList)) {
                    tmpHotelFacility.setParkingLotList(tmpParkingLotList);
                }
            }

            List<ChargingPile> chargingPileList = hotelFacility.getChargingPileList();
            if (CollectionUtils.isNotEmpty(chargingPileList)) {
                List<QueryHotelDetailRespVo.ChargingPile> tmpChargingPileList = new ArrayList<>();
                for (ChargingPile chargingPile : chargingPileList) {
                    if (chargingPile == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.ChargingPile tmpChargingPile = new QueryHotelDetailRespVo.ChargingPile();
                    tmpChargingPile.setTypeDesc(chargingPile.getTypeDesc());
                    tmpChargingPile.setLocationDesc(chargingPile.getLocationDesc());
                    tmpChargingPileList.add(tmpChargingPile);
                }
                if (CollectionUtils.isNotEmpty(tmpChargingPileList)) {
                    tmpHotelFacility.setChargingPileList(tmpChargingPileList);
                }
            }

            List<FacilityGroup> facilityGroupList = hotelFacility.getFacilityGroupList();
            if (CollectionUtils.isNotEmpty(facilityGroupList)) {
                List<QueryHotelDetailRespVo.FacilityGroup> tmpFacilityGroupList = new ArrayList<>();
                for (FacilityGroup facilityGroup : facilityGroupList) {
                    if (facilityGroup == null || CollectionUtils.isEmpty(facilityGroup.getFacilityList())) {
                        continue;
                    }
                    QueryHotelDetailRespVo.FacilityGroup tmpFacilityGroup = new QueryHotelDetailRespVo.FacilityGroup();
                    tmpFacilityGroup.setName(facilityGroup.getName());
                    List<QueryHotelDetailRespVo.Facility> facilityList = new ArrayList<>();
                    for (Facility facility : facilityGroup.getFacilityList()) {
                        if (facility == null) {
                            continue;
                        }
                        QueryHotelDetailRespVo.Facility tmpFacility = new QueryHotelDetailRespVo.Facility();
                        tmpFacility.setName(facility.getName());
                        tmpFacility.setCharge(facility.getCharge());
                        facilityList.add(tmpFacility);
                    }
                    tmpFacilityGroup.setFacilityList(facilityList);
                    tmpFacilityGroupList.add(tmpFacilityGroup);
                }
                if (CollectionUtils.isNotEmpty(tmpFacilityGroupList)) {
                    tmpHotelFacility.setFacilityGroupList(tmpFacilityGroupList);
                }
            }
            queryHotelDetailRespVo.setHotelFacility(tmpHotelFacility);
        }

        HotelPolicyService hotelPolicyService = hotelDetail.getHotelPolicyService();
        if (hotelPolicyService != null) {
            QueryHotelDetailRespVo.HotelPolicyService tmpHotelPolicyService = new QueryHotelDetailRespVo.HotelPolicyService();
            ArrivalAndDeparturePolicy arrivalAndDeparturePolicy = hotelPolicyService.getArrivalAndDeparturePolicy();
            if (arrivalAndDeparturePolicy != null) {
                QueryHotelDetailRespVo.ArrivalAndDeparturePolicy tmpArrivalAndDeparturePolicy = new QueryHotelDetailRespVo.ArrivalAndDeparturePolicy();
                tmpArrivalAndDeparturePolicy.setArrivalDesc(arrivalAndDeparturePolicy.getArrivalDesc());
                tmpArrivalAndDeparturePolicy.setDepartureDesc(arrivalAndDeparturePolicy.getDepartureDesc());
                tmpHotelPolicyService.setArrivalAndDeparturePolicy(tmpArrivalAndDeparturePolicy);
            }

            ChildAndAddBedPolicy childAndAddBedPolicy = hotelPolicyService.getChildAndAddBedPolicy();
            if (childAndAddBedPolicy != null) {
                QueryHotelDetailRespVo.ChildAndAddBedPolicy tmpChildAndAddBedPolicy = new QueryHotelDetailRespVo.ChildAndAddBedPolicy();
                tmpChildAndAddBedPolicy.setDescList(childAndAddBedPolicy.getDescList());
                tmpChildAndAddBedPolicy.setChargeDescList(childAndAddBedPolicy.getChargeDescList());
                tmpChildAndAddBedPolicy.setSpecialRemarkDesc(childAndAddBedPolicy.getSpecialRemarkDesc());
                tmpChildAndAddBedPolicy.setHotelRemarkDesc(childAndAddBedPolicy.getHotelRemarkDesc());
                tmpHotelPolicyService.setChildAndAddBedPolicy(tmpChildAndAddBedPolicy);
            }

            MealPolicy mealPolicy = hotelPolicyService.getMealPolicy();
            if (mealPolicy != null) {
                QueryHotelDetailRespVo.MealPolicy tmpMealPolicy = new QueryHotelDetailRespVo.MealPolicy();
                tmpMealPolicy.setDesc(mealPolicy.getDesc());
                tmpMealPolicy.setType(mealPolicy.getType());
                tmpMealPolicy.setStyle(mealPolicy.getStyle());
                tmpMealPolicy.setPrice(mealPolicy.getPrice());
                tmpMealPolicy.setOpenTimeDesc(mealPolicy.getOpenTimeDesc());
                tmpHotelPolicyService.setMealPolicy(tmpMealPolicy);
            }

            tmpHotelPolicyService.setPetPolicyDesc(hotelPolicyService.getPetPolicyDesc());

            List<PaymentTool> paymentToolList = hotelPolicyService.getPaymentToolList();
            if (CollectionUtils.isNotEmpty(paymentToolList)) {
                List<QueryHotelDetailRespVo.PaymentTool> tmpPaymentToolList = new ArrayList<>();
                for (PaymentTool paymentTool : paymentToolList) {
                    if (paymentTool == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.PaymentTool tmpPaymentTool = new QueryHotelDetailRespVo.PaymentTool();
                    tmpPaymentTool.setName(paymentTool.getName());
                    tmpPaymentTool.setIconUrl(paymentTool.getIconUrl());
                    tmpPaymentToolList.add(tmpPaymentTool);
                }
                tmpHotelPolicyService.setPaymentToolList(tmpPaymentToolList);
            }
            queryHotelDetailRespVo.setHotelPolicyService(tmpHotelPolicyService);
        }

        List<NearByGroup> nearByGroupList = hotelDetail.getNearByGroupList();
        if (CollectionUtils.isNotEmpty(nearByGroupList)) {
            List<QueryHotelDetailRespVo.NearByGroup> tmpNearByGroupList = new ArrayList<>();
            for (NearByGroup nearByGroup : nearByGroupList) {
                if (nearByGroup == null || CollectionUtils.isEmpty(nearByGroup.getNearByList())) {
                    continue;
                }
                QueryHotelDetailRespVo.NearByGroup tmpNearByGroup = new QueryHotelDetailRespVo.NearByGroup();
                tmpNearByGroup.setName(nearByGroup.getName());
                NearByGroupTypeEnum nearByGroupTypeEnum = nearByGroup.getNearByGroupTypeEnum();
                tmpNearByGroup.setType(nearByGroupTypeEnum == null ? null : nearByGroupTypeEnum.getCode());
                List<QueryHotelDetailRespVo.NearBy> tmpNearByList = new ArrayList<>();
                for (NearBy nearBy : nearByGroup.getNearByList()) {
                    if (nearBy == null) {
                        continue;
                    }
                    QueryHotelDetailRespVo.NearBy tmpNearBy = new QueryHotelDetailRespVo.NearBy();
                    tmpNearBy.setName(nearBy.getName());
                    tmpNearBy.setDistanceDesc(nearBy.getDistanceDesc());
                    tmpNearByList.add(tmpNearBy);
                }
                if (CollectionUtils.isNotEmpty(tmpNearByList)) {
                    tmpNearByGroup.setNearByList(tmpNearByList);
                    tmpNearByGroupList.add(tmpNearByGroup);
                }
            }
            if (CollectionUtils.isNotEmpty(nearByGroupList)) {
                queryHotelDetailRespVo.setNearByGroupList(tmpNearByGroupList);
            }
        }
        log.info("queryHotelDetailRespVo:{}", queryHotelDetailRespVo);
    }

    /**
     * 转换供应商返回的星级数据
     *
     * @param supplierStarInfo 供应商星级信息
     * @return 星级信息
     */
    private Map<String, QueryHotelDetailRespVo.SupplierStarDTO>
        buildSupplierStar(Map<String, SupplierStarInfo> supplierStarInfo) {
        if (MapUtils.isEmpty(supplierStarInfo)) {
            return new HashMap<>(0);
        }
        Map<String, QueryHotelDetailRespVo.SupplierStarDTO> supplierStarMap =
            Maps.newHashMapWithExpectedSize(supplierStarInfo.size());
        supplierStarInfo.forEach((supplierCode, starInfo) -> {
            QueryHotelDetailRespVo.SupplierStarDTO supplierStarDto = new QueryHotelDetailRespVo.SupplierStarDTO();
            supplierStarDto.setStar(starInfo.getStar());
            supplierStarDto.setStarLicence(starInfo.getStarLicence());
            supplierStarMap.put(supplierCode, supplierStarDto);
        });
        return supplierStarMap;
    }
    /**
     * 获取协议标签
     */
    public AgreementInfoQueryResp getContent() {
        try {
            JSONResult<AgreementInfoQueryResp> agreementInfo = contentDicitionaryClient.getAgreementInfo();
            if(agreementInfo==null||agreementInfo.getData()==null){
                return null;
            }
            return agreementInfo.getData();
        } catch (Exception exception) {
            log.error("获取协议标签", exception);
            return null;
        }
    }
}
