package com.corpgovernment.core.service;

import com.corpgovernment.api.applytrip.soa.response.ApplyTripTrafficVerifyResponse;
import com.corpgovernment.api.basic.dto.BasicCityInfoDto;
import com.corpgovernment.api.basic.dto.BasicTimeZoneDto;
import com.corpgovernment.api.basic.enums.GeographyTypeEnum;
import com.corpgovernment.api.basic.request.BasicCityListRequest;
import com.corpgovernment.api.basic.request.BasicGeographyInfoRequest;
import com.corpgovernment.api.basic.request.FuzzyHotelCityFromDistributionRequest;
import com.corpgovernment.api.basic.response.BasicGeographyInfoResponse;
import com.corpgovernment.api.basic.response.BasicIntegratedCityResponse;
import com.corpgovernment.api.basic.response.FuzzyHotelCityFromDistributionResponse;
import com.corpgovernment.api.basic.response.FuzzyHotelCityFromDistributionResponse.DestinationInfoListDTO;
import com.corpgovernment.api.basic.soa.BasicDataClient;
import com.corpgovernment.basic.handle.HotelBasicDataHandler;
import com.corpgovernment.basic.impl.HotelBasicDataService;
import com.corpgovernment.common.apollo.CommonApollo;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.oversea.OverseaCityUtils;
import com.corpgovernment.common.utils.HttpUtils;
import com.corpgovernment.core.controller.vo.HandleMidnightReqVo;
import com.corpgovernment.core.controller.vo.HandleMidnightRespVo;
import com.corpgovernment.core.controller.vo.KeywordVo;
import com.corpgovernment.core.controller.vo.SearchKeywordReqVo;
import com.corpgovernment.core.controller.vo.SearchKeywordRespVo;
import com.corpgovernment.core.dao.apollo.impl.HotelCoreApolloDao;
import com.corpgovernment.core.dao.dto.SearchKeywordReqDto;
import com.corpgovernment.core.dao.dto.SearchKeywordRespDto;
import com.corpgovernment.core.dao.dto.TimeZoneReqDto;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.corpgovernment.basic.constant.DestinationTypeEnum.HOTEL;
import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.CITY_ID_IS_NULL;
import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.CTRIP_SEARCH_KEYWORD_FAIL;
import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.CTRIP_SEARCH_KEYWORD_URL_IS_NULL;
import static com.corpgovernment.basic.constant.HotelResponseCodeEnum.OPTIONAL_CITY_ERROR;
import static com.corpgovernment.basic.handle.HotelBasicDataHandler.DOMESTIC_TYPE;
import static com.corpgovernment.basic.utils.ApplyTripTrafficUtil.checkApplyTripCityLimited;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
@Service
@Slf4j
public class HotelCoreService {
    @Autowired
    private TimeZoneUtils timeZoneUtils;
    @Autowired
    private OverseaCityUtils overseaCityUtils;
    @Autowired
    private HotelCoreApolloDao hotelCoreApolloDao;
    @Autowired
    private HotelBasicDataHandler hotelBasicDataHandler;
    @Autowired
    private BasicDataClient basicDataClient;
    @Autowired
    private HotelBasicDataService hotelBasicDataService;
    @Autowired
    private IHotelDataService iHotelDataService;
    @Autowired
    private CommonApollo commonApollo;

    private List<String> hongKongMacauTaiwanProvinceIdList  = Arrays.asList("32", "33", "53");

    public JSONResult<HandleMidnightRespVo> handleMidnight(HandleMidnightReqVo requestParam) {
        // 查询城市时区信息
        BasicGeographyInfoRequest timeZoneRequest = new BasicGeographyInfoRequest();
        timeZoneRequest
            .setGeographyTypes(Lists.newArrayList(GeographyTypeEnum.SEARCH_CITY_TIMEZONE.getType()));
        timeZoneRequest.setCityIds(Lists.newArrayList(requestParam.getCityId()));
        JSONResult<BasicGeographyInfoResponse> response =
            basicDataClient.listGeographyInfoByTypeAndCityIds(timeZoneRequest);
        String timeZone = Optional.ofNullable(response.getData())
            .flatMap(v -> v.getTimeZoneList().stream().filter(item -> item.getCityId().equals(requestParam.getCityId()))
                .map(BasicTimeZoneDto::getTimeZone).map(String::valueOf).findFirst())
            .orElse(null);
        // 程曦基础数据库已录入时区信息，则直接返回，否则走旧流程兜底
        if (StringUtils.isNotBlank(timeZone)) {
            // 构建响应，系统时间转当地时间
            return new JSONResult<>(new HandleMidnightRespVo(timeZone));
        }
        log.info("listGeographyInfoByTypeAndCityIds时区获取失败 request={} response={}",
            JsonUtils.toJsonString(timeZoneRequest), JsonUtils.toJsonString(response));
        // 获取城市数据
        BasicCityListRequest basicCityRequest = new BasicCityListRequest();
        basicCityRequest.setCityIdList(Collections.singletonList(requestParam.getCityId()));
        JSONResult<BasicIntegratedCityResponse> cityResponse = basicDataClient.listBasicCityInfoByIds(basicCityRequest);
        BasicCityInfoDto basicCityInfoDto = Optional.ofNullable(cityResponse).map(JSONResult::getData)
            .map(BasicIntegratedCityResponse::getCityInfoList).orElse(new ArrayList<>()).stream().findFirst()
            .orElseThrow(() -> new CorpBusinessException(OPTIONAL_CITY_ERROR));
        timeZone = timeZoneUtils.getTimeZone(new TimeZoneReqDto(basicCityInfoDto));
        if (StringUtils.isBlank(timeZone)) {
            log.info("时区获取失败 requestParam={} basicCityInfoDto={}", JsonUtils.toJsonString(requestParam),
                JsonUtils.toJsonString(basicCityInfoDto));
            timeZone = "480";
        }
        // 构建响应，系统时间转当地时间
        return new JSONResult<>(new HandleMidnightRespVo(timeZone));
    }

    public JSONResult<SearchKeywordRespVo> searchKeyword(@RequestBody SearchKeywordReqVo requestParam) {
        //出差申请单城市管控判断
        boolean applyCityControl = false;
        if (Objects.nonNull(requestParam.getTrafficId())){
            BasicCityListRequest request = new BasicCityListRequest();
            request.setCityIdList(Collections.singletonList(requestParam.getCityId()));
            JSONResult<BasicIntegratedCityResponse> cityResponse = basicDataClient.listBasicCityInfoByIds(request);
            BasicCityInfoDto cityInfoDto = Optional.ofNullable(cityResponse).map(JSONResult::getData).map(BasicIntegratedCityResponse::getCityInfoList).orElse(new ArrayList<>()).stream().findFirst().orElse(new BasicCityInfoDto());
            boolean domesticTag = Objects.equals(cityInfoDto.getAreaType(), DOMESTIC_TYPE);
            //有出差申请单走出差申请单
            ApplyTripTrafficVerifyResponse trafficVerifyResponse = hotelBasicDataService.getApplyTripTrafficVerify(requestParam.getTrafficId());
            applyCityControl = checkApplyTripCityLimited(trafficVerifyResponse, domesticTag);
        }
        // 城市Id必传
        if (requestParam.getCityId() == null) {
            throw new CorpBusinessException(CITY_ID_IS_NULL);
        }
        // 关键词搜索URL 目前先只用商旅的，因为没法做到id转换
        String ctripSearchKeywordUrl = hotelCoreApolloDao.getCtripSearchKeywordUrl();
        if (ctripSearchKeywordUrl == null) {
            throw new CorpBusinessException(CTRIP_SEARCH_KEYWORD_URL_IS_NULL);
        }
        // 关键词搜索
        SearchKeywordReqDto searchKeywordReqDto = new SearchKeywordReqDto(requestParam.getKeyword(), requestParam.getCityId());
        SearchKeywordRespDto searchKeywordRespDto = null;
        try {
            searchKeywordRespDto = JsonUtils.parse(HttpUtils.doPostJSON("ctrip", "关键词搜索查询", ctripSearchKeywordUrl, JsonUtils.toJsonString(searchKeywordReqDto)), SearchKeywordRespDto.class);
        } catch (Exception e) {
            log.error("关键词搜索查询异常", e);
        }
        if (searchKeywordRespDto == null || searchKeywordRespDto.getStatus() == null || !Boolean.TRUE.equals(searchKeywordRespDto.getStatus().getSuccess())) {
            throw new CorpBusinessException(CTRIP_SEARCH_KEYWORD_FAIL);
        }
        // 组装数据
        List<KeywordVo> keywordList = new ArrayList<>(16);
        List<KeywordVo> otherCityKeywordList = new ArrayList<>(16);
        List<SearchKeywordRespDto.DestinationInfo> destinationInfoList = searchKeywordRespDto.getDestinationInfoList();
        if (CollectionUtils.isNotEmpty(destinationInfoList)) {
            keywordList.addAll(destinationInfoList.stream().map(KeywordVo::new).collect(Collectors.toList()));
        }
        keywordList = keywordList.subList(0, Math.min(keywordList.size(), 15));
        // 非申请单模式必须组装到15条
        if (!applyCityControl) {
            int size = Math.max(0, 15 - keywordList.size());
            List<SearchKeywordRespDto.OtherCityDestinationInfo> otherCityDestinationList = searchKeywordRespDto.getOtherCityDestinationList();
            //基于传入cityId,进行海内外酒店过滤
            otherCityDestinationList = hotelBasicDataHandler.filterOverseaOrDomesticHotelByCityId(otherCityDestinationList, requestParam.getCityId());
            for (int i = 0; i < size && i < otherCityDestinationList.size(); i++) {
                SearchKeywordRespDto.OtherCityDestinationInfo otherCityDestinationInfo = otherCityDestinationList.get(i);
                KeywordVo keywordVo = new KeywordVo(otherCityDestinationList.get(i));
                if (otherCityDestinationInfo.getCountryId() != null && otherCityDestinationInfo.getProvinceId() != null) {
                    keywordVo.setCityType(Boolean.TRUE.equals(overseaCityUtils.isOverseaOrGATCity(otherCityDestinationInfo.getCountryId(), otherCityDestinationInfo.getProvinceId())) ? "foreign" : "domestic");
                }
                otherCityKeywordList.add(keywordVo);
            }
        }
        // 填充国家数据
        BasicCityListRequest basicCityRequest = new BasicCityListRequest();
        basicCityRequest.setCityIdList(Collections.singletonList(requestParam.getCityId()));
        JSONResult<BasicIntegratedCityResponse> cityResponse = basicDataClient.listBasicCityInfoByIds(basicCityRequest);
        BasicCityInfoDto basicCityInfoDto = Optional.ofNullable(cityResponse).map(JSONResult::getData).map(BasicIntegratedCityResponse::getCityInfoList).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
        if (basicCityInfoDto == null) {
            return JSONResult.success(new SearchKeywordRespVo(keywordList, otherCityKeywordList));
        }
        keywordList.forEach(item -> {
            item.setCountryId(basicCityInfoDto.getCountryId());
            item.setCountryName(basicCityInfoDto.getCountryName());
        });
        return JSONResult.success(new SearchKeywordRespVo(keywordList, otherCityKeywordList));
    }

    public JSONResult<SearchKeywordRespVo> searchKeywordFromDistribution(@RequestBody SearchKeywordReqVo requestParam) {
        log.info("fuzzyHotelCityFromDistributionRequestParam: {}", JsonUtils.toJsonString(requestParam));
        //出差申请单城市管控判断
        boolean applyCityControl = false;
        if (Objects.nonNull(requestParam.getTrafficId())){
            BasicCityListRequest request = new BasicCityListRequest();
            request.setCityIdList(Collections.singletonList(requestParam.getCityId()));
            JSONResult<BasicIntegratedCityResponse> cityResponse = basicDataClient.listBasicCityInfoByIds(request);
            BasicCityInfoDto cityInfoDto = Optional.ofNullable(cityResponse).map(JSONResult::getData).map(BasicIntegratedCityResponse::getCityInfoList).orElse(new ArrayList<>()).stream().findFirst().orElse(new BasicCityInfoDto());
            boolean domesticTag = Objects.equals(cityInfoDto.getAreaType(), DOMESTIC_TYPE);
            //有出差申请单走出差申请单
            ApplyTripTrafficVerifyResponse trafficVerifyResponse = hotelBasicDataService.getApplyTripTrafficVerify(requestParam.getTrafficId());
            applyCityControl = checkApplyTripCityLimited(trafficVerifyResponse, domesticTag);
        }
        // 组装数据
        List<KeywordVo> keywordList = new ArrayList<>(16);
        List<KeywordVo> otherCityKeywordList = new ArrayList<>(16);
        FuzzyHotelCityFromDistributionRequest fromDistributionRequest = new FuzzyHotelCityFromDistributionRequest();
        fromDistributionRequest.setKeyword(requestParam.getKeyword());
        fromDistributionRequest.setCorpPayType(requestParam.getCorpPayType());
        fromDistributionRequest.setProductType(requestParam.getProductType());
        fromDistributionRequest.setHaveApplyNo(applyCityControl);
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        fromDistributionRequest.setUserInfo(baseUserInfo);
        fromDistributionRequest.setOnlyGeoData(false);
        fromDistributionRequest.setSearchRange(requestParam.getSearchRange());
        log.info("fuzzyHotelCityFromDistributionFromDistributionRequest: {}", JsonUtils.toJsonString(fromDistributionRequest));
        FuzzyHotelCityFromDistributionResponse fuzzyHotelCityFromDistributionResponse = iHotelDataService.fuzzyHotelCityFromDistribution(fromDistributionRequest);
        log.info("fuzzyHotelCityFromDistributionResponseJSONResult: {}", JsonUtils.toJsonString(fuzzyHotelCityFromDistributionResponse.getDestinationInfoList()));
        if (fuzzyHotelCityFromDistributionResponse != null && CollectionUtils.isNotEmpty(fuzzyHotelCityFromDistributionResponse.getDestinationInfoList())
                && requestParam != null && (StringUtils.isNotEmpty(requestParam.getCityId()) || StringUtils.isNotEmpty(requestParam.getProductType()))) {
            log.info("requestParamGetCityId(): {}", JsonUtils.toJsonString(requestParam.getCityId()));
            Integer cityId = Integer.parseInt(requestParam.getCityId());
            List<DestinationInfoListDTO> destinationInfoList=fuzzyHotelCityFromDistributionResponse.getDestinationInfoList().stream()
                    .filter(item -> cityId.equals(item.getCityId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(destinationInfoList)) {
                keywordList.addAll(destinationInfoList.stream().map(KeywordVo::new).collect(Collectors.toList()));
            }
            keywordList = keywordList.subList(0, Math.min(keywordList.size(), 15));
            log.info("fuzzyHotelCityFromDistribution applyCityControl: {}", JsonUtils.toJsonString(applyCityControl));
            // 非申请单模式必须组装到15条
            if (!applyCityControl) {
                fromDistributionRequest.setHaveApplyNo(false);
                int size = Math.max(0, 15 - keywordList.size());
                List<DestinationInfoListDTO> otherCityDestinationList;
                Boolean hmtCityDisplay = commonApollo.getHMTCityDisplay();
                if (!hmtCityDisplay && HOTEL.getCode().equalsIgnoreCase(requestParam.getProductType())) {
                    otherCityDestinationList = fuzzyHotelCityFromDistributionResponse.getDestinationInfoList().stream()
                            .filter(item -> item.getProvinceId() != null && item.getProvinceId() != null &&!hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId().toString()))
                            .filter(item -> item.getCityId() != null && !cityId.equals(item.getCityId()))
                            .collect(Collectors.toList());
                } else if (hmtCityDisplay && !HOTEL.getCode().equalsIgnoreCase(requestParam.getProductType())) {
                    otherCityDestinationList = fuzzyHotelCityFromDistributionResponse.getDestinationInfoList().stream()
                            .filter(item -> item.getProvinceId() != null && item.getProvinceId() != null &&!hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId().toString()))
                            .filter(item -> item.getCityId() != null && !cityId.equals(item.getCityId()))
                            .collect(Collectors.toList());
                } else{
                    otherCityDestinationList = fuzzyHotelCityFromDistributionResponse.getDestinationInfoList().stream()
                            .filter(item -> item.getCityId() != null && !cityId.equals(item.getCityId()))
                            .collect(Collectors.toList());
                }
                log.info("otherCityDestinationList(): {}", JsonUtils.toJsonString(otherCityDestinationList));
                //基于传入cityId,进行海内外酒店过滤
                for (int i = 0; i < size && i < otherCityDestinationList.size(); i++) {
                    KeywordVo keywordVo = new KeywordVo(otherCityDestinationList.get(i));
                    otherCityKeywordList.add(keywordVo);
                }
            }else{
                fromDistributionRequest.setHaveApplyNo(true);
                List<DestinationInfoListDTO> otherCityDestinationList;
                log.info("fuzzyHotelCityFromDistribution hmtCityDisplay:{}", JsonUtils.toJsonString(commonApollo.getHMTCityDisplay()));
                Boolean hmtCityDisplay = commonApollo.getHMTCityDisplay();
                if (!hmtCityDisplay && HOTEL.getCode().equalsIgnoreCase(requestParam.getProductType())) {
                    otherCityDestinationList = fuzzyHotelCityFromDistributionResponse.getDestinationInfoList().stream()
                            .filter(destination -> destination != null && destination.getParentCityList() != null && destination.getParentCityList().stream()
                                    .anyMatch(cityEntity -> cityEntity != null && cityEntity.getCityId() != null && cityEntity.getCityId().equals(cityId)))
                            .filter(item -> item.getProvinceId() != null && !hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId().toString()))
                            .filter(item -> item.getCityId() != null && !cityId.equals(item.getCityId()))
                            .collect(Collectors.toList());
                } else if (hmtCityDisplay && !HOTEL.getCode().equalsIgnoreCase(requestParam.getProductType())) {
                    otherCityDestinationList = fuzzyHotelCityFromDistributionResponse.getDestinationInfoList().stream()
                            .filter(destination -> destination != null && destination.getParentCityList() != null && destination.getParentCityList().stream()
                                    .anyMatch(cityEntity -> cityEntity != null && cityEntity.getCityId() != null && cityEntity.getCityId().equals(cityId)))
                            .filter(item -> item.getProvinceId() != null && !hongKongMacauTaiwanProvinceIdList.contains(item.getProvinceId().toString()))
                            .filter(item -> item.getCityId() != null && !cityId.equals(item.getCityId()))
                            .collect(Collectors.toList());
                } else{
                    otherCityDestinationList = fuzzyHotelCityFromDistributionResponse.getDestinationInfoList().stream()
                            .filter(item -> item.getCityId() != null && !cityId.equals(item.getCityId()))
                            .filter(destination -> destination != null && destination.getParentCityList() != null && destination.getParentCityList().stream()
                                    .anyMatch(cityEntity -> cityEntity != null && cityEntity.getCityId() != null &&cityEntity.getCityId().equals(cityId)))
                            .collect(Collectors.toList());
                }
                log.info("关键词接口替换 otherCityDestinationList={}", JsonUtils.toJsonString(otherCityDestinationList));
                if (CollectionUtils.isNotEmpty(otherCityDestinationList)) {
                    otherCityKeywordList.addAll(otherCityDestinationList.stream().map(KeywordVo::new).collect(Collectors.toList()));
                }
            }
        }
        log.info("关键词接口替换 keywordList={},otherCityKeywordList={}", JsonUtils.toJsonString(keywordList), JsonUtils.toJsonString(otherCityKeywordList));
        return JSONResult.success(new SearchKeywordRespVo(keywordList, otherCityKeywordList));
    }
}
