package com.corpgovernment.core.service.custom.impl;

import com.corpgovernment.api.hotel.booking.core.QueryHotelListReqVo;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.hotelconfig.model.entity.MultiTravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelApplication;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.BasicRoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hoteldetail.model.entity.OverLimitInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomBaseInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPrice;
import com.corpgovernment.core.domain.hoteldetail.model.enums.RoomBedTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.WindowEnum;
import com.corpgovernment.core.domain.hoteldetail.service.IHotelDetailDomainService;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelCard;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPage;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControl;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControlRequest;
import com.corpgovernment.core.domain.hotelpage.service.IHotelPageDomainService;
import com.corpgovernment.core.service.custom.IHotelCustomService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-25 12:03
 */
@Service
@Slf4j
public class XuGongHotelCustomService implements IHotelCustomService {

    @Resource
    private IHotelPageDomainService hotelPageDomainService;

    @Resource
    private ICommonDomainService commonDomainService;

    @Resource
    private IHotelDetailDomainService hotelDetailDomainService;

    @Resource(name = "xuGongQueryHotelList")
    private ThreadPoolExecutor xuGongQueryHotelList;

    @Override
    public List<TravelControl> getTravelControlList(HotelPageRequest hotelPageRequest, TravelStandard travelStandard, TravelConfig travelConfig, Boolean travelStandardFilter) {
        List<TravelControlRequest> travelControlRequestList = new ArrayList<>();

        if (travelStandard != null && travelStandard.getMultiTravelStandard() != null && CollectionUtils.isNotEmpty(travelStandard.getMultiTravelStandard().getLadderTravelStandardList())) {

            TravelApplication travelApplication = Null.or(travelStandard.getTravelApplication(), new TravelApplication());

            for (MultiTravelStandard.LadderTravelStandard ladderTravelStandard : travelStandard.getMultiTravelStandard().getLadderTravelStandardList()) {
                if (ladderTravelStandard == null) {
                    continue;
                }
                travelControlRequestList.add(TravelControlRequest.builder()
                        .priority(ladderTravelStandard.getSort())
                        .canBook(true)
                        .minPrice(ladderTravelStandard.getMinPrice())
                        .maxPrice(ladderTravelStandard.getMaxPrice())
                        .starList(ladderTravelStandard.getStarList())
                        .bookKeyWord(travelApplication.getBookAddress())
                        .bookLat(travelApplication.getBookLat())
                        .bookLon(travelApplication.getBookLon())
                        .sortTypeEnum(ladderTravelStandard.getSortTypeEnum())
                        .sortDirectionEnum(ladderTravelStandard.getSortDirectionEnum())
                        .hotelDistance(ladderTravelStandard.getHotelDistance())
                        .hotelQuantity(ladderTravelStandard.getHotelQuantity())
                        .brandIdList(ladderTravelStandard.getBrandIdList())
                        .hasBreakfast(ladderTravelStandard.getHasBreakfast()).build());
            }
        }

        if (CollectionUtils.isEmpty(travelControlRequestList)) {
            travelControlRequestList.add(TravelControlRequest.builder()
                    .priority(1)
                    .canBook(true).build());
        }

        return hotelPageDomainService.controlRequest(hotelPageRequest, travelControlRequestList);
    }

    @Override
    public HotelPage queryHotelList(QueryHotelListReqVo requestParam, List<TravelControl> travelControlList, TravelConfig travelConfig) {
        if (CollectionUtils.isEmpty(travelControlList) || travelConfig == null) {
            return null;
        }

        // 去除管控差标并排序
        travelControlList = travelControlList.stream().filter(item -> item != null && CollectionUtils.isEmpty(item.getControlledList()))
                .sorted(Comparator.comparing(item -> Null.or(item.getPriority(), Integer.MAX_VALUE))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(travelControlList)) {
            return null;
        }

        // 多线程
        ConcurrentHashMap<Integer, List<HotelCard>> hotelCardMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        for (TravelControl travelControl : travelControlList) {
            if (travelControl == null || travelControl.getHotelPageRequest() == null) {
                continue;
            }

            completableFutureList.add(CompletableFuture.runAsync(() -> {
                // 获取酒店卡片列表
                Map<String, HotelPage> hotelPageMap = hotelPageDomainService.getHotelPage(travelControl.getHotelPageRequest(), 1, false);

                // 聚合
                Map<String, List<HotelCard>> hotelCardListMap = new HashMap<>();
                hotelPageMap.forEach((key, value) -> {
                    if (value == null || StringUtils.isBlank(key) || CollectionUtils.isEmpty(value.getHotelCardList())) {
                        return;
                    }
                    hotelCardListMap.put(key, value.getHotelCardList());
                });
                List<HotelCard> hotelCardList = hotelPageDomainService.aggregationHotelCardListMap(
                        hotelCardListMap,
                        travelControl.getHotelPageRequest().getHotelSort(),
                        Optional.ofNullable(travelConfig.getHotelListSortRule()).map(item -> item == 1).orElse(false),
                        travelConfig.getPrioritySupplierCodeList(),
                        null);

                // 设置差标标识
                if (CollectionUtils.isNotEmpty(hotelCardList)) {
                    for (HotelCard hotelCard : hotelCardList) {
                        if (hotelCard == null) {
                            continue;
                        }
                        hotelCard.setTravelStandardMark(Optional.ofNullable(travelControl.getPriority()).map(Object::toString).orElse("1"));
                    }
                }

                hotelCardMap.put(Null.or(travelControl.getPriority(), 1), hotelCardList);
            }, xuGongQueryHotelList));
        }

        try {
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(commonDomainService.getSupplierTimeOut(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("请求超时", e);
        }

        // 获取pageSize
        Integer pageSize = travelControlList.stream()
                .filter(item -> item != null
                        && item.getHotelPageRequest() != null
                        && item.getHotelPageRequest().getPageSize() != null)
                .map(item -> item.getHotelPageRequest().getPageSize()).findFirst().orElse(null);

        if (CollectionUtils.isEmpty(hotelCardMap) || pageSize == null) {
            return null;
        }

        // 数据填充
        List<HotelCard> resultHotelCardList = new ArrayList<>();
        Set<String> existSupplierHotelIdSet = new HashSet<>();
        for (List<HotelCard> hotelCardList : new TreeMap<>(hotelCardMap).values()) {
            if (CollectionUtils.isEmpty(hotelCardList)) {
                continue;
            }
            if (resultHotelCardList.size() >= pageSize) {
                break;
            }
            for (HotelCard hotelCard : hotelCardList) {
                if (hotelCard == null || CollectionUtils.isEmpty(hotelCard.getSupplierList())) {
                    continue;
                }
                if (resultHotelCardList.size() >= pageSize) {
                    break;
                }
                Set<String> supplierHotelIdSet = hotelCard.getSupplierList().stream().filter(item -> item != null && StringUtils.isNotBlank(item.getSupplierCode()) && StringUtils.isNotBlank(item.getHotelId()))
                        .map(item -> item.getSupplierCode() + item.getHotelId()).collect(Collectors.toSet());
                if (supplierHotelIdSet.stream().anyMatch(existSupplierHotelIdSet::contains)) {
                    existSupplierHotelIdSet.addAll(supplierHotelIdSet);
                    continue;
                }
                existSupplierHotelIdSet.addAll(supplierHotelIdSet);
                resultHotelCardList.add(hotelCard);
            }
        }

        return HotelPage.builder()
                .hotelCardList(resultHotelCardList)
                .lastPage(true).build();
    }

    @Override
    public void specialHandleHotelDetail(HotelDetail hotelDetail, List<String> prioritySupplierCodeList, Integer hotelRoomSortRule) {
        if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return;
        }

        // 展开房型卡片列表
        List<BasicRoomCard> unfoldBasicRoomCardList = hotelDetailDomainService.unfoldBasicRoomCard(hotelDetail);
        log.info("展开房型卡片列表：{}", JsonUtils.toJsonString(unfoldBasicRoomCardList));
        if (CollectionUtils.isEmpty(unfoldBasicRoomCardList)) {
            return;
        }

        // 过滤不可订和超标的房型
        List<BasicRoomCard> tmpList = unfoldBasicRoomCardList.stream().filter(item -> item != null
                && item.getMinRoomPrice() != null
                && commonDomainService.checkCustomPrice(item.getMinRoomPrice().getFinalAvgPrice())
                && Boolean.TRUE.equals(checkRoomCardList(item.getRoomCardList()))
                && Boolean.TRUE.equals(item.getRoomCardList().get(0).getCanReserve())
                && Boolean.TRUE.equals(checkOverLimitInfo(item.getRoomCardList().get(0).getOverLimitInfo()))).collect(Collectors.toList());

        // 找最低价大床房
        BasicRoomCard kingBasicRoomCard = tmpList.stream()
                .filter(item -> item.getRoomCardList().get(0).getRoomBaseInfo() != null
                        && Objects.equals(item.getRoomCardList().get(0).getRoomBaseInfo().getRoomBedTypeEnum(), RoomBedTypeEnum.KING_ROOM))
                .sorted(
                        Comparator.comparing((BasicRoomCard item) -> Optional.ofNullable(item)
                                        .map(BasicRoomCard::getRoomCardList)
                                        .map(a -> a.get(0))

                                        .map(RoomCard::getRoomBaseInfo)
                                        .map(RoomBaseInfo::getWindowEnum)
                                        .map(WindowEnum::getPriority)
                                        .orElse(WindowEnum.UNKNOWN.getPriority()))
                        .thenComparing(item -> Optional.ofNullable(item)
                                .map(BasicRoomCard::getRoomCardList)
                                .map(a -> a.get(0))
                                .map(RoomCard::getRoomPrice)
                                .map(RoomPrice::getFinalAvgPrice)
                                .map(Price::getCustomPrice)
                                .orElse(HotelCoreConstant.MAX_PRICE)))
                .findFirst()
                .orElse(null);

        // 找最低价双床房
        BasicRoomCard twinBasicRoomCard = tmpList.stream().filter(item -> item.getRoomCardList().get(0).getRoomBaseInfo() != null
                        && Objects.equals(item.getRoomCardList().get(0).getRoomBaseInfo().getRoomBedTypeEnum(), RoomBedTypeEnum.TWIN_ROOM))
                .sorted(
                        Comparator.comparing((BasicRoomCard item) -> Optional.ofNullable(item)
                                        .map(BasicRoomCard::getRoomCardList)
                                        .map(a -> a.get(0))
                                        .map(RoomCard::getRoomBaseInfo)
                                        .map(RoomBaseInfo::getWindowEnum)
                                        .map(WindowEnum::getPriority)
                                        .orElse(WindowEnum.UNKNOWN.getPriority()))
                                .thenComparing(item -> Optional.ofNullable(item)
                                        .map(BasicRoomCard::getRoomCardList)
                                        .map(a -> a.get(0))
                                        .map(RoomCard::getRoomPrice)
                                        .map(RoomPrice::getFinalAvgPrice)
                                        .map(Price::getCustomPrice)
                                        .orElse(HotelCoreConstant.MAX_PRICE)))
                .findFirst()
                .orElse(null);

        // 组装返回
        List<BasicRoomCard> resultList = new ArrayList<>();
        if (kingBasicRoomCard != null) {
            resultList.add(kingBasicRoomCard);
        }
        if (twinBasicRoomCard != null) {
            resultList.add(twinBasicRoomCard);
        }
        hotelDetail.setBasicRoomCardList(resultList);
        // 排序
        hotelDetailDomainService.sortBasicRoomCardList(hotelDetail, prioritySupplierCodeList, hotelRoomSortRule);
    }

    @Override
    public HotelCustomEnum hotelCustomEnum() {
        return HotelCustomEnum.XU_GONG;
    }

    /**
     * 校验房型列表
     * @param roomCardList 房型列表
     * @return 返回是否校验通过
     */
    private Boolean checkRoomCardList(List<RoomCard> roomCardList) {
        return CollectionUtils.isNotEmpty(roomCardList) && roomCardList.get(0) != null;
    }

    /**
     * 校验超限信息
     * @param overLimitInfo 超限信息
     * @return 返回是否校验通过
     */
    private Boolean checkOverLimitInfo(OverLimitInfo overLimitInfo) {
        return overLimitInfo == null || CollectionUtils.isEmpty(overLimitInfo.getOverLimitRuleNameList());
    }

}
