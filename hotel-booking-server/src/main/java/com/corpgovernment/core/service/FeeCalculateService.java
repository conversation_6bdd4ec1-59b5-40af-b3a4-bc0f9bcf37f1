package com.corpgovernment.core.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.corpgovernment.dto.snapshot.dto.hotel.fee.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.corpgovernment.api.hotel.booking.core.BonusPointInfo;
import com.corpgovernment.api.hotel.booking.core.CancelPolicy;
import com.corpgovernment.api.hotel.booking.core.FeeCalculateReq;
import com.corpgovernment.api.hotel.booking.core.FeeCalculateResp;
import com.corpgovernment.api.hotel.booking.core.FeeInfo;
import com.corpgovernment.api.hotel.booking.core.HotelLimitInformation;
import com.corpgovernment.api.hotel.booking.core.RoomDailyPriceInfo;
import com.corpgovernment.api.hotel.booking.core.ServiceFeeInfo;
import com.corpgovernment.api.hotel.booking.core.StepCancelPolicy;
import com.corpgovernment.api.hotel.booking.core.TaxFeeInfo;
import com.corpgovernment.api.hotel.booking.core.TimeInformation;
import com.corpgovernment.api.hotel.booking.core.TravelExceedInfo;
import com.corpgovernment.api.organization.enums.SwitchFieldKeyEnum;
import com.corpgovernment.api.travelstandard.enums.ControlTypeEnum;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.ExpenseTypeEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.converter.model.queryparam.QueryParamModel;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.constant.PaymentMethodType;
import com.corpgovernment.core.domain.common.model.enums.TravelAttributeEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.SnapshotDataTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.CancelRuleEnum;
import com.corpgovernment.core.service.bo.PayTypeEnumDTO;
import com.corpgovernment.core.service.bo.RoomInfo;
import com.corpgovernment.dto.config.AllSwitchDTO;
import com.corpgovernment.dto.config.SwitchDTO;
import com.corpgovernment.dto.config.request.GetBookingConfigByTokenRequest;
import com.corpgovernment.dto.config.response.GetBookingConfigByTokenResponse;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.snapshot.PriceInfoType;
import com.corpgovernment.dto.snapshot.SnapshotQtyCmd;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.ServiceFeeDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.BasicRoomInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.FilterRoomInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.HotelBaseInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.RoomInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.RoomPolicyServiceDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.SupplierStarDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelFeeSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelProductSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.request.SaveHotelCalculateResultRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelFeeSnapshotResponse;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelProductSnapshotResponse;
import com.corpgovernment.dto.snapshot.dto.hotel.response.SaveHotelCalculateResultResponse;
import com.corpgovernment.util.AmountPrecisionUtil;
import com.corpgovernment.util.BizTypeContextUtil;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.DateUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 费用计算
 */
@Slf4j
@Service
public class FeeCalculateService {


    @Autowired
    private CoreServiceClient coreServiceClient;

    @Autowired
    private TravelCheckService travelCheckService;
    
    @Resource
    private ICommonDomainService commonDomainService;
    
    /**
     * 费用计算
     */
    public FeeCalculateResp calculate(FeeCalculateReq req) {
        log.info("++++费用计算req:{}", JsonUtils.toJsonString(req));
        // 1. 查询费用快照、 产品快照、酒店快照、预定快照
        GetHotelFeeSnapshotResponse getHotelFeeSnapshotResponse = queryFeeSnapshot(req.getToken());
        HotelFeeInfoDTO feeSnapshot = getHotelFeeSnapshotResponse.getHotelFeeInfo();
        GetHotelProductSnapshotResponse productSnapshot = queryProductSnapshot(req.getToken(), req.getProductId(), req.getHotelId(), req.getRoomId(), req.getSupplierCode(), req.getPayment());
        GetBookingConfigByTokenResponse bookingConfig = getBookingConfig(req.getToken());
        QueryParamModel queryParamModel = getQueryParamModel(req.getToken());
        BizTypeContextUtil.saveBizTypeToContext(Optional.ofNullable(queryParamModel)
                .map(QueryParamModel::getProductType)
                .map(BizTypeEnum::getByCodeOrName)
                .orElse(null));
        
        // 2. 构建房间基本信息：计算订单金额, 房费金额, 服务费金额
        RoomInfo roomInfo = buildRoomInfo(req, feeSnapshot, productSnapshot, bookingConfig, queryParamModel);

        // 3. 获取可用支付方式
        PayTypeEnumDTO usablePayTypeDTO = getUsablePayTypes(roomInfo);

        // 4. 封装结果
        FeeCalculateResp ret = buildResult(usablePayTypeDTO, feeSnapshot, roomInfo, bookingConfig);

        // 5. 保存计算结果
        saveResult(ret, req.getToken());

        log.info("++++费用计算ret:{}", JsonUtils.toJsonString(ret));
        return ret;
    }
    
    private QueryParamModel getQueryParamModel(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        // 获取快照
        SnapshotQtyCmd snapshotQtyCmd = new SnapshotQtyCmd();
        snapshotQtyCmd.setToken(token);
        snapshotQtyCmd.setDataTypeList(Collections.singletonList(SnapshotDataTypeEnum.QUERY_PARAM.getCode()));
        JSONResult<QuerySnapshotResponseDTO> result = coreServiceClient.getSnapshot(snapshotQtyCmd);
        log.info("获取快照 snapshot={}", JsonUtils.toJsonString(result));
        
        return Optional.ofNullable(result)
                .map(JSONResult::getData)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .map(item -> item.get(0)).map(item -> JsonUtils.parse(item.getSnapshotData(), QueryParamModel.class))
                .orElse(null);
    }
    
    private Boolean getOverseasHotelControlIncludeExtraTax(String expenseType, GetBookingConfigByTokenResponse bookingConfig, QueryParamModel queryParamModel) {
        if (!StringUtils.equalsIgnoreCase(ExpenseTypeEnum.PUB.getCode(), expenseType)) {
            return false;
        }
        
        BizTypeEnum bizTypeEnum = Optional.ofNullable(queryParamModel).map(QueryParamModel::getProductType).map(BizTypeEnum::getByCodeOrName).orElse(null);
        if (!Objects.equals(bizTypeEnum, BizTypeEnum.HOTEL_INTL)) {
            return false;
        }
        
        return Optional.ofNullable(bookingConfig)
                .map(GetBookingConfigByTokenResponse::getAllSwitch)
                .map(AllSwitchDTO::getSwitchInfoSoaMap)
                .map(item -> item.get(TravelAttributeEnum.OVERSEAS_HOTEL_CONTROL_INCLUDE_EXTRA_TAX.getCode()))
                .map(SwitchDTO::getValue)
                .map(item -> JsonUtils.parseArray(item, Integer.class))
                .map(item -> item.get(0))
                .map(item -> item == 1).orElse(false);
    }
    
    private GetBookingConfigByTokenResponse getBookingConfig(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        GetBookingConfigByTokenRequest request = new GetBookingConfigByTokenRequest();
        request.setToken(token);
        try {
            JSONResult<GetBookingConfigByTokenResponse> result = coreServiceClient.getBookingConfigByToken(request);
            if (result == null) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("获取预定配置异常", e);
            return null;
        }
    }

    /**
     * 保存计算结果
     */
    private void saveResult(FeeCalculateResp ret, String token) {
        SaveHotelCalculateResultRequest req = convertResultToSaveReq(ret, token);
        log.info("+++++保存费用计算结果：req: {}", JsonUtils.toJsonString(req));
        JSONResult<SaveHotelCalculateResultResponse> saveRet = coreServiceClient.saveHotelCalculateResult(req);
        log.info("+++++保存费用计算结果：ret: {}", JsonUtils.toJsonString(saveRet));
        if (saveRet == null || !saveRet.isSUCCESS()) {
            throw new CorpBusinessException(HotelResponseCodeEnum.UN, "费用计算结果保存失败");
        }
    }


    /**
     * 获取可用的支付方式
     * 逻辑参考文档2.2：<a href="http://conf.ctripcorp.com/pages/resumedraft.action?draftId=2491941558&draftShareId=458a27db-faeb-4983-b691-1694e5c22f74&">...</a>
     * @param roomInfo                   房间信息
     */
    private PayTypeEnumDTO getUsablePayTypes(RoomInfo roomInfo) {
        log.info("++++++获取可用的支付方式,参数roomInfo:{}", JsonUtils.toJsonString(roomInfo));

        PayTypeEnumDTO ret = null;
        if (ExpenseTypeEnum.PUB == roomInfo.getExpensiveType()) {
            ret = getUsablePayTypesForPub(roomInfo);
        } else {
            ret = getUsablePayTypesForOwn(roomInfo.getPaymentMethodType(), roomInfo.isSupportPPAY());
        }
        log.info("++++++获取可用的支付方式,响应：{}", JsonUtils.toJsonString(ret));
        
        if (CollectionUtils.isEmpty(ret.getPayTypeEnums())) {
            log.warn("+++++++没有可用的支付方式，ret:{}", JsonUtils.toJsonString(ret));
        }
        return ret;
    }


    /**
     * 构建响应结果
     *
     * @param payTypeEnumDTO： 支持的付费类型
     * @param feeSnapshot：    费用快照
     */
    private FeeCalculateResp buildResult(PayTypeEnumDTO payTypeEnumDTO,
                                         HotelFeeInfoDTO feeSnapshot,
                                         RoomInfo roomInfo,
                                         GetBookingConfigByTokenResponse bookingConfig) {
        log.info("payTypeEnumDTO={} feeSnapshot={} roomInfo={} bookingConfig={}",
                JsonUtils.toJsonString(payTypeEnumDTO),
                JsonUtils.toJsonString(feeSnapshot),
                JsonUtils.toJsonString(roomInfo),
                JsonUtils.toJsonString(bookingConfig));
        FeeCalculateResp resp = new FeeCalculateResp();

        // set payment method
        String paymentMethodType = roomInfo.getPaymentMethodType().getCode(); // 酒店支付方式：在线付/前台到付
        resp.setPaymentMethod(paymentMethodType);

        // set fee info map
        if(CollectionUtils.isNotEmpty(payTypeEnumDTO.getPayTypeEnums())){
            Map<String, List<FeeInfo>> feeMap = buildFeeInfo(payTypeEnumDTO, feeSnapshot, roomInfo);
            resp.setFeeInfoMap(feeMap);
        }else{
            resp.setFeeInfoMap(Collections.emptyMap());
        }
        
        TravelExceedInfo travelExceedInfo = handleRoomExceedInfoList(payTypeEnumDTO.getTravelExceedInfo(), roomInfo.getRoomNum());
        // set travel exceed info
        resp.setTravelExceedInfo(travelExceedInfo);

        // set bonus point
        BonusPointInfo bonusPointInfo = buildBonusPointInfoType(feeSnapshot.getBonusPointInfo());
        resp.setBonusPointInfo(bonusPointInfo);


        // set invoice type List
        InvoiceInfoType invoiceInfo = feeSnapshot.getInvoiceInfo();
        if (invoiceInfo != null && CollectionUtils.isNotEmpty(invoiceInfo.getInvoiceList())) {
            resp.setInvoiceTypeList(invoiceInfo.getInvoiceList());
        }

        // set remark info
        com.corpgovernment.api.hotel.booking.core.RemarkInfoType remarkInfo = buildRemarkInfo(feeSnapshot);
        resp.setRemarkInfo(remarkInfo);

        // set room limit
        HotelLimitInformation hotelLimitInformation = buildHotelLimitInformation(feeSnapshot);
        resp.setHotelLimitInformationType(hotelLimitInformation);
        
        Boolean becomeOverLimit = Boolean.FALSE.equals(feeSnapshot.getLastOverLimit())
                && Optional.ofNullable(payTypeEnumDTO.getTravelExceedInfo()).map(TravelExceedInfo::isExceed).orElse(false);
        ExpenseTypeEnum expenseTypeEnum = roomInfo.getExpensiveType();
        ChangeInfoType changeInfoType = feeSnapshot.getChangeInfo();
        resp.setBecomeOverLimit(becomeOverLimit);
        resp.setPriceChangeNoAllowReserve(getPriceChangeNoAllowReserve(becomeOverLimit, expenseTypeEnum, changeInfoType));
        resp.setExtraTaxChangeNoAllowReserve(getExtraTaxChangeNoAllowReserve(becomeOverLimit, expenseTypeEnum, changeInfoType, bookingConfig));
        
        // 构建取消政策
        CancelPolicy cancelPolicy = buildCancelPolicy(feeSnapshot);
        log.info("构建取消政策 feeSnapshot={} cancelPolicy={}", JsonUtils.toJsonString(feeSnapshot), JsonUtils.toJsonString(cancelPolicy));
        resp.setCancelPolicy(cancelPolicy);
        
        // 塞时间信息
        TimeInformation timeInformation = buildTimeInformationType(feeSnapshot.getTimeInformationType());
        log.info("构建时间信息 timeInformation={}", JsonUtils.toJsonString(timeInformation));
        resp.setTimeInformationType(timeInformation);
        
        return resp;
    }
    
    private TimeInformation buildTimeInformationType(TimeInformationType timeInformationType) {
        if (timeInformationType == null) {
            return null;
        }
        
        TimeInformation timeInformation = new TimeInformation();
        timeInformation.setLastCancelTime(timeInformationType.getLastCancelTime());
        timeInformation.setEarlyArrivalTime(timeInformationType.getEarlyArrivalTime());
        timeInformation.setLastArrivalTime(timeInformationType.getLastArrivalTime());
        timeInformation.setHoldTime(timeInformationType.getHoldTime());
        return timeInformation;
    }
    
    private CancelPolicy buildCancelPolicy(HotelFeeInfoDTO feeSnapshot) {
        if (feeSnapshot == null) {
            return null;
        }
        
        // 阶梯取消政策优先级最高
        List<LadderDeductionInfoType> ladderDeductionInfoList = feeSnapshot.getLadderDeductionInfoList();
        if (CollectionUtils.isNotEmpty(ladderDeductionInfoList)
                && ladderDeductionInfoList.stream().anyMatch(item -> item != null && item.getLadderDeductionInfo() != null)) {
            // 转换
            List<StepCancelPolicy> stepCancelPolicyList = ladderDeductionInfoList.stream()
                    .filter(item -> item != null && item.getLadderDeductionInfo() != null)
                    .map(item -> {
                        StepCancelPolicy stepCancelPolicy = new StepCancelPolicy();
                        stepCancelPolicy.setCancelRuleType(Optional.ofNullable(item.getDeductionType())
                                .map(CancelRuleEnum::getEnum)
                                .map(CancelRuleEnum::getCode)
                                .orElse(null));
                        stepCancelPolicy.setPrice(item.getLadderDeductionInfo().getAmount());
                        stepCancelPolicy.setEndTime(DateUtils.format(item.getLadderDeductionInfo().getEndDeductTime(), DateUtils.DATE_TIME_FORMAT));
                        stepCancelPolicy.setStartTime(DateUtils.format(item.getLadderDeductionInfo().getStartDeductTime(), DateUtils.DATE_TIME_FORMAT));
                        return stepCancelPolicy;
                    })
                    .sorted(Comparator.comparing(item -> Null.or(item.getEndTime(), HotelCoreConstant.DEFAULT_TIME)))
                    .collect(Collectors.toList());
            
            // 返回
            CancelPolicy cancelPolicy = new CancelPolicy();
            cancelPolicy.setCancelRuleType(CancelRuleEnum.LADDER.getCode());
            cancelPolicy.setStepCancelPolicyList(stepCancelPolicyList);
            return cancelPolicy;
        }
        
        // 转换取消类型
        CancelRuleEnum cancelRuleEnum = Optional.ofNullable(feeSnapshot.getCancelPenalties())
                .map(CancelPenaltyType::getPolicyType)
                .map(CancelRuleEnum::getEnum)
                .orElse(null);
        if (cancelRuleEnum == null) {
            cancelRuleEnum = Optional.ofNullable(feeSnapshot.getCancelPenalties())
                    .map(CancelPenaltyType::getFreeCancelPolicySceneType)
                    .map(CancelRuleEnum::getEnum)
                    .orElse(CancelRuleEnum.UN_KNOWN);
        }
        
        CancelPolicy cancelPolicy = new CancelPolicy();
        cancelPolicy.setCancelRuleType(cancelRuleEnum.getCode());
        // 限时取消
        if (Objects.equals(cancelRuleEnum, CancelRuleEnum.TIME_LIMIT)) {
            cancelPolicy.setEndFreeCancelTime(Optional.ofNullable(feeSnapshot.getTimeInformationType())
                    .map(TimeInformationType::getLastCancelTime)
                    .orElse(null));
        }
        
        return cancelPolicy;
    }
    
    private TravelExceedInfo handleRoomExceedInfoList(TravelExceedInfo travelExceedInfo, Integer roomNum) {
        if (travelExceedInfo == null) {
            return null;
        }
        
        List<TravelExceedInfo.RoomExceedInfo> roomExceedInfoList = travelExceedInfo.getRoomExceedInfoList();
        if (roomExceedInfoList != null) {
            return travelExceedInfo;
        }
        
        if (roomNum == null || roomNum <= 0) {
            return travelExceedInfo;
        }
        
        roomExceedInfoList = new ArrayList<>();
        for (int i = 0; i < roomNum; i++) {
            TravelExceedInfo.RoomExceedInfo roomExceedInfo = new TravelExceedInfo.RoomExceedInfo();
            roomExceedInfo.setExceed(false);
            roomExceedInfoList.add(roomExceedInfo);
        }
        travelExceedInfo.setRoomExceedInfoList(roomExceedInfoList);
        return travelExceedInfo;
    }
    
    private Boolean getExtraTaxChangeNoAllowReserve(Boolean becomeOverLimit, ExpenseTypeEnum expenseTypeEnum, ChangeInfoType changeInfoType, GetBookingConfigByTokenResponse bookingConfig) {
        boolean openSwitch = false;
        String value = Optional.ofNullable(bookingConfig)
                .map(GetBookingConfigByTokenResponse::getAllSwitch)
                .map(AllSwitchDTO::getSwitchInfoSoaMap)
                .map(item -> item.get(TravelAttributeEnum.OVERSEAS_HOTEL_CONTROL_INCLUDE_EXTRA_TAX.getCode()))
                .map(SwitchDTO::getValue)
                .orElse(null);
        if (StringUtils.isNotBlank(value)) {
            List<Integer> switchValue = JsonUtils.parseArray(value, Integer.class);
            openSwitch = CollectionUtils.isNotEmpty(switchValue) && switchValue.get(0) != null && switchValue.get(0) == 1;
        }
        
        
        // 因公 + 变超标 + 税费变化 + 开关打开
        return expenseTypeEnum == ExpenseTypeEnum.PUB
                && Boolean.TRUE.equals(becomeOverLimit)
                && Boolean.TRUE.equals(Optional.ofNullable(changeInfoType).map(ChangeInfoType::getChangeExtraTax).orElse(false))
                && openSwitch;
    }
    
    private Boolean getPriceChangeNoAllowReserve(Boolean becomeOverLimit, ExpenseTypeEnum expenseTypeEnum, ChangeInfoType changeInfoType) {
        // 因公 + 变超标 + 价格变化
        return expenseTypeEnum == ExpenseTypeEnum.PUB
                && Boolean.TRUE.equals(becomeOverLimit)
                && Boolean.TRUE.equals(Optional.ofNullable(changeInfoType).map(ChangeInfoType::getChangePrice).orElse(false));
    }
    
    private HotelLimitInformation buildHotelLimitInformation(HotelFeeInfoDTO feeSnapshot) {
        if(feeSnapshot == null){
            return null;
        }
        HotelLimitInformationType hotelLimitInformationType = feeSnapshot.getHotelLimitInformationType();
        if(hotelLimitInformationType == null){
            return null;
        }
        HotelLimitInformation hotelLimitInformation = new HotelLimitInformation();
        hotelLimitInformation.setGuestPerson(hotelLimitInformationType.getGuestPerson());
        hotelLimitInformation.setMaxBookingRoomNum(hotelLimitInformationType.getMaxBookingRoomNum());
        hotelLimitInformation.setMinBookingRoomNum(hotelLimitInformationType.getMinBookingRoomNum());
        return hotelLimitInformation;
    }

    private com.corpgovernment.api.hotel.booking.core.RemarkInfoType buildRemarkInfo(HotelFeeInfoDTO feeSnapshot) {
        RemarkInfoType remarkInfo = feeSnapshot.getRemarkInfo();
        if(remarkInfo == null){
            return null;
        }
        com.corpgovernment.api.hotel.booking.core.RemarkInfoType remarkInfoType = new com.corpgovernment.api.hotel.booking.core.RemarkInfoType();
        remarkInfoType.setReceiveTextRemark(remarkInfo.getReceiveTextRemark());
        List<RemarkType> remarkList = remarkInfo.getRemarkList();
        if(CollectionUtils.isNotEmpty(remarkList)){
            List<com.corpgovernment.api.hotel.booking.core.RemarkType> list = remarkList.stream().map(
                    item -> {
                        com.corpgovernment.api.hotel.booking.core.RemarkType remarkType = new com.corpgovernment.api.hotel.booking.core.RemarkType();
                        remarkType.setId(item.getId());
                        remarkType.setDesc(item.getDesc());
                        remarkType.setKey(item.getKey());
                        remarkType.setTitle(item.getTitle());
                        remarkType.setUnique(item.getUnique());
                        remarkType.setDefaultOption(item.getDefaultOption());
                        remarkType.setNeedUserValue(item.getNeedUserValue());
                        return remarkType;
                    }
            ).collect(Collectors.toList());
            remarkInfoType.setRemarkList(list);
        }
        return remarkInfoType;
    }


    /**
     * 获取可用的支付方式(因公)
     */
    private PayTypeEnumDTO getUsablePayTypesForPub(RoomInfo roomInfo) {
        PaymentMethodType paymentMethodType = roomInfo.getPaymentMethodType();
        if (paymentMethodType.isOnline()) {// 在线付
            return getUsablePayTypesForPubWithOnline(roomInfo);
        } else { // 到店付
            return getUsablePayTypesForPubWithCash(roomInfo);
        }
    }

    private PayTypeEnumDTO getUsablePayTypesForPubWithCash(RoomInfo roomInfo) {
        log.error("++++获取可用的支付方式(因公=>到店付), roomInfo:{}", JsonUtils.toJsonString(roomInfo));
        // 超标检查
        TravelExceedInfo travelExceedInfo = verifyTravelStandard(roomInfo);
        ArrayList<PayTypeEnum> payTypeEnums = new ArrayList<>();
        // 超标
        if (travelExceedInfo.isExceed()) {
            if(travelExceedInfo.getRejectTypes().contains(ControlTypeEnum.C.getCode())){ //管控包含RC
                payTypeEnums.add(PayTypeEnum.CASH); // 公付
            }else{
                log.error("+++++++++到付超标，但是不支持RC,所以无可以支付方式，禁止预定，travelExceedInfo：{} ", JsonUtils.toJsonString(travelExceedInfo));
            }
        } else {
            payTypeEnums.add(PayTypeEnum.CASH); // 前台现付
        }
        PayTypeEnumDTO ret = PayTypeEnumDTO.build(travelExceedInfo, payTypeEnums);
        log.error("++++获取可用的支付方式(因公=>到店付), ret:{}", JsonUtils.toJsonString(ret));
        return ret;
    }

    /**
     * 获取可用的支付方式(因私)
     */
    private PayTypeEnumDTO getUsablePayTypesForOwn(PaymentMethodType paymentMethodType, boolean supportPPAY) {
        if (paymentMethodType.isOnline()) {// 在线付
            if (supportPPAY) {
                return PayTypeEnumDTO.build(PayTypeEnum.PPAY); // 个人支付
            }
        } else { // 到店付
            return PayTypeEnumDTO.build(PayTypeEnum.CASH);  // 前台现付
        }
        return PayTypeEnumDTO.build(null);
    }

    /**
     * 获取可用的支付方式(因公=>在线支付)
     */
    private PayTypeEnumDTO getUsablePayTypesForPubWithOnline(RoomInfo roomInfo) {
        log.error("++++获取可用的支付方式(因公=>在线支付), roomInfo:{}", JsonUtils.toJsonString(roomInfo));
        ArrayList<PayTypeEnum> payTypeEnums = new ArrayList<>();
        // 超标检查
        TravelExceedInfo travelExceedInfo = verifyTravelStandard(roomInfo);
        // 超标
        if (travelExceedInfo.isExceed()) {
            Set<String> rejectTypes = travelExceedInfo.getRejectTypes();
            if (rejectTypes.contains(ControlTypeEnum.M.getCode())) { //管控包含随心付
                if (roomInfo.isSupportACCNT()) { // 支持个人支付且支持公账支付
                    if (!Boolean.TRUE.equals(roomInfo.getDirectSupplier())) {
                        payTypeEnums.add(PayTypeEnum.MIXPAY);
                    }
                }
            }
            if (rejectTypes.contains(ControlTypeEnum.C.getCode())) { // 支持RC
                if (roomInfo.isSupportACCNT()) { // 支持公账支付
                    payTypeEnums.add(PayTypeEnum.ACCNT);
                }
                if (roomInfo.isSupportPPAY()) { // 支持个付
                    payTypeEnums.add(PayTypeEnum.PPAY);
                }
            }
        } else { // 不超标
            if (roomInfo.isSupportACCNT()) { // 支持公付
                payTypeEnums.add(PayTypeEnum.ACCNT);
            }
            if (roomInfo.isSupportPPAY()) { // 支持个付
                payTypeEnums.add(PayTypeEnum.PPAY);
            }
        }
        PayTypeEnumDTO ret = PayTypeEnumDTO.build(travelExceedInfo, payTypeEnums);
        log.error("++++获取可用的支付方式(因公=>在线支付), ret:{}", JsonUtils.toJsonString(ret));
        return ret;
    }


    /**
     * 基于费用快照计算订单金额
     * 订单金额 = 房费 + 服务费
     *
     * @param roomDailyPriceTypeList 每日房价
     * @param roomNum                房间数
     */
    private BigDecimal calculateRoomsAmount(List<RoomDailyPriceType> roomDailyPriceTypeList, Integer roomNum) {
        if (CollectionUtils.isEmpty(roomDailyPriceTypeList)) {
            log.error("++++费用快照，每日房价信息为空，roomDailyPriceTypeList is null.");
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "费用快照，每日房价信息为空");
        }

        BigDecimal roomsAmount = BigDecimal.ZERO;
        // 累加房费
        for (RoomDailyPriceType dailyPrice : roomDailyPriceTypeList) {
            PriceInfoType sellPrice = dailyPrice.getSellPrice();
            if (sellPrice == null || sellPrice.getPrice() == null) {
                log.error("++++费用快照，每日房价信息为空，roomDailyPriceTypeList: {}", JsonUtils.toJsonString(roomDailyPriceTypeList));
                throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "费用快照，每日房价信息为空");
            }
            roomsAmount = roomsAmount.add(sellPrice.getPrice().multiply(BigDecimal.valueOf(roomNum)));
        }
        return roomsAmount;
    }
    
    private BigDecimal getRoomAmount(HotelFeeInfoDTO feeSnapshot) {
        BigDecimal supplierTotalPrice = Optional.ofNullable(feeSnapshot)
                .map(HotelFeeInfoDTO::getSupplierTotalPrice)
                .map(PriceInfoType::getPrice).orElse(null);
        if (supplierTotalPrice == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "费用快照，供应商总价格为空");
        }
        return supplierTotalPrice;
    }

    /**
     * 查询产品快照
     *
     * @param token：   快照token
     * @param hotelId: 酒店ID
     * @param roomId：  房型ID
     */
    private GetHotelProductSnapshotResponse queryProductSnapshot(String token, String productId, String hotelId, String roomId, String supplierCode, String paymentMethod) {
        GetHotelProductSnapshotRequest req = buildGetHotelProductSnapshotRequest(token, productId, hotelId, roomId, supplierCode, paymentMethod);
        log.info("+++++++查询产品快照， req: {}", JsonUtils.toJsonString(req));
        JSONResult<GetHotelProductSnapshotResponse> response = coreServiceClient.getHotelProductSnapshot(req);
        log.info("+++++++查询产品快照, response:{}", JsonUtils.toJsonString(response));
        if (response != null && response.isSUCCESS()) {
            return response.getData();
        } else {
            throw new CorpBusinessException(response.getStatus(), response.getMsg());
        }
    }


    /**
     * 查询费用快照
     */
    private GetHotelFeeSnapshotResponse queryFeeSnapshot(String token) {
        log.info("++++查询费用快照，token:{}", token);
        GetHotelFeeSnapshotRequest req = new GetHotelFeeSnapshotRequest();
        req.setToken(token);
        JSONResult<GetHotelFeeSnapshotResponse> response = coreServiceClient.getFeeInfoSnapshot(req);
        log.info("++++查询费用快照，response:{}", JsonUtils.toJsonString(response));
        if (response != null && response.isSUCCESS()) {
            HotelFeeInfoDTO dto = response.getData().getHotelFeeInfo();
            if (dto == null) {
                log.error("++++查询费用快照, 快照信息不存在，token：{}", token);
                throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "快照信息不存在");
            }
        } else {
            log.error("费用快照查询出错, token:" + token);
            throw new CorpBusinessException(HotelResponseCodeEnum.UN.code(), "费用快照查询出错");
        }
        return response.getData();
    }

    /**
     * 超标校验
     * roomInfo
     */
    private TravelExceedInfo verifyTravelStandard(RoomInfo roomInfo) {
        VerifyTravelStandardRequest req = buildVerifyTravelStandardRequest(roomInfo);
        return travelCheckService.verifyTravelStandard(req);
    }
    /*
     * 构建费用信息
     * @param payTypeList: 支持的支付类型
     * @param orderOrderAmount: 订单金额
     * @param exceedAmount: 超标金额
     * @param feeSnapshot: 费用快照
     */
    private Map<String/*payType*/, List<FeeInfo>> buildFeeInfo(PayTypeEnumDTO payTypeListDTO, HotelFeeInfoDTO feeSnapshot, RoomInfo roomInfo) {
        Map<String, List<FeeInfo>> retMap = new HashMap<>();
        for (PayTypeEnum payType : payTypeListDTO.getPayTypeEnums()) {
            FeeInfo feeInfo = new FeeInfo();
            // set currency
            feeInfo.setCurrency(getCurrencyFromFeeSnapshot(feeSnapshot));
            // 每日房价
            feeInfo.setRoomDailyPriceInfoList(buildRoomDailyPriceInfo(feeSnapshot.getRoomDailyPriceTypeList()));
            // 服务费
            feeInfo.setServiceFeeInfo(buildServiceFeeInfo(feeSnapshot.getServiceFee()));
            // 税费
            feeInfo.setTaxFeeInfoList(buildTaxFeeInfo(feeSnapshot));
            // setAmount
            TravelExceedInfo travelExceedInfo = payTypeListDTO.getTravelExceedInfo();
            BigDecimal exceedAmount = travelExceedInfo != null ? travelExceedInfo.getExceedAmount() : BigDecimal.ZERO;
            setFeeInfoAmount(payType, roomInfo.getOrderAmount(), exceedAmount, feeInfo);
            // set rooms amount
            feeInfo.setRoomsAmount(roomInfo.getRoomsAmount());
            // set service charge info
            setServiceChangeInfo(roomInfo, payType, feeInfo);
            // fill map
            retMap.put(payType.getType(), Lists.newArrayList(feeInfo));
        }
        return retMap;
    }

    private void setServiceChangeInfo(RoomInfo roomInfo, PayTypeEnum payType, FeeInfo feeInfo) {
        Map<String, ServiceChargeInfoDTO> serviceChargeInfoMap = roomInfo.getServiceChargeInfoMap();
        if(serviceChargeInfoMap != null){
            ServiceChargeInfoDTO serviceChargeInfoDTO = serviceChargeInfoMap.get(payType.getType());
            if(serviceChargeInfoDTO != null){
                log.info("++++++++++= set serviceChargeInfoDTO, serviceChargeInfoDTO:{}", JsonUtils.toJsonString(serviceChargeInfoDTO));
                setServiceChargeInfo(feeInfo, serviceChargeInfoDTO, roomInfo.getPaymentMethodType(), payType);
            }
        }
    }

    /**
     * 设置服务费信息
     * 在线付-> 个付：加到个付金额、公付｜混付：加到公付金额
     * 到店付-> 加到个付金额
     * -- -- -- --
     * 订单金额：都要➕服务费
     */
    private void setServiceChargeInfo(FeeInfo feeInfo, ServiceChargeInfoDTO serviceChargeInfoDTO, PaymentMethodType paymentMethodType, PayTypeEnum payType) {
        if (serviceChargeInfoDTO == null) {
            return;
        }
        log.info("+++++ setServiceChargeInfo, feeInfo:{}, dto:{}", JsonUtils.toJsonString(feeInfo), JsonUtils.toJsonString(serviceChargeInfoDTO));
        feeInfo.setTotalServiceCharge(serviceChargeInfoDTO.getTotalServiceCharge());
        feeInfo.setServiceChargeStrategy(serviceChargeInfoDTO.getServiceChargeStrategy());
        feeInfo.setServiceChargeStrategyValue(serviceChargeInfoDTO.getServiceChargeStrategyValue());
        if (serviceChargeInfoDTO.getTotalServiceCharge().compareTo(BigDecimal.ZERO) > 0) {
            log.info("++++ add total charge, totalServiceCharge:{}", serviceChargeInfoDTO.getTotalServiceCharge());
            if(paymentMethodType.isOnline()){ // 在线付-> 个付：加到个付金额、公付｜混付：加到公付金额
                if(PayTypeEnum.PPAY == payType){
                    addServiceFeeToPriPayamount(feeInfo, serviceChargeInfoDTO);
                }else{
                    addServiceFeeToPubPayAmount(feeInfo, serviceChargeInfoDTO);
                }
            }else{ // 到店付资源，且前收服务费时，需展示在线个付金额（等于服务费金额）
                addServiceFeeToPriPayamount(feeInfo, serviceChargeInfoDTO);
            }
            addServiceFeeToOrderAmount(feeInfo, serviceChargeInfoDTO);
        }
    }

    private void addServiceFeeToOrderAmount(FeeInfo feeInfo, ServiceChargeInfoDTO serviceChargeInfoDTO) {
        // 设置服务费信息
        BigDecimal orderAmount = feeInfo.getOrderAmount();
        if(orderAmount == null){
            orderAmount = serviceChargeInfoDTO.getTotalServiceCharge();
        }else {
            orderAmount = orderAmount.add(serviceChargeInfoDTO.getTotalServiceCharge());
        }
        feeInfo.setOrderAmount(orderAmount);
    }

    private void addServiceFeeToPriPayamount(FeeInfo feeInfo, ServiceChargeInfoDTO serviceChargeInfoDTO) {
        log.info("+++ add to priPayAmount");
        BigDecimal priPayAmount = feeInfo.getPriPayAmount();
        if (priPayAmount == null) {
            priPayAmount = serviceChargeInfoDTO.getTotalServiceCharge();
        } else {
            priPayAmount = priPayAmount.add(serviceChargeInfoDTO.getTotalServiceCharge());
        }
        feeInfo.setPriPayAmount(priPayAmount);
    }

    private void addServiceFeeToPubPayAmount(FeeInfo feeInfo, ServiceChargeInfoDTO serviceChargeInfoDTO) {
        log.info("++++ add to pubPayAmount");
        BigDecimal pubPayAmount = feeInfo.getPubPayAmount();
        if (pubPayAmount == null) {
            pubPayAmount = serviceChargeInfoDTO.getTotalServiceCharge();
        } else {
            pubPayAmount = pubPayAmount.add(serviceChargeInfoDTO.getTotalServiceCharge());
        }
        feeInfo.setPubPayAmount(pubPayAmount);
    }

    // 取任意一个当前费用的币种
    private String getCurrencyFromFeeSnapshot(HotelFeeInfoDTO feeSnapshot) {
        return feeSnapshot.getRoomDailyPriceTypeList().get(0).getSellPrice().getCurrency();
    }

    // 设置费用信息
    private void setFeeInfoAmount(PayTypeEnum payType, BigDecimal orderOrderAmount, BigDecimal exceedAmount, FeeInfo feeInfo) {
        if (payType == PayTypeEnum.ACCNT) { // 公付
            feeInfo.setPubPayAmount(orderOrderAmount);
        }

        if (payType == PayTypeEnum.PPAY) { // 个付
            feeInfo.setPriPayAmount(orderOrderAmount);
        }

        if (payType == PayTypeEnum.MIXPAY) { // 超标自付
            feeInfo.setPriPayAmount(exceedAmount);
            feeInfo.setPubPayAmount(orderOrderAmount.subtract(exceedAmount));
        }

        feeInfo.setOrderAmount(orderOrderAmount);
    }


    /**
     * 构建税费信息
     */
    private List<TaxFeeInfo> buildTaxFeeInfo(HotelFeeInfoDTO feeInfoDTO) {
        if (feeInfoDTO == null || feeInfoDTO.getTaxInfo() == null || CollectionUtils.isEmpty(feeInfoDTO.getTaxInfo().getTaxFeeInfoList())) {
            log.info("+++++++税费信息为空");
            return Collections.emptyList();
        }
        List<TaxFeeType> taxList = feeInfoDTO.getTaxInfo().getTaxFeeInfoList();
        List<TaxFeeInfo> retList = new ArrayList<>();
        // 根据是否包含在订单总价中分类
        Map<Boolean, List<TaxFeeType>> feeClassMap = taxList.stream().collect(Collectors.groupingBy(TaxFeeType::getIncludeInTotalPrice));
        Set<Map.Entry<Boolean, List<TaxFeeType>>> feeClassMapEntry = feeClassMap.entrySet();
        for (Map.Entry<Boolean, List<TaxFeeType>> entry : feeClassMapEntry) {
            TaxFeeInfo info = buildTaxFeeInfo(entry);
            retList.add(info);
        }
        return retList;
    }

    /**
     * 构建税费信息
     */
    private TaxFeeInfo buildTaxFeeInfo(Map.Entry<Boolean, List<TaxFeeType>> entry) {
        // set include
        Boolean include = entry.getKey();

        TaxFeeInfo info = new TaxFeeInfo();
        info.setIncludeInTotalPrice(include);

        // set itemList
        List<TaxFeeInfo.Item> details = entry.getValue().stream().map(
                item -> {
                    TaxFeeInfo.Item detail = new TaxFeeInfo.Item();
                    detail.setTaxId(item.getTaxId());
                    detail.setTypeName(item.getTaxTypeName());
                    detail.setSellPrice(item.getSellPrice().getPrice());
                    if (item.getOriginalPrice() != null) {
                        detail.setOriginPrice(item.getOriginalPrice().getPrice());
                    }
                    return detail;
                }
        ).collect(Collectors.toList());
        info.setItemList(details);

        // set origin currency
        if (entry.getValue().get(0).getOriginalPrice() != null) {
            String originCurrency = entry.getValue().get(0).getOriginalPrice().getCurrency();
            info.setOriginCurrency(originCurrency);
        }

        // set total price
        BigDecimal totalSellPrice = BigDecimal.ZERO;
        BigDecimal totalOriginPrice = BigDecimal.ZERO;
        for (TaxFeeInfo.Item detail : details) {
            if(detail.getSellPrice() != null){
                totalSellPrice = totalSellPrice.add(detail.getSellPrice());
            }
            if (detail.getOriginPrice() != null) {
                totalOriginPrice = totalOriginPrice.add(detail.getOriginPrice());
            }
        }
        info.setTotalOriginPrice(totalOriginPrice);
        info.setTotalSellPrice(totalSellPrice);
        return info;
    }

    /**
     * 构建每日房价信息
     */
    private List<RoomDailyPriceInfo> buildRoomDailyPriceInfo(List<RoomDailyPriceType> prices) {
        return prices.stream().map(
                item -> {
                    RoomDailyPriceInfo info = new RoomDailyPriceInfo();
                    info.setAmount(item.getSellPrice().getPrice());
                    info.setEffectDate(item.getEffectDate());
                    info.setMealCount(item.getMealCount());
                    return info;
                }
        ).collect(Collectors.toList());
    }

    /**
     * 构建积分信息
     */
    private BonusPointInfo buildBonusPointInfoType(BonusPointInfoType bpit) {
        if (bpit == null) {
            return null;
        }
        BonusPointInfo info = new BonusPointInfo();
        info.setGroupId(bpit.getGroupId());
        info.setGroupName(bpit.getGroupName());
        info.setBonusPointType(bpit.getBonusPointType());
        info.setBonusPointDescList(bpit.getFillPageRuleDescList());
        return info;
    }

    private ServiceFeeInfo buildServiceFeeInfo(ServiceFeeDTO dto) {
        if (dto == null) {
            return null;
        }
        ServiceFeeInfo info = new ServiceFeeInfo();
        info.setServiceFeeName(dto.getServiceFeeName());
        info.setServiceFeeDesc(dto.getServiceFeeDesc());
        info.setPrice(dto.getFee().getPrice());
        if (dto.getOriginalFee() != null) {
            info.setOriginCurrency(dto.getOriginalFee().getCurrency());
            info.setTotalOriginPrice(dto.getOriginalFee().getPrice());
        }
        return info;
    }


    private VerifyTravelStandardRequest buildVerifyTravelStandardRequest(RoomInfo roomInfo) {
        VerifyTravelStandardRequest req = new VerifyTravelStandardRequest();
        req.setTravelStandardToken(roomInfo.getToken());
        req.setBizType(BizTypeEnum.HOTEL.getCode());
        HotelVerifyRequest vr = new HotelVerifyRequest();
        vr.setHotelId(roomInfo.getHotelId());
        vr.setResourcesId(String.format(roomInfo.getHotelId()) + "#" + String.format(roomInfo.getRoomId()));
        vr.setRoomId(roomInfo.getRoomId());
        // 传入均价验超标
        BigDecimal averageAmount = AmountPrecisionUtil.divideByBizTypeContext(roomInfo.getRoomsAmount(), new BigDecimal(roomInfo.getRoomDayCount()));
        if (Boolean.TRUE.equals(roomInfo.getOverseasHotelControlIncludeExtraTax()) && roomInfo.getAvgExtraTax() != null) {
            averageAmount = averageAmount.add(roomInfo.getAvgExtraTax());
        }
        vr.setPrice(averageAmount);
        vr.setStar(roomInfo.getStar());
        vr.setStarLicence(roomInfo.getStarLicence());
        vr.setBrandId(roomInfo.getBrandId());
        vr.setPriceControlStrategy(roomInfo.getPriceControlStrategy());
        vr.setDailyPriceList(buildDailyPriceList(roomInfo.getDailyPriceList(), roomInfo.getOverseasHotelControlIncludeExtraTax(), roomInfo.getAvgExtraTax()));
        req.setHotelList(Lists.newArrayList(vr));
        Integer levelStep = travelCheckService.parseLevelStep(roomInfo.getTravelStandardMark());
        vr.setStepLevel(levelStep);
        return req;
    }
    
    private List<HotelVerifyRequest.DailyPrice> buildDailyPriceList(List<RoomInfo.DailyPrice> dailyPriceList, Boolean overseasHotelControlIncludeExtraTax, BigDecimal avgExtraTax) {
        if (CollectionUtils.isEmpty(dailyPriceList)) {
            return null;
        }
        
        List<HotelVerifyRequest.DailyPrice> resultList = new ArrayList<>();
        for (RoomInfo.DailyPrice dailyPrice : dailyPriceList) {
            if (dailyPrice == null || StringUtils.isBlank(dailyPrice.getDate()) || dailyPrice.getPrice() == null) {
                continue;
            }
            
            BigDecimal price = dailyPrice.getPrice();
            if (Boolean.TRUE.equals(overseasHotelControlIncludeExtraTax) && avgExtraTax != null) {
                price = price.add(avgExtraTax);
            }
            
            resultList.add(HotelVerifyRequest.DailyPrice.builder()
                    .date(dailyPrice.getDate())
                    .price(price).build());
        }
        
        return resultList;
    }

    private GetHotelProductSnapshotRequest buildGetHotelProductSnapshotRequest(String token, String productId, String hotelId, String roomId, String supplierCode, String paymentMethod) {
        GetHotelProductSnapshotRequest req = new GetHotelProductSnapshotRequest();
        req.setToken(token);
        FilterRoomInfoType ft = new FilterRoomInfoType();
        ft.setHotelId(hotelId);
        ft.setRoomId(roomId);
        ft.setSupplierCode(supplierCode);
        ft.setPaymentMethod(paymentMethod);
        ft.setProductId(productId);
        req.setFilterRoomInfo(ft);
        return req;
    }

    private RoomInfo buildRoomInfo(FeeCalculateReq req, HotelFeeInfoDTO feeSnapshot, GetHotelProductSnapshotResponse productSnapshot, GetBookingConfigByTokenResponse bookingConfig, QueryParamModel queryParamModel) {
        String travelStandardMark = travelCheckService.getTravelStandardMark(req.getToken());
        Integer roomDayCount = req.getRoomNum() * feeSnapshot.getRoomDailyPriceTypeList().size();// 间夜数
//        BigDecimal roomsAmount = calculateRoomsAmount(feeSnapshot.getRoomDailyPriceTypeList(), req.getRoomNum());
        BigDecimal roomsAmount = getRoomAmount(feeSnapshot);
        List<String> supportedPayTypeList = feeSnapshot.getPayTypeList().stream()
                .filter(item -> req.getExpenseType().equals(item.getName()))
                .map(PayTypeInfoType::getCode).collect(Collectors.toList());
        String urgentPayType = feeSnapshot.getUrgentPayType();
        Boolean urgentBooking = feeSnapshot.getUrgentBooking();
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setToken(req.getToken());
        roomInfo.setHotelId(req.getHotelId());
        roomInfo.setRoomId(req.getRoomId());
        roomInfo.setOrderAmount(roomsAmount);
        if(CollectionUtils.isNotEmpty(feeSnapshot.getServiceChargeInfoList())){
            log.info("++++++ set service charge list, serviceChargeInfoList:{}", JsonUtils.toJsonString(feeSnapshot.getServiceChargeInfoList()));
            Map<String/*payType*/, ServiceChargeInfoDTO> serviceChargeMap = feeSnapshot.getServiceChargeInfoList().stream()
                    .collect(Collectors.toMap(ServiceChargeInfoDTO::getPayType, Function.identity(), (v1, v2) -> v1));
            roomInfo.setServiceChargeInfoMap(serviceChargeMap);
        }
        roomInfo.setRoomsAmount(roomsAmount);
        HotelBaseInfoDTO hotelBaseInfoDto = getHotelProductSnapshot(productSnapshot);
        if (hotelBaseInfoDto.getSupplierStarInfo() == null) {
            roomInfo.setStar(hotelBaseInfoDto.getStar());
            roomInfo.setStarLicence(hotelBaseInfoDto.getStarLicence());
        } else {
            SupplierStarDTO supplierStarDto = hotelBaseInfoDto.getSupplierStarInfo().get(req.getSupplierCode());
            roomInfo.setStar(supplierStarDto.getStar());
            roomInfo.setStarLicence(supplierStarDto.getStarLicence());
        }
        roomInfo.setPaymentMethodType(getPaymentMethodTypeFromHotelProductSnapshot(productSnapshot));
        roomInfo.setRoomDayCount(roomDayCount);
        roomInfo.setBrandId(getBrandIdFromHotelProductSnapshot(productSnapshot));
        roomInfo.setTravelStandardMark(travelStandardMark);
        roomInfo.setSupportACCNT(supportACCNT(urgentBooking, urgentPayType, supportedPayTypeList));
        roomInfo.setSupportPPAY(supportPPAY(urgentBooking, urgentPayType, supportedPayTypeList));
        roomInfo.setExpensiveType(getExpensiveType(req.getExpenseType()));
        roomInfo.setDirectSupplier(Optional.ofNullable(productSnapshot.getBasicRoomInfo())
                .map(item -> item.get(0))
                .map(BasicRoomInfoDTO::getRoomCardList)
                .map(item -> item.get(0))
                .map(RoomInfoDTO::getDirectSupplier)
                .orElse(null));
        roomInfo.setOverseasHotelControlIncludeExtraTax(getOverseasHotelControlIncludeExtraTax(req.getExpenseType(), bookingConfig, queryParamModel));
        roomInfo.setPriceControlStrategy(getPriceControlStrategy(bookingConfig));
        BigDecimal totalExtraTax = getTotalExtraTax(feeSnapshot);
        if (totalExtraTax != null) {
            roomInfo.setTotalExtraTax(totalExtraTax);
            roomInfo.setAvgExtraTax(AmountPrecisionUtil.divideByBizTypeContext(totalExtraTax, new BigDecimal(roomDayCount)));
        }
        roomInfo.setRoomNum(req.getRoomNum());
        roomInfo.setDailyPriceList(buildFeeDailyPriceList(feeSnapshot.getRoomDailyPriceTypeList()));
        log.info("++++++++roomInfo:{}", JsonUtils.toJsonString(roomInfo));
        return roomInfo;
    }
    
    private List<RoomInfo.DailyPrice> buildFeeDailyPriceList(List<RoomDailyPriceType> roomDailyPriceTypeList) {
        if (CollectionUtils.isEmpty(roomDailyPriceTypeList)) {
            return null;
        }
        
        List<RoomInfo.DailyPrice> resultList = new ArrayList<>();
        for (RoomDailyPriceType roomDailyPriceType : roomDailyPriceTypeList) {
            if (roomDailyPriceType == null || roomDailyPriceType.getSellPrice() == null || roomDailyPriceType.getSellPrice().getPrice() == null) {
                continue;
            }
            
            resultList.add(RoomInfo.DailyPrice.builder()
                    .price(roomDailyPriceType.getSellPrice().getPrice())
                    .date(roomDailyPriceType.getEffectDate()).build());
        }
        
        return resultList;
    }
    
    private String getPriceControlStrategy(GetBookingConfigByTokenResponse bookingConfig) {
        try {
            return Optional.ofNullable(bookingConfig)
                    .map(GetBookingConfigByTokenResponse::getAllSwitch)
                    .map(AllSwitchDTO::getSwitchInfoSoaMap)
                    .map(item -> item.get(TravelAttributeEnum.HOTEL_PRICE_CONTROL_STRATEGY.getCode()))
                    .map(SwitchDTO::getValue)
                    .map(item -> JsonUtils.parseArray(item, Integer.class))
                    .map(item -> item.get(0))
                    .filter(item -> item == 2)
                    .map(item -> PriceControlStrategyEnum.DAILY_PRICE.getCode())
                    .orElse(PriceControlStrategyEnum.AVG_PRICE.getCode());
        } catch (Exception exception) {
            log.error("getPriceControlStrategy error", exception);
            return PriceControlStrategyEnum.AVG_PRICE.getCode();
        }
    }
    
    private BigDecimal getTotalExtraTax(HotelFeeInfoDTO feeSnapshot) {
        List<TaxFeeType> taxFeeTypeList = Optional.ofNullable(feeSnapshot)
                .map(HotelFeeInfoDTO::getTaxInfo)
                .map(TaxInfoType::getTaxFeeInfoList).orElse(null);
        if (CollectionUtils.isEmpty(taxFeeTypeList)) {
            return null;
        }
        
        BigDecimal totalExtraTax = BigDecimal.ZERO;
        
        for (TaxFeeType taxFeeType : taxFeeTypeList) {
            if (taxFeeType == null || !Boolean.FALSE.equals(taxFeeType.getIncludeInTotalPrice())) {
                continue;
            }
            
            totalExtraTax = totalExtraTax.add(Optional.ofNullable(taxFeeType.getSellPrice())
                    .map(PriceInfoType::getPrice).orElse(BigDecimal.ZERO));
        }
        
        return totalExtraTax;
    }
    
    /**
     * 是否支持公账支付
     */
    private boolean supportACCNT(Boolean urgentBooking, String urgentPayType, List<String> supportedPayTypeList){
        if(urgentBooking != null && urgentBooking){
            if(SwitchFieldKeyEnum.URGENT_PAY_TYPE_ALL.getKey().equals(urgentPayType) || SwitchFieldKeyEnum.URGENT_PAY_TYPE_ACCNT.getKey().equals(urgentPayType)){
                // 企业支持的支付类型
                return supportedPayTypeList.contains(PayTypeEnum.ACCNT.getType());
            }
        }else {
            // 企业支持的支付类型
            return supportedPayTypeList.contains(PayTypeEnum.ACCNT.getType());
        }
        return false;
    }

    /**
     * 是否支持个人支付
     */
    private boolean supportPPAY(Boolean urgentBooking, String urgentPayType, List<String> supportedPayTypeList){
        if(urgentBooking != null && urgentBooking){
            if(SwitchFieldKeyEnum.URGENT_PAY_TYPE_ALL.getKey().equals(urgentPayType) || SwitchFieldKeyEnum.URGENT_PAY_TYPE_PPAY.getKey().equals(urgentPayType)){
                // 企业支持的支付类型
                return supportedPayTypeList.contains(PayTypeEnum.PPAY.getType());
            }
        }else {
            // 企业支持的支付类型
            return supportedPayTypeList.contains(PayTypeEnum.PPAY.getType());
        }
        return false;
    }


    private HotelBaseInfoDTO getHotelProductSnapshot(GetHotelProductSnapshotResponse productSnapshot) {
        HotelBaseInfoDTO hotelInfo = productSnapshot.getHotelInfo();
        if (hotelInfo == null) {
            log.error("+++++产品快照数据有误, hotelInfo为空， productSnapshot:{}", JsonUtils.toJsonString(productSnapshot));
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "产品快照数据有误");
        }
        return hotelInfo;
    }

    private PaymentMethodType getPaymentMethodTypeFromHotelProductSnapshot(GetHotelProductSnapshotResponse productSnapshot) {
        List<BasicRoomInfoDTO> basicRoomInfoList = productSnapshot.getBasicRoomInfo();
        if (CollectionUtils.isEmpty(basicRoomInfoList)) {
            log.error("+++++产品快照数据有误, basicRoomInfo为空， productSnapshot:{}", JsonUtils.toJsonString(productSnapshot));
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "产品快照数据有误");
        }

        // 基于roomID 查询结果只有一个
        BasicRoomInfoDTO basicRoomInfo = basicRoomInfoList.get(0);
        List<RoomInfoDTO> roomCardList = basicRoomInfo.getRoomCardList();
        if (CollectionUtils.isEmpty(roomCardList)) {
            log.error("+++++产品快照数据有误, roomCardList为空， productSnapshot:{}", JsonUtils.toJsonString(productSnapshot));
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "产品快照数据有误");
        }

        // roomCard 也只有一个
        RoomInfoDTO roomCard = roomCardList.get(0);
        RoomPolicyServiceDTO roomPolicyService = roomCard.getRoomPolicyService();
        if (roomPolicyService == null) {
            log.error("+++++产品快照数据有误, roomPolicyService为空， productSnapshot:{}", JsonUtils.toJsonString(productSnapshot));
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "产品快照数据有误");
        }

        com.corpgovernment.dto.snapshot.dto.hotel.PaymentMethodType paymentMethod = roomPolicyService.getPaymentMethod();
        if (paymentMethod == null || StringUtils.isBlank(paymentMethod.getType())) {
            log.error("+++++产品快照数据有误, PaymentMethodType为空， productSnapshot:{}", JsonUtils.toJsonString(productSnapshot));
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "产品快照数据有误");
        }

        String typeCode = paymentMethod.getType();
        PaymentMethodType type = PaymentMethodType.getByCode(typeCode);
        if (type == null) {
            log.error("+++++产品快照数据有误, PaymentMethodType为不合法， productSnapshot:{}", JsonUtils.toJsonString(productSnapshot));
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "产品快照数据有误");
        }
        return type;
    }

    private ExpenseTypeEnum getExpensiveType(String type) {
        for (ExpenseTypeEnum item : ExpenseTypeEnum.values()) {
            if (item.getCode().equals(type)) {
                return item;
            }
        }
        return null;
    }


    /**
     * 从产品快照获得品牌ID
     */
    private String getBrandIdFromHotelProductSnapshot(GetHotelProductSnapshotResponse productSnapshot) {
        if(productSnapshot == null){
            return null;
        }
        List<BasicRoomInfoDTO> basicRoomInfoList = productSnapshot.getBasicRoomInfo();
        if(CollectionUtils.isEmpty(basicRoomInfoList)){
            return null;
        }
        BasicRoomInfoDTO basicRoomInfo = basicRoomInfoList.get(0);
        if(basicRoomInfo == null){
            return null;
        }
        List<RoomInfoDTO> roomCardList = basicRoomInfo.getRoomCardList();
        if(CollectionUtils.isEmpty(roomCardList)){
            return null;
        }
        RoomInfoDTO roomInfo = roomCardList.get(0);
        if(roomInfo == null){
            return null;
        }
        return roomInfo.getBrandId();
    }

    /**
     * 转换快照结果对象
     */
    private SaveHotelCalculateResultRequest convertResultToSaveReq(FeeCalculateResp ret, String token) {
        SaveHotelCalculateResultRequest req = new SaveHotelCalculateResultRequest();
        req.setPaymentMethod(ret.getPaymentMethod());
        req.setTravelExceedInfo(convertTravelExceedInfo(ret.getTravelExceedInfo()));
        req.setFeeInfoMap(convertFeeInfoMap(ret.getFeeInfoMap()));
        req.setHasGeneratedToken(token);
        return req;
    }

    private Map<String, FeeInfoType> convertFeeInfoMap(Map<String, List<FeeInfo>> feeInfoMap) {
        if (CollectionUtils.isEmpty(feeInfoMap)) {
            return Collections.emptyMap();
        }
        Map<String, FeeInfoType> retMap = new HashMap<>();
        for (Map.Entry<String, List<FeeInfo>> feeInfoItem : feeInfoMap.entrySet()) {
            String payType = feeInfoItem.getKey();
            FeeInfoType feeInfoType = convertFeeInfoType(feeInfoItem.getValue());
            if (StringUtil.isBlank(payType) || feeInfoType == null) {
                log.error("+++++保存费用快照信息出错, 结果不正确, payType:{}, feeInfoType:{}", payType, JsonUtils.toJsonString(feeInfoType));
                continue;
            }
            retMap.put(payType, feeInfoType);
        }
        return retMap;
    }

    private FeeInfoType convertFeeInfoType(List<FeeInfo> feeInfos) {
        if (CollectionUtils.isEmpty(feeInfos)) {
            return null;
        }

        FeeInfo feeInfo = feeInfos.get(0); // 当前只支持一个币种
        String currency = feeInfo.getCurrency();
        PriceInfoType orderAmount = buildPriceInfoType(feeInfo.getOrderAmount(), currency);
        PriceInfoType roomsAmount = buildPriceInfoType(feeInfo.getRoomsAmount(), currency);
        PriceInfoType pubPayAmount = buildPriceInfoType(feeInfo.getPubPayAmount(), currency);
        PriceInfoType priPayAmount = buildPriceInfoType(feeInfo.getPriPayAmount(), currency);
        PriceInfoType totalServiceCharge = buildPriceInfoType(feeInfo.getTotalServiceCharge(), currency);
        String serviceChargeStrategy = feeInfo.getServiceChargeStrategy();
        BigDecimal serviceChargeStrategyValue = feeInfo.getServiceChargeStrategyValue();


        FeeInfoType feeInfoType = new FeeInfoType();
        feeInfoType.setOrderAmount(orderAmount);
        feeInfoType.setRoomsAmount(roomsAmount);
        feeInfoType.setPubPayAmount(pubPayAmount);
        feeInfoType.setPriPayAmount(priPayAmount);
        feeInfoType.setTotalServiceCharge(totalServiceCharge);
        feeInfoType.setServiceChargeStrategy(serviceChargeStrategy);
        feeInfoType.setServiceChargeStrategyValue(serviceChargeStrategyValue);
        return feeInfoType;
    }


    private TravelExceedInfoType convertTravelExceedInfo(TravelExceedInfo info) {
        TravelExceedInfoType item = new TravelExceedInfoType();
        item.setExceed(info.isExceed());
        item.setExceedItemList(info.getExceedItemList());
        item.setRejectTypes(info.getRejectTypes());
        item.setReasonList(convertReasonList(info.getReasonList()));
        item.setExceedType(info.getExceedType());
        item.setExceedResultDescList(info.getExceedResultDescList());
        item.setRoomExceedInfoList(buildRoomExceedInfoList(info.getRoomExceedInfoList()));
        return item;
    }
    
    private List<TravelExceedInfoType.RoomExceedInfo> buildRoomExceedInfoList(List<TravelExceedInfo.RoomExceedInfo> roomExceedInfoList) {
        if (CollectionUtils.isEmpty(roomExceedInfoList)) {
            return null;
        }
        
        return roomExceedInfoList.stream().filter(Objects::nonNull).map(item -> {
            TravelExceedInfoType.RoomExceedInfo roomExceedInfo = new TravelExceedInfoType.RoomExceedInfo();
            roomExceedInfo.setExceed(item.isExceed());
            roomExceedInfo.setExceedType(item.getExceedType());
            roomExceedInfo.setExceedAmount(item.getExceedAmount());
            return roomExceedInfo;
        }).collect(Collectors.toList());
    }
    
    private List<TravelExceedInfoType.ExceedReason> convertReasonList(List<TravelExceedInfo.ExceedReason> reasonList) {
        if (CollectionUtils.isEmpty(reasonList)) {
            return Collections.emptyList();
        }
        return reasonList.stream().map(item -> {
            TravelExceedInfoType.ExceedReason reason = new TravelExceedInfoType.ExceedReason();
            reason.setId(item.getId());
            reason.setName(item.getName());
            return reason;
        }).collect(Collectors.toList());
    }


    private PriceInfoType buildPriceInfoType(BigDecimal price, String currency) {
        PriceInfoType priceInfoType = new PriceInfoType();
        priceInfoType.setCurrency(currency);
        priceInfoType.setPrice(price);
        return priceInfoType;
    }
}
