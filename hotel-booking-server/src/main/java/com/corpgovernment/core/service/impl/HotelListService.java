package com.corpgovernment.core.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.corpgovernment.api.hotel.booking.core.MemberTag;
import com.corpgovernment.api.hotel.booking.core.QueryHotelListReqVo;
import com.corpgovernment.api.hotel.booking.core.QueryHotelListRespVo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import com.corpgovernment.core.domain.common.model.enums.ShuntEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.common.service.IHotelIndicatorDomainService;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelApplication;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hotelconfig.service.IHotelConfigDomainService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.MemberTagInfo;
import com.corpgovernment.core.domain.hotelpage.model.entity.*;
import com.corpgovernment.core.domain.hotelpage.model.enums.RequestControlEnum;
import com.corpgovernment.core.domain.hotelpage.service.IHotelPageDomainService;
import com.corpgovernment.core.service.IHotelListService;
import com.corpgovernment.core.service.custom.factory.HotelCustomServiceFactory;
import com.corpgovernment.orgsdk.client.ContentDicitionaryClient;
import com.corpgovernment.orgsdk.response.AgreementInfoQueryResp;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2024/3/8
 */
@Service
@Slf4j
public class HotelListService implements IHotelListService {

    @Resource
    private IHotelConfigDomainService hotelConfigDomainService;

    @Resource
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;

    @Resource
    private IHotelPageDomainService hotelPageDomainService;

    @Resource
    private ICommonDomainService commonDomainService;

    @Resource
    private IHotelIndicatorDomainService hotelIndicatorDomainService;

    @Resource
    private HotelCustomServiceFactory hotelCustomServiceFactory;

    // 协议类型
    public static final Integer TWO_PARTY_CONTRACT = 2;
    public static final Integer THREE_PARTY_CONTRACT = 3;
    public static final String AGREEMENT_TAG_ENABLE = "1";

    @Autowired
    private ContentDicitionaryClient contentDicitionaryClient;

    @Override
    @BusinessBehaviorMonitor
    public QueryHotelListRespVo queryHotelList(QueryHotelListReqVo requestParam) {
        QueryHotelListRespVo queryHotelListRespVo = null;
        TravelConfig travelConfig = null;
        HotelCustomEnum hotelCustomEnum = null;

        try {
            // 参数校验
            if (StringUtils.isBlank(requestParam.getCityId()) || requestParam.getPageIndex() == null || requestParam.getPageSize() == null
                    || requestParam.getRoomNum() == null || StringUtils.isBlank(requestParam.getCheckInDate()) || StringUtils.isBlank(requestParam.getCheckOutDate())
                    || StringUtils.isBlank(requestParam.getToken()) || requestParam.getPersonNum() == null || StringUtils.isBlank(requestParam.getTravelMode())) {
                throw new CorpBusinessException(HotelResponseCodeEnum.QUERY_PARAM_ERROR);
            }

            // 校验停留时间
            Boolean checkPass = checkStayTime(requestParam.getPageIndex());
            if (!Boolean.TRUE.equals(checkPass)) {
                throw new CorpBusinessException(HotelResponseCodeEnum.STAY_TIME_CHECK_FAIL);
            }
            if (CollectionUtils.isEmpty(requestParam.getStarList())){
                requestParam.setStarList(IntStream.rangeClosed(1,5).boxed().collect(Collectors.toList()));
            }

            // 获取差旅配置
            travelConfig = getTravelConfig(requestParam.getToken(), requestParam.getTravelMode(), requestParam.getProductType());

            // 获取差标
            TravelStandard travelStandard = getTravelStandard(requestParam.getToken(), requestParam.getTravelMode(), travelConfig.getBizTypeEnum());

            // 组装hotelPageRequest
            HotelPageRequest hotelPageRequest = HotelPageRequest.builder()
                    .abroad(BizTypeEnum.HOTEL_INTL.equals(travelConfig.getBizTypeEnum()))
                    .hotelBaseFilter(getHotelBaseFilter(requestParam))
                    .hotelAdvancedFilter(getHotelAdvancedFilter(requestParam, travelConfig))
                    .hotelSort(HotelSort.builder()
                            .sortType(requestParam.getSortType())
                            .sortDirection(requestParam.getSortDirection())
                            .build())
                    .supplierProductList(hotelConfigDomainService.getSupplierProductList(travelConfig, hotelOperatorTypeConfig.getGetBusinessHotelList(), requestParam.getHasHourlyRoom()))
                    .protocolPrioritySort(travelConfig.getHotelListSortRule() == 1)
                    .hotelDistanceDesc(new HotelDistanceDesc())
                    .hotelPositionFilter(new HotelPositionFilter())
                    .pageSize(requestParam.getPageSize())
                    .travelMode(travelConfig.getTravelModeEnum())
                    .build();

            // 位置信息处理
            hotelPageDomainService.handlePosition(hotelPageRequest, PositionRequest.builder()
                    .metroId(requestParam.getMetroId())
                    .districtId(requestParam.getDistrictId())
                    .zoneId(requestParam.getZoneId())
                    .cityGeoType(requestParam.getCityGeoType())
                    .landmarkId(requestParam.getLandmarkId())
                    .metroStationId(requestParam.getMetroStationId())
                    .geoName(requestParam.getGeoName())
                    .geoLat(requestParam.getLatGeo())
                    .geoLon(requestParam.getLonGeo())
                    .radius(requestParam.getRadius()).build());

            // 关键词处理
            hotelPageDomainService.handleKeyWord(hotelPageRequest, KeyWordRequest.builder()
                    .keywordId(requestParam.getKeywordId())
                    .keywordLat(requestParam.getLatKeyword())
                    .keywordLon(requestParam.getLonKeyword())
                    .keywordName(requestParam.getKeywordName())
                    .keywordType(requestParam.getKeywordType())
                    .keyword(requestParam.getKeyword()).build());

            // 获取定制枚举
            hotelCustomEnum = hotelCustomServiceFactory.getHotelCustomEnum(travelStandard);

            // 差旅控制
            List<TravelControl> travelControlList = hotelCustomServiceFactory.getTravelControlList(hotelCustomEnum, hotelPageRequest, travelStandard, travelConfig, requestParam.getTravelStandardFilter());
            if (CollectionUtils.isNotEmpty(travelControlList)
                    && travelControlList.stream()
                    .anyMatch(item -> CollectionUtils.isNotEmpty(item.getControlledList())
                            && (item.getControlledList().contains(RequestControlEnum.STAR_INTERSECTION.getCode())
                            || item.getControlledList().contains(RequestControlEnum.PRICE_INTERSECTION.getCode())))) {
                throw new CorpBusinessException(HotelResponseCodeEnum.CONTROL_NON_INTERSECTION);
            }
            if (CollectionUtils.isEmpty(travelControlList) || travelControlList.stream().allMatch(item -> CollectionUtils.isNotEmpty(item.getControlledList()))) {
                throw new CorpBusinessException(HotelResponseCodeEnum.CONTROL_TAKE_EFFECT);
            }

            // 查询
            HotelPage hotelPage = hotelCustomServiceFactory.queryHotelList(hotelCustomEnum, requestParam, travelControlList, travelConfig);
            if (hotelPage == null) {
                return null;
            }

            // 浏览过
            hotelPageDomainService.handleViewedHotel(hotelPage);

            // 构建结果
            queryHotelListRespVo = new QueryHotelListRespVo();
            queryHotelListRespVo.setHotelCardList(hotelPage.getHotelCardList().stream().map(this::convert).collect(Collectors.toList()));
            queryHotelListRespVo.setLastPage(hotelPage.getLastPage());
            return queryHotelListRespVo;
        } finally {
            // 监控
            hotelIndicatorDomainService.queryHotelList(requestParam, queryHotelListRespVo, travelConfig, hotelCustomEnum);
        }
    }

    /**
     * 校验停留时间
     * @param pageIndex 页码
     * @return 是否校验通过
     */
    private Boolean checkStayTime(Integer pageIndex) {
        if (pageIndex == null) {
            return false;
        }
        if (pageIndex == 1) {
            return Boolean.TRUE.equals(commonDomainService.setStayTime());
        }
        else {
            return StringUtils.isNotBlank(commonDomainService.getStayTime());
        }
    }

    /**
     * 获取酒店高级筛选条件
     * @param requestParam 请求参数
     * @return 酒店高级筛选条件
     */
    private HotelAdvancedFilter getHotelAdvancedFilter(QueryHotelListReqVo requestParam, TravelConfig travelConfig) {
        return HotelAdvancedFilter.builder()
                .lowPrice(requestParam.getLowPrice())
                .highPrice(requestParam.getHighPrice())
                .choiceStarList(requestParam.getStarList())
                .brandIdList(requestParam.getBrandIdList())
                .brandFeatureList(requestParam.getBrandFeatureList())
                .groupIdList(requestParam.getGroupIdList())
                .keyword(requestParam.getKeyword())
                .bedType(requestParam.getBedType())
                .hasBreakfast(requestParam.getHasBreakfast())
                .hasJustifyConfirm(requestParam.getHasJustifyConfirm())
                .hasFreeCancel(requestParam.getHasFreeCancel())
                .hasAirportShuttle(requestParam.getHasAirportShuttle())
                .hasFitnessCenter(requestParam.getHasFitnessCenter())
                .hasSwimmingPool(requestParam.getHasSwimmingPool())
                .hasParking(requestParam.getHasParking())
                .hasAirportPickup(requestParam.getHasAirportPickup())
                .hasSpa(requestParam.getHasSpa())
                .hasFreeWifi(requestParam.getHasFreeWifi())
                .hasFreeWiredBroadband(requestParam.getHasFreeWiredBroadband())
                .hasFgRoom(requestParam.getHasFgRoom())
                .hasPpRoom(requestParam.getHasPpRoom())
                .hasCompanyAccountPayment(requestParam.getHasCompanyAccountPayment())
                .applyForeignGuest(requestParam.getApplyForeignGuest())
                .applyGat(requestParam.getApplyGat())
                .hasContractRoom(requestParam.getHasContractRoom())
                .hasHotelBonusPoint(requestParam.getHasHotelBonusPoint())
                .hasFullAmountOnlinePay(requestParam.getHasFullAmountOnlinePay())
                .priceFilterIncludeExtraTax(Boolean.TRUE.equals(requestParam.getTravelStandardFilter())
                        && Boolean.TRUE.equals(travelConfig.getOverseasHotelControlIncludeExtraTax()))
                .filterWithServiceCharge(travelConfig.extractFilterWithServiceCharge())
                .hasHourlyRoom(requestParam.getHasHourlyRoom())
                .build();
    }

    /**
     * 获取酒店基础筛选
     * @param requestParam 请求参数
     * @return 酒店基础筛选
     */
    private HotelBaseFilter getHotelBaseFilter(QueryHotelListReqVo requestParam) {
        int dayNum = (int) DateUtil.between(DateUtil.parse(requestParam.getCheckInDate()), DateUtil.parse(requestParam.getCheckOutDate()), DateUnit.DAY);
        
        return HotelBaseFilter.builder().cityId(requestParam.getCityId())
                .checkInDate(requestParam.getCheckInDate())
                .checkOutDate(requestParam.getCheckOutDate())
                .roomNum(requestParam.getRoomNum())
                .personNum(requestParam.getPersonNum())
                .dayNum(dayNum)
                .roomDayNum(dayNum * requestParam.getRoomNum())
                .build();
    }

    /**
     * 获取差标
     * @param token token
     * @param travelMode 差旅模式
     * @return 差标
     */
    private TravelStandard getTravelStandard(String token, String travelMode, BizTypeEnum bizTypeEnum) {
        if (!TravelModeEnum.PUB.getCode().equals(travelMode)) {
            return null;
        }

        // 获取差标
        TravelStandard travelStandard = hotelConfigDomainService.getTravelStandard(token);
        if (travelStandard == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.TRAVEL_STANDARD_GET_ERROR);
        }

        // 出差申请单
        TravelApplication travelApplication = travelStandard.getTravelApplication();
        if (travelStandard.getTravelApplication() == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.TRAVEL_APPLICATION_GET_ERROR);
        }

        // 如果有出差申请单
        if (StringUtils.isNotBlank(travelApplication.getTravelId())) {
            // 出差申请单是否含有经纬度
            if (Boolean.TRUE.equals(hotelConfigDomainService.checkLatLonTravelApplication(travelApplication))) {
                // 多差标
                if (travelStandard.getMultiTravelStandard() == null) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.MULTI_TRAVEL_STANDARD_GET_ERROR);
                }
            }
            else {
                // 单差标
                if (travelStandard.getSignalTravelStandard() == null) {
                    throw new CorpBusinessException(HotelResponseCodeEnum.SIGNAL_TRAVEL_STANDARD_GET_ERROR);
                }
            }
        }
        else {
            // 单差标
            if (travelStandard.getSignalTravelStandard() == null) {
                throw new CorpBusinessException(HotelResponseCodeEnum.SIGNAL_TRAVEL_STANDARD_GET_ERROR);
            }
        }
        
        // 强校验：徐工定制+因公+国内酒店
        if (Boolean.TRUE.equals(commonDomainService.openFeature(ShuntEnum.XU_GONG_CUSTOM_CHECK.getCode())) && BizTypeEnum.HOTEL.equals(bizTypeEnum)) {
            // 多差标
            if (travelStandard.getMultiTravelStandard() == null) {
                throw new CorpBusinessException(HotelResponseCodeEnum.MULTI_TRAVEL_STANDARD_GET_ERROR);
            }
        }

        return travelStandard;
    }

    /**
     * 获取差旅配置
     * @param token token
     * @param travelMode 差旅模式
     * @return 差旅配置
     */
    private TravelConfig getTravelConfig(String token, String travelMode, String productType) {
        String operatorType = hotelOperatorTypeConfig.getGetBusinessHotelList();

        // 获取差旅配置
        TravelConfig travelConfig = hotelConfigDomainService.getTravelConfig(
                token,
                travelMode,
                productType,
                Collections.singletonList(operatorType));

        // 差旅配置为空
        if (travelConfig == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.TRAVEL_CONFIG_GET_ERROR);
        }

        // 差线类型为空
        BizTypeEnum bizTypeEnum = travelConfig.getBizTypeEnum();
        if (bizTypeEnum == null) {
            throw new CorpBusinessException(HotelResponseCodeEnum.BIZ_TYPE_ENUM_GET_ERROR);
        }

        // 供应商配置为空
        Map<String, List<SupplierProduct>> supplierProductListMap = travelConfig.getSupplierProductListMap();
        if (CollectionUtils.isEmpty(travelConfig.getPrioritySupplierCodeList()) || CollectionUtils.isEmpty(supplierProductListMap)
                || CollectionUtils.isEmpty(supplierProductListMap.get(operatorType))) {
            throw new CorpBusinessException(HotelResponseCodeEnum.SUPPLIER_CONFIG_GET_ERROR);
        }

        // 因私场景无需屏蔽星级
        if (!StringUtils.equalsIgnoreCase(travelMode, TravelModeEnum.PUB.getCode())) {
            travelConfig.setShieldStarList(null);
        }

        return travelConfig;
    }

    /**
     * 转换
     * @param hotelCard 酒店卡片
     * @return 酒店卡片
     */
    private QueryHotelListRespVo.HotelCard convert(HotelCard hotelCard) {
        if (hotelCard == null) {
            return null;
        }
        QueryHotelListRespVo.HotelCard result = new QueryHotelListRespVo.HotelCard();
        result.setName(hotelCard.getName());
        result.setNameEn(hotelCard.getNameEn());
        result.setAddress(hotelCard.getAddress());
        log.info("getDistanceText {}", hotelCard.getDistanceText());
        result.setDistanceText(hotelCard.getDistanceText());
        result.setLogoUrl(hotelCard.getLogoUrl());
        result.setStar(hotelCard.getStar());
        result.setStarLicence(hotelCard.getStarLicence());
        result.setLevelName(hotelCard.getLevelName());
        result.setReviewScore(hotelCard.getReviewScore());
        result.setProtocolType(hotelCard.getProtocolType());
        result.setLat(hotelCard.getLat());
        result.setLon(hotelCard.getLon());
        AgreementInfoQueryResp content = getContent();
        if (hotelCard.getProtocolType()!=null&&content!=null){
            if(TWO_PARTY_CONTRACT.equals(hotelCard.getProtocolType())) {
                if (StringUtils.isNotBlank(content.getTwoContent())&&content.getTwoSwitch().equals(AGREEMENT_TAG_ENABLE)){
                    result.setCorpBaShortName(content.getTwoContent());
                }
            }else if (THREE_PARTY_CONTRACT.equals(hotelCard.getProtocolType())){
                if (StringUtils.isNotBlank(content.getThreeContent())&&content.getThreeSwitch().equals(AGREEMENT_TAG_ENABLE)){
                    result.setCorpShortName(content.getThreeContent());
                }
            }
        }
        log.info("getCorpShortName {}", result.getCorpShortName());
        result.setFacilityList(hotelCard.getFacilityList());
        result.setViewed(hotelCard.getViewed());
        result.setTravelStandardMark(hotelCard.getTravelStandardMark());
        HotelPrice minHotelPrice = hotelCard.getMinHotelPrice();
        if (minHotelPrice != null) {
            Price avgPriceIncludeTax = minHotelPrice.getAvgPriceIncludeTax();
            if (Boolean.TRUE.equals(Price.checkCustomPrice(avgPriceIncludeTax))) {
                result.setMinSalePriceIncludeTax(avgPriceIncludeTax.getCustomPrice());
            }
            Price avgExtraTax = minHotelPrice.getAvgExtraTax();
            if (Boolean.TRUE.equals(Price.checkCustomPrice(avgExtraTax))) {
                result.setExtraTax(avgExtraTax.getCustomPrice());
            }
            Price avgServiceCharge = minHotelPrice.getAvgServiceCharge();
            if (Boolean.TRUE.equals(Price.checkCustomPrice(avgServiceCharge))) {
                result.setAvgServiceCharge(avgServiceCharge.getCustomPrice());
            }
            if (Boolean.TRUE.equals(Price.checkCustomPrice(minHotelPrice.getTotalServiceCharge()))) {
                result.setTotalServiceCharge(minHotelPrice.getTotalServiceCharge().getCustomPrice());
            }
            Price avgPriceIncludeTaxAndServiceCharge = minHotelPrice.getAvgPriceIncludeTaxAndServiceCharge();
            if (Boolean.TRUE.equals(Price.checkCustomPrice(avgPriceIncludeTaxAndServiceCharge))) {
                result.setMinPriceIncludeTaxAndServiceCharge(avgPriceIncludeTaxAndServiceCharge.getCustomPrice());
            }
            result.setResourcePriceIncludeServiceCharge(minHotelPrice.getResourcePriceIncludeServiceCharge());
        }
        result.setSupplierNum(hotelCard.getSupplierList().stream().map(HotelSupplier::getSupplierCode).collect(Collectors.toSet()).size());
        result.setSupplierList(hotelCard.getSupplierList().stream().filter(Objects::nonNull).map(a -> {
            QueryHotelListRespVo.HotelSupplier hotelSupplier = new QueryHotelListRespVo.HotelSupplier();
            hotelSupplier.setHotelId(a.getHotelId());
            hotelSupplier.setSupplierCode(a.getSupplierCode());
            hotelSupplier.setSupplierName(a.getSupplierName());
            HotelPrice tmpMinPrice = a.getMinHotelPrice();
            if (tmpMinPrice != null) {
                if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(tmpMinPrice.getAvgPriceIncludeTax()))) {
                    hotelSupplier.setMinSalePriceIncludeTax(tmpMinPrice.getAvgPriceIncludeTax().getCustomPrice());
                }
                if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(tmpMinPrice.getAvgExtraTax()))) {
                    hotelSupplier.setExtraTax(tmpMinPrice.getAvgExtraTax().getCustomPrice());
                }
                if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(tmpMinPrice.getAvgServiceCharge()))) {
                    hotelSupplier.setAvgServiceCharge(tmpMinPrice.getAvgServiceCharge().getCustomPrice());
                }
                if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(tmpMinPrice.getTotalServiceCharge()))) {
                    hotelSupplier.setTotalServiceCharge(tmpMinPrice.getTotalServiceCharge().getCustomPrice());
                }
                if (Boolean.TRUE.equals(commonDomainService.checkCustomPrice(tmpMinPrice.getAvgPriceIncludeTaxAndServiceCharge()))) {
                    hotelSupplier.setMinPriceIncludeTaxAndServiceCharge(tmpMinPrice.getAvgPriceIncludeTaxAndServiceCharge().getCustomPrice());
                }
                hotelSupplier.setResourcePriceIncludeServiceCharge(tmpMinPrice.getResourcePriceIncludeServiceCharge());
            }
            return hotelSupplier;
        }).collect(Collectors.toList()));
        result.setAllHotelTagList(buildHotelTagList(hotelCard.getAllHotelTagList()));
        result.setUnavailableReason("canBooking");
        return result;
    }

    private List<MemberTag> buildHotelTagList(List<MemberTagInfo> hotelTagInfoList) {
        if (CollectionUtils.isEmpty(hotelTagInfoList)) {
            return Collections.emptyList();
        }
        return hotelTagInfoList.stream().map(tagInfo -> {
            MemberTag tag = new MemberTag();
            tag.setTagName(tagInfo.getTagName());
            tag.setTagDesc(tagInfo.getTagDesc());
            tag.setTagCode(tagInfo.getTagCode());
            return tag;
        }).collect(Collectors.toList());


    }
    /**
     * 获取协议标签
     */
    public AgreementInfoQueryResp getContent() {
        try {
            JSONResult<AgreementInfoQueryResp> agreementInfo = contentDicitionaryClient.getAgreementInfo();
            if(agreementInfo==null||agreementInfo.getData()==null){
                return null;
            }
            return agreementInfo.getData();
        } catch (Exception exception) {
            log.error("获取协议标签", exception);
            return null;
        }
    }
}
