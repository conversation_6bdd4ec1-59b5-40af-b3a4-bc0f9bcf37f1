package com.corpgovernment.core.service;

import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.core.controller.vo.CheckHotelCarRespVo;
import com.corpgovernment.core.controller.vo.CheckHotelCardReqVo;
import com.corpgovernment.core.controller.vo.CreateOrUpdateHotelCardReqVo;
import com.corpgovernment.core.controller.vo.GetUserHotelCardReqVo;
import com.corpgovernment.core.controller.vo.GetUserHotelCardRespVo;
import com.corpgovernment.core.dao.dataobject.HotelBonusPointInfoDo;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
public interface IHotelBonusPointService {

    String CARD_NUM_REGEX= "^[a-zA-Z0-9]{1,30}$";

    String MOBILE_PHONE_REGEX = "(\\d{3})\\*{4}(\\d{4})";

    String NO_PASS_DESC_HUA_ZHU = "会员积分需要保证联系人手机号与入住人手机号一致，请更换联系人手机号";

    HotelBonusPointInfoDo getHotelBonusPointInfo(String supplierCode, String groupId);

    HotelBonusPointInfoDo getHotelBonusPointInfo(String supplierCode, String groupId, String bonusPointCode);

    GetUserHotelCardRespVo getUserHotelCard(GetUserHotelCardReqVo requestParam);

    void createOrUpdateHotelCard(CreateOrUpdateHotelCardReqVo requestParam);

    CheckHotelCarRespVo checkHotelCard(CheckHotelCardReqVo checkHotelCardReqVo, BaseUserInfo baseUserInfo);

}
