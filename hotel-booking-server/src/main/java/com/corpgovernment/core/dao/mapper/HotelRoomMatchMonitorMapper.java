package com.corpgovernment.core.dao.mapper;

import com.corpgovernment.core.dao.entity.db.HotelMatchMonitorDo;
import com.corpgovernment.core.dao.entity.db.HotelRoomMatchMonitorDo;
import com.corpgovernment.hotel.product.common.mybatis.TkMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.shard.annotation.ShardModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
public interface HotelRoomMatchMonitorMapper extends TkMapper<HotelRoomMatchMonitorDo> {

    @ShardModel(targetDataSource = "apollo_common")
    default void batchInsertOrUpdate(List<HotelRoomMatchMonitorDo> hotelRoomMatchMonitorDoList) {
        if (CollectionUtils.isEmpty(hotelRoomMatchMonitorDoList)) {
            return;
        }
        batchInsertOrUpdateSql(hotelRoomMatchMonitorDoList);
    }

    void batchInsertOrUpdateSql(@Param("list") List<HotelRoomMatchMonitorDo> hotelRoomMatchMonitorDoList);

}
