package com.corpgovernment.core.dao.entity.db;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @description
 * @create 2025-03-27 21:13
 */
@Data
@Table(name = "ms_manual_hotel_relation")
public class ManualHotelRelationDo {
    
    @Id
    private Long id;
    
    private String masterSupplierCode;
    
    private String masterHotelId;
    
    private String subSupplierCode;
    
    private String subHotelId;
    
    private Boolean matchStatus;
    
}
