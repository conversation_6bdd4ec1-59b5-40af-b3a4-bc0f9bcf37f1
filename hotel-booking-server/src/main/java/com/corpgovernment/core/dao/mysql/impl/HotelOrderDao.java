package com.corpgovernment.core.dao.mysql.impl;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.enums.OrderTypeEnum;
import com.corpgovernment.core.dao.entity.db.HotelOrderDo;
import com.corpgovernment.core.dao.mapper.HotelOrderMapper;
import com.corpgovernment.core.dao.mysql.IHotelOrderDao;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @create 2024-08-31 15:23
 */
@Repository
@Slf4j
public class HotelOrderDao implements IHotelOrderDao {
    
    @Resource
    private HotelOrderMapper hotelOrderMapper;
    
    @Override
    @BusinessBehaviorMonitor
    public HotelOrderDo getHotelOrderDo(String orderId, OrderTypeEnum orderTypeEnum) {
        if (orderTypeEnum == null || StringUtils.isBlank(orderId)) {
            return null;
        }
        
        if (orderTypeEnum == OrderTypeEnum.HN) {
            return hotelOrderMapper.listHotelOrderDoFromIn(orderId);
        } else if (orderTypeEnum == OrderTypeEnum.HI) {
            return hotelOrderMapper.listHotelOrderDoFromOut(orderId);
        }
        
        return null;
    }
    
}
