package com.corpgovernment.core.dao.mysql;

import com.corpgovernment.core.dao.entity.db.HotelRelationDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
public interface IHotelRelationDao {

    List<HotelRelationDo> listForwardMatchedRelation(String masterSupplierCode, List<String> masterHotelIdList);

    List<HotelRelationDo> listBackwardMatchedRelation(String subSupplierCode, List<String> subHotelIdList);

}
