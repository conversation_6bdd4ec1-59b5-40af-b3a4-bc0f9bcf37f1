package com.corpgovernment.core.dao.rpc.impl;

import com.corpgovernment.api.organization.dto.EmployeeInfoVo;
import com.corpgovernment.api.organization.dto.request.EmployeeByUidRequestVo;
import com.corpgovernment.api.organization.model.employee.EmployeeInfoBo;
import com.corpgovernment.api.organization.soa.IEmployeeClient;
import com.corpgovernment.api.organization.soa.IOrganizationEmployeeClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.core.dao.rpc.IHotelCoreRpcDao;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
@Repository
@Slf4j
public class HotelCoreRpcDao implements IHotelCoreRpcDao {

    @Resource(name = "organizationEmployeeService")
    private IOrganizationEmployeeClient organizationEmployeeClient;
    @Resource(name = "employeeService")
    private IEmployeeClient employeeClient;

    public EmployeeInfoBo getEmployeeInfoByUid(String uid) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        JSONResult<EmployeeInfoBo> result;
        try {
            result = organizationEmployeeClient.findByUid(uid);
        } catch (Exception e) {
            log.error("getEmployeeInfoByUidList 远程调用失败", e);
            return null;
        }
        if (result == null || !result.isSUCCESS()) {
            log.error("getEmployeeInfoByUid 远程调用返回异常 result={}", result);
            return null;
        }
        return result.getData();
    }

    public List<EmployeeInfoVo> getEmpOrNonEmpByUidAndType(List<EmployeeByUidRequestVo> requestParam) {
        if (CollectionUtils.isEmpty(requestParam)) {
            return new ArrayList<>(0);
        }
        JSONResult<List<EmployeeInfoVo>> result;
        try {
            result = employeeClient.getEmployeeInfoByUidOrNonEmpId(requestParam);
        } catch (Exception e) {
            log.error("getEmployeeInfoByUidList 远程调用失败", e);
            return null;
        }
        if (result == null || !result.isSUCCESS()) {
            log.error("getEmployeeInfoByUidList 远程调用返回异常 result={}", result);
            return null;
        }
        return result.getData();
    }

}
