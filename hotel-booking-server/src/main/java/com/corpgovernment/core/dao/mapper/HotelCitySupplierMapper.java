package com.corpgovernment.core.dao.mapper;

import com.corpgovernment.core.dao.entity.db.HotelCitySupplierDo;
import com.corpgovernment.hotel.product.common.mybatis.TkMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/18
 */
public interface HotelCitySupplierMapper extends TkMapper<HotelCitySupplierDo> {

    default HotelCitySupplierDo get(String cityId, String supplierCode) {
        if (StringUtils.isBlank(cityId) || StringUtils.isBlank(supplierCode)) {
            return null;
        }
        Example example = new Example(HotelCitySupplierDo.class);
        example.createCriteria().andEqualTo("cityId", cityId)
                .andEqualTo("supplierCode", supplierCode);
        List<HotelCitySupplierDo> hotelCitySupplierDoList = selectByExample(example);
        return CollectionUtils.isEmpty(hotelCitySupplierDoList) ? null : hotelCitySupplierDoList.get(0);
    }

}
