package com.corpgovernment.core.dao.entity.db;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/22
 */
@Data
@Table(name = "ms_hotel_relation")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelRelationDo {

    @Id
    private Long id;

    private String masterSupplierCode;

    private String masterHotelId;

    private String subSupplierCode;

    private String subHotelId;

    private Boolean matchStatus;

}
