package com.corpgovernment.core.dao.mapper;

import com.corpgovernment.core.dao.entity.db.HotelCityDo;
import com.corpgovernment.hotel.product.common.mybatis.TkMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */

public interface HotelCityMapper extends TkMapper<HotelCityDo> {

    default HotelCityDo getHotelCityDoByCityId(String cityId) {
        if (StringUtils.isBlank(cityId)) {
            return null;
        }
        Example example = new Example(HotelCityDo.class);
        example.createCriteria().andEqualTo("cityId", cityId);
        List<HotelCityDo> hotelCityDoList = selectByExample(example);
        return CollectionUtils.isEmpty(hotelCityDoList) ? null : hotelCityDoList.get(0);
    }

}
