package com.corpgovernment.core.dao.mysql.impl;

import com.corpgovernment.core.dao.entity.db.UserHotelCardRelationDo;
import com.corpgovernment.core.dao.mapper.UserHotelCardRelationMapper;
import com.corpgovernment.core.dao.mysql.IUserHotelCardRelationDao;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 */
@Repository
public class UserHotelCardRelationDao implements IUserHotelCardRelationDao {

    @Resource
    private UserHotelCardRelationMapper userHotelCardRelationMapper;

    @Override
    public UserHotelCardRelationDo get(String masterGroupId, String employeeType, String uid) {
        if (StringUtils.isBlank(masterGroupId) || StringUtils.isBlank(employeeType) || StringUtils.isBlank(uid)) {
            return null;
        }
        Example example = new Example(UserHotelCardRelationDo.class);
        example.createCriteria().andEqualTo("masterGroupId", masterGroupId).andEqualTo("employeeType", employeeType).andEqualTo("uid", uid).andEqualTo("deleted", 0);
        List<UserHotelCardRelationDo> result = userHotelCardRelationMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }

    @Override
    public void create(UserHotelCardRelationDo userHotelCardRelationDo) {
        if (userHotelCardRelationDo == null) {
            return;
        }
        userHotelCardRelationMapper.insertSelective(userHotelCardRelationDo);
    }

    @Override
    public void update(UserHotelCardRelationDo userHotelCardRelationDo) {
        if (userHotelCardRelationDo == null || userHotelCardRelationDo.getId() == null) {
            return;
        }
        userHotelCardRelationMapper.updateByPrimaryKeySelective(userHotelCardRelationDo);
    }

}
