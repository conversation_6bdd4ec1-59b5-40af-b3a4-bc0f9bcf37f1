package com.corpgovernment.core.dao.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/12/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryTimeZoneReqDto {

    private String keyword;
    private String areaType;
    private Auth auth;

    @Data
    public static class Auth {
        private String appKey;
        private String ticket;
    }

    public QueryTimeZoneReqDto(String keyword, String areaType, String appKey, String ticket) {
        this.keyword = keyword;
        this.areaType = areaType;
        this.auth = new Auth();
        this.auth.appKey = appKey;
        this.auth.ticket = ticket;
    }

}
