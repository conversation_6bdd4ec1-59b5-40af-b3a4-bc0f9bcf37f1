package com.corpgovernment.core.dao.openfeign;

import com.corpgovernment.api.basic.request.BasicCityListRequest;
import com.corpgovernment.api.basic.request.CorpBrandByBrandIdRequest;
import com.corpgovernment.api.basic.response.BasicGeographyInfoResponse;
import com.corpgovernment.api.basic.response.BasicIntegratedCityResponse;

public interface IBasicDataClientOpenFeignDao {
    /**
     * 酒店品牌查询
     * @param request brandIds 品牌ID   type 数据完整度，参考枚举BasicCorpBrandTypeEnum（不传默认全部数据）
     * @return 品牌数据
     */
    BasicGeographyInfoResponse searchCorpBrandByBrandId(CorpBrandByBrandIdRequest request);

    /**
     * 根据城市id集合获取城市数据（新）
     * @param request
     * @return
     */
    BasicIntegratedCityResponse listBasicCityInfoByIds(BasicCityListRequest request);

}
