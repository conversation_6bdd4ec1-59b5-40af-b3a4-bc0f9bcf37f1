package com.corpgovernment.core.dao.mysql.impl;

import com.corpgovernment.core.dao.entity.db.ManualHotelRelationDo;
import com.corpgovernment.core.dao.mapper.HotelRelationMapper;
import com.corpgovernment.core.dao.mysql.IHotelRelationDao;
import com.corpgovernment.core.dao.entity.db.HotelRelationDo;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.shard.annotation.ShardModel;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
@Repository
public class HotelRelationDao implements IHotelRelationDao {

    @Resource
    private HotelRelationMapper hotelRelationMapper;

    @Override
    @ShardModel(targetDataSource = "apollo_common")
    public List<HotelRelationDo> listForwardMatchedRelation(String masterSupplierCode, List<String> masterHotelIdList) {
        if (StringUtils.isBlank(masterSupplierCode) || CollectionUtils.isEmpty(masterHotelIdList)) {
            return new ArrayList<>(0);
        }
        Example example = new Example(HotelRelationDo.class);
        example.createCriteria().andEqualTo("masterSupplierCode", masterSupplierCode)
                .andIn("masterHotelId", masterHotelIdList)
                .andEqualTo("matchStatus", true);
        return hotelRelationMapper.selectByExample(example);
    }

    @Override
    @ShardModel(targetDataSource = "apollo_common")
    public List<HotelRelationDo> listBackwardMatchedRelation(String subSupplierCode, List<String> subHotelIdList) {
        if (StringUtils.isBlank(subSupplierCode) || CollectionUtils.isEmpty(subHotelIdList)) {
            return new ArrayList<>(0);
        }
        Example example = new Example(HotelRelationDo.class);
        example.createCriteria().andEqualTo("subSupplierCode", subSupplierCode)
                .andIn("subHotelId", subHotelIdList)
                .andEqualTo("matchStatus", true);
        return hotelRelationMapper.selectByExample(example);
    }
}
