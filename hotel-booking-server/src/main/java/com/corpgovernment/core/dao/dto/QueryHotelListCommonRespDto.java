package com.corpgovernment.core.dao.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
public class QueryHotelListCommonRespDto {

    private List<HotelInfo> hotelInfo;
    private SearchResult searchResult;
    private Status status;
    private String errorCode;
    private String message;

    @Data
    public static class HotelBaseInfo {
        private String hotelId;
        private String hotelName;
        private String hotelEnName;
        private String hotelAddress;
        private String hotelLogoUrl;
        private Integer hotelStar;
        private Boolean starLicence;
        private HotelBrandInfo hotelBrandInfo;
    }
    
    @Data
    public static class HotelBrandInfo {
        
        private String groupId;
        
    }

    @Data
    public static class HotelStaticInfo {
        private HotelGeoInfo hotelGeoInfo;
        private HotelReviewInfo hotelReviewInfo;
        private HotelFacilitiesInfo hotelFacilitiesInfo;
    }

    @Data
    public static class HotelFacilitiesInfo {
        private List<FacilityInfo> facilityInfoList;
    }

    @Data
    public static class FacilityInfo {
        private String facilityType;
        private String facilityName;
    }

    @Data
    public static class HotelGeoInfo {
        private List<HotelMapInfo> hotelMapInfo;
        private IdName districtInfo;
        private CityInfo cityInfo;
        private ProvinceInfo provinceInfo;
        private CountryInfo countryInfo;
        private List<IdName> zoneInfoList;
        private Double landMarkDistance;
    }

    @Data
    public static class HotelMapInfo {
        private Double lat;
        private Double lon;
        private String mapType;
    }

    @Data
    public static class IdName {
        private String id;
        private String name;
    }

    @Data
    public static class CityInfo {
        private String id;
        private String name;
        private List<IdName> parentCityList;
        private List<IdName> childCityList;
    }

    @Data
    public static class ProvinceInfo {
        private String id;
        private String name;
    }

    @Data
    public static class CountryInfo {
        private String id;
        private String name;
    }

    @Data
    public static class HotelReviewInfo {
        private String hotelReviewScore;
        private String totalNumberOfHotelReviews;
    }

    @Data
    public static class HotelInfo {
        private HotelBaseInfo hotelBaseInfo;
        private HotelStaticInfo hotelStaticInfo;
        private List<HotelTagInfo> hotelTagInfo;
        private MinPriceRoomInfo minPriceRoomInfo;
        private Boolean hasContractRoom;
        private String unAvailableReason;
    }

    @Data
    public static class HotelTagInfo {
        private String hotelTagType;
        private List<HotelTag> hotelTagList;
    }

    @Data
    public static class HotelTag {
        private String tagCode;
        private String name;
        private String desc;
    }

    @Data
    public static class MinPriceRoomInfo {
        private String saleRoomId;
        private Boolean canReserve;
        private MinPriceInfo minPriceInfo;
        private PromotionsPriceInfo promotionsPriceInfo;
        private BigDecimal minSalePrice;
        private BigDecimal minSalePriceIncludeTax;
        private List<TaxInfo> taxInfoList;
        private ServiceChargeInfo serviceChargeInfo;

        @Data
        public static class MinPriceInfo {
            private PriceInfo avgPriceExcludeTax;
            private PriceInfo avgPriceIncludeTax;
        }

        @Data
        public static class PromotionsPriceInfo {
            private PriceInfo avgDiscountedPrice;
            private BigDecimal minSalePrice;
            private BigDecimal minSalePriceIncludeTax;
        }

        @Data
        public static class PriceInfo {
            private OriginPriceInfo originPriceInfo;
            private BigDecimal customPrice;

            @Data
            public static class OriginPriceInfo {
                private String currency;
                private BigDecimal price;
            }

        }

        @Data
        public static class ServiceChargeInfo {
            private BigDecimal customChargePrice;
            private BigDecimal customChargePricePerRoomNights;
        }

        @Data
        public static class TaxInfo {
            private PriceInfo taxPrice;
            private String taxType;
            private Boolean includeInTotalPrice;
        }

    }

    @Data
    public static class SearchResult {
        private Boolean firstPage;
        private Boolean lastPage;
        private Integer hotelCount;
    }

    @Data
    public static class Status {
        private Boolean success;
        private String errorCode;
        private String errorMessage;
    }

}
