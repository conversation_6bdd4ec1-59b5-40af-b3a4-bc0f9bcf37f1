package com.corpgovernment.core.dao.clickhouse.impl;

import cn.hutool.core.util.StrUtil;
import com.corpgovernment.common.businessmonitor.model.BusinessBehavior;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.dao.apollo.impl.HotelCoreApolloDao;
import com.corpgovernment.core.dao.clickhouse.IHotelCoreClickHouseDao;
import com.corpgovernment.core.dao.entity.db.TagPoolDo;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
@Repository
@Slf4j
public class HotelCoreClickHouseDao implements IHotelCoreClickHouseDao {

    @Resource
    private HotelCoreApolloDao hotelCoreApolloDao;

    @Override
    public void batchCreateTag(List<TagPoolDo> tagPoolDoList) {
        if (CollectionUtils.isEmpty(tagPoolDoList)) {
            return;
        }
        StringBuilder sql = new StringBuilder("insert into tag_pool(uid, request_id, tenant_id, create_time, supplier_code, tag_code, url, request_body, response_body, duration, page_index, simple_desc, service_name, call_mode) values ");
        for (TagPoolDo item : tagPoolDoList) {
            sql.append(StrUtil.format("('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}'),", item.getUid(), item.getRequestId(), item.getTenantId(), item.getCreatTime(), item.getSupplierCode(), item.getTagCode(), item.getUrl(), Null.or(item.getRequestBody(), "").replace("'", ""), Null.or(item.getResponseBody(), "").replace("'", ""), item.getDuration(), item.getPageIndex(), item.getSimpleDesc(), item.getServiceName(), item.getCallMode()));
        }
        sql.deleteCharAt(sql.length() - 1);
        try (Connection connection = DriverManager.getConnection(hotelCoreApolloDao.getClickhouseUrl(), hotelCoreApolloDao.getClickhouseUsername(), hotelCoreApolloDao.getClickhousePassword())) {
            try (Statement statement = connection.createStatement()) {
                String str = sql.toString();
                statement.execute(str);
            }
        } catch (Exception e) {
            log.error("clickhouse操作失败", e);
        }
    }
    
    @Override
    public List<BusinessBehavior> queryData(String sql, String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return null;
        }
        
        if (StringUtils.isNotBlank(startTime)) {
            sql += StrUtil.format(" and timestamp >= '{}'", startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            sql += StrUtil.format(" and timestamp <= '{}'", endTime);
        }
        
        List<BusinessBehavior> businessBehaviorList = new ArrayList<>();
        try (Connection connection = DriverManager.getConnection(hotelCoreApolloDao.getClickhouseUrl(), hotelCoreApolloDao.getClickhouseUsername(), hotelCoreApolloDao.getClickhousePassword())) {
            try (Statement stmt = connection.createStatement()) {
                try (ResultSet rs = stmt.executeQuery(sql)) {
                    while (rs.next()) {
                        Gson gson = new Gson();
                        businessBehaviorList.add(BusinessBehavior.builder()
                                .requestId(rs.getString("request_id"))
                                .tenantId(rs.getString("tenant_id"))
                                .corpId(rs.getString("corp_id"))
                                .orgId(rs.getString("org_id"))
                                .uid(rs.getString("uid"))
                                .serviceName(rs.getString("service_name"))
                                .className(rs.getString("class_name"))
                                .methodName(rs.getString("method_name"))
                                .requestParam(gson.fromJson(rs.getString("request_param"), Map.class))
                                .responseParam(gson.fromJson(rs.getString("response_param"), Map.class))
                                .duration(rs.getLong("duration"))
                                .extraData(gson.fromJson(rs.getString("extra_data"), Map.class))
                                .startTime(rs.getString("start_time"))
                                .endTime(rs.getString("end_time"))
                                .timestamp(rs.getString("timestamp")).build());
                    }
                }
            }
        } catch (Exception e) {
            log.error("clickhouse操作失败", e);
            return null;
        }
        return businessBehaviorList;
    }
    
    @Override
    public Long queryCount(String sql, String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return null;
        }
        
        if (StringUtils.isNotBlank(startTime)) {
            sql += StrUtil.format(" and timestamp >= '{}'", startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            sql += StrUtil.format(" and timestamp <= '{}'", endTime);
        }
        
        List<BusinessBehavior> businessBehaviorList = new ArrayList<>();
        try (Connection connection = DriverManager.getConnection(hotelCoreApolloDao.getClickhouseUrl(), hotelCoreApolloDao.getClickhouseUsername(), hotelCoreApolloDao.getClickhousePassword())) {
            try (Statement stmt = connection.createStatement()) {
                try (ResultSet rs = stmt.executeQuery(sql)) {
                    while (rs.next()) {
                        return rs.getLong(1);
                    }
                }
            }
        } catch (Exception e) {
            log.error("clickhouse操作失败", e);
            return null;
        }
        return null;
    }
    
}
