package com.corpgovernment.core.dao.entity.db;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "bd_hp_hotel_city")
public class HotelCityDo {

    @Id
/**
 * 自增主键
 */
    private Long id;

    /**
     * 城市ID
     */
    private String cityId;

    /**
     * 城市三字码
     */
    private String cityCode;

    /**
     * 城市编码
     */
    private String cityMailCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市英文名称
     */
    private String cityEnName;

    /**
     * 国家ID
     */
    private String countryId;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 国家英文名称
     */
    private String countryEnName;

    /**
     * 省份ID
     */
    private String provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 省英文名
     */
    private String provinceEnName;

    /**
     * 省编码
     */
    private String provinceMailCode;

    /**
     * 洲ID
     */
    private String continentId;

    /**
     * 洲名称
     */
    private String continentName;

    /**
     * 城市拼音首字母
     */
    private String firstChar;

    /**
     * 城市简拼
     */
    private String jianPin;

    /**
     * 城市中心点纬度
     */
    private String centerLat;

    /**
     * 城市中心点经度
     */
    private String centerLon;

    /**
     * 创建时间
     */
    private Date datachangeCreatetime;

    /**
     * 修改时间
     */
    private Date datachangeLasttime;

    /**
     * 省份拼音
     */
    private String provincePinyin;

    /**
     * 县级市标志位，true为县级市/县，否则不是
     */
    private Integer countryCityFlag;

    /**
     * 父级城市id，只有县级市/县有父级城市
     */
    private String parentCityId;

    /**
     * 城市拼音
     */
    private String cityPinyin;

}
