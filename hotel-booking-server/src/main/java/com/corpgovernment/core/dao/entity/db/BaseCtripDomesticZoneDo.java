package com.corpgovernment.core.dao.entity.db;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Table(name = "ms_base_ctrip_domestic_zone")
public class BaseCtripDomesticZoneDo {

    @Id
    private Long id;
    private String zoneId;
    private String zoneName;
    private String cityId;
    private Double gdLat;
    private Double gdLon;
    private Boolean isDeleted;

}
