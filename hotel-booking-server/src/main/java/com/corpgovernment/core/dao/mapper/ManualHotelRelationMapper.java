package com.corpgovernment.core.dao.mapper;

import com.corpgovernment.core.dao.entity.db.ManualHotelRelationDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025-03-27 21:16
 */
@Mapper
public interface ManualHotelRelationMapper {
    
    List<ManualHotelRelationDo> queryCtripManualHotelRelations(@Param("ctripHotelIds") List<String> ctripHotelIds);
    
    List<ManualHotelRelationDo> queryNonCtripManualHotelRelations(@Param("supplierCode") String supplierCode,
                                                                  @Param("hotelIds") List<String> hotelIds);
    
}
