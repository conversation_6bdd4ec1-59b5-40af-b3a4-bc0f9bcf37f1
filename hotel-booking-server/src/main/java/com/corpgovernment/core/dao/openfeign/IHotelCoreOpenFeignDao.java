package com.corpgovernment.core.dao.openfeign;

import com.corpgovernment.api.basic.request.BasicCityInfoRequest;
import com.corpgovernment.api.basic.response.BasicCityInfoResponse;
import com.corpgovernment.api.supplier.vo.request.ListSupplierUrlReqVo;
import com.corpgovernment.api.supplier.vo.response.ListSupplierUrlRespVo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.dto.config.response.GetBookingConfigByTokenResponse;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelFeeSnapshotResponse;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelProductSnapshotResponse;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 */
public interface IHotelCoreOpenFeignDao {

    GetBookingConfigByTokenResponse getBookingConfig(String token);

    List<ListSupplierUrlRespVo.SupplierUrl> getSupplierUrlList(String productType, List<ListSupplierUrlReqVo.SupplierKey> supplierKeyList);

    List<ResourcesVerifyResponse> verifyTravelStandard(String token, String bizType, List<HotelVerifyRequest> hotelVerifyRequestList);

    List<TravelStandardResponse> getTravelStandardByToken(String token);

    QuerySnapshotResponseDTO getSnapshot(String token, List<String> dataTypeList);
    
    GetHotelFeeSnapshotResponse getHotelFeeSnapshot(String token);
    
    GetHotelProductSnapshotResponse getHotelProductSnapshot(String token, String supplierCode, String hotelId, String roomId, String paymentType);
    
    JSONResult<BasicCityInfoResponse> getCityInfoByIdOrName(@RequestBody BasicCityInfoRequest request);

}
