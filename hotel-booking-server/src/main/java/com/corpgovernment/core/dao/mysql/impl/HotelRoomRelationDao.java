package com.corpgovernment.core.dao.mysql.impl;

import com.corpgovernment.core.dao.mysql.IHotelRoomRelationDao;
import com.corpgovernment.hotel.product.entity.db.HotelRoomRelationDo;
import com.corpgovernment.hotel.product.mapper.HotelRoomRelationMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.shard.annotation.ShardModel;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Repository
public class HotelRoomRelationDao implements IHotelRoomRelationDao {

    @Resource
    private HotelRoomRelationMapper hotelRoomRelationMapper;

    @Override
    @ShardModel(targetDataSource = "apollo_common")
    public List<HotelRoomRelationDo> listForwardRoomRelation(String supplierCode, List<String> otherSupplierCodeList, List<String> hotelIdList) {
        if (StringUtils.isBlank(supplierCode) || CollectionUtils.isEmpty(hotelIdList) || CollectionUtils.isEmpty(otherSupplierCodeList)) {
            return new ArrayList<>(0);
        }
        Example example = new Example(HotelRoomRelationDo.class);
        example.createCriteria().andEqualTo("masterSupplierCode", supplierCode)
                .andIn("masterHotelId", hotelIdList)
                .andIn("subSupplierCode", otherSupplierCodeList)
                .andEqualTo("matchStatus", true);
        return hotelRoomRelationMapper.selectByExample(example);
    }

    @Override
    @ShardModel(targetDataSource = "apollo_common")
    public List<HotelRoomRelationDo> listBackwardRoomRelation(String supplierCode, List<String> hotelIdList) {
        if (StringUtils.isBlank(supplierCode) || CollectionUtils.isEmpty(hotelIdList)) {
            return new ArrayList<>(0);
        }
        Example example = new Example(HotelRoomRelationDo.class);
        example.createCriteria().andEqualTo("subSupplierCode", supplierCode)
                .andIn("subHotelId", hotelIdList)
                .andEqualTo("matchStatus", true);
        return hotelRoomRelationMapper.selectByExample(example);
    }
}
