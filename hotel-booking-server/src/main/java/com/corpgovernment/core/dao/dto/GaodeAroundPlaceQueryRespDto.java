package com.corpgovernment.core.dao.dto;

import com.corpgovernment.hotel.product.model.gaode.AroundPlaceRespDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-10-08 13:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GaodeAroundPlaceQueryRespDto {
    
    private String info;
    private String infoCode;
    private List<AroundPlaceRespDto.Poi> pois;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Poi {
        // 名称
        private String name;
        // 经纬度
        private String location;
        // 省份
        private String pName;
        // 城市
        private String cityName;
        // 区县
        private String adName;
    }
    
}
