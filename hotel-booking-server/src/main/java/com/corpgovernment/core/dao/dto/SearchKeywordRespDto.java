package com.corpgovernment.core.dao.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
@Data
public class SearchKeywordRespDto {

    private Status status;
    private List<DestinationInfo> destinationInfoList;
    private List<OtherCityDestinationInfo> otherCityDestinationList;

    @Data
    public static class DestinationInfo {
        private String keywordName;
        private String destinationId;
        private String destinationType;
        private String cityId;
        private String cityName;
        private CoordinateInfo coordinateInfo;
    }

    @Data
    public static class OtherCityDestinationInfo {
        private String destinationName;
        private String destinationId;
        private String destinationType;
        private String cityId;
        private String cityName;
        private CoordinateInfo coordinateInfo;
        private String provinceId;
        private String provinceName;
        private String countryId;
        private String countryName;
    }

    @Data
    public static class CoordinateInfo {
        private Double gdLat;
        private Double gdLon;
    }

    @Data
    public static class Status {
        private Boolean success;
        private String errorCode;
        private String errorMessage;
    }

}
