package com.corpgovernment.core.dao.entity.db;

import com.corpgovernment.core.dao.dto.QueryTimeZoneRespDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "ho_city_time_zone")
public class CityTimeZoneDo {

    @Id
    private Long id;
    private String cityId;
    private String cityName;
    private String provinceName;
    private String countryName;
    private String timeZone;
    private Boolean deleted;

    public CityTimeZoneDo(String cityId, String cityName) {
        this.cityId = cityId;
        this.cityName = cityName;
        this.provinceName = "";
        this.countryName = "";
        this.timeZone = "";
    }

    public CityTimeZoneDo(String cityId, String cityName, QueryTimeZoneRespDto queryTimeZoneRespDto) {
        this.cityId = cityId;
        this.cityName = cityName;
        Optional<QueryTimeZoneRespDto.PositionInfo> positionInfo = Optional.ofNullable(queryTimeZoneRespDto).map(QueryTimeZoneRespDto::getData).orElse(Collections.emptyList()).stream()
                .filter(item -> Arrays.asList(4, 5).contains(item.getType()) && item.getName().contains(cityName) && StringUtils.isNotBlank(item.getTimeZone())).findFirst();
        this.provinceName = positionInfo.map(QueryTimeZoneRespDto.PositionInfo::getProvince).orElse("");
        this.countryName = positionInfo.map(QueryTimeZoneRespDto.PositionInfo::getCountry).orElse("");
        this.timeZone =  positionInfo.map(QueryTimeZoneRespDto.PositionInfo::getTimeZone).orElse("");
    }

}
