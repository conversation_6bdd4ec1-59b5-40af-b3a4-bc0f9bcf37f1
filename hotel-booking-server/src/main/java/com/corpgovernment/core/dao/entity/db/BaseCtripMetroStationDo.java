package com.corpgovernment.core.dao.entity.db;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Table(name = "ms_base_ctrip_metro_station")
public class BaseCtripMetroStationDo {

    @Id
    private Long id;
    private String stationId;
    private String stationName;
    private String cityId;
    private Double gdLat;
    private Double gdLon;
    private Boolean isDeleted;

}
