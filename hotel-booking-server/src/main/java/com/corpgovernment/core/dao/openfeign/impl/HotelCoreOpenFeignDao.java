package com.corpgovernment.core.dao.openfeign.impl;

import com.corpgovernment.api.basic.request.BasicCityInfoRequest;
import com.corpgovernment.api.basic.response.BasicCityInfoResponse;
import com.corpgovernment.api.basic.soa.BasicDataClient;
import com.corpgovernment.api.supplier.soa.SupplierCompanyClient;
import com.corpgovernment.api.supplier.vo.request.ListSupplierUrlReqVo;
import com.corpgovernment.api.supplier.vo.response.ListSupplierUrlRespVo;
import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.client.ManagementClientUtil;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.dao.openfeign.IHotelCoreOpenFeignDao;
import com.corpgovernment.dto.config.request.GetBookingConfigByTokenRequest;
import com.corpgovernment.dto.config.response.GetBookingConfigByTokenResponse;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.request.VerifyTravelStandardRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.dto.snapshot.SnapshotQtyCmd;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.FilterRoomInfoType;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelFeeSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelProductSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelFeeSnapshotResponse;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelProductSnapshotResponse;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 */
@Repository
@Slf4j
public class HotelCoreOpenFeignDao implements IHotelCoreOpenFeignDao {

    @Resource
    private ManagementClientUtil managementClientUtil;
    @Resource
    private CoreServiceClient coreServiceClient;
    @Resource
    private SupplierCompanyClient supplierCompanyClient;
    @Resource
    @Lazy
    private HotelCoreOpenFeignDao self;
    @Resource
    private BasicDataClient basicDataClient;

    @Override
    public GetBookingConfigByTokenResponse getBookingConfig(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        GetBookingConfigByTokenRequest request = new GetBookingConfigByTokenRequest();
        request.setToken(token);
        try {
            JSONResult<GetBookingConfigByTokenResponse> result = self.getBookingConfigByToken(request);
            if (result == null) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("获取预定配置异常", e);
            return null;
        }
    }

    @Override
    public List<ListSupplierUrlRespVo.SupplierUrl> getSupplierUrlList(String productType, List<ListSupplierUrlReqVo.SupplierKey> supplierKeyList) {
        if (StringUtils.isBlank(productType) || CollectionUtils.isEmpty(supplierKeyList)) {
            log.info("获取供应商url异常 请求参数不全");
            return new ArrayList<>(0);
        }
        ListSupplierUrlReqVo listSupplierUrlReqVo = new ListSupplierUrlReqVo();
        listSupplierUrlReqVo.setProductType(productType);
        listSupplierUrlReqVo.setSupplierKeyList(supplierKeyList);
        try {
            JSONResult<ListSupplierUrlRespVo> result = self.listSupplierUrl(listSupplierUrlReqVo);
            if (result == null) {
                return new ArrayList<>(0);
            }
            ListSupplierUrlRespVo data = result.getData();
            if (data == null) {
                return new ArrayList<>(0);
            }
            return Null.or(data.getSupplierUrlList(), new ArrayList<>(0));
        } catch (Exception e) {
            log.error("获取供应商url异常", e);
            return new ArrayList<>(0);
        }
    }

    @Override
    public List<ResourcesVerifyResponse> verifyTravelStandard(String token, String bizType, List<HotelVerifyRequest> hotelVerifyRequestList) {
        if (StringUtils.isBlank(token) || StringUtils.isBlank(bizType) || CollectionUtils.isEmpty(hotelVerifyRequestList)) {
            log.info("校验差标异常 请求参数不全");
            return new ArrayList<>(0);
        }
        VerifyTravelStandardRequest verifyTravelStandardRequest = new VerifyTravelStandardRequest();
        verifyTravelStandardRequest.setTravelStandardToken(token);
        verifyTravelStandardRequest.setBizType(bizType);
        verifyTravelStandardRequest.setHotelList(hotelVerifyRequestList);
        try {
            JSONResult<List<ResourcesVerifyResponse>> result = self.verifyTravelStandard(verifyTravelStandardRequest);
            if (result == null) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("校验差标异常", e);
            return null;
        }
    }

    @Override
    @BusinessBehaviorMonitor
    public List<TravelStandardResponse> getTravelStandardByToken(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
        getTravelStandardByTokenRequest.setTokenList(Collections.singletonList(token));
        return managementClientUtil.getTravelStandardByToken(getTravelStandardByTokenRequest);
    }

    @Override
    public QuerySnapshotResponseDTO getSnapshot(String token, List<String> dataTypeList) {
        if (StringUtils.isBlank(token) || CollectionUtils.isEmpty(dataTypeList)) {
            return null;
        }
        SnapshotQtyCmd snapshotQtyCmd = new SnapshotQtyCmd();
        snapshotQtyCmd.setDataTypeList(dataTypeList);
        snapshotQtyCmd.setToken(token);
        JSONResult<QuerySnapshotResponseDTO> result = self.getSnapShotByToken(snapshotQtyCmd);
        if (result == null) {
            log.error("获取快照失败");
            return null;
        }
        return result.getData();
    }
    
    @Override
    public GetHotelFeeSnapshotResponse getHotelFeeSnapshot(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        GetHotelFeeSnapshotRequest getHotelFeeSnapshotRequest = new GetHotelFeeSnapshotRequest();
        getHotelFeeSnapshotRequest.setToken(token);
        try {
            JSONResult<GetHotelFeeSnapshotResponse> jsonResult = self.getHotelFeeSnapshot(getHotelFeeSnapshotRequest);
            if (jsonResult == null) {
                return null;
            }
            
            return jsonResult.getData();
        } catch (Exception exception) {
            log.error("获取酒店费用快照异常", exception);
            return null;
        }
    }
    
    @Override
    public GetHotelProductSnapshotResponse getHotelProductSnapshot(String token, String supplierCode, String hotelId, String roomId, String paymentType) {
        GetHotelProductSnapshotRequest getHotelProductSnapshotRequest = new GetHotelProductSnapshotRequest();
        getHotelProductSnapshotRequest.setToken(token);
        FilterRoomInfoType filterRoomInfoType = new FilterRoomInfoType();
        filterRoomInfoType.setHotelId(hotelId);
        filterRoomInfoType.setRoomId(roomId);
        filterRoomInfoType.setSupplierCode(supplierCode);
        filterRoomInfoType.setPaymentMethod(paymentType);
        getHotelProductSnapshotRequest.setFilterRoomInfo(filterRoomInfoType);
        try {
            JSONResult<GetHotelProductSnapshotResponse> jsonResult = self.getHotelProductSnapshot(getHotelProductSnapshotRequest);
            if (jsonResult == null) {
                return null;
            }
            return jsonResult.getData();
        } catch (Exception exception) {
            log.error("获取酒店产品快照异常", exception);
            return null;
        }
    }
    
    @Override
    @BusinessBehaviorMonitor
    public JSONResult<BasicCityInfoResponse> getCityInfoByIdOrName(BasicCityInfoRequest request) {
        return basicDataClient.getCityInfoByIdOrName(request);
    }
    
    @BusinessBehaviorMonitor
    public JSONResult<GetHotelProductSnapshotResponse> getHotelProductSnapshot(GetHotelProductSnapshotRequest getHotelProductSnapshotRequest) {
        return coreServiceClient.getHotelProductSnapshot(getHotelProductSnapshotRequest);
    }
    
    @BusinessBehaviorMonitor
    public JSONResult<GetHotelFeeSnapshotResponse> getHotelFeeSnapshot(GetHotelFeeSnapshotRequest getHotelFeeSnapshotRequest) {
        return coreServiceClient.getFeeInfoSnapshot(getHotelFeeSnapshotRequest);
    }
    
    @BusinessBehaviorMonitor
    public JSONResult<QuerySnapshotResponseDTO> getSnapShotByToken(SnapshotQtyCmd snapshotQtyCmd) {
        return coreServiceClient.getSnapshot(snapshotQtyCmd);
    }

    @BusinessBehaviorMonitor
    public JSONResult<GetBookingConfigByTokenResponse> getBookingConfigByToken(GetBookingConfigByTokenRequest getBookingConfigByTokenRequest) {
        return coreServiceClient.getBookingConfigByToken(getBookingConfigByTokenRequest);
    }

    @BusinessBehaviorMonitor
    public JSONResult<ListSupplierUrlRespVo> listSupplierUrl(ListSupplierUrlReqVo listSupplierUrlReqVo) {
        return supplierCompanyClient.listSupplierUrl(listSupplierUrlReqVo);
    }

    @BusinessBehaviorMonitor
    public JSONResult<List<ResourcesVerifyResponse>> verifyTravelStandard(VerifyTravelStandardRequest verifyTravelStandardRequest) {
        return coreServiceClient.verifyTravelStandard(verifyTravelStandardRequest);
    }

}
