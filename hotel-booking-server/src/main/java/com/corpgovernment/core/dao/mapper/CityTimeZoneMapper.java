package com.corpgovernment.core.dao.mapper;

import com.corpgovernment.core.dao.entity.db.CityTimeZoneDo;
import com.corpgovernment.hotel.product.common.mybatis.TkMapper;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2024/1/5
 */
public interface CityTimeZoneMapper extends TkMapper<CityTimeZoneDo> {

    void insertOrUpdateCityTimeZone(CityTimeZoneDo cityTimeZoneDo);

    default String getTimeZone(String cityId) {
        Example example = new Example(CityTimeZoneDo.class);
        example.createCriteria().andEqualTo("cityId", cityId).andEqualTo("deleted", 0);
        CityTimeZoneDo cityTimeZoneDo = selectOneByExample(example);
        if (cityTimeZoneDo == null) {
            return null;
        }
        return cityTimeZoneDo.getTimeZone();
    }

}
