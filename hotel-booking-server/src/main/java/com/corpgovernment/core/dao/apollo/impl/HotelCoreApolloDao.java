package com.corpgovernment.core.dao.apollo.impl;

import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.apollo.IHotelCoreApolloDao;
import com.corpgovernment.core.dao.dataobject.HotelBonusPointInfoDo;
import com.corpgovernment.core.dao.dataobject.SupplierUrlDo;
import com.corpgovernment.core.domain.common.model.entity.TimeoutConfig;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/27
 */
@Repository
@Data
@Slf4j
public class HotelCoreApolloDao implements IHotelCoreApolloDao {

    @Value("${queryTimeZoneUrl:}")
    private String queryTimeZoneUrl;
    @Value("${ctripSearchKeywordUrl:}")
    private String ctripSearchKeywordUrl;
    @Value("${hotelBonusPointInfoList:}")
    private String hotelBonusPointInfoList;
    @Value("${supplierShieldConditionMap:}")
    private String supplierShieldConditionMap;
    @Value("${spring.datasource.clickhouse.url:}")
    private String clickhouseUrl;
    @Value("${spring.datasource.clickhouse.username:}")
    private String clickhouseUsername;
    @Value("${spring.datasource.clickhouse.password:}")
    private String clickhousePassword;
    @Value("${companyShortNameMap:}")
    private String companyShortNameMap;
    @Value("${cacheTimeMap:}")
    private String cacheTimeMap;
    @Value("${hotelStayTime:900}")
    private String hotelStayTime;
    @Value("${supplierTimeOut:30}")
    private Integer supplierTimeOut;
    @Value("${timeoutConfigList:}")
    private String timeoutConfigList;
    @Value("${supplierConfigList:}")
    private String supplierConfigList;
    @Value("${hotelOnlineMatchDistanceRestrict:50}")
    private String hotelOnlineMatchDistanceRestrict;
    @Value("${hotelPageSize:50}")
    private String hotelPageSize;
    @Value("${supplier_ticket_json:}")
    private String supplierTicketJson;
    @Value("${fuzzySearchDestinationUrl:}")
    private String fuzzySearchDestinationUrl;

    public SupplierUrlDo getQueryTimeZoneUrl() {
        return JsonUtils.parse(queryTimeZoneUrl, SupplierUrlDo.class);
    }

    @Override
    public List<HotelBonusPointInfoDo> getHotelBonusPointInfoList() {
        if (StringUtils.isBlank(hotelBonusPointInfoList)) {
            return new ArrayList<>(0);
        }
        return JsonUtils.parseArray(hotelBonusPointInfoList, HotelBonusPointInfoDo.class);
    }

    @Override
    public Map<String, List<String>> getSupplierShieldCondition() {
        if (StringUtils.isBlank(supplierShieldConditionMap)) {
            return new HashMap<>(0);
        }
        try {
            return JsonUtils.parse(supplierShieldConditionMap, new TypeReference<Map<String, List<String>>>() {});
        } catch (Exception e) {
            log.error("获取供应商屏蔽条件失败", e);
            return new HashMap<>(0);
        }
    }

    @Override
    public String getCompanyShortName() {
        if (StringUtils.isBlank(companyShortNameMap)) {
            return "";
        }
        String companyShortName = "公司";
        try {
            Map<String, String> tmpMap = JsonUtils.parse(companyShortNameMap, new TypeReference<Map<String, String>>() {});
            if (CollectionUtils.isNotEmpty(tmpMap)) {
                companyShortName = tmpMap.getOrDefault(TenantContext.getTenantId(), "公司");
            }
        } catch (Exception e) {
            log.error("解析companyShortNameMap异常", e);
        }
        return companyShortName;
    }

    @Override
    public Long getCacheTime(String key) {
        if (StringUtils.isBlank(cacheTimeMap) || StringUtils.isBlank(key)) {
            return null;
        }
        Map<String, Long> tmpMap = JsonUtils.parse(cacheTimeMap, new TypeReference<Map<String, Long>>() {});
        return tmpMap.get(key);
    }

    @Override
    public Long getHotelStayTime() {
        return Long.valueOf(hotelStayTime);
    }

    @Override
    public Integer getSupplierTimeOut() {
        return supplierTimeOut;
    }

    @Override
    public String getSupplierTicketJson() {
        return supplierTicketJson;
    }
    
    @Override
    public List<TimeoutConfig> getTimeoutConfigList() {
        try {
            if (StringUtils.isBlank(timeoutConfigList)) {
                return null;
            }
            return JsonUtils.parse(timeoutConfigList, new TypeReference<List<TimeoutConfig>>() {});
        } catch (Exception e) {
            log.error("获取供应商timeout配置失败", e);
            return null;
        }
    }
    
    @Override
    public String getSupplierConfigListStr() {
        return supplierConfigList;
    }
    
    @Override
    public Integer getHotelOnlineMatchDistanceRestrict() {
        if (StringUtils.isBlank(hotelOnlineMatchDistanceRestrict)) {
            return HotelCoreConstant.DEFAULT_Hotel_ONLINE_MATCH_DISTANCE_RESTRICT;
        }
        try {
            return Integer.valueOf(hotelOnlineMatchDistanceRestrict);
        } catch (Exception exception) {
            log.error("获取酒店在线匹配距离限制异常", exception);
            return HotelCoreConstant.DEFAULT_Hotel_ONLINE_MATCH_DISTANCE_RESTRICT;
        }
    }
    
    @Override
    public Integer getHotelPageSize() {
        if (StringUtils.isBlank(hotelPageSize)) {
            return HotelCoreConstant.DEFAULT_HOTEL_PAGE_SIZE;
        }
        try {
            return Integer.valueOf(hotelPageSize);
        } catch (Exception exception) {
            log.info("apollo hotelPageSize设置异常", exception);
            return HotelCoreConstant.DEFAULT_HOTEL_PAGE_SIZE;
        }
    }

}
