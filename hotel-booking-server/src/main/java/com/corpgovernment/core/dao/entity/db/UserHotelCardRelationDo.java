package com.corpgovernment.core.dao.entity.db;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@Data
@Table(name = "ho_user_hotel_card_relation")
public class UserHotelCardRelationDo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String uid;
    private String employeeType;
    private String masterGroupId;
    private String cardNum;
    private Boolean deleted;

}
