package com.corpgovernment.core.dao.dataobject;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@Data
public class HotelBonusPointInfoDo {

    /**
     * supplierCode+groupId
     */
    private List<String> supplierGroupList;
    /**
     * 集团名称
     */
    private String groupName;
    /**
     * 积分规则
     */
    private List<String> bonusPointCodeList;
    /**
     * 积分类型
     */
    private String bonusPointType;
    /**
     * 详情页积分规则说明
     */
    private List<String> hotelDetailPageRuleDescList;
    /**
     * 填写页积分规则说明
     */
    private List<String> fillPageRuleDescList;
    /**
     * 订单详情页积分规则说明
     */
    private List<String> orderDetailPageRuleDescList;


}
