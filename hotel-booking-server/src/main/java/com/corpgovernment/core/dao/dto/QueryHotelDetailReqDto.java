package com.corpgovernment.core.dao.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryHotelDetailReqDto {

    private BaseInfo baseInfo;
    @JsonProperty("hotelID")
    private String hotelId;
    private String checkInDate;
    private String checkOutDate;
    private Integer roomQuantity;
    private Integer guestQuantity;
    private String roomType;
    private String corpPayType;
    private roomFilter roomFilter;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BaseInfo {
        @JsonProperty("corpID")
        private String corpId;
        private String uid;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class roomFilter {
        private Boolean onlyHourRoom;
    }

}
