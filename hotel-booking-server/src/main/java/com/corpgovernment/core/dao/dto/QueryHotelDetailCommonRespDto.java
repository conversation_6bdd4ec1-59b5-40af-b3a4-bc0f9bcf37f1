package com.corpgovernment.core.dao.dto;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Data
public class QueryHotelDetailCommonRespDto {

    private HotelDetailInfo hotelDetailInfo;
    private HotelRatePlan hotelRatePlan;
    private List<HotelPic> hotelPicList;
    private Status status;
    private String errorCode;
    private String message;

    @Data
    public static class HotelPic {
        private String basicRoomTypeId;
        private String pictureTitle;
        private String pictureUrl;
    }

    @Data
    public static class Status {
        private Boolean success;
        private String errorCode;
        private String errorMessage;
    }

    @Data
    public static class HotelRatePlan {
        private List<BasicRoom> basicRoomList;
    }

    @Data
    public static class BasicRoom {
        private String baseRoomName;
        private String floor;
        private String roomArea;
        private String hasWindowDesc;
        private String masterBasicRoomId;
        private List<RoomInfo> roomInfoList;
        private BasicRoomStaticInfo basicRoomStaticInfo;
    }

    @Data
    public static class BasicRoomStaticInfo {
        private List<String> basicRoomImageUrl;
        private List<BedInfo> bedInfoList;
    }

    @Data
    public static class RoomInfo {
        private String hotelId;
        private String roomId;
        private String roomName;
        private BigDecimal salePrice;
        private BigDecimal salePriceIncludeTax;
        private BigDecimal avgSalePrice;
        private BigDecimal avgSalePriceIncludeTax;
        private String productId;
        private String roomType;
        private Boolean tmcPrice;
        private String balanceType;
        private List<TaxDetail> taxDetails;
        private String broadBand;
        private Integer maxGuestNumber;
        private BookingRules bookingRules;
        private List<DailyRate> dailyRates;
        private Price avgOriginPriceIncludeTax;
        private Price avgCustomPriceIncludeTax;
        private InvoiceInfo invoiceInfo;
        private RoomStaticInfo roomStaticInfo;
        private RoomMealInfo roomMealInfo;
        private String floorRange;
        private PackageRoomInfo packageRoomInfo;
        private List<SaleRoomTag> saleRoomTags;
        private ServiceChargeInfo serviceChargeInfo;
        private Boolean hourlyRoom;
        private HourlyRoomInfo hourlyRoomInfo;
    }
    
    @Data
    public static class HourlyRoomInfo {
        
        private Integer duration;
        private Integer intervalStartTime;
        private Integer intervalEndTime;
        private String hourlyRoomTips;
        
    }
    
    @Data
    public static class ServiceChargeInfo {
        private Price customChargePrice;
        private Price customChargePricePerRoomNights;
        private List<ServiceChargeDetailInfo> serviceChargeDetailInfoList;
    }
    
    @Data
    public static class ServiceChargeDetailInfo {
        
        private String chargeType;
        
        private String chargingStrategy;
        
        private Price customChargePricePerUnit;
        
    }
    
    @Data
    public static class Price {
        private BigDecimal price;
        private String currency;
    }
    
    @Data
    public static class SpecialNotice {
        
        private String noticeValue;
    
    }

    @Data
    public static class PackageRoomInfo {
        private String packageId;
    }

    @Data
    public static class SaleRoomTag {
        private String tagCode;
        private String tagDesc;
        private String tagName;
    }

    @Data
    public static class RoomStaticInfo {
        private String nonSmokeDesc;
        private List<BedInfo> bedInfoList;
        private WindowInfo windowInfo;
        private List<SpecialNotice> specialNoticeList;
    }

    @Data
    public static class WindowInfo {
        private String windowTypeName;
        private Integer windowType;
    }

    @Data
    public static class BedInfo {
        private String parentBedTypeName;
        private List<ChildBedInfo> childBedInfoList;
    }
    
    @Data
    public static class RoomMealInfo {
        
        private Integer mealType;
        
    }

    @Data
    public static class ChildBedInfo {
        private String childBedTypeName;
        private Integer bedCount;
        private Float bedWidth;
    }

    @Data
    public static class InvoiceInfo {
        private Boolean hasSpecialInvoice;
    }

    @Data
    public static class DailyRate {
        private String effectDate;
        private BigDecimal salePriceIncludeTax;
        private Integer meals;
    }

    @Data
    public static class BookingRules {
        private Boolean canReserve;
        private Boolean justifyConfirm;
        private CancelRuleInfo cancelRuleInfo;
        private Integer roomQuantity;
        private ApplicativeAreaInfo applicativeAreaInfo;
        private PersonPrice personPrice;
    }

    @Data
    public static class ApplicativeAreaInfo {
        private String applicativeAreaDesc;
        private String applicativeAreaTitle;
    }

    @Data
    public static class CancelRuleInfo {
        private String cancelRule;
        private LastCancelTimeInfo lastCancelTimeInfo;
        private List<LadderDeductionInfo> ladderDeductionInfo;
    }

    @Data
    public static class PersonPrice{
        private String rateId;
        private Integer adult;
    }

    @Data
    public static class LadderDeductionInfo {
        private String deductionType;
        private LadderDeductionDetailInfo ladderDeductionDetailInfo;
    }

    @Data
    public static class LadderDeductionDetailInfo {
        private String startDeductTime;
        private String endDeductTime;
        private Price customPrice;
    }

    @Data
    public static class LastCancelTimeInfo {
        private String lastCancelTime;
    }

    @Data
    public static class TaxDetail {
        private Boolean includeInTotalPrice;
        private String currency;
        private BigDecimal amount;
        private String taxTypeName;
        private String customCurrency;
        private BigDecimal customAmount;
    }

    @Data
    public static class HotelDetailInfo {
        private HotelBaseInfo hotelBaseInfo;
        private HotelCommentInfo hotelCommentInfo;
        private HotelFacilityInfo hotelFacilityInfo;
        private List<HotelTrafficInfoGroup> hotelTrafficInfoGroupList;
        private HotelIntroductionInfo hotelIntroductionInfo;
        private HotelPolicyInfo hotelPolicyInfo;
        private List<ReservationNoticeTip> reservationNoticeTip;
    }

    @Data
    public static class ReservationNoticeTip {
        private List<NoticeTipDetail> noticeTipDetail;
    }

    @Data
    public static class NoticeTipDetail {
        private String subTitle;
        private List<NoticeTipItem> noticeTipItem;
    }

    @Data
    public static class NoticeTipItem {
        private String content;
    }

    @Data
    public static class HotelPolicyInfo {
        private ArrivalAndDeparture arrivalAndDeparture;
        private ChildAndAddBed childAndAddBed;
        private MealPolicy mealPolicy;
        private String petPolicy;
        private List<CreditCardInfo> creditCardInfo;
    }

    @Data
    public static class CreditCardInfo {
        private String creditCardName;
        private String creditCardIconUrl;
    }

    @Data
    public static class MealPolicy {
        private String breakfastDesc;
        private String breakfastType;
        private String breakfastStyle;
        private String breakfastPrice;
        private List<String> openTime;
    }

    @Data
    public static class ChildAndAddBed {
        private String childLimitRule;
        private ExistingBedRule existingBedRule;
        private String hotelRemarks;
        private String specialRemarks;
    }

    @Data
    public static class ExistingBedRule {
        private String baseInfoDesc;
        private List<String> chargeDescList;
    }

    @Data
    public static class ArrivalAndDeparture {
        private String arrivalDesc;
        private String departureDesc;
    }

    @Data
    public static class HotelIntroductionInfo {
        private String hotelOpenRenovationDesc;
        private HotelBaseInfoDesc hotelBaseInfoDesc;
        private HotelIntroductionInfoItem hotelIntroductionInfo;
    }

    @Data
    public static class HotelBaseInfoDesc {
        private String content;
    }

    @Data
    public static class HotelIntroductionInfoItem {
        private String content;
    }

    @Data
    public static class HotelTrafficInfoGroup {
        private String trafficInfoGroupName;
        private List<TrafficInfo> trafficInfoList;
    }

    @Data
    public static class TrafficInfo {
        private String landMarkName;
        private String trafficInfoDes;
    }

    @Data
    public static class HotelFacilityInfo {
        private FacilityList facilityList;
        private FacilityDetail facilityDetail;
        private List<NearByFacilityGroup> nearByFacilityGroupList;
    }

    @Data
    public static class NearByFacilityGroup {
        private String nearByFacilityGroupName;
        private List<String> nearByFacilityNameList;
    }

    @Data
    public static class FacilityDetail {
        private List<FacilityGroup> facilityGroupList;
        private ParkingPolicyInfo parkingPolicyInfo;
    }

    @Data
    public static class ParkingPolicyInfo {
        private List<ParkingServiceInfo> parkingServiceInfoList;
        private List<ChargingPoint> chargingPointList;
    }

    @Data
    public static class ChargingPoint {
        private String locationDesc;
        private String typeDesc;
    }

    @Data
    public static class ParkingServiceInfo {
        private ParkingServiceDetail parkingserviceDetail;
    }

    @Data
    public static class ParkingServiceDetail {
        private String reservedDesc;
        private String locationDesc;
        private String typeDesc;
        private String chargeableDesc;
    }

    @Data
    public static class FacilityGroup {
        private String facilityGroupName;
        private List<FacilityItem> facilityItemList;
    }

    @Data
    public static class FacilityItem {
        private String facilityItemName;
        private List<String> masterBasicRoomId;
        private ChargeInfo chargeInfo;
        private String facilityLimit;
    }

    @Data
    public static class ChargeInfo {
        private Boolean chargeable;
        private String chargeableDesc;
    }

    @Data
    public static class FacilityList {
        private List<Facility> facilityList;
    }

    @Data
    public static class Facility {
        private String facilityTypeName;
        private String facilityName;
    }

    @Data
    public static class HotelCommentInfo {
        private ScoreInfo scoreInfo;
        private Integer commenterCount;
    }

    @Data
    public static class ScoreInfo {
        private String total;
        private String location;
        private String cleanliness;
        private String service;
        private String facility;
    }

    @Data
    public static class HotelBaseInfo {
        private String hotelId;
        private String hotelName;
        private String hotelEnName;
        private HotelPositionInfo hotelPositionInfo;
        private List<HotelVideoInfo> hotelVideoInfo;
        private HotelPictureInfo hotelPictureInfo;
        private HotelStarInfo hotelStarInfo;
        private HotelBrandInfo hotelBrandInfo;
        private HotelContactInfo hotelContactInfo;
    }

    @Data
    public static class HotelContactInfo {
        private String telephone;
    }

    @Data
    public static class HotelBrandInfo {
        private String brandId;
        private String groupId;
    }

    @Data
    public static class HotelStarInfo {
        private Integer starNum;
        private String iconType;
    }

    @Data
    public static class HotelPictureInfo {
        private List<CommonPicture> commonPictureList;
        private String hotelLogoUrl;
    }

    @Data
    public static class CommonPicture {
        private String commonPictureTypeName;
        private String hotelLogoUrl;
    }

    @Data
    public static class HotelVideoInfo {
        private String videoUrl;
        private String coverPicUrl;
    }

    @Data
    public static class HotelPositionInfo {
        private String hotelAddress;
        private LocationInfo locationInfo;
        private CityInfo cityInfo;
        private List<ZoneInfo> zoneInfoList;
        private List<CoordinateInfo> coordinateInfoList;
    }

    @Data
    public static class ZoneInfo {
        private String id;
        private String name;
    }

    @Data
    public static class CityInfo {
        private String id;
        private String name;
    }

    @Data
    public static class CoordinateInfo {
        private Double lat;
        private Double lon;
        private String mapType;
    }

    @Data
    public static class LocationInfo {
        private String id;
        private String name;
    }

    public static Boolean checkPrice(Price price) {
        return price != null && price.getPrice() != null && !StringUtils.isBlank(price.getCurrency());
    }

}
