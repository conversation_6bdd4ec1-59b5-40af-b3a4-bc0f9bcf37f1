package com.corpgovernment.core.dao.apollo;

import com.corpgovernment.core.dao.dataobject.HotelBonusPointInfoDo;
import com.corpgovernment.core.domain.common.model.entity.TimeoutConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
public interface IHotelCoreApolloDao {

    String getCompanyShortName();
    Long getCacheTime(String key);
    Long getHotelStayTime();
    Map<String, List<String>> getSupplierShieldCondition();
    List<HotelBonusPointInfoDo> getHotelBonusPointInfoList();
    Integer getSupplierTimeOut();

    String getSupplierTicketJson();

    List<TimeoutConfig> getTimeoutConfigList();
    String getSupplierConfigListStr();
    Integer getHotelOnlineMatchDistanceRestrict();
    Integer getHotelPageSize();
    String getCtripSearchKeywordUrl();
    String getFuzzySearchDestinationUrl();
    
}
