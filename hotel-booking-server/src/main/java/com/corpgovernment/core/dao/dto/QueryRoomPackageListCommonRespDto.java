package com.corpgovernment.core.dao.dto;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15
 */
@Data
public class QueryRoomPackageListCommonRespDto {

    private List<PackageRoomInfo> packageRoomInfo;
    private Status status;

    @Data
    public static class Status {
        private Boolean success;
        private String errorCode;
        private String errorMessage;
    }

    @Data
    public static class PackageRoomInfo {
        private String packageId;
        private List<XProductInfo> xProductInfo;
    }

    @Data
    public static class XProductInfo {
        private List<Integer> categoryId;
        private Integer xProductUnit;
        private String xProductName;
        private Integer quantity;
        private String xProductDesc;
        private MealInfo mealInfo;
        private ApplicableNumberInfo applicableNumberInfo;
        private ReservationInfo reservationInfo;
        private List<ContactNumberInfo> contactNumberInfo;
        private List<ReceptionTimeInfo> receptionTimeInfo;
    }

    @Data
    public static class ReceptionTimeInfo {
        private String startTime;
        private String endTime;
    }

    @Data
    public static class ContactNumberInfo {
        private String phoneNumber;
    }

    @Data
    public static class ReservationInfo {
        private Integer reservationType;
        private Integer reservationValue;
    }

    @Data
    public static class ApplicableNumberInfo {
        private Integer maxNum;
    }

    @Data
    public static class MealInfo {
        private List<MenuItem> mealInfo;
    }

    @Data
    public static class MenuItem {
        private FoodDetail foodDetail;
    }

    @Data
    public static class FoodDetail {
        private String name;
        private Integer num;
    }

}
