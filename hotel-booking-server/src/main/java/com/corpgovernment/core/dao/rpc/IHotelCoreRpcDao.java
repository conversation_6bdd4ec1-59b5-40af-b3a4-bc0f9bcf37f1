package com.corpgovernment.core.dao.rpc;

import com.corpgovernment.api.organization.dto.EmployeeInfoVo;
import com.corpgovernment.api.organization.dto.request.EmployeeByUidRequestVo;
import com.corpgovernment.api.organization.model.employee.EmployeeInfoBo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 */
public interface IHotelCoreRpcDao {

    EmployeeInfoBo getEmployeeInfoByUid(String uid);

    List<EmployeeInfoVo> getEmpOrNonEmpByUidAndType(List<EmployeeByUidRequestVo> requestParam);

}
