package com.corpgovernment.core.dao.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryHotelListReqDto {

    private BaseInfo baseInfo;
    private HotelFilterInfo hotelFilterInfo;
    private RoomFilterInfo roomFilterInfo;
    private SearchBaseInfo searchBaseInfo;
    private List<ExpandStrategyType> ExpandStrategy;

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class RoomFilterInfo {
        private RoomInfoFilter roomInfoFilter;
        private RoomPolicyFilter roomPolicyFilter;
        private RoomPriceRange roomPriceRange;
    }
    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class HotelShieldInfo {
        /**
         * 星级屏蔽
         */
        private List<Integer> OfficialHotelStar;
        /**
         * 钻级屏蔽
         */
        private List<Integer> DiamondHotelStar;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class RoomPriceRange {
        private BigDecimal lowPrice;
        private BigDecimal highPrice;
        private Boolean filterWithExtraPayTax;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class RoomPolicyFilter {
        private Boolean onlyFgRoom;
        private Boolean onlyPpRoom;
        private Boolean justifyConfirm = false;
        private Boolean hasBreakfast;
        private Boolean companyAccountPayment;
        private Boolean freeCancel;
        private ApplicativeAreaInfo applicativeAreaInfo;
        private Boolean onlyBonusPoint;
        private String roomType;
        private Boolean filterWithServiceCharge;
        private Boolean onlyHourRoom;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class ApplicativeAreaInfo {
        private Boolean foreignGuestsApplicative;
        private Boolean gatApplicative;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class RoomInfoFilter {
        private String bedType;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class BaseInfo {
        private String uid;
        @JsonProperty("corpID")
        private String corpId;
        private String language;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class HotelFilterInfo {
        private HotelInfoFilter hotelInfoFilter;
        private HotelPositionFilter hotelPositionFilter;
        private HotelFacilitiesFilter hotelFacilitiesFilter;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class HotelFacilitiesFilter {
        private Boolean hasAirportShuttle;
        private Boolean hasFitnessCenter;
        private Boolean hasSwimmingPool;
        private Boolean hasParking;
        private Boolean hasAirportPickup;
        private Boolean sPA;
        private Boolean freeWirelessBroadband;
        private Boolean freeWiredBroadband;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class HotelPositionFilter {
        private List<String> zoneId;
        private String districtId;
        private String metroId;
        private String metroDistance;
        private MapSearchInfo mapSearchInfo;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class MapSearchInfo {
        private Double lon;
        private Double lat;
        private String radius;
        private String mapType = "GAO_DE";
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class HotelInfoFilter {
        private Boolean onlyViewAgreementHotel = false;
        private HotelBrandGroupInfo hotelBrandGroupInfo;
        private List<Integer> hotelStar;
        private String keyword;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class HotelBrandGroupInfo {
        private List<String> hotelBrand;
        private List<String> hotelGroup;
        private List<String> hotelBrandFeature;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class SearchBaseInfo {
        @JsonProperty("hotelIDList")
        private List<String> hotelIdList;
        @JsonProperty("cityID")
        private String cityId;
        private String checkInDate;
        private String checkOutDate;
        private Integer roomQuantity;
        private Integer guestQuantity;
        private PagingInfo pagingInfo;
        private SortInfo sortInfo;
        private String corpPayType;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class PagingInfo {
        private Integer pageIndex;
        private Integer pageSize;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class SortInfo {
        private String sortType;
        private String sortDirection;
    }
    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class ExpandStrategyType {
        private String StrategyType;
        private List<String> StrategyValue;
    }
}
