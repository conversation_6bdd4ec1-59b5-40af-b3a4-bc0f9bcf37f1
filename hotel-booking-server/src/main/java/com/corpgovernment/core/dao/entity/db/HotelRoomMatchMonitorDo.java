package com.corpgovernment.core.dao.entity.db;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Table(name = "ms_hotel_room_match_monitor")
public class HotelRoomMatchMonitorDo {

    @Id
    private Long id;
    private String matchKey;
    private String supplierCode;
    private String hotelId;
    private Integer ctripMatchedCount;
    private Integer selfMatchedCount;
    private Integer ctripSelfMatchedCount;
    private Integer roomCount;

}
