package com.corpgovernment.core.dao.dto;

import com.corpgovernment.api.basic.dto.BasicCityInfoDto;
import com.corpgovernment.core.dao.entity.db.HotelCityDo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/5
 */
@Data
public class TimeZoneReqDto {

    private String cityId;
    private String cityName;
    private String provinceId;
    private String countryId;

    public TimeZoneReqDto(BasicCityInfoDto basicCityInfoDto) {
        this.cityId = basicCityInfoDto.getCityId();
        this.cityName = basicCityInfoDto.getCityName();
        this.provinceId = basicCityInfoDto.getProvinceId();
        this.countryId = basicCityInfoDto.getCountryId();
    }

}
