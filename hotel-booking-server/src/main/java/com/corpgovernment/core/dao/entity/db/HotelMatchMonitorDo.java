package com.corpgovernment.core.dao.entity.db;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/2/28
 */
@Data
@Table(name = "ms_hotel_match_monitor")
public class HotelMatchMonitorDo {

    @Id
    private Long id;
    private String supplierCode;
    private String hotelId;
    private Integer strategy;
    private Integer showMatched;
    private Integer staticMatched;
    private String errorRecord;

}
