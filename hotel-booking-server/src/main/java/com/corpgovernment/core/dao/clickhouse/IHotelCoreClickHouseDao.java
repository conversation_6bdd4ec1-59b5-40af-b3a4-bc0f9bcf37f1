package com.corpgovernment.core.dao.clickhouse;

import com.corpgovernment.common.businessmonitor.model.BusinessBehavior;
import com.corpgovernment.core.dao.entity.db.TagPoolDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
public interface IHotelCoreClickHouseDao {

    void batchCreateTag(List<TagPoolDo> tagPoolDoList);
    
    List<BusinessBehavior> queryData(String sql, String startTime, String endTime);
    
    Long queryCount(String sql, String startTime, String endTime);

}
