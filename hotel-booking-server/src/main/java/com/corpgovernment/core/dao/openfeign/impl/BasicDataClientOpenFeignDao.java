package com.corpgovernment.core.dao.openfeign.impl;

import com.corpgovernment.api.basic.request.BasicCityListRequest;
import com.corpgovernment.api.basic.request.CorpBrandByBrandIdRequest;
import com.corpgovernment.api.basic.response.BasicGeographyInfoResponse;
import com.corpgovernment.api.basic.response.BasicIntegratedCityResponse;
import com.corpgovernment.api.basic.soa.BasicDataClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.core.dao.openfeign.IBasicDataClientOpenFeignDao;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
@Slf4j
public class BasicDataClientOpenFeignDao implements IBasicDataClientOpenFeignDao {
    @Resource
    private BasicDataClient basicDataClient;

    /**
     * 酒店品牌查询
     * @param request brandIds 品牌ID   type 数据完整度，参考枚举BasicCorpBrandTypeEnum（不传默认全部数据）
     * @return 品牌数据
     */
    @Override
    public BasicGeographyInfoResponse searchCorpBrandByBrandId(CorpBrandByBrandIdRequest request) {
        try {
            JSONResult<BasicGeographyInfoResponse> result = basicDataClient.searchCorpBrandByBrandId(request);
            log.info("hotel brand searchCorpBrandByBrandId result: {}", JsonUtils.toJsonString(result));
            if (result == null) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("酒店品牌查询", e);
            return null;
        }
    }

    /**
     * 根据城市id集合获取城市数据（新）
     */
    @Override
    public BasicIntegratedCityResponse listBasicCityInfoByIds(BasicCityListRequest request) {
        try {
            JSONResult<BasicIntegratedCityResponse> result = basicDataClient.listBasicCityInfoByIds(request);
            log.info("hotel brand listBasicCityInfoByIds result: {}", JsonUtils.toJsonString(result));
            if (result == null) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("根据城市id集合获取城市数据", e);
            return null;
        }
    }
}
