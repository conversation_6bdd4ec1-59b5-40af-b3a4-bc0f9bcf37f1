package com.corpgovernment.core.dao.mapper;

import com.corpgovernment.core.dao.entity.db.HotelMatchMonitorDo;
import com.corpgovernment.hotel.product.common.mybatis.TkMapper;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.shard.annotation.ShardModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/28
 */
public interface HotelMatchMonitorMapper extends TkMapper<HotelMatchMonitorDo> {

    @ShardModel(targetDataSource = "apollo_common")
    default void batchInsertOrUpdate(List<HotelMatchMonitorDo> hotelMatchMonitorDoList) {
        if (CollectionUtils.isEmpty(hotelMatchMonitorDoList)) {
            return;
        }
        batchInsertOrUpdateSql(hotelMatchMonitorDoList);
    }

    void batchInsertOrUpdateSql(@Param("list") List<HotelMatchMonitorDo> hotelMatchMonitorDoList);

}
