package com.corpgovernment.core.dao.entity.db;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @create 2024-08-31 14:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelOrderDo {

    private String orderId;
    
    private String supplierOrderId;
    
    private String travelStandardToken;
    
    private String orderStatus;
    
    private Date actualCheckInTime;
    
    private Date actualCheckOutTime;
    
    private String corpId;
    
    private String supplierCode;
    
    private String corpPayType;

}
