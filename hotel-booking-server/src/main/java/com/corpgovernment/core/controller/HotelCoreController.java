package com.corpgovernment.core.controller;

import com.corpgovernment.api.hotel.booking.checkavail.request.CheckAvailV2RequestVo;
import com.corpgovernment.api.hotel.booking.checkavail.response.CheckAvailV2ResponseVo;
import com.corpgovernment.api.hotel.booking.core.DistrictTravelStandardDetermineReqVo;
import com.corpgovernment.api.hotel.booking.core.DistrictTravelStandardDetermineRespVo;
import com.corpgovernment.api.hotel.booking.core.FeeCalculateReq;
import com.corpgovernment.api.hotel.booking.core.FeeCalculateResp;
import com.corpgovernment.api.hotel.booking.core.HotelChummageVerifyReqVo;
import com.corpgovernment.api.hotel.booking.core.HotelChummageVerifyRespVo;
import com.corpgovernment.api.hotel.booking.core.OrderCancelInquiryReqVo;
import com.corpgovernment.api.hotel.booking.core.OrderCancelInquiryRespVo;
import com.corpgovernment.api.hotel.booking.core.OrderModifyInquiryReqVo;
import com.corpgovernment.api.hotel.booking.core.OrderModifyInquiryRespVo;
import com.corpgovernment.api.hotel.booking.core.QueryDynamicFilterReqVo;
import com.corpgovernment.api.hotel.booking.core.QueryDynamicFilterRespVo;
import com.corpgovernment.api.hotel.booking.core.QueryHotelDetailReqVo;
import com.corpgovernment.api.hotel.booking.core.QueryHotelDetailRespVo;
import com.corpgovernment.api.hotel.booking.core.QueryHotelListReqVo;
import com.corpgovernment.api.hotel.booking.core.QueryHotelListRespVo;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.core.controller.vo.CheckHotelCarRespVo;
import com.corpgovernment.core.controller.vo.CheckHotelCardReqVo;
import com.corpgovernment.core.controller.vo.CreateOrUpdateHotelCardReqVo;
import com.corpgovernment.core.controller.vo.GetUserHotelCardReqVo;
import com.corpgovernment.core.controller.vo.GetUserHotelCardRespVo;
import com.corpgovernment.core.controller.vo.HandleMidnightReqVo;
import com.corpgovernment.core.controller.vo.HandleMidnightRespVo;
import com.corpgovernment.core.controller.vo.IndicatorGenerateReqVo;
import com.corpgovernment.core.controller.vo.IndicatorGenerateRespVo;
import com.corpgovernment.core.controller.vo.SearchKeywordReqVo;
import com.corpgovernment.core.controller.vo.SearchKeywordRespVo;
import com.corpgovernment.core.helper.ParamChecker;
import com.corpgovernment.core.helper.PermissionHelper;
import com.corpgovernment.core.service.CheckAvailV2ServiceImpl;
import com.corpgovernment.core.service.FeeCalculateService;
import com.corpgovernment.core.service.HotelCoreService;
import com.corpgovernment.core.service.IHotelBonusPointService;
import com.corpgovernment.core.service.IHotelDetailService;
import com.corpgovernment.core.service.IHotelDirectOpenapiService;
import com.corpgovernment.core.service.IHotelListService;
import com.corpgovernment.core.service.IHotelMainFlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/27
 */
@Slf4j
@RestController
@RequestMapping("/hotelCore")
public class HotelCoreController {

    @Autowired
    private HotelCoreService hotelCoreService;
    @Autowired
    private IHotelBonusPointService hotelBonusPointService;
    @Autowired
    private IHotelListService hotelListService;
    @Autowired
    private IHotelDetailService hotelDetailService;
    @Autowired
    private CheckAvailV2ServiceImpl checkAvailV2Service;

    @Autowired
    private FeeCalculateService feeCalculateService;
    
    @Resource
    private IHotelDirectOpenapiService hotelDirectOpenapiService;
    
    @Resource
    private IHotelMainFlowService hotelMainFlowService;

    @Autowired
    private PermissionHelper permissionHelper;

    @PostMapping("/handleMidnight")
    public JSONResult<HandleMidnightRespVo> handleMidnight(@RequestBody HandleMidnightReqVo requestParam) {
        return hotelCoreService.handleMidnight(requestParam);
    }

    @PostMapping("/searchKeyword")
    public JSONResult<SearchKeywordRespVo> searchKeyword(@RequestBody SearchKeywordReqVo requestParam) {
        return hotelCoreService.searchKeyword(requestParam);
    }

    @PostMapping("/searchKeywordFromDistribution")
    public JSONResult<SearchKeywordRespVo> searchKeywordFromDistribution(@RequestBody SearchKeywordReqVo requestParam, BaseUserInfo baseUserInfo) {
        requestParam.setUserInfo(baseUserInfo);
        return hotelCoreService.searchKeywordFromDistribution(requestParam);
    }

    @PostMapping("/getUserHotelCard")
    public JSONResult<GetUserHotelCardRespVo> getUserHotelCard(@RequestBody GetUserHotelCardReqVo requestParam) {
        permissionHelper.checkAgentRelation(requestParam.getUid(), "");
        return new JSONResult<>(hotelBonusPointService.getUserHotelCard(requestParam));
    }

    @PostMapping("/createOrUpdateHotelCard")
    public JSONResult<String> createOrUpdateHotelCard(@RequestBody CreateOrUpdateHotelCardReqVo requestParam) {
        permissionHelper.checkAgentRelation(requestParam.getUid(), "");
        hotelBonusPointService.createOrUpdateHotelCard(requestParam);
        return new JSONResult<>("");
    }

    @PostMapping("/checkHotelCard")
    public JSONResult<CheckHotelCarRespVo> checkHotelCard(@RequestBody CheckHotelCardReqVo requestParam, BaseUserInfo baseUserInfo) {
        return new JSONResult<>(hotelBonusPointService.checkHotelCard(requestParam, baseUserInfo));
    }

    @PostMapping("/queryHotelList")
    public JSONResult<QueryHotelListRespVo> queryHotelList(@RequestBody QueryHotelListReqVo requestParam) {
        return new JSONResult<>(hotelListService.queryHotelList(requestParam));
    }

    @PostMapping("/queryHotelDetail")
    public JSONResult<QueryHotelDetailRespVo> queryHotelDetail(@RequestBody QueryHotelDetailReqVo requestParam) {
        return new JSONResult<>(hotelDetailService.queryHotelDetail(requestParam));
    }

    @PostMapping("/checkAvailV2")
    public JSONResult<CheckAvailV2ResponseVo> checkAvailV2(@RequestBody CheckAvailV2RequestVo request, BaseUserInfo baseUserInfo) {
        return new JSONResult<>(checkAvailV2Service.checkAvail(request,baseUserInfo));
    }

    /**
     * 费用计算
     */
    @PostMapping("/feeCalculate")
    public JSONResult<FeeCalculateResp> feeCalculate(@RequestBody  FeeCalculateReq req){
        ParamChecker.check(req);
        return new JSONResult<>(feeCalculateService.calculate(req));
    }
    
    @PostMapping("/inquiryOrderCancel")
    public JSONResult<OrderCancelInquiryRespVo> inquiryOrderCancel(@RequestBody OrderCancelInquiryReqVo orderCancelInquiryReqVo) {
        return new JSONResult<>(hotelDirectOpenapiService.inquiryOrderCancel(orderCancelInquiryReqVo));
    }
    
    @PostMapping("/inquiryOrderModify")
    public JSONResult<OrderModifyInquiryRespVo> inquiryOrderModify(@RequestBody OrderModifyInquiryReqVo orderModifyInquiryReqVo) {
        return new JSONResult<>(hotelDirectOpenapiService.inquiryOrderModify(orderModifyInquiryReqVo));
    }
    
    @PostMapping("/verifyHotelChummage")
    public JSONResult<HotelChummageVerifyRespVo> verifyHotelChummage(@RequestBody HotelChummageVerifyReqVo hotelChummageVerifyReqVo) {
        return new JSONResult<>(hotelMainFlowService.verifyHotelChummage(hotelChummageVerifyReqVo));
    }
    
    
    @PostMapping("/determineDistrictTravelStandard")
    public JSONResult<DistrictTravelStandardDetermineRespVo> determineDistrictTravelStandard(@RequestBody DistrictTravelStandardDetermineReqVo requestParam) {
        return new JSONResult<>(hotelMainFlowService.determineDistrictTravelStandard(requestParam));
    }
    
    @PostMapping("/generateIndicator")
    public JSONResult<IndicatorGenerateRespVo> generateIndicator(@RequestBody IndicatorGenerateReqVo indicatorGenerateReqVo) {
        return new JSONResult<>(hotelMainFlowService.generateIndicator(indicatorGenerateReqVo));
    }
    
    @PostMapping("/queryDynamicFilter")
    public JSONResult<QueryDynamicFilterRespVo> queryDynamicFilter(@RequestBody QueryDynamicFilterReqVo requestParam) {
        return new JSONResult<>(hotelMainFlowService.queryDynamicFilter(requestParam));
    }
    
}
