package com.corpgovernment.core.controller.vo;

import com.corpgovernment.api.basic.response.FuzzyHotelCityFromDistributionResponse.DestinationInfoListDTO;
import com.corpgovernment.core.dao.dto.SearchKeywordRespDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
@Data
public class KeywordVo {

    private static final String DOMESTIC = "domestic";
    private static final String FOREIGN = "foreign";

    private String name;
    private String type;
    private String id;
    private Double latitude;
    private Double longitude;
    private String cityId;
    private String cityName;
    private String countryId;
    private String countryName;
    private String cityType;

    public KeywordVo(SearchKeywordRespDto.DestinationInfo destinationInfo) {
        this.name = destinationInfo.getKeywordName();
        this.type = destinationInfo.getDestinationType();
        this.id = destinationInfo.getDestinationId();
        if (destinationInfo.getCoordinateInfo() != null) {
            this.latitude = destinationInfo.getCoordinateInfo().getGdLat();
            this.longitude = destinationInfo.getCoordinateInfo().getGdLon();
        }
        this.cityId = destinationInfo.getCityId();
        this.cityName = destinationInfo.getCityName();
    }

    public KeywordVo(SearchKeywordRespDto.OtherCityDestinationInfo otherCityDestinationInfo) {
        this.name = otherCityDestinationInfo.getDestinationName();
        this.type = otherCityDestinationInfo.getDestinationType();
        this.id = otherCityDestinationInfo.getDestinationId();
        if (otherCityDestinationInfo.getCoordinateInfo() != null) {
            this.latitude = otherCityDestinationInfo.getCoordinateInfo().getGdLat();
            this.longitude = otherCityDestinationInfo.getCoordinateInfo().getGdLon();
        }
        this.cityId = otherCityDestinationInfo.getCityId();
        this.cityName = otherCityDestinationInfo.getCityName();
        this.countryId = otherCityDestinationInfo.getCountryId();
        this.countryName = otherCityDestinationInfo.getCountryName();
    }

    public KeywordVo(DestinationInfoListDTO destinationInfoListDTO) {
        this.name = destinationInfoListDTO.getDestinationName();
        this.type = destinationInfoListDTO.getResultType();
        this.id = destinationInfoListDTO.getDestinationId().toString();
        if (destinationInfoListDTO.getCoordinateInfo() != null) {
            this.latitude = (destinationInfoListDTO.getCoordinateInfo() != null && destinationInfoListDTO.getCoordinateInfo().getGDLat() != null) ? destinationInfoListDTO.getCoordinateInfo().getGDLat() : null;
            this.longitude = (destinationInfoListDTO.getCoordinateInfo() != null && destinationInfoListDTO.getCoordinateInfo().getGDLon() != null) ? destinationInfoListDTO.getCoordinateInfo().getGDLon() : null;;
        }
        this.cityId = destinationInfoListDTO.getCityId().toString();
        this.cityName = destinationInfoListDTO.getCityName();
        this.countryId = String.valueOf(destinationInfoListDTO.getCountryId());
        this.countryName = destinationInfoListDTO.getCountryName();
        if (destinationInfoListDTO.getCountryId() != null && Integer.valueOf(1).equals(destinationInfoListDTO.getCountryId())) {
            this.cityType = DOMESTIC;
        } else {
            this.cityType = FOREIGN;
        }
    }
}
