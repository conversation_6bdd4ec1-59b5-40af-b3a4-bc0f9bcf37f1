package com.corpgovernment.core.constant;

import com.ctrip.corp.obt.generic.utils.StringUtils;

/**
 * 酒店支持的支付方式
 */
public enum PaymentMethodType {

    ONLINE("PP", "ONLINE", "在线付", true),

    ONLINE_FG("USE_FG", "ONLINE", "在线付", true),

    CASH("FG", "CASH","到店付", false);

    private String code;

    private String innerCode;

    private String name;

    private boolean online;


    public static PaymentMethodType getByInnerCode(String innerCode){
        if(StringUtils.isBlank(innerCode)){
            return null;
        }
        for(PaymentMethodType paymentMethodType : PaymentMethodType.values()){
            if(paymentMethodType.innerCode.equals(innerCode)){
                return paymentMethodType;
            }
        }
        return null;
    }



    public static PaymentMethodType getByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for(PaymentMethodType paymentMethodType : PaymentMethodType.values()){
            if(paymentMethodType.code.equals(code)){
                return paymentMethodType;
            }
        }
        return null;
    }

    PaymentMethodType(String code, String innerCode, String name, boolean online) {
        this.code = code;
        this.innerCode = innerCode;
        this.name = name;
        this.online = online;
    }

    public String getCode() {
        return this.code;
    }


    public boolean isOnline() {
        return this.online;
    }
}
