package com.corpgovernment.core.constant;

import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/2/7
 */
public class HotelCoreConstant {

    public static final Id HOT_HOTEL_BASIC_DATA = Metrics.REGISTRY.createId("hot_hotel_basic_data");

    public static final String DEFAULT_RADIUS = "30";

    public static final String MEIYA = "meiya";

    public static final Integer PAGE_SIZE = 50;

    public static final String COLON = ":";

    public static final Long DEFAULT_CACHE_TIME = 60 * 15L;

    public static final Long DAY_CACHE_TIME = 60 * 60 * 24L;

    public static final String CITY_CENTER = "市中心";

    public static final String YOU = "您";

    public static final String CTRIP = "ctrip";

    public static final String UNKNOWN_STATUS = "-1";

    public static final String OTHER = "其他";

    public static final String HOTEL = "hotel";

    public static final String ZERO = "0";

    public static final String COMMA = "，";

    public static final String FULL_STOP = "。";

    public static final String OPEN_DESC = "开业";

    public static final String RENOVATION_DESC = "装修";

    public static final String CHILD_AND_ADD_BED_POLICY_DESC = "具体房型政策详询酒店";

    public static final String VERTICAL_LINE = "\\|";

    public static final String DEFAULT_TIME = "1970-01-01 08:00:00";

    public static final String CNY = "CNY";

    public static final String DATE_FORMAT = "yyyy-MM-dd";

    public static final String HOTEL_STAR_SHIELD_CONTROL = "hotel_star_shield_control";

    public static final String HOTEL_ROOM_SORT_RULE = "hotel_room_sort_rule";

    public static final String HOTEL_LIST_SORT_RULE = "hotel_list_sort_rule";

    public static final BigDecimal MAX_PRICE = new BigDecimal("1000000000");

    public static final String T = "T";

    public static final String CTRIP_ZOOM_OUT_PICTURE = "_W_400_0";

    public static final String ACCNT = "ACCNT";

    public static final String APPLY_TRIP_CONTEXT = "apply_trip_context";

    public static final String NONE = "NONE";

    public static final String UNKNOWN = "unknown";

    public static final String UNKNOWN_CN = "未知";

    public static final String F = "F";

    public static final String ATTR_MANAGEMENT_CONTROL = "attr_management_control";

    public static final String ALL = "ALL";

    public static final String URGENT_ENABLE = "urgent_enable";
    
    public static final Long MAX_TIMEOUT_MS = 30000L;
    
    public static final String MAX_DATE_TIME = "9999-12-31 23:59:59";
    
    public static final String MIN_DATE_TIME = "1970-01-01 00:00:00";
    
    public static final String ZERO_TIME = "00:00:00";
    
    public static final String ADDITIONAL_MAP_KEY_PLATFORM_ORDER_ID = "platformOrderId";
    
    public static final String MAP_CENTER = "地图中心点";
    
    public static final Integer DEFAULT_Hotel_ONLINE_MATCH_DISTANCE_RESTRICT = 50;
    
    public static final Integer DEFAULT_HOTEL_PAGE_SIZE = 50;
    
    public static class RedisKey {

        public static final String STATE_TIME_KEY = "stateTime_key";

        public static final String HOTEL_LIST_META_DATA = "hotelListMetaData";

        public static final String HOTEL_PAGE_META_DATA = "hotelPageMetaData";

        public static final String HOTEL_PAGE = "hotelPage";

        public static final String HOTEL_DETAIL = "hotelDetail";

        public static final String VIEWED_HOTEL = "viewedHotel";
    }

}
