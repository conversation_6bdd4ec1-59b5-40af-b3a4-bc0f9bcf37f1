package com.corpgovernment.core.config;

import com.ctrip.corp.obt.generic.threadpool.core.spring.EnableDynamicTp;
import com.ctrip.corp.obt.generic.threadpool.core.support.DynamicTp;
import com.ctrip.corp.obt.generic.threadpool.core.support.ThreadPoolBuilder;
import com.ctrip.corp.obt.generic.threadpool.core.support.task.wrapper.TaskWrappers;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.ctrip.corp.obt.generic.threadpool.common.em.QueueTypeEnum.ARRAY_BLOCKING_QUEUE;

/**
 * 线程池配置
 * <AUTHOR>
 * @date 2023/10/11
 */
@Configuration
@Slf4j
@EnableDynamicTp
public class HotelCoreThreadPoolConfig {

    @DynamicTp("queryHotelMinPriceThreadPool")
    @Bean(name = "queryHotelMinPriceThreadPool")
    public ThreadPoolExecutor queryHotelMinPriceThreadPool() {
        int corePoolSize = 50;
        int maximumPoolSize = 50;
        int threadQueueSize = 10000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    @DynamicTp("queryHotelListThreadPool")
    @Bean(name = "queryHotelListThreadPool")
    public ThreadPoolExecutor queryHotelListThreadPool() {
        int corePoolSize = 50;
        int maximumPoolSize = 50;
        int threadQueueSize = 10000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }
    
    @DynamicTp("queryHotelListExternalThreadPool")
    @Bean(name = "queryHotelListExternalThreadPool")
    public ThreadPoolExecutor queryHotelListExternalThreadPool() {
        int corePoolSize = 50;
        int maximumPoolSize = 50;
        int threadQueueSize = 10000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    @DynamicTp("hotelPageThreadPool")
    @Bean(name = "hotelPageThreadPool")
    public ThreadPoolExecutor hotelPageThreadPool() {
        int corePoolSize = 50;
        int maximumPoolSize = 50;
        int threadQueueSize = 10000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    @DynamicTp("queryHotelDetailThreadPool")
    @Bean(name = "queryHotelDetailThreadPool")
    public ThreadPoolExecutor queryHotelDetailThreadPool() {
        int corePoolSize = 50;
        int maximumPoolSize = 50;
        int threadQueueSize = 10000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    @DynamicTp("queryRoomPackageThreadPool")
    @Bean(name = "queryRoomPackageThreadPool")
    public ThreadPoolExecutor queryRoomPackageThreadPool() {
        int corePoolSize = 50;
        int maximumPoolSize = 50;
        int threadQueueSize = 10000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    @DynamicTp("dataReportThreadPool")
    @Bean(name = "dataReportThreadPool")
    public ThreadPoolExecutor dataReportThreadPool() {
        int corePoolSize = 10;
        int maximumPoolSize = 10;
        int threadQueueSize = 5000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    @DynamicTp("xuGongQueryHotelList")
    @Bean(name = "xuGongQueryHotelList")
    public ThreadPoolExecutor xuGongQueryHotelList() {
        int corePoolSize = 50;
        int maximumPoolSize = 50;
        int threadQueueSize = 10000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

}
