package com.corpgovernment.core.helper;

import com.corpgovernment.api.hotel.booking.core.FeeCalculateReq;
import com.corpgovernment.basic.constant.HotelResponseCodeEnum;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.common.enums.ExpenseTypeEnum;
import com.corpgovernment.core.constant.PaymentMethodType;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.RoomDailyPriceType;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import java.util.List;

public class ParamChecker {

    public static void check(FeeCalculateReq req) {
        if(StringUtils.isBlank(req.getExpenseType())){
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "expenseType is blank");
        }
        if(!validExpenseType(req.getExpenseType())){
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "illegal expenseType, expenseType:" + req.getExpenseType());
        }
        if(StringUtils.isBlank(req.getHotelId())){
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "hotelId is null.");
        }
        if(StringUtils.isBlank(req.getRoomId())){
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "roomId is null.");
        }
        if(StringUtils.isBlank(req.getToken())){
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "token is null.");
        }
        if(StringUtils.isBlank(req.getSupplierCode())){
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "supplierCode is null.");
        }
        if(StringUtils.isBlank(req.getPayment())){
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "payment is null.");
        }
        if(PaymentMethodType.getByInnerCode(req.getPayment()) == null){
            throw new CorpBusinessException(HotelResponseCodeEnum.ERROR_PARAM.code(), "payment is illegal. value:" + req.getPayment());
        }
    }



    public static boolean validExpenseType(String type){
        for (ExpenseTypeEnum  item: ExpenseTypeEnum.values()) {
            if(item.getCode().equals(type)){
                return true;
            }
        }
        return false;
    }

}
