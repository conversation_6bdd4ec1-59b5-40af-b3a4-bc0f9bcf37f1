package com.corpgovernment.core.domain.model;


import com.corpgovernment.api.hotel.product.enums.DeductionEnum;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.core.dao.dto.QueryHotelDetailCommonRespDto;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HourlyRoomInfo;
import com.corpgovernment.core.domain.hoteldetail.model.enums.MealTypeEnum;
import com.corpgovernment.core.domain.model.snapshot.product.PriceInfoModel;
import com.corpgovernment.dto.snapshot.dto.hotel.fee.BookingRulesDTO;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomItemV2;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.corpgovernment.api.hotel.product.enums.DeductionEnum.Free;

/**
 * <AUTHOR>
 * @date 2024/7/25
 */
@Data
public class SupplierCheckAvailResultModel {

    /**
     * 状态信息
     */
    private Boolean success;
    /**
     * 不可用原因
     */
    private String failedReason;
    /**
     * 房型信息
     */
    private RoomItem roomInfo;
    /**
     * 房型每日信息
     */
    private List<RoomDailyInfo> roomDailyInfoList;
    /**
     * 房间是否变价
     */
    public Boolean changePrice;
    
    /**
     * 取消政策是否变化
     */
    private Boolean changeCancelPolicy;

    /**
     * 价格变化信息
     */
    public List<ChangePriceDetailInfo> changePriceDetailList;
    /**
     * checkCode
     */
    private String checkCode;
    /**
     * 服务费
     */
    private BigDecimal serviceFee = BigDecimal.ZERO;
    /**
     * 税费信息
     */
    private TaxInfo taxInfo;
    /**
     * 预定规则
     */
    private BookingRules bookingRules;
    
    /**
     * 服务费信息
     */
    public List<ServiceChargeInfo> serviceChargeInfoList;
    
    /**
     * 钟点房信息
     */
    private HourlyRoomInfo hourlyRoomInfo;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ServiceChargeInfo {
        
        private PayTypeEnum payTypeEnum;
        
        private BigDecimal totalServiceCharge;
        
        private ServiceChargeStrategyEnum serviceChargeStrategyEnum;
        
        private BigDecimal serviceChargeStrategyValue;
        
        private BigDecimal avgServiceCharge;
        
    }


    @Data
    public static class ChangePriceDetailInfo {
        /**
         * 变价日期
         */
        public String date;

        /**
         * 价格
         */
        public BigDecimal price;

    }

    @Data
    public static class RoomItem {
        /**
         * 酒店阶梯扣款政策
         */
        private List<LadderDeductionInfo> ladderDeductionInfoList;
        /**
         * 房型最晚取消修改时间
         */
        private String lastCancelTime;
        /**
         * 接口酒店最早到店时间
         */
        private String earlyArrivalTime;
        /**
         * 接口酒店最晚到店时间
         */
        private String lastArrivalTime;

        /**
         * 房型最晚保留时间
         *
         * 例如：2024-04-08 18:00:00
         */
        private String holdTime;
        /**
         * 按照请求的间数n，返回n间的房型原币种价格
         */
        private BigDecimal originAmount;


        /**
         * 按照请求的间数n，返回n间的房型人民币价格
         */
        @JsonProperty(value = "cNYAmount")
        private BigDecimal cnyAmount;
        /**
         * 【房型人数限制】每间房最多可住几人
         */
        private Integer guestPerson;
        /**
         * 【预订间数限制】本次预订最少要订几间
         */
        private Integer minBookingRoomNum;
        /**
         * 【预订间数限制】本次预订最多可订几间
         */
        private Integer maxBookingRoomNum;
        /**
         * 可选备注
         */
        private List<Remark> remarkList;
        /**
         * 是否接受文本备注
         */
        private Boolean receiveTextRemark;
        /**
         * 酒店特别提示
         */
        private List<String> specialTipList;
        /**
         * 房型可开的增值税发票类型(普票/专票/无)
         */
        @JsonProperty(value = "availableVATInvoiceType")
        private String availableVatInvoiceType;
        /**
         * 结算币种金额（1477配置币种,可订请求传币种的时候，该字段才有值）
         */
        private BigDecimal customAmount;
        /**
         * 积分
         */
        private BonusPointInfo bonusPointInfo;
        /**
         * 供应商下单透传额外信息
         */
        public List<String> additionalSupplierInfo;
        /**
         *
         * 支持的发票类型列表（VAT 增值税纸质普票、EVAT 电子普票、VATSpecial 增值税纸质专票
         * DInvoice:增值税数电普票 DVatInvoice: 增值税数电专票 EVATSpecial电子专票）
         */

        private List<String> supportInvoiceTypeList;

        private CancelPenaltyModel cancelPenalties;
        
        /**
         * 特惠房型信息
         */
        private SpecialOfferRoomInfo specialOfferRoomInfo;
        
        /**
         * 餐食类型
         */
        private MealTypeEnum mealTypeEnum;
        
    }
    
    @Data
    public static class SpecialOfferRoomInfo {
        
        /**
         * 支持的证件类型
         * IdentityCard（身份证）；Passport（护照）；TaiwaneseCertificate（台胞证）；TaiwanPass（大陆居民往来台湾通行证）；HKAndMacauPass（港澳通行证）；HometownPermit（回乡证）
         */
        private List<String> supportCertificateType;
        
    }

    @Data
    public static class BonusPointInfo {
        /**
         * 供应商code
         */
        private String supplierCode;
        /**
         * 集团ID
         */
        private String groupId;
        /**
         * 集团名称
         */
        private String groupName;
        /**
         * 积分规则
         */
        private String bonusPointCode;
        /**
         * 积分类型
         */
        private String bonusPointType;
        /**
         * 填写页积分规则说明
         */
        private List<String> fillPageRuleDescList;
        /**
         * 订单详情页积分规则说明
         */
        private List<String> orderDetailPageRuleDescList;
    }
    @Data
    public static class TaxInfo{
        /**
         * 关于该房型房费包含的所有种类的税和服务费的描述
         */
        private String includeTaxFeeDesc;
        /**
         * 关于该房型房费未包含的所有种类的税和服务费的描述
         */
        private String excludeTaxFeeDesc;
        /**
         * 税费明细列表
         */
        private List<TaxDetailInfo> taxFeeInfoList;
    }
    @Data
    public static class TaxDetailInfo{
        /**
         * 税费id
         */
        private Integer taxId;
        /**
         * 税费类型名称
         */
        private String taxTypeName;
        /**
         * 收费模式（PER_STAY-每次入住; PER_PERSON_PER_STAY-每人每次; PER_NIGHT-每夜;
         * PER_PERSON_NIGHT-每人夜; PER_ORDER_AMOUNT-百分比; PER_ROOM_PER_STAY-每间每次入住;
         * PER_ROOM_PER_NIGHT-每间每晚; NOT_CALCULABLE-不能计算; OTHER-其他）
         */
        private String chargeMode;
        /**
         * 税费拆分规则（1-固定金额 每次入住; 2-固定金额 每间每次入住; 3-固定金额 每人每次入住;
         * 4-固定金额 每间每晚; 5-固定金额 每人每晚; 6-百分比; 7-每单位; 8-阶梯-百分比; 9-阶梯-每人每晚）
         */
        private Integer taxFeeCalculateType;
        /**
         * true-费用已经包含在订单中
         * false-费用由酒店按实际情况收取，不包含在订单总价中
         */
        private Boolean includeInTotalPrice;
        /**
         * 税费百分比，只有ChargeMode的值等于PerOrderAmount时有效，默认为0
         */
        private BigDecimal percentage;
        /**
         * 原币种价格
         */
        private PriceInfoModel originalPrice;

        /**
         * 报价（展示价格）
         */
        private PriceInfoModel sellPrice;
    }


    /**
     * 限时取消获取取消提示
     */
    public static List<String> getCancelDetailList(List<LadderDeductionInfo> ladderDeductionInfoList) {
        List<String> cancelDetailList = Lists.newArrayList();
        ladderDeductionInfoList.forEach(l -> {
            String deductionType = l.getDeductionType();
            LadderDeductionDetail ladderDeductionInfo = l.getLadderDeductionInfo();
            BigDecimal amount = Optional.ofNullable(ladderDeductionInfo.getAmount()).orElse(new BigDecimal(0));
            Date endDeductTime = ladderDeductionInfo.getEndDeductTime();
            Date startDeductTime = ladderDeductionInfo.getStartDeductTime();
            if (Free.name().equals(deductionType)) {
                cancelDetailList.add(DateUtil.dateToString(endDeductTime, DateUtil.DF_YMD_HM) + "之前可以免费取消");
            }
            if (DeductionEnum.Ladder.name().equals(deductionType)) {
                cancelDetailList.add(DateUtil.dateToString(startDeductTime, DateUtil.DF_YMD_HM) + "~" +
                        DateUtil.dateToString(endDeductTime, DateUtil.DF_YMD_HM) + "收取" + amount + "元取消费");
            }
            if (DeductionEnum.CanNotCancelation.name().equals(deductionType)) {
                cancelDetailList.add(DateUtil.dateToString(startDeductTime, DateUtil.DF_YMD_HM) + "之后不可取消");
            }
        });
        return cancelDetailList;
    }

    @Data
    public static class LadderDeductionInfo {
        private String deductionType;
        private LadderDeductionDetail ladderDeductionInfo;
    }

    @Data
    public static class LadderDeductionDetail {
        private Date startDeductTime;
        private Date endDeductTime;
        private BigDecimal deductionRatio;
        private BigDecimal amount;
        /**
         * 扣款币种
         */
        private String currency;
        /**
         * 原币种价格
         */
        private BigDecimal originalAmount;
        /**
         * 原币种
         */
        private String originalCurrency;
    }

    @Data
    public static class Remark {
        /**
         * 可选备注key
         */
        private String key;
        /**
         * 可选备注id
         */
        private String id;
        /**
         * 标题，在可选项多选选择时，可以根据title分组
         */
        private String title;
        /**
         * 描述
         */
        private String desc;
        /**
         * 单选还是多选
         */
        private String unique;
        /**
         * true:客户端如果不填写这个分组下的任何选项，那么用这个项作为group的value处理。
         * false：客户端不会使用这个选项作为默认值。没有指定值：那么这个Group不会有默认值填充。
         */
        private Boolean defaultOption;
        /**
         是否要求客户输入可选项。例如7天可选项：牙具，杯具，拖鞋数量等。
         */
        private Boolean needUserValue;
    }

    @Data
    public static class RoomDailyInfo {
        /**
         * 日期（输出格式化为String）
         */
        private String effectDate;

        /**
         * 售价
         */
        private BigDecimal sellPrice;

        /**
         * 币种，实价币种代码
         */
        private String currency;
        /**
         * 餐食数量
         */
        private Integer meals;
        /**
         * 可用房量
         * 1（表示不限）；0（表示无房）
         */
        private Integer holdRoomQuantity;
    }

    @Data
    public static class ServiceFeeInfo {
        /**
         * 价格
         */
        private BigDecimal price;
        /**
         * 类型（F前收-体现在预订过程中，用户支付费用、B后收-不体现在预订过程中仅落库，后续结算使用）
         */
        private String type;
    }
    @Data
    public static class BookingRules {
        /**
         * 确认规则
         */
        private ConfirmRules confirmRules;

        /**
         * 可订客人信息
         */
        private BillingGuestInfo billingGuestInfo;

        /**
         * 证件信息(1:需要; 0:不需要)
         */
        private CertificateInfo certificateInfo;
        
        /**
         * 最小入住天数
         */
        private Integer minLOS;
    }
    @Data
    public static class ConfirmRules{
        private Boolean justifyConfirm;
    }

    /**
     * 可订客人信息
     */
    @Data
    public static class BillingGuestInfo{
        private List<String> guestsNameLanguages;
        /**
         * 是否需要邮箱
         */
        private Boolean needEmail;
    }

    @Data
    public static class CertificateInfo{
        /**
         * 是否需要证件(1:需要; 0:不需要)
         */
        private Boolean needCertificate;
        /**
         * 证件类型（1:身份证; 2:护照; 3:学生证; 4:军人证; 6:驾驶证; 7:回乡证; 8:台胞证; 10:港澳通行证; 11:国际海员证; 21:旅行证; 22:台湾通行证; 25:户口簿; 27:出生证明; 28:外国人永久居留身份证; 32:港澳台居民居住证; 99:其他; 0:未知）
         */
        private String supportCertificateType;
    }
    @Data
    public static class CancelPenaltyModel {
        /**
         * 取消类型
         */
        private String policyType;
        /**
         * 30分钟免费取消场景
         */
        private String freeCancelPolicySceneType;
    }

}
