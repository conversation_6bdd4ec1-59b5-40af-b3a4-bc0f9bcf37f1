package com.corpgovernment.core.domain.hotelconfig.service;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.domain.common.gateway.IHotelIndicatorGateway;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.common.model.enums.SystemSupplierEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.common.model.enums.HotelForceChummageEnum;
import com.corpgovernment.core.domain.hotelconfig.gateway.IHotelConfigGateway;
import com.corpgovernment.core.domain.hotelconfig.model.entity.SignalTravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.entity.Guest;
import com.corpgovernment.core.domain.hotelconfig.model.entity.HotelRoomCheckInInfo;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelApplication;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.enums.AreaTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.GenderEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.HotelChummageVerifyResultEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/13
 */
@Service
@Slf4j
public class HotelConfigDomainService implements IHotelConfigDomainService {

    @Resource
    private IHotelConfigGateway hotelConfigGateway;

    @Resource
    private IHotelIndicatorGateway hotelIndicatorGateway;
    
    @Resource
    private ICommonDomainService commonDomainService;

    @Override
    @BusinessBehaviorMonitor
    public TravelConfig getTravelConfig(String token, String travelMode, String productType, List<String> operatorTypeList) {
        TravelConfig travelConfig = null;
        try {
            travelConfig = hotelConfigGateway.getTravelConfig(token, travelMode, productType, operatorTypeList);
            return travelConfig;
        } finally {
            hotelIndicatorGateway.getTravelConfig(travelConfig);
        }
    }

    @Override
    @BusinessBehaviorMonitor
    public TravelStandard getTravelStandard(String token) {
        TravelStandard travelStandard = null;
        try {
            travelStandard = hotelConfigGateway.getTravelStandard(token);
            return travelStandard;
        } finally {
            hotelIndicatorGateway.getTravelStandard(travelStandard);
        }
    }

    @Override
    public List<SupplierProduct> getSupplierProductList(TravelConfig travelConfig, String operatorType, Boolean hasHourlyRoom) {
        if (travelConfig == null || CollectionUtils.isEmpty(travelConfig.getSupplierProductListMap())) {
            return new ArrayList<>(0);
        }
        List<SupplierProduct> supplierProductList = travelConfig.getSupplierProductListMap().get(operatorType);
        if (CollectionUtils.isEmpty(supplierProductList)) {
            return new ArrayList<>(0);
        }
        
        List<SupplierProduct> results = supplierProductList.stream()
                .filter(item -> item != null && StringUtils.isNotBlank(item.getSupplierCode()))
                .sorted(Comparator.comparing((SupplierProduct item) -> Null.or(item.getSortNum(), Integer.MAX_VALUE))
                        .thenComparing(item -> Null.or(item.getSupplierCode(), "")))
                .collect(Collectors.toList());
        
        if (Boolean.TRUE.equals(hasHourlyRoom)) {
            results = results.stream()
                    .filter(item -> item != null && SystemSupplierEnum.CTRIP.getCode().equals(item.getSupplierCode()))
                    .collect(Collectors.toList());
        }
        return results;
    }

    @Override
    public Boolean checkLatLonTravelApplication(TravelApplication travelApplication) {
        return travelApplication != null
                && travelApplication.getBookLon() != null
                && travelApplication.getBookLat() != null
                && StringUtils.isNotBlank(travelApplication.getBookAddress());
    }
    
    @Override
    public List<String> getPaymentMethodList(TravelStandard travelStandard, TravelConfig travelConfig) {
        if (travelConfig == null || CollectionUtils.isEmpty(travelConfig.getPaymentMethodList())) {
            return null;
        }
        
        List<String> paymentMethodList = travelConfig.getPaymentMethodList();
        
        // 紧急预订
        if (travelStandard != null && Boolean.TRUE.equals(travelStandard.getUrgencyReserve())) {
            List<String> urgentReservePaymentMethodList = travelConfig.getUrgentReservePaymentMethodList();
            if (CollectionUtils.isEmpty(urgentReservePaymentMethodList)) {
                return null;
            }
            
            // 取交集
            paymentMethodList = paymentMethodList.stream().filter(urgentReservePaymentMethodList::contains).collect(Collectors.toList());
        }
        
        return paymentMethodList;
    }
    
    @Override
    public HotelChummageVerifyResultEnum verifyHotelChummage(HotelForceChummageEnum hotelForceChummageEnum,
                                                             Boolean hotelForceSameSexChummage,
                                                             List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList) {
        if (CollectionUtils.isEmpty(hotelRoomCheckInInfoList) || hotelForceChummageEnum == null || hotelForceChummageEnum == HotelForceChummageEnum.NO_FORCE_CHUMMAGE) {
            return HotelChummageVerifyResultEnum.VERIFY_PASS;
        }
        
        // 过滤出房间单个客人的客人
        List<Guest> guestList = hotelRoomCheckInInfoList.stream()
                .filter(item -> item != null
                        && item.getGuestList() != null
                        && item.getGuestList().stream().filter(Objects::nonNull).count() == 1)
                .flatMap(item -> item.getGuestList().stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        // 单人的房间小于等于1间，校验通过
        if (CollectionUtils.isEmpty(guestList) || guestList.size() <= 1) {
            return HotelChummageVerifyResultEnum.VERIFY_PASS;
        }
        
        // 同性合住判断
        if (Boolean.TRUE.equals(hotelForceSameSexChummage)) {
            Boolean haveSameSexGuest = haveSameSexGuest(guestList);
            if (Boolean.TRUE.equals(haveSameSexGuest)) {
                return HotelChummageVerifyResultEnum.NEED_SAME_SEX_CHUMMAGE;
            }
            return HotelChummageVerifyResultEnum.VERIFY_PASS;
        }
        else {
            return HotelChummageVerifyResultEnum.NEED_CHUMMAGE;
        }
    }
    
    @Override
    public Boolean getHaveHotelChummage(List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList) {
        if (CollectionUtils.isEmpty(hotelRoomCheckInInfoList)) {
            return true;
        }
        
        // 过滤出有多个客人的房间
        List<HotelRoomCheckInInfo> tmpList = hotelRoomCheckInInfoList.stream()
                .filter(item -> item != null
                        && item.getGuestList() != null
                        && item.getGuestList().stream().filter(Objects::nonNull).count() > 1)
                .collect(Collectors.toList());
        
        return CollectionUtils.isNotEmpty(tmpList);
    }
    
    @Override
    @BusinessBehaviorMonitor
    public TravelConfig getTravelConfig(String token, List<String> operatorTypeList) {
        TravelConfig travelConfig = null;
        try {
            travelConfig = hotelConfigGateway.getTravelConfig(token, operatorTypeList);
            return travelConfig;
        } finally {
            hotelIndicatorGateway.getTravelConfig(travelConfig);
        }
    }
    
    @Override
    public String getNoChummageDesc(HotelChummageVerifyResultEnum hotelChummageVerifyResultEnum, HotelForceChummageEnum hotelForceChummageEnum) {
        // 需要合住
        if (Objects.equals(hotelChummageVerifyResultEnum, HotelChummageVerifyResultEnum.NEED_CHUMMAGE)
                && Objects.equals(hotelForceChummageEnum, HotelForceChummageEnum.FORCE_CHUMMAGE)) {
            return "根据公司政策，预订酒店必须合住，请修改入住信息";
        }
        // 需要合住，并选择rc
        else if (Objects.equals(hotelChummageVerifyResultEnum, HotelChummageVerifyResultEnum.NEED_CHUMMAGE)
                && Objects.equals(hotelForceChummageEnum, HotelForceChummageEnum.NO_CHUMMAGE_NEED_SELECT_RC)) {
            return "根据公司政策，未合住时需在下单时选择原因，是否继续预订？";
        }
        // 需要同性合住
        else if (Objects.equals(hotelChummageVerifyResultEnum, HotelChummageVerifyResultEnum.NEED_SAME_SEX_CHUMMAGE)
                && Objects.equals(hotelForceChummageEnum, HotelForceChummageEnum.FORCE_CHUMMAGE)) {
            return "根据公司政策，预订酒店同性必须合住，请修改入住信息";
        }
        // 需要同性合住，并选择rc
        else if (Objects.equals(hotelChummageVerifyResultEnum, HotelChummageVerifyResultEnum.NEED_SAME_SEX_CHUMMAGE)
                && Objects.equals(hotelForceChummageEnum, HotelForceChummageEnum.NO_CHUMMAGE_NEED_SELECT_RC)) {
            return "根据公司政策，同性未合住时需在下单时选择原因，是否继续预订？";
        }
        return null;
    }
    
    private Boolean haveSameSexGuest(List<Guest> guestList) {
        if (CollectionUtils.isEmpty(guestList)) {
            return false;
        }
        
        Set<GenderEnum> genderEnumSet = new HashSet<>();
        for (Guest guest : guestList) {
            if (guest == null) {
                continue;
            }
            
            GenderEnum genderEnum = guest.getGenderEnum();
            if (genderEnum == null) {
                // 直接判断 UNKNOWN 的情况
                if (genderEnumSet.contains(GenderEnum.MALE) || genderEnumSet.contains(GenderEnum.FEMALE)) {
                    return true;
                }
                genderEnumSet.add(GenderEnum.MALE);
                genderEnumSet.add(GenderEnum.FEMALE);
            } else {
                // 判断其他性别的情况
                if (genderEnumSet.contains(genderEnum)) {
                    return true;
                }
                genderEnumSet.add(genderEnum);
            }
        }
        
        return false;
    }
    
    @Override
    @BusinessBehaviorMonitor
    public String determineDistrictTravelStandard(String token, String cityId, String districtId, String countyId) {
        // 刷新token
        SignalTravelStandard signalTravelStandard = hotelConfigGateway.refreshTravelStandardToken(token, cityId, districtId, countyId);
        
        // 判断是否切换了差标
        String travelStandardCityId = Optional.ofNullable(signalTravelStandard)
                .map(SignalTravelStandard::getAvgPriceTravelStandard)
                .map(SignalTravelStandard.AvgPriceTravelStandard::getCityId).orElse(null);
        AreaTypeEnum areaTypeEnum = Optional.ofNullable(signalTravelStandard)
                .map(SignalTravelStandard::getAvgPriceTravelStandard)
                .map(SignalTravelStandard.AvgPriceTravelStandard::getAreaTypeEnum).orElse(null);
        String cityName = Optional.ofNullable(signalTravelStandard)
                .map(SignalTravelStandard::getAvgPriceTravelStandard)
                .map(SignalTravelStandard.AvgPriceTravelStandard::getCityName).orElse(null);
        // 如果是行政区差标 或 县级市差标且cityId不一样
        if (Objects.equals(areaTypeEnum, AreaTypeEnum.DISTRICT)
                || ((Objects.equals(areaTypeEnum, AreaTypeEnum.COUNTY) || Objects.equals(areaTypeEnum, AreaTypeEnum.CITY)) && !StringUtils.equalsIgnoreCase(cityId, travelStandardCityId))) {
            return cityName;
        }
        
        return null;
    }
    
    @Override
    @BusinessBehaviorMonitor
    public HotelCustomEnum getHotelCustomEnum(String token) {
        return getHotelCustomEnum(hotelConfigGateway.getTravelApplication(token));
    }
    
    private HotelCustomEnum getHotelCustomEnum(TravelApplication travelApplication) {
        if (travelApplication != null
                && travelApplication.getBookLon() != null
                && travelApplication.getBookLat() != null
                && StringUtils.isNotBlank(travelApplication.getBookAddress())) {
            return HotelCustomEnum.XU_GONG;
        }
        return HotelCustomEnum.DEFAULT;
    }
    
}
