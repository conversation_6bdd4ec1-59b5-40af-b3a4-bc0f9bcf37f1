package com.corpgovernment.core.domain.openapi.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @create 2024-08-31 19:27
 */
@AllArgsConstructor
@Getter
public enum ModifyReasonEnum {
    
    ITINERARY_CHANGE("ITINERARY_CHANGE", "行程改变"),
    TRANSPORTATION_PROBLEM("TRANSPORTATION_PROBLEM", "交通延误/取消"),
    BOOKING_ERROR("BOOKING_ERROR", "预订错误"),
    HEALTH_ILLNESS("HEALTH_ILLNESS", "身体不适"),
    HOTEL_ISSUE("HOTEL_ISSUE", "酒店原因");
    
    private final String code;
    
    private final String info;
    
}
