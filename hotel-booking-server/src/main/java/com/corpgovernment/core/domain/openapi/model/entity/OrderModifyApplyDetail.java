package com.corpgovernment.core.domain.openapi.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @create 2024-09-26 13:36
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderModifyApplyDetail {
    
    private String applyFormId;
    
    private String orderId;
    
    private String applyTime;
    
    private Integer status;

}
