package com.corpgovernment.core.domain.common.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/5/10
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class Price {

    private String name;
    private String originCurrency;
    private BigDecimal originPrice;
    private String customCurrency;
    private BigDecimal customPrice;

    public static Boolean checkCustomPrice(Price price) {
        return price != null && checkPrice(price.getCustomPrice());
    }

    public static Boolean checkOriginPrice(Price price) {
        return price != null && checkPrice(price.getOriginPrice());
    }

    public static Boolean checkPrice(BigDecimal bigDecimal) {
        return bigDecimal != null && bigDecimal.compareTo(BigDecimal.ZERO) > 0;
    }

    public static BigDecimal getMaxPrice() {
        return new BigDecimal("1000000000");
    }


}
