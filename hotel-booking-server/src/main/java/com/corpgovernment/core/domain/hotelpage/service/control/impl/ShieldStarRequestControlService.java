package com.corpgovernment.core.domain.hotelpage.service.control.impl;

import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelAdvancedFilter;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControlRequest;
import com.corpgovernment.core.domain.hotelpage.model.enums.RequestControlEnum;
import com.corpgovernment.core.domain.hotelpage.service.control.IRequestControlService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-23 13:58
 */
@Service
@Slf4j
public class ShieldStarRequestControlService implements IRequestControlService {

    @Override
    public String controlRequest(HotelPageRequest hotelPageRequest, TravelControlRequest travelControlRequest) {
        if (hotelPageRequest == null || travelControlRequest == null || CollectionUtils.isEmpty(travelControlRequest.getShieldStarList())) {
            return "";
        }

        // 获取需要管控的星级
        List<Integer> userViewStarList = Optional.ofNullable(hotelPageRequest.getHotelAdvancedFilter()).map(HotelAdvancedFilter::getChoiceStarList).orElse(null);
        Set<Integer> userViewStarSet = CollectionUtils.isEmpty(userViewStarList) ?
                IntStream.rangeClosed(1, 5).boxed().collect(Collectors.toSet()) : new HashSet<>(userViewStarList);

        // 控制
        for (Integer shieldStar : travelControlRequest.getShieldStarList()) {
            userViewStarSet.remove(shieldStar);
        }
        if (CollectionUtils.isEmpty(userViewStarSet)) {
            return requestControlItemEnum().getCode();
        }

        // 生效
        HotelAdvancedFilter hotelAdvancedFilter = hotelPageRequest.getHotelAdvancedFilter();
        if (hotelAdvancedFilter == null) {
            hotelAdvancedFilter = new HotelAdvancedFilter();
        }
        if (hotelAdvancedFilter.getControlStarList() == null) {
            hotelAdvancedFilter.setControlStarList(new ArrayList<>(userViewStarSet));
        } else {
            Collection<Integer> list = CollectionUtils.intersection(hotelAdvancedFilter.getControlStarList(), Null.or(userViewStarSet, new ArrayList<>()));
            hotelAdvancedFilter.setControlStarList(new ArrayList<>(list));
        }
        hotelAdvancedFilter.setChoiceStarList(new ArrayList<>(userViewStarSet));

        hotelPageRequest.setHotelAdvancedFilter(hotelAdvancedFilter);

        return "";
    }

    @Override
    public RequestControlEnum requestControlItemEnum() {
        return RequestControlEnum.SHIELD_STAR;
    }
}
