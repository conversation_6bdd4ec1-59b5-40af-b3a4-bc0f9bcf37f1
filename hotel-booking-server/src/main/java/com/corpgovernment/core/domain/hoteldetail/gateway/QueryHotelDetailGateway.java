package com.corpgovernment.core.domain.hoteldetail.gateway;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.dto.QueryHotelDetailCommonRespDto;
import com.corpgovernment.core.dao.dto.QueryHotelDetailReqDto;
import com.corpgovernment.core.dao.dto.QueryHotelDetailRespDto;
import com.corpgovernment.core.domain.common.gateway.ICommonGateway;
import com.corpgovernment.core.domain.common.gateway.IHotelIndicatorGateway;
import com.corpgovernment.core.domain.common.model.entity.MapInfo;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.HotelLevelEnum;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.*;
import com.corpgovernment.core.domain.hoteldetail.model.enums.*;
import com.corpgovernment.util.AmountPrecisionUtil;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Repository
@Slf4j
public class QueryHotelDetailGateway implements IQueryHotelDetailGateway {

    @Resource
    private ICommonGateway commonGateway;

    @Resource
    private IHotelIndicatorGateway hotelIndicatorGateway;

    @Resource
    private IHotelDetailGateway hotelDetailGateway;
    
    @Resource
    private ICommonDomainService commonDomainService;


    @Override
    @BusinessBehaviorMonitor
    public HotelDetail queryHotelDetail(QueryHotelDetailRequest queryHotelDetailRequest) {
        if (queryHotelDetailRequest == null || queryHotelDetailRequest.getSupplierProduct() == null) {
            return null;
        }

        QueryHotelDetailReqDto request = null;
        QueryHotelDetailRespDto response = null;
        String code = null;
        String msg = null;
        try {
            // 构建请求
            request = buildRequest(queryHotelDetailRequest);
            
            // 调用供应商接口
            SupplierProduct supplierProduct = queryHotelDetailRequest.getSupplierProduct();
            response = commonGateway.doPostHttp(
                    supplierProduct.getSupplierCode(),
                    "酒店详情查询", supplierProduct.getUrl(),
                    supplierProduct.getUserKey(),
                    JsonUtils.toJsonString(request),
                    QueryHotelDetailRespDto.class);

            // 结果处理
            if (response == null) {
                log.error("调用供应商失败");
                code = HotelCoreConstant.UNKNOWN_STATUS;
                msg = "未知错误";
                return null;
            }
            if (response.getCode() != null || response.getMsg() != null || response.getData() != null) {
                log.info("走新契约");
                code = response.getCode();
                msg = response.getMsg();
                if (response.getCode() == null || !"0".equals(response.getCode()) || response.getData() == null) {
                    return null;
                }
                return buildHotelDetail(supplierProduct, response.getData(), queryHotelDetailRequest);
            } else {
                log.info("走老契约");
                if (response.getStatus() != null) {
                    code = response.getStatus().getErrorCode();
                    msg = response.getStatus().getErrorMessage();
                } else if (response.getErrorCode() != null) {
                    code = response.getErrorCode();
                    msg = response.getMessage();
                }
                if (response.getStatus() != null && !Boolean.TRUE.equals(response.getStatus().getSuccess())) {
                    return null;
                }
                if (response.getErrorCode() != null && !Objects.equals(response.getErrorCode(), "0")) {
                    return null;
                }
                return buildHotelDetail(supplierProduct, response, queryHotelDetailRequest);
            }
        } catch (Exception e) {
            log.error("调用供应商失败", e);
            code = HotelCoreConstant.UNKNOWN_STATUS;
            msg = e.toString();
            return null;
        } finally {
            hotelIndicatorGateway.execute(queryHotelDetailRequest.getSupplierProduct().getSupplierCode(), code, msg, request, response);
        }
    }

    private QueryHotelDetailReqDto buildRequest(QueryHotelDetailRequest queryHotelDetailRequest) {
        if (queryHotelDetailRequest == null) {
            return null;
        }
        SupplierProduct supplierProduct = queryHotelDetailRequest.getSupplierProduct();
        if (supplierProduct == null) {
            return null;
        }
        return QueryHotelDetailReqDto.builder()
                .baseInfo(QueryHotelDetailReqDto.BaseInfo.builder()
                        .uid(handleUid(supplierProduct.getUid(), supplierProduct.getSupplierCode()))
                        .corpId(supplierProduct.getCorpId()).build())
                .hotelId(queryHotelDetailRequest.getHotelId())
                .checkInDate(queryHotelDetailRequest.getCheckInDate())
                .checkOutDate(queryHotelDetailRequest.getCheckOutDate())
                .roomQuantity(queryHotelDetailRequest.getRoomQuantity())
                .guestQuantity(queryHotelDetailRequest.getGuestQuantity())
                .roomType(queryHotelDetailRequest.getRoomType())
                .corpPayType(queryHotelDetailRequest.extractSupplierTravelMode())
                .roomFilter(QueryHotelDetailReqDto.roomFilter.builder()
                        .onlyHourRoom(queryHotelDetailRequest.getHasHourlyRoom())
                        .build())
                .build();
    }

    private String handleUid(String uid, String supplierCode) {
        if (StringUtils.isNotBlank(uid)) {
            return uid;
        }
        if (StringUtils.equalsIgnoreCase(HotelCoreConstant.CTRIP, supplierCode)) {
            return "";
        }
        return null;
    }

    private HotelDetail buildHotelDetail(SupplierProduct supplierProduct, QueryHotelDetailCommonRespDto response, QueryHotelDetailRequest queryHotelDetailRequest) {
        if (response == null || supplierProduct == null || queryHotelDetailRequest == null) {
            return null;
        }

        // 上下文数据
        SupplierData supplierData = buildSupplierData(supplierProduct, response, queryHotelDetailRequest);

        // 构建hotelDetail
        HotelDetail hotelDetail = HotelDetail.builder()
                .hotelId(queryHotelDetailRequest.getHotelId())
                .supplierCode(supplierProduct.getSupplierCode())
                .supplierName(supplierProduct.getSupplierName())
                .hotelBaseInfo(buildHotelBaseInfo(response.getHotelDetailInfo(), supplierData))
                .hotelFacility(buildFacility(response.getHotelDetailInfo()))
                .hotelPolicyService(buildHotelPolicyService(response.getHotelDetailInfo()))
                .reservationNoticeList(buildReservationNoticeList(response.getHotelDetailInfo()))
                .nearByGroupList(buildNearByGroupList(response.getHotelDetailInfo()))
                .basicRoomCardList(buildBasicRoomCardList(response.getHotelRatePlan(), supplierData,response.getHotelDetailInfo()))
                .build();

        hotelDetailGateway.handleHotelDetailAggAttr(hotelDetail);

        return hotelDetail;
    }

    private List<BasicRoomCard> buildBasicRoomCardList(QueryHotelDetailCommonRespDto.HotelRatePlan hotelRatePlan, SupplierData supplierData, QueryHotelDetailCommonRespDto.HotelDetailInfo hotelDetailInfo) {
        if (hotelRatePlan == null || CollectionUtils.isEmpty(hotelRatePlan.getBasicRoomList()) || supplierData == null) {
            return null;
        }

        List<BasicRoomCard> basicRoomCardList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.BasicRoom basicRoom : hotelRatePlan.getBasicRoomList()) {
            if (basicRoom == null || CollectionUtils.isEmpty(basicRoom.getRoomInfoList())) {
                continue;
            }

            String basicRoomId = getBasicRoomId(basicRoom.getMasterBasicRoomId(), basicRoom.getRoomInfoList(), supplierData.getSupplierCode());
            supplierData.setBasicRoomId(basicRoomId);
            supplierData.setBasicRoomFacilityGroupList(buildBasicRoomFacilityGroupList(supplierData.getFacilityGroupList(), basicRoomId));
            supplierData.setAreaDesc(getAreaDesc(basicRoom.getRoomArea()));

            BasicRoomCard basicRoomCard = BasicRoomCard.builder()
                    .supplierCode(supplierData.getSupplierCode())
                    .supplierName(supplierData.getSupplierName())
                    .hotelId(supplierData.getHotelId())
                    .basicRoomId(basicRoomId)
                    .name(basicRoom.getBaseRoomName())
                    .areaDesc(getAreaDesc(basicRoom.getRoomArea()))
                    .floorDesc(basicRoom.getFloor() == null ? null :
                            basicRoom.getFloor().replace("F", "").replace("f", "").replace("层", "") + "F")
                    .pictureList(buildPictureList(basicRoom.getBasicRoomStaticInfo(), supplierData))
                    .bedDesc(getBedDesc(basicRoom.getBasicRoomStaticInfo(), basicRoom.getRoomInfoList()))
                    .roomCardList(buildRoomCardList(basicRoom.getRoomInfoList(), supplierData)).build();

            hotelDetailGateway.handleBasicRoomCardAggAttr(basicRoomCard);

            basicRoomCardList.add(basicRoomCard);
        }
        return basicRoomCardList;
    }

    private List<RoomCard> buildRoomCardList(List<QueryHotelDetailCommonRespDto.RoomInfo> roomInfoList, SupplierData supplierData) {
        if (CollectionUtils.isEmpty(roomInfoList) || supplierData == null) {
            return null;
        }

        List<RoomCard> roomCardList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.RoomInfo roomInfo : roomInfoList) {
            if (roomInfo == null) {
                continue;
            }

            roomCardList.add(RoomCard.builder()
                    .hotelId(supplierData.getHotelId())
                    .basicRoomId(supplierData.getBasicRoomId())
                    .roomId(roomInfo.getRoomId())
                    .productId(roomInfo.getProductId())
                    .groupId(supplierData.getGroupId())
                    .brandId(supplierData.getBrandId())
                    .city(supplierData.getCity())
                    .district(supplierData.getDistrict())
                    .zoneList(supplierData.getZoneList())
                    .supplierCode(supplierData.getSupplierCode())
                    .supplierName(supplierData.getSupplierName())
                    .directSupplier(supplierData.getDirectSupplier())
                    .facilityGroupList(supplierData.getBasicRoomFacilityGroupList())
                    .canReserve(roomInfo.getBookingRules() == null ? null : roomInfo.getBookingRules().getCanReserve())
                    .protocolType(getProtocolType(roomInfo.getRoomType(), roomInfo.getTmcPrice()))
                    .roomPackage(roomInfo.getPackageRoomInfo() == null
                            || StringUtils.isBlank(roomInfo.getPackageRoomInfo().getPackageId())
                            || StringUtils.equalsIgnoreCase(roomInfo.getPackageRoomInfo().getPackageId(), "0")
                            ? null : RoomPackage.builder()
                            .packageId(roomInfo.getPackageRoomInfo().getPackageId()).build())
                    .balanceTypeEnum(BalanceTypeEnum.getEnum(roomInfo.getBalanceType()))
                    .roomBaseInfo(buildRoomBaseInfo(roomInfo, supplierData))
                    .roomPolicyService(buildRoomPolicyService(roomInfo))
                    .roomPrice(buildRoomPrice(roomInfo, supplierData))
                    .allRoomTagList(getRoomTagList(roomInfo))
                    .personPrice(buildPersonPrice(roomInfo.getBookingRules()))
                    .hourlyRoomInfo(buildHourlyRoomInfo(roomInfo, supplierData))
                    .build());
        }
        return roomCardList;
    }
    
    private HourlyRoomInfo buildHourlyRoomInfo(QueryHotelDetailCommonRespDto.RoomInfo roomInfo, SupplierData supplierData) {
        if (roomInfo == null || supplierData == null) {
            return null;
        }
        
        HourlyRoomInfo.HourlyRoomInfoBuilder hourlyRoomInfoBuilder = HourlyRoomInfo.builder();
        hourlyRoomInfoBuilder.hourlyRoom(roomInfo.getHourlyRoom());
        hourlyRoomInfoBuilder.checkInDate(supplierData.getCheckInDate());
        QueryHotelDetailCommonRespDto.HourlyRoomInfo hourlyRoomInfo = roomInfo.getHourlyRoomInfo();
        if (hourlyRoomInfo != null) {
            hourlyRoomInfoBuilder.hourlyRoomTip(hourlyRoomInfo.getHourlyRoomTips());
            hourlyRoomInfoBuilder.durationHour(hourlyRoomInfo.getDuration());
            hourlyRoomInfoBuilder.intervalStartMinute(hourlyRoomInfo.getIntervalStartTime());
            hourlyRoomInfoBuilder.intervalEndMinute(hourlyRoomInfo.getIntervalEndTime());
        }
        HourlyRoomInfo result = hourlyRoomInfoBuilder.build();
        result.generateDesc();
        return result;
    }
    
    private List<String> extractSpecialNotes(List<QueryHotelDetailCommonRespDto.SpecialNotice> specialNoticeList) {
        if (CollectionUtils.isEmpty(specialNoticeList)) {
            return null;
        }
        
        return specialNoticeList.stream()
                .filter(item -> item != null && StringUtils.isNotBlank(item.getNoticeValue()))
                .map(QueryHotelDetailCommonRespDto.SpecialNotice::getNoticeValue)
                .collect(Collectors.toList());
    }
    
    private List<MemberTagInfo> getRoomTagList(QueryHotelDetailCommonRespDto.RoomInfo roomInfo) {
        if (roomInfo == null || CollectionUtils.isEmpty(roomInfo.getSaleRoomTags())) {
            return Collections.emptyList();
        }

        return roomInfo.getSaleRoomTags().stream()
                .filter(Objects::nonNull)
                .filter(tag -> StringUtils.isNotBlank(tag.getTagCode()))
                .map(tag -> {
                    MemberTagInfo roomTagInfo = new MemberTagInfo();
                    roomTagInfo.setTagCode(tag.getTagCode());
                    roomTagInfo.setTagName(tag.getTagName());
                    roomTagInfo.setTagDesc(tag.getTagDesc());
                    return roomTagInfo;
                }).collect(Collectors.toList());
    }

    private PersonPrice buildPersonPrice(QueryHotelDetailCommonRespDto.BookingRules bookingRules) {
        if (bookingRules == null || bookingRules.getPersonPrice() == null) {
            return null;
        }

        QueryHotelDetailCommonRespDto.PersonPrice personPrice = bookingRules.getPersonPrice();
        PersonPrice res = new PersonPrice();
        res.setAdult(personPrice.getAdult());
        res.setRateId(personPrice.getRateId());
        return res;
    }

    private RoomPrice buildRoomPrice(QueryHotelDetailCommonRespDto.RoomInfo roomInfo, SupplierData supplierData) {
        if (roomInfo == null || supplierData == null || supplierData.getRoomQuantity() == null || supplierData.getDayQuantity() == null) {
            return null;
        }

        BigDecimal roomDayQuantity = BigDecimal.valueOf(supplierData.getDayQuantity()).multiply(BigDecimal.valueOf(supplierData.getRoomQuantity()));
        Boolean abroad = supplierData.getAbroad();
        BigDecimal avgSalePriceIncludeTax = roomInfo.getAvgSalePriceIncludeTax();
        BigDecimal salePriceIncludeTax = roomInfo.getSalePriceIncludeTax();
        BigDecimal salePrice = roomInfo.getSalePrice();
        BigDecimal avgSalePrice = roomInfo.getAvgSalePrice();
        RoomPrice taxRoomPrice = buildTaxRoomPrice(roomInfo.getTaxDetails(), abroad, roomDayQuantity);
        BigDecimal totalServiceCharge = Optional.ofNullable(roomInfo.getServiceChargeInfo())
                .map(QueryHotelDetailCommonRespDto.ServiceChargeInfo::getCustomChargePrice)
                .map(QueryHotelDetailCommonRespDto.Price::getPrice)
                .orElse(BigDecimal.ZERO);
        BigDecimal avgServiceCharge = Optional.ofNullable(roomInfo.getServiceChargeInfo())
                .map(QueryHotelDetailCommonRespDto.ServiceChargeInfo::getCustomChargePricePerRoomNights)
                .map(QueryHotelDetailCommonRespDto.Price::getPrice)
                .orElse(BigDecimal.ZERO);
        BigDecimal totalPriceIncludeTax = getTotalPriceIncludeTax(salePriceIncludeTax, avgSalePriceIncludeTax, roomDayQuantity);
        QueryHotelDetailCommonRespDto.ServiceChargeDetailInfo serviceChargeDetailInfo = getServiceChargeDetailInfo(roomInfo.getServiceChargeInfo());
        ServiceChargeStrategyEnum serviceChargeStrategyEnum = Optional.ofNullable(serviceChargeDetailInfo)
                .map(QueryHotelDetailCommonRespDto.ServiceChargeDetailInfo::getChargingStrategy)
                .map(ServiceChargeStrategyEnum::getEnum)
                .orElse(null);
        
        return RoomPrice.builder()
                .roomQuantity(supplierData.getRoomQuantity())
                .dayQuantity(supplierData.getDayQuantity())
                .dailyRateList(buildDailyRateList(roomInfo.getDailyRates(), abroad))
                .totalPriceIncludeTax(Price.builder()
                        .customPrice(totalPriceIncludeTax)
                        .customCurrency(HotelCoreConstant.CNY).build())
                .avgPriceIncludeTax(Boolean.TRUE.equals(commonGateway.checkPrice(avgSalePriceIncludeTax)) ? Price.builder()
                        .customPrice(avgSalePriceIncludeTax)
                        .customCurrency(HotelCoreConstant.CNY).build() : null)
                .totalPriceExcludeTax(buildTotalPriceExcludeTax(salePrice, avgSalePrice, roomDayQuantity, abroad))
                .avgPriceExcludeTax(Boolean.TRUE.equals(commonGateway.checkPrice(avgSalePrice)) ? Price.builder()
                        .customPrice(avgSalePrice)
                        .customCurrency(HotelCoreConstant.CNY).build() : null)
                .totalRoomTax(taxRoomPrice == null ? null : taxRoomPrice.getTotalRoomTax())
                .totalRoomTaxDetailList(taxRoomPrice == null ? null : taxRoomPrice.getTotalRoomTaxDetailList())
                .totalExtraTax(taxRoomPrice == null ? null : taxRoomPrice.getTotalExtraTax())
                .totalExtraTaxDetailList(taxRoomPrice == null ? null : taxRoomPrice.getTotalExtraTaxDetailList())
                .avgExtraTax(taxRoomPrice == null ? null : taxRoomPrice.getAvgExtraTax())
                .serviceChargeList(supplierData.getServiceChargeList())
                .resourcePriceIncludeServiceCharge(supplierData.getResourcePriceIncludeServiceCharge())
                .totalServiceCharge(Price.builder()
                        .customPrice(totalServiceCharge)
                        .customCurrency(HotelCoreConstant.CNY)
                        .build())
                .avgServiceCharge(Price.builder()
                        .customPrice(avgServiceCharge)
                        .customCurrency(HotelCoreConstant.CNY)
                        .build())
                .serviceChargeStrategyEnum(serviceChargeStrategyEnum)
                .serviceChargeStrategyValue(serviceChargeDetailInfo == null || serviceChargeDetailInfo.getCustomChargePricePerUnit() == null || Objects.equals(serviceChargeStrategyEnum, ServiceChargeStrategyEnum.FIXED_RATIO)? null : serviceChargeDetailInfo.getCustomChargePricePerUnit().getPrice())
                .totalPriceIncludeTaxAndServiceCharge(totalPriceIncludeTax == null ? null : Price.builder()
                        .customPrice(totalPriceIncludeTax.add(totalServiceCharge))
                        .customCurrency(HotelCoreConstant.CNY)
                        .build())
                .avgPriceIncludeTaxAndServiceCharge(avgSalePriceIncludeTax == null ? null : Price.builder()
                        .customPrice(avgSalePriceIncludeTax.add(avgServiceCharge))
                        .customCurrency(HotelCoreConstant.CNY)
                        .build())
                .build();
    }
    
    private QueryHotelDetailCommonRespDto.ServiceChargeDetailInfo getServiceChargeDetailInfo(QueryHotelDetailCommonRespDto.ServiceChargeInfo serviceChargeInfo) {
        if (serviceChargeInfo == null || CollectionUtils.isEmpty(serviceChargeInfo.getServiceChargeDetailInfoList())) {
            return null;
        }
        
        // 复杂明细直接跳过
        if (serviceChargeInfo.getServiceChargeDetailInfoList().size() > 1) {
            return null;
        }
        
        // 仅支持普通类型
        QueryHotelDetailCommonRespDto.ServiceChargeDetailInfo serviceChargeDetailInfo = serviceChargeInfo.getServiceChargeDetailInfoList().get(0);
        if (serviceChargeDetailInfo == null || !StringUtils.equalsIgnoreCase(serviceChargeDetailInfo.getChargeType(), "ORDINARY")) {
            return null;
        }
        
        return serviceChargeDetailInfo;
    }
    
    private Price buildTotalPriceExcludeTax(BigDecimal salePrice, BigDecimal avgSalePrice, BigDecimal roomDayQuantity, Boolean abroad) {
        if (Boolean.TRUE.equals(commonGateway.checkPrice(salePrice))) {
            return Price.builder()
                    .customPrice(salePrice)
                    .customCurrency(HotelCoreConstant.CNY).build();
        }
        
        if (Boolean.TRUE.equals(commonGateway.checkPrice(avgSalePrice)) && roomDayQuantity != null) {
            return Price.builder()
                    .customPrice(avgSalePrice.multiply(roomDayQuantity))
                    .customCurrency(HotelCoreConstant.CNY).build();
        }
        
        return null;
    }
    
    private BigDecimal getTotalPriceIncludeTax(BigDecimal salePriceIncludeTax, BigDecimal avgSalePriceIncludeTax, BigDecimal roomDayQuantity) {
        if (Boolean.TRUE.equals(commonGateway.checkPrice(salePriceIncludeTax))) {
            return salePriceIncludeTax;
        }
        
        if (Boolean.TRUE.equals(commonGateway.checkPrice(avgSalePriceIncludeTax)) && roomDayQuantity != null) {
            return avgSalePriceIncludeTax.multiply(roomDayQuantity);
        }
        
        return null;
    }

    private RoomPrice buildTaxRoomPrice(List<QueryHotelDetailCommonRespDto.TaxDetail> taxDetailList, Boolean abroad, BigDecimal roomDayQuantity) {
        if (CollectionUtils.isEmpty(taxDetailList)) {
            return null;
        }

        BigDecimal totalOriginExtraTax = BigDecimal.ZERO;
        BigDecimal totalCustomExtraTax = BigDecimal.ZERO;
        BigDecimal totalOriginRoomTax = BigDecimal.ZERO;
        BigDecimal totalCustomRoomTax = BigDecimal.ZERO;
        String originCurrency = null;
        String customCurrency = null;
        List<Price> totalRoomTaxDetailList = new ArrayList<>();
        List<Price> totalExtraTaxDetailList = new ArrayList<>();

        for (QueryHotelDetailCommonRespDto.TaxDetail taxDetail : taxDetailList) {
            if (taxDetail == null || (taxDetail.getAmount() == null && taxDetail.getCustomAmount() == null)) {
                continue;
            }
            String taxTypeName = taxDetail.getTaxTypeName();
            originCurrency = taxDetail.getCurrency();
            customCurrency = taxDetail.getCustomCurrency();
            BigDecimal amount = taxDetail.getAmount();
            BigDecimal customAmount = taxDetail.getCustomAmount();
            // 不包含在订单中的税费
            if (Boolean.FALSE.equals(taxDetail.getIncludeInTotalPrice())) {
                totalOriginExtraTax = totalOriginExtraTax.add(taxDetail.getAmount());
                totalCustomExtraTax = totalCustomExtraTax.add(taxDetail.getCustomAmount());
                totalExtraTaxDetailList.add(Price.builder()
                        .name(taxTypeName)
                        .originCurrency(originCurrency)
                        .originPrice(amount)
                        .customCurrency(customCurrency)
                        .customPrice(customAmount).build());
            }
            // 包含在订单中的税费
            else if (Boolean.TRUE.equals(taxDetail.getIncludeInTotalPrice())) {
                totalOriginRoomTax = totalOriginRoomTax.add(taxDetail.getAmount());
                totalCustomRoomTax = totalCustomRoomTax.add(taxDetail.getCustomAmount());
                totalRoomTaxDetailList.add(Price.builder()
                        .name(taxTypeName)
                        .originCurrency(originCurrency)
                        .originPrice(amount)
                        .customCurrency(customCurrency)
                        .customPrice(customAmount).build());
            }
        }

        return RoomPrice.builder()
                .totalRoomTaxDetailList(totalRoomTaxDetailList)
                .totalExtraTaxDetailList(totalExtraTaxDetailList)
                .totalRoomTax(Price.builder()
                        .originCurrency(originCurrency)
                        .originPrice(totalOriginRoomTax)
                        .customCurrency(customCurrency)
                        .customPrice(totalCustomRoomTax).build())
                .totalExtraTax(Price.builder()
                        .originCurrency(originCurrency)
                        .originPrice(totalOriginExtraTax)
                        .customCurrency(customCurrency)
                        .customPrice(totalCustomExtraTax).build())
                .avgExtraTax(Price.builder()
                        .originCurrency(originCurrency)
                        .originPrice(AmountPrecisionUtil.divideByBizTypeContext(totalOriginExtraTax, roomDayQuantity))
                        .customCurrency(customCurrency)
                        .customPrice(AmountPrecisionUtil.divideByBizTypeContext(totalCustomExtraTax, roomDayQuantity)).build()).build();
    }

    private List<DailyRate> buildDailyRateList(List<QueryHotelDetailCommonRespDto.DailyRate> dailyRateList, Boolean abroad) {
        if (CollectionUtils.isEmpty(dailyRateList)) {
            return null;
        }

        List<DailyRate> resultList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.DailyRate dailyRate : dailyRateList) {
            if (dailyRate == null || !commonGateway.checkPrice(dailyRate.getSalePriceIncludeTax())) {
                continue;
            }
            try {
                resultList.add(DailyRate.builder()
                        .date(DateUtil.format(DateUtil.parse(dailyRate.getEffectDate()), HotelCoreConstant.DATE_FORMAT))
                        .avgPriceIncludeTax(Price.builder()
                                .customCurrency(HotelCoreConstant.CNY)
                                .customPrice(dailyRate.getSalePriceIncludeTax()).build()).build());
            } catch (Exception e) {
                log.error("buildDailyRateList error", e);
            }
        }

        return resultList.stream().sorted(Comparator.comparing(item -> Null.or(item.getDate(), HotelCoreConstant.DEFAULT_TIME))).collect(Collectors.toList());
    }

    private RoomPolicyService buildRoomPolicyService(QueryHotelDetailCommonRespDto.RoomInfo roomInfo) {
        if (roomInfo == null) {
            return null;
        }

        QueryHotelDetailCommonRespDto.BookingRules bookingRules = roomInfo.getBookingRules();
        QueryHotelDetailCommonRespDto.InvoiceInfo invoiceInfo = roomInfo.getInvoiceInfo();
        List<QueryHotelDetailCommonRespDto.SaleRoomTag> saleRoomTagList = roomInfo.getSaleRoomTags();
        List<QueryHotelDetailCommonRespDto.DailyRate> dailyRateList = roomInfo.getDailyRates();
        QueryHotelDetailCommonRespDto.ApplicativeAreaInfo applicativeAreaInfo = bookingRules == null
                ? null : bookingRules.getApplicativeAreaInfo();

        return RoomPolicyService.builder()
                .applicativeArea(applicativeAreaInfo == null ? null : ApplicativeArea.builder()
                        .desc(applicativeAreaInfo.getApplicativeAreaDesc())
                        .name(applicativeAreaInfo.getApplicativeAreaTitle()).build())
                .cancelPolicy(buildCancelPolicy(bookingRules))
                .justifyConfirm(bookingRules == null ? null : bookingRules.getJustifyConfirm())
                .hotelBonusPoint(CollectionUtils.isNotEmpty(saleRoomTagList) && saleRoomTagList.stream().anyMatch(item -> StringUtils.equalsIgnoreCase("JDJF", item.getTagCode()) || StringUtils.equalsIgnoreCase("JYJF", item.getTagCode())))
                .supportSpecialInvoice(invoiceInfo != null && Boolean.TRUE.equals(invoiceInfo.getHasSpecialInvoice()))
                .breakfastCount(CollectionUtils.isNotEmpty(dailyRateList) && dailyRateList.get(0) != null ? dailyRateList.get(0).getMeals() : null)
                .mealTypeEnum(MealTypeEnum.getEnum(Optional.ofNullable(roomInfo.getRoomMealInfo())
                        .map(QueryHotelDetailCommonRespDto.RoomMealInfo::getMealType)
                        .map(Object::toString)
                        .orElse(null)))
                .mealInfoList(buildMealInfoList(roomInfo.getDailyRates()))
                .specialNotes(extractSpecialNotes(Optional.ofNullable(roomInfo.getRoomStaticInfo())
                        .map(QueryHotelDetailCommonRespDto.RoomStaticInfo::getSpecialNoticeList)
                        .orElse(null)))
                .build();
    }
    
    private List<MealInfo> buildMealInfoList(List<QueryHotelDetailCommonRespDto.DailyRate> dailyRateList) {
        if (CollectionUtils.isEmpty(dailyRateList)) {
            return null;
        }
        
        List<MealInfo> mealInfoList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.DailyRate dailyRate : dailyRateList) {
            if (dailyRate == null) {
                continue;
            }
            mealInfoList.add(MealInfo.builder()
                    .mealCount(dailyRate.getMeals())
                    .effectDate(dailyRate.getEffectDate())
                    .build());
        }
        return mealInfoList;
    }
    
    private CancelPolicy buildCancelPolicy(QueryHotelDetailCommonRespDto.BookingRules bookingRules) {
        if (bookingRules == null || bookingRules.getCancelRuleInfo() == null) {
            return null;
        }

        // 阶梯取消
        List<QueryHotelDetailCommonRespDto.LadderDeductionInfo> ladderDeductionInfoList = bookingRules.getCancelRuleInfo().getLadderDeductionInfo();
        if (CollectionUtils.isNotEmpty(ladderDeductionInfoList) && ladderDeductionInfoList.stream().anyMatch(item -> item != null && item.getLadderDeductionDetailInfo() != null)) {
            List<StepCancelPolicy> stepCancelPolicyList = new ArrayList<>();
            for (QueryHotelDetailCommonRespDto.LadderDeductionInfo ladderDeductionInfo : ladderDeductionInfoList) {
                if (ladderDeductionInfo == null || ladderDeductionInfo.getLadderDeductionDetailInfo() == null) {
                    continue;
                }

                QueryHotelDetailCommonRespDto.Price customPrice = ladderDeductionInfo.getLadderDeductionDetailInfo().getCustomPrice();

                stepCancelPolicyList.add(StepCancelPolicy.builder().cancelRuleEnum(CancelRuleEnum.getEnum(ladderDeductionInfo.getDeductionType()))
                        .startTime(ladderDeductionInfo.getLadderDeductionDetailInfo().getStartDeductTime())
                        .endTime(ladderDeductionInfo.getLadderDeductionDetailInfo().getEndDeductTime())
                        .price(customPrice != null ? customPrice.getPrice() : null).build());
            }

            return CancelPolicy.builder()
                    .cancelRuleEnum(CancelRuleEnum.LADDER)
                    .stepCancelPolicyList(stepCancelPolicyList.stream().sorted(Comparator.comparing(
                            item -> Null.or(item.getEndTime(), HotelCoreConstant.DEFAULT_TIME))).collect(Collectors.toList())).build();
        }
        
        CancelRuleEnum cancelRuleEnum = CancelRuleEnum.getEnum(bookingRules.getCancelRuleInfo().getCancelRule());
        if (cancelRuleEnum == null) {
            cancelRuleEnum = CancelRuleEnum.UN_KNOWN;
        }
        
        // 限时取消
        if (Objects.equals(cancelRuleEnum, CancelRuleEnum.TIME_LIMIT)) {
            return CancelPolicy.builder()
                    .cancelRuleEnum(CancelRuleEnum.TIME_LIMIT)
                    .endFreeCancelTime(Optional.ofNullable(bookingRules.getCancelRuleInfo().getLastCancelTimeInfo())
                            .map(QueryHotelDetailCommonRespDto.LastCancelTimeInfo::getLastCancelTime)
                            .orElse(null))
                    .build();
        }
        
        return CancelPolicy.builder()
                .cancelRuleEnum(cancelRuleEnum)
                .build();
    }

    private RoomBaseInfo buildRoomBaseInfo(QueryHotelDetailCommonRespDto.RoomInfo roomInfo, SupplierData supplierData) {
        if (roomInfo == null || supplierData == null) {
            return null;
        }

        QueryHotelDetailCommonRespDto.RoomStaticInfo roomStaticInfo = Null.or(roomInfo.getRoomStaticInfo(), new QueryHotelDetailCommonRespDto.RoomStaticInfo());

        return RoomBaseInfo.builder()
                .floorDesc(StringUtils.isNotBlank(roomInfo.getFloorRange()) ? roomInfo.getFloorRange().replace("F", "").replace("f", "").replace("层", "") + "F" : null)
                .wifiDesc(roomInfo.getBroadBand())
                .smokeDesc(roomStaticInfo.getNonSmokeDesc())
                .maxGuestNum(roomInfo.getMaxGuestNumber())
                .bedDesc(getBedDesc(roomStaticInfo.getBedInfoList()))
                .areaDesc(supplierData.getAreaDesc())
                .roomBedTypeEnum(getRoomBedTypeEnum(roomStaticInfo.getBedInfoList(), roomInfo.getRoomName()))
                .parentBedDesc(getParentBedDesc(roomStaticInfo.getBedInfoList()))
                .windowEnum(getWindowEnum(roomStaticInfo.getWindowInfo())).build();
    }
    
    private WindowEnum getWindowEnum(QueryHotelDetailCommonRespDto.WindowInfo windowInfo) {
        if (windowInfo == null) {
            return WindowEnum.UNKNOWN;
        }
        
        WindowEnum windowEnum;
        // 用ID找
        Integer windowType = windowInfo.getWindowType();
        if (windowType != null) {
            windowEnum = WindowEnum.getEnum(windowType.toString());
            if (windowEnum != null) {
                return windowEnum;
            }
        }
        
        // 用名称找
        windowEnum = WindowEnum.getEnum(windowInfo.getWindowTypeName());
        if (windowEnum != null) {
            return windowEnum;
        }
        
        return WindowEnum.UNKNOWN;
    }
    
    private String getParentBedDesc(List<QueryHotelDetailCommonRespDto.BedInfo> bedInfoList) {
        if (CollectionUtils.isEmpty(bedInfoList)) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        for (QueryHotelDetailCommonRespDto.BedInfo bedInfo : bedInfoList) {
            if (bedInfo == null || StringUtils.isBlank(bedInfo.getParentBedTypeName())) {
                continue;
            }
            sb.append(bedInfo.getParentBedTypeName()).append("或");
        }
        
        if (sb.toString().endsWith("或")) {
            sb.deleteCharAt(sb.length() - 1);
        }
        
        return sb.toString();
    }
    
    private RoomBedTypeEnum getRoomBedTypeEnum(List<QueryHotelDetailCommonRespDto.BedInfo> bedInfoList, String roomName) {
        RoomBedTypeEnum roomBedTypeEnum = getRoomBedTypeEnum(bedInfoList);
        if (roomBedTypeEnum != null) {
            return roomBedTypeEnum;
        }
        if (StringUtils.isBlank(roomName)) {
            return null;
        }
        if (roomName.contains("大床")) {
            return RoomBedTypeEnum.KING_ROOM;
        }
        if (roomName.contains("双床")) {
            return RoomBedTypeEnum.TWIN_ROOM;
        }
        return null;
    }

    private RoomBedTypeEnum getRoomBedTypeEnum(List<QueryHotelDetailCommonRespDto.BedInfo> bedInfoList) {
        if (CollectionUtils.isEmpty(bedInfoList)) {
            return null;
        }

        // 找大床房
        if (bedInfoList.stream().filter(Objects::nonNull)
                .map(item -> ParentBedEnum.getEnum(item.getParentBedTypeName())).anyMatch(item -> (item == ParentBedEnum.QUEEN_BED || item == ParentBedEnum.SINGLE_BED))) {
            return RoomBedTypeEnum.KING_ROOM;
        }

        // 找双床
        for (QueryHotelDetailCommonRespDto.BedInfo bedInfo : bedInfoList) {
            if (bedInfo == null) {
                continue;
            }
            ParentBedEnum parentBedEnum = ParentBedEnum.getEnum(bedInfo.getParentBedTypeName());
            if (parentBedEnum == null) {
                continue;
            }

            if (parentBedEnum == ParentBedEnum.TWIN_BED) {
                return RoomBedTypeEnum.TWIN_ROOM;
            }

            if (parentBedEnum == ParentBedEnum.MULTIPLE_BED && CollectionUtils.isNotEmpty(bedInfo.getChildBedInfoList())
                    && bedInfo.getChildBedInfoList().stream().filter(item -> item != null && item.getBedCount() != null)
                    .mapToInt(QueryHotelDetailCommonRespDto.ChildBedInfo::getBedCount).sum() == 2) {
                return RoomBedTypeEnum.TWIN_ROOM;
            }
        }

        return null;
    }

    private Integer getProtocolType(String roomType, Boolean tmcPrice) {
        if (!StringUtils.equalsIgnoreCase(RoomTypeEnum.C.getCode(), roomType)) {
            return null;
        }
        return Boolean.TRUE.equals(tmcPrice) ? 2 : 3;
    }

    private String getBedDesc(QueryHotelDetailCommonRespDto.BasicRoomStaticInfo basicRoomStaticInfo, List<QueryHotelDetailCommonRespDto.RoomInfo> roomInfoList) {
        String bedDesc = null;
        if (basicRoomStaticInfo != null) {
            bedDesc = getBedDesc(basicRoomStaticInfo.getBedInfoList());
        }
        if (StringUtils.isNotBlank(bedDesc)) {
            return bedDesc;
        }

        if (CollectionUtils.isEmpty(roomInfoList) || roomInfoList.get(0) == null || roomInfoList.get(0).getRoomStaticInfo() == null) {
            return null;
        }
        return getBedDesc(roomInfoList.get(0).getRoomStaticInfo().getBedInfoList());
    }

    private List<String> buildPictureList(QueryHotelDetailCommonRespDto.BasicRoomStaticInfo basicRoomStaticInfo, SupplierData supplierData) {
        Set<String> pictureSet = new HashSet<>();
        String supplierCode = Optional.ofNullable(supplierData).map(SupplierData::getSupplierCode).orElse(null);

        if (basicRoomStaticInfo != null && CollectionUtils.isNotEmpty(basicRoomStaticInfo.getBasicRoomImageUrl())) {
            pictureSet.addAll(commonDomainService.zoomOutPictureList(supplierCode, basicRoomStaticInfo.getBasicRoomImageUrl()));
        }

        if (supplierData != null && StringUtils.isNotBlank(supplierData.getBasicRoomId()) && CollectionUtils.isNotEmpty(supplierData.getPictureListMap())) {
            pictureSet.addAll(supplierData.getPictureListMap().getOrDefault(supplierData.getBasicRoomId(), new ArrayList<>(0)).stream().filter(Objects::nonNull)
                    .map(item -> commonDomainService.zoomOutPicture(supplierCode, item.getUrl())).collect(Collectors.toList()));
        }

        return new ArrayList<>(pictureSet);
    }

    private List<FacilityGroup> buildBasicRoomFacilityGroupList(List<FacilityGroup> facilityGroupList, String basicRoomId) {
        if (CollectionUtils.isEmpty(facilityGroupList) || StringUtils.isBlank(basicRoomId)) {
            return null;
        }
        List<FacilityGroup> resultList = new ArrayList<>();
        for (FacilityGroup facilityGroup : facilityGroupList) {
            if (facilityGroup == null || CollectionUtils.isEmpty(facilityGroup.getFacilityList())) {
                continue;
            }
            List<Facility> facilityList = new ArrayList<>();
            for (Facility facility : facilityGroup.getFacilityList()) {
                if (facility == null || CollectionUtils.isEmpty(facility.getBasicRoomIdSet()) || !facility.getBasicRoomIdSet().contains(basicRoomId)) {
                    continue;
                }
                facilityList.add(facility);
            }
            if (CollectionUtils.isEmpty(facilityList)) {
                continue;
            }
            resultList.add(FacilityGroup.builder()
                    .facilityList(facilityList)
                    .name(facilityGroup.getName()).build());
        }
        return resultList;
    }

    private String getBasicRoomId(String masterBasicRoomId, List<QueryHotelDetailCommonRespDto.RoomInfo> roomInfoList, String supplierCode) {
        if (!StringUtils.equalsIgnoreCase(HotelCoreConstant.MEIYA, supplierCode)) {
            return masterBasicRoomId;
        }
        if (CollectionUtils.isEmpty(roomInfoList) || roomInfoList.get(0) == null || StringUtils.isBlank(roomInfoList.get(0).getRoomId())) {
            return masterBasicRoomId;
        }
        String[] split = roomInfoList.get(0).getRoomId().split(HotelCoreConstant.VERTICAL_LINE);
        if (split.length < 2) {
            return masterBasicRoomId;
        }
        return roomInfoList.get(0).getRoomId().split(HotelCoreConstant.VERTICAL_LINE)[1];
    }

    private List<NearByGroup> buildNearByGroupList(QueryHotelDetailCommonRespDto.HotelDetailInfo hotelDetailInfo) {
        if (hotelDetailInfo == null) {
            return null;
        }

        List<NearByGroup> nearByGroupList = new ArrayList<>();
        if (hotelDetailInfo.getHotelFacilityInfo() != null && CollectionUtils.isNotEmpty(hotelDetailInfo.getHotelFacilityInfo().getNearByFacilityGroupList())) {
            for (QueryHotelDetailCommonRespDto.NearByFacilityGroup nearByFacilityGroup : hotelDetailInfo.getHotelFacilityInfo().getNearByFacilityGroupList()) {
                if (nearByFacilityGroup == null || StringUtils.isBlank(nearByFacilityGroup.getNearByFacilityGroupName()) || CollectionUtils.isEmpty(nearByFacilityGroup.getNearByFacilityNameList())) {
                    continue;
                }
                List<NearBy> nearByList = new ArrayList<>();
                for (String nearByFacilityName : nearByFacilityGroup.getNearByFacilityNameList()) {
                    if (StringUtils.isBlank(nearByFacilityName)) {
                        continue;
                    }
                    nearByList.add(NearBy.builder()
                            .name(nearByFacilityName).build());
                }
                nearByGroupList.add(NearByGroup.builder()
                        .nearByGroupTypeEnum(NearByGroupTypeEnum.FACILITY)
                        .name(nearByFacilityGroup.getNearByFacilityGroupName())
                        .nearByList(nearByList).build());
            }
        }

        if (CollectionUtils.isNotEmpty(hotelDetailInfo.getHotelTrafficInfoGroupList())) {
            for (QueryHotelDetailCommonRespDto.HotelTrafficInfoGroup hotelTrafficInfoGroup : hotelDetailInfo.getHotelTrafficInfoGroupList()) {
                if (hotelTrafficInfoGroup == null || StringUtils.isBlank(hotelTrafficInfoGroup.getTrafficInfoGroupName()) || CollectionUtils.isEmpty(hotelTrafficInfoGroup.getTrafficInfoList())) {
                    continue;
                }
                List<NearBy> nearByList = new ArrayList<>();
                for (QueryHotelDetailCommonRespDto.TrafficInfo trafficInfo : hotelTrafficInfoGroup.getTrafficInfoList()) {
                    if (trafficInfo == null || StringUtils.isBlank(trafficInfo.getLandMarkName())) {
                        continue;
                    }
                    nearByList.add(NearBy.builder()
                            .name(trafficInfo.getLandMarkName())
                            .distanceDesc(trafficInfo.getTrafficInfoDes()).build());
                }
                nearByGroupList.add(NearByGroup.builder()
                        .nearByGroupTypeEnum(NearByGroupTypeEnum.TRAFFIC)
                        .name(hotelTrafficInfoGroup.getTrafficInfoGroupName())
                        .nearByList(nearByList).build());
            }
        }

        // 合并去重
        if (CollectionUtils.isNotEmpty(nearByGroupList)) {
            Map<String, NearByGroup> nearByGroupMap = new HashMap<>();
            for (NearByGroup nearByGroup : nearByGroupList) {
                if (nearByGroup == null || StringUtils.isBlank(nearByGroup.getName()) || CollectionUtils.isEmpty(nearByGroup.getNearByList())) {
                    continue;
                }
                NearByGroup tmpNearByGroup = nearByGroupMap.getOrDefault(nearByGroup.getName(), null);
                if (tmpNearByGroup == null) {
                    tmpNearByGroup = nearByGroup;
                } else {
                    List<NearBy> nearByList = Null.or(tmpNearByGroup.getNearByList(), new ArrayList<>(0));
                    nearByList.addAll(nearByGroup.getNearByList());
                    tmpNearByGroup.setNearByList(nearByList);
                }
                nearByGroupMap.put(tmpNearByGroup.getName(), tmpNearByGroup);
            }
            nearByGroupList = new ArrayList<>(nearByGroupMap.values());
        }
        return nearByGroupList;
    }

    private List<ReservationNotice> buildReservationNoticeList(QueryHotelDetailCommonRespDto.HotelDetailInfo hotelDetailInfo) {
        if (hotelDetailInfo == null || CollectionUtils.isEmpty(hotelDetailInfo.getReservationNoticeTip())) {
            return null;
        }
        List<QueryHotelDetailCommonRespDto.ReservationNoticeTip> reservationNoticeTipList = hotelDetailInfo.getReservationNoticeTip();

        List<ReservationNotice> reservationNoticeList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.ReservationNoticeTip reservationNoticeTip : reservationNoticeTipList) {
            if (reservationNoticeTip == null || CollectionUtils.isEmpty(reservationNoticeTip.getNoticeTipDetail())) {
                continue;
            }
            for (QueryHotelDetailCommonRespDto.NoticeTipDetail tipDetail : reservationNoticeTip.getNoticeTipDetail()) {
                if (tipDetail == null || CollectionUtils.isEmpty(tipDetail.getNoticeTipItem())) {
                    continue;
                }
                reservationNoticeList.add(ReservationNotice.builder()
                        .title(tipDetail.getSubTitle())
                        .desc(StringUtils.join(tipDetail.getNoticeTipItem().stream().filter(Objects::nonNull)
                                .map(QueryHotelDetailCommonRespDto.NoticeTipItem::getContent).collect(Collectors.toList()), "")).build());
            }
        }
        return reservationNoticeList;
    }

    private HotelPolicyService buildHotelPolicyService(QueryHotelDetailCommonRespDto.HotelDetailInfo hotelDetailInfo) {
        if (hotelDetailInfo == null || hotelDetailInfo.getHotelPolicyInfo() == null) {
            return null;
        }
        QueryHotelDetailCommonRespDto.HotelPolicyInfo hotelPolicyInfo = hotelDetailInfo.getHotelPolicyInfo();
        QueryHotelDetailCommonRespDto.ArrivalAndDeparture arrivalAndDeparture = hotelPolicyInfo.getArrivalAndDeparture();
        QueryHotelDetailCommonRespDto.MealPolicy mealPolicy = hotelPolicyInfo.getMealPolicy();
        QueryHotelDetailCommonRespDto.ChildAndAddBed childAndAddBed = hotelPolicyInfo.getChildAndAddBed();

        return HotelPolicyService.builder()
                .petPolicyDesc(hotelPolicyInfo.getPetPolicy())
                .arrivalAndDeparturePolicy(ArrivalAndDeparturePolicy.builder()
                        .arrivalDesc(arrivalAndDeparture == null ? null : arrivalAndDeparture.getArrivalDesc())
                        .departureDesc(arrivalAndDeparture == null ? null : arrivalAndDeparture.getDepartureDesc()).build())
                .mealPolicy(MealPolicy.builder()
                        .desc(mealPolicy == null ? null : mealPolicy.getBreakfastDesc())
                        .type(mealPolicy == null ? null : mealPolicy.getBreakfastType())
                        .style(mealPolicy == null ? null : mealPolicy.getBreakfastStyle())
                        .price(mealPolicy == null ? null : mealPolicy.getBreakfastPrice())
                        .openTimeDesc(mealPolicy == null ? null : mealPolicy.getOpenTime()).build())
                .childAndAddBedPolicy(ChildAndAddBedPolicy.builder()
                        .descList(childAndAddBed == null || StringUtils.isBlank(childAndAddBed.getChildLimitRule())
                                ? null : Arrays.asList(childAndAddBed.getChildLimitRule(), HotelCoreConstant.CHILD_AND_ADD_BED_POLICY_DESC))
                        .hotelRemarkDesc(childAndAddBed == null ? null : childAndAddBed.getHotelRemarks())
                        .specialRemarkDesc(childAndAddBed == null ? null : childAndAddBed.getSpecialRemarks()).build())
                .paymentToolList(buildPaymentToolList(hotelPolicyInfo.getCreditCardInfo()))
                .build();
    }

    private List<PaymentTool> buildPaymentToolList(List<QueryHotelDetailCommonRespDto.CreditCardInfo> creditCardInfoList) {
        if (CollectionUtils.isEmpty(creditCardInfoList)) {
            return null;
        }
        List<PaymentTool> paymentToolList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.CreditCardInfo creditCardInfo : creditCardInfoList) {
            if (creditCardInfo == null) {
                continue;
            }
            paymentToolList.add(PaymentTool.builder()
                    .iconUrl(creditCardInfo.getCreditCardIconUrl())
                    .name(creditCardInfo.getCreditCardName()).build());
        }
        return paymentToolList;
    }

    private HotelFacility buildFacility(QueryHotelDetailCommonRespDto.HotelDetailInfo hotelDetailInfo) {
        if (hotelDetailInfo == null || hotelDetailInfo.getHotelFacilityInfo() == null || hotelDetailInfo.getHotelFacilityInfo().getFacilityDetail() == null) {
            return null;
        }

        QueryHotelDetailCommonRespDto.FacilityDetail facilityDetail = hotelDetailInfo.getHotelFacilityInfo().getFacilityDetail();

        return HotelFacility.builder()
                .facilityGroupList(buildFacilityGroupList(facilityDetail.getFacilityGroupList(), true))
                .parkingLotList(buildParkingLotList(facilityDetail.getParkingPolicyInfo()))
                .chargingPileList(buildChargingPileList(facilityDetail.getParkingPolicyInfo())).build();
    }

    private List<ChargingPile> buildChargingPileList(QueryHotelDetailCommonRespDto.ParkingPolicyInfo parkingPolicyInfo) {
        if (parkingPolicyInfo == null || CollectionUtils.isEmpty(parkingPolicyInfo.getChargingPointList())) {
            return null;
        }

        List<ChargingPile> chargingPileList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.ChargingPoint chargingPoint : parkingPolicyInfo.getChargingPointList()) {
            if (chargingPoint == null) {
                continue;
            }
            chargingPileList.add(ChargingPile.builder()
                    .locationDesc(chargingPoint.getLocationDesc())
                    .typeDesc(chargingPoint.getTypeDesc()).build());
        }
        return chargingPileList;
    }

    private List<ParkingLot> buildParkingLotList(QueryHotelDetailCommonRespDto.ParkingPolicyInfo parkingPolicyInfo) {
        if (parkingPolicyInfo == null || CollectionUtils.isEmpty(parkingPolicyInfo.getParkingServiceInfoList())) {
            return null;
        }

        List<ParkingLot> parkingLotList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.ParkingServiceInfo parkingServiceInfo : parkingPolicyInfo.getParkingServiceInfoList()) {
            if (parkingServiceInfo == null || parkingServiceInfo.getParkingserviceDetail() == null) {
                continue;
            }
            QueryHotelDetailCommonRespDto.ParkingServiceDetail parkingserviceDetail = parkingServiceInfo.getParkingserviceDetail();
            parkingLotList.add(ParkingLot.builder()
                    .reservedDesc(parkingserviceDetail.getReservedDesc())
                    .locationDesc(parkingserviceDetail.getLocationDesc())
                    .typeDesc(parkingserviceDetail.getTypeDesc())
                    .chargeableDesc(parkingserviceDetail.getChargeableDesc()).build());
        }
        return parkingLotList;
    }

    private List<FacilityGroup> buildFacilityGroupList(List<QueryHotelDetailCommonRespDto.FacilityGroup> facilityGroupList, Boolean hotel) {
        if (CollectionUtils.isEmpty(facilityGroupList)) {
            return null;
        }

        List<FacilityGroup> resultList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.FacilityGroup facilityGroup : facilityGroupList) {
            if (facilityGroup == null || CollectionUtils.isEmpty(facilityGroup.getFacilityItemList())) {
                continue;
            }
            List<Facility> facilityList = new ArrayList<>();
            for (QueryHotelDetailCommonRespDto.FacilityItem facilityItem : facilityGroup.getFacilityItemList()) {
                if (facilityItem == null) {
                    continue;
                }
                if (Boolean.TRUE.equals(hotel) && CollectionUtils.isNotEmpty(facilityItem.getMasterBasicRoomId())) {
                    continue;
                }
                facilityList.add(Facility.builder()
                        .basicRoomIdSet(CollectionUtils.isEmpty(facilityItem.getMasterBasicRoomId()) ? null : new HashSet<>(facilityItem.getMasterBasicRoomId()))
                        .name(facilityItem.getFacilityItemName())
                        .charge(facilityItem.getChargeInfo() == null ? null : facilityItem.getChargeInfo().getChargeable()).build());
            }
            resultList.add(FacilityGroup.builder()
                    .name(facilityGroup.getFacilityGroupName())
                    .facilityList(facilityList).build());
        }
        return resultList;
    }

    private HotelBaseInfo buildHotelBaseInfo(QueryHotelDetailCommonRespDto.HotelDetailInfo hotelDetailInfo, SupplierData supplierData) {
        if (hotelDetailInfo == null || supplierData == null) {
            return null;
        }

        QueryHotelDetailCommonRespDto.HotelBaseInfo hotelBaseInfo = hotelDetailInfo.getHotelBaseInfo();
        QueryHotelDetailCommonRespDto.HotelPositionInfo hotelPositionInfo = hotelBaseInfo == null
                || hotelBaseInfo.getHotelPositionInfo() == null
                ? null : hotelBaseInfo.getHotelPositionInfo();
        QueryHotelDetailCommonRespDto.CoordinateInfo coordinateInfo = getCoordinateInfo(hotelBaseInfo);
        String telephone = hotelBaseInfo == null
                || hotelBaseInfo.getHotelContactInfo() == null
                || StringUtils.isBlank(hotelBaseInfo.getHotelContactInfo().getTelephone())
                ? null : hotelBaseInfo.getHotelContactInfo().getTelephone();
        QueryHotelDetailCommonRespDto.HotelStarInfo hotelStarInfo = hotelBaseInfo == null ||
                hotelBaseInfo.getHotelStarInfo() == null
                ? null : hotelBaseInfo.getHotelStarInfo();

        return HotelBaseInfo.builder()
                .name(hotelBaseInfo == null
                        || StringUtils.isBlank(hotelBaseInfo.getHotelName())
                        ? null : hotelBaseInfo.getHotelName())
                .nameEn(hotelBaseInfo == null
                        || StringUtils.isBlank(hotelBaseInfo.getHotelEnName())
                        ? null : hotelBaseInfo.getHotelEnName())
                .address(hotelPositionInfo == null ? null : hotelPositionInfo.getHotelAddress())
                .districtName(hotelPositionInfo == null
                        || hotelPositionInfo.getLocationInfo() == null
                        ? null : hotelPositionInfo.getLocationInfo().getName())
                .cityName(hotelPositionInfo == null
                        || hotelPositionInfo.getCityInfo() == null
                        ? null : hotelPositionInfo.getCityInfo().getName())
                .lat(coordinateInfo == null ? null : coordinateInfo.getLat())
                .lon(coordinateInfo == null ? null : coordinateInfo.getLon())
                .pictureList(buildPictureList(hotelDetailInfo.getHotelBaseInfo(), supplierData))
                .logoUrl(getLogoUrl(hotelDetailInfo.getHotelBaseInfo(), supplierData))
                .videoList(buildVideoList(hotelDetailInfo.getHotelBaseInfo()))
                .telephone(StringUtils.isBlank(telephone) || StringUtils.equalsIgnoreCase(telephone, HotelCoreConstant.ZERO) ? null : telephone)
                .star(hotelStarInfo == null ? null : hotelStarInfo.getStarNum())
                .starLicence(hotelStarInfo != null && StringUtils.equalsIgnoreCase(hotelStarInfo.getIconType(), "STAR"))
                .levelName(hotelStarInfo == null ? null : HotelLevelEnum.getNameByStar(hotelStarInfo.getStarNum()))
                .reviewScore(hotelDetailInfo.getHotelCommentInfo() == null
                        || hotelDetailInfo.getHotelCommentInfo().getScoreInfo() == null
                        ? null : hotelDetailInfo.getHotelCommentInfo().getScoreInfo().getTotal())
                .facilityList(hotelDetailInfo.getHotelFacilityInfo() == null
                        || hotelDetailInfo.getHotelFacilityInfo().getFacilityList() == null
                        || hotelDetailInfo.getHotelFacilityInfo().getFacilityList().getFacilityList() == null
                        ? null : hotelDetailInfo.getHotelFacilityInfo().getFacilityList().getFacilityList().stream().map(QueryHotelDetailCommonRespDto.Facility::getFacilityName).collect(Collectors.toList()))
                .openDateDesc(getOpenRenovationDateDesc(hotelDetailInfo.getHotelIntroductionInfo(), HotelCoreConstant.OPEN_DESC))
                .renovationDateDesc(getOpenRenovationDateDesc(hotelDetailInfo.getHotelIntroductionInfo(), HotelCoreConstant.RENOVATION_DESC))
                .mapInfoList(getMapInfoList(hotelBaseInfo))
                .build();
    }
    
    private List<MapInfo> getMapInfoList(QueryHotelDetailCommonRespDto.HotelBaseInfo hotelBaseInfo) {
        List<QueryHotelDetailCommonRespDto.CoordinateInfo> coordinateInfoList = Optional.ofNullable(hotelBaseInfo)
                .map(QueryHotelDetailCommonRespDto.HotelBaseInfo::getHotelPositionInfo)
                .map(QueryHotelDetailCommonRespDto.HotelPositionInfo::getCoordinateInfoList)
                .orElse(null);
        if (CollectionUtils.isEmpty(coordinateInfoList)) {
            return null;
        }
        
        List<MapInfo> mapInfoList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.CoordinateInfo coordinateInfo : coordinateInfoList) {
            if (coordinateInfo == null) {
                continue;
            }
            
            mapInfoList.add(MapInfo.builder()
                    .lon(coordinateInfo.getLon())
                    .lat(coordinateInfo.getLat())
                    .mapType(MapTypeEnum.getEnum(coordinateInfo.getMapType()))
                    .build());
        }
        return mapInfoList;
    }
    
    private String getOpenRenovationDateDesc(QueryHotelDetailCommonRespDto.HotelIntroductionInfo hotelIntroductionInfo, String desc) {
        if (hotelIntroductionInfo == null || StringUtils.isBlank(hotelIntroductionInfo.getHotelOpenRenovationDesc())) {
            return null;
        }

        String hotelOpenRenovationDesc = hotelIntroductionInfo.getHotelOpenRenovationDesc();
        String[] split = hotelOpenRenovationDesc.replace(HotelCoreConstant.COMMA, HotelCoreConstant.FULL_STOP).split(HotelCoreConstant.FULL_STOP);

        for (String s : split) {
            if (s.contains(desc)) {
                return s.trim();
            }
        }
        return null;
    }

    private List<Video> buildVideoList(QueryHotelDetailCommonRespDto.HotelBaseInfo hotelBaseInfo) {
        if (hotelBaseInfo == null || CollectionUtils.isEmpty(hotelBaseInfo.getHotelVideoInfo())) {
            return null;
        }
        List<Video> videoList = new ArrayList<>();
        for (QueryHotelDetailCommonRespDto.HotelVideoInfo hotelVideoInfo : hotelBaseInfo.getHotelVideoInfo()) {
            if (hotelVideoInfo == null || StringUtils.isBlank(hotelVideoInfo.getVideoUrl())) {
                continue;
            }
            videoList.add(Video.builder()
                    .url(hotelVideoInfo.getVideoUrl())
                    .coverPictureUrl(hotelVideoInfo.getCoverPicUrl()).build());
        }
        return videoList;
    }

    private List<Picture> buildPictureList(QueryHotelDetailCommonRespDto.HotelBaseInfo hotelBaseInfo, SupplierData supplierData) {
        Set<Picture> pictureSet = new HashSet<>();
        String supplierCode = Optional.ofNullable(supplierData).map(SupplierData::getSupplierCode).orElse(null);

        if (supplierData != null && CollectionUtils.isNotEmpty(supplierData.getPictureListMap())) {
            pictureSet.addAll(supplierData.getPictureListMap().getOrDefault(HotelCoreConstant.HOTEL, new ArrayList<>(0)).stream().map(item -> Picture.builder()
                    .url(commonDomainService.zoomOutPicture(supplierCode, item.getUrl()))
                    .type(item.getType()).build()).collect(Collectors.toSet()));
        }

        if (hotelBaseInfo != null && hotelBaseInfo.getHotelPictureInfo() != null && CollectionUtils.isNotEmpty(hotelBaseInfo.getHotelPictureInfo().getCommonPictureList())) {
            for (QueryHotelDetailCommonRespDto.CommonPicture commonPicture : hotelBaseInfo.getHotelPictureInfo().getCommonPictureList()) {
                if (commonPicture == null || StringUtils.isBlank(commonPicture.getHotelLogoUrl())) {
                    continue;
                }

                String commonPictureTypeName = commonPicture.getCommonPictureTypeName();
                pictureSet.add(Picture.builder()
                        .url(commonDomainService.zoomOutPicture(supplierCode, commonPicture.getHotelLogoUrl()))
                        .type(StringUtils.isBlank(commonPictureTypeName) ? HotelCoreConstant.OTHER : commonPictureTypeName).build());
            }
        }
        return new ArrayList<>(pictureSet);
    }

    private String getLogoUrl(QueryHotelDetailCommonRespDto.HotelBaseInfo hotelBaseInfo, SupplierData supplierData) {
        if (hotelBaseInfo == null || hotelBaseInfo.getHotelPictureInfo() == null) {
            return null;
        }

        String supplierCode = Optional.ofNullable(supplierData).map(SupplierData::getSupplierCode).orElse(null);

        return commonDomainService.zoomOutPicture(supplierCode, hotelBaseInfo.getHotelPictureInfo().getHotelLogoUrl());
    }

    private QueryHotelDetailCommonRespDto.CoordinateInfo getCoordinateInfo(QueryHotelDetailCommonRespDto.HotelBaseInfo hotelBaseInfo) {
        if (hotelBaseInfo == null || hotelBaseInfo.getHotelPositionInfo() == null || CollectionUtils.isEmpty(hotelBaseInfo.getHotelPositionInfo().getCoordinateInfoList())) {
            return null;
        }

        List<QueryHotelDetailCommonRespDto.CoordinateInfo> coordinateInfoList = hotelBaseInfo.getHotelPositionInfo().getCoordinateInfoList();

        Map<String, QueryHotelDetailCommonRespDto.CoordinateInfo> coordinateInfoMap = new HashMap<>();
        for (QueryHotelDetailCommonRespDto.CoordinateInfo coordinateInfo : coordinateInfoList) {
            if (coordinateInfo == null || StringUtils.isBlank(coordinateInfo.getMapType()) || !Boolean.TRUE.equals(commonGateway.checkLatLon(coordinateInfo.getLat(), coordinateInfo.getLon()))) {
                continue;
            }
            coordinateInfoMap.put(coordinateInfo.getMapType(), coordinateInfo);
        }

        QueryHotelDetailCommonRespDto.CoordinateInfo coordinateInfo = coordinateInfoMap.get(MapTypeEnum.GAODE.getCode());
        if (coordinateInfo != null) {
            return coordinateInfo;
        }
        return coordinateInfoMap.get(MapTypeEnum.GOOGLE.getCode());
    }

    private SupplierData buildSupplierData(SupplierProduct supplierProduct, QueryHotelDetailCommonRespDto response, QueryHotelDetailRequest queryHotelDetailRequest) {
        if (supplierProduct == null || queryHotelDetailRequest == null) {
            return new SupplierData();
        }

        QueryHotelDetailCommonRespDto.HotelBaseInfo hotelBaseInfo = response == null
                || response.getHotelDetailInfo() == null
                || response.getHotelDetailInfo().getHotelBaseInfo() == null
                ? null : response.getHotelDetailInfo().getHotelBaseInfo();
        QueryHotelDetailCommonRespDto.HotelPositionInfo hotelPositionInfo = hotelBaseInfo == null
                || hotelBaseInfo.getHotelPositionInfo() == null
                ? null : hotelBaseInfo.getHotelPositionInfo();


        return SupplierData.builder()
                .serviceChargeList(supplierProduct.getServiceChargeList())
                .hotelId(queryHotelDetailRequest.getHotelId())
                .supplierCode(supplierProduct.getSupplierCode())
                .supplierName(supplierProduct.getSupplierName())
                .directSupplier(supplierProduct.getDirectSupplier())
                .dayQuantity(getDayQuantity(queryHotelDetailRequest.getCheckInDate(), queryHotelDetailRequest.getCheckOutDate()))
                .roomQuantity(queryHotelDetailRequest.getRoomQuantity())
                .abroad(queryHotelDetailRequest.getAbroad())
                .pictureListMap(buildPictureListMap(response))
                .district(hotelPositionInfo == null
                        || hotelPositionInfo.getLocationInfo() == null
                        ? null : District.builder()
                        .districtId(hotelPositionInfo.getLocationInfo().getId())
                        .districtName(hotelPositionInfo.getLocationInfo().getName()).build())
                .city(hotelPositionInfo == null
                        || hotelPositionInfo.getCityInfo() == null
                        ? null : City.builder()
                        .cityId(hotelPositionInfo.getCityInfo().getId())
                        .cityName(hotelPositionInfo.getCityInfo().getName()).build())
                .zoneList(hotelPositionInfo == null
                        || CollectionUtils.isEmpty(hotelPositionInfo.getZoneInfoList())
                        ? null : hotelPositionInfo.getZoneInfoList().stream().filter(Objects::nonNull).map(item -> Zone.builder()
                        .zoneId(item.getId())
                        .zoneName(item.getName()).build()).collect(Collectors.toList()))
                .brandId(hotelBaseInfo == null
                        || hotelBaseInfo.getHotelBrandInfo() == null
                        || StringUtils.isBlank(hotelBaseInfo.getHotelBrandInfo().getBrandId())
                        ? null : hotelBaseInfo.getHotelBrandInfo().getBrandId())
                .groupId(hotelBaseInfo == null
                        || hotelBaseInfo.getHotelBrandInfo() == null
                        || StringUtils.isBlank(hotelBaseInfo.getHotelBrandInfo().getGroupId())
                        ? null : response.getHotelDetailInfo().getHotelBaseInfo().getHotelBrandInfo().getGroupId())
                .facilityGroupList(response == null
                        || response.getHotelDetailInfo() == null
                        || response.getHotelDetailInfo().getHotelFacilityInfo() == null
                        || response.getHotelDetailInfo().getHotelFacilityInfo().getFacilityDetail() == null
                        || CollectionUtils.isEmpty(response.getHotelDetailInfo().getHotelFacilityInfo().getFacilityDetail().getFacilityGroupList())
                        ? null : buildFacilityGroupList(response.getHotelDetailInfo().getHotelFacilityInfo().getFacilityDetail().getFacilityGroupList(), false))
                .resourcePriceIncludeServiceCharge(queryHotelDetailRequest.getResourcePriceIncludeServiceCharge())
                .checkInDate(queryHotelDetailRequest.getCheckInDate())
                .build();
    }

    private Integer getDayQuantity(String checkInDate, String checkOutDate) {
        if (StringUtils.isBlank(checkInDate) || StringUtils.isBlank(checkOutDate)) {
            return null;
        }
        try {
            return (int) DateUtil.between(DateUtil.parse(checkInDate), DateUtil.parse(checkOutDate), DateUnit.DAY);
        } catch (Exception e) {
            log.error("计算入住天数失败", e);
            return null;
        }
    }

    private Map<String, List<Picture>> buildPictureListMap(QueryHotelDetailCommonRespDto response) {
        if (response == null || CollectionUtils.isEmpty(response.getHotelPicList())) {
            return new HashMap<>(0);
        }
        Map<String, List<Picture>> pictureListMap = new HashMap<>();
        for (QueryHotelDetailCommonRespDto.HotelPic hotelPic : response.getHotelPicList()) {
            if (hotelPic == null || StringUtils.isBlank(hotelPic.getPictureUrl())) {
                continue;
            }

            String pictureTitle = hotelPic.getPictureTitle();
            Picture picture = Picture.builder()
                    .type(StringUtils.isBlank(pictureTitle) ? HotelCoreConstant.OTHER : pictureTitle)
                    .url(hotelPic.getPictureUrl()).build();

            String basicRoomTypeId = hotelPic.getBasicRoomTypeId();
            String key = StringUtils.isBlank(basicRoomTypeId) ? HotelCoreConstant.HOTEL : basicRoomTypeId;
            List<Picture> pictureList = pictureListMap.getOrDefault(key, new ArrayList<>());
            pictureList.add(picture);
            pictureListMap.put(key, pictureList);
        }
        return pictureListMap;
    }

    private String getBedDesc(List<QueryHotelDetailCommonRespDto.BedInfo> bedInfoList) {
        if (CollectionUtils.isEmpty(bedInfoList)) {
            return "";
        }
        StringBuilder bedDesc = new StringBuilder();
        for (QueryHotelDetailCommonRespDto.BedInfo bedInfo : bedInfoList) {
            if (bedInfo == null) {
                continue;
            }
            List<QueryHotelDetailCommonRespDto.ChildBedInfo> childBedInfoList = bedInfo.getChildBedInfoList();
            if (CollectionUtils.isEmpty(childBedInfoList)) {
                continue;
            }
            // 子床型和的关系
            for (QueryHotelDetailCommonRespDto.ChildBedInfo childBedInfo : childBedInfoList) {
                if (childBedInfo == null) {
                    continue;
                }
                Integer bedCount = childBedInfo.getBedCount();
                Float bedWidth = childBedInfo.getBedWidth();
                String childBedTypeName = childBedInfo.getChildBedTypeName();
                if (StringUtils.isNotBlank(childBedTypeName)) {
                    if (bedCount != null && !childBedTypeName.contains("张")) {
                        bedDesc.append(bedCount).append("张");
                    }
                    if (bedWidth != null && Float.compare(bedWidth, 0) > 0 && !childBedTypeName.contains("米") && !childBedTypeName.contains("m") && !childBedTypeName.contains("cm") && !childBedTypeName.contains("厘米")) {
                        bedDesc.append(String.format("%.1f", bedWidth)).append("米");
                    }
                    bedDesc.append(childBedTypeName);
                } else {
                    if (bedWidth != null && Float.compare(bedWidth, 0) > 0) {
                        if (bedCount != null) {
                            bedDesc.append(bedCount).append("张");
                        }
                        bedDesc.append(String.format("%.1f", bedWidth)).append("米").append("床");
                    } else {
                        bedDesc.append("其他床型");
                    }
                }
                bedDesc.append("、");
            }
            if (bedDesc.toString().endsWith("、")) {
                bedDesc.deleteCharAt(bedDesc.length() - 1);
            }
            bedDesc.append("或");
        }
        if (bedDesc.toString().endsWith("或")) {
            bedDesc.deleteCharAt(bedDesc.length() - 1);
        }
        return bedDesc.toString();
    }

    private String getAreaDesc(String areaDesc) {
        if (StringUtils.isBlank(areaDesc) || StringUtils.equalsIgnoreCase(areaDesc, "undefined-un")) {
            return null;
        }
        return areaDesc.replace("㎡", "").replace("m²", "").replace("平方米", "") + "㎡";
    }

}
