package com.corpgovernment.core.domain.hoteldetail.gateway;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.dto.QueryRoomPackageListCommonRespDto;
import com.corpgovernment.core.dao.dto.QueryRoomPackageListReqDto;
import com.corpgovernment.core.dao.dto.QueryRoomPackageListRespDto;
import com.corpgovernment.core.domain.common.gateway.IHotelIndicatorGateway;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.hoteldetail.model.entity.PackageProduct;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPackage;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/15
 */
@Repository
@Slf4j
public class QueryRoomPackageListGateway implements IQueryRoomPackageListGateway {

    @Resource
    private CommonService commonService;

    @Resource
    private IHotelIndicatorGateway hotelIndicatorGateway;

    @Override
    @BusinessBehaviorMonitor
    public List<RoomPackage> queryRoomPackageList(SupplierProduct supplierProduct, List<String> packageIdList) {
        if (supplierProduct == null || CollectionUtils.isEmpty(packageIdList)) {
            return null;
        }

        String code = null;
        String msg = null;
        try {
            QueryRoomPackageListReqDto request = assembleQueryRoomPackageListReqDto(supplierProduct, packageIdList);
            // 调用供应商接口
            String responseJson = commonService.doPostJSON(supplierProduct.getSupplierCode(), "酒店套餐查询", supplierProduct.getUrl(), supplierProduct.getUserKey(), JsonUtils.toJsonString(request));
            QueryRoomPackageListRespDto response = JsonUtils.parse(responseJson, QueryRoomPackageListRespDto.class);
            if (response == null) {
                log.error("调用供应商失败");
                code = HotelCoreConstant.UNKNOWN_STATUS;
                msg = "未知错误";
                return null;
            }
            if (response.getCode() != null || response.getMsg() != null || response.getData() != null) {
                log.info("走新契约");
                code = response.getCode();
                msg = response.getMsg();
                if (response.getCode() == null || !"0".equals(response.getCode()) || response.getData() == null) {
                    return null;
                }
                return assembleRoomPackageList(response.getData());
            } else {
                log.info("走老契约");
                if (response.getStatus() != null) {
                    code = response.getStatus().getErrorCode();
                    msg = response.getStatus().getErrorMessage();
                }
                if (response.getStatus() != null && !Boolean.TRUE.equals(response.getStatus().getSuccess())) {
                    return null;
                }
                return assembleRoomPackageList(response);
            }
        } catch (Exception e) {
            log.error("调用供应商失败", e);
            code = HotelCoreConstant.UNKNOWN_STATUS;
            msg = e.toString();
            return null;
        } finally {
            hotelIndicatorGateway.execute(supplierProduct.getSupplierCode(), code, msg);
        }
    }

    private List<RoomPackage> assembleRoomPackageList(QueryRoomPackageListCommonRespDto response) {
        List<QueryRoomPackageListCommonRespDto.PackageRoomInfo> packageRoomInfoList = response.getPackageRoomInfo();
        if (CollectionUtils.isEmpty(packageRoomInfoList)) {
            return new ArrayList<>(0);
        }
        List<RoomPackage> roomPackageList = new ArrayList<>();
        for (QueryRoomPackageListCommonRespDto.PackageRoomInfo packageRoomInfo : packageRoomInfoList) {
            if (packageRoomInfo == null || CollectionUtils.isEmpty(packageRoomInfo.getXProductInfo())) {
                continue;
            }
            RoomPackage roomPackage = new RoomPackage();
            roomPackage.setPackageId(packageRoomInfo.getPackageId());
            List<QueryRoomPackageListCommonRespDto.XProductInfo> xProductInfoList = packageRoomInfo.getXProductInfo();
            List<PackageProduct> packageProductList = new ArrayList<>();
            for (QueryRoomPackageListCommonRespDto.XProductInfo xProductInfo : xProductInfoList) {
                if (xProductInfo == null) {
                    continue;
                }
                PackageProduct packageProduct = new PackageProduct();
                List<Integer> categoryIdList = xProductInfo.getCategoryId();
                if (CollectionUtils.isNotEmpty(categoryIdList)) {
                    packageProduct.setIconNameList(categoryIdList.stream().filter(Objects::nonNull).map(item -> item == 201 ? "食" : "享").collect(Collectors.toList()));
                }

                Integer xProductUnit = xProductInfo.getXProductUnit();
                if (xProductUnit == 1) {
                    packageProduct.setPackageName(xProductInfo.getXProductName() + xProductInfo.getQuantity() + "份");
                } else if (xProductUnit == 2) {
                    packageProduct.setPackageName(xProductInfo.getXProductName() + xProductInfo.getQuantity() + "份/天");
                }

                if (xProductInfo.getMealInfo() != null && CollectionUtils.isNotEmpty(xProductInfo.getMealInfo().getMealInfo())) {
                    packageProduct.setMealDescList(xProductInfo.getMealInfo().getMealInfo().stream().filter(item -> item != null && item.getFoodDetail() != null)
                            .map(item -> item.getFoodDetail().getName() + "x" + item.getFoodDetail().getNum()).collect(Collectors.toList()));
                }

                QueryRoomPackageListCommonRespDto.ApplicableNumberInfo applicableNumberInfo = xProductInfo.getApplicableNumberInfo();
                if (applicableNumberInfo != null) {
                    packageProduct.setMaxGuestDesc(applicableNumberInfo.getMaxNum() + "名成人");
                }

                QueryRoomPackageListCommonRespDto.ReservationInfo reservationInfo = xProductInfo.getReservationInfo();
                if (reservationInfo != null && reservationInfo.getReservationValue() != null && reservationInfo.getReservationType() != null) {
                    int reservationType = reservationInfo.getReservationType();
                    if (reservationType == 1) {
                        packageProduct.setBookRuleDesc("需要提前预约");
                    } else if (reservationType == 2) {
                        packageProduct.setBookRuleDesc("提前" + reservationInfo.getReservationValue() + "天预约");
                    } else if (reservationType == 3) {
                        packageProduct.setBookRuleDesc("提前" + reservationInfo.getReservationValue() + "小时预约");
                    } else {
                        packageProduct.setBookRuleDesc("无需预约");
                    }
                }

                List<QueryRoomPackageListCommonRespDto.ContactNumberInfo> contactNumberInfoList = xProductInfo.getContactNumberInfo();
                if (CollectionUtils.isNotEmpty(contactNumberInfoList)) {
                    packageProduct.setTelephoneList(contactNumberInfoList.stream().filter(Objects::nonNull).map(QueryRoomPackageListCommonRespDto.ContactNumberInfo::getPhoneNumber).collect(Collectors.toList()));
                }

                List<QueryRoomPackageListCommonRespDto.ReceptionTimeInfo> receptionTimeInfoList = xProductInfo.getReceptionTimeInfo();
                if (CollectionUtils.isNotEmpty(receptionTimeInfoList)) {
                    packageProduct.setReceptionTimeDescList(receptionTimeInfoList.stream().filter(Objects::nonNull).map(item -> item.getStartTime() + "-" + item.getEndTime()).collect(Collectors.toList()));
                }
                packageProduct.setSpecialDesc(xProductInfo.getXProductDesc());
                packageProductList.add(packageProduct);
            }
            roomPackage.setPackageProductList(packageProductList);
            roomPackageList.add(roomPackage);
        }
        return roomPackageList;
    }

    private QueryRoomPackageListReqDto assembleQueryRoomPackageListReqDto(SupplierProduct supplierProduct, List<String> packageIdList) {
        QueryRoomPackageListReqDto queryRoomPackageListReqDto = new QueryRoomPackageListReqDto();
        QueryRoomPackageListReqDto.BaseInfo baseInfo = new QueryRoomPackageListReqDto.BaseInfo();
        // uid
        String uid = supplierProduct.getUid();
        if (StringUtils.isBlank(uid)) {
            baseInfo.setUid(StringUtils.equalsIgnoreCase("ctrip", supplierProduct.getSupplierCode()) ? "" : null);
        } else {
            baseInfo.setUid(uid);
        }
        // corpId
        baseInfo.setCorpId(supplierProduct.getCorpId());
        queryRoomPackageListReqDto.setBaseInfo(baseInfo);
        queryRoomPackageListReqDto.setPackageId(packageIdList);
        return queryRoomPackageListReqDto;
    }

}
