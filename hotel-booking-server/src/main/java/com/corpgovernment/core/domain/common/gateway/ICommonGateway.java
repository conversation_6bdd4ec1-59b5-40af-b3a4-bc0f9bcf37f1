package com.corpgovernment.core.domain.common.gateway;

import com.corpgovernment.core.domain.common.model.entity.CityInfo;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.SupplierConfig;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-15 22:28
 */
public interface ICommonGateway {

    String getStayTime();

    Boolean setStayTime();

    String getCorpShortName();

    Integer getSupplierTimeOut();

    Boolean checkPrice(BigDecimal price);

    Boolean checkCustomPrice(Price price);

    BigDecimal divide(BigDecimal price1, BigDecimal price2, Boolean abroad);

    BigDecimal convertPrice(BigDecimal price, Boolean abroad);

    <T> T doPostHttp(String supplier, String name, String requestUrl, String userKey, String requestBody, Class<T> clazz) throws IOException;

    Boolean checkLatLon(Double lat, Double lon);

    String handleUid(String uid, String supplierCode);

    BigDecimal getRoomNightNum(String checkInDate, String checkOutDate, Integer roomQuantity);

    Set<String> getViewedHotel();

    void setViewedHotel(List<String> viewedHotelIdList);

    List<SupplierProduct> getPrioritySupplierList(List<SupplierProduct> supplierProductList);

    List<String> getPrioritySupplierCodeList(List<SupplierProduct> supplierProductList);

    List<SupplierProduct> removeRandomInfo(List<SupplierProduct> supplierProductList);
    
    List<String> getOverLimitModeList(List<String> overLimitModeList, List<String> paymentMethodList);
    
    Map<String, Long> getSupplierTimeoutMap();
    
    CityInfo getCityDistrictInfo(String cityName, String districtName);
    
    CityInfo getCityCountyInfo(String cityName, String countyName);
    
    Boolean openFeature(String feature);
    
    List<SupplierConfig> getSupplierConfigList();
    
    CityInfo getCityCountyInfo(String countyName, Double lat, Double lon);
    
}
