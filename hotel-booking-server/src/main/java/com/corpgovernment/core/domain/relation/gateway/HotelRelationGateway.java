package com.corpgovernment.core.domain.relation.gateway;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.apollo.IHotelCoreApolloDao;
import com.corpgovernment.core.dao.entity.db.HotelRelationDo;
import com.corpgovernment.core.dao.mysql.IHotelRelationDao;
import com.corpgovernment.core.domain.common.model.enums.SystemSupplierEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.relation.model.HotelInfo;
import com.corpgovernment.core.domain.relation.model.HotelKey;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2024-11-20 16:12
 */
@Repository
@Slf4j
public class HotelRelationGateway implements IHotelRelationGateway {
    
    @Resource
    private ICommonDomainService commonDomainService;
    
    @Resource
    private IHotelRelationDao hotelRelationDao;
    
    @Resource
    private IHotelCoreApolloDao hotelCoreApolloDao;
    
    @Override
    @BusinessBehaviorMonitor
    public Map<HotelKey, Set<HotelKey>> getOnlineHotelRelationMap(Map<String, List<HotelInfo>> hotelInfoListMap) {
        if (CollectionUtils.isEmpty(hotelInfoListMap)) {
            return null;
        }
        
        // 匹配距离限制
        Integer hotelOnlineMatchDistanceRestrict = Null.or(hotelCoreApolloDao.getHotelOnlineMatchDistanceRestrict(), HotelCoreConstant.DEFAULT_Hotel_ONLINE_MATCH_DISTANCE_RESTRICT);
        
        // 聚合同名酒店
        Map<String, List<HotelInfo>> tmpMap = hotelInfoListMap.values().stream()
                .flatMap(List::stream)
                .filter(item -> item != null && StringUtils.isNotBlank(item.getName()))
                .collect(Collectors.groupingBy(HotelInfo::getName));
        if (CollectionUtils.isEmpty(tmpMap)) {
            return null;
        }
        
        // 构建匹配关系
        Map<HotelKey, Set<HotelKey>> hotelRelationMap = new HashMap<>(0);
        for (List<HotelInfo> tmpList : tmpMap.values()) {
            if (CollectionUtils.isEmpty(tmpList) || tmpList.size() == 1 || tmpList.get(0) == null) {
                continue;
            }
            
            // 第一个酒店作为主体酒店
            HotelInfo hotelInfo = tmpList.get(0);
            
            // 构建匹配酒店集合
            Set<HotelKey> hotelKeySet = new HashSet<>();
            hotelKeySet.add(HotelKey.builder()
                    .supplierCode(hotelInfo.getSupplierCode())
                    .hotelId(hotelInfo.getHotelId()).build());
            for (int i = 1; i < tmpList.size(); i++) {
                // 获取酒店
                HotelInfo tmp = tmpList.get(i);
                if (tmp == null) {
                    continue;
                }
                
                // 校验距离
                Double distance = commonDomainService.getDistance(hotelInfo.getLat(), hotelInfo.getLon(), tmp.getLat(), tmp.getLon());
                if (distance == null || hotelOnlineMatchDistanceRestrict == null || distance > hotelOnlineMatchDistanceRestrict) {
                    log.info("在线匹配失败 酒店同名但距离条件不满足 distance={}", distance);
                    continue;
                }
                hotelKeySet.add(HotelKey.builder()
                    .supplierCode(tmp.getSupplierCode())
                    .hotelId(tmp.getHotelId()).build());
            }
            
            // 建立索引
            if (CollectionUtils.isEmpty(hotelKeySet) || hotelKeySet.size() < 2) {
                continue;
            }
            for (HotelKey key : hotelKeySet) {
                hotelRelationMap.put(key, hotelKeySet);
            }
        }
        return hotelRelationMap;
    }
    
    @Override
    @BusinessBehaviorMonitor
    public Map<HotelKey, Set<HotelKey>> getOfflineHotelRelationMap(Map<String, List<HotelInfo>> hotelInfoListMap,
                                                                   Set<String> supplierCodeSet,
                                                                   Set<String> directSupplierCodeSet) {
        if (CollectionUtils.isEmpty(hotelInfoListMap) || CollectionUtils.isEmpty(supplierCodeSet)) {
            return new HashMap<>(0);
        }

        // 找所有酒店的匹配关系
        List<HotelRelationDo> hotelRelationDoList = new ArrayList<>(0);
        hotelInfoListMap.forEach((supplierCode, hotelInfoList) -> {
            if (StringUtils.isBlank(supplierCode) || CollectionUtils.isEmpty(hotelInfoList)) {
                return;
            }
            List<String> hotelIdList = hotelInfoList.stream().map(HotelInfo::getHotelId).collect(Collectors.toList());
            if (StringUtils.equalsIgnoreCase(SystemSupplierEnum.CTRIP.getCode(), commonDomainService.getSystemSupplierCode(supplierCode))) {
                hotelRelationDoList.addAll(hotelRelationDao.listForwardMatchedRelation(supplierCode, hotelIdList));
            } else {
                hotelRelationDoList.addAll(hotelRelationDao.listBackwardMatchedRelation(supplierCode, hotelIdList));
            }
        });
        log.info("酒店匹配关系 hotelRelationDoList={}", hotelRelationDoList);

        // 直连特殊逻辑：直连关系构建
        List<HotelRelationDo> directHotelRelationList = getDirectHotelRelationList(hotelInfoListMap, directSupplierCodeSet);
        log.info("直连关系 directHotelRelationList={}", directHotelRelationList);
        if (CollectionUtils.isNotEmpty(directHotelRelationList)) {
            hotelRelationDoList.addAll(directHotelRelationList);
        }

        // 没有匹配关系
        if (CollectionUtils.isEmpty(hotelRelationDoList)) {
            log.info("无任何匹配关系");
            return new HashMap<>(0);
        }

        // 构建正向匹配关系map
        Map<HotelKey, Set<HotelKey>> forwardHotelRelationMap = new HashMap<>(0);
        for (HotelRelationDo hotelRelationDo : hotelRelationDoList) {
            HotelKey tmp1 = HotelKey.builder()
                    .supplierCode(hotelRelationDo.getMasterSupplierCode())
                    .hotelId(hotelRelationDo.getMasterHotelId()).build();
            HotelKey tmp2 = HotelKey.builder()
                    .supplierCode(hotelRelationDo.getSubSupplierCode())
                    .hotelId(hotelRelationDo.getSubHotelId()).build();
            // 正向放入
            Set<HotelKey> set = forwardHotelRelationMap.getOrDefault(tmp1, new HashSet<>());
            set.add(tmp2);
            forwardHotelRelationMap.put(tmp1, set);
        }

        // 构建匹配关系
        Map<HotelKey, Set<HotelKey>> hotelRelationMap = new HashMap<>(0);
        for (HotelKey tmp1 : forwardHotelRelationMap.keySet()) {
            Set<HotelKey> tmpSet1 = forwardHotelRelationMap.get(tmp1);
            tmpSet1.add(tmp1);
            // 过滤无关元素
            Set<HotelKey> tmpSet2 = tmpSet1.stream().filter(item -> supplierCodeSet.contains(item.getSupplierCode())).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(tmpSet2)) {
                continue;
            }
            // 建立索引
            for (HotelKey key : tmpSet2) {
                hotelRelationMap.put(key, tmpSet2);
            }
        }

        return hotelRelationMap;
    }
    
    @Override
    @BusinessBehaviorMonitor
    public Map<HotelKey, Set<HotelKey>> mergeHotelRelationMap(Map<HotelKey, Set<HotelKey>> hotelRelationMap1, Map<HotelKey, Set<HotelKey>> hotelRelationMap2) {
        if (CollectionUtils.isEmpty(hotelRelationMap1)) {
            return hotelRelationMap2;
        }
        if (CollectionUtils.isEmpty(hotelRelationMap2)) {
            return hotelRelationMap1;
        }
        
        Map<HotelKey, Set<HotelKey>> resultMap = copyHotelRelationMap(hotelRelationMap1);
        log.info("resultMap={}", JsonUtils.toJsonString(resultMap));
        
        hotelRelationMap2.forEach((key, value) -> {
            if (CollectionUtils.isEmpty(value)) {
                return;
            }
            HotelKey hotelSupplier = value.stream()
                    .filter(item -> item != null && resultMap.get(item) != null)
                    .findFirst()
                    .orElse(null);
            // 把原有的关系增量添加进去
            if (hotelSupplier != null) {
                Set<HotelKey> tmpSet = resultMap.get(hotelSupplier);
                tmpSet.addAll(value);
                for (HotelKey tmp : tmpSet) {
                    resultMap.put(tmp, tmpSet);
                }
            }
            // 增量关系直接添加
            else {
                resultMap.put(key, value);
            }
        });
        return resultMap;
    }
    
    private Map<HotelKey, Set<HotelKey>> copyHotelRelationMap(Map<HotelKey, Set<HotelKey>> hotelRelationMap) {
        if (CollectionUtils.isEmpty(hotelRelationMap)) {
            return new HashMap<>(0);
        }
        
        Map<HotelKey, Set<HotelKey>> resultMap = new HashMap<>(0);
        hotelRelationMap.forEach((key, value) -> {
            if (key == null || CollectionUtils.isEmpty(value)) {
                return;
            }
            
            HotelKey tmp = buildHotelKey(key);
            Set<HotelKey> tmpSet = value.stream().map(this::buildHotelKey).filter(Objects::nonNull).collect(Collectors.toSet());
            if (tmp == null || CollectionUtils.isEmpty(tmpSet)) {
                return;
            }
            
            resultMap.put(tmp, tmpSet);
        });
        
        return resultMap;
    }
    
    private HotelKey buildHotelKey(HotelKey hotelKey) {
        if (hotelKey == null) {
            return null;
        }
        return HotelKey.builder()
                .supplierCode(hotelKey.getSupplierCode())
                .hotelId(hotelKey.getHotelId()).build();
    }
    
    private List<HotelRelationDo> getDirectHotelRelationList(Map<String, List<HotelInfo>> hotelInfoListMap, Set<String> directSupplierCodeSet) {
        if (CollectionUtils.isEmpty(hotelInfoListMap) || CollectionUtils.isEmpty(directSupplierCodeSet)) {
            return null;
        }
        
        List<HotelRelationDo> hotelRelationDoList = new ArrayList<>();
        
        List<String> directSupplierCodeList = directSupplierCodeSet.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        String ctripClientSupplierCode = SystemSupplierEnum.CTRIP.getCode();
        
        for (String supplierCode : hotelInfoListMap.keySet()) {
            List<HotelInfo> hotelInfoList = hotelInfoListMap.get(supplierCode);
            if (StringUtils.isBlank(supplierCode) || CollectionUtils.isEmpty(hotelInfoList)) {
                continue;
            }
            // 携程资源直接构建直连关系
            if (StringUtils.equalsIgnoreCase(commonDomainService.getSystemSupplierCode(supplierCode), SystemSupplierEnum.CTRIP.getCode())) {
                ctripClientSupplierCode = supplierCode;
                for (HotelInfo hotelInfo : hotelInfoList) {
                    if (hotelInfo == null) {
                        continue;
                    }
                    for (String directSupplierCode : directSupplierCodeSet) {
                        if (StringUtils.isBlank(directSupplierCode)) {
                            continue;
                        }
                        hotelRelationDoList.add(HotelRelationDo.builder()
                                .masterSupplierCode(supplierCode)
                                .masterHotelId(hotelInfo.getHotelId())
                                .subSupplierCode(directSupplierCode)
                                .subHotelId(hotelInfo.getHotelId())
                                .matchStatus(true)
                                .build());
                    }
                }
            }
            // 如果是直连资源
            else if (directSupplierCodeList.contains(supplierCode)) {
                for (HotelInfo hotelInfo : hotelInfoList) {
                    if (hotelInfo == null) {
                        continue;
                    }
                    hotelRelationDoList.add(HotelRelationDo.builder()
                            .masterSupplierCode(ctripClientSupplierCode)
                            .masterHotelId(hotelInfo.getHotelId())
                            .subSupplierCode(supplierCode)
                            .subHotelId(hotelInfo.getHotelId())
                            .matchStatus(true)
                            .build());
                }
            }
        }
        
        return hotelRelationDoList;
    }
    
}
