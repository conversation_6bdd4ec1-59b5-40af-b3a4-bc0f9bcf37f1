package com.corpgovernment.core.domain.hotelpage.model.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/12/20
 */
public enum UnAvailableReasonEnum {
    /**
     * 满房
     */
    Full,
    /**
     * 无价
     */
    Priceless,
    /**
     * 歇业
     */
    OutOfBusiness,
    /**
     * 可订
     */
    canBooking,
    /**
     * 不可订
     */
    cantBooking,
    /**
     * 未知
     */
    UNKnown;

    public static UnAvailableReasonEnum getReasonEnum(String reason){
        return Stream.of(UnAvailableReasonEnum.values())
                .filter(t->t.name().equalsIgnoreCase(reason)).findFirst().orElse(UNKnown);
    }
}
