package com.corpgovernment.core.domain.hoteldetail.model.entity;

import com.corpgovernment.core.domain.common.model.entity.OverLimitReasonCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-26 13:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OverLimitInfo {

    private List<String> overLimitRuleNameList;

    private List<String> overLimitModeList;

}
