package com.corpgovernment.core.domain.model.snapshot.fee;

import com.corpgovernment.dto.snapshot.PriceInfoType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/23
 */
@Data
public class TaxFeeModel {
    /**
     * 税费id
     */
    private Integer taxId;
    /**
     * 税费类型名称
     */
    private String taxTypeName;
    /**
     * 收费模式（PER_STAY-每次入住; PER_PERSON_PER_STAY-每人每次; PER_NIGHT-每夜;
     * PER_PERSON_NIGHT-每人夜; PER_ORDER_AMOUNT-百分比; PER_ROOM_PER_STAY-每间每次入住;
     * PER_ROOM_PER_NIGHT-每间每晚; NOT_CALCULABLE-不能计算; OTHER-其他）
     */
    private String chargeMode;
    /**
     * 税费拆分规则（1-固定金额 每次入住; 2-固定金额 每间每次入住; 3-固定金额 每人每次入住;
     * 4-固定金额 每间每晚; 5-固定金额 每人每晚; 6-百分比; 7-每单位; 8-阶梯-百分比; 9-阶梯-每人每晚）
     */
    private Integer taxFeeCalculateType;
    /**
     * true-费用已经包含在订单中
     * false-费用由酒店按实际情况收取，不包含在订单总价中
     */
    private Boolean includeInTotalPrice;
    /**
     * 税费百分比，只有ChargeMode的值等于PerOrderAmount时有效，默认为0
     */
    private BigDecimal percentage;
    /**
     * 原币种价格
     */
    private PriceInfoModel originalPrice;

    /**
     * 报价（展示价格）
     */
    private PriceInfoModel sellPrice;
}
