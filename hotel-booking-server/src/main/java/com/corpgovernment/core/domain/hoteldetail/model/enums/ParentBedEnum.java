package com.corpgovernment.core.domain.hoteldetail.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-29 21:27
 */
@Getter
@AllArgsConstructor
public enum ParentBedEnum {

    QUEEN_BED("360", "大床"),
    TWIN_BED("361", "双床"),
    SINGLE_BED("362", "单人床"),
    MULTIPLE_BED("363", "多张床"),;

    private final String code;

    private final String info;

    private static final Map<String, ParentBedEnum> map = new HashMap<>();

    static {
        for (ParentBedEnum tmpEnum : values()) {
            map.put(tmpEnum.getCode(), tmpEnum);
            map.put(tmpEnum.getInfo(), tmpEnum);
        }
    }

    public static ParentBedEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }

}
