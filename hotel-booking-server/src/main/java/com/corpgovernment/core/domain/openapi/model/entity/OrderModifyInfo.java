package com.corpgovernment.core.domain.openapi.model.entity;

import com.corpgovernment.core.domain.openapi.model.enums.UnmodifiableReasonEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-08-31 19:22
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderModifyInfo {
    
    private Boolean canEarlyDeparture;
    
    private UnmodifiableReasonEnum unmodifiableReasonEnum;
    
    private List<OrderModifyReason> modifyReasonList;
    
    private Boolean canEarlyDepartureAheadLimit;
    
}
