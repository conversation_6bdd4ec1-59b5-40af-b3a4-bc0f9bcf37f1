package com.corpgovernment.core.domain.common.gateway;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.corpgovernment.api.basic.dto.CityCountyResponse;
import com.corpgovernment.api.basic.dto.CityLocationResponse;
import com.corpgovernment.api.basic.request.BasicCityInfoRequest;
import com.corpgovernment.api.basic.response.BasicCityInfoResponse;
import com.corpgovernment.api.basic.vo.BasiCityInfoVo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.shunt.ShuntConfigDao;
import com.corpgovernment.common.utils.HttpUtils;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.apollo.IHotelCoreApolloDao;
import com.corpgovernment.core.dao.openfeign.impl.HotelCoreOpenFeignDao;
import com.corpgovernment.core.domain.common.model.entity.*;
import com.corpgovernment.core.domain.common.model.entity.CityInfo;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.SupplierConfig;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.entity.TimeoutConfig;
import com.corpgovernment.core.domain.common.model.enums.OverLimitModeEnum;
import com.corpgovernment.core.domain.common.model.enums.SystemSupplierEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PaymentMethodEnum;
import com.corpgovernment.hotel.product.dataloader.soa.BasicDataClientLoader;
import com.corpgovernment.redis.cache.RedisUtils;
import com.corpgovernment.redis.cache.UserCacheManager;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-15 22:30
 */
@Service
@Slf4j
public class CommonGateway implements ICommonGateway {

    @Resource
    private UserCacheManager userCacheManager;

    @Resource
    private IHotelCoreApolloDao hotelCoreApolloDao;

    @Resource
    private RedisUtils redisUtils;
    
    @Resource
    private BasicDataClientLoader basicDataClientLoader;
    
    @Resource
    private ShuntConfigDao shuntConfigDao;
    @Autowired
    private HotelCoreOpenFeignDao hotelCoreOpenFeignDao;
    
    @Override
    public String getStayTime() {
        try {
            return userCacheManager.getUserCache(HotelCoreConstant.RedisKey.STATE_TIME_KEY, String.class);
        } catch (Exception e) {
            log.error("getStayTime error", e);
            return null;
        }
    }

    @Override
    public Boolean setStayTime() {
        try {
            userCacheManager.setUserCache(HotelCoreConstant.RedisKey.STATE_TIME_KEY, new Date(), hotelCoreApolloDao.getHotelStayTime());
        } catch (Exception e) {
            log.error("setStayTime error", e);
            return false;
        }
        return true;
    }

    @Override
    public String getCorpShortName() {
        return hotelCoreApolloDao.getCompanyShortName();
    }

    @Override
    public Integer getSupplierTimeOut() {
        return hotelCoreApolloDao.getSupplierTimeOut();
    }

    @Override
    public Boolean checkPrice(BigDecimal price) {
        return price != null && price.compareTo(BigDecimal.ZERO) > 0;
    }

    @Override
    public Boolean checkCustomPrice(Price price) {
        return price != null && price.getCustomPrice() != null;
    }

    @Override
    public BigDecimal divide(BigDecimal price1, BigDecimal price2, Boolean abroad) {
        if (price1 == null || price2 == null) {
            return null;
        }
        return Boolean.TRUE.equals(abroad) ? divideByCeiling(price1, price2) : divideByHalfUp(price1, price2);
    }

    @Override
    public BigDecimal convertPrice(BigDecimal price, Boolean abroad) {
        if (price == null) {
            return null;
        }
        if (Boolean.TRUE.equals(abroad)) {
            return setByCeiling(price);
        } else {
            return setByHalfUp(price);
        }
    }

    @Override
    @BusinessBehaviorMonitor
    public <T> T doPostHttp(String supplier, String name, String requestUrl, String userKey, String requestBody, Class<T> clazz) throws IOException {
        return JsonUtils.parse(HttpUtils.doPostJSONUseSignReplaceSupplierCardNo(supplier, name, requestUrl, requestBody, userKey), clazz);
    }

    @Override
    public Boolean checkLatLon(Double lat, Double lon) {
        return lat != null && lon != null && lat != -1 && lon != -1 && lat != 0 && lon != 0;
    }

    @Override
    public String handleUid(String uid, String supplierCode) {
        if (StringUtils.isNotBlank(uid)) {
            return uid;
        }
        if (StringUtils.equalsIgnoreCase(HotelCoreConstant.CTRIP, supplierCode)) {
            return "";
        }
        return null;
    }

    @Override
    public BigDecimal getRoomNightNum(String checkInDate, String checkOutDate, Integer roomQuantity) {
        if (StringUtils.isBlank(checkInDate) || StringUtils.isBlank(checkOutDate) || roomQuantity == null) {
            return null;
        }

        try {
            return BigDecimal.valueOf(
                    roomQuantity *
                    DateUtil.between(DateUtil.parse(checkInDate), DateUtil.parse(checkOutDate), DateUnit.DAY));
        } catch (Exception e) {
            log.error("getRoomNightNum error", e);
            return null;
        }
    }

    @Override
    public Set<String> getViewedHotel() {
        String key = buildViewedHotelKey();
        if (StringUtils.isBlank(key)) {
            return null;
        }
        List<String> cacheList = redisUtils.getCacheList(key, String.class);
        if (CollectionUtils.isEmpty(cacheList)) {
            return null;
        }
        return new HashSet<>(cacheList);
    }

    @Override
    public void setViewedHotel(List<String> viewedHotelIdList) {
        if (CollectionUtils.isEmpty(viewedHotelIdList)) {
            return;
        }
        String key = buildViewedHotelKey();
        if (StringUtils.isBlank(key)) {
            return;
        }
        redisUtils.setList(key, viewedHotelIdList, HotelCoreConstant.DAY_CACHE_TIME);
    }

    @Override
    public List<SupplierProduct> getPrioritySupplierList(List<SupplierProduct> supplierProductList) {
        if (CollectionUtils.isEmpty(supplierProductList)) {
            return new ArrayList<>(0);
        }
        return supplierProductList.stream()
                .filter(item -> item != null && StringUtils.isNotBlank(item.getSupplierCode()))
                .sorted(Comparator.comparing((SupplierProduct item) -> Null.or(item.getSortNum(), Integer.MAX_VALUE))
                        .thenComparing(item -> Null.or(item.getSupplierCode(), "")))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getPrioritySupplierCodeList(List<SupplierProduct> supplierProductList) {
        List<SupplierProduct> prioritySupplierList = getPrioritySupplierList(supplierProductList);
        if (CollectionUtils.isEmpty(prioritySupplierList)) {
            return new ArrayList<>(0);
        }
        return prioritySupplierList.stream()
                .map(SupplierProduct::getSupplierCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<SupplierProduct> removeRandomInfo(List<SupplierProduct> supplierProductList) {
        if (CollectionUtils.isEmpty(supplierProductList)) {
            return new ArrayList<>(0);
        }
        List<SupplierProduct> resultList = new ArrayList<>();
        for (SupplierProduct supplierProduct : supplierProductList) {
            if (supplierProduct == null) {
                continue;
            }
            resultList.add(SupplierProduct.builder()
                    .supplierCode(null)
                    .supplierName(null)
                    .url(null)
                    .parameterlessUrl(supplierProduct.getParameterlessUrl())
                    .uid(supplierProduct.getUid())
                    .corpId(supplierProduct.getCorpId())
                    .userKey(null)
                    .shield(supplierProduct.getShield())
                    .sortNum(supplierProduct.getSortNum())
                    .aid(supplierProduct.getAid())
                    .sid(supplierProduct.getSid())
                    .serviceChargeList(null).build());
        }
        return resultList;
    }
    
    @Override
    public List<String> getOverLimitModeList(List<String> overLimitModeList, List<String> paymentMethodList) {
        if (CollectionUtils.isEmpty(overLimitModeList)) {
            return null;
        }
        
        // 混付只要有公帐支付在即可
        if (overLimitModeList.contains(OverLimitModeEnum.M.getCode())
                && CollectionUtils.isNotEmpty(paymentMethodList)
                && !paymentMethodList.contains(PaymentMethodEnum.ACCNT.getCode())) {
            overLimitModeList.remove(OverLimitModeEnum.M.getCode());
        }
        
        return overLimitModeList;
    }
    
    @Override
    public Map<String, Long> getSupplierTimeoutMap() {
        List<TimeoutConfig> timeoutConfigList = hotelCoreApolloDao.getTimeoutConfigList();
        if (CollectionUtils.isEmpty(timeoutConfigList)) {
            return null;
        }
        
        // 获取耗时配置
        Map<String, Long> supplierTimeoutMap = new HashMap<>();
        String tenantId = TenantContext.getTenantId();
        for (TimeoutConfig timeoutConfig : timeoutConfigList) {
            if (timeoutConfig == null || !StringUtils.equalsIgnoreCase(timeoutConfig.getTenantId(), tenantId)) {
                continue;
            }
            
            // 没有配置则跳出
            if (CollectionUtils.isEmpty(timeoutConfig.getSupplierTimeoutConfigList())) {
                break;
            }
            
            for (TimeoutConfig.SupplierTimeoutConfig supplierTimeoutConfig : timeoutConfig.getSupplierTimeoutConfigList()) {
                if (supplierTimeoutConfig == null || StringUtils.isBlank(supplierTimeoutConfig.getSupplierCode()) || supplierTimeoutConfig.getTimeout() == null) {
                    continue;
                }
                supplierTimeoutMap.put(supplierTimeoutConfig.getSupplierCode(), supplierTimeoutConfig.getTimeout());
            }
        }
        
        return supplierTimeoutMap;
    }
    
    @Override
    public Boolean openFeature(String feature) {
        return shuntConfigDao.openFeature(feature);
    }
    
    @Override
    public List<SupplierConfig> getSupplierConfigList() {
        String supplierConfigListStr = hotelCoreApolloDao.getSupplierConfigListStr();
        if (StringUtils.isBlank(supplierConfigListStr)) {
            return null;
        }
        
        try {
            return JsonUtils.parse(supplierConfigListStr, new TypeReference<List<SupplierConfig>>() {});
        } catch (Exception e) {
            log.error("解析supplierConfigListStr异常", e);
            return null;
        }
    }
    
    @Override
    public CityInfo getCityCountyInfo(String countyName, Double lat, Double lon) {
        if (StringUtils.isBlank(countyName)) {
            return null;
        }
        
        // 获取城市信息
        BasicCityInfoRequest basicCityInfoRequest = new BasicCityInfoRequest();
        basicCityInfoRequest.setCityName(countyName);
        basicCityInfoRequest.setCenterLat(lat);
        basicCityInfoRequest.setCenterLon(lon);
        JSONResult<BasicCityInfoResponse> cityInfoByIdOrName = hotelCoreOpenFeignDao.getCityInfoByIdOrName(basicCityInfoRequest);
        BasiCityInfoVo basiCityInfoVo = Optional.ofNullable(cityInfoByIdOrName)
                .map(JSONResult::getData)
                .map(BasicCityInfoResponse::getBasiCityInfoList)
                .flatMap(item -> item.stream().findFirst())
                .orElse(null);
        // 转换构建
        return buildCityInfo(basiCityInfoVo);
    }
    
    private CityInfo buildCityInfo(BasiCityInfoVo basiCityInfoVo) {
        if (basiCityInfoVo == null) {
            return null;
        }
        
        return CityInfo.builder()
                .cityId(basiCityInfoVo.getParentCityId())
                .cityName(null)
                .countyId(basiCityInfoVo.getCityId())
                .countyName(basiCityInfoVo.getCityName())
                .build();
    }
    
    @Override
    public CityInfo getCityDistrictInfo(String cityName, String districtName) {
        List<CityLocationResponse> cityLocationResponseList = basicDataClientLoader.searchLocation(cityName, districtName);
        if (CollectionUtils.isEmpty(cityLocationResponseList) || cityLocationResponseList.get(0) == null) {
            return null;
        }
        
        return CityInfo.builder()
                .cityId(cityLocationResponseList.get(0).getCityId())
                .cityName(cityLocationResponseList.get(0).getCityName())
                .districtId(cityLocationResponseList.get(0).getLocationId())
                .districtName(cityLocationResponseList.get(0).getLocationName())
                .build();
    }
    
    @Override
    public CityInfo getCityCountyInfo(String cityName, String countyName) {
        List<CityCountyResponse> cityCountyResponseList = basicDataClientLoader.searchCounty(cityName, countyName);
        if (CollectionUtils.isEmpty(cityCountyResponseList) || cityCountyResponseList.get(0) == null) {
            return null;
        }
        
        return CityInfo.builder()
                .cityId(cityCountyResponseList.get(0).getCityId())
                .cityName(cityCountyResponseList.get(0).getCityName())
                .countyId(cityCountyResponseList.get(0).getCountyId())
                .countyName(cityCountyResponseList.get(0).getCountyName())
                .build();
    }
    
    private String buildViewedHotelKey() {
        Object uid = UserInfoContext.getContextParams("uid");
        if (uid == null) {
            return null;
        }
        return HotelCoreConstant.RedisKey.VIEWED_HOTEL + HotelCoreConstant.COLON + uid + HotelCoreConstant.COLON + DateUtil.today();
    }

    private BigDecimal setByHalfUp(BigDecimal price) {
        if (price == null) {
            return null;
        }
        return price.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros();
    }

    private BigDecimal setByCeiling(BigDecimal price) {
        if (price == null) {
            return null;
        }
        return price.setScale(2, RoundingMode.CEILING).stripTrailingZeros();
    }

    private BigDecimal divideByHalfUp(BigDecimal price1, BigDecimal price2) {
        if (price1 == null || price2 == null) {
            return null;
        }
        return price1.divide(price2, 2, RoundingMode.HALF_UP).stripTrailingZeros();
    }

    private BigDecimal divideByCeiling(BigDecimal price1, BigDecimal price2) {
        if (price1 == null || price2 == null) {
            return null;
        }
        return price1.divide(price2, 2, RoundingMode.CEILING).stripTrailingZeros();
    }

}
