package com.corpgovernment.core.domain.relation.gateway;

import com.corpgovernment.core.domain.relation.model.HotelInfo;
import com.corpgovernment.core.domain.relation.model.HotelKey;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @create 2024-11-20 16:11
 */
public interface IHotelRelationGateway {
    
    Map<HotelKey, Set<HotelKey>> getOnlineHotelRelationMap(Map<String, List<HotelInfo>> hotelInfoListMap);
    
    Map<HotelKey, Set<HotelKey>> getOfflineHotelRelationMap(Map<String, List<HotelInfo>> hotelInfoListMap,
                                                            Set<String> supplierCodeSet,
                                                            Set<String> directSupplierCodeSet);
    
    Map<HotelKey, Set<HotelKey>> mergeHotelRelationMap(Map<HotelKey, Set<HotelKey>> hotelRelationMap1, Map<HotelKey, Set<HotelKey>> hotelRelationMap2);
    
}
