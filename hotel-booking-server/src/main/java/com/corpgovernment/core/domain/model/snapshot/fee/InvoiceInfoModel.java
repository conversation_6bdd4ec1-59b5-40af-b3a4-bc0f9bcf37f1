package com.corpgovernment.core.domain.model.snapshot.fee;

import com.corpgovernment.dto.snapshot.PriceInfoType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
@Data
public class InvoiceInfoModel {
    /**
     * 房型可开的增值税发票类型。None无；Special增值税专用发票；Ordinary增值税普通发票
     */
    private List<String> invoiceType;
    /**
     * 配送费
     */
    private List<PriceInfoType> deliveryFee;
}
