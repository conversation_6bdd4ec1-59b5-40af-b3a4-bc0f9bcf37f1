package com.corpgovernment.core.domain.relation.gateway;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.apollo.IHotelCoreApolloDao;
import com.corpgovernment.core.dao.entity.db.HotelRelationDo;
import com.corpgovernment.core.dao.entity.db.ManualHotelRelationDo;
import com.corpgovernment.core.dao.mapper.ManualHotelRelationMapper;
import com.corpgovernment.core.dao.mysql.IHotelRelationDao;
import com.corpgovernment.core.domain.common.model.enums.SystemSupplierEnum;
import com.corpgovernment.core.domain.relation.model.GeoDistance;
import com.corpgovernment.core.domain.relation.model.GeoLocation;
import com.corpgovernment.core.domain.relation.model.HotelInfo;
import com.corpgovernment.core.domain.relation.model.HotelKey;
import com.corpgovernment.core.domain.relation.model.HotelMergerSourceEnum;
import com.corpgovernment.core.domain.relation.model.HotelRelation;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025-03-27 02:38
 */
@Repository
@Slf4j
public class HotelRelationGatewayV2 implements IHotelRelationGatewayV2 {
    
    @Value("${hotelRelationV2Enable:true}")
    private String hotelRelationV2Enable;
    
    @Resource
    private IHotelCoreApolloDao hotelCoreApolloDao;
    
    @Resource
    private IHotelRelationDao hotelRelationDao;
    
    @Resource
    private ManualHotelRelationMapper manualHotelRelationMapper;
    
    @Override
    @BusinessBehaviorMonitor
    public List<HotelRelation> queryManualHotelRelations(Map<String, List<HotelInfo>> hotelInfoLists, Set<String> supplierCodes) {
        if (CollectionUtils.isEmpty(hotelInfoLists) || CollectionUtils.isEmpty(supplierCodes)) {
            return null;
        }
        
        List<HotelRelation> results = new ArrayList<>();
        hotelInfoLists.forEach((supplierCode, hotelInfos) -> {
            List<HotelRelation> temps = queryManualHotelRelations(supplierCode, hotelInfos);
            if (CollectionUtils.isNotEmpty(temps)) {
                results.addAll(temps);
            }
        });
        
        return filterValidHotelRelations(results, supplierCodes);
    }
    
    @Override
    @BusinessBehaviorMonitor
    public List<HotelRelation> queryStaticHotelRelations(Map<String, List<HotelInfo>> hotelInfoLists, Set<String> supplierCodes) {
        if (CollectionUtils.isEmpty(hotelInfoLists) || CollectionUtils.isEmpty(supplierCodes)) {
            return null;
        }
        
        List<HotelRelation> results = new ArrayList<>();
        hotelInfoLists.forEach((supplierCode, hotelInfos) -> {
            List<HotelRelation> temps = queryStaticHotelRelations(supplierCode, hotelInfos, supplierCodes);
            if (CollectionUtils.isNotEmpty(temps)) {
                results.addAll(temps);
            }
        });
        
        return filterValidHotelRelations(results, supplierCodes);
    }
    
    @Override
    @BusinessBehaviorMonitor
    public List<HotelRelation> queryDirectHotelRelations(Map<String, List<HotelInfo>> hotelInfoLists, Set<String> directSupplierCodes) {
        if (CollectionUtils.isEmpty(hotelInfoLists) || CollectionUtils.isEmpty(directSupplierCodes)) {
            return null;
        }
        
        List<HotelRelation> hotelRelations = new ArrayList<>();
        hotelInfoLists.forEach((supplierCode, hotelInfos) -> {
            List<HotelRelation> tmpList = generateDirectHotelRelations(supplierCode, hotelInfos, directSupplierCodes);
            if (CollectionUtils.isNotEmpty(tmpList)) {
                hotelRelations.addAll(tmpList);
            }
        });
        return hotelRelations;
    }
    
    @Override
    @BusinessBehaviorMonitor
    public List<HotelRelation> queryRealTimeHotelRelations(Map<String, List<HotelInfo>> hotelInfoLists) {
        if (CollectionUtils.isEmpty(hotelInfoLists)) {
            return null;
        }
        
        // 匹配距离限制
        Integer distanceLimit = Null.or(hotelCoreApolloDao.getHotelOnlineMatchDistanceRestrict(), HotelCoreConstant.DEFAULT_Hotel_ONLINE_MATCH_DISTANCE_RESTRICT);
        
        // 聚合同名酒店
        Map<String, List<HotelInfo>> sameNameHotelInfos = hotelInfoLists.values().stream()
                .flatMap(List::stream)
                .filter(item -> item != null && StringUtils.isNotBlank(item.getName()))
                .collect(Collectors.groupingBy(HotelInfo::getName));
        if (CollectionUtils.isEmpty(sameNameHotelInfos)) {
            return null;
        }
        
        return sameNameHotelInfos.values().stream()
                .map(hotelInfos -> generateSameNameHotelRelations(hotelInfos, distanceLimit))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }
    
    @Override
    @BusinessBehaviorMonitor
    public boolean isHotelRelationV2Enable() {
        return StringUtils.equalsIgnoreCase(hotelRelationV2Enable, "true");
    }
    
    private boolean isCtrip(String supplierCode) {
        return StringUtils.equalsIgnoreCase(supplierCode, SystemSupplierEnum.CTRIP.getCode());
    }
    
    private List<HotelRelation> generateSameNameHotelRelations(List<HotelInfo> hotelInfos, Integer distanceLimit) {
        if (hotelInfos ==  null || hotelInfos.size() <= 1 || hotelInfos.get(0) == null) {
            return null;
        }
        
        HotelInfo masterHotelInfo = hotelInfos.get(0);
        return hotelInfos.stream()
                .skip(1)
                .filter(Objects::nonNull)
                .filter(hotelInfo -> isDistanceWithinLimit(masterHotelInfo.getGeoLocation(), hotelInfo.getGeoLocation(), distanceLimit))
                .map(hotelInfo -> HotelRelation.builder()
                        .masterHotelKey(toHotelKey(masterHotelInfo.getSupplierCode(), masterHotelInfo.getHotelId()))
                        .subHotelKey(toHotelKey(hotelInfo.getSupplierCode(), hotelInfo.getHotelId()))
                        .matchStatus(true)
                        .hotelMergerSource(HotelMergerSourceEnum.REAL_TIME)
                        .build())
                .collect(Collectors.toList());
    }
    
    private boolean isDistanceWithinLimit(GeoLocation geoLocationA, GeoLocation geoLocationB, Integer distanceLimit) {
        if (distanceLimit == null) {
            return false;
        }
        
        GeoDistance geoDistance = GeoDistance.builder()
                .geoLocationA(geoLocationA)
                .geoLocationB(geoLocationB)
                .build();
        
        Double distance = geoDistance.calculateDistance();
        if (distance == null) {
            return false;
        }
        
        boolean isDistanceWithinLimit = distance <= distanceLimit;
        if (isDistanceWithinLimit) {
            log.info("在线匹配失败 酒店同名但距离条件不满足 distance={}", distance);
        }
        
        return isDistanceWithinLimit;
    }
    
    private List<HotelRelation> generateDirectHotelRelations(String supplierCode, List<HotelInfo> hotelInfos, Set<String> directSupplierCodes) {
        if (CollectionUtils.isEmpty(directSupplierCodes)) {
            return null;
        }
        
        if (isCtrip(supplierCode)) {
            return generateCtripDirectHotelRelations(hotelInfos, directSupplierCodes);
        }
        
        if (!directSupplierCodes.contains(supplierCode)) {
            return null;
        }
        return generateNonCtripDirectHotelRelations(hotelInfos, directSupplierCodes);
    }
    
    private List<HotelRelation> generateNonCtripDirectHotelRelations(List<HotelInfo> hotelInfos, Set<String> directSupplierCodes) {
        if (CollectionUtils.isEmpty(hotelInfos) || CollectionUtils.isEmpty(directSupplierCodes)) {
            return null;
        }
        
        return hotelInfos.stream()
                .filter(Objects::nonNull)
                .map(hotelInfo -> HotelRelation.builder()
                        .masterHotelKey(toHotelKey(SystemSupplierEnum.CTRIP.getCode(), hotelInfo.getHotelId()))
                        .subHotelKey(toHotelKey(hotelInfo.getSupplierCode(), hotelInfo.getHotelId()))
                        .matchStatus(true)
                        .hotelMergerSource(HotelMergerSourceEnum.DIRECT)
                        .build())
                .collect(Collectors.toList());
    }
    
    private List<HotelRelation> generateCtripDirectHotelRelations(List<HotelInfo> hotelInfos, Set<String> directSupplierCodes) {
        if (CollectionUtils.isEmpty(hotelInfos) || CollectionUtils.isEmpty(directSupplierCodes)) {
            return null;
        }
        
        return hotelInfos.stream()
                .filter(Objects::nonNull)
                .flatMap(hotelInfo -> directSupplierCodes.stream()
                        .map(directSupplierCode -> HotelRelation.builder()
                                .masterHotelKey(toHotelKey(hotelInfo.getSupplierCode(), hotelInfo.getHotelId()))
                                .subHotelKey(toHotelKey(directSupplierCode, hotelInfo.getHotelId()))
                                .matchStatus(true)
                                .hotelMergerSource(HotelMergerSourceEnum.DIRECT)
                                .build()))
                .collect(Collectors.toList());
    }
    
    private List<HotelRelation> filterValidHotelRelations(List<HotelRelation> hotelRelations, Set<String> validSupplierCodes) {
        if (CollectionUtils.isEmpty(hotelRelations) || CollectionUtils.isEmpty(validSupplierCodes)) {
            return null;
        }
        
        return hotelRelations.stream()
                .filter(item -> item != null &&
                        isValidSupplierCode(item.getMasterHotelKey(), validSupplierCodes) &&
                        isValidSupplierCode(item.getSubHotelKey(), validSupplierCodes))
                .collect(Collectors.toList());
    }
    
    private boolean isValidSupplierCode(HotelKey hotelKey, Set<String> validSupplierCodes) {
        if (hotelKey == null || StringUtils.isBlank(hotelKey.getSupplierCode()) || CollectionUtils.isEmpty(validSupplierCodes)) {
            return false;
        }
        
        return validSupplierCodes.contains(hotelKey.getSupplierCode());
    }
    
    private List<HotelRelation> queryManualHotelRelations(String supplierCode, List<HotelInfo> hotelInfos) {
        List<String> hotelIds = toHotelIds(hotelInfos);
        if (isCtrip(supplierCode)) {
            return queryCtripManualHotelRelations(hotelIds);
        }
        
        return queryNonCtripManualHotelRelations(supplierCode, hotelIds);
    }
    
    private List<HotelRelation> queryNonCtripManualHotelRelations(String supplierCode, List<String> hotelIds) {
        if (CollectionUtils.isEmpty(hotelIds) || StringUtils.isBlank(supplierCode)) {
            return null;
        }
        
        List<ManualHotelRelationDo> manualHotelRelations = manualHotelRelationMapper.queryNonCtripManualHotelRelations(supplierCode, hotelIds);
        return toManualHotelRelations(manualHotelRelations);
    }
    
    private List<String> toHotelIds(List<HotelInfo> hotelInfos) {
        if (CollectionUtils.isEmpty(hotelInfos)) {
            return null;
        }
        
        return hotelInfos.stream()
                .map(HotelInfo::getHotelId)
                .collect(Collectors.toList());
    }
    
    private List<HotelRelation> queryCtripManualHotelRelations(List<String> ctripHotelIds) {
        if (CollectionUtils.isEmpty(ctripHotelIds)) {
            return null;
        }
        
        List<ManualHotelRelationDo> manualHotelRelations = manualHotelRelationMapper.queryCtripManualHotelRelations(ctripHotelIds);
        return toManualHotelRelations(manualHotelRelations);
    }
    
    private List<HotelRelation> toManualHotelRelations(List<ManualHotelRelationDo> manualHotelRelations) {
        if (CollectionUtils.isEmpty(manualHotelRelations)) {
            return null;
        }
        
        return manualHotelRelations.stream()
                .filter(Objects::nonNull)
                .map(this::toManualHotelRelation)
                .collect(Collectors.toList());
    }
    
    private HotelRelation toManualHotelRelation(ManualHotelRelationDo manualHotelRelation) {
        if (manualHotelRelation == null) {
            return null;
        }
        
        return HotelRelation.builder()
                .masterHotelKey(toHotelKey(manualHotelRelation.getMasterSupplierCode(), manualHotelRelation.getMasterHotelId()))
                .subHotelKey(toHotelKey(manualHotelRelation.getSubSupplierCode(), manualHotelRelation.getSubHotelId()))
                .matchStatus(manualHotelRelation.getMatchStatus())
                .hotelMergerSource(HotelMergerSourceEnum.MANUAL)
                .build();
    }
    
    private HotelKey toHotelKey(String supplierCode, String hotelId) {
        return HotelKey.builder()
                .supplierCode(supplierCode)
                .hotelId(hotelId)
                .build();
    }
    
    
    private List<HotelRelation> queryStaticHotelRelations(String supplierCode, List<HotelInfo> hotelInfos, Set<String> supplierCodes) {
        List<String> hotelIds = toHotelIds(hotelInfos);
        if (isCtrip(supplierCode)) {
            return queryCtripStaticHotelRelations(hotelIds);
        }
        
        return queryNonCtripStaticHotelRelations(supplierCode, hotelIds);
    }
    
    private List<HotelRelation> queryNonCtripStaticHotelRelations(String supplierCode, List<String> hotelIds) {
        if (CollectionUtils.isEmpty(hotelIds) || StringUtils.isBlank(supplierCode)) {
            return null;
        }
        
        List<HotelRelationDo> hotelRelations = hotelRelationDao.listBackwardMatchedRelation(supplierCode, hotelIds);
        return toHotelRelations(hotelRelations);
    }
    
    private List<HotelRelation> queryCtripStaticHotelRelations(List<String> hotelIds) {
        if (CollectionUtils.isEmpty(hotelIds)) {
            return null;
        }
        
        List<HotelRelationDo> hotelRelationDos = hotelRelationDao.listForwardMatchedRelation(SystemSupplierEnum.CTRIP.getCode(), hotelIds);
        return toHotelRelations(hotelRelationDos);
    }
    
    private List<HotelRelation> toHotelRelations(List<HotelRelationDo> hotelRelations) {
        if (CollectionUtils.isEmpty(hotelRelations)) {
            return null;
        }
        
        return hotelRelations.stream()
                .filter(Objects::nonNull)
                .map(this::toHotelRelation)
                .collect(Collectors.toList());
    }
    
    private HotelRelation toHotelRelation(HotelRelationDo hotelRelation) {
        if (hotelRelation == null) {
            return null;
        }
        
        return HotelRelation.builder()
                .masterHotelKey(toHotelKey(hotelRelation.getMasterSupplierCode(), hotelRelation.getMasterHotelId()))
                .subHotelKey(toHotelKey(hotelRelation.getSubSupplierCode(), hotelRelation.getSubHotelId()))
                .matchStatus(hotelRelation.getMatchStatus())
                .hotelMergerSource(HotelMergerSourceEnum.STATIC)
                .build();
    }
    
    
}
