package com.corpgovernment.core.domain.model.snapshot.fee;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
@Data
public class PriceInfoModel {
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 币种
        */
    private String currency;
    /**
     * 价格类型(待拓展字段，考虑维护成枚举类型，包括对客结算币种、供应商结算币种等)
        */
    private String priceType;
    /**
     * 汇率token
     */
    private String exchangeRateToken;
}
