package com.corpgovernment.core.domain.hotelpage.service.control.impl;

import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControlRequest;
import com.corpgovernment.core.domain.hotelpage.model.enums.RequestControlEnum;
import com.corpgovernment.core.domain.hotelpage.service.control.IRequestControlService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @create 2024-09-18 16:49
 */
@Service
public class PriceIntersectionRequestControlService implements IRequestControlService {
    @Override
    public String controlRequest(HotelPageRequest hotelPageRequest, TravelControlRequest travelControlRequest) {
        if (travelControlRequest == null) {
            return "";
        }
        
        // 差标价格无交集
        BigDecimal minPrice = travelControlRequest.getMinPrice();
        BigDecimal maxPrice = travelControlRequest.getMaxPrice();
        if (minPrice != null && maxPrice != null && minPrice.compareTo(maxPrice) > 0) {
            return requestControlItemEnum().getCode();
        }
        
        return "";
    }
    
    @Override
    public RequestControlEnum requestControlItemEnum() {
        return RequestControlEnum.PRICE_INTERSECTION;
    }
}
