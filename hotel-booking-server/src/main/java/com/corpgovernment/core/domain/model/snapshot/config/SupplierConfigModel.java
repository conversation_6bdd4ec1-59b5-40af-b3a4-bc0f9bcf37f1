package com.corpgovernment.core.domain.model.snapshot.config;

import com.corpgovernment.dto.config.ServiceFeeDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
@Data
public class SupplierConfigModel {
    /**
     * 公司编号
     */
    private String companyCode;
    /**
     * 供应商code
     */
    private String supplierCode;
    /**
     * 供应商名
     */
    private String supplierName;
    /**
     * 供应商展示名
     */
    private String supplierFullName;
    /**
     * 服务类型
     * 'flight','train','hotel','car'
     */
    private String productType;
    /**
     * 1表示开启，0表示停用
     */
    private Boolean isEnable;
    /**
     * 是否可以使用该配置，条件：isEnable=true && 合同在生效期
     */
    private Boolean inUse;
    /**
     * 1表示因公，2表示因私，3表示因公因私
     */
    private Integer busPriType;
    /**
     * 供应商企业ID
     */
    private String supplierCorpId;
    /**
     * 供应商用户名
     */
    private String supplierUid;
    /**
     * 供应商主账户ID
     */
    private String supplierAccountId;
    /**
     * 服务费获取类型 1从配置读取 2从服务商接口读取
     */
    private Integer serviceFeeOptionType;
    /**
     * 1开启选择保险 2关闭选择保险 3强制购买保险
     */
    private Integer insuranceOptionType;
    /**
     * 服务费列表
     */
    private List<ServiceFeeDTO> serviceFeeList;
    /**
     * 服务商排序：数字越小越大
     */
    private Integer sortNum;

}
