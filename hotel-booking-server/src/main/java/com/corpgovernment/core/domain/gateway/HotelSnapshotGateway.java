package com.corpgovernment.core.domain.gateway;


import com.corpgovernment.converter.model.queryparam.QueryParamModel;
import com.corpgovernment.core.domain.model.snapshot.config.BookingConfigModel;
import com.corpgovernment.core.domain.model.snapshot.config.HotelQueryContextModel;
import com.corpgovernment.core.domain.model.snapshot.fee.HotelFeeInfoModel;
import com.corpgovernment.core.domain.model.snapshot.product.ProductSnapshotModel;
import com.corpgovernment.core.domain.model.snapshot.product.RoomInfoModel;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelFeeSnapshotResponse;


/**
 * <AUTHOR>
 * @date 2024/7/23
 */
public interface HotelSnapshotGateway {
    ProductSnapshotModel getHotelProductSnapshot(String token,String productId,String supplierCode,String hotelId,String roomId,String paymentMethod);

    BookingConfigModel getBookingConfigSnapshot(String token);

    void updateHotelProductSnapshot(String token, HotelFeeInfoModel hotelFeeInfoModel,RoomInfoModel roomInfo);

     HotelQueryContextModel getHotelContextSnapshot(String token);

    QueryParamModel getQueryParam(String token);
     void saveHotelFeeSnapshot(String token, HotelFeeInfoModel hotelFeeInfoModel);

    /**
     * 通过token获取酒店以及房型信息
     * @param token
     * @return
     */
    ProductSnapshotModel getHotelProductSnapshot(String token);
    
    GetHotelFeeSnapshotResponse getHotelFeeSnapshot(String token);
    
}
