package com.corpgovernment.core.domain.hotelpage.service.keyword.impl;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.domain.hotelpage.gateway.IHotelPageGateway;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelAdvancedFilter;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelDistanceDesc;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPositionFilter;
import com.corpgovernment.core.domain.hotelpage.model.entity.KeyWordRequest;
import com.corpgovernment.core.domain.hotelpage.model.enums.KeyWordTypeEnum;
import com.corpgovernment.core.domain.hotelpage.service.keyword.IKeyWordService;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2024/4/13
 */
@Service
public class RangePositionKeyWordService implements IKeyWordService {

    @Resource
    private IHotelPageGateway hotelPageGateway;

    @Override
    public void handleKeyWord(HotelPageRequest hotelPageRequest, KeyWordRequest keyWordRequest) {
        if (hotelPageRequest == null || keyWordRequest == null) {
            return;
        }

        HotelPositionFilter old = Null.or(hotelPageRequest.getHotelPositionFilter(), new HotelPositionFilter());

        HotelDistanceDesc hotelDistanceDesc = new HotelDistanceDesc();
        HotelPositionFilter hotelPositionFilter = new HotelPositionFilter();
        HotelAdvancedFilter hotelAdvancedFilter = Null.or(hotelPageRequest.getHotelAdvancedFilter(), new HotelAdvancedFilter());

        String keyword = keyWordRequest.getKeyword();
        Double lat = keyWordRequest.getKeywordLat();
        Double lon = keyWordRequest.getKeywordLon();

        // 位置筛选
        if (lat != null && lon != null) {
            hotelPositionFilter.setLatitude(lat);
            hotelPositionFilter.setLongitude(lon);
            hotelPositionFilter.setRadius(HotelCoreConstant.DEFAULT_RADIUS);
        } else {
            hotelAdvancedFilter.setKeyword(keyword);
        }

        // 文案
        if (lat != null && lon != null && StringUtils.isNotBlank(keyword)) {
            hotelDistanceDesc.setLat(lat);
            hotelDistanceDesc.setLon(lon);
            hotelDistanceDesc.setPointDesc(keyword);
        }

        // 直线距离
        String radius = hotelPageGateway.getRadius(old);
        if (StringUtils.isNotBlank(radius) && lat != null && lon != null) {
            hotelPositionFilter.setLatitude(lat);
            hotelPositionFilter.setLongitude(lon);
            hotelPositionFilter.setRadius(radius);
        }

        hotelPageRequest.setHotelDistanceDesc(hotelDistanceDesc);
        hotelPageRequest.setHotelPositionFilter(hotelPositionFilter);
        hotelPageRequest.setHotelAdvancedFilter(hotelAdvancedFilter);
    }

    @Override
    public Set<KeyWordTypeEnum> keyWordTypeEnumList() {
        return new HashSet<>(Arrays.asList(KeyWordTypeEnum.SCENIC_AREA, KeyWordTypeEnum.LANDMARK, KeyWordTypeEnum.AIRPORT,
                KeyWordTypeEnum.INTL_AIRPORT, KeyWordTypeEnum.METRO_STATION, KeyWordTypeEnum.RAILWAY_STATION, KeyWordTypeEnum.CORP_PLACE));
    }
}
