package com.corpgovernment.core.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Getter
@AllArgsConstructor
public enum HotelTagEnum {

    // 酒店列表无结果标签
    DISTANCE_DESC_NOT_SHOW("距离文案未展示"),
    DISTANCE_FILTER_NOT_EFFECT("直线距离筛选未生效"),
    POSITION_FILTER_NOT_EFFECT("位置筛选未生效"),
    CITY_RELATION_GET_FAIL("城市关系获取失败"),

    // 酒店页
    HOTEL_PAGE_CACHE("酒店页缓存"),
    HOTEL_PAGE_METADATA_CACHE("酒店页元数据缓存"),
    HOTEL_PAGE_RETURN_NORMAL("酒店页管理模块返回正常"),
    HOTEL_PAGE_RETURN_ALL_NO_RESULT("酒店页管理模块返回全部无结果"),
    HOTEL_PAGE_RETURN_PART_NO_RESULT("酒店页管理模块返回部分无结果"),

    // 酒店列表
    HOTEL_LIST_METADATA_CACHE("酒店列表元数据缓存"),
    HOTEL_LIST_FALLBACK_QUERY("酒店列表页兜底查询"),
    HOTEL_LIST_GET_HOTEL_CONFIG("获取酒店配置"),
    HOTEL_LIST_CALL_HOTEL_PAGE("调用酒店页管理模块"),
    HOTEL_LIST_RESULT("酒店列表查询结果"),

    // 供应商
    QUERY_HOTEL_DETAIL_FROM_SUPPLIER("供应商查询酒店详情"),
    QUERY_ROOM_PACKAGE_LIST_FROM_SUPPLIER("供应商查询房型套餐列表"),
    QUERY_HOTEL_LIST_BY_HOTEL_ID_LIST_FROM_SUPPLIER("根据酒店ID查询酒店列表"),
    QUERY_HOTEL_LIST_FROM_SUPPLIER("酒店列表查询"),

    // 酒店详情
    HOTEL_DETAIL_CACHE_HIT("酒店详情缓存命中"),
    HOTEL_DETAIL_RESULT("酒店详情结果"),
    HOTEL_DETAIL_GET_HOTEL_CONFIG("获取酒店配置"),
    HOTEL_DETAIL_CALL_SUPPLIER("从供应商获取酒店详情"),

    ;

    private final String simpleDesc;

}
