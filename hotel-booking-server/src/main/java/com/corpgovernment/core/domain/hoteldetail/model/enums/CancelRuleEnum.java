package com.corpgovernment.core.domain.hoteldetail.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/14
 */
@Getter
@AllArgsConstructor
public enum CancelRuleEnum {

//    用于兼容供应商
//    NOT_ALLOWED("NOT_ALLOWED","不可取消",8, null, null, null),

    UN_KNOWN("UN_KNOWN","未知",0, "UNKNOWN", null, null),
    FREE("FREE","免费取消",1, "Free", null, null),
    TIME_LIMIT("TIME_LIMIT","限时取消", 2, "LIMIT", null, null),
    CANNOT_CANCEL("CANNOT_CANCEL","不可取消",8, "NOT_ALLOWED", "IRREVOCABLE", "CanNotCancelation"),
    FREE_IN_30_MINUTE("FREE_IN_30_MINUTE","30分钟内免费取消",null, "FREE_CANCEL_IN_30_MINUTES", null, null),
    LADDER("LADDER","阶梯取消",null, "LADDER_LIMIT", "LADDER_FREE", "Ladder");

    private final String code;
    private final String desc;
    private final Integer type;
    private final String compatibleCode1;
    private final String compatibleCode2;
    private final String compatibleCode3;

    private static final Map<String, CancelRuleEnum> map = new HashMap<>();

    static {
        for (CancelRuleEnum cancelRuleEnum : values()) {
            map.put(cancelRuleEnum.getCode(), cancelRuleEnum);
            if (StringUtils.isNotBlank(cancelRuleEnum.getCompatibleCode1())) {
                map.put(cancelRuleEnum.getCompatibleCode1(), cancelRuleEnum);
            }
            if (StringUtils.isNotBlank(cancelRuleEnum.getCompatibleCode2())) {
                map.put(cancelRuleEnum.getCompatibleCode2(), cancelRuleEnum);
            }
            if (StringUtils.isNotBlank(cancelRuleEnum.getCompatibleCode3())) {
                map.put(cancelRuleEnum.getCompatibleCode3(), cancelRuleEnum);
            }
        }
    }

    public static CancelRuleEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }
    /**
     * 根据code获取type的静态方法
     * @param code
     * @return
     */
    public static Integer getTypeByCode(String code) {
        for (CancelRuleEnum cancelRule : CancelRuleEnum.values()) {
            if (cancelRule.code.equals(code)) {
                return cancelRule.type;
            }
        }
        return null; // 如果没有找到匹配的code，则返回null
    }
}
