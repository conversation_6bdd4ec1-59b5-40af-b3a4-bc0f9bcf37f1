package com.corpgovernment.core.domain.hotelpage.model.entity;

import com.corpgovernment.core.domain.hoteldetail.model.entity.MemberTagInfo;
import com.corpgovernment.core.domain.hotelpage.model.enums.UnAvailableReasonEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelCard {

    private String hotelId;
    private String supplierCode;
    private Boolean directSupplier;
    private String supplierName;

    private String name;
    private String nameEn;
    private String address;
    private String distanceText;
    // 后排序使用
    private Double distance;
    private Double lon;
    private Double lat;
    private String logoUrl;
    private Integer star;
    private Boolean starLicence;
    private String levelName;
    private String reviewScore;
    private List<String> facilityList;
    private Boolean viewed;
    private String groupId;

    private Integer protocolType;
    private HotelPrice minHotelPrice;
    private List<HotelSupplier> supplierList;
    private Integer defaultSortNum;

    private String travelStandardMark;

    /**
     * 所有酒店Tag
     */
    private List<MemberTagInfo> allHotelTagList;

    private UnAvailableReasonEnum unavailableReason;

}
