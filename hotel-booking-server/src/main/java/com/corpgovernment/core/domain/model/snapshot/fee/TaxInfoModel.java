package com.corpgovernment.core.domain.model.snapshot.fee;

import com.corpgovernment.dto.snapshot.dto.hotel.fee.TaxFeeType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/23
 */
@Data
public class TaxInfoModel {
    /**
     * 关于该房型房费包含的所有种类的税和服务费的描述
     */
    private String includeTaxFeeDesc;
    /**
     * 关于该房型房费未包含的所有种类的税和服务费的描述
     */
    private String excludeTaxFeeDesc;
    /**
     * 税费明细列表
     */
    private List<TaxFeeModel> taxFeeInfoList;
}
