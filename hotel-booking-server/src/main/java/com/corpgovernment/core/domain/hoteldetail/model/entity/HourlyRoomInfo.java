package com.corpgovernment.core.domain.hoteldetail.model.entity;

import cn.hutool.core.util.StrUtil;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025-06-10 15:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HourlyRoomInfo {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    // 入住日期 yyyy-MM-dd
    private String checkInDate;
    
    // 是否是钟点房
    private Boolean hourlyRoom;
    
    // 钟点房提示
    private String hourlyRoomTip;
    
    // 钟点房时长
    private Integer durationHour;
    
    // 钟点房可订起始时间
    private Integer intervalStartMinute;
    
    // 钟点房可订结束时间
    private Integer intervalEndMinute;
    
    // 可用时间段
    private List<String> availableTimeSlots;
    
    // 可用时间段描述
    private String availablePeriodsDesc;
    
    // 钟点房描述
    private String hourlyRoomDesc;
    
    public void generateDesc() {
        generateHourlyRoomDesc();
        generateAvailablePeriodsDesc();
    }
    
    public void generateAvailableTimeSlots() {
        if (StringUtils.isBlank(checkInDate) || intervalStartMinute == null || intervalEndMinute == null || durationHour == null) {
            return;
        }
        
        // 开始时间
        LocalDate checkInLocalDate = LocalDate.parse(checkInDate, DATE_FORMATTER);
        LocalDate today = LocalDate.now();
        int currentStart;
        if (checkInLocalDate.isBefore(today)) {
            return;
        } else if (checkInLocalDate.isEqual(today)) {
            LocalTime now = LocalTime.now();
            int currentMinute = now.getHour() * 60 + now.getMinute();
            currentStart = Math.max(computeNearestUpMinute(intervalStartMinute), computeNearestUpMinute(currentMinute));
        } else {
            currentStart = computeNearestUpMinute(intervalStartMinute);
        }
        // 结束时间
        int endMinute = computeNearestDownMinute(intervalEndMinute);
        // 间隔时长
        int durationMinute = durationHour * 60;
        
        availableTimeSlots = new ArrayList<>();
        for (int i = 0; i < 30; i++) {
            // 结束时间
            int currentEnd = currentStart + durationMinute;
            if (currentEnd >= endMinute) {
                currentEnd = endMinute;
            }
            
            // 只有当开始时间早于结束时间时才添加
            if (currentStart < currentEnd) {
                availableTimeSlots.add(StrUtil.format("{}-{}", formatMinutesToTime(currentStart), formatMinutesToTime(currentEnd)));
            }

            // 移动到下一个时间段（半小时后）
            currentStart = currentStart + durationMinute;
            
            // 检查是否已经超过结束时间
            if (currentStart >= endMinute) {
                break;
            }
        }
    }
    
    public void generateHourlyRoomDesc() {
        if (intervalStartMinute == null || intervalEndMinute == null || durationHour == null) {
            return;
        }
        
        hourlyRoomDesc = StrUtil.format("可住时段：{}-{}，连住{}小时",
                formatMinutesToTime(intervalStartMinute),
                formatMinutesToTime(intervalEndMinute),
                durationHour);
    }
    
    public void generateAvailablePeriodsDesc() {
        if (intervalStartMinute == null || intervalEndMinute == null) {
            return;
        }
        
        availablePeriodsDesc = StrUtil.format("可住时段：{}-{}",
                formatMinutesToTime(intervalStartMinute),
                formatMinutesToTime(intervalEndMinute));
    }
    
    private int computeNearestUpMinute(int minute) {
        if (minute % 30 == 0) {
            return minute;
        }
        
        // 否则向上取整到最近的60的倍数
        return ((minute / 60) + 1) * 60;
    }
    
    private int computeNearestDownMinute(int minute) {
        if (minute % 30 == 0) {
            return minute;
        }
        
        // 否则向下取整到最近的60的倍数
        return ((minute / 60) - 1) * 60;
    }
    
    private String formatMinutesToTime(int minute) {
        int MINUTES_PER_DAY = 1440;
        
        // 计算总天数和剩余分钟
        int days = minute / MINUTES_PER_DAY;
        int remainingMinutes = minute % MINUTES_PER_DAY;
        if (days > 1) {
            return null;
        }
        
        int hours = remainingMinutes / 60;
        int minutes = remainingMinutes % 60;
        
        // 格式化时间
        String time =  String.format("%02d:%02d", hours, minutes);
        
        // 添加“次日”信息
        if (days == 1) {
            time = time + "(次日)";
        }
        
        return time;
    }
    
}
