package com.corpgovernment.core.domain.hoteldetail.model.entity;

import com.corpgovernment.core.domain.common.model.entity.ServiceCharge;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SupplierData {

    private String hotelId;
    private String basicRoomId;

    private City city;
    private District district;
    private List<Zone> zoneList;

    private String brandId;
    private String groupId;

    private String supplierCode;
    private String supplierName;
    private Boolean directSupplier;
    private Boolean abroad;
    private Integer roomQuantity;
    private Integer dayQuantity;
    private String areaDesc;
    private List<FacilityGroup> facilityGroupList;
    private List<FacilityGroup> basicRoomFacilityGroupList;
    private Map<String, List<Picture>> pictureListMap;

    private List<ServiceCharge> serviceChargeList;
    
    private Boolean resourcePriceIncludeServiceCharge;
    
    private String checkInDate;

}
