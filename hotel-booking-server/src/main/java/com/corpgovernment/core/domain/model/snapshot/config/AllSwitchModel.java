package com.corpgovernment.core.domain.model.snapshot.config;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/19
 */
@Data
public class AllSwitchModel {
    private String orgId;
    private String uId;
    private List<SwitchModel> switchInfoSoaList;
    private Map<String, SwitchModel> switchInfoSoaMap;
    private String urgentPayType;
    private Boolean urgentEnable;
    private String policyRange;
}
