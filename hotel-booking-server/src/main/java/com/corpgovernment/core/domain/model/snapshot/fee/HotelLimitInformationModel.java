package com.corpgovernment.core.domain.model.snapshot.fee;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/29
 */
@Data
public class HotelLimitInformationModel {
    /**
     * 房型人数限制
     *
     * 每间房最多可住几人
     */
    private Integer guestPerson;
    /**
     * 预订间数限制
     *
     * 本次预订最少要订几间
     */
    private Integer minBookingRoomNum;
    /**
     * 预订间数限制
     *
     * 本次预订最多可订几间
     */
    private Integer maxBookingRoomNum;

}
