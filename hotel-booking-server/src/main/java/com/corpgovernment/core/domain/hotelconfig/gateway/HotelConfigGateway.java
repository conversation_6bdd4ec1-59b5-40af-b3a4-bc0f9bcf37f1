package com.corpgovernment.core.domain.hotelconfig.gateway;

import com.corpgovernment.api.basic.enums.HotelAreaConfigurationEnum;
import com.corpgovernment.api.supplier.vo.request.ListSupplierUrlReqVo;
import com.corpgovernment.api.supplier.vo.response.ListSupplierUrlRespVo;
import com.corpgovernment.client.ManagementClientUtil;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.converter.model.context.ApplyTripContextModel;
import com.corpgovernment.converter.model.passenger.PassengerParamModel;
import com.corpgovernment.converter.model.context.HotelContextModel;
import com.corpgovernment.converter.model.passenger.PassengerInfo;
import com.corpgovernment.converter.model.passenger.PassengerParamModel;
import com.corpgovernment.converter.model.queryparam.QueryParamModel;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.apollo.IHotelCoreApolloDao;
import com.corpgovernment.core.dao.openfeign.IHotelCoreOpenFeignDao;
import com.corpgovernment.core.domain.common.gateway.CommonGateway;
import com.corpgovernment.core.domain.common.model.entity.OverLimitReasonCode;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.ServiceCharge;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.HotelForceChummageEnum;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeTypeEnum;
import com.corpgovernment.core.domain.common.model.enums.TravelAttributeEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.hotelconfig.model.entity.Guest;
import com.corpgovernment.core.domain.hotelconfig.model.entity.HotelRoomCheckInInfo;
import com.corpgovernment.core.domain.hotelconfig.model.entity.MultiTravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.entity.RcInfo;
import com.corpgovernment.core.domain.hotelconfig.model.entity.SignalTravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelApplication;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelAttribute;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.entity.UrgentReserveSwitch;
import com.corpgovernment.core.domain.hotelconfig.model.enums.AreaTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.EmployeeTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.GenderEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PaymentMethodEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.RcTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.RemarkConfigEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.SnapshotDataTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hotelpage.model.enums.SortDirectionEnum;
import com.corpgovernment.core.domain.hotelpage.model.enums.SortTypeEnum;
import com.corpgovernment.dto.config.AllSwitchDTO;
import com.corpgovernment.dto.config.PayInfoDTO;
import com.corpgovernment.dto.config.RcInfoDTO;
import com.corpgovernment.dto.config.ServiceFeeDTO;
import com.corpgovernment.dto.config.SupplierConfigDTO;
import com.corpgovernment.dto.config.SwitchDTO;
import com.corpgovernment.dto.config.response.GetBookingConfigByTokenResponse;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.SnapShotDTO;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardRequest;
import com.corpgovernment.dto.travelstandard.request.PassengerGroupDTO;
import com.corpgovernment.dto.travelstandard.request.PassengerRequest;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.BrandInfoDTO;
import com.corpgovernment.dto.travelstandard.response.rule.BrandRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.CohabitRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.FloatPriceRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.HotelFixRuleVo;
import com.corpgovernment.dto.travelstandard.response.rule.HotelStepRuleVo;
import com.corpgovernment.dto.travelstandard.response.rule.OffPeakSeasonRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.PriceRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.RuleChainVO;
import com.corpgovernment.dto.travelstandard.response.rule.StarRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.StepStandardVo;
import com.corpgovernment.util.BizTypeContextUtil;
import com.ctrip.corp.obt.generic.core.context.RequestContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/4/13
 */
@Repository
@Slf4j
public class HotelConfigGateway implements IHotelConfigGateway {

    @Resource
    private IHotelCoreOpenFeignDao hotelCoreOpenFeignDao;
    @Resource
    private IHotelCoreApolloDao hotelCoreApolloDao;
    @Autowired
    private CommonGateway commonGateway;
    
    @Resource
    private ManagementClientUtil managementClientUtil;
    
    @Resource
    private ICommonDomainService commonDomainService;

    @Override
    public Map<String, List<String>> getSupplierShieldCondition() {
        return hotelCoreApolloDao.getSupplierShieldCondition();
    }

    @Override
    public TravelConfig getTravelConfig(String token, String travelMode, String productType, List<String> operatorTypeList) {
        // 获取预定配置
        GetBookingConfigByTokenResponse bookingConfig = hotelCoreOpenFeignDao.getBookingConfig(token);
        log.info("预定配置 bookingConfig={}", JsonUtils.toJsonString(bookingConfig));
        // 组装
        return getTravelConfig(
                getBizTypeEnum(productType),
                TravelModeEnum.PUB.getCode().equals(travelMode) ? TravelModeEnum.PUB : TravelModeEnum.OWN,
                operatorTypeList,
                bookingConfig,
                null);
    }
    
    @Override
    public TravelStandard getTravelStandard(String token) {
        // 获取差标
        List<TravelStandardResponse> travelStandardResponseList = hotelCoreOpenFeignDao.getTravelStandardByToken(token);
        log.info("获取差标 travelStandardResponseList={}", JsonUtils.toJsonString(travelStandardResponseList));

        // 获取快照
        QuerySnapshotResponseDTO snapshot = hotelCoreOpenFeignDao.getSnapshot(token, Arrays.asList(SnapshotDataTypeEnum.APPLY_TRIP_CONTEXT.getCode(), SnapshotDataTypeEnum.QUERY_PARAM.getCode()));
        log.info("获取快照 snapshot={}", JsonUtils.toJsonString(snapshot));

        // 出差申请单快照
        ApplyTripContextModel applyTripContextModel = null;
        // 查询参数
        QueryParamModel queryParamModel = null;
        if (snapshot != null && CollectionUtils.isNotEmpty(snapshot.getSnapshotList())) {
            List<SnapShotDTO> snapshotList = snapshot.getSnapshotList();
            applyTripContextModel = snapshotList.stream()
                    .filter(item -> item != null && item.getSnapshotData() != null
                            && StringUtils.equalsIgnoreCase(item.getDataType(), SnapshotDataTypeEnum.APPLY_TRIP_CONTEXT.getCode()))
                    .map(item -> JsonUtils.parse(item.getSnapshotData(), ApplyTripContextModel.class)).findFirst().orElse(null);
            queryParamModel = snapshotList.stream()
                    .filter(item -> item != null && item.getSnapshotData() != null
                            && StringUtils.equalsIgnoreCase(item.getDataType(), SnapshotDataTypeEnum.QUERY_PARAM.getCode()))
                    .map(item -> JsonUtils.parse(item.getSnapshotData(), QueryParamModel.class)).findFirst().orElse(null);
        }

        return TravelStandard.builder()
                .signalTravelStandard(buildSignalTravelStandard(travelStandardResponseList))
                .multiTravelStandard(buildMultiTravelStandard(travelStandardResponseList))
                .urgencyReserve(queryParamModel != null ? queryParamModel.getUrgentApply() : null)
                .travelApplication(applyTripContextModel != null
                        ? TravelApplication.builder()
                        .travelApplicationId(applyTripContextModel.getApprovalNo())
                        .travelId(applyTripContextModel.getTravelNo())
                        .bookAddress(applyTripContextModel.getAddress())
                        .bookLat(applyTripContextModel.getLatitude())
                        .bookLon(applyTripContextModel.getLongitude()).build()
                        : new TravelApplication()).build();
    }
    
    private Boolean getOverseasHotelControlIncludeExtraTax(AllSwitchDTO allSwitch, BizTypeEnum bizTypeEnum, TravelModeEnum travelModeEnum) {
        if (!Objects.equals(BizTypeEnum.HOTEL_INTL, bizTypeEnum) || !Objects.equals(travelModeEnum, TravelModeEnum.PUB)) {
            return Boolean.FALSE;
        }
        List<Integer> switchValue = getSwitchValue(allSwitch, TravelAttributeEnum.OVERSEAS_HOTEL_CONTROL_INCLUDE_EXTRA_TAX.getCode());
        return CollectionUtils.isNotEmpty(switchValue) && switchValue.get(0) != null && switchValue.get(0) == 1;
    }

    
    @Override
    public TravelConfig getTravelConfig(String token, List<String> operatorTypeList) {
        // 获取预定配置
        GetBookingConfigByTokenResponse bookingConfig = hotelCoreOpenFeignDao.getBookingConfig(token);
        log.info("预定配置 bookingConfig={}", JsonUtils.toJsonString(bookingConfig));
        
        // 获取快照
        QuerySnapshotResponseDTO snapshot = hotelCoreOpenFeignDao.getSnapshot(
                token,
                Arrays.asList(
                        SnapshotDataTypeEnum.HOTEL_CONTEXT.getCode(),
                        SnapshotDataTypeEnum.QUERY_PARAM.getCode(),
                        SnapshotDataTypeEnum.PASSENGER_DATA.getCode()));
        log.info("获取快照 snapshot={}", JsonUtils.toJsonString(snapshot));
        QueryParamModel queryParamModel = null;
        HotelContextModel hotelContextModel = null;
        PassengerParamModel passengerParamModel = null;
        if (snapshot != null && CollectionUtils.isNotEmpty(snapshot.getSnapshotList())) {
            List<SnapShotDTO> snapshotList = snapshot.getSnapshotList();
            hotelContextModel = snapshotList.stream()
                    .filter(item -> item != null && item.getSnapshotData() != null
                            && StringUtils.equalsIgnoreCase(item.getDataType(), SnapshotDataTypeEnum.HOTEL_CONTEXT.getCode()))
                    .map(item -> JsonUtils.parse(item.getSnapshotData(), HotelContextModel.class)).findFirst().orElse(null);
            queryParamModel = snapshotList.stream()
                    .filter(item -> item != null && item.getSnapshotData() != null
                            && StringUtils.equalsIgnoreCase(item.getDataType(), SnapshotDataTypeEnum.QUERY_PARAM.getCode()))
                    .map(item -> JsonUtils.parse(item.getSnapshotData(), QueryParamModel.class)).findFirst().orElse(null);
            passengerParamModel = snapshotList.stream()
                    .filter(item -> item != null && item.getSnapshotData() != null
                            && StringUtils.equalsIgnoreCase(item.getDataType(), SnapshotDataTypeEnum.PASSENGER_DATA.getCode()))
                    .map(item -> JsonUtils.parse(item.getSnapshotData(), PassengerParamModel.class)).findFirst().orElse(null);
        }
        
        return getTravelConfig(
                getBizTypeEnum(queryParamModel == null ? BizTypeEnum.HOTEL.getCode() : queryParamModel.getProductType()),
                hotelContextModel == null || TravelModeEnum.PUB.getCode().equals(hotelContextModel.getCorpPayType()) ? TravelModeEnum.PUB : TravelModeEnum.OWN,
                operatorTypeList,
                bookingConfig,
                passengerParamModel);
    }
    
    private TravelConfig getTravelConfig(BizTypeEnum bizTypeEnum,
                                         TravelModeEnum travelModeEnum,
                                         List<String> operatorTypeList,
                                         GetBookingConfigByTokenResponse bookingConfig,
                                         PassengerParamModel passengerParamModel) {
        if (bookingConfig == null) {
            return null;
        }
        
        // 把产线放到上下文中
        BizTypeContextUtil.saveBizTypeToContext(bizTypeEnum);
        
        // 过滤供应商
        List<SupplierConfigDTO> supplierConfigDtoList = filterSupplierConfigDtoList(bookingConfig.getSupplierConfigList(), travelModeEnum.getCode());
        
        // 差旅属性
        AllSwitchDTO allSwitch = bookingConfig.getAllSwitch();
        
        return TravelConfig.builder()
                .shieldStarList(getShieldStarList(allSwitch))
                .hotelListSortRule(Null.or(getHotelListSortRule(allSwitch), 0))
                .hotelRoomSortRule(Null.or(getHotelRoomSortRule(allSwitch), 0))
                .paymentMethodList(getPaymentMethod(travelModeEnum.getCode(), bookingConfig.getPayInfoList()))
                .bizTypeEnum(bizTypeEnum)
                .prioritySupplierCodeList(buildPrioritySupplierCodeList(supplierConfigDtoList))
                .supplierProductListMap(getSupplierProductListMap(bizTypeEnum, travelModeEnum, supplierConfigDtoList, operatorTypeList))
                .travelModeEnum(travelModeEnum)
                .urgentReservePaymentMethodList(buildUrgentReservePaymentMethodList(allSwitch))
                .hotelForceChummageEnum(Null.or(getHotelForceChummageEnum(allSwitch), HotelForceChummageEnum.NO_FORCE_CHUMMAGE))
                .hotelForceSameSexChummage(Null.or(getHotelForceSameSexChummage(allSwitch), false))
                .hotelRoomCheckInInfoList(buildHotelRoomCheckInInfoList(passengerParamModel))
                .rcInfoList(buildRcInfoList(bookingConfig.getRcInfoList()))
                .overseasHotelControlIncludeExtraTax(getOverseasHotelControlIncludeExtraTax(allSwitch, bizTypeEnum, travelModeEnum))
                .priceControlStrategyEnum(Null.or(getPriceControlStrategyEnum(allSwitch), PriceControlStrategyEnum.AVG_PRICE))
                .resourcePriceIncludeServiceCharge(extractResourcePriceIncludeServiceCharge(allSwitch))
                .domesticHotelBookHourlyRoom(extractDomesticHotelBookHourlyRoom(allSwitch))
                .build();
    }
    
    private Boolean extractDomesticHotelBookHourlyRoom(AllSwitchDTO allSwitch) {
        List<String> switchValue = getSwitchValue(allSwitch, TravelAttributeEnum.DOMESTIC_HOTEL_BOOK_HOURLY_ROOM.getCode(), String.class);
        if (CollectionUtils.isEmpty(switchValue) || switchValue.get(0) == null) {
            return false;
        }
        return StringUtils.equalsIgnoreCase(switchValue.get(0), "YES");
    }
    
    private boolean extractResourcePriceIncludeServiceCharge(AllSwitchDTO allSwitch) {
        List<Integer> switchValue = getSwitchValue(allSwitch, TravelAttributeEnum.RESOURCE_PRICE_INCLUDE_SERVICE_CHARGE.getCode());
        if (CollectionUtils.isEmpty(switchValue) || switchValue.get(0) == null) {
            return false;
        }
        return switchValue.get(0) == 1;
    }
    
    private PriceControlStrategyEnum getPriceControlStrategyEnum(AllSwitchDTO allSwitch) {
        List<Integer> switchValue = getSwitchValue(allSwitch, TravelAttributeEnum.HOTEL_PRICE_CONTROL_STRATEGY.getCode());
        if (CollectionUtils.isEmpty(switchValue) || switchValue.get(0) == null) {
            return null;
        }
        return PriceControlStrategyEnum.getEnum(switchValue.get(0).toString());
    }
    
    private List<RcInfo> buildRcInfoList(List<RcInfoDTO> rcInfoList) {
        if (CollectionUtils.isEmpty(rcInfoList)) {
            return null;
        }
        
        List<RcInfo> resultList = new ArrayList<>();
        
        for (RcInfoDTO rcInfoDTO : rcInfoList) {
            if (rcInfoDTO == null || rcInfoDTO.getStatus() == null || rcInfoDTO.getStatus() != 1) {
                continue;
            }
            
            resultList.add(RcInfo.builder()
                    .id(rcInfoDTO.getId())
                    .name(rcInfoDTO.getName())
                    .code(rcInfoDTO.getCode())
                    .remarkConfigEnum(RemarkConfigEnum.getEnum(rcInfoDTO.getRemarkType()))
                    .rcTypeEnumList(buildRcTypeEnumList(rcInfoDTO.getTypes()))
                    .build());
        }
        return resultList;
    }
    
    private List<RcTypeEnum> buildRcTypeEnumList(List<String> types) {
        if (CollectionUtils.isEmpty(types)) {
            return null;
        }
        
        return types.stream().map(RcTypeEnum::getEnum).filter(Objects::nonNull).collect(Collectors.toList());
    }
    
    private List<HotelRoomCheckInInfo> buildHotelRoomCheckInInfoList(PassengerParamModel passengerParamModel) {
        if (passengerParamModel == null || passengerParamModel.getPassengerInfoSnapshotMap() == null) {
            return null;
        }
        
        List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList = new ArrayList<>();
        passengerParamModel.getPassengerInfoSnapshotMap().forEach((key, value) -> {
            List<Guest> guestList = buildGuestList(value);
            
            if (CollectionUtils.isEmpty(guestList)) {
                return;
            }
            
            hotelRoomCheckInInfoList.add(HotelRoomCheckInInfo.builder()
                    .roomIndex(key)
                    .guestList(guestList).build());
        });
        
        return hotelRoomCheckInInfoList;
    }
    
    private List<Guest> buildGuestList(List<PassengerInfo> value) {
        if (CollectionUtils.isEmpty(value)) {
            return null;
        }
        
        List<Guest> guestList = new ArrayList<>();
        for (PassengerInfo passengerInfo : value) {
            if (passengerInfo == null) {
                continue;
            }
            
            EmployeeTypeEnum employeeTypeEnum = EmployeeTypeEnum.getEnum(passengerInfo.getEmployeeType() == null ? null : passengerInfo.getEmployeeType().toString());
            
            guestList.add(Guest.builder()
                    .uid(Objects.equals(employeeTypeEnum, EmployeeTypeEnum.EXTERNAL_EMPLOYEE) ? passengerInfo.getNoEmployeeId() : passengerInfo.getUid())
                    .employeeTypeEnum(employeeTypeEnum)
                    .orgId(passengerInfo.getOrgId())
                    .genderEnum(GenderEnum.getEnum(passengerInfo.getGender())).build());
        }
        return guestList;
    }
    
    private Boolean getHotelForceSameSexChummage(AllSwitchDTO allSwitch) {
        if (allSwitch == null || CollectionUtils.isEmpty(allSwitch.getSwitchInfoSoaMap())) {
            return null;
        }
        
        SwitchDTO switchDTO = allSwitch.getSwitchInfoSoaMap().get(TravelAttributeEnum.HOTEL_FORCE_CHUMMAGE.getCode());
        if (switchDTO == null || StringUtils.isBlank(switchDTO.getExtendSwitch())) {
            return null;
        }
        
        List<TravelAttribute> travelAttributeList = JsonUtils.parse(switchDTO.getExtendSwitch(), new TypeReference<List<TravelAttribute>>() {});
        if (CollectionUtils.isEmpty(travelAttributeList)) {
            return null;
        }
        TravelAttribute travelAttribute = travelAttributeList.stream()
                .filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getKey(), TravelAttributeEnum.HOTEL_FORCE_SAME_SEX_CHUMMAGE.getCode()))
                .findFirst().orElse(null);
        
        if (travelAttribute == null || travelAttribute.getNowVal() == null || travelAttribute.getNowVal().get(0) == null) {
            return null;
        }
        
        return StringUtils.equalsIgnoreCase(travelAttribute.getNowVal().get(0), "1");
    }
    
    private HotelForceChummageEnum getHotelForceChummageEnum(AllSwitchDTO allSwitch) {
        List<Integer> switchValue = getSwitchValue(allSwitch, TravelAttributeEnum.HOTEL_FORCE_CHUMMAGE.getCode());
        if (CollectionUtils.isEmpty(switchValue) || switchValue.get(0) == null) {
            return null;
        }
        return HotelForceChummageEnum.getEnum(switchValue.get(0).toString());
    }
    
    
    @Override
    public SignalTravelStandard refreshTravelStandardToken(String token, String cityId, String districtId, String countyId) {
        if (StringUtils.isBlank(token) || (StringUtils.isBlank(districtId) && StringUtils.isBlank(countyId))) {
            return null;
        }
        
        QuerySnapshotResponseDTO snapshot = hotelCoreOpenFeignDao.getSnapshot(
                token,
                Arrays.asList(SnapshotDataTypeEnum.PASSENGER_DATA.getCode(), SnapshotDataTypeEnum.QUERY_PARAM.getCode()));
        log.info("获取快照 snapshot={}", JsonUtils.toJsonString(snapshot));
        
        QueryParamModel queryParamModel = getQueryParamModel(snapshot);
        PassengerParamModel passengerParamModel = getPassengerParamModel(snapshot);
        if (queryParamModel == null || passengerParamModel == null) {
            return null;
        }
        
        // 请求参数组装
        GetTravelStandardRequest getTravelStandardRequest = new GetTravelStandardRequest();
        getTravelStandardRequest.setHasGeneratedToken(token);
        getTravelStandardRequest.setUid(queryParamModel.getPolicyUid());
        getTravelStandardRequest.setOrgId(queryParamModel.getPolicyOrgId());
        getTravelStandardRequest.setEmployeeType(queryParamModel.getPolicyEmployeeType());
        getTravelStandardRequest.setStartDate(cn.hutool.core.date.DateUtil.parseDate(queryParamModel.getStartDate()));
        getTravelStandardRequest.setEndDate(cn.hutool.core.date.DateUtil.parseDate(queryParamModel.getEndDate()));
        getTravelStandardRequest.setBizType(queryParamModel.getProductType());
        getTravelStandardRequest.setTrafficId(queryParamModel.getTravelNo());
        List<PassengerGroupDTO> passengerGroupList = new ArrayList<>();
        passengerParamModel.getPassengerInfoSnapshotMap().forEach((key, value) -> {
            PassengerGroupDTO passengerGroupDTO = new PassengerGroupDTO();
            passengerGroupDTO.setGroupId(String.valueOf(key));
            passengerGroupDTO.setPassengerList(value.stream().map(item -> {
                PassengerRequest passengerRequest = new PassengerRequest();
                if (2 == item.getEmployeeType()) {
                    passengerRequest.setUid(item.getNoEmployeeId());
                } else {
                    passengerRequest.setUid(item.getUid());
                }
                passengerRequest.setOrgId(item.getOrgId());
                passengerRequest.setEmployeeType(item.getEmployeeType());
                return passengerRequest;
            }).collect(Collectors.toList()));
            passengerGroupList.add(passengerGroupDTO);
        });
        getTravelStandardRequest.setPassengerGroupList(passengerGroupList);
        getTravelStandardRequest.setCityId(cityId);
        if (StringUtils.isNotBlank(countyId)) {
            getTravelStandardRequest.setCountryCityId(countyId);
        }
        if (StringUtils.isNotBlank(districtId)) {
            getTravelStandardRequest.setLocationId(districtId);
        }
        List<TravelStandardResponse> travelStandardResponseList = managementClientUtil.getTravelStandardToken(getTravelStandardRequest);
        log.info("刷新token请求 getTravelStandardRequest:{} travelStandardResponseList:{}", JsonUtils.toJsonString(getTravelStandardRequest), JsonUtils.toJsonString(travelStandardResponseList));
        return buildSignalTravelStandard(travelStandardResponseList);
    }
    
    @Override
    @BusinessBehaviorMonitor
    public TravelApplication getTravelApplication(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        // 获取快照
        QuerySnapshotResponseDTO snapshot = hotelCoreOpenFeignDao.getSnapshot(token, Collections.singletonList(SnapshotDataTypeEnum.APPLY_TRIP_CONTEXT.getCode()));
        log.info("获取快照 snapshot={}", JsonUtils.toJsonString(snapshot));
        
        // 出差申请单快照
        ApplyTripContextModel applyTripContextModel = getApplyTripContextModel(snapshot);
        if (applyTripContextModel == null) {
            return null;
        }
        
        return TravelApplication.builder()
                .travelApplicationId(applyTripContextModel.getApprovalNo())
                .travelId(applyTripContextModel.getTravelNo())
                .bookAddress(applyTripContextModel.getAddress())
                .bookLat(applyTripContextModel.getLatitude())
                .bookLon(applyTripContextModel.getLongitude()).build();
    }
    
    private ApplyTripContextModel getApplyTripContextModel(QuerySnapshotResponseDTO snapshot) {
        List<SnapShotDTO> snapshotList = Optional.ofNullable(snapshot)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .orElse(null);
        if (CollectionUtils.isEmpty(snapshotList)) {
            return null;
        }
        
        return snapshotList.stream()
                .filter(item -> item != null
                        && item.getSnapshotData() != null
                        && StringUtils.equalsIgnoreCase(item.getDataType(), SnapshotDataTypeEnum.APPLY_TRIP_CONTEXT.getCode()))
                .map(item -> JsonUtils.parse(item.getSnapshotData(), ApplyTripContextModel.class))
                .findFirst()
                .orElse(null);
    }
    
    private PassengerParamModel getPassengerParamModel(QuerySnapshotResponseDTO snapshot) {
        return Optional.ofNullable(snapshot)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .flatMap(list -> list.stream()
                        .filter(item -> item != null
                                && item.getSnapshotData() != null
                                && StringUtils.equalsIgnoreCase(item.getDataType(), SnapshotDataTypeEnum.PASSENGER_DATA.getCode()))
                        .map(item -> JsonUtils.parse(item.getSnapshotData(), PassengerParamModel.class)).findFirst())
                .orElse(null);
    }
    
    private QueryParamModel getQueryParamModel(QuerySnapshotResponseDTO snapshot) {
        return Optional.ofNullable(snapshot)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .flatMap(list -> list.stream()
                        .filter(item -> item != null
                                && item.getSnapshotData() != null
                                && StringUtils.equalsIgnoreCase(item.getDataType(), SnapshotDataTypeEnum.QUERY_PARAM.getCode()))
                        .map(item -> JsonUtils.parse(item.getSnapshotData(), QueryParamModel.class)).findFirst())
                .orElse(null);
    }
    
    /**
     * 获取紧急申请支付渠道
     * @param allSwitchDto 所有配置开关
     * @return 紧急申请支付渠道
     */
    private List<String> buildUrgentReservePaymentMethodList(AllSwitchDTO allSwitchDto) {
        if (allSwitchDto == null || CollectionUtils.isEmpty(allSwitchDto.getSwitchInfoSoaMap())) {
            return null;
        }

        SwitchDTO switchDto = allSwitchDto.getSwitchInfoSoaMap().get(HotelCoreConstant.ATTR_MANAGEMENT_CONTROL);
        if (switchDto == null || StringUtils.isBlank(switchDto.getExtendSwitch())) {
            return null;
        }

        List<UrgentReserveSwitch> urgentReserveSwitchList = JsonUtils.parseArray(switchDto.getExtendSwitch(), UrgentReserveSwitch.class);
        if (CollectionUtils.isEmpty(urgentReserveSwitchList)) {
            return null;
        }

        UrgentReserveSwitch urgentReserveSwitch = urgentReserveSwitchList.stream().filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getKey(), HotelCoreConstant.URGENT_ENABLE)).findFirst().orElse(null);
        if (urgentReserveSwitch == null || CollectionUtils.isEmpty(urgentReserveSwitch.getValueDetail()) || CollectionUtils.isEmpty(urgentReserveSwitch.getNowVal())) {
            return null;
        }
        UrgentReserveSwitch.ValueDetail valueDetail = urgentReserveSwitch.getValueDetail().get(urgentReserveSwitch.getNowVal().get(0));
        if (valueDetail == null || CollectionUtils.isEmpty(valueDetail.getNowVal())) {
            return null;
        }

        String paymentMethod = valueDetail.getNowVal().get(0);
        if (StringUtils.equalsIgnoreCase(paymentMethod, HotelCoreConstant.ALL)) {
            return Arrays.asList(PaymentMethodEnum.ACCNT.getCode(), PaymentMethodEnum.PPAY.getCode());
        }

        return Collections.singletonList(paymentMethod);
    }

    private List<String> buildPrioritySupplierCodeList(List<SupplierConfigDTO> supplierConfigDtoList) {
        if (CollectionUtils.isEmpty(supplierConfigDtoList)) {
            return null;
        }

        return supplierConfigDtoList.stream()
                .sorted(Comparator.comparing((SupplierConfigDTO item) -> Null.or(item.getSortNum(), Integer.MAX_VALUE))
                        .thenComparing(item -> Null.or(item.getSupplierCode(), "")))
                .map(SupplierConfigDTO::getSupplierCode).collect(Collectors.toList());
    }

    /**
     * 构建多差标
     * @param travelStandardResponseList 差标列表
     * @return 多差标
     */
    private MultiTravelStandard buildMultiTravelStandard(List<TravelStandardResponse> travelStandardResponseList) {
        if (CollectionUtils.isEmpty(travelStandardResponseList)) {
            return null;
        }

        // 选择差标
        TravelStandardResponse travelStandardResponse = travelStandardResponseList.stream()
                .filter(item -> item.getTravelStandardToken() != null
                        && item.getTravelStandardToken().getOwnerType() != null
                        && item.getTravelStandardToken().getOwnerType() == 5
                        && Boolean.TRUE.equals(item.getRuleStatus()))
                .findFirst().orElse(null);
        log.info("选择的多差标 travelStandardResponse={}", JsonUtils.toJsonString(travelStandardResponse));
        if (travelStandardResponse == null || !Boolean.TRUE.equals(travelStandardResponse.getRuleStatus())) {
            return null;
        }

        // 不限制差标
        if (travelStandardResponse.getRuleChain() == null || CollectionUtils.isEmpty(travelStandardResponse.getRuleChain().getRuleList())) {
            return new MultiTravelStandard();
        }

        // 固定差标
        HotelFixRuleVo hotelFixRuleVo = travelStandardResponse.getRuleChain().getRuleList().stream().filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), "HotelFixRule"))
                .map(HotelFixRuleVo.class::cast).findFirst().orElse(new HotelFixRuleVo());

        // 阶梯差标
        List<StepStandardVo> stepStandardVoList = new ArrayList<>();
        HotelStepRuleVo hotelStepRuleVo = travelStandardResponse.getRuleChain().getRuleList().stream().filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), "HotelStepRule"))
                .map(HotelStepRuleVo.class::cast).findFirst().orElse(null);
        if (hotelStepRuleVo != null && CollectionUtils.isNotEmpty(hotelStepRuleVo.getStepStandardList())) {
            stepStandardVoList = hotelStepRuleVo.getStepStandardList();
        }

        return buildMultiTravelStandard(hotelFixRuleVo, stepStandardVoList);
    }

    private MultiTravelStandard buildMultiTravelStandard(HotelFixRuleVo hotelFixRuleVo, List<StepStandardVo> stepStandardVoList) {
        hotelFixRuleVo = Null.or(hotelFixRuleVo, new HotelFixRuleVo());

        if (CollectionUtils.isEmpty(stepStandardVoList)) {
            return MultiTravelStandard.builder()
                    .ladderTravelStandardList(Collections.singletonList(MultiTravelStandard.LadderTravelStandard.builder()
                            .hotelDistance(hotelFixRuleVo.getScopeLimit() == null ? null : String.valueOf(hotelFixRuleVo.getScopeLimit().doubleValue()))
                            .hotelQuantity(hotelFixRuleVo.getHotelQuantityLimit())
                            .sortTypeEnum(getSortTypeEnum(hotelFixRuleVo.getSortRule()))
                            .sortDirectionEnum(getSortDirectionEnum(hotelFixRuleVo.getSortRule())).build())).build();
        }

        HotelFixRuleVo finalHotelFixRuleVo = hotelFixRuleVo;
        return MultiTravelStandard.builder()
                .ladderTravelStandardList(stepStandardVoList.stream().filter(Objects::nonNull).map(item -> MultiTravelStandard.LadderTravelStandard.builder()
                        .sort(item.getSort())
                        .hotelDistance(finalHotelFixRuleVo.getScopeLimit() == null ? null : String.valueOf(finalHotelFixRuleVo.getScopeLimit().doubleValue()))
                        .hotelQuantity(finalHotelFixRuleVo.getHotelQuantityLimit())
                        .sortTypeEnum(getSortTypeEnum(finalHotelFixRuleVo.getSortRule()))
                        .sortDirectionEnum(getSortDirectionEnum(finalHotelFixRuleVo.getSortRule()))
                        .starList(buildStarList(item.getStarList()))
                        .brandIdList(CollectionUtils.isEmpty(item.getBrandList()) ? null
                                : item.getBrandList().stream().map(BrandInfoDTO::getBrandId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()))
                        .hasBreakfast(StringUtils.equalsIgnoreCase(item.getContainBreakfast(), HotelCoreConstant.T))
                        .minPrice(item.getLowerLimit())
                        .maxPrice(item.getUpperLimit()).build()).collect(Collectors.toList())).build();
    }

    private List<Integer> buildStarList(List<Integer> starList) {
        if (CollectionUtils.isEmpty(starList)) {
            return null;
        }
        
        // 如果只有0就是不限
        if (starList.size() == 1 && starList.get(0) != null && starList.get(0) == 0) {
            return null;
        }

        Set<Integer> starSet = new HashSet<>(starList);
        if (starSet.contains(2)) {
            starSet.add(1);
        }
        return new ArrayList<>(starSet);
    }

    private SortDirectionEnum getSortDirectionEnum(String sortRule) {
        if (StringUtils.equalsIgnoreCase(sortRule, "lowPrice") || StringUtils.equalsIgnoreCase(sortRule, "distance")) {
            return SortDirectionEnum.ASC;
        } else if (StringUtils.equalsIgnoreCase(sortRule, "highPrice") || StringUtils.equalsIgnoreCase(sortRule, "highStar")) {
            return SortDirectionEnum.DESC;
        }
        return SortDirectionEnum.DEFAULT;
    }

    private SortTypeEnum getSortTypeEnum(String sortRule) {
        if (StringUtils.equalsIgnoreCase(sortRule, "recommend")) {
            return SortTypeEnum.DEFAULT;
        } else if (StringUtils.equalsIgnoreCase(sortRule, "lowPrice") || StringUtils.equalsIgnoreCase(sortRule, "highPrice")) {
            return SortTypeEnum.MIN_PRICE;
        } else if (StringUtils.equalsIgnoreCase(sortRule, "highStar")) {
            return SortTypeEnum.STAR;
        } else if (StringUtils.equalsIgnoreCase(sortRule, "distance")) {
            return SortTypeEnum.DISTANCE;
        }
        return null;
    }

    /**
     * 构建单差标
     * @param travelStandardResponseList 差标列表
     * @return 单差标
     */
    private SignalTravelStandard buildSignalTravelStandard(List<TravelStandardResponse> travelStandardResponseList) {
        if (CollectionUtils.isEmpty(travelStandardResponseList)) {
            return null;
        }

        // 选择差标
        TravelStandardResponse travelStandardResponse = travelStandardResponseList.stream()
                .filter(item -> item.getTravelStandardToken() != null
                        && item.getTravelStandardToken().getOwnerType() != null
                        && item.getTravelStandardToken().getOwnerType() == 4
                        && Boolean.TRUE.equals(item.getRuleStatus()))
                .findFirst().orElse(travelStandardResponseList.get(0));
        log.info("选择的单差标 travelStandardResponse={}", JsonUtils.toJsonString(travelStandardResponse));
        if (travelStandardResponse == null || !Boolean.TRUE.equals(travelStandardResponse.getRuleStatus())) {
            return null;
        }

        // ruleList选择最高优先级的差标
        List<TravelStandardRuleVO> ruleList = getPriorityRuleList(travelStandardResponse.getRuleChain());
        log.info("最高优先级的ruleList ruleList={}", JsonUtils.toJsonString(ruleList));
        if (CollectionUtils.isEmpty(ruleList)) {
            SignalTravelStandard signalTravelStandard = new SignalTravelStandard();
            signalTravelStandard.setCanBook(Optional.of(travelStandardResponse.getRuleChain()).map(RuleChainVO::getBookable).orElse(null));
            return signalTravelStandard;
        }

        return SignalTravelStandard.builder()
                .canBook((Optional.of(travelStandardResponse.getRuleChain()).map(RuleChainVO::getBookable).orElse(null)))
                .avgPriceTravelStandard(buildAvgPriceTravelStandard(ruleList))
                .starList(buildStarTravelStandard(ruleList))
                .brandList(buildBrandIdList(ruleList))
                .overLimitModeList(getOverLimitModeList(ruleList))
                .overLimitReasonCodeList(
                        ruleList.stream().filter(item -> item != null && CollectionUtils.isNotEmpty(item.getExceedReasonList()))
                                .flatMap(item -> item.getExceedReasonList().stream()).filter(Objects::nonNull).distinct()
                                .map(item -> OverLimitReasonCode.builder()
                                        .id(item.getId())
                                        .name(item.getName()).build()).collect(Collectors.toList())).build();
    }

    private List<String> getOverLimitModeList(List<TravelStandardRuleVO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }

        Set<String> overLimitModeSet = new HashSet<>();
        for (TravelStandardRuleVO travelStandardRuleVO : ruleList) {
            if (travelStandardRuleVO == null || CollectionUtils.isEmpty(travelStandardRuleVO.getRejectTypes())) {
                continue;
            }

            for (String rejectType : travelStandardRuleVO.getRejectTypes()) {
                if (StringUtils.isBlank(rejectType)) {
                    continue;
                }
                overLimitModeSet.add(rejectType);
            }
        }

        // 如果有多个差标管控方式，存在F就去除F
        if (overLimitModeSet.size() >= 2) {
            overLimitModeSet.remove(HotelCoreConstant.F);
        }

        return new ArrayList<>(overLimitModeSet);
    }

    /**
     * 构建品牌差标
     * @param ruleList 差标规则
     * @return 品牌差标
     */
    private List<SignalTravelStandard.Brand> buildBrandIdList(List<TravelStandardRuleVO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }

        BrandRuleVO brandRuleVO = ruleList.stream().filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), "BrandRule"))
                .map(BrandRuleVO.class::cast).findFirst().orElse(null);
        if (brandRuleVO == null || CollectionUtils.isEmpty(brandRuleVO.getBrandList())) {
            return null;
        }

        return brandRuleVO.getBrandList().stream().filter(Objects::nonNull).map(item -> SignalTravelStandard.Brand.builder()
                .brandId(item.getBrandId())
                .brandName(item.getBrandName()).build()).collect(Collectors.toList());
    }

    /**
     * 构建星级差标
     * @param ruleList 差标规则
     * @return 星级差标
     */
    private List<Integer> buildStarTravelStandard(List<TravelStandardRuleVO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }

        StarRuleVO starRuleVO = ruleList.stream().filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), "StarRule"))
                .map(StarRuleVO.class::cast).findFirst().orElse(null);
        if (starRuleVO == null) {
            return null;
        }

        return buildStarList(starRuleVO.getStarList());
    }

    /**
     * 构建均价差标
     * @param ruleList 差标规则
     * @return 均价差标
     */
    private SignalTravelStandard.AvgPriceTravelStandard buildAvgPriceTravelStandard(List<TravelStandardRuleVO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }

        // 获取最高优先级的差标 同住-淡旺季-均价
        int cohabitRuleIndex = -1;
        int priceRuleIndex = -1;
        int offPeakSeasonRuleIndex = -1;
        for (int i = 0; i < ruleList.size(); i++) {
            TravelStandardRuleVO travelStandardRuleVO = ruleList.get(i);
            if (travelStandardRuleVO == null) {
                continue;
            }
            String name = travelStandardRuleVO.getName();
            if (StringUtils.equalsIgnoreCase(name, "CohabitRule")) {
                cohabitRuleIndex = i;
            } else if (StringUtils.equalsIgnoreCase(name, "PriceRule")) {
                priceRuleIndex = i;
            } else if (StringUtils.equalsIgnoreCase(name, "OffPeakSeasonRule")) {
                offPeakSeasonRuleIndex = i;
            }
        }

        // 同住
        if (cohabitRuleIndex >= 0) {
            CohabitRuleVO travelStandardRuleVO = (CohabitRuleVO) ruleList.get(cohabitRuleIndex);
            return SignalTravelStandard.AvgPriceTravelStandard.builder()
                    .foreignCurrency(travelStandardRuleVO.getCurrency())
                    .cnyMinPrice(Null.or(travelStandardRuleVO.getMinPrice(), BigDecimal.ZERO))
                    .foreignMinPrice(travelStandardRuleVO.getMinForeignPrice())
                    .cnyMaxPrice(travelStandardRuleVO.getMaxPrice())
                    .foreignMaxPrice(travelStandardRuleVO.getForeignPrice())
                    .areaTypeEnum(AreaTypeEnum.getEnum(travelStandardRuleVO.getCityType()))
                    .cityId(travelStandardRuleVO.getCityId())
                    .cityName(travelStandardRuleVO.getCityName()).build();
        }

        // 淡旺季
        if (offPeakSeasonRuleIndex >= 0) {
            OffPeakSeasonRuleVO travelStandardRuleVO = (OffPeakSeasonRuleVO) ruleList.get(offPeakSeasonRuleIndex);
            return SignalTravelStandard.AvgPriceTravelStandard.builder()
                    .foreignCurrency(travelStandardRuleVO.getCurrency())
                    .cnyMinPrice(Null.or(travelStandardRuleVO.getMinPrice(), BigDecimal.ZERO))
                    .foreignMinPrice(null)
                    .cnyMaxPrice(travelStandardRuleVO.getMaxPrice())
                    .foreignMaxPrice(travelStandardRuleVO.getForeignPrice())
                    .areaTypeEnum(AreaTypeEnum.getEnum(travelStandardRuleVO.getCityType()))
                    .cityId(travelStandardRuleVO.getCityId())
                    .cityName(travelStandardRuleVO.getCityName()).build();
        }

        // 均价
        if (priceRuleIndex >= 0) {
            PriceRuleVO travelStandardRuleVO = (PriceRuleVO) ruleList.get(priceRuleIndex);
            return SignalTravelStandard.AvgPriceTravelStandard.builder()
                    .foreignCurrency(travelStandardRuleVO.getCurrency())
                    .cnyMinPrice(Null.or(travelStandardRuleVO.getMinPrice(), BigDecimal.ZERO))
                    .foreignMinPrice(travelStandardRuleVO.getMinForeignPrice())
                    .cnyMaxPrice(travelStandardRuleVO.getMaxPrice())
                    .foreignMaxPrice(travelStandardRuleVO.getForeignPrice())
                    .areaTypeEnum(AreaTypeEnum.getEnum(travelStandardRuleVO.getCityType()))
                    .cityId(travelStandardRuleVO.getCityId() == null ? null : travelStandardRuleVO.getCityId().toString())
                    .cityName(travelStandardRuleVO.getCityName()).build();
        }

        return null;
    }

    /**
     * 获取最高优先级的差标
     * @param ruleChain 差标链
     * @return 差标规则
     */
    private List<TravelStandardRuleVO> getPriorityRuleList(RuleChainVO ruleChain) {
        if (ruleChain == null || CollectionUtils.isEmpty(ruleChain.getRuleList())) {
            return null;
        }

        // 按照城市类型分组
        Map<String, List<TravelStandardRuleVO>> travelStandardRuleVoListMap = ruleChain.getRuleList().stream()
                .filter(Objects::nonNull).collect(Collectors.groupingBy(this::getCityType));
        log.info("按照城市类型分组 ruleChain={} travelStandardRuleVoListMap={}", JsonUtils.toJsonString(ruleChain), JsonUtils.toJsonString(travelStandardRuleVoListMap));

        // 城市类型数量
        long cityTypeCount = travelStandardRuleVoListMap.keySet().size();

        // 只有一个城市类型
        if (cityTypeCount == 1) {
            return travelStandardRuleVoListMap.values().stream().findFirst().orElse(null);
        }

        // 多个城市类型
        if (cityTypeCount > 1) {
            return travelStandardRuleVoListMap.get(String.valueOf(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType()));
        }

        return null;
    }

    /**
     * 获取城市类型
     * @param travelStandardRuleVO 差标规则
     * @return 城市类型
     */
    private String getCityType(TravelStandardRuleVO travelStandardRuleVO) {
        if (travelStandardRuleVO == null) {
            return "";
        }
        String name = travelStandardRuleVO.getName();
        if (StringUtils.equalsIgnoreCase(name, "CohabitRule")) {
            CohabitRuleVO tmp = (CohabitRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "PriceRule")) {
            PriceRuleVO tmp = (PriceRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "OffPeakSeasonRule")) {
            OffPeakSeasonRuleVO tmp = (OffPeakSeasonRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "StarRule")) {
            StarRuleVO tmp = (StarRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "FloatPriceRule")) {
            FloatPriceRuleVO tmp = (FloatPriceRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "BrandRule")) {
            BrandRuleVO tmp = (BrandRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        }
        return "";
    }

    /**
     * 获取支付方式
     * @param travelMode 差旅模式
     * @param payInfoList 支付方式
     * @return 支付方式
     */
    private List<String> getPaymentMethod(String travelMode, List<PayInfoDTO> payInfoList) {
        if (CollectionUtils.isEmpty(payInfoList)) {
            return null;
        }
        List<String> resultList = new ArrayList<>();
        for (PayInfoDTO payInfoDTO : payInfoList) {
            if (payInfoDTO == null) {
                continue;
            }
            if (StringUtils.equalsIgnoreCase(payInfoDTO.getName(), travelMode)) {
                resultList.add(payInfoDTO.getCode());
            }
        }
        return resultList;
    }

    /**
     * 获取酒店房间排序规则
     * @param allSwitchDto 开关
     * @return 酒店房间排序规则
     */
    private Integer getHotelRoomSortRule(AllSwitchDTO allSwitchDto) {
        List<Integer> hotelRoomSortRuleList = getSwitchValue(allSwitchDto, HotelCoreConstant.HOTEL_ROOM_SORT_RULE);
        if (CollectionUtils.isEmpty(hotelRoomSortRuleList)) {
            return null;
        }
        return hotelRoomSortRuleList.get(0);
    }

    /**
     * 获取酒店列表排序规则
     * @param allSwitchDto 开关
     * @return 酒店列表排序规则
     */
    private Integer getHotelListSortRule(AllSwitchDTO allSwitchDto) {
        List<Integer> hotelListSortRuleList = getSwitchValue(allSwitchDto, HotelCoreConstant.HOTEL_LIST_SORT_RULE);
        if (CollectionUtils.isEmpty(hotelListSortRuleList)) {
            return null;
        }
        return hotelListSortRuleList.get(0);
    }

    /**
     * 获取屏蔽星级
     * @param allSwitchDto 开关
     * @return 屏蔽星级
     */
    private List<Integer> getShieldStarList(AllSwitchDTO allSwitchDto) {
        List<Integer> shieldStarList = getSwitchValue(allSwitchDto, HotelCoreConstant.HOTEL_STAR_SHIELD_CONTROL);
        if (shieldStarList == null) {
            return null;
        }
        if (shieldStarList.stream().anyMatch(item -> item == 2)) {
            shieldStarList.add(1);
        }
        return shieldStarList;
    }

    /**
     * 获取开关值
     * @param allSwitchDto 开关
     * @param switchName 开关名称
     * @return 开关值
     */
    private List<Integer> getSwitchValue(AllSwitchDTO allSwitchDto, String switchName) {
        if (allSwitchDto == null || CollectionUtils.isEmpty(allSwitchDto.getSwitchInfoSoaMap())) {
            return null;
        }

        SwitchDTO switchDto = allSwitchDto.getSwitchInfoSoaMap().get(switchName);
        if (switchDto == null || StringUtils.isBlank(switchDto.getValue())) {
            return null;
        }
        return JsonUtils.parseArray(switchDto.getValue(), Integer.class);
    }
    
    private <T> List<T> getSwitchValue(AllSwitchDTO allSwitchDto, String switchName, Class<T> tClass) {
        if (allSwitchDto == null || CollectionUtils.isEmpty(allSwitchDto.getSwitchInfoSoaMap())) {
            return null;
        }
        
        SwitchDTO switchDto = allSwitchDto.getSwitchInfoSoaMap().get(switchName);
        if (switchDto == null || StringUtils.isBlank(switchDto.getValue())) {
            return null;
        }
        return JsonUtils.parseArray(switchDto.getValue(), tClass);
    }

    /**
     * 获取产线
     * @param productType 产线类型
     * @return 产线
     */
    private BizTypeEnum getBizTypeEnum(String productType) {
        if (StringUtils.isBlank(productType)) {
            return null;
        }
        return BizTypeEnum.getByCodeOrName(productType);
    }

    /**
     * 获取供应商产品
     * @param bizTypeEnum 产线
     * @param supplierConfigDtoList 供应商配置
     * @param operatorTypeList 操作类型
     * @return 供应商产品
     */
    private Map<String, List<SupplierProduct>> getSupplierProductListMap(BizTypeEnum bizTypeEnum,
                                                                         TravelModeEnum travelModeEnum,
                                                                         List<SupplierConfigDTO> supplierConfigDtoList,
                                                                         List<String> operatorTypeList) {
        // 获取供应商url
        List<ListSupplierUrlRespVo.SupplierUrl> supplierUrlList = getSupplierUrlList(supplierConfigDtoList, operatorTypeList, bizTypeEnum);
        log.info("获取供应商url supplierUrlList={}", JsonUtils.toJsonString(supplierUrlList));
        if (CollectionUtils.isEmpty(supplierUrlList)) {
            return null;
        }

        // list转map supplierCode+operatorType
        Map<String, ListSupplierUrlRespVo.SupplierUrl> supplierUrlMap = new HashMap<>();
        for (ListSupplierUrlRespVo.SupplierUrl supplierUrl : supplierUrlList) {
            if (supplierUrl == null || StringUtils.isBlank(supplierUrl.getSupplierCode()) || StringUtils.isBlank(supplierUrl.getOperatorType())) {
                continue;
            }
            supplierUrlMap.put(supplierUrl.getSupplierCode() + supplierUrl.getOperatorType(), supplierUrl);
        }

        // 组装供应商产品
        return getSupplierProductListMap(bizTypeEnum, travelModeEnum, supplierConfigDtoList, operatorTypeList, supplierUrlMap);
    }

    /**
     * 组装供应商产品
     * @param supplierConfigDtoList 供应商配置
     * @param operatorTypeList 操作类型
     * @param supplierUrlMap 供应商url
     * @return 供应商产品
     */
    private Map<String, List<SupplierProduct>> getSupplierProductListMap(BizTypeEnum bizTypeEnum,
                                                                         TravelModeEnum travelModeEnum,
                                                                         List<SupplierConfigDTO> supplierConfigDtoList,
                                                                         List<String> operatorTypeList,
                                                                         Map<String, ListSupplierUrlRespVo.SupplierUrl> supplierUrlMap) {
        if (CollectionUtils.isEmpty(supplierConfigDtoList) || CollectionUtils.isEmpty(operatorTypeList) || supplierUrlMap == null) {
            return null;
        }
        Map<String, List<SupplierProduct>> supplierProductListMap = new HashMap<>();

        // 供应商配置
        for (SupplierConfigDTO supplierConfigDto : supplierConfigDtoList) {
            if (supplierConfigDto == null || StringUtils.isBlank(supplierConfigDto.getSupplierCode())) {
                continue;
            }

            // 供应商操作类型
            for (String operatorType : operatorTypeList) {
                if (StringUtils.isBlank(operatorType)) {
                    continue;
                }
                ListSupplierUrlRespVo.SupplierUrl supplierUrl = supplierUrlMap.get(supplierConfigDto.getSupplierCode() + operatorType);
                if (supplierUrl == null) {
                    log.info("获取供应商URL失败 supplierCode={} operatorType={}", supplierConfigDto.getSupplierCode(), operatorType);
                    continue;
                }

                // 组装供应商产品
                List<SupplierProduct> tmpList = supplierProductListMap.getOrDefault(operatorType, new ArrayList<>());
                SupplierProduct supplierProduct = SupplierProduct.builder()
                        .supplierCode(supplierUrl.getSupplierCode())
                        .directSupplier(supplierConfigDto.getDirectSupplier())
                        .supplierName(supplierConfigDto.getSupplierName())
                        .uid(supplierConfigDto.getSupplierUid())
                        .corpId(supplierConfigDto.getSupplierCorpId())
                        .userKey(supplierUrl.getUserKey())
                        .sortNum(supplierConfigDto.getSortNum())
                        .url(supplierUrl.getProductUrl())
                        .aid(getAid(supplierUrl.getProductUrl()))
                        .sid(getSid(supplierUrl.getProductUrl()))
                        .parameterlessUrl(getParameterlessUrl(supplierUrl.getProductUrl()))
                        .serviceChargeList(buildServiceChargeList(bizTypeEnum, travelModeEnum, supplierConfigDto.getServiceFeeList()))
                        .build();
                tmpList.add(supplierProduct);
                supplierProductListMap.put(operatorType, tmpList);
            }
        }
        return supplierProductListMap;
    }

    private List<ServiceCharge> buildServiceChargeList(BizTypeEnum bizTypeEnum,
                                             TravelModeEnum travelModeEnum,
                                             List<ServiceFeeDTO> serviceFeeList) {
        if (CollectionUtils.isEmpty(serviceFeeList) || bizTypeEnum == null || Objects.equals(travelModeEnum, TravelModeEnum.PUB)) {
            return null;
        }

        // 过滤 产线 - 因公因私 - 统一支付 - 前收
        ServiceFeeDTO serviceFeeDTO = serviceFeeList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getBizType(), bizTypeEnum.name())
                && StringUtils.equalsIgnoreCase(item.getFeeType(), travelModeEnum.getCode())
                && item.getCollectionType() != null && item.getCollectionType() == 2
                && StringUtils.equalsIgnoreCase(item.getPayType(), HotelCoreConstant.ACCNT)
                && ServiceChargeStrategyEnum.getEnum(Optional.ofNullable(item.getScope()).map(Object::toString).orElse(null)) != null).findFirst().orElse(null);
        if (serviceFeeDTO == null) {
            return null;
        }

        return Collections.singletonList(ServiceCharge.builder()
                .serviceChargeTypeEnum(ServiceChargeTypeEnum.PLATFORM)
                .serviceChargeStrategyEnum(ServiceChargeStrategyEnum.getEnum(Optional.ofNullable(serviceFeeDTO.getScope()).map(Object::toString).orElse(null)))
                .price(Price.builder()
                        .customCurrency(HotelCoreConstant.CNY)
                        .customPrice(serviceFeeDTO.getFee()).build()).build());
    }

    /**
     * 获取aid
     * @param productUrl 产品url
     * @return aid
     */
    private String getAid(String productUrl) {
        if (StringUtils.isBlank(productUrl)) {
            return null;
        }
        for (String s : productUrl.split("&")) {
            if (s != null && s.contains("aid=")) {
                return s.split("=")[1];
            }
        }
        return null;
    }

    /**
     * 获取sid
     * @param productUrl 产品url
     * @return sid
     */
    private String getSid(String productUrl) {
        if (StringUtils.isBlank(productUrl)) {
            return null;
        }
        for (String s : productUrl.split("&")) {
            if (s != null && s.contains("sid=")) {
                return s.split("=")[1];
            }
        }
        return null;
    }

    /**
     * 获取无参数url
     * @param productUrl 产品url
     * @return 无参数url
     */
    private String getParameterlessUrl(String productUrl) {
        if (StringUtils.isBlank(productUrl)) {
            return null;
        }
        String[] split = productUrl.split("\\?");
        if (split.length > 0) {
            return split[0];
        }
        return productUrl;
    }

    /**
     * 获取供应商url
     * @param supplierConfigDtoList 供应商配置
     * @param operatorTypeList 操作类型
     * @param bizTypeEnum 产线
     * @return 供应商url
     */
    private List<ListSupplierUrlRespVo.SupplierUrl> getSupplierUrlList(List<SupplierConfigDTO> supplierConfigDtoList, List<String> operatorTypeList, BizTypeEnum bizTypeEnum) {
        if (CollectionUtils.isEmpty(supplierConfigDtoList) || CollectionUtils.isEmpty(operatorTypeList) || bizTypeEnum == null) {
            return null;
        }
        List<ListSupplierUrlReqVo.SupplierKey> supplierKeyList = new ArrayList<>();

        // 供应商配置
        for (SupplierConfigDTO supplierConfigDTO : supplierConfigDtoList) {
            if (supplierConfigDTO == null) {
                continue;
            }

            // 操作类型
            for (String operatorType : operatorTypeList) {
                ListSupplierUrlReqVo.SupplierKey supplierKey = new ListSupplierUrlReqVo.SupplierKey();
                supplierKey.setSupplierCode(supplierConfigDTO.getSupplierCode());
                supplierKey.setOperatorType(operatorType);
                supplierKeyList.add(supplierKey);
            }
        }

        // 获取供应商url
        return hotelCoreOpenFeignDao.getSupplierUrlList(bizTypeEnum.getTransport(), supplierKeyList);
    }

    /**
     * 过滤供应商配置
     * @param supplierConfigDtoList 供应商配置
     * @param travelMode 差旅模式
     * @return 过滤后的供应商配置
     */
    private List<SupplierConfigDTO> filterSupplierConfigDtoList(List<SupplierConfigDTO> supplierConfigDtoList, String travelMode) {
        if (CollectionUtils.isEmpty(supplierConfigDtoList)) {
            return new ArrayList<>(0);
        }

        List<SupplierConfigDTO> resultList = new ArrayList<>();

        // 过滤供应商配置
        for (SupplierConfigDTO supplierConfigDTO : supplierConfigDtoList) {
            if (supplierConfigDTO == null || !Boolean.TRUE.equals(supplierConfigDTO.getInUse())
                    || StringUtils.isBlank(supplierConfigDTO.getSupplierCode()) || supplierConfigDTO.getSortNum() == null) {
                continue;
            }

            // 供应商因公因私配置
            Integer busPriType = supplierConfigDTO.getBusPriType();
            if (busPriType == null) {
                continue;
            }

            // 供应商因公因私配置判断
            if (busPriType == 3 || (busPriType == 1 && StringUtils.equalsIgnoreCase(TravelModeEnum.PUB.getCode(), travelMode))
                    || (busPriType == 2 && StringUtils.equalsIgnoreCase(TravelModeEnum.OWN.getCode(), travelMode))) {
                resultList.add(supplierConfigDTO);
            }
        }

        return resultList;
    }

}
