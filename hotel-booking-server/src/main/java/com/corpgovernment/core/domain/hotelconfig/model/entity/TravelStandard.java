package com.corpgovernment.core.domain.hotelconfig.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TravelStandard {

    // 常规差标
    private SignalTravelStandard signalTravelStandard;

    // 阶梯差标
    private MultiTravelStandard multiTravelStandard;

    // 出差申请单
    private TravelApplication travelApplication;

    // 紧急预订
    private Boolean urgencyReserve;

}
