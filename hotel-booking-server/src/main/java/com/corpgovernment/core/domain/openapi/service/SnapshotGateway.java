package com.corpgovernment.core.domain.openapi.service;

import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.dto.snapshot.dto.hotel.request.SaveHotelProductSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.response.SaveHotelProductSnapshotResponse;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/***
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/2/18
 **/
@Component
@Slf4j
public class SnapshotGateway {
    @Autowired
    private CoreServiceClient coreServiceClient;

    /**
     * 保存酒店产品快照
     *
     * @param request 保存酒店产品快照请求对象
     * @return 保存酒店产品快照响应对象
     */
    public SaveHotelProductSnapshotResponse saveHotelProductSnapshot(SaveHotelProductSnapshotRequest request){
        log.info("保存酒店产品快照 远程调用开始 request:{}", JsonUtils.toJsonString(request));
        JSONResult<SaveHotelProductSnapshotResponse>  result = null;
        try {
            result = coreServiceClient.saveHotelProductSnapshot(request);
            log.info("保存酒店产品快照 远程调用结束 result:{}", JsonUtils.toJsonString(result));
        } catch (Exception e) {
            log.error("保存酒店产品快照 远程调用失败 result:{}", result);
        }
        if (result == null || !result.isSUCCESS()) {
            log.error("保存酒店产品快照 远程调用返回异常 result:{}", result);
            return null;
        }
        return result.getData();
    }
}
