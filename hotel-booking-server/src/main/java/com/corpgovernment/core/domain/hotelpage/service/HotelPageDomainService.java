package com.corpgovernment.core.domain.hotelpage.service;

import cn.hutool.core.collection.CollUtil;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.domain.common.gateway.ICommonGateway;
import com.corpgovernment.core.domain.common.gateway.IHotelIndicatorGateway;
import com.corpgovernment.core.domain.common.model.entity.HotelListMetaData;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.QueryHotelListRequest;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.service.IHotelIndicatorDomainService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.MemberTagInfo;
import com.corpgovernment.core.domain.hotelpage.gateway.IHotelPageGateway;
import com.corpgovernment.core.domain.hotelpage.gateway.IQueryHotelListGateway;
import com.corpgovernment.core.domain.hotelpage.model.entity.*;
import com.corpgovernment.core.domain.hotelpage.model.enums.*;
import com.corpgovernment.core.domain.hotelpage.service.control.factory.RequestControlFactory;
import com.corpgovernment.core.domain.hotelpage.service.keyword.IKeyWordService;
import com.corpgovernment.core.domain.hotelpage.service.keyword.factory.KeyWordServiceFactory;
import com.corpgovernment.core.domain.hotelpage.service.position.factory.PositionServiceFactory;
import com.corpgovernment.core.domain.relation.gateway.IHotelRelationGatewayV2;
import com.corpgovernment.core.domain.relation.model.GeoLocation;
import com.corpgovernment.core.domain.relation.model.HotelInfo;
import com.corpgovernment.core.domain.relation.model.HotelKey;
import com.corpgovernment.core.domain.relation.model.HotelRelation;
import com.corpgovernment.core.domain.relation.service.HotelRelationDomainServiceV2;
import com.corpgovernment.core.domain.relation.service.IHotelRelationDomainService;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelListSnapshot;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.Md5Utils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-15 18:53
 */
@Service
@Slf4j
public class HotelPageDomainService implements IHotelPageDomainService {

    @Resource
    private PositionServiceFactory positionServiceFactory;

    @Resource
    private KeyWordServiceFactory keyWordServiceFactory;

    @Resource
    private IHotelPageGateway hotelPageGateway;

    @Resource
    private IQueryHotelListGateway queryHotelListGateway;

    @Resource(name = "queryHotelListThreadPool")
    private ThreadPoolExecutor queryHotelListThreadPool;
    
    @Resource(name = "queryHotelListExternalThreadPool")
    private ThreadPoolExecutor queryHotelListExternalThreadPool;

    @Resource(name = "hotelPageThreadPool")
    private ThreadPoolExecutor hotelPageThreadPool;

    @Resource(name = "queryHotelMinPriceThreadPool")
    private ThreadPoolExecutor queryHotelMinPriceThreadPool;

    @Resource
    private IHotelIndicatorGateway hotelIndicatorGateway;

    @Resource
    private ICommonGateway commonGateway;

    @Resource
    private RequestControlFactory requestControlFactory;
    
    @Resource
    private IHotelRelationDomainService hotelRelationDomainService;

    @Resource
    private HotelPageDomainService self;

    @Resource
    private IHotelIndicatorDomainService hotelIndicatorDomainService;
    
    @Resource
    private HotelRelationDomainServiceV2 hotelRelationDomainServiceV2;
    
    @Resource
    private IHotelRelationGatewayV2 hotelRelationGatewayV2;

    @Override
    public void handlePosition(HotelPageRequest hotelPageRequest, PositionRequest positionRequest) {
        positionServiceFactory.handlePosition(hotelPageRequest, positionRequest);
    }

    @Override
    @BusinessBehaviorMonitor
    public void handleKeyWord(HotelPageRequest hotelPageRequest, KeyWordRequest keyWordRequest) {
        KeyWordTypeEnum keyWordTypeEnum = null;
        try {
            if (hotelPageRequest == null || keyWordRequest == null) {
                return;
            }

            if (StringUtils.isBlank(keyWordRequest.getKeyword()) && StringUtils.isNotBlank(keyWordRequest.getKeywordName())) {
                keyWordRequest.setKeyword(keyWordRequest.getKeywordName());
            }

            keyWordTypeEnum = KeyWordTypeEnum.getKeyWordTypeEnum(keyWordRequest.getKeywordType());
            log.info("keyWordTypeEnum={}", keyWordTypeEnum);

            // 关键词处理
            IKeyWordService keyWordService = keyWordServiceFactory.getKeyWordService(keyWordTypeEnum);
            if (keyWordService != null) {
                keyWordService.handleKeyWord(hotelPageRequest, keyWordRequest);
                log.info("处理关键词成功 keyWordRequest={}", keyWordRequest);
                return;
            }

            HotelAdvancedFilter hotelAdvancedFilter = hotelPageRequest.getHotelAdvancedFilter();
            if (hotelAdvancedFilter == null) {
                hotelAdvancedFilter = new HotelAdvancedFilter();
            }
            hotelAdvancedFilter.setKeyword(keyWordRequest.getKeyword());
            hotelPageRequest.setHotelAdvancedFilter(hotelAdvancedFilter);
        } finally {
            hotelIndicatorGateway.handleKeyWord(keyWordTypeEnum, hotelPageRequest);
        }
    }

    @Override
    @BusinessBehaviorMonitor
    public Map<String, HotelPage> getHotelPage(HotelPageRequest hotelPageRequest, Integer pageIndex, Boolean asyncLoadNextPage) {
        Map<String, HotelPage> hotelPageMap = null;
        Boolean hitCache = null;
        // 获取酒店页大小配置
        Integer hotelPageSize = null;
        try {
            if (hotelPageRequest == null || pageIndex == null || CollectionUtils.isEmpty(hotelPageRequest.getSupplierProductList())) {
                return null;
            }

            // 条件固化
            HotelPageRequest tmpHotelPageRequest = JsonUtils.parse(JsonUtils.toJsonString(hotelPageRequest), HotelPageRequest.class);
            preProcess(tmpHotelPageRequest);

            // 获取酒店页大小配置
            hotelPageSize = hotelPageGateway.getHotelPageSize();

            // 获取酒店页数据
            HotelPageMapCache hotelPageMapCache = hotelPageGateway.getHotelPageMapCache(buildHotelPageMapKey(tmpHotelPageRequest, pageIndex, hotelPageSize));
            if (hotelPageMapCache == null) {
                hitCache = false;
                hotelPageMap = createHotelPageCache(tmpHotelPageRequest, pageIndex, "同步", hotelPageSize);
            } else {
                hitCache = true;
                hotelPageMap = hotelPageMapCache.getHotelPageMap();
                log.info("酒店页缓存跟踪 读取缓存数据 version={} hotelPageMap={}", hotelPageMapCache.getVersion(), hotelPageMap);
            }

            // 异步加载
            Boolean finalHitCache = hitCache;
            Integer finalHotelPageSize = hotelPageSize;
            CompletableFuture.runAsync(() -> {
                if (Boolean.TRUE.equals(finalHitCache)) {
                    createHotelPageCache(tmpHotelPageRequest, pageIndex, "异步", finalHotelPageSize);
                }
                if (Boolean.TRUE.equals(asyncLoadNextPage)) {
                    createHotelPageCache(tmpHotelPageRequest, pageIndex + 1, "异步", finalHotelPageSize);
                }
            }, hotelPageThreadPool);

            return hotelPageMap;
        } finally {
            hotelIndicatorGateway.getHotelPage(hitCache, hotelPageRequest, pageIndex, hotelPageMap, hotelPageSize);
        }
    }

    @Override
    public List<HotelCard> aggregationHotelCardListMap(Map<String, List<HotelCard>> hotelCardListMap,
                                                       HotelSort hotelSort,
                                                       Boolean protocolPriority,
                                                       List<String> prioritySupplierList,
                                                       String prePageLastHotelSupplierCode) {
        if (CollectionUtils.isEmpty(hotelCardListMap)) {
            return new ArrayList<>(0);
        }

        // 默认排序赋值
        List<HotelCard> hotelCardPool = new ArrayList<>();
        for (List<HotelCard> hotelCardList : hotelCardListMap.values()) {
            if (CollectionUtils.isEmpty(hotelCardList)) {
                continue;
            }
            int i = 0;
            for (HotelCard hotelCard : hotelCardList) {
                if (hotelCard == null) {
                    continue;
                }
                hotelCard.setDefaultSortNum(i);
                i++;
                hotelCardPool.add(hotelCard);
            }
        }

        // 默认排序下要从上一页开始
        if (CollectionUtils.isNotEmpty(prioritySupplierList) && StringUtils.isNotBlank(prePageLastHotelSupplierCode)
                && (hotelSort == null || SortTypeEnum.DEFAULT.name().equals(hotelSort.getSortType()))) {
            int index = prioritySupplierList.indexOf(prePageLastHotelSupplierCode);
            if (index != -1) {
                List<String> newList = new ArrayList<>(prioritySupplierList.subList(index + 1, prioritySupplierList.size()));
                newList.addAll(prioritySupplierList.subList(0, index + 1));
                prioritySupplierList = newList;
            }
        }

        // 排序
        return sortHotelCardList(hotelCardPool, hotelSort, prioritySupplierList, protocolPriority);
    }

    @Override
    @BusinessBehaviorMonitor
    public List<TravelControl> controlRequest(HotelPageRequest hotelPageRequest, List<TravelControlRequest> travelControlRequestList) {
        try {
            List<TravelControl> travelControlList = new ArrayList<>();
            // 无需控制
            if (hotelPageRequest == null || CollectionUtils.isEmpty(travelControlRequestList)) {
                travelControlList.add(TravelControl.builder()
                        .hotelPageRequest(hotelPageRequest).build());
                return travelControlList;
            }

            // 控制
            for (TravelControlRequest travelControlRequest : travelControlRequestList) {
                if (travelControlRequest == null) {
                    continue;
                }

                HotelPageRequest tmpHotelPageRequest = JsonUtils.parse(JsonUtils.toJsonString(hotelPageRequest), HotelPageRequest.class);

                List<String> controlledList = requestControlFactory.controlRequest(tmpHotelPageRequest, travelControlRequest);
                travelControlList.add(TravelControl.builder()
                        .hotelPageRequest(tmpHotelPageRequest)
                        .controlledList(controlledList)
                        .priority(travelControlRequest.getPriority()).build());
            }

            return travelControlList;
        } finally {
            hotelIndicatorGateway.controlRequest();
        }
    }

    @Override
    public HotelListMetaData getHotelListMetaData(String key, Integer pageIndex) {
        return hotelPageGateway.getHotelListMetaData(key, pageIndex);
    }

    @Override
    public Boolean setHotelListMetaData(String key, HotelListMetaData hotelListMetaData) {
        return hotelPageGateway.setHotelListMetaData(key, hotelListMetaData);
    }

    @Override
    public void handleViewedHotel(HotelPage hotelPage) {
        if (hotelPage == null || CollectionUtils.isEmpty(hotelPage.getHotelCardList())) {
            return;
        }

        Set<String> viewedHotelSet = commonGateway.getViewedHotel();
        if (CollectionUtils.isEmpty(viewedHotelSet)) {
            return;
        }

        for (HotelCard hotelCard : hotelPage.getHotelCardList()) {
            if (hotelCard == null) {
                continue;
            }

            if (hotelCard.getSupplierList().stream()
                    .filter(item -> item != null && item.getHotelId() != null && item.getSupplierCode() != null)
                    .map(item -> item.getSupplierCode() + item.getHotelId()).anyMatch(viewedHotelSet::contains)) {
                hotelCard.setViewed(true);
            }
        }
    }

    /**
     * 获取酒店页数据
     * @param hotelPageRequest 酒店页请求
     * @param pageIndex 页号
     * @param mode 模式
     * @return 酒店页数据
     */
    private Map<String, HotelPage> createHotelPageCache(HotelPageRequest hotelPageRequest, Integer pageIndex, String mode, Integer pageSize) {
        // 缓存版本号
        String version = UUID.randomUUID().toString();

        // 获取酒店页元数据
        Map<String, HotelPageMetaData> hotelPageMetaDataMap = getHotelPageMetaDataMap(hotelPageRequest);
        log.info("酒店页元数据 hotelPageMetaDataMap={}", JsonUtils.toJsonString(hotelPageMetaDataMap));

        // 构建酒店页
        Map<String, HotelPage> hotelPageMap = self.createHotelPage(hotelPageRequest, hotelPageMetaDataMap, pageIndex, pageSize, version, mode);
        if (CollectionUtils.isEmpty(hotelPageMap)) {
            return null;
        }

        // 缓存页数据
        HotelPageMapCache hotelPageMapCache = new HotelPageMapCache();
        hotelPageMapCache.setHotelPageMap(hotelPageMap);
        hotelPageMapCache.setVersion(version);
        String hotelPageMapKey = buildHotelPageMapKey(hotelPageRequest, pageIndex, pageSize);
        Boolean success = hotelPageGateway.setHotelPageMap(hotelPageMapKey, hotelPageMapCache);
        log.info("酒店页缓存跟踪 写入缓存数据 success={} version={} hotelPageMapKey={} hotelPageMapCache={}", success, version, hotelPageMapKey, JsonUtils.toJsonString(hotelPageMapCache));

        // 最后一页标记
        hotelPageMap.forEach((supplierCode, hotelPage) -> {
            HotelPageMetaData hotelPageMetaData = hotelPageMetaDataMap.get(supplierCode);
            if (hotelPageMetaData != null && hotelPageMetaData.getLastPageIndex() == null && Boolean.TRUE.equals(hotelPage.getLastPage())) {
                hotelPageMetaData.setLastPageIndex(pageIndex);
            }
        });

        // 缓存元数据
        String hotelPageMetaDataMapKey = buildHotelPageMetaDataMapKey(hotelPageRequest);
        success = hotelPageGateway.setHotelPageMetaDataMap(hotelPageMetaDataMapKey, hotelPageMetaDataMap);
        log.info("酒店页元数据缓存跟踪 写入缓存数据 success={} hotelPageMetaDataMapKey={} hotelPageMetaDataMap={}", success, hotelPageMetaDataMapKey, JsonUtils.toJsonString(hotelPageMetaDataMap));
        return hotelPageMap;
    }

    /**
     * 构建酒店页
     */
    @BusinessBehaviorMonitor
    public Map<String, HotelPage> createHotelPage(HotelPageRequest hotelPageRequest,
                                                  Map<String, HotelPageMetaData> hotelPageMetaDataMap,
                                                  Integer pageIndex,
                                                  Integer pageSize,
                                                  String version,
                                                  String mode) {
        Map<String, HotelPage> hotelPageMap = null;
        try {
            if (hotelPageRequest == null || CollectionUtils.isEmpty(hotelPageMetaDataMap) || pageIndex == null || pageSize == null) {
                log.info("【构建酒店页】输入参数异常");
                return null;
            }

            // 临时酒店页数据容器
            Map<String, HotelPage> tmpHotelPageMap = new HashMap<>();
            // 优先供应商列表
            List<String> prioritySupplierCodeList = commonGateway.getPrioritySupplierCodeList(hotelPageRequest.getSupplierProductList());

            // 多线程调供应商请求
            List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
            Map<String, Long> supplierTimeoutMap = Null.or(commonGateway.getSupplierTimeoutMap(), new HashMap<>(0));
            hotelPageMetaDataMap.forEach((supplierCode, hotelPageMetaData) -> completableFutureList.add(CompletableFuture.runAsync(() -> {
                try {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        QueryHotelListRequest queryHotelListRequest = QueryHotelListRequest.builder()
                                .supplierProduct(hotelPageMetaData.getSupplierProduct())
                                .hotelBaseFilter(hotelPageMetaData.getHotelBaseFilter())
                                .hotelPositionFilter(hotelPageMetaData.getHotelPositionFilter())
                                .hotelAdvancedFilter(buildHotelAdvancedFilter(hotelPageMetaData.getHotelAdvancedFilter(), supplierCode))
                                .hotelSort(hotelPageMetaData.getHotelSort())
                                .hotelDistanceDesc(hotelPageMetaData.getHotelDistanceDesc())
                                .pageSize(pageSize)
                                .pageIndex(pageIndex)
                                .lastPageIndex(hotelPageMetaData.getLastPageIndex())
                                .abroad(hotelPageMetaData.getAbroad())
                                .travelMode(hotelPageRequest.getTravelMode())
                                .build();
                        HotelPage hotelPage = queryHotelListGateway.execute(queryHotelListRequest, mode, version, "酒店列表查询");
                        if (hotelPage != null) {
                            tmpHotelPageMap.put(supplierCode, hotelPage);
                        }
                    }, queryHotelListThreadPool);
                    future.get(supplierTimeoutMap.getOrDefault(supplierCode, HotelCoreConstant.MAX_TIMEOUT_MS), TimeUnit.MILLISECONDS);
                } catch (Exception exception) {
                    log.error("【构建酒店页】供应商异常", exception);
                    log.info("【构建酒店页】供应商酒店列表查询超时 supplierCode={}", supplierCode);
                }
            }, queryHotelListExternalThreadPool)));

            // 等待所有请求完成
            try {
                CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(HotelCoreConstant.MAX_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("【构建酒店页】供应商异常", e);
            }

            // 固化酒店页数据
            hotelPageMap = JsonUtils.parse(JsonUtils.toJsonString(tmpHotelPageMap), new TypeReference<Map<String, HotelPage>>() {});
            log.info("【构建酒店页】从供应商获取的酒店页数据 hotelPageMap={}", JsonUtils.toJsonString(hotelPageMap));

            // 返回null表示异常
            if (CollectionUtils.isEmpty(hotelPageMap)) {
                log.error("【构建酒店页】供应商均未返回数据");
                return null;
            }

            // 酒店聚合
            aggregateHotelPage(hotelPageMap, hotelPageMetaDataMap, prioritySupplierCodeList, mode, version);
            log.info("【构建酒店页】聚合后 hotelPageMap={}", JsonUtils.toJsonString(hotelPageMap));

            // 酒店排序
            hotelPageMap.forEach((supplierCode, hotelPage) -> hotelPage.setHotelCardList(
                    sortHotelCardList(
                            hotelPage.getHotelCardList(),
                            hotelPageRequest.getHotelSort(),
                            prioritySupplierCodeList,
                            hotelPageRequest.getProtocolPrioritySort())));
            log.info("【构建酒店页】排序后 hotelPageMap={}", JsonUtils.toJsonString(hotelPageMap));
            return hotelPageMap;
        } finally {
            hotelIndicatorDomainService.createHotelPage(hotelPageRequest, pageIndex, pageSize, hotelPageMap, buildHotelPageMapKey(hotelPageRequest, pageIndex, pageSize));
        }
    }

    /**
     * 构建高级过滤
     *
     * <AUTHOR> 2024/12/24
     */
    private HotelAdvancedFilter buildHotelAdvancedFilter(HotelAdvancedFilter hotelAdvancedFilter, String supplierCode) {
        if (hotelAdvancedFilter == null) {
            return null;
        }
        HotelAdvancedFilter result = new HotelAdvancedFilter();
        BeanUtils.copyProperties(hotelAdvancedFilter, result);
        // 判断当前供应商是否存在hotelId
        if (CollUtil.isNotEmpty(hotelAdvancedFilter.getSupplierCodeHotelIdMap())
            && hotelAdvancedFilter.getSupplierCodeHotelIdMap().containsKey(supplierCode)) {
            result
                .setHotelIdList(Lists.newArrayList(hotelAdvancedFilter.getSupplierCodeHotelIdMap().get(supplierCode)));
            result.setKeyword(null);
        }
        return result;

    }
    /**
     * 排序酒店卡片列表
     * @param hotelCardList 酒店卡片列表
     * @param hotelSort 酒店排序
     * @param prioritySupplierList 优先供应商列表
     * @param protocolPriority 协议优先
     * @return 排序后的酒店卡片列表
     */
    private List<HotelCard> sortHotelCardList(List<HotelCard> hotelCardList, HotelSort hotelSort, List<String> prioritySupplierList, Boolean protocolPriority) {
        if (CollectionUtils.isEmpty(hotelCardList)) {
            return null;
        }

        List<HotelCard> resultList = new ArrayList<>();

        // 协议优先
        if (Boolean.TRUE.equals(protocolPriority)) {
            // 分成协议和非协议
            List<HotelCard> protocolHotelCardList = new ArrayList<>();
            List<HotelCard> nonProtocolHotelCardList = new ArrayList<>();
            for (HotelCard hotelCard : hotelCardList) {
                if (hotelCard == null) {
                    continue;
                }
                Integer protocolType = hotelCard.getProtocolType();
                if (protocolType != null && (protocolType == 2 || protocolType == 3)) {
                    protocolHotelCardList.add(hotelCard);
                } else {
                    nonProtocolHotelCardList.add(hotelCard);
                }
            }

            // 排序
            resultList.addAll(sortHotelCardList(protocolHotelCardList, hotelSort, prioritySupplierList));
            resultList.addAll(sortHotelCardList(nonProtocolHotelCardList, hotelSort, prioritySupplierList));
        } else {
            resultList.addAll(sortHotelCardList(hotelCardList, hotelSort, prioritySupplierList));
        }

        return resultList;
    }

    /**
     * 排序酒店卡片列表
     * @param hotelCardList 酒店卡片列表
     * @param hotelSort 酒店排序
     * @param prioritySupplierList 优先供应商列表
     * @return 排序后的酒店卡片列表
     */
    private List<HotelCard> sortHotelCardList(List<HotelCard> hotelCardList, HotelSort hotelSort, List<String> prioritySupplierList) {
        if (CollectionUtils.isEmpty(hotelCardList)) {
            return hotelCardList;
        }
        hotelCardList = hotelCardList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        // 价格排序
        if (hotelSort != null && SortTypeEnum.MIN_PRICE.name().equals(hotelSort.getSortType())) {
            return hotelCardList.stream().filter(Objects::nonNull)
                    .sorted(Comparator.comparing(
                                    (HotelCard item) -> Optional.ofNullable(item.getMinHotelPrice()).map(HotelPrice::getFinalAvgPrice).map(Price::getCustomPrice).orElse(HotelCoreConstant.MAX_PRICE),
                                    SortDirectionEnum.DESC.name().equals(hotelSort.getSortDirection()) ? Comparator.reverseOrder() : Comparator.naturalOrder())
                            .thenComparing(item -> Null.or(item.getDefaultSortNum(), Integer.MAX_VALUE))
                            .thenComparing(item -> getSortNum(item, prioritySupplierList)))
                    .collect(Collectors.toList());
        }
        // 星级排序
        else if (hotelSort != null && SortTypeEnum.STAR.name().equals(hotelSort.getSortType())) {
            return hotelCardList.stream().filter(Objects::nonNull)
                    .sorted(Comparator.comparing(
                                    (HotelCard item) -> Null.or(item.getStar(), 0),
                                    SortDirectionEnum.DESC.name().equals(hotelSort.getSortDirection()) ? Comparator.reverseOrder() : Comparator.naturalOrder())
                            .thenComparing(item -> Null.or(item.getDefaultSortNum(), Integer.MAX_VALUE))
                            .thenComparing(item -> getSortNum(item, prioritySupplierList)))
                    .collect(Collectors.toList());
        }
        // 距离排序
        else if (hotelSort != null && SortTypeEnum.DISTANCE.name().equals(hotelSort.getSortType())) {
            return hotelCardList.stream().filter(Objects::nonNull)
                    .sorted(Comparator.comparing(
                                    (HotelCard item) -> Null.or(item.getDistance(), 1000000D),
                                    SortDirectionEnum.DESC.name().equals(hotelSort.getSortDirection()) ? Comparator.reverseOrder() : Comparator.naturalOrder())
                            .thenComparing(item -> Null.or(item.getDefaultSortNum(), Integer.MAX_VALUE))
                            .thenComparing(item -> getSortNum(item, prioritySupplierList)))
                    .collect(Collectors.toList());
        }
        // 默认排序
        else {
            return hotelCardList.stream().filter(Objects::nonNull)
                    .sorted(Comparator.comparing((HotelCard item) -> Null.or(item.getDefaultSortNum(), Integer.MAX_VALUE))
                            .thenComparing(item -> getSortNum(item, prioritySupplierList)))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 聚合酒店页
     * @param hotelPageMap 酒店页map
     * @param hotelPageMetaDataMap 酒店页元数据map
     * @param prioritySupplierList 优先供应商列表
     * @param scene 场景
     * @param version 版本
     */
    private void aggregateHotelPage(Map<String, HotelPage> hotelPageMap, Map<String, HotelPageMetaData> hotelPageMetaDataMap, List<String> prioritySupplierList, String scene, String version) {
        if (CollectionUtils.isEmpty(hotelPageMap) || CollectionUtils.isEmpty(hotelPageMetaDataMap)) {
            return;
        }

        // 产线
        boolean atDomestic = hotelPageMetaDataMap.values().stream()
                .filter(item -> item != null && item.getAbroad() != null)
                .map(item -> !item.getAbroad())
                .findFirst()
                .orElse(true);
        
        // 直连特殊逻辑：获取直连供应商
        Set<String> directSupplierCodeSet = new HashSet<>();
        Set<String> supplierCodeSet = new HashSet<>();
        hotelPageMetaDataMap.forEach((supplierCode, hotelPageMetaData) -> {
            if (hotelPageMetaData != null && hotelPageMetaData.getSupplierProduct() != null && StringUtils.isNotBlank(supplierCode)) {
                if (Boolean.TRUE.equals(hotelPageMetaData.getSupplierProduct().getDirectSupplier())) {
                    directSupplierCodeSet.add(supplierCode);
                }
                supplierCodeSet.add(supplierCode);
            }
        });
        
        // 匹配关系
        Map<HotelKey, Set<HotelKey>> hotelRelationMap;
        
        // 转换成hotelInfo
        Map<String, List<HotelInfo>> hotelInfos = buildHotelInfoListMap(hotelPageMap);
        
        // 新匹配关系
        if (hotelRelationGatewayV2.isHotelRelationV2Enable()) {
            // 获取全量原始匹配关系
            List<HotelRelation> hotelRelations = hotelRelationDomainServiceV2.queryHotelRelations(hotelInfos, supplierCodeSet, directSupplierCodeSet);
            
            // 转换为索引
            hotelRelationMap = hotelRelationDomainServiceV2.extractAllHotelRelations(hotelRelations);
            
            // 转换为列表快照
            HotelListSnapshot hotelListSnapshot = self.toHotelListSnapshot(hotelPageMap, hotelRelations, supplierCodeSet, atDomestic);
            log.info("列表快照 hotelListSnapshot={}", JsonUtils.toJsonString(hotelListSnapshot));
        } else {
            hotelRelationMap = hotelRelationDomainService.getHotelRelationMap(hotelInfos, supplierCodeSet, directSupplierCodeSet);
        }

        // 没有匹配关系
        if (CollectionUtils.isEmpty(hotelRelationMap)) {
            return;
        }

        // 所有酒店卡片map
        Map<HotelKey, HotelCard> hotelCardMap = new HashMap<>();
        hotelPageMap.values().forEach(hotelPage -> {
            if (hotelPage == null || CollectionUtils.isEmpty(hotelPage.getHotelCardList())) {
                return;
            }
            hotelPage.getHotelCardList().forEach(hotelCard -> {
                hotelCardMap.put(
                        HotelKey.builder()
                                .supplierCode(hotelCard.getSupplierCode())
                                .hotelId(hotelCard.getHotelId()).build(),
                        hotelCard);
            });
        });

        // 需要查询起价的map
        Map<String, Set<String>> needQueryStartPriceMap = new HashMap<>();
        for (Set<HotelKey> hotelKeySet : hotelRelationMap.values()) {
            for (HotelKey hotelKey : hotelKeySet) {
                if (hotelCardMap.containsKey(hotelKey)) {
                    continue;
                }
                String tmpSupplierCode = hotelKey.getSupplierCode();
                Set<String> set = needQueryStartPriceMap.getOrDefault(tmpSupplierCode, new HashSet<>(0));
                set.add(hotelKey.getHotelId());
                needQueryStartPriceMap.put(tmpSupplierCode, set);
            }
        }
        log.info("需要查询起价的酒店 needQueryStartPriceMap={}", JsonUtils.toJsonString(needQueryStartPriceMap));

        // 起价查询
        callSupplierQueryHotelMinPrice(hotelCardMap, needQueryStartPriceMap, hotelPageMetaDataMap, scene, version);
        log.info("所有酒店卡片数据 hotelCardMap={}", JsonUtils.toJsonString(hotelCardMap));

        // 聚合
        Map<HotelKey, Set<HotelKey>> finalHotelRelationMap = hotelRelationMap;
        hotelPageMap.forEach((supplierCode, hotelPage) -> assembleHotelPage(hotelPage, finalHotelRelationMap, hotelCardMap, prioritySupplierList, supplierCode));
    }
    
    @BusinessBehaviorMonitor
    public HotelListSnapshot toHotelListSnapshot(Map<String, HotelPage> hotelPages, List<HotelRelation> hotelRelations, Set<String> supplierCodes, Boolean atDomestic) {
        Map<String, List<HotelCard>> supplierHotels = toSupplierHotels(hotelPages);
        return HotelListSnapshot.builder()
                .hotelRelations(hotelRelations)
                .supplierHotels(supplierHotels)
                .supplierCodes(supplierCodes)
                .atDomestic(atDomestic)
                .build();
    }
    
    private Map<String, List<HotelCard>> toSupplierHotels(Map<String, HotelPage> hotelPageMap) {
        if (CollectionUtils.isEmpty(hotelPageMap)) {
            return null;
        }
        
        Map<String, List<HotelCard>> supplierHotels = new HashMap<>();
        hotelPageMap.forEach((supplierCode, hotelPage) -> {
            if (hotelPage == null || StringUtils.isBlank(supplierCode)) {
                return;
            }
            
            supplierHotels.put(supplierCode, hotelPage.getHotelCardList());
        });
        return supplierHotels;
    }
    
    private Map<String, List<HotelInfo>> buildHotelInfoListMap(Map<String, HotelPage> hotelPageMap) {
        if (CollectionUtils.isEmpty(hotelPageMap)) {
            return null;
        }
        
        Map<String, List<HotelInfo>> hotelInfoListMap = new HashMap<>();
        hotelPageMap.forEach((supplierCode, hotelPage) -> {
            if (StringUtils.isBlank(supplierCode) || hotelPage == null || CollectionUtils.isEmpty(hotelPage.getHotelCardList())) {
                return;
            }
            hotelInfoListMap.put(
                    supplierCode,
                    hotelPage.getHotelCardList().stream()
                            .filter(Objects::nonNull)
                            .map(item -> HotelInfo.builder()
                                    .supplierCode(item.getSupplierCode())
                                    .hotelId(item.getHotelId())
                                    .name(item.getName())
                                    .lon(item.getLon())
                                    .lat(item.getLat())
                                    .geoLocation(GeoLocation.builder()
                                            .lat(item.getLat())
                                            .lon(item.getLon())
                                            .build())
                                    .build())
                            .collect(Collectors.toList()));
        });
        return hotelInfoListMap;
    }

    /**
     * 组装酒店页
     * @param hotelPage 酒店页
     * @param hotelRelationMap 酒店关系map
     * @param hotelCardMap 酒店卡片map
     * @param prioritySupplierList 优先供应商列表
     * @param supplierCode 供应商编码
     */
    private void assembleHotelPage(HotelPage hotelPage, Map<HotelKey, Set<HotelKey>> hotelRelationMap, Map<HotelKey, HotelCard> hotelCardMap, List<String> prioritySupplierList, String supplierCode) {
        List<HotelCard> hotelCardList = new ArrayList<>();
        // 匹配并组装
        for (HotelCard hotelCard : hotelPage.getHotelCardList()) {
            // 找匹配关系
            HotelKey key = HotelKey.builder()
                    .supplierCode(hotelCard.getSupplierCode())
                    .hotelId(hotelCard.getHotelId()).build();
            Set<HotelKey> relationSet = hotelRelationMap.get(key);

            // 无匹配关系
            if (CollectionUtils.isEmpty(relationSet)) {
                hotelCardList.add(hotelCard);
                continue;
            }

            // 有匹配关系
            relationSet.add(key);
            List<HotelCard> tmpList = relationSet.stream().map(hotelCardMap::get).filter(Objects::nonNull)
                    .sorted(Comparator.comparing(item -> getSortNum(item, prioritySupplierList))).collect(Collectors.toList());

            // 组装卡片
            HotelCard resultHotelCard = new HotelCard();
            resultHotelCard.setSupplierCode(supplierCode);
            assembleStaticAttr(resultHotelCard, tmpList, supplierCode);
            assembleDynamicAttr(resultHotelCard, tmpList, supplierCode);
            hotelCardList.add(resultHotelCard);
        }
        hotelPage.setHotelCardList(hotelCardList);
    }

    /**
     * 组装动态属性
     * @param hotelCard 酒店卡片
     * @param hotelCardList 酒店卡片列表
     * @param supplierCode 供应商编码
     */
    private void assembleDynamicAttr(HotelCard hotelCard, List<HotelCard> hotelCardList, String supplierCode) {
        if (hotelCard == null || CollectionUtils.isEmpty(hotelCardList)) {
            return;
        }
        Set<HotelSupplier> tmpHotelSupplierSet = new HashSet<>();
        for (HotelCard item : hotelCardList) {
            if (item == null) {
                continue;
            }

            // 填充供应商
            HotelSupplier hotelSupplier = HotelSupplier.builder()
                    .hotelId(item.getHotelId())
                    .minHotelPrice(item.getMinHotelPrice())
                    .supplierCode(item.getSupplierCode())
                    .supplierName(item.getSupplierName()).build();
            tmpHotelSupplierSet.add(hotelSupplier);

            // 更新最小价格
            HotelPrice tmpMinPrice = item.getMinHotelPrice();
            HotelPrice minHotelPrice = hotelCard.getMinHotelPrice();
            if ((tmpMinPrice != null && Price.checkCustomPrice(tmpMinPrice.getFinalAvgPrice()))
                    && (minHotelPrice == null || !Price.checkCustomPrice(minHotelPrice.getFinalAvgPrice()) || tmpMinPrice.getFinalAvgPrice().getCustomPrice().compareTo(minHotelPrice.getFinalAvgPrice().getCustomPrice()) < 0)) {
                hotelCard.setMinHotelPrice(tmpMinPrice);
            }

            // 更新协议标签
            Integer tmpProtocolType = item.getProtocolType();
            Integer protocolType = hotelCard.getProtocolType();

            if (tmpProtocolType != null && (protocolType == null || protocolType != 3)) {
                hotelCard.setProtocolType(tmpProtocolType);
            }
            hotelCard.setUnavailableReason(mergeUnAvailAbleReason(hotelCardList));

            // 更新会员标签
            List<MemberTagInfo> finalHotelTagList = mergeMemberTagInfoList(hotelCard, item);
            hotelCard.setAllHotelTagList(finalHotelTagList);

        }
        // 把主酒店提前
        hotelCard.setSupplierList(tmpHotelSupplierSet.stream().sorted(Comparator.comparing(
                item -> item != null && StringUtils.equalsIgnoreCase(item.getSupplierCode(), supplierCode) ? 0 : Integer.MAX_VALUE)).collect(Collectors.toList()));
    }

    private UnAvailableReasonEnum mergeUnAvailAbleReason(List<HotelCard> hotelCardList) {
        if (CollectionUtils.isEmpty(hotelCardList)) {
            return UnAvailableReasonEnum.canBooking;
        }
        Set<UnAvailableReasonEnum> unAvailableReasonEnumSet =
                hotelCardList.stream().
                        filter(t->!UnAvailableReasonEnum.UNKnown.name().equals(t.getUnavailableReason().name()))
                        .map(HotelCard::getUnavailableReason).collect(Collectors.toSet());
        if (unAvailableReasonEnumSet.size() == 1 ){
            return unAvailableReasonEnumSet.iterator().next();
        }
        if (unAvailableReasonEnumSet.size() > 1 ) {
            return UnAvailableReasonEnum.cantBooking;
        }
        return UnAvailableReasonEnum.canBooking;
    }

    private List<MemberTagInfo> mergeMemberTagInfoList(HotelCard that, HotelCard item) {
        if (CollectionUtils.isEmpty(that.getAllHotelTagList()) && CollectionUtils.isEmpty(item.getAllHotelTagList())) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(that.getAllHotelTagList())) {
            return item.getAllHotelTagList();
        }
        if (CollectionUtils.isEmpty(item.getAllHotelTagList())) {
            return that.getAllHotelTagList();
        }

        return new ArrayList<>(Stream.concat(that.getAllHotelTagList().stream(), item.getAllHotelTagList().stream())
                .collect(Collectors.toMap(MemberTagInfo::getTagCode, Function.identity(), (a, b) -> a))
                .values());
    }


    /**
     * 组装静态属性
     * @param hotelCard 酒店卡片
     * @param hotelCardList 酒店卡片列表
     */
    private void assembleStaticAttr(HotelCard hotelCard, List<HotelCard> hotelCardList, String supplierCode) {
        if (hotelCard == null || CollectionUtils.isEmpty(hotelCardList)) {
            return;
        }
        for (HotelCard item : hotelCardList) {
            if (item == null) {
                continue;
            }
            if (StringUtils.isBlank(hotelCard.getName())) {
                hotelCard.setName(item.getName());
            }
            if (StringUtils.isBlank(hotelCard.getNameEn())) {
                hotelCard.setNameEn(item.getNameEn());
            }
            if (StringUtils.isBlank(hotelCard.getAddress())) {
                hotelCard.setAddress(item.getAddress());
            }
            if (hotelCard.getLon() == null || hotelCard.getLat() == null || hotelCard.getLon() == 0 || hotelCard.getLat() == 0 || hotelCard.getLon() == -1 || hotelCard.getLat() == -1) {
                hotelCard.setLon(item.getLon());
                hotelCard.setLat(item.getLat());
                hotelCard.setDistance(item.getDistance());
                hotelCard.setDistanceText(item.getDistanceText());
            }
            if (StringUtils.isBlank(hotelCard.getLogoUrl())) {
                hotelCard.setLogoUrl(item.getLogoUrl());
            }
            if (supplierCode.equals(item.getSupplierCode())) {
                hotelCard.setStar(item.getStar());
                hotelCard.setStarLicence(item.getStarLicence());
                hotelCard.setLevelName(item.getLevelName());
            }
            if (StringUtils.isBlank(hotelCard.getReviewScore())) {
                hotelCard.setReviewScore(item.getReviewScore());
            }
            if (CollectionUtils.isEmpty(hotelCard.getFacilityList())) {
                hotelCard.setFacilityList(item.getFacilityList());
            }
        }
    }

    /**
     * 获取排序序号
     * @param hotelCard 酒店卡片
     * @param prioritySupplierList 优先供应商列表
     * @return 排序序号
     */
    private Integer getSortNum(HotelCard hotelCard, List<String> prioritySupplierList) {
        if (hotelCard == null || CollectionUtils.isEmpty(prioritySupplierList)) {
            return Integer.MAX_VALUE;
        }
        int index = prioritySupplierList.indexOf(hotelCard.getSupplierCode());
        return index == -1 ? Integer.MAX_VALUE : index;
    }

    /**
     * 起价查询
     * @param hotelCardMap 酒店卡片map
     * @param needQueryStartPriceMap 需要查询起价的map
     * @param hotelPageMetaDataMap 酒店页元数据map
     * @param scene 场景
     * @param version 版本
     */
    private void callSupplierQueryHotelMinPrice(Map<HotelKey, HotelCard> hotelCardMap, Map<String, Set<String>> needQueryStartPriceMap, Map<String, HotelPageMetaData> hotelPageMetaDataMap, String scene, String version) {
        if (CollectionUtils.isEmpty(hotelCardMap) || CollectionUtils.isEmpty(needQueryStartPriceMap) || CollectionUtils.isEmpty(hotelPageMetaDataMap)) {
            return;
        }

        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        needQueryStartPriceMap.forEach((supplierCode, hotelIdSet) -> {
            // 请求元数据
            HotelPageMetaData hotelPageMetaData = hotelPageMetaDataMap.get(supplierCode);
            if (hotelPageMetaData == null || hotelPageMetaData.getSupplierProduct() == null || CollectionUtils.isEmpty(hotelIdSet)) {
                return;
            }

            // 分页请求
            CollUtil.split(new ArrayList<>(hotelIdSet), HotelCoreConstant.PAGE_SIZE).forEach(list -> completableFutureList.add(CompletableFuture.runAsync(() -> {
                QueryHotelListRequest queryHotelListRequest = QueryHotelListRequest.builder()
                        .supplierProduct(hotelPageMetaData.getSupplierProduct())
                        .hotelBaseFilter(hotelPageMetaData.getHotelBaseFilter())
                        .hotelPositionFilter(new HotelPositionFilter())
                        .hotelAdvancedFilter(HotelAdvancedFilter.builder()
                                .choiceStarList(Optional.ofNullable(hotelPageMetaData.getHotelAdvancedFilter())
                                        .map(HotelAdvancedFilter::getChoiceStarList).orElse(IntStream.rangeClosed(1,5).boxed().collect(Collectors.toList())))
                                .lowPrice(Optional.ofNullable(hotelPageMetaData.getHotelAdvancedFilter())
                                        .map(HotelAdvancedFilter::getLowPrice).orElse(null))
                                .highPrice(Optional.ofNullable(hotelPageMetaData.getHotelAdvancedFilter())
                                        .map(HotelAdvancedFilter::getHighPrice).orElse(null))
                                .filterWithServiceCharge(hotelPageMetaData.extractFilterWithServiceCharge())
                                .build())
                        .hotelSort(new HotelSort())
                        .hotelDistanceDesc(hotelPageMetaData.getHotelDistanceDesc())
                        .pageSize(HotelCoreConstant.PAGE_SIZE)
                        .pageIndex(1)
                        .lastPageIndex(null)
                        .abroad(hotelPageMetaData.getAbroad())
                        .travelMode(hotelPageMetaData.getTravelMode())
                        .build();
                queryHotelListRequest.getHotelAdvancedFilter().setHotelIdList(list);
                HotelPage hotelPage = queryHotelListGateway.execute(queryHotelListRequest, scene, version, "酒店起价查询");
                if (hotelPage == null || CollectionUtils.isEmpty(hotelPage.getHotelCardList())) {
                    return;
                }

                hotelPage.getHotelCardList().forEach(hotelCard -> {
                    hotelCardMap.put(
                            HotelKey.builder()
                                    .supplierCode(hotelCard.getSupplierCode())
                                    .hotelId(hotelCard.getHotelId()).build(),
                            hotelCard);
                });
            }, queryHotelMinPriceThreadPool)));
        });
        // 等待所有请求完成
        try {
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(commonGateway.getSupplierTimeOut(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("请求超时", e);
        }
    }

    /**
     * 构建酒店页元数据缓存key
     * @param hotelPageRequest 酒店页请求
     * @return 缓存key
     */
    private String buildHotelPageMetaDataMapKey(HotelPageRequest hotelPageRequest) {
        String key = removeRandomInfo(hotelPageRequest);
        if (StringUtils.isBlank(key)) {
            return null;
        }

        return HotelCoreConstant.RedisKey.HOTEL_PAGE_META_DATA + HotelCoreConstant.COLON + key;
    }

    /**
     * 构建酒店页缓存key
     * @param hotelPageRequest 酒店页请求
     * @param pageIndex 页码
     * @return 缓存key
     */
    private String buildHotelPageMapKey(HotelPageRequest hotelPageRequest, Integer pageIndex, Integer pageSize) {
        if (pageIndex == null || pageSize == null) {
            return null;
        }

        String key = removeRandomInfo(hotelPageRequest);
        if (StringUtils.isBlank(key)) {
            return null;
        }

        return HotelCoreConstant.RedisKey.HOTEL_PAGE + HotelCoreConstant.COLON + key + HotelCoreConstant.COLON + pageIndex + HotelCoreConstant.COLON + pageSize;
    }

    /**
     * 移除随机信息
     * @param hotelPageRequest 酒店页请求
     * @return 移除随机信息后的md5
     */
    private String removeRandomInfo(HotelPageRequest hotelPageRequest) {
        if (hotelPageRequest == null) {
            return null;
        }

        HotelPageRequest tmp = JsonUtils.parse(JsonUtils.toJsonString(hotelPageRequest), HotelPageRequest.class);

        if (CollectionUtils.isNotEmpty(tmp.getSupplierProductList())) {
            List<SupplierProduct> supplierProductList = new ArrayList<>();
            for (SupplierProduct supplierProduct : tmp.getSupplierProductList()) {
                if (supplierProduct == null) {
                    continue;
                }
                supplierProduct.setSupplierCode(null);
                supplierProduct.setSupplierName(null);
                supplierProduct.setUrl(null);
                supplierProduct.setUserKey(null);
                supplierProduct.setServiceChargeList(null);
                supplierProductList.add(supplierProduct);
            }
            tmp.setSupplierProductList(supplierProductList);
        }

        return Md5Utils.md5Hex(tmp.toString());
    }

    /**
     * 获取酒店页元数据map
     * @param hotelPageRequest 酒店页请求
     * @return 酒店页元数据map
     */
    private Map<String, HotelPageMetaData> getHotelPageMetaDataMap(HotelPageRequest hotelPageRequest) {
        if (hotelPageRequest == null) {
            return null;
        }

        // 从缓存获取元数据
        Map<String, HotelPageMetaData> hotelPageMetaDataMap = hotelPageGateway.getHotelPageMetaDataMap(buildHotelPageMetaDataMapKey(hotelPageRequest));
        if (hotelPageMetaDataMap != null) {
            return hotelPageMetaDataMap;
        }
        // 重新组装
        hotelPageMetaDataMap = new HashMap<>();

        // 转换
        String jsonString = JsonUtils.toJsonString(hotelPageRequest);
        for (SupplierProduct supplierProduct : hotelPageRequest.getSupplierProductList()) {
            HotelPageMetaData hotelPageMetaData = new HotelPageMetaData();
            HotelPageRequest tmp = JsonUtils.parse(jsonString, HotelPageRequest.class);
            hotelPageMetaData.setSupplierProduct(supplierProduct);
            hotelPageMetaData.setHotelBaseFilter(tmp.getHotelBaseFilter());
            hotelPageMetaData.setHotelPositionFilter(tmp.getHotelPositionFilter());
            hotelPageMetaData.setHotelAdvancedFilter(tmp.getHotelAdvancedFilter());
            hotelPageMetaData.setHotelSort(tmp.getHotelSort());
            hotelPageMetaData.setHotelDistanceDesc(tmp.getHotelDistanceDesc());
            hotelPageMetaData.setLastPageIndex(null);
            hotelPageMetaData.setAbroad(tmp.getAbroad());
            hotelPageMetaData.setTravelMode(tmp.getTravelMode());

            // 只有美亚需要城市id替换
            String supplierCode = supplierProduct.getSupplierCode();
            if (HotelCoreConstant.MEIYA.equals(supplierCode)){
                HotelBaseFilter hotelBaseFilter = hotelPageMetaData.getHotelBaseFilter();
                String supplierCityId = hotelPageGateway.translationCityId(hotelBaseFilter.getCityId(), supplierCode);
                if (StringUtils.isBlank(supplierCityId)) {
                    log.error("城市id替换失败 hotelPageCondition={}", hotelPageMetaData);
                }
                log.info("城市id替换成功 supplierCityId={}", supplierCityId);
                hotelBaseFilter.setCityId(supplierCityId);
            }
            hotelPageMetaDataMap.put(supplierCode, hotelPageMetaData);
        }
        return hotelPageMetaDataMap;
    }
    
    /**
     * 预处理
     * @param hotelPageRequest 酒店页请求
     */
    private void preProcess(HotelPageRequest hotelPageRequest) {
        if (hotelPageRequest.getHotelBaseFilter() == null) {
            hotelPageRequest.setHotelBaseFilter(new HotelBaseFilter());
        }
        if (hotelPageRequest.getHotelPositionFilter() == null) {
            hotelPageRequest.setHotelPositionFilter(new HotelPositionFilter());
        }
        if (hotelPageRequest.getHotelAdvancedFilter() == null) {
            hotelPageRequest.setHotelAdvancedFilter(new HotelAdvancedFilter());
        }
        if (hotelPageRequest.getHotelSort() == null) {
            hotelPageRequest.setHotelSort(new HotelSort());
        }
        if (hotelPageRequest.getHotelDistanceDesc() == null) {
            hotelPageRequest.setHotelDistanceDesc(new HotelDistanceDesc());
        }
        if (hotelPageRequest.getSupplierProductList() == null) {
            hotelPageRequest.setSupplierProductList(new ArrayList<>());
        }
        if (CollectionUtils.isNotEmpty(hotelPageRequest.getSupplierProductList())) {
            hotelPageRequest.setSupplierProductList(getPrioritySupplierProductList(hotelPageRequest.getSupplierProductList()));
        }
        if (CollectionUtils.isNotEmpty(hotelPageRequest.getHotelAdvancedFilter().getControlStarList())) {
            hotelPageRequest.getHotelAdvancedFilter().getControlStarList().sort(Comparator.comparing(item -> Null.or(item, Integer.MAX_VALUE)));
        }
        if (CollectionUtils.isNotEmpty(hotelPageRequest.getHotelAdvancedFilter().getChoiceStarList())) {
            hotelPageRequest.getHotelAdvancedFilter().getChoiceStarList().sort(Comparator.comparing(item -> Null.or(item, Integer.MAX_VALUE)));
        }
        if (CollectionUtils.isNotEmpty(hotelPageRequest.getHotelAdvancedFilter().getBrandIdList())) {
            hotelPageRequest.getHotelAdvancedFilter().getBrandIdList().sort(Comparator.comparing(item -> Null.or(item, "")));
        }
        if (CollectionUtils.isNotEmpty(hotelPageRequest.getHotelAdvancedFilter().getGroupIdList())) {
            hotelPageRequest.getHotelAdvancedFilter().getGroupIdList().sort(Comparator.comparing(item -> Null.or(item, "")));
        }
        hotelPageRequest.setPageSize(null);
    }

    /**
     * 获取优先级供应商产品列表
     * @param supplierProductList 供应商产品列表
     * @return 优先级供应商产品列表
     */
    private List<SupplierProduct> getPrioritySupplierProductList(List<SupplierProduct> supplierProductList) {
        if (CollectionUtils.isEmpty(supplierProductList)) {
            return null;
        }

        return supplierProductList.stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing((SupplierProduct item) -> Null.or(item.getSortNum(), Integer.MAX_VALUE))
                        .thenComparing(item -> Null.or(item.getSupplierCode(), "")))
                .collect(Collectors.toList());
    }

}
