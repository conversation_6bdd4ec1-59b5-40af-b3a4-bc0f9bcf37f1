package com.corpgovernment.core.domain.model.snapshot.fee;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/29
 */
@Data
public class TimeInformationModel {
    /**
     * 房型最晚取消修改时间
     *
     * 例如：2024-04-08 18:00:00
     */

    private String lastCancelTime;
    /**
     * 酒店最早到店时间
     *
     * 例如：2024-04-08 18:00:00
     */

    private String earlyArrivalTime;
    /**
     *
     * 酒店最晚到店时间
     *
     * 例如：2024-04-08 18:00:00
     */
    private String lastArrivalTime;
    /**
     * 房型最晚保留时间
     *
     * 例如：2024-04-08 18:00:00
     */
    private String holdTime;
}
