package com.corpgovernment.core.domain.model.snapshot.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
@Data
public class HotelQueryContextModel {
    /**
     * 城市id
     */
    private String cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 入住时间
     */
    private String checkInDate;

    /**
     * 离店时间
     */
    private String checkOutDate;

    /**
     * 房间数
     */
    private Integer roomNum;

    /**
     * 入住人数
     */
    private Integer personNum;

    /**
     * 因公/因私(PUB/OWN)
     */
    private String corpPayType;
}
