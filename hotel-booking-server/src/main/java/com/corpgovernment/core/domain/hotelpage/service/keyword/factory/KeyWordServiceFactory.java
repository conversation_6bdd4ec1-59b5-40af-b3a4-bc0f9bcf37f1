package com.corpgovernment.core.domain.hotelpage.service.keyword.factory;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.core.domain.hotelpage.model.enums.KeyWordTypeEnum;
import com.corpgovernment.core.domain.hotelpage.service.keyword.IKeyWordService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-15 19:24
 */
@Service
public class KeyWordServiceFactory {

    private final Map<KeyWordTypeEnum, IKeyWordService> map = new HashMap<>();

    public IKeyWordService getKeyWordService(KeyWordTypeEnum keyWordTypeEnum) {
        return map.get(keyWordTypeEnum);
    }

    @Autowired
    public void setMap(List<IKeyWordService> keyWordServiceList) {
        if (CollectionUtils.isEmpty(keyWordServiceList)) {
            return;
        }

        for (IKeyWordService keyWordService : keyWordServiceList) {
            Set<KeyWordTypeEnum> keyWordTypeEnumList = keyWordService.keyWordTypeEnumList();
            if (CollectionUtils.isEmpty(keyWordTypeEnumList)) {
                continue;
            }
            for (KeyWordTypeEnum keyWordTypeEnum : keyWordTypeEnumList) {
                map.put(keyWordTypeEnum, keyWordService);
            }
        }
    }

}
