package com.corpgovernment.core.domain.hotelpage.model.entity;

import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 酒店页查询
 * <AUTHOR>
 * @date 2023/12/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelPageRequest {

    // 供应商产品
    private List<SupplierProduct> supplierProductList;
    // 查询条件
    private HotelBaseFilter hotelBaseFilter;
    private HotelPositionFilter hotelPositionFilter;
    private HotelAdvancedFilter hotelAdvancedFilter;
    // 排序
    private HotelSort hotelSort;
    // 距离文案
    private HotelDistanceDesc hotelDistanceDesc;
    // 海外产线
    private Boolean abroad;
    // 协议优先排序
    private Boolean protocolPrioritySort;
    // 查询数量
    private Integer pageSize;
    // 因公因私
    private TravelModeEnum travelMode;

}
