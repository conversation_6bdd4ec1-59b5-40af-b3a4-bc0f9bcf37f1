package com.corpgovernment.core.domain.common.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024-08-01 20:16
 */
@Getter
@AllArgsConstructor
public enum ServiceChargeStrategyEnum {

    ORDER("order", "按订单收取", "BY_BOOKING"),
    ROOM_NIGHT("roomNight", "按间夜收取", "BY_ROOM_NIGHTS"),
    ROOM("room", "按房间收取", "BY_ROOM_QUANTITY"),
    FIXED_RATIO("fixedRatio", "按固定比率收取", "BY_AMOUNT"),
    ;

    private final String code;

    private final String info;
    
    private final String supplierCode;

    private static final Map<String, ServiceChargeStrategyEnum> map = new HashMap<>();

    static {
        for (ServiceChargeStrategyEnum tmpEnum : values()) {
            map.put(tmpEnum.getCode(), tmpEnum);
            map.put(tmpEnum.getInfo(), tmpEnum);
            map.put(tmpEnum.getSupplierCode(), tmpEnum);
        }
    }

    public static ServiceChargeStrategyEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }

}
