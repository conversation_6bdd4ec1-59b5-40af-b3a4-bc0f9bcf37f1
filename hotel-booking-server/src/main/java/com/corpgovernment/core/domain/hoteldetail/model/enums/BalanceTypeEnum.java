package com.corpgovernment.core.domain.hoteldetail.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-21 03:24
 */
@AllArgsConstructor
@Getter
public enum BalanceTypeEnum {

    PP("PP", "在线付"),
    USE_FG("USE_FG", "在线付"),
    FG("FG", "到店付"),
    ;

    private final String code;
    private final String desc;

    private static final Map<String, BalanceTypeEnum> map = new HashMap<>();

    static {
        for (BalanceTypeEnum balanceTypeEnum : values()) {
            map.put(balanceTypeEnum.getCode(), balanceTypeEnum);
        }
    }

    public static BalanceTypeEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }

}
