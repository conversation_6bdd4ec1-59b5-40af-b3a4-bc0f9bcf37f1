package com.corpgovernment.core.domain.hotelconfig.service;

import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.HotelCustomEnum;
import com.corpgovernment.core.domain.common.model.enums.HotelForceChummageEnum;
import com.corpgovernment.core.domain.hotelconfig.model.entity.HotelRoomCheckInInfo;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelApplication;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelConfig;
import com.corpgovernment.core.domain.hotelconfig.model.entity.TravelStandard;
import com.corpgovernment.core.domain.hotelconfig.model.enums.HotelChummageVerifyResultEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/13
 */
public interface IHotelConfigDomainService {

    /**
     * 获取差旅配置
     * @param token token
     * @param travelMode 差旅模式
     * @param operatorTypeList 运营商类型列表
     * @return 差旅配置
     */
    TravelConfig getTravelConfig(String token, String travelMode, String productType, List<String> operatorTypeList);

    /**
     * 获取差旅标准
     * @param token token
     * @return 差旅标准
     */
    TravelStandard getTravelStandard(String token);

    /**
     * 获取供应商产品列表
     * @param travelConfig 差旅配置
     * @param operatorType 运营商类型
     * @return 供应商产品列表
     */
    List<SupplierProduct> getSupplierProductList(TravelConfig travelConfig, String operatorType, Boolean hasHourlyRoom);

    /**
     * 检查经纬度差旅申请
     * @param travelApplication 差旅申请
     * @return 是否通过
     */
    Boolean checkLatLonTravelApplication(TravelApplication travelApplication);
    
    List<String> getPaymentMethodList(TravelStandard travelStandard, TravelConfig travelConfig);
    
    HotelChummageVerifyResultEnum verifyHotelChummage(HotelForceChummageEnum hotelForceChummageEnum,
                                                      Boolean hotelForceSameSexChummage,
                                                      List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList);
    
    Boolean getHaveHotelChummage(List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList);
    
    TravelConfig getTravelConfig(String token, List<String> operatorTypeList);
    
    String getNoChummageDesc(HotelChummageVerifyResultEnum hotelChummageVerifyResultEnum,
                             HotelForceChummageEnum hotelForceChummageEnum);
    
    
    /**
     * 确定区县差标
     */
    String determineDistrictTravelStandard(String token, String cityId, String districtId, String countyId);
    
    HotelCustomEnum getHotelCustomEnum(String token);

}
