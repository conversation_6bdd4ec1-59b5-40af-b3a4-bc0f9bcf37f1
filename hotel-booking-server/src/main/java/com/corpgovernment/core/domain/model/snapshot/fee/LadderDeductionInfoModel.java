package com.corpgovernment.core.domain.model.snapshot.fee;

import com.corpgovernment.core.domain.model.SupplierCheckAvailResultModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/31
 */
@Data
public class LadderDeductionInfoModel {
    /**
     * 扣款类型
     *
     * PPFirstDay（预付扣首日）；PPFull（预付扣全额）；CanNotCancelation（不可取消，已过最晚取消时间）
     */
    private String deductionType;
    private LadderDeductionDetailModel ladderDeductionInfo;
}
