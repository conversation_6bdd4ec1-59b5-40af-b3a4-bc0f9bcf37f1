package com.corpgovernment.core.domain.common.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SupplierProduct {

    private String supplierCode;
    private Boolean directSupplier;
    private String supplierName;
    private String url;
    private String parameterlessUrl;
    private String uid;
    private String corpId;
    private String userKey;
    private Boolean shield;
    private Integer sortNum;
    private String aid;
    private String sid;
    private List<ServiceCharge> serviceChargeList;

}
