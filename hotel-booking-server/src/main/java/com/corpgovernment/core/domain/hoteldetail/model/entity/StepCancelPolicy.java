package com.corpgovernment.core.domain.hoteldetail.model.entity;

import com.corpgovernment.core.domain.hoteldetail.model.enums.CancelRuleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/5/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StepCancelPolicy {

    private CancelRuleEnum cancelRuleEnum;
    private String startTime;
    private String endTime;
    private BigDecimal price;

}
