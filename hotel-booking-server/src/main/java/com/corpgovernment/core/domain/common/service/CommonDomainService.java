package com.corpgovernment.core.domain.common.service;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.shunt.ShuntConfigDao;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.domain.common.gateway.IAroundPlaceQueryGateway;
import com.corpgovernment.core.domain.common.gateway.ICommonGateway;
import com.corpgovernment.core.domain.common.model.entity.CityInfo;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.SupplierConfig;
import com.corpgovernment.core.domain.common.model.enums.SystemSupplierEnum;
import com.corpgovernment.util.AmountPrecisionUtil;
import com.corpgovernment.util.BizTypeContextUtil;
import com.ctrip.corp.obt.generic.core.context.RequestContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-15 22:35
 */
@Service
@Slf4j
public class CommonDomainService implements ICommonDomainService {

    @Resource
    private ICommonGateway commonGateway;
    
    @Resource
    private IAroundPlaceQueryGateway gaodeAroundPlaceQueryGateway;

    @Override
    public String getStayTime() {
        return commonGateway.getStayTime();
    }

    @Override
    public Boolean setStayTime() {
        return commonGateway.setStayTime();
    }

    @Override
    public String getCorpShortName() {
        return commonGateway.getCorpShortName();
    }

    @Override
    public Integer getSupplierTimeOut() {
        return commonGateway.getSupplierTimeOut();
    }

    @Override
    public BigDecimal getRoomNightNum(String checkInDate, String checkOutDate, Integer roomQuantity) {
        return commonGateway.getRoomNightNum(checkInDate, checkOutDate, roomQuantity);
    }

    @Override
    public Boolean checkCustomPrice(Price price) {
        return price != null && price.getCustomPrice() != null;
    }

    @Override
    public void setViewedHotel(List<String> viewedHotelIdList) {
        commonGateway.setViewedHotel(viewedHotelIdList);
    }
    
    @Override
    public List<String> getOverLimitModeList(List<String> overLimitModeList, List<String> paymentMethodList) {
        return commonGateway.getOverLimitModeList(overLimitModeList, paymentMethodList);
    }
    
    @Override
    @BusinessBehaviorMonitor
    public CityInfo getCityInfo(Double lon, Double lat) {
        CityInfo cityInfo = gaodeAroundPlaceQueryGateway.queryAroundPlace(lon, lat);
        
        String cityName = Optional.ofNullable(cityInfo).map(CityInfo::getCityName).orElse(null);
        String countyName = Optional.ofNullable(cityInfo).map(CityInfo::getCountyName).orElse(null);
        String districtName = Optional.ofNullable(cityInfo).map(CityInfo::getDistrictName).orElse(null);
        Double addressLon = Optional.ofNullable(cityInfo).map(CityInfo::getAddressLon).orElse(null);
        Double addressLat = Optional.ofNullable(cityInfo).map(CityInfo::getAddressLat).orElse(null);
        
        // name转id
        if (StringUtils.isNotBlank(countyName)) {
            return commonGateway.getCityCountyInfo(countyName, addressLat, addressLon);
        } else if (StringUtils.isNotBlank(districtName)) {
            return commonGateway.getCityDistrictInfo(cityName, districtName);
        }
        
        return null;
    }
    
    @Override
    public SupplierConfig getSupplierConfig(String supplierCode) {
        try {
            List<SupplierConfig> supplierConfigList = commonGateway.getSupplierConfigList();
            if (CollectionUtils.isEmpty(supplierConfigList) || StringUtils.isBlank(supplierCode)) {
                return null;
            }
            
            for (SupplierConfig supplierConfig : supplierConfigList) {
                if (supplierConfig == null) {
                    continue;
                }
                
                // 所有供应商编码
                Set<String> supplierCodeSet = new HashSet<>();
                if (StringUtils.isNotBlank(supplierConfig.getSystemSupplierCode())) {
                    supplierCodeSet.add(supplierConfig.getSystemSupplierCode());
                }
                if (supplierConfig.getClientSupplierCodeList() != null) {
                    List<String> tmpList = supplierConfig.getClientSupplierCodeList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tmpList)) {
                        supplierCodeSet.addAll(tmpList);
                    }
                }
                
                if (supplierCodeSet.contains(supplierCode)) {
                    return supplierConfig;
                }
            }
            
            return null;
        } catch (Exception exception) {
            log.error("getSupplierConfig error", exception);
            return null;
        }
    }
    
    @Override
    public String getSystemSupplierCode(String supplierCode) {
        SupplierConfig supplierConfig = getSupplierConfig(supplierCode);
        if (supplierConfig != null && StringUtils.isNotBlank(supplierConfig.getSystemSupplierCode())) {
            return supplierConfig.getSystemSupplierCode();
        }
        
        return supplierCode;
    }
    
    @Override
    public String zoomOutPicture(String supplierCode, String pictureUrl) {
        try {
            if (StringUtils.isBlank(pictureUrl)) {
                return pictureUrl;
            }
            
            // 携程图像裁剪
            if (StringUtils.equalsIgnoreCase(SystemSupplierEnum.CTRIP.getCode(), getSystemSupplierCode(supplierCode))) {
                // 图片有被裁剪
                pictureUrl = pictureUrl.replaceAll("_R_\\d+_\\d+", "");
                int index = pictureUrl.lastIndexOf(".");
                if (index != -1) {
                    return new StringBuilder(pictureUrl).insert(index, HotelCoreConstant.CTRIP_ZOOM_OUT_PICTURE).toString();
                }
            }
            
            return pictureUrl;
        } catch (Exception e) {
            log.error("zoomOutPicture error", e);
            return pictureUrl;
        }
    }
    
    @Override
    public List<String> zoomOutPictureList(String supplierCode, List<String> pictureUrlList) {
        try {
            if (CollectionUtils.isEmpty(pictureUrlList)) {
                return pictureUrlList;
            }
            
            return pictureUrlList.stream().map(pictureUrl -> zoomOutPicture(supplierCode, pictureUrl)).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("zoomOutPictureList error", e);
            return pictureUrlList;
        }
    }
    
    @Override
    public Double getDistance(Double lat1, Double lon1, Double lat2, Double lon2) {
        if (lat1 == null || lon1 == null || lat2 == null || lon2 == null) {
            return null;
        }
        
        return new GeodeticCalculator()
                .calculateGeodeticCurve(Ellipsoid.Sphere, new GlobalCoordinates(lat1, lon1), new GlobalCoordinates(lat2, lon2))
                .getEllipsoidalDistance();
    }
    
    @Override
    public BigDecimal getCustomPrice(Price price) {
        if (price == null || price.getCustomPrice() == null) {
            return BigDecimal.ZERO;
        }
        return price.getCustomPrice();
    }
    
    @Override
    public Boolean openFeature(String feature) {
        return commonGateway.openFeature(feature);
    }
    
}
