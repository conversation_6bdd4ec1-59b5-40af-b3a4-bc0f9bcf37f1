package com.corpgovernment.core.domain.hotelpage.service.control.impl;

import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControlRequest;
import com.corpgovernment.core.domain.hotelpage.model.enums.RequestControlEnum;
import com.corpgovernment.core.domain.hotelpage.service.control.IRequestControlService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-09-18 16:49
 */
@Service
public class StarIntersectionRequestControlService implements IRequestControlService {
    @Override
    public String controlRequest(HotelPageRequest hotelPageRequest, TravelControlRequest travelControlRequest) {
        if (travelControlRequest == null) {
            return "";
        }
        
        // 星级无交集
        List<Integer> shieldStarList = travelControlRequest.getShieldStarList();
        List<Integer> starList = travelControlRequest.getStarList();
        if (CollectionUtils.isNotEmpty(shieldStarList) && CollectionUtils.isNotEmpty(starList)
                && starList.stream().noneMatch(item -> item != null && !shieldStarList.contains(item))) {
            return requestControlItemEnum().getCode();
        }
        
        return "";
    }
    
    @Override
    public RequestControlEnum requestControlItemEnum() {
        return RequestControlEnum.STAR_INTERSECTION;
    }
}
