package com.corpgovernment.core.domain.hotelpage.service;

import com.corpgovernment.core.domain.common.model.entity.HotelListMetaData;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelCard;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPage;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPageRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelSort;
import com.corpgovernment.core.domain.hotelpage.model.entity.KeyWordRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.PositionRequest;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControl;
import com.corpgovernment.core.domain.hotelpage.model.entity.TravelControlRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-15 18:53
 */
public interface IHotelPageDomainService {

    /**
     * 处理位置信息
     * @param hotelPageRequest 酒店页面请求
     * @param positionRequest 位置请求
     */
    void handlePosition(HotelPageRequest hotelPageRequest, PositionRequest positionRequest);

    /**
     * 处理关键字
     * @param hotelPageRequest 酒店页面请求
     * @param keyWordRequest 关键字请求
     */
    void handleKeyWord(HotelPageRequest hotelPageRequest, KeyWordRequest keyWordRequest);

    /**
     * 获取酒店页面
     * @param hotelPageRequest 酒店页面请求
     * @param pageIndex 页号
     * @param asyncLoadNextPage 是否异步加载下一页
     * @return 酒店页面
     */
    Map<String, HotelPage> getHotelPage(HotelPageRequest hotelPageRequest, Integer pageIndex, Boolean asyncLoadNextPage);

    /**
     * 聚合酒店卡片列表
     * @param hotelCardListMap 酒店卡片列表Map
     * @param hotelSort 酒店排序
     * @param protocolPriority 是否协议优先
     * @param prioritySupplierList 优先供应商列表
     * @param prePageLastHotelSupplierCode 上一页最后一个酒店供应商Code
     * @return 酒店卡片列表
     */
    List<HotelCard> aggregationHotelCardListMap(Map<String, List<HotelCard>> hotelCardListMap,
                                                HotelSort hotelSort,
                                                Boolean protocolPriority,
                                                List<String> prioritySupplierList,
                                                String prePageLastHotelSupplierCode);

    /**
     * 管控
     * @param hotelPageRequest 酒店页面请求
     * @param travelControlRequestList 管控请求列表
     * @return 管控列表
     */
    List<TravelControl> controlRequest(HotelPageRequest hotelPageRequest, List<TravelControlRequest> travelControlRequestList);

    /**
     * 获取酒店列表元数据
     * @param key 缓存key
     * @param pageIndex 页号
     * @return 酒店列表元数据
     */
    HotelListMetaData getHotelListMetaData(String key, Integer pageIndex);

    /**
     * 设置酒店列表元数据
     * @param key 缓存key
     * @param hotelListMetaData 酒店列表元数据
     * @return 是否成功
     */
    Boolean setHotelListMetaData(String key, HotelListMetaData hotelListMetaData);

    void handleViewedHotel(HotelPage hotelPage);

}
