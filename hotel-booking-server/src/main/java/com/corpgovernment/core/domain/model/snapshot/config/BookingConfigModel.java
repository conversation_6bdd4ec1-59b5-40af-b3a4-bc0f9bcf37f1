package com.corpgovernment.core.domain.model.snapshot.config;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
@Data
public class BookingConfigModel {

    private List<SupplierConfigModel> supplierConfigList;

    private List<PayTypeInfoModel> payInfoList;

    private String urgentPayType;
    
    /**
     * 差旅属性开关
     */
    private AllSwitchModel allSwitch;
}
