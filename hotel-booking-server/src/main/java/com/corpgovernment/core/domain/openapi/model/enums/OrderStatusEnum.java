package com.corpgovernment.core.domain.openapi.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024-08-31 14:24
 */
@AllArgsConstructor
@Getter
public enum OrderStatusEnum {
    
    SI("SI", "提交中", 1),
    PW("PW", "待支付", 2),
    TW("TW", "待确认", 3),
    TA("TA", "已确认", 4),
    CA("CA", "已取消", 5),
    AW("AW", "待审批", 7),
    ED("ED", "已完成", 6);
    
    private final String code;
    private final String name;
    private final Integer node;
    
    public static OrderStatusEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return enumMap.get(key);
    }
    
    private static final Map<String, OrderStatusEnum> enumMap = new HashMap<>();
    
    static {
        for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            enumMap.put(orderStatusEnum.getCode(), orderStatusEnum);
            enumMap.put(orderStatusEnum.getName(), orderStatusEnum);
            enumMap.put(orderStatusEnum.getNode().toString(), orderStatusEnum);
            enumMap.put(orderStatusEnum.name(), orderStatusEnum);
        }
    }
    
}
