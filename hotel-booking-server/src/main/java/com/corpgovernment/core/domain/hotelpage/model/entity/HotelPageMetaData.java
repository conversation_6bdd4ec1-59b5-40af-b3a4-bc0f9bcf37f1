package com.corpgovernment.core.domain.hotelpage.model.entity;

import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/12/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotelPageMetaData {

    // 供应商产品
    private SupplierProduct supplierProduct;
    // 查询条件
    private HotelBaseFilter hotelBaseFilter;
    private HotelPositionFilter hotelPositionFilter;
    private HotelAdvancedFilter hotelAdvancedFilter;
    // 排序
    private HotelSort hotelSort;
    // 距离文案
    private HotelDistanceDesc hotelDistanceDesc;
    // 最后一页
    private Integer lastPageIndex;
    // 海外产线
    private Boolean abroad;
    // 因公因私
    private TravelModeEnum travelMode;
    
    public boolean extractFilterWithServiceCharge() {
        return Optional.ofNullable(hotelAdvancedFilter)
                .map(HotelAdvancedFilter::getFilterWithServiceCharge)
                .orElse(false);
    }

}
