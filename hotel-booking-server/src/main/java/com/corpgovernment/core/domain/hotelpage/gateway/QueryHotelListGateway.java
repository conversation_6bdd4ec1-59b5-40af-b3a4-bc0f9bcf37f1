package com.corpgovernment.core.domain.hotelpage.gateway;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.dto.QueryHotelListCommonRespDto;
import com.corpgovernment.core.dao.dto.QueryHotelListReqDto;
import com.corpgovernment.core.dao.dto.QueryHotelListRespDto;
import com.corpgovernment.core.domain.common.gateway.ICommonGateway;
import com.corpgovernment.core.domain.common.gateway.IHotelIndicatorGateway;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.QueryHotelListRequest;
import com.corpgovernment.core.domain.common.model.entity.SupplierConfig;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.ContractEnum;
import com.corpgovernment.core.domain.common.model.enums.HotelLevelEnum;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import com.corpgovernment.core.domain.common.model.enums.ShuntEnum;
import com.corpgovernment.core.domain.common.service.ICommonDomainService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.MemberTagInfo;
import com.corpgovernment.core.domain.hoteldetail.model.enums.MapTypeEnum;
import com.corpgovernment.core.domain.hotelpage.model.entity.*;
import com.corpgovernment.util.AmountPrecisionUtil;
import com.corpgovernment.core.domain.hotelpage.model.enums.UnAvailableReasonEnum;
import com.corpgovernment.hotel.booking.enums.LanguageEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
@Repository
@Slf4j
public class QueryHotelListGateway implements IQueryHotelListGateway {

    @Resource
    private IHotelIndicatorGateway hotelIndicatorGateway;

    @Resource
    private ICommonGateway commonGateway;

    @Resource
    private ICommonDomainService commonDomainService;

    private static final String SHIELD_OFFICIAL_STAR = "SHIELD_OFFICIAL_STAR";
    private static final String SHIELD_DIAMOND = "SHIELD_DIAMOND";
    @Override
    @BusinessBehaviorMonitor
    public HotelPage execute(QueryHotelListRequest queryHotelListRequest, String callMode, String version, String source) {
        SupplierProduct supplierProduct = Null.or(queryHotelListRequest.getSupplierProduct(), new SupplierProduct());
        QueryHotelListRespDto response = null;
        QueryHotelListReqDto request = null;
        HotelPage hotelPage = null;
        String code = null;
        String msg = null;
        try {
            if (queryHotelListRequest.getPageIndex() == null || queryHotelListRequest.getPageSize() == null
                    || queryHotelListRequest.getSupplierProduct() == null || StringUtils.isBlank(queryHotelListRequest.getSupplierProduct().getSupplierCode())) {
                return null;
            }
            // 上一页是最后一页就不需要调用
            Integer lastPageIndex = queryHotelListRequest.getLastPageIndex();
            if (lastPageIndex != null && queryHotelListRequest.getPageIndex() > lastPageIndex) {
                hotelPage = new HotelPage();
                hotelPage.setHotelCardList(new ArrayList<>(0));
                hotelPage.setLastPage(true);
                return hotelPage;
            }

            // 直连酒店判断
            if (Boolean.TRUE.equals(queryHotelListRequest.getSupplierProduct().getDirectSupplier())) {
                List<String> ctripGroupIdList = Optional.ofNullable(commonDomainService.getSupplierConfig(supplierProduct.getSupplierCode()))
                        .map(SupplierConfig::getCtripGroupIdList)
                        .orElse(new ArrayList<>(0));
                if (CollectionUtils.isEmpty(ctripGroupIdList)) {
                    log.error("直连供应商ctripGroupIdList为空 supplierCode={}", queryHotelListRequest.getSupplierProduct().getSupplierCode());
                    return null;
                }

                List<String> groupIdList = Optional.ofNullable(queryHotelListRequest.getHotelAdvancedFilter())
                        .map(HotelAdvancedFilter::getGroupIdList)
                        .orElse(null);
                if (CollectionUtils.isNotEmpty(groupIdList)) {
                    List<String> filterGroupIdList = ctripGroupIdList.stream().filter(groupIdList::contains).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(filterGroupIdList)) {
                        log.error("用户筛选集团导致无需请求 supplierCode={}", queryHotelListRequest.getSupplierProduct().getSupplierCode());
                        return null;
                    }
                }
            }
            
            // 调用供应商接口
            request = buildQueryHotelListReqDto(queryHotelListRequest);
            response = commonGateway.doPostHttp(
                    supplierProduct.getSupplierCode(),
                    "酒店列表查询",
                    supplierProduct.getUrl(),
                    supplierProduct.getUserKey(),
                    JsonUtils.toJsonString(request),
                    QueryHotelListRespDto.class);

            if (response == null) {
                log.error("调用供应商失败");
                code = HotelCoreConstant.UNKNOWN_STATUS;
                msg = "未知错误";
                return null;
            }

            if (response.getCode() != null || response.getMsg() != null || response.getData() != null) {
                log.info("走新契约");
                code = response.getCode();
                msg = response.getMsg();
                if (response.getCode() == null || !"0".equals(response.getCode()) || response.getData() == null) {
                    return null;
                }
                hotelPage = buildHotelPage(queryHotelListRequest, response.getData());
            } else {
                log.info("走老契约");
                if (response.getStatus() != null) {
                    code = response.getStatus().getErrorCode();
                    msg = response.getStatus().getErrorMessage();
                } else if (response.getErrorCode() != null) {
                    code = response.getErrorCode();
                    msg = response.getMessage();
                }
                if (response.getStatus() != null && !Boolean.TRUE.equals(response.getStatus().getSuccess())) {
                    return null;
                }
                if (response.getErrorCode() != null && !Objects.equals(response.getErrorCode(), "0")){
                    return null;
                }
                hotelPage = buildHotelPage(queryHotelListRequest, response);
            }
            return hotelPage;
        } catch (Exception e) {
            log.error("调用供应商失败", e);
            code = HotelCoreConstant.UNKNOWN_STATUS;
            msg = e.toString();
            return null;
        } finally {
            log.info("酒店页缓存跟踪 供应商数据 version={} supplierCode={} url={}\n request={}\n response={}", version, supplierProduct.getSupplierCode(), supplierProduct.getUrl(), JsonUtils.toJsonString(request), JsonUtils.toJsonString(response));
            hotelIndicatorGateway.execute(code, msg, queryHotelListRequest, response, Optional.ofNullable(hotelPage).map(HotelPage::getLastPage).orElse(null));
        }
    }

    /**
     * 埋点。记录各供应商返回的不可订酒店数量及占比，不可订原因的占比。
     * <p>
     * unavailableReason：Full（满房）；Priceless（无价）；OutOfBusiness（歇业）
     * @param data 查询酒店列表返回数据
     * @param supplierProduct 供应商产品
     */
    private void registry(QueryHotelListCommonRespDto data, SupplierProduct supplierProduct) {
        log.info("the hotel list is unavailable,  QueryHotelListCommonRespDto={} , SupplierProduct={}", JsonUtils.toJsonString(data), JsonUtils.toJsonString(supplierProduct));

        try {

            // 获取不可订酒店信息。unavailableReason：Full（满房）；Priceless（无价）；OutOfBusiness（歇业）
            List<QueryHotelListCommonRespDto.HotelInfo> hotelInfo = data.getHotelInfo();
            if (CollectionUtils.isEmpty(hotelInfo)) {
                log.error("the hotelInfo is empty");
                return;
            }
            List<QueryHotelListCommonRespDto.HotelInfo> unavailableHotels = hotelInfo.stream().filter(f -> f != null && StringUtils.isNotBlank(f.getUnAvailableReason())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(unavailableHotels)) {
                log.error("the unavailableHotels is empty");
                return;
            }

            unavailableHotels.forEach(hotel -> {
                log.info("the hotel is unavailable, hotelId={}, hotelName={}, supplierCode={}, supplierName={}, unavailableReason={}",
                        hotel.getHotelBaseInfo().getHotelId(), hotel.getHotelBaseInfo().getHotelName(), supplierProduct.getSupplierCode(), supplierProduct.getSupplierName(), hotel.getUnAvailableReason());

                // 埋点。记录各供应商返回的不可订酒店数量及占比，不可订原因的占比。
                Metrics.REGISTRY.counter(Metrics.REGISTRY.createId("hotel_unavailable")
                                .withTag("supplierCode", supplierProduct.getSupplierCode())
                                .withTag("supplierName", supplierProduct.getSupplierName())
                                // 不可订原因
                                .withTag("unavailableReason", hotel.getUnAvailableReason())
                        )
                        .increment();
            });

        } catch (Throwable e) {
            // 此处不应该影响主流程，所以捕获异常
            log.error("registry error", e);
        }

    }

    private HotelPage buildHotelPage(QueryHotelListRequest queryHotelListRequest, QueryHotelListCommonRespDto queryHotelListCommonRespDto) {
        if (queryHotelListRequest == null || queryHotelListCommonRespDto == null) {
            return null;
        }

        return HotelPage.builder()
                .lastPage(getLastPage(queryHotelListRequest, queryHotelListCommonRespDto))
                .hotelCardList(buildHotelCardList(queryHotelListRequest, queryHotelListCommonRespDto.getHotelInfo())).build();
    }

    private List<HotelCard> buildHotelCardList(QueryHotelListRequest queryHotelListRequest, List<QueryHotelListCommonRespDto.HotelInfo> hotelInfoList) {
        if (CollectionUtils.isEmpty(hotelInfoList)) {
            return new ArrayList<>(0);
        }

        List<HotelCard> hotelCardList = new ArrayList<>();
        for (QueryHotelListCommonRespDto.HotelInfo hotelInfo : hotelInfoList) {
            if (hotelInfo == null) {
                continue;
            }

            QueryHotelListCommonRespDto.HotelBaseInfo hotelBaseInfo = Null.or(hotelInfo.getHotelBaseInfo(), new QueryHotelListCommonRespDto.HotelBaseInfo());
            Optional<SupplierProduct> optionalSupplierProduct = Optional.ofNullable(queryHotelListRequest).map(QueryHotelListRequest::getSupplierProduct);
            HotelCard distanceInfo = getDistanceInfo(hotelInfo, Optional.ofNullable(queryHotelListRequest).map(QueryHotelListRequest::getHotelDistanceDesc).orElse(null));

            HotelCard hotelCard = HotelCard.builder()
                    .hotelId(hotelBaseInfo.getHotelId())
                    .supplierCode(optionalSupplierProduct.map(SupplierProduct::getSupplierCode).orElse(null))
                    .directSupplier(optionalSupplierProduct.map(SupplierProduct::getDirectSupplier).orElse(null))
                    .supplierName(optionalSupplierProduct.map(SupplierProduct::getSupplierName).orElse(null))
                    .groupId(hotelBaseInfo.getHotelBrandInfo() == null ? null : hotelBaseInfo.getHotelBrandInfo().getGroupId())
                    .name(hotelBaseInfo.getHotelName())
                    .nameEn(hotelBaseInfo.getHotelEnName())
                    .address(hotelBaseInfo.getHotelAddress())
                    .distanceText(distanceInfo == null ? null : distanceInfo.getDistanceText())
                    .distance(distanceInfo == null ? null : distanceInfo.getDistance())
                    .lon(distanceInfo == null ? null : distanceInfo.getLon())
                    .lat(distanceInfo == null ? null : distanceInfo.getLat())
                    .logoUrl(commonDomainService.zoomOutPicture(optionalSupplierProduct.map(SupplierProduct::getSupplierCode).orElse(null), hotelBaseInfo.getHotelLogoUrl()))
                    .star(hotelBaseInfo.getHotelStar())
                    .starLicence(Null.or(hotelBaseInfo.getStarLicence(), false))
                    .levelName(HotelLevelEnum.getNameByStar(hotelBaseInfo.getHotelStar()))
                    .reviewScore(getReviewScore(hotelInfo.getHotelStaticInfo()))
                    .facilityList(hotelInfo.getHotelStaticInfo() == null
                            || hotelInfo.getHotelStaticInfo().getHotelFacilitiesInfo() == null
                            || CollectionUtils.isEmpty(hotelInfo.getHotelStaticInfo().getHotelFacilitiesInfo().getFacilityInfoList())
                            ? null : hotelInfo.getHotelStaticInfo().getHotelFacilitiesInfo().getFacilityInfoList().stream().map(QueryHotelListCommonRespDto.FacilityInfo::getFacilityName).collect(Collectors.toList()))
                    .protocolType(getProtocolType(hotelInfo))
                    .minHotelPrice(buildMinHotelPrice(hotelInfo.getMinPriceRoomInfo(), queryHotelListRequest))
                    .allHotelTagList(getHotelTagList(hotelInfo))
                    .unavailableReason(UnAvailableReasonEnum.getReasonEnum(hotelInfo.getUnAvailableReason()))
                    .build();
            hotelCard.setSupplierList(Collections.singletonList(HotelSupplier.builder()
                    .hotelId(hotelCard.getHotelId())
                    .minHotelPrice(hotelCard.getMinHotelPrice())
                    .supplierCode(hotelCard.getSupplierCode())
                    .supplierName(hotelCard.getSupplierName()).build()));

            if (hotelCard.getMinHotelPrice() == null || !commonGateway.checkCustomPrice(hotelCard.getMinHotelPrice().getAvgPriceIncludeTax())) {
                continue;
            }

            hotelCardList.add(hotelCard);
        }

        return hotelCardList;
    }

    private String getReviewScore(QueryHotelListCommonRespDto.HotelStaticInfo hotelStaticInfo) {
        if (hotelStaticInfo == null || hotelStaticInfo.getHotelReviewInfo() == null) {
            return null;
        }

        String hotelReviewScore = hotelStaticInfo.getHotelReviewInfo().getHotelReviewScore();

        if (StringUtils.isBlank(hotelReviewScore)) {
            return HotelCoreConstant.UNKNOWN_CN;
        }

        try {
            if (Double.parseDouble(hotelReviewScore) == 0) {
                return HotelCoreConstant.UNKNOWN_CN;
            }
        } catch (Exception e) {
            log.error("评分类型转换失败", e);
        }

        return hotelReviewScore;
    }

    private HotelPrice buildMinHotelPrice(QueryHotelListCommonRespDto.MinPriceRoomInfo minPriceRoomInfo, QueryHotelListRequest queryHotelListRequest) {
        if (minPriceRoomInfo == null || queryHotelListRequest == null
                || queryHotelListRequest.getHotelBaseFilter() == null || queryHotelListRequest.getHotelBaseFilter().getRoomDayNum() == null) {
            return null;
        }
        
        BigDecimal minSalePriceIncludeTax = minPriceRoomInfo.getMinSalePriceIncludeTax();
        BigDecimal totalServiceCharge = Optional.ofNullable(minPriceRoomInfo.getServiceChargeInfo())
                .map(QueryHotelListCommonRespDto.MinPriceRoomInfo.ServiceChargeInfo::getCustomChargePrice)
                .orElse(BigDecimal.ZERO);
        BigDecimal avgServiceCharge = Optional.ofNullable(minPriceRoomInfo.getServiceChargeInfo())
                .map(QueryHotelListCommonRespDto.MinPriceRoomInfo.ServiceChargeInfo::getCustomChargePricePerRoomNights)
                .orElse(BigDecimal.ZERO);
        Boolean resourcePriceIncludeServiceCharge = Optional.ofNullable(queryHotelListRequest.getHotelAdvancedFilter())
                .map(HotelAdvancedFilter::getFilterWithServiceCharge)
                .orElse(false);
        
        return HotelPrice.builder()
                .avgPriceIncludeTax(Price.builder()
                        .customCurrency(HotelCoreConstant.CNY)
                        .customPrice(minSalePriceIncludeTax).build())
                .avgExtraTax(buildAvgExtraTax(minPriceRoomInfo.getTaxInfoList(), queryHotelListRequest))
                .avgPriceIncludeTaxAndServiceCharge(minSalePriceIncludeTax == null ? null : Price.builder()
                        .customCurrency(HotelCoreConstant.CNY)
                        .customPrice(minSalePriceIncludeTax.add(avgServiceCharge)).build())
                .avgServiceCharge(Price.builder()
                        .customCurrency(HotelCoreConstant.CNY)
                        .customPrice(avgServiceCharge).build())
                .totalServiceCharge(Price.builder()
                        .customCurrency(HotelCoreConstant.CNY)
                        .customPrice(totalServiceCharge).build())
                .resourcePriceIncludeServiceCharge(resourcePriceIncludeServiceCharge)
                .build();
    }
    
    private Price buildAvgExtraTax(List<QueryHotelListCommonRespDto.MinPriceRoomInfo.TaxInfo> taxInfoList, QueryHotelListRequest queryHotelListRequest) {
        if (CollectionUtils.isEmpty(taxInfoList) || queryHotelListRequest == null || !Boolean.TRUE.equals(queryHotelListRequest.getAbroad())
                || queryHotelListRequest.getHotelBaseFilter() == null || queryHotelListRequest.getHotelBaseFilter().getRoomDayNum() == null) {
            return null;
        }
        
        BigDecimal tmpBigDecimal = BigDecimal.valueOf(queryHotelListRequest.getHotelBaseFilter().getRoomDayNum());

        BigDecimal originExtraTax = BigDecimal.ZERO;
        String originCurrency = null;
        BigDecimal customExtraTax = BigDecimal.ZERO;

        for (QueryHotelListCommonRespDto.MinPriceRoomInfo.TaxInfo taxInfo : taxInfoList) {
            if (taxInfo == null || !Boolean.FALSE.equals(taxInfo.getIncludeInTotalPrice())
                    || taxInfo.getTaxPrice() == null || taxInfo.getTaxPrice().getOriginPriceInfo() == null || taxInfo.getTaxPrice().getOriginPriceInfo().getPrice() == null) {
                continue;
            }
            originExtraTax = originExtraTax.add(taxInfo.getTaxPrice().getOriginPriceInfo().getPrice());
            customExtraTax = customExtraTax.add(taxInfo.getTaxPrice().getCustomPrice());
            originCurrency = taxInfo.getTaxPrice().getOriginPriceInfo().getCurrency();
        }

        return Price.builder()
                .originPrice(AmountPrecisionUtil.divideByBizTypeContext(originExtraTax, tmpBigDecimal))
                .originCurrency(originCurrency)
                .customCurrency(HotelCoreConstant.CNY)
                .customPrice(AmountPrecisionUtil.divideByBizTypeContext(customExtraTax, tmpBigDecimal)).build();
    }

    private HotelCard getDistanceInfo(QueryHotelListCommonRespDto.HotelInfo hotelInfo, HotelDistanceDesc hotelDistanceDesc) {
        if (hotelInfo.getHotelStaticInfo() == null || hotelInfo.getHotelStaticInfo().getHotelGeoInfo() == null) {
            return null;
        }

        String distanceText = "";
        Double lat = null;
        Double lon = null;
        Double distance = null;

        // 距离文案
        QueryHotelListCommonRespDto.HotelGeoInfo hotelGeoInfo = hotelInfo.getHotelStaticInfo().getHotelGeoInfo();
        List<QueryHotelListCommonRespDto.HotelMapInfo> hotelMapInfoList = hotelGeoInfo.getHotelMapInfo();
        if (CollectionUtils.isNotEmpty(hotelMapInfoList)) {
            QueryHotelListCommonRespDto.HotelMapInfo hotelMapInfo = hotelMapInfoList.stream()
                    .filter(item -> item != null && Objects.equals(item.getMapType(), MapTypeEnum.GAODE.getCode()) && checkLatLon(item.getLat(), item.getLon())).findFirst().orElse(null);
            // 兜底用google经纬度 以后做转换
            if (hotelMapInfo == null) {
                hotelMapInfo = hotelMapInfoList.stream()
                        .filter(item -> item != null && Objects.equals(item.getMapType(), MapTypeEnum.GOOGLE.getCode()) && checkLatLon(item.getLat(), item.getLon())).findFirst().orElse(null);
            }
            if (hotelMapInfo != null) {
                lat = hotelMapInfo.getLat();
                lon = hotelMapInfo.getLon();
                if (hotelDistanceDesc != null && checkLatLon(hotelDistanceDesc.getLat(), hotelDistanceDesc.getLon())) {
                    distance = new GeodeticCalculator().calculateGeodeticCurve(
                            Ellipsoid.Sphere,
                            new GlobalCoordinates(hotelDistanceDesc.getLat(), hotelDistanceDesc.getLon()),
                            new GlobalCoordinates(hotelMapInfo.getLat(), hotelMapInfo.getLon())).getEllipsoidalDistance();
                    if (distance >= 1000) {
                        distanceText = "距" + hotelDistanceDesc.getPointDesc() + NumberUtil.roundStr(distance / 1000, 1) + "公里";
                    } else {
                        distanceText = "距" + hotelDistanceDesc.getPointDesc() + NumberUtil.roundStr(distance, 0) + "米";
                    }
                }
            }
        }

        List<QueryHotelListCommonRespDto.IdName> zoneInfoList = Null.or(hotelGeoInfo.getZoneInfoList(), new ArrayList<>(0));
        zoneInfoList = zoneInfoList.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(zoneInfoList)) {
            if (StringUtils.isBlank(distanceText)) {
                distanceText += "近" + zoneInfoList.get(0).getName();
                if (zoneInfoList.size() >= 2) {
                    distanceText += "|近" +  zoneInfoList.get(1).getName();
                }
            } else {
                distanceText += "|近" +  zoneInfoList.get(0).getName();
            }
        }

        return HotelCard.builder()
                .distanceText(distanceText)
                .distance(distance)
                .lon(lon)
                .lat(lat).build();
    }

    private List<MemberTagInfo> getHotelTagList(QueryHotelListCommonRespDto.HotelInfo hotelInfo) {
        if (hotelInfo == null || CollectionUtils.isEmpty(hotelInfo.getHotelTagInfo())) {
            return Collections.emptyList();
        }

        return hotelInfo.getHotelTagInfo().stream()
                .filter(tagInfo -> CollectionUtils.isNotEmpty(tagInfo.getHotelTagList()))
                .flatMap(tagInfo -> tagInfo.getHotelTagList().stream())
                .filter(tagInfo -> StringUtils.isNotBlank(tagInfo.getTagCode()))
                .map(tag -> {
                    MemberTagInfo hotelTagInfo = new MemberTagInfo();
                    hotelTagInfo.setTagCode(tag.getTagCode());
                    hotelTagInfo.setTagName(tag.getName());
                    hotelTagInfo.setTagDesc(tag.getDesc());
                    return hotelTagInfo;
                }).collect(Collectors.toList());
    }

    private Integer getProtocolType(QueryHotelListCommonRespDto.HotelInfo hotelInfo) {
        if (hotelInfo == null || hotelInfo.getHasContractRoom() == null
                || Boolean.FALSE.equals(hotelInfo.getHasContractRoom()) || CollectionUtils.isEmpty(hotelInfo.getHotelTagInfo())) {
            return null;
        }

        Integer protocolType = null;
        for (QueryHotelListCommonRespDto.HotelTagInfo hotelTagInfo : hotelInfo.getHotelTagInfo()) {
            if (hotelTagInfo == null || CollectionUtils.isEmpty(hotelTagInfo.getHotelTagList())) {
                continue;
            }

            for (QueryHotelListCommonRespDto.HotelTag hotelTag : hotelTagInfo.getHotelTagList()) {
                if (hotelTag == null) {
                    continue;
                }
                if (ContractEnum.THREE.getTagCode().equals(hotelTag.getTagCode())) {
                    return 3;
                }
                if (ContractEnum.TWO.getTagCode().equals(hotelTag.getTagCode())) {
                    protocolType = 2;
                }
            }
        }

        return protocolType;
    }

    private Boolean getLastPage(QueryHotelListRequest queryHotelListRequest, QueryHotelListCommonRespDto queryHotelListCommonRespDto) {
        QueryHotelListCommonRespDto.SearchResult searchResult = Optional.ofNullable(queryHotelListCommonRespDto.getSearchResult()).orElse(new QueryHotelListCommonRespDto.SearchResult());

        // 有lastPage标识直接取
        if (searchResult.getLastPage() != null) {
            return searchResult.getLastPage();
        }

        // 没有lastPage标识就用count判断
        if (queryHotelListRequest != null && queryHotelListRequest.getPageSize() != null
                && queryHotelListRequest.getPageIndex() != null && searchResult.getHotelCount() != null) {
            return searchResult.getHotelCount() <= queryHotelListRequest.getPageSize() * queryHotelListRequest.getPageIndex();
        }

        // 兜底逻辑，如果输出酒店为空就是最后一页
        return CollectionUtils.isEmpty(queryHotelListCommonRespDto.getHotelInfo());
    }

    private Boolean checkLatLon(Double lat, Double lon) {
        return lat != null && lon != null && lat != -1 && lon != -1 && lat != 0 && lon != 0;
    }

    private QueryHotelListReqDto buildQueryHotelListReqDto(QueryHotelListRequest queryHotelListRequest) {
        if (queryHotelListRequest == null || queryHotelListRequest.getSupplierProduct() == null) {
            return null;
        }
        SupplierProduct supplierProduct = queryHotelListRequest.getSupplierProduct();

        QueryHotelListReqDto queryHotelListReqDto = QueryHotelListReqDto.builder()
                .baseInfo(QueryHotelListReqDto.BaseInfo.builder()
                        .uid(commonGateway.handleUid(supplierProduct.getUid(), supplierProduct.getSupplierCode()))
                        .corpId(supplierProduct.getCorpId()).language(LanguageEnum.ZH_CN.getCode()).build())
                .hotelFilterInfo(QueryHotelListReqDto.HotelFilterInfo.builder()
                        .hotelFacilitiesFilter(buildHotelFacilitiesFilter(queryHotelListRequest.getHotelAdvancedFilter()))
                        .hotelInfoFilter(buildHotelInfoFilter(queryHotelListRequest.getHotelAdvancedFilter(), supplierProduct))
                        .hotelPositionFilter(buildHotelPositionFilter(queryHotelListRequest.getHotelPositionFilter())).build())
                .roomFilterInfo(QueryHotelListReqDto.RoomFilterInfo.builder()
                        .roomInfoFilter(buildRoomInfoFilter(queryHotelListRequest.getHotelAdvancedFilter()))
                        .roomPolicyFilter(buildRoomPolicyFilter(queryHotelListRequest.getHotelAdvancedFilter()))
                        .roomPriceRange(buildRoomPriceRange(queryHotelListRequest.getHotelAdvancedFilter())).build())
                // todo
                .ExpandStrategy(getExpandStrategyType(queryHotelListRequest.getHotelAdvancedFilter().getChoiceStarList(), queryHotelListRequest.getHotelAdvancedFilter().getControlStarList()))
                .searchBaseInfo(buildSearchBaseInfo(queryHotelListRequest)).build();
        if(!"ctrip".equals(supplierProduct.getSupplierCode())){
            queryHotelListReqDto.setExpandStrategy(null);
        } else {
            Optional.ofNullable(queryHotelListReqDto).map(QueryHotelListReqDto::getHotelFilterInfo).map(QueryHotelListReqDto.HotelFilterInfo::getHotelInfoFilter).ifPresent(t->t.setHotelStar(Collections.emptyList()));
        }
        return queryHotelListReqDto;
    }
    private List<QueryHotelListReqDto.ExpandStrategyType> getExpandStrategyType(List<Integer> choiceStarList, List<Integer> controlStarList){
        List<Integer> allDiamond = IntStream.rangeClosed(1, 5).boxed().collect(Collectors.toList());
        // 去除已选的星级就是未选的星级
        if (CollectionUtils.isNotEmpty(choiceStarList)) {
            allDiamond.removeAll(choiceStarList);
        }else {
            allDiamond = Collections.emptyList();
        }
        List<Integer> needShieldDimondList = allDiamond.stream().distinct().collect(Collectors.toList());
        QueryHotelListReqDto.ExpandStrategyType expandStrategyType =
                QueryHotelListReqDto.ExpandStrategyType.builder().StrategyType(SHIELD_OFFICIAL_STAR).StrategyValue(getAllStarExpectChoice(controlStarList, choiceStarList).stream().map(Object::toString).collect(Collectors.toList())).build();
        QueryHotelListReqDto.ExpandStrategyType diamondExpandStrategyType =
                QueryHotelListReqDto.ExpandStrategyType.builder().StrategyType(SHIELD_DIAMOND).StrategyValue(needShieldDimondList.stream().map(Object::toString).collect(Collectors.toList())).build();
        return Lists.newArrayList(expandStrategyType, diamondExpandStrategyType);
    }

    private List<Integer> getAllStarExpectChoice(List<Integer> controlStarList, List<Integer> choiceStarList){
        if (CollectionUtils.isEmpty(choiceStarList)  && CollectionUtils.isEmpty(controlStarList)) {
            return Collections.emptyList();
        }
        List<Integer> starList;
        if (CollectionUtils.isEmpty(controlStarList)) {
            return IntStream.rangeClosed(1,5).boxed().filter(t -> CollectionUtils.isEmpty(choiceStarList) || !choiceStarList.contains(t)).collect(Collectors.toList());
        } else {
            starList = controlStarList;
        }
        return IntStream.rangeClosed(1,5).boxed().filter(t -> !starList.contains(t)).collect(Collectors.toList());
    }


    private QueryHotelListReqDto.SearchBaseInfo buildSearchBaseInfo(QueryHotelListRequest queryHotelListRequest) {
        if (queryHotelListRequest == null) {
            return new QueryHotelListReqDto.SearchBaseInfo();
        }

        HotelAdvancedFilter hotelAdvancedFilter = queryHotelListRequest.getHotelAdvancedFilter();
        HotelBaseFilter hotelBaseFilter = queryHotelListRequest.getHotelBaseFilter();
        HotelSort hotelSort = queryHotelListRequest.getHotelSort();

        return QueryHotelListReqDto.SearchBaseInfo.builder()
                .hotelIdList(hotelAdvancedFilter == null ? null : hotelAdvancedFilter.getHotelIdList())
                .cityId(hotelBaseFilter == null ? null : hotelBaseFilter.getCityId())
                .checkInDate(hotelBaseFilter == null ? null : hotelBaseFilter.getCheckInDate())
                .checkOutDate(hotelBaseFilter == null ? null : hotelBaseFilter.getCheckOutDate())
                .roomQuantity(hotelBaseFilter == null ? null : hotelBaseFilter.getRoomNum())
                .guestQuantity(hotelBaseFilter == null ? null : hotelBaseFilter.getPersonNum())
                .pagingInfo(QueryHotelListReqDto.PagingInfo.builder()
                        .pageIndex(queryHotelListRequest.getPageIndex())
                        .pageSize(queryHotelListRequest.getPageSize()).build())
                .sortInfo(hotelSort == null ? new QueryHotelListReqDto.SortInfo() : QueryHotelListReqDto.SortInfo.builder()
                        .sortType(hotelSort.getSortType())
                        .sortDirection(hotelSort.getSortDirection()).build())
                .corpPayType(queryHotelListRequest.extractSupplierTravelMode())
                .build();
    }

    private QueryHotelListReqDto.RoomPriceRange buildRoomPriceRange(HotelAdvancedFilter hotelAdvancedFilter) {
        if (hotelAdvancedFilter == null) {
            return new QueryHotelListReqDto.RoomPriceRange();
        }

        return QueryHotelListReqDto.RoomPriceRange.builder()
                .lowPrice(hotelAdvancedFilter.getLowPrice())
                .highPrice(hotelAdvancedFilter.getHighPrice())
                .filterWithExtraPayTax(hotelAdvancedFilter.getPriceFilterIncludeExtraTax()).build();
    }

    private QueryHotelListReqDto.RoomPolicyFilter buildRoomPolicyFilter(HotelAdvancedFilter hotelAdvancedFilter) {
        if (hotelAdvancedFilter == null) {
            return QueryHotelListReqDto.RoomPolicyFilter.builder()
                    .onlyPpRoom(getOnlyPpRoom(null))
                    .build();
        }

        return QueryHotelListReqDto.RoomPolicyFilter.builder()
                .onlyFgRoom(Null.or(hotelAdvancedFilter.getHasFgRoom(), false))
                .onlyPpRoom(getOnlyPpRoom(hotelAdvancedFilter))
                .justifyConfirm(Null.or(hotelAdvancedFilter.getHasJustifyConfirm(), false))
                .hasBreakfast(Null.or(hotelAdvancedFilter.getHasBreakfast(), false))
                .companyAccountPayment(Null.or(hotelAdvancedFilter.getHasCompanyAccountPayment(), false))
                .freeCancel(Null.or(hotelAdvancedFilter.getHasFreeCancel(), false))
                .applicativeAreaInfo(QueryHotelListReqDto.ApplicativeAreaInfo.builder()
                        .foreignGuestsApplicative(Null.or(hotelAdvancedFilter.getApplyForeignGuest(), false))
                        .gatApplicative(Null.or(hotelAdvancedFilter.getApplyGat(), false)).build())
                .onlyBonusPoint(Null.or(hotelAdvancedFilter.getHasHotelBonusPoint(), false))
                .roomType(hotelAdvancedFilter.getRoomType())
                .filterWithServiceCharge(hotelAdvancedFilter.getFilterWithServiceCharge())
                .onlyHourRoom(hotelAdvancedFilter.getHasHourlyRoom())
                .build();
    }

    private Boolean getOnlyPpRoom(HotelAdvancedFilter hotelAdvancedFilter) {
        // 屏蔽现付房型
        if (Boolean.TRUE.equals(commonGateway.openFeature(ShuntEnum.CASH_ROOM_SHIELD.getCode()))) {
            return true;
        }

        return Optional.ofNullable(hotelAdvancedFilter)
                .map(HotelAdvancedFilter::getHasPpRoom)
                .orElse(false);
    }

    private QueryHotelListReqDto.RoomInfoFilter buildRoomInfoFilter(HotelAdvancedFilter hotelAdvancedFilter) {
        if (hotelAdvancedFilter == null) {
            return new QueryHotelListReqDto.RoomInfoFilter();
        }

        return QueryHotelListReqDto.RoomInfoFilter.builder()
                .bedType(hotelAdvancedFilter.getBedType()).build();
    }

    private QueryHotelListReqDto.HotelPositionFilter buildHotelPositionFilter(HotelPositionFilter hotelPositionFilter) {
        if (hotelPositionFilter == null) {
            return new QueryHotelListReqDto.HotelPositionFilter();
        }

        return QueryHotelListReqDto.HotelPositionFilter.builder()
                .districtId(hotelPositionFilter.getDistrictId())
                .metroId(hotelPositionFilter.getMetroId())
                .metroDistance(hotelPositionFilter.getMetroDistance())
                .zoneId(StringUtils.isNotBlank(hotelPositionFilter.getZoneId()) ? Collections.singletonList(hotelPositionFilter.getZoneId()) : null)
                .mapSearchInfo(StringUtils.isBlank(hotelPositionFilter.getRadius())
                        ? null : QueryHotelListReqDto.MapSearchInfo.builder()
                        .lat(hotelPositionFilter.getLatitude())
                        .lon(hotelPositionFilter.getLongitude())
                        .radius(hotelPositionFilter.getRadius())
                        .mapType("GAO_DE").build()).build();
    }

    private QueryHotelListReqDto.HotelInfoFilter buildHotelInfoFilter(HotelAdvancedFilter hotelAdvancedFilter, SupplierProduct supplierProduct) {
        if (hotelAdvancedFilter == null) {
            return QueryHotelListReqDto.HotelInfoFilter.builder()
                    .hotelBrandGroupInfo(QueryHotelListReqDto.HotelBrandGroupInfo.builder()
                            .hotelGroup(getGroupIdList(null, supplierProduct)).build()).build();
        }

        return QueryHotelListReqDto.HotelInfoFilter.builder()
                .onlyViewAgreementHotel(Null.or(hotelAdvancedFilter.getHasContractRoom(), false))
                .hotelBrandGroupInfo(QueryHotelListReqDto.HotelBrandGroupInfo.builder()
                        .hotelBrand(hotelAdvancedFilter.getBrandIdList())
                        .hotelGroup(getGroupIdList(hotelAdvancedFilter.getGroupIdList(), supplierProduct))
                        .hotelBrandFeature(hotelAdvancedFilter.getBrandFeatureList()).build())
                .hotelStar(getStarList(hotelAdvancedFilter.getChoiceStarList(),hotelAdvancedFilter.getControlStarList()))
                .keyword(hotelAdvancedFilter.getKeyword()).build();
    }

    private List<Integer> getStarList(List<Integer> choiceStarList,List<Integer> controlStarList) {
        if (CollectionUtils.isEmpty(controlStarList)){
            return choiceStarList;
        }
        if (CollectionUtils.isEmpty(choiceStarList)){
            choiceStarList = IntStream.rangeClosed(1, 5).boxed().collect(Collectors.toList());
        }
        return choiceStarList.stream().filter(t -> controlStarList.contains(t)).collect(Collectors.toList());
    }


    private List<String> getGroupIdList(List<String> groupIdList, SupplierProduct supplierProduct) {
        // 直连逻辑处理
        if (supplierProduct != null && Boolean.TRUE.equals(supplierProduct.getDirectSupplier())) {
            List<String> ctripGroupIdList = Optional.ofNullable(commonDomainService.getSupplierConfig(supplierProduct.getSupplierCode()))
                    .map(SupplierConfig::getCtripGroupIdList)
                    .orElse(new ArrayList<>(0));
            if (CollectionUtils.isEmpty(groupIdList)) {
                return ctripGroupIdList;
            }

            List<String> filterGroupIdList = ctripGroupIdList.stream().filter(groupIdList::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterGroupIdList)) {
                return filterGroupIdList;
            }

            return ctripGroupIdList;
        }

        return groupIdList;
    }

    private QueryHotelListReqDto.HotelFacilitiesFilter buildHotelFacilitiesFilter(HotelAdvancedFilter hotelAdvancedFilter) {
        if (hotelAdvancedFilter == null) {
            return new QueryHotelListReqDto.HotelFacilitiesFilter();
        }

        return QueryHotelListReqDto.HotelFacilitiesFilter.builder()
                .hasAirportShuttle(hotelAdvancedFilter.getHasAirportShuttle())
                .hasFitnessCenter(hotelAdvancedFilter.getHasFitnessCenter())
                .hasSwimmingPool(hotelAdvancedFilter.getHasSwimmingPool())
                .hasParking(hotelAdvancedFilter.getHasParking())
                .hasAirportPickup(hotelAdvancedFilter.getHasAirportPickup())
                .sPA(hotelAdvancedFilter.getHasSpa())
                .freeWirelessBroadband(hotelAdvancedFilter.getHasFreeWifi())
                .freeWiredBroadband(hotelAdvancedFilter.getHasFreeWiredBroadband()).build();
    }

}
