package com.corpgovernment.core.domain.hoteldetail.gateway;

import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPrice;
import com.corpgovernment.core.domain.hoteldetail.model.entity.BasicRoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelBonusPoint;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomSupplier;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public interface IHotelDetailGateway {

    void setHotelDetail(String key, HotelDetail hotelDetail);

    HotelDetail getHotelDetail(String key);

    Map<String, HotelBonusPoint> getHotelBonusPointInfoMap();

    void getCtripRoomRelationMap(Map<RoomSupplier, Set<RoomSupplier>> allRoomRelationMap, Map<String, List<String>> hotelIdListMap);

    void getSelfRoomRelationMap(Map<RoomSupplier, Set<RoomSupplier>> allRoomRelationMap, List<BasicRoomCard> basicRoomCardList);

    void handleBasicRoomCardAggAttr(BasicRoomCard basicRoomCard);

    void handleHotelDetailAggAttr(HotelDetail hotelDetail);

    RoomPrice getMinRoomPrice(RoomPrice roomPrice1, RoomPrice roomPrice2);

    Integer getProtocolType(Integer protocolType1, Integer protocolType2);

    Boolean verifyTravelStandard(HotelDetail hotelDetail,
                                 String token,
                                 String travelStandardMark,
                                 BizTypeEnum bizTypeEnum,
                                 BigDecimal roomNightNum,
                                 List<String> paymentMethodList,
                                 Boolean overseasHotelControlIncludeExtraTax,
                                 PriceControlStrategyEnum priceControlStrategyEnum);
    
    HotelDetail verifyApplyTrip(HotelDetail hotelDetail, String travelId, String productType);

    void getDirectRoomRelationMap(Map<RoomSupplier, Set<RoomSupplier>> allRoomRelationMap, List<BasicRoomCard> basicRoomCardList);
}
