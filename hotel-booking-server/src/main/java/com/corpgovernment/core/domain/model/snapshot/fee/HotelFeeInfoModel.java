package com.corpgovernment.core.domain.model.snapshot.fee;


import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.MealTypeEnum;
import com.corpgovernment.core.domain.model.SupplierCheckAvailResultModel;
import com.corpgovernment.core.domain.model.snapshot.config.PayTypeInfoModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
@Data
public class HotelFeeInfoModel {
    /**
     * 每日房价信息
     */
    private List<RoomDailyPriceModel> roomDailyPriceTypeList;
    
    /**
     * 前收服务费
     */
    private List<ServiceChargeInfo> serviceChargeInfoList;
    
    /**
     * 税费信息
     */
    private TaxInfoModel taxInfo;
    /**
     * 时间信息(取消修改时间/最早到店时间/最晚到店时间/最晚保留时间
     */
    private TimeInformationModel timeInformationType;
    /**
     * 时间信息(取消修改时间/最早到店时间/最晚到店时间/最晚保留时间
     */
    private HotelLimitInformationModel hotelLimitInformationType;

    /**
     * 取消惩罚
     */
    private CancelPenaltyModel cancelPenalties;

    /**
     * 酒店阶梯扣款政策
     */
    private List<LadderDeductionInfoModel> ladderDeductionInfoList;
    /**
     * 发票信息
     */
    private InvoiceInfoModel invoiceInfo;
    /**
     * 积分信息
     */
    private BonusPointInfoModel bonusPointInfo;
    /**
     * 备注信息
     */
    private RemarkInfoModel remarkInfo;

    /**
     * 供应商下单透传额外信息
     */

    private List<String> additionalSupplierInfo;
    /**
     * 支付方式
     */
    private List<PayTypeInfoModel> payTypeList;

    /**
     * 紧急预订支付方式
     */
    private String urgentPayType;

    /**
     * 是否紧急预订
     */
    private Boolean urgentBooking;
    
    /**
     * 变化
     */
    private ChangeInfoModel changeInfo;
    
    /**
     * 前一次超标结果
     */
    private Boolean lastOverLimit;
    
    /**
     * 供应商订单总价
     */
    private PriceInfoModel supplierTotalPrice;

    /**
     * 餐食数量
     */
    private Integer meals;
    
    /**
     * 餐食类型
     */
    private MealTypeEnum mealTypeEnum;

    private BookingRules bookingRules;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ServiceChargeInfo {
        
        private PayTypeEnum payTypeEnum;
        
        private BigDecimal totalServiceCharge;
        
        private ServiceChargeStrategyEnum serviceChargeStrategyEnum;
        
        private BigDecimal serviceChargeStrategyValue;
        
        private BigDecimal avgServiceCharge;
        
    }
    
    @Data
    public static class BookingRules {
        /**
         * 确认规则
         */
        private ConfirmRules confirmRules;

        /**
         * 可订客人信息
         */
        private BillingGuestInfo billingGuestInfo;

        /**
         * 证件信息(1:需要; 0:不需要)
         */
        private CertificateInfo certificateInfo;
        
        /**
         * 最少入住天数
         */
        private Integer minLOS;
    }
    @Data
    public static class ConfirmRules{
        private Boolean justifyConfirm;
    }

    /**
     * 可订客人信息
     */
    @Data
    public static class BillingGuestInfo{
        private List<String> guestsNameLanguages;
        /**
         * 是否需要邮箱
         */
        private Boolean needEmail;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CertificateInfo{
        /**
         * 是否需要证件(1:需要; 0:不需要)
         */
        private Boolean needCertificate;
        /**
         * 证件类型（1:身份证; 2:护照; 3:学生证; 4:军人证; 6:驾驶证; 7:回乡证; 8:台胞证; 10:港澳通行证; 11:国际海员证; 21:旅行证; 22:台湾通行证; 25:户口簿; 27:出生证明; 28:外国人永久居留身份证; 32:港澳台居民居住证; 99:其他; 0:未知）
         */
        private String supportCertificateType;
        
        private List<String> supportCertificateTypeList;
    }
}
