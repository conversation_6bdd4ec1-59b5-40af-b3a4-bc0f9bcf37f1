package com.corpgovernment.core.domain.hoteldetail.model.entity;

import com.corpgovernment.core.domain.hoteldetail.model.enums.BalanceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RoomCard {

    private String hotelId;
    private String basicRoomId;
    private String virtualBasicRoomId;
    private String roomId;
    private String productId;

    private String groupId;
    private String brandId;

    private City city;
    private District district;
    private List<Zone> zoneList;

    private String supplierCode;
    private String supplierName;
    private Boolean directSupplier;
    private Boolean canReserve;
    private Integer protocolType;
    private BalanceTypeEnum balanceTypeEnum;
    private RoomBaseInfo roomBaseInfo;
    private List<FacilityGroup> facilityGroupList;
    private Integer star;
    private Boolean starLicence;
    private RoomPackage roomPackage;
    private RoomPolicyService roomPolicyService;
    private RoomPrice roomPrice;
    private OverLimitInfo overLimitInfo;
    private ApplyTripOverLimitReminder applyTripOverLimitReminder;
    private RoomControl roomControl;
    // 所有会员标签列表
    private List<MemberTagInfo> allRoomTagList;

    private PersonPrice personPrice;
    
    private HourlyRoomInfo hourlyRoomInfo;


}
