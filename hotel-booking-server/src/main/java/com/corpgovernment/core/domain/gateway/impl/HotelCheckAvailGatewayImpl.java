package com.corpgovernment.core.domain.gateway.impl;

import com.corpgovernment.api.hotel.product.model.checkavail.request.LocalCheckAvailRequestBo;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.common.utils.DateUtil;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.core.dao.dataobject.HotelBonusPointInfoDo;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import com.corpgovernment.core.domain.enums.SupplierPayTypeEnum;
import com.corpgovernment.core.domain.gateway.HotelCheckAvailGateway;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HourlyRoomInfo;
import com.corpgovernment.core.domain.hoteldetail.model.enums.MealTypeEnum;
import com.corpgovernment.core.domain.model.SupplierCheckAvailResultModel;
import com.corpgovernment.core.domain.model.snapshot.product.PersonPriceInfo;
import com.corpgovernment.core.domain.model.snapshot.product.PriceInfoModel;
import com.corpgovernment.core.service.impl.HotelBonusPointService;
import com.corpgovernment.hotel.booking.enums.AvailableVatInvoiceTypeEnum;
import com.corpgovernment.hotel.booking.enums.LanguageEnum;
import com.corpgovernment.hotel.product.model.ctrip.v2.BaseEntity;
import com.corpgovernment.hotel.product.model.ctrip.v2.CheckAvailV2RequestType;
import com.corpgovernment.hotel.product.model.ctrip.v2.CheckAvailV2ResponseType;
import com.corpgovernment.hotel.product.model.ctrip.v2.CheckRoomEntityV2;
import com.corpgovernment.hotel.product.model.ctrip.v2.LadderDeductionDetailEntityV2;
import com.corpgovernment.hotel.product.model.ctrip.v2.MultipleLanguageText;
import com.corpgovernment.hotel.product.model.ctrip.v2.ResponseStatus;
import com.corpgovernment.hotel.product.model.ctrip.v2.RoomItemV2;
import com.corpgovernment.hotel.product.model.ctrip.wrap.WrapCheckAvailV2ResponseType;
import com.corpgovernment.hotel.product.supplier.CommonService;
import com.corpgovernment.hotel.product.supplier.enums.InvoiceEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/25
 */
@Component
@Slf4j
public class HotelCheckAvailGatewayImpl implements HotelCheckAvailGateway {
    @Autowired
    private CommonService commonService;
    @Autowired
    private HotelBonusPointService hotelBonusPointService;
    @Override
    public SupplierCheckAvailResultModel checkAvail(LocalCheckAvailRequestBo request, SupplierProductBo supplierProduct) {
        try {
            if (request == null || supplierProduct == null) {
                return null;
            }
            CheckAvailV2RequestType requestBo = this.convertCheckAvailRequest(request, supplierProduct);
            String productUrl = supplierProduct.getProductUrl();
            String supplierCode = supplierProduct.getSupplierCode();
            String userKey = supplierProduct.getUserKey();
            String json = commonService.doPostJSON(supplierCode, "可订查询", productUrl, userKey, JsonUtils.toJsonString(requestBo));

            WrapCheckAvailV2ResponseType response = JsonUtils.parse(json, WrapCheckAvailV2ResponseType.class);
            if (response != null && (response.getData() != null || response.getCode() != null || response.getMsg() != null)) {
                log.info("走新契约");
                return convertCheckAvailResponseNew(response, request);
            }
            else {
                log.info("走旧契约");
                return this.convertCheckAvailResponse(response, request);
            }
        } catch (Exception e) {
            log.warn("获取供应商可定查询数据失败", e);
        }
        return null;
    }
    /**
     * 获取供应商可订查询request
     *
     * @param requestBo 本地可订查询入参
     * @return 供应商可订查询request
     */
    private CheckAvailV2RequestType convertCheckAvailRequest(LocalCheckAvailRequestBo requestBo,
                                                             SupplierProductBo supplierProduct) {
        if (requestBo == null) {
            return null;
        }
        CheckAvailV2RequestType request = new CheckAvailV2RequestType();
        BaseEntity baseInfo = new BaseEntity();
        if (StringUtils.equalsIgnoreCase(supplierProduct.getSupplierCode(), "ctrip")) {
            baseInfo.setUid(supplierProduct.getSupplierUid());
        } else {
            baseInfo.setUid(StringUtils.isBlank(supplierProduct.getSupplierUid()) ? null : supplierProduct.getSupplierUid());
        }
        baseInfo.setCorpId(supplierProduct.getSupplierCorpId());
        baseInfo.setLanguage(LanguageEnum.ZH_CN.getCode());
        request.setBaseInfo(baseInfo);
        CheckRoomEntityV2 roomInfo = new CheckRoomEntityV2();
        Optional.ofNullable(requestBo.getRoomInfo()).ifPresent(e -> {
            roomInfo.setProductId(e.getProductId());
            roomInfo.setCheckInDate(e.getCheckInDate());
            roomInfo.setCheckOutDate(e.getCheckOutDate());
            roomInfo.setQuantity(e.getQuantity());
            roomInfo.setGuestPerson(e.getGuestPerson());
            roomInfo.setRoomId(e.getRoomId());
            // 因公因私标识
            roomInfo.setFeeType(e.getFeeType());

            CheckRoomEntityV2.PersonPrice personPrice=new CheckRoomEntityV2.PersonPrice();
            personPrice.setAdult(e.getAdult());
            personPrice.setRateId(e.getRateId());
            roomInfo.setPersonPrice(personPrice);
        });
        request.setRoomInfo(roomInfo);
        return request;
    }
    private SupplierCheckAvailResultModel convertCheckAvailResponseNew(WrapCheckAvailV2ResponseType response, LocalCheckAvailRequestBo requestBo) {
        if (response == null) {
            return null;
        }
        log.info("CommonSupplierLoader.convertCheckAvailResponse, CheckAvailV2ResponseType:{}", JsonUtils.toJsonString(response));
        SupplierCheckAvailResultModel responseBo = new SupplierCheckAvailResultModel();

        // 状态码校验
        // 可订检查不通过
        String code = response.getCode();
        CheckAvailV2ResponseType data = response.getData();
        if (!"0".equals(code) || data == null) {
            responseBo.setSuccess(false);
            responseBo.setFailedReason(response.getMsg());
            responseBo.setCheckCode("2");
            if (Arrays.asList("10812023", "10812030", "10812032", "10812035", "10812036").contains(code)) {
                responseBo.setFailedReason(response.getMsg());
            } else {
                responseBo.setFailedReason(null);
            }
            log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情:{}", JsonUtils.toJsonString(responseBo));
            return responseBo;
        }

        responseBo.setSuccess(true);
        responseBo.setFailedReason(Optional.ofNullable(data.getFailedReason()).map(MultipleLanguageText::getTextGB).orElse(null));
        responseBo.setRoomInfo(this.roomInfo(data, requestBo));
        responseBo.setRoomDailyInfoList(this.roomDailyInfo(data));
        responseBo.setChangePrice(data.getChangePrice());
        responseBo.setChangeCancelPolicy(data.getChangeCancelPolicy());
        responseBo.setChangePriceDetailList(this.changePriceDetail(data));
        responseBo.setBookingRules(getBookingRules(data));
        responseBo.setTaxInfo(getTaxInfo(data));
        log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情:{}", JsonUtils.toJsonString(responseBo));
        return responseBo;
    }
    private SupplierCheckAvailResultModel convertCheckAvailResponse(CheckAvailV2ResponseType response,
                                                                LocalCheckAvailRequestBo requestBo) {
        if (response == null) {
            return null;
        }
        log.info("CommonSupplierLoader.convertCheckAvailResponse, CheckAvailV2ResponseType:{}", JsonUtils.toJsonString(response));
        SupplierCheckAvailResultModel responseBo = new SupplierCheckAvailResultModel();

        if (Objects.nonNull(response.getSuccess()) && !response.getSuccess()) {
            responseBo.setSuccess(response.getSuccess());
            responseBo.setFailedReason(response.getErrorMessage());
            if(Objects.isNull(response.getStatus()) || Objects.isNull(response.getStatus().getErrorCode())) {
                log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情1:{}", JsonUtils.toJsonString(responseBo));
                return responseBo;
            }
            // 返回的错误码为10812023、10812030、10812032、10812035、10812036，用户点击【确定】按钮则返回酒店详情页并刷新
            // 返回其他错误码，用户点击【确定】按钮则返回酒店填单页无需刷新
            if(response.getStatus().getErrorCode() == 10812023 || response.getStatus().getErrorCode() == 10812030 ||
                    response.getStatus().getErrorCode() == 10812032 || response.getStatus().getErrorCode() == 10812035 ||
                    response.getStatus().getErrorCode() == 10812036 ) {
                responseBo.setCheckCode("2");
                responseBo.setFailedReason(Optional.ofNullable(response.getStatus()).map(ResponseStatus::getErrorMessage).orElse(null));
            } else if(response.getStatus().getErrorCode() != 0){
                responseBo.setCheckCode("2");
                responseBo.setFailedReason(null);
            }

            log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情2:{}", JsonUtils.toJsonString(responseBo));
            return responseBo;
        }
        
        String checkInDate = Optional.ofNullable(requestBo)
                .map(LocalCheckAvailRequestBo::getRoomInfo)
                .map(LocalCheckAvailRequestBo.CheckRoomInfo::getCheckInDate)
                .orElse(null);
        
        responseBo.setSuccess(Optional.ofNullable(response.getStatus()).map(ResponseStatus::getSuccess).orElse(false));
        responseBo.setFailedReason(
                Optional.ofNullable(response.getFailedReason()).map(MultipleLanguageText::getTextGB).orElse(null));
        responseBo.setRoomInfo(this.roomInfo(response, requestBo));
        responseBo.setRoomDailyInfoList(this.roomDailyInfo(response));
        responseBo.setChangePrice(response.getChangePrice());
        responseBo.setChangeCancelPolicy(response.getChangeCancelPolicy());
        responseBo.setChangePriceDetailList(this.changePriceDetail(response));
        responseBo.setTaxInfo(getTaxInfo(response));
        responseBo.setBookingRules(getBookingRules(response));
        responseBo.setServiceChargeInfoList(convertServiceChargeInfoList(response.getRoomInfo()));
        responseBo.setHourlyRoomInfo(convertHourlyRoomInfo(response.getRoomInfo(), checkInDate));
        if(responseBo.getSuccess() || Objects.isNull(response.getStatus()) || Objects.isNull(response.getStatus().getErrorCode())) {
            log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情3:{}", JsonUtils.toJsonString(responseBo));
            return responseBo;
        }
        // 返回的错误码为10812023、10812030、10812032、10812035、10812036，用户点击【确定】按钮则返回酒店详情页并刷新
        // 返回其他错误码，用户点击【确定】按钮则返回酒店填单页无需刷新
        if(response.getStatus().getErrorCode() == 10812023 || response.getStatus().getErrorCode() == 10812030 ||
                response.getStatus().getErrorCode() == 10812032 || response.getStatus().getErrorCode() == 10812035 ||
                response.getStatus().getErrorCode() == 10812036 ) {
            responseBo.setCheckCode("2");
            responseBo.setFailedReason(Optional.ofNullable(response.getStatus()).map(ResponseStatus::getErrorMessage).orElse(null));
        }
        log.info("CommonSupplierLoader.convertCheckAvailResponse, 封装后的酒店详情4:{}", JsonUtils.toJsonString(responseBo));
        return responseBo;
    }
    
    private HourlyRoomInfo convertHourlyRoomInfo(RoomItemV2 roomInfo, String checkInDate) {
        if (roomInfo == null) {
            return null;
        }
        
        HourlyRoomInfo hourlyRoomInfo = new HourlyRoomInfo();
        hourlyRoomInfo.setHourlyRoom(roomInfo.getHourlyRoom());
        hourlyRoomInfo.setCheckInDate(checkInDate);
        RoomItemV2.HourRoomDetail hourRoomDetail = roomInfo.getHourRoomDetail();
        if (hourRoomDetail != null) {
            hourlyRoomInfo.setDurationHour(hourRoomDetail.getDuration());
            hourlyRoomInfo.setIntervalStartMinute(hourRoomDetail.getEarliestArriveTime());
            hourlyRoomInfo.setIntervalEndMinute(hourRoomDetail.getLatestLeaveTime());
            hourlyRoomInfo.generateDesc();
            hourlyRoomInfo.generateAvailableTimeSlots();
        }
        return hourlyRoomInfo;
    }
    
    private List<SupplierCheckAvailResultModel.ServiceChargeInfo> convertServiceChargeInfoList(RoomItemV2 roomInfo) {
        if (roomInfo == null || CollectionUtils.isEmpty(roomInfo.getServiceChargeInfoList())) {
            return null;
        }
        
        return roomInfo.getServiceChargeInfoList().stream()
                .map(this::convertServiceChargeInfo)
                .filter(item -> item != null && item.getPayTypeEnum() != null)
                .collect(Collectors.toList());
    }
    
    private SupplierCheckAvailResultModel.ServiceChargeInfo convertServiceChargeInfo(RoomItemV2.ServiceChargeInfo item) {
        if (item == null) {
            return null;
        }
        
        SupplierCheckAvailResultModel.ServiceChargeInfo.ServiceChargeInfoBuilder serviceChargeInfoBuilder = SupplierCheckAvailResultModel.ServiceChargeInfo.builder();
        
        SupplierPayTypeEnum supplierPayTypeEnum = SupplierPayTypeEnum.getEnum(item.getRoomPaymentMethod());
        if (supplierPayTypeEnum != null) {
            serviceChargeInfoBuilder.payTypeEnum(supplierPayTypeEnum.getPayTypeEnum());
        }
        
        if (item.getCustomChargePrice() != null) {
            serviceChargeInfoBuilder.totalServiceCharge(item.getCustomChargePrice().getPrice());
        }
        
        if (item.getCustomChargePricePerRoomNights() != null) {
            serviceChargeInfoBuilder.avgServiceCharge(item.getCustomChargePricePerRoomNights().getPrice());
        }
        
        RoomItemV2.ServiceChargeDetailInfo serviceChargeDetailInfo = getServiceChargeDetailInfo(item);
        
        if (serviceChargeDetailInfo != null) {
            ServiceChargeStrategyEnum serviceChargeStrategyEnum = ServiceChargeStrategyEnum.getEnum(serviceChargeDetailInfo.getChargingStrategy());
            serviceChargeInfoBuilder.serviceChargeStrategyEnum(serviceChargeStrategyEnum);
            
            RoomItemV2.Price customChargePricePerUnit = serviceChargeDetailInfo.getCustomChargePricePerUnit();
            if (customChargePricePerUnit != null && !Objects.equals(serviceChargeStrategyEnum, ServiceChargeStrategyEnum.FIXED_RATIO)) {
                serviceChargeInfoBuilder.serviceChargeStrategyValue(customChargePricePerUnit.getPrice());
            }
        }
        
        return serviceChargeInfoBuilder.build();
    }
    
    private RoomItemV2.ServiceChargeDetailInfo getServiceChargeDetailInfo(RoomItemV2.ServiceChargeInfo serviceChargeInfo) {
        if (serviceChargeInfo == null || CollectionUtils.isEmpty(serviceChargeInfo.getServiceChargeDetailInfoList())) {
            return null;
        }
        
        // 复杂明细直接跳过
        if (serviceChargeInfo.getServiceChargeDetailInfoList().size() > 1) {
            return null;
        }
        
        // 仅支持普通类型
        RoomItemV2.ServiceChargeDetailInfo serviceChargeDetailInfo = serviceChargeInfo.getServiceChargeDetailInfoList().get(0);
        if (serviceChargeDetailInfo == null || !StringUtils.equalsIgnoreCase(serviceChargeDetailInfo.getChargeType(), "ORDINARY")) {
            return null;
        }
        
        return serviceChargeDetailInfo;
    }
    
    /**
     * 获取可订房间信息
     *
     * @param responseBo 可定接口返回
     * @return 可订接口房间信息
     */
    private SupplierCheckAvailResultModel.RoomItem roomInfo(CheckAvailV2ResponseType responseBo, LocalCheckAvailRequestBo requestBo) {
        RoomItemV2 roomInfo = responseBo.getRoomInfo();
        if (roomInfo == null) {
            return null;
        }
        SupplierCheckAvailResultModel.RoomItem roomItem = new SupplierCheckAvailResultModel.RoomItem();
        Optional.ofNullable(roomInfo.getTimeInformation()).ifPresent(e -> {
            roomItem.setLastCancelTime(e.getLastCancelTime());
            roomItem.setEarlyArrivalTime(e.getEarlyArrivalTime());
            roomItem.setLastArrivalTime(e.getLastArrivalTime());
            roomItem.setHoldTime(e.getHoldTime());
        });
        Optional.ofNullable(roomInfo.getPriceInformation()).ifPresent(e -> {
            roomItem.setOriginAmount(e.getSellPrice());
            roomItem.setCnyAmount(e.getSellPrice());
            roomItem.setCustomAmount(e.getSellPrice());
        });
        Optional.ofNullable(roomInfo.getLimitInformation()).ifPresent(e -> {
            roomItem.setGuestPerson(e.getGuestPerson());
            roomItem.setMinBookingRoomNum(e.getMinBookingRoomNum());
            roomItem.setMaxBookingRoomNum(e.getMaxBookingRoomNum());
        });
        Optional.ofNullable(roomInfo.getCancelPenalties()).ifPresent(e -> {
            SupplierCheckAvailResultModel.CancelPenaltyModel cancelPenaltyModel =
                    new SupplierCheckAvailResultModel.CancelPenaltyModel();
            cancelPenaltyModel.setPolicyType(e.getPolicyType());
            cancelPenaltyModel.setFreeCancelPolicySceneType(e.getFreeCancelPolicySceneType());
            roomItem.setCancelPenalties(cancelPenaltyModel);

        });
        List<SupplierCheckAvailResultModel.Remark> remarkList =
                Optional.ofNullable(roomInfo.getRemarkList()).orElse(new ArrayList<>()).stream().map(e -> {
                    if (e == null) {
                        return null;
                    }
                    SupplierCheckAvailResultModel.Remark remark = new SupplierCheckAvailResultModel.Remark();
                    remark.setKey(e.getKey());
                    remark.setId(e.getId());
                    remark.setTitle(e.getTitle());
                    remark.setDesc(e.getDesc());
                    remark.setUnique(e.getUnique());
                    remark.setNeedUserValue(e.getNeedUserValue());
                    remark.setDefaultOption(e.getDefaultOption());
                    return remark;
                }).filter(Objects::nonNull).collect(Collectors.toList());
        roomItem.setRemarkList(remarkList);
        roomItem.setReceiveTextRemark(roomInfo.getReceiveTextRemark());
        roomItem.setSpecialTipList(roomInfo.getSpecialTipList());
        List<SupplierCheckAvailResultModel.LadderDeductionInfo> ladderDeductionInfoList =
                Optional.ofNullable(roomInfo.getLadderDeductionInfoList()).orElse(new ArrayList<>()).stream().map(e -> {
                    if (e == null) {
                        return null;
                    }
                    SupplierCheckAvailResultModel.LadderDeductionInfo ladder = new SupplierCheckAvailResultModel.LadderDeductionInfo();
                    ladder.setDeductionType(e.getDeductionType());
                    LadderDeductionDetailEntityV2 ladderDeductionInfo = e.getLadderDeductionInfo();
                    SupplierCheckAvailResultModel.LadderDeductionDetail detail = new SupplierCheckAvailResultModel.LadderDeductionDetail();
                    if (ladderDeductionInfo != null) {
                        detail.setStartDeductTime(
                                DateUtil.stringToDate(ladderDeductionInfo.getStartDeductTime(), DateUtil.DF_YMD_HM));
                        detail.setEndDeductTime(
                                DateUtil.stringToDate(ladderDeductionInfo.getEndDeductTime(), DateUtil.DF_YMD_HM));
                        detail.setDeductionRatio(ladderDeductionInfo.getDeductionRatio());
                        detail.setAmount(ladderDeductionInfo.getAmount());
                        detail.setCurrency(ladderDeductionInfo.getCurrency());
                        detail.setOriginalAmount(ladderDeductionInfo.getOriginalAmount());
                        detail.setOriginalCurrency(ladderDeductionInfo.getOriginalCurrency());
                        ladder.setLadderDeductionInfo(detail);
                    }
                    return ladder;
                }).filter(Objects::nonNull).collect(Collectors.toList());
        roomItem.setLadderDeductionInfoList(ladderDeductionInfoList);
        roomItem.setAvailableVatInvoiceType(
                AvailableVatInvoiceTypeEnum.getByType(roomInfo.getAvailableVATInvoiceType()).getType());
        // 酒店积分
        String supplierCode = Optional.ofNullable(requestBo).map(LocalCheckAvailRequestBo::getBaseInfo).map(LocalCheckAvailRequestBo.CheckBaseInfo::getSupplierCode).orElse("");
        String groupId = Optional.ofNullable(requestBo).map(LocalCheckAvailRequestBo::getGroupId).orElse("");
        String bonusPointCode = Optional.ofNullable(roomInfo.getBonusPointInfo()).map(RoomItemV2.BonusPointInfo::getBonusPointCode).orElse("");
        HotelBonusPointInfoDo hotelBonusPointInfoDo = hotelBonusPointService.getHotelBonusPointInfo(supplierCode, groupId, bonusPointCode);
        log.info("获取酒店积分信息 supplierCode={} groupId={} bonusPointCode={} hotelBonusPointInfo={}", supplierCode, groupId, bonusPointCode, hotelBonusPointInfoDo);
        // 填充积分
        if (hotelBonusPointInfoDo != null) {
            SupplierCheckAvailResultModel.BonusPointInfo bonusPointInfo = convert(hotelBonusPointInfoDo, bonusPointCode, groupId, supplierCode);
            roomItem.setBonusPointInfo(bonusPointInfo);
            log.info("酒店积分填充 bonusPointInfo={}", bonusPointInfo);
        }
        // 支持的发票类型列表 需要兼容老字段
        Set<String> supportInvoiceTypeSet = new HashSet<>();
        String availableVATInvoiceType = roomInfo.getAvailableVATInvoiceType();
        if (StringUtils.equalsIgnoreCase(availableVATInvoiceType, "Special")) {
            supportInvoiceTypeSet.add(InvoiceEnum.DVatInvoice.getCode());
            supportInvoiceTypeSet.add(InvoiceEnum.DInvoice.getCode());
        } else if (StringUtils.equalsIgnoreCase(availableVATInvoiceType, "Ordinary")) {
            supportInvoiceTypeSet.add(InvoiceEnum.DInvoice.getCode());
        }
        List<String> supportInvoiceTypeList = roomInfo.getSupportInvoiceTypeList();
        if (CollectionUtils.isNotEmpty(supportInvoiceTypeList)) {
            supportInvoiceTypeSet.addAll(supportInvoiceTypeList);
        }
        roomItem.setSupportInvoiceTypeList(new ArrayList<>(supportInvoiceTypeSet));
        // 供应商下单透传额外信息
        roomItem.setAdditionalSupplierInfo(roomInfo.getAdditionalSupplierInfo());
        // 特惠房型信息
        SupplierCheckAvailResultModel.SpecialOfferRoomInfo specialOfferRoomInfo = new SupplierCheckAvailResultModel.SpecialOfferRoomInfo();
        specialOfferRoomInfo.setSupportCertificateType(Optional.ofNullable(roomInfo.getSpecialOfferRoomInfo()).map(RoomItemV2.SpecialOfferRoomInfo::getSupportCertificateType).orElse(null));
        roomItem.setSpecialOfferRoomInfo(specialOfferRoomInfo);
        roomItem.setMealTypeEnum(Optional.ofNullable(roomInfo.getRoomMealInfo())
                .map(RoomItemV2.RoomMealInfo::getMealType)
                .map(Object::toString)
                .map(MealTypeEnum::getEnum)
                .orElse(null));
        return roomItem;
    }

    private SupplierCheckAvailResultModel.BonusPointInfo convert(HotelBonusPointInfoDo hotelBonusPointInfoDo, String bonusPointCode, String groupId, String supplierCode) {
        SupplierCheckAvailResultModel.BonusPointInfo tmp = new SupplierCheckAvailResultModel.BonusPointInfo();
        tmp.setSupplierCode(supplierCode);
        tmp.setGroupId(groupId);
        tmp.setGroupName(hotelBonusPointInfoDo.getGroupName());
        tmp.setBonusPointCode(bonusPointCode);
        tmp.setBonusPointType(hotelBonusPointInfoDo.getBonusPointType());
        tmp.setFillPageRuleDescList(hotelBonusPointInfoDo.getFillPageRuleDescList());
        tmp.setOrderDetailPageRuleDescList(hotelBonusPointInfoDo.getOrderDetailPageRuleDescList());
        return tmp;
    }
    private SupplierCheckAvailResultModel.BookingRules getBookingRules(CheckAvailV2ResponseType response) {
        if (response.getBookingRules() == null) {
            return null;
        }
        SupplierCheckAvailResultModel.BookingRules bookingRules = new SupplierCheckAvailResultModel.BookingRules();

        SupplierCheckAvailResultModel.BillingGuestInfo billingGuestInfo = new SupplierCheckAvailResultModel.BillingGuestInfo();
        billingGuestInfo.setGuestsNameLanguages(Null.or(response.getBookingRules().getBillingGuestInfo(), CheckAvailV2ResponseType.BillingGuestInfo::getGuestsNameLanguages));
        billingGuestInfo.setNeedEmail(Null.or(response.getBookingRules().getBillingGuestInfo(), CheckAvailV2ResponseType.BillingGuestInfo::getNeedEmail));

        SupplierCheckAvailResultModel.CertificateInfo certificateInfo = new SupplierCheckAvailResultModel.CertificateInfo();
        certificateInfo.setNeedCertificate(Null.or(response.getBookingRules().getCertificateInfo(), CheckAvailV2ResponseType.CertificateInfo::getNeedCertificate));
        certificateInfo.setSupportCertificateType(Null.or(response.getBookingRules().getCertificateInfo(), CheckAvailV2ResponseType.CertificateInfo::getSupportCertificateType));

        SupplierCheckAvailResultModel.ConfirmRules confirmRules = new SupplierCheckAvailResultModel.ConfirmRules();
        confirmRules.setJustifyConfirm(Null.or(response.getBookingRules().getConfirmRules(), CheckAvailV2ResponseType.ConfirmRules::getJustifyConfirm));
        bookingRules.setCertificateInfo(certificateInfo);
        bookingRules.setBillingGuestInfo(billingGuestInfo);
        bookingRules.setConfirmRules(confirmRules);
        
        bookingRules.setMinLOS(response.getBookingRules().getMinLOS());
        return bookingRules;
    }

    private List<SupplierCheckAvailResultModel.ChangePriceDetailInfo> changePriceDetail(CheckAvailV2ResponseType response) {
        if (CollectionUtils.isEmpty(response.getChangePriceDetailList())) {
            return new ArrayList<>();
        }
        return response.getChangePriceDetailList().stream().map(e -> {
            if (e == null) {
                return null;
            }
            SupplierCheckAvailResultModel.ChangePriceDetailInfo priceDetailInfo = new SupplierCheckAvailResultModel.ChangePriceDetailInfo();
            priceDetailInfo.setDate(e.getDate());
            priceDetailInfo.setPrice(e.getPrice());
            return priceDetailInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
    private SupplierCheckAvailResultModel.TaxInfo getTaxInfo(CheckAvailV2ResponseType response) {
        if (response.getRoomInfo() == null || response.getRoomInfo().getPriceInformation() == null
                || response.getRoomInfo().getPriceInformation().getTaxInfo() == null
        ||  response.getRoomInfo().getPriceInformation().getTaxInfo().getTaxDetails() == null) {
            return null;
        }
        List<SupplierCheckAvailResultModel.TaxDetailInfo> taxDetailInfoList = response.getRoomInfo().getPriceInformation().getTaxInfo().getTaxDetails().stream().map(e -> {
            if (e == null) {
                return null;
            }
            SupplierCheckAvailResultModel.TaxDetailInfo taxInfo = new SupplierCheckAvailResultModel.TaxDetailInfo();
            taxInfo.setTaxId(e.getTaxId());
            taxInfo.setPercentage(e.getPercentage());
            taxInfo.setTaxTypeName(e.getTaxTypeName());
            PriceInfoModel orginalPriceInfoModel = new PriceInfoModel();
            orginalPriceInfoModel.setPrice(e.getAmount());
            orginalPriceInfoModel.setExchangeRateToken(null);
            orginalPriceInfoModel.setCurrency(e.getCurrency());
            taxInfo.setOriginalPrice(orginalPriceInfoModel);
            if (e.getCustomAmount() != null && e.getCustomCurrency() != null){
                PriceInfoModel sellPriceInfoModel = new PriceInfoModel();
                sellPriceInfoModel.setPrice(e.getCustomAmount());
                sellPriceInfoModel.setExchangeRateToken(null);
                sellPriceInfoModel.setCurrency(e.getCustomCurrency());
                taxInfo.setSellPrice(sellPriceInfoModel);
            } else {
                taxInfo.setSellPrice(orginalPriceInfoModel);
            }

            taxInfo.setTaxFeeCalculateType(e.getTaxFeeCalculateType());
            taxInfo.setIncludeInTotalPrice(e.getIncludeInTotalPrice());
            taxInfo.setChargeMode(e.getChargeMode());
            return taxInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        SupplierCheckAvailResultModel.TaxInfo taxInfo = new SupplierCheckAvailResultModel.TaxInfo();
        taxInfo.setTaxFeeInfoList(taxDetailInfoList);
        taxInfo.setIncludeTaxFeeDesc(response.getRoomInfo().getPriceInformation().getTaxInfo().getIncludeTaxFeeDesc());
        taxInfo.setExcludeTaxFeeDesc(response.getRoomInfo().getPriceInformation().getTaxInfo().getExcludeTaxFeeDesc());
        return taxInfo;
    }

    /**
     * 每日房价信息
     *
     * @param response
     * @return
     */
    private List<SupplierCheckAvailResultModel.RoomDailyInfo> roomDailyInfo(CheckAvailV2ResponseType response) {
        if (CollectionUtils.isEmpty(response.getRoomDailyInfoList())) {
            return new ArrayList<>();
        }
        return response.getRoomDailyInfoList().stream().map(e -> {
            if (e == null) {
                return null;
            }
            SupplierCheckAvailResultModel.RoomDailyInfo roomDailyInfo = new SupplierCheckAvailResultModel.RoomDailyInfo();
            roomDailyInfo.setEffectDate(e.getEffectDate());
            roomDailyInfo.setSellPrice(e.getSellPrice());
            roomDailyInfo.setMeals(e.getMeals());
            return roomDailyInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
