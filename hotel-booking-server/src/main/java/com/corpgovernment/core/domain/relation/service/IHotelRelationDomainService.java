package com.corpgovernment.core.domain.relation.service;

import com.corpgovernment.core.domain.relation.model.HotelInfo;
import com.corpgovernment.core.domain.relation.model.HotelKey;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @create 2024-11-20 16:40
 */
public interface IHotelRelationDomainService {
    
    Map<HotelKey, Set<HotelKey>> getHotelRelationMap(Map<String, List<HotelInfo>> hotelInfoListMap,
                                                     Set<String> supplierCodeSet,
                                                     Set<String> directSupplierCodeSet);
    
}
