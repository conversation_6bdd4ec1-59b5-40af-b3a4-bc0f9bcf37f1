package com.corpgovernment.core.domain.hotelconfig.gateway;

import com.corpgovernment.api.supplier.soa.SupplierCompanyClient;
import com.corpgovernment.api.supplier.soa.request.SupplierProductGetReqVo;
import com.corpgovernment.api.supplier.soa.response.SupplierProductGetRespVo;
import com.corpgovernment.api.supplier.vo.HotelOperatorTypeConfig;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.apollo.IHotelCoreApolloDao;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @create 2025-05-27 16:33
 */
@Repository
@Slf4j
public class SupplierProductGateway implements ISupplierProductGateway {
    
    @Resource
    private HotelOperatorTypeConfig hotelOperatorTypeConfig;
    
    @Resource
    private SupplierCompanyClient supplierCompanyClient;
    
    @Resource
    private IHotelCoreApolloDao hotelCoreApolloDao;
    
    @Override
    public SupplierProduct queryKeyWordSupplierProduct(String productType) {
        // 获取产线
        BizTypeEnum bizTypeEnum = BizTypeEnum.getByCodeOrName(productType);
        // 查询目的地模糊查询接口配置
        SupplierProduct supplierProduct = queryKeyWordSupplierProduct(bizTypeEnum);
        log.info("供应商配置={}", JsonUtils.toJsonString(supplierProduct));
        // 配置为空则返回默认配置
        if (supplierProduct == null) {
            return getDefaultKeyWordSupplierProduct();
        }
        return supplierProduct;
    }
    
    private SupplierProduct queryKeyWordSupplierProduct(BizTypeEnum bizTypeEnum) {
        // 没有产线直接返回
        if (bizTypeEnum == null) {
            return null;
        }
        
        // 查询目的地模糊查询接口配置
        SupplierProductGetReqVo supplierProductGetReqVo = new SupplierProductGetReqVo();
        supplierProductGetReqVo.setSupplierCode(HotelCoreConstant.CTRIP);
        supplierProductGetReqVo.setProductType(bizTypeEnum.getTransport());
        supplierProductGetReqVo.setOperateType(hotelOperatorTypeConfig.getFuzzySearchDestination());
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        supplierProductGetReqVo.setCompanyCode(baseUserInfo.getCorpId());
        JSONResult<SupplierProductGetRespVo> jsonResult = supplierCompanyClient.getSupplierProduct(supplierProductGetReqVo);
        log.info("供应商配置 request={} response={}", JsonUtils.toJsonString(supplierProductGetReqVo), JsonUtils.toJsonString(jsonResult));
        
        return toSupplierProduct(jsonResult);
    }
    
    private SupplierProduct toSupplierProduct(JSONResult<SupplierProductGetRespVo> jsonResult) {
        if (jsonResult == null || jsonResult.getData() == null) {
            return null;
        }
        
        SupplierProductGetRespVo data = jsonResult.getData();
        if (StringUtils.isBlank(data.getProductUrl()) || StringUtils.isBlank(data.getSupplierCorpId())) {
            return null;
        }
        
        SupplierProduct supplierProduct = new SupplierProduct();
        supplierProduct.setSupplierCode(HotelCoreConstant.CTRIP);
        supplierProduct.setUrl(data.getProductUrl());
        supplierProduct.setCorpId(data.getSupplierCorpId());
        return supplierProduct;
    }
    
    private SupplierProduct getDefaultKeyWordSupplierProduct() {
        SupplierProduct supplierProduct = new SupplierProduct();
        supplierProduct.setSupplierCode(HotelCoreConstant.CTRIP);
        supplierProduct.setUrl(hotelCoreApolloDao.getFuzzySearchDestinationUrl());
        supplierProduct.setCorpId(null);
        log.info("企业未配置目的地模糊查询接口，默认配置={}", JsonUtils.toJsonString(supplierProduct));
        return supplierProduct;
    }
    
}
