package com.corpgovernment.core.domain.common.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-11-11 14:08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SupplierConfig {
    
    private String systemSupplierCode;
    
    private List<String> clientSupplierCodeList;
    
    private List<String> ctripGroupIdList;
    
    private Boolean directSupplier;
    
}
