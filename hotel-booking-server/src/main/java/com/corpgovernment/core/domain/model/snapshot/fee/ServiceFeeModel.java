package com.corpgovernment.core.domain.model.snapshot.fee;


import com.corpgovernment.dto.snapshot.PriceInfoType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/19
 */
@Data
public class ServiceFeeModel {

    /**
     * 服务费名
     */
    private String serviceFeeName;

    private String serviceFeeDesc;
    /**
     *	服务费收费策略(ByAmount：以成交金额的固定比率收取服务费
     *	ByBooking：以每张订单收取服务费 ByRoomQuantity：以每间房收取服务费 ByRoomNights：以每间夜收取服务费)
     */
    private String chargingStrategy;

    /**
     * 原币种
     */
    private PriceInfoType originalFee;
    /**
     * 报价
     */
    private PriceInfoType fee;
}
