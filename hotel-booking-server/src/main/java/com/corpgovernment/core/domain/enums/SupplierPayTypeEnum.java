package com.corpgovernment.core.domain.enums;

import com.corpgovernment.common.enums.PayTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.BalanceTypeEnum;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2025-03-06 20:29
 */
@Getter
@AllArgsConstructor
public enum SupplierPayTypeEnum {
    
    INDIVIDUAL_PAY(PayTypeEnum.PPAY),
    ACCOUNT_PAY(PayTypeEnum.ACCNT),
    MIX_PAY(PayTypeEnum.MIXPAY),
    CASH_PAY(PayTypeEnum.CASH);

    private final PayTypeEnum payTypeEnum;
    
    private static final Map<String, SupplierPayTypeEnum> map = new HashMap<>();
    
    static {
        for (SupplierPayTypeEnum tmpEnum : values()) {
            map.put(tmpEnum.name(), tmpEnum);
        }
    }
    
    public static SupplierPayTypeEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }
}
