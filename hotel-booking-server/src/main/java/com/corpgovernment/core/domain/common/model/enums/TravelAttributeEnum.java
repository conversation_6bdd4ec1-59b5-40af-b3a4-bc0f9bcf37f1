package com.corpgovernment.core.domain.common.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @create 2024-09-03 16:19
 */
@Getter
@AllArgsConstructor
public enum TravelAttributeEnum {

    OVERSEAS_HOTEL_CONTROL_INCLUDE_EXTRA_TAX("overseas_hotel_control_include_extra_tax"),
    HOTEL_FORCE_CHUMMAGE("hotel_force_chummage"),
    HOTEL_FORCE_SAME_SEX_CHUMMAGE("hotel_force_same_sex_chummage"),
    HOTEL_PRICE_CONTROL_STRATEGY("hotel_price_control_strategy"),
    RESOURCE_PRICE_INCLUDE_SERVICE_CHARGE("hotel_service_charge"),
    DOMESTIC_HOTEL_BOOK_HOURLY_ROOM("domestic_hotel_book_hourly_room")
    ;

    private final String code;
    
}
