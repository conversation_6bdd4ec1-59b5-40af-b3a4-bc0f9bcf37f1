package com.corpgovernment.core.domain.model.snapshot.product;

import com.corpgovernment.dto.snapshot.dto.hotel.RoomInfoDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/18
 */
@Data
public class BasicRoomInfoModel {
    // 图片列表
    private List<String> pictureList;
    // 基础房型名
    private String name;
    // 床描述
    private String bedDesc;
    // 面积描述
    private String areaDesc;
    // 楼层描述
    private String floorDesc;
    // 最低房型均价
    private BigDecimal minAvgPriceIncludeTax;
    // 是否可预定（售罄/非售罄）
    private Boolean canReserve;
    // 协议类型 2两方协议 3三方协议
    private Integer protocolType;
    // 房型卡片列表
    private List<RoomInfoModel> roomCardList;
}
