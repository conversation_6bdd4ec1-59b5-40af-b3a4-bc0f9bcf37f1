package com.corpgovernment.core.domain.model.snapshot.fee;

import com.corpgovernment.core.domain.model.SupplierCheckAvailResultModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/31
 */
@Data
public class LadderDeductionDetailModel {
    /**
     * 扣款开始时间（北京时间）
     *
     * 免费取消类型的开始时间默认给1970-01-01 08:00:00
     *
     * 例如：2024-04-16 18:00:00
     */
    private Date startDeductTime;
    /**
     * 扣款结束时间（北京时间）
     *
     * 不可取消的结束时间默认给9999-12-31 23:59:59
     *
     * 例如：2024-04-16 18:00:00
     */
    private Date endDeductTime;
    /**
     * 扣款比例
     *
     * 例如：0.1
     */
    private BigDecimal deductionRatio;
    private BigDecimal amount;
    /**
     * 扣款币种
     */
    private String currency;
    /**
     * 原币种价格
     */
    private BigDecimal originalAmount;
    /**
     * 原币种
     */
    private String originalCurrency;
}
