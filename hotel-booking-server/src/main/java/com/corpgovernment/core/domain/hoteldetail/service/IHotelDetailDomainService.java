package com.corpgovernment.core.domain.hoteldetail.service;

import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.core.domain.hoteldetail.model.entity.BasicRoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetailFilter;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetailRequest;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-19 15:14
 */
public interface IHotelDetailDomainService {

    /**
     * 获取酒店详情
     * @param hotelDetailRequest 酒店详情请求
     * @param useCache 是否使用缓存
     * @return 酒店详情
     */
    HotelDetail getHotelDetail(HotelDetailRequest hotelDetailRequest, Boolean useCache);

    /**
     * 校验差标
     * @param hotelDetail 酒店详情
     * @param token token
     * @param bizTypeEnum 产线
     * @param roomNightNum 间夜数
     * @return 是否校验通过
     */
    Boolean verifyTravelStandard(HotelDetail hotelDetail,
                                 String token,
                                 String travelStandardMark,
                                 BizTypeEnum bizTypeEnum,
                                 BigDecimal roomNightNum,
                                 List<String> paymentMethodList,
                                 Boolean overseasHotelControlIncludeExtraTax,
                                 PriceControlStrategyEnum priceControlStrategyEnum);

    /**
     * 处理酒店积分
     * @param hotelDetail 酒店详情
     */
    void processHotelBonusPoint(HotelDetail hotelDetail);

    /**
     * 过滤酒店房型
     * @param hotelDetail 酒店详情
     * @param hotelDetailFilter 酒店详情过滤器
     */
    void filterRoom(HotelDetail hotelDetail, HotelDetailFilter hotelDetailFilter);

    /**
     * 展开基础房型
     * @param hotelDetail 酒店详情
     * @return 基础房型列表
     */
    List<BasicRoomCard> unfoldBasicRoomCard(HotelDetail hotelDetail);

    void handleBasicRoomCardAggAttr(BasicRoomCard basicRoomCard);

    void sortBasicRoomCardList(HotelDetail hotelDetail, List<String> prioritySupplierCodeList, Integer hotelRoomSortRule);
    
    HotelDetail verifyApplyTrip(HotelDetail hotelDetail, String travelId, String productType);

    
    HotelDetail handleRoomControl(HotelDetail hotelDetail, Boolean overseasHotelControlIncludeExtraTax);
    
}
