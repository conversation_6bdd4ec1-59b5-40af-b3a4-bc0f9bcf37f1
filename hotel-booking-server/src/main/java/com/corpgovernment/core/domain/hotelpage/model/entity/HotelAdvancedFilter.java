package com.corpgovernment.core.domain.hotelpage.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelAdvancedFilter {

    private BigDecimal lowPrice;
    private BigDecimal highPrice;
    // 用户选择的钻级和星级
    private List<Integer> choiceStarList;
    // 最终管控的星级
    private List<Integer> controlStarList;
    private List<String> hotelIdList;
    private List<String> brandIdList;
    private List<String> brandFeatureList;
    private List<String> groupIdList;
    private String keyword;
    private String bedType;
    private Boolean hasBreakfast;
    private Boolean hasJustifyConfirm;
    private Boolean hasFreeCancel;
    private Boolean hasAirportShuttle;
    private Boolean hasFitnessCenter;
    private Boolean hasSwimmingPool;
    private Boolean hasParking;
    private Boolean hasAirportPickup;
    private Boolean hasSpa;
    private Boolean hasFreeWifi;
    private Boolean hasFreeWiredBroadband;
    private Boolean hasFgRoom;
    private Boolean hasPpRoom;
    private Boolean hasCompanyAccountPayment;
    private Boolean applyForeignGuest;
    private Boolean applyGat;
    private Boolean hasContractRoom;
    private Boolean hasHotelBonusPoint;
    private String roomType;
    private Boolean hasFullAmountOnlinePay;
    private Boolean priceFilterIncludeExtraTax;
    private Map<String, String> supplierCodeHotelIdMap;
    private Boolean filterWithServiceCharge;
    private Boolean hasHourlyRoom;

}
