package com.corpgovernment.core.domain.common.model.entity;

import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelAdvancedFilter;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelBaseFilter;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelDistanceDesc;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelPositionFilter;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelSort;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryHotelListRequest {

    // 供应商产品
    private SupplierProduct supplierProduct;
    // 查询条件
    private HotelBaseFilter hotelBaseFilter;
    private HotelPositionFilter hotelPositionFilter;
    private HotelAdvancedFilter hotelAdvancedFilter;
    // 排序
    private HotelSort hotelSort;
    // 距离文案
    private HotelDistanceDesc hotelDistanceDesc;
    private Integer pageIndex;
    private Integer pageSize;
    private Integer lastPageIndex;
    // 海外产线
    private Boolean abroad;
    // 因公因私
    private TravelModeEnum travelMode;
    
    public String extractSupplierTravelMode() {
        return Optional.ofNullable(travelMode)
                .map(TravelModeEnum::getSupplierCode)
                .orElse(null);
    }
    
    public Boolean extractHasHourlyRoom() {
        return Optional.ofNullable(hotelAdvancedFilter)
                .map(HotelAdvancedFilter::getHasHourlyRoom)
                .orElse(false);
    }

}
