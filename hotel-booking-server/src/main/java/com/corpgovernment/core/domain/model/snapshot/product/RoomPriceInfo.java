package com.corpgovernment.core.domain.model.snapshot.product;

import com.corpgovernment.dto.snapshot.dto.hotel.DailyRoomPrice;
import com.corpgovernment.dto.snapshot.dto.hotel.TaxPriceType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/18
 */
@Data
public class RoomPriceInfo {
    // 间数
    private Integer roomQuantity;
    // 天数
    private Integer dayQuantity;
    // 每日房价
    private List<DailyRoomPriceModel> dailyRateList;
    // 包含税费总价
    private BigDecimal totalPriceIncludeTax;
    // 房间税费总价
    private BigDecimal totalRoomTax;
    // 房间税费外币总价
    private BigDecimal foreignTotalRoomTax;
    // 额外税费总价
    private BigDecimal totalExtraTax;
    // 额外税费外币总价
    private BigDecimal foreignTotalExtraTax;
    // 包含税费均价
    private BigDecimal avgPriceIncludeTax;
    // 房间税费均价
    private BigDecimal avgExtraTax;
    // 外币币种
    private String foreignCurrency;
    // 额外税费明细
    private List<TaxPriceInfo> totalExtraTaxDetailList;
    // 房间税费明细
    private List<TaxPriceInfo> totalRoomTaxDetailList;
    // 多人房价相关信息
    private PersonPriceInfo personPriceInfo;
}
