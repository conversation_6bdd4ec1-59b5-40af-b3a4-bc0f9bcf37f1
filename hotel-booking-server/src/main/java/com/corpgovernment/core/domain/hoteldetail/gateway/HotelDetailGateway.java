package com.corpgovernment.core.domain.hoteldetail.gateway;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Repository;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.dao.apollo.IHotelCoreApolloDao;
import com.corpgovernment.core.dao.dataobject.HotelBonusPointInfoDo;
import com.corpgovernment.core.dao.mysql.IHotelRoomRelationDao;
import com.corpgovernment.core.dao.openfeign.IHotelCoreOpenFeignDao;
import com.corpgovernment.core.domain.common.gateway.ICommonGateway;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.ServiceCharge;
import com.corpgovernment.core.domain.common.model.enums.OverLimitModeEnum;
import com.corpgovernment.core.domain.common.model.enums.ServiceChargeStrategyEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.core.domain.hoteldetail.model.entity.ApplyTripOverLimitReminder;
import com.corpgovernment.core.domain.hoteldetail.model.entity.BasicRoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.DailyRate;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelBaseInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelBonusPoint;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hoteldetail.model.entity.Item;
import com.corpgovernment.core.domain.hoteldetail.model.entity.OverLimitInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPolicyService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPrice;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomSupplier;
import com.corpgovernment.core.domain.hoteldetail.model.entity.SupplierStarInfo;
import com.corpgovernment.core.domain.hoteldetail.model.enums.BalanceTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.WindowEnum;
import com.corpgovernment.dto.management.request.HotelVerifyRequest;
import com.corpgovernment.dto.management.response.ResourcesVerifyResponse;
import com.corpgovernment.dto.management.response.TravelStandardRuleVerifyResultVO;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.corpgovernment.hotel.booking.bo.ApplyTripControlBo;
import com.corpgovernment.hotel.booking.bo.ApplyTripItemBo;
import com.corpgovernment.hotel.booking.service.ApplyTripService;
import com.corpgovernment.hotel.product.entity.db.HotelRoomRelationDo;
import com.corpgovernment.redis.cache.RedisUtils;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@Repository
@Slf4j
public class HotelDetailGateway implements IHotelDetailGateway {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private IHotelCoreApolloDao hotelCoreApolloDao;

    @Resource
    private IHotelRoomRelationDao hotelRoomRelationDao;

    @Resource
    private ICommonGateway commonGateway;

    @Resource
    private IHotelCoreOpenFeignDao hotelCoreOpenFeignDao;
    
    @Resource
    private ApplyTripService applyTripService;


    @Override
    @BusinessBehaviorMonitor
    public void setHotelDetail(String key, HotelDetail hotelDetail) {
        if (StringUtils.isBlank(key) || hotelDetail == null) {
            return;
        }
        Long cacheTime = Null.or(hotelCoreApolloDao.getCacheTime(HotelCoreConstant.RedisKey.HOTEL_DETAIL), HotelCoreConstant.DEFAULT_CACHE_TIME);
        try {
            redisUtils.setCache(key, hotelDetail, cacheTime);
        } catch (Exception e) {
            log.error("保存详情缓存异常", e);
        }
    }

    @Override
    @BusinessBehaviorMonitor
    public HotelDetail getHotelDetail(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        try {
            return redisUtils.getCache(key, HotelDetail.class);
        } catch (Exception e) {
            log.error("获取详情缓存异常", e);
            return null;
        }
    }

    @Override
    public Map<String, HotelBonusPoint> getHotelBonusPointInfoMap() {
        List<HotelBonusPointInfoDo> hotelBonusPointInfoList = hotelCoreApolloDao.getHotelBonusPointInfoList();
        if (CollectionUtils.isEmpty(hotelBonusPointInfoList)) {
            return new HashMap<>(0);
        }

        Map<String, HotelBonusPoint> hotelBonusPointInfoMap = new HashMap<>();
        for (HotelBonusPointInfoDo hotelBonusPointInfoDo : hotelBonusPointInfoList) {
            if (hotelBonusPointInfoDo == null || CollectionUtils.isEmpty(hotelBonusPointInfoDo.getSupplierGroupList())) {
                continue;
            }
            for (String supplierGroup : hotelBonusPointInfoDo.getSupplierGroupList()) {
                if (StringUtils.isBlank(supplierGroup)) {
                    continue;
                }
                hotelBonusPointInfoMap.put(supplierGroup, HotelBonusPoint.builder()
                        .groupName(hotelBonusPointInfoDo.getGroupName())
                        .bonusPointCodeList(hotelBonusPointInfoDo.getBonusPointCodeList())
                        .bonusPointType(hotelBonusPointInfoDo.getBonusPointType())
                        .hotelDetailPageRuleDescList(hotelBonusPointInfoDo.getHotelDetailPageRuleDescList())
                        .fillPageRuleDescList(hotelBonusPointInfoDo.getFillPageRuleDescList())
                        .orderDetailPageRuleDescList(hotelBonusPointInfoDo.getOrderDetailPageRuleDescList()).build());
            }
        }
        return hotelBonusPointInfoMap;
    }

    @Override
    @BusinessBehaviorMonitor
    public void getCtripRoomRelationMap(Map<RoomSupplier, Set<RoomSupplier>> allRoomRelationMap, Map<String, List<String>> hotelIdListMap) {
        if (CollectionUtils.isEmpty(hotelIdListMap)) {
            return;
        }

        Set<HotelRoomRelationDo> hotelRoomRelationDoSet = new HashSet<>();
        Set<String> supplierCodeSet = hotelIdListMap.keySet();
        hotelIdListMap.forEach((supplierCode, hotelIdList) -> {
            if (CollectionUtils.isEmpty(hotelIdList)) {
                return;
            }
            // 正向找
            if (StringUtils.equalsIgnoreCase("ctrip", supplierCode)) {
                List<HotelRoomRelationDo> tmpList = hotelRoomRelationDao.listForwardRoomRelation(supplierCode, supplierCodeSet.stream().filter(item -> !StringUtils.equalsIgnoreCase(item, supplierCode)).collect(Collectors.toList()), hotelIdList);
                if (CollectionUtils.isNotEmpty(tmpList)) {
                    hotelRoomRelationDoSet.addAll(tmpList);
                }
            }
            // 反向找
            else {
                List<HotelRoomRelationDo> tmpList = hotelRoomRelationDao.listBackwardRoomRelation(supplierCode, hotelIdList);
                if (CollectionUtils.isNotEmpty(tmpList)) {
                    hotelRoomRelationDoSet.addAll(tmpList);
                }
            }
        });
        log.info("hotelRoomRelationDoSet:{}", hotelRoomRelationDoSet);
        if (CollectionUtils.isEmpty(hotelRoomRelationDoSet)) {
            return;
        }

        // 全量正向关系
        Map<RoomSupplier, Set<RoomSupplier>> forwardRoomRelationMap = new HashMap<>();
        for (HotelRoomRelationDo hotelRoomRelationDo : hotelRoomRelationDoSet) {
            if (hotelRoomRelationDo == null) {
                continue;
            }
            RoomSupplier masterRoomSupplier = RoomSupplier.builder()
                    .supplierCode(hotelRoomRelationDo.getMasterSupplierCode())
                    .hotelId(hotelRoomRelationDo.getMasterHotelId())
                    .basicRoomId(hotelRoomRelationDo.getMasterRoomId()).build();
            RoomSupplier subRoomSupplier = RoomSupplier.builder()
                    .supplierCode(hotelRoomRelationDo.getSubSupplierCode())
                    .hotelId(hotelRoomRelationDo.getSubHotelId())
                    .basicRoomId(hotelRoomRelationDo.getSubRoomId()).build();
            Set<RoomSupplier> tmpSet = forwardRoomRelationMap.getOrDefault(masterRoomSupplier, new HashSet<>());
            tmpSet.add(subRoomSupplier);
            forwardRoomRelationMap.put(masterRoomSupplier, tmpSet);
        }

        // 全量关系
        forwardRoomRelationMap.forEach((masterRoomSupplier, subRoomSupplierSet) -> {
            subRoomSupplierSet.add(masterRoomSupplier);
            Set<RoomSupplier> originSet = subRoomSupplierSet.stream().filter(subRoomSupplierSet::contains).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(originSet) || originSet.size() == 1) {
                return;
            }
            // 原来关系+增量关系
            Set<RoomSupplier> allSet = new HashSet<>(originSet);
            for (RoomSupplier roomSupplier : originSet) {
                Set<RoomSupplier> tmpSet = allRoomRelationMap.get(roomSupplier);
                if (CollectionUtils.isNotEmpty(tmpSet)) {
                    allSet.addAll(tmpSet);
                }
            }
            // 全向关系导入
            for (RoomSupplier roomSupplier : allSet) {
                allRoomRelationMap.put(roomSupplier, allSet);
            }
        });
    }

    @Override
    @BusinessBehaviorMonitor
    public void getSelfRoomRelationMap(Map<RoomSupplier, Set<RoomSupplier>> allRoomRelationMap, List<BasicRoomCard> basicRoomCardList) {
        if (CollectionUtils.isEmpty(basicRoomCardList)) {
            return;
        }
        Map<String, List<BasicRoomCard>> basicRoomCardListMap = basicRoomCardList.stream().filter(item -> item != null && item.getName() != null).collect(Collectors.groupingBy(BasicRoomCard::getName));
        if (CollectionUtils.isEmpty(basicRoomCardListMap)) {
            return;
        }
        basicRoomCardListMap.forEach((roomName, list) -> {
            if (CollectionUtils.isEmpty(list) || list.size() == 1) {
                return;
            }
            Set<RoomSupplier> originSet = list.stream().map(item -> RoomSupplier.builder()
                    .supplierCode(item.getSupplierCode())
                    .hotelId(item.getHotelId())
                    .basicRoomId(item.getBasicRoomId()).build()).collect(Collectors.toSet());
            // 原来关系+增量关系
            Set<RoomSupplier> allSet = new HashSet<>(originSet);
            for (RoomSupplier roomSupplier : originSet) {
                Set<RoomSupplier> tmpSet = allRoomRelationMap.get(roomSupplier);
                if (CollectionUtils.isNotEmpty(tmpSet)) {
                    allSet.addAll(tmpSet);
                }
            }
            // 全向关系导入
            for (RoomSupplier roomSupplier : allSet) {
                allRoomRelationMap.put(roomSupplier, allSet);
            }
        });
    }

    @Override
    public void handleBasicRoomCardAggAttr(BasicRoomCard basicRoomCard) {
        if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
            return;
        }

        // 清空
        basicRoomCard.setCanReserve(null);
        basicRoomCard.setProtocolType(null);
        basicRoomCard.setMinRoomPrice(null);
        basicRoomCard.setWindowEnum(null);

        // 计算
        for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
            if (roomCard == null) {
                continue;
            }
            // 是否有可订房型
            basicRoomCard.setCanReserve(Boolean.TRUE.equals(basicRoomCard.getCanReserve()) || Boolean.TRUE.equals(roomCard.getCanReserve()));
            // 协议标签
            basicRoomCard.setProtocolType(getProtocolType(basicRoomCard.getProtocolType(), roomCard.getProtocolType()));
            // 起价
            basicRoomCard.setMinRoomPrice(getMinRoomPrice(roomCard.getRoomPrice(), basicRoomCard.getMinRoomPrice()));
        }
        
        basicRoomCard.setWindowEnum(getWindowEnum(basicRoomCard.getRoomCardList()));
    }
    
    private WindowEnum getWindowEnum(List<RoomCard> roomCardList) {
        if (CollectionUtils.isEmpty(roomCardList)) {
            return WindowEnum.UNKNOWN;
        }
        
        List<WindowEnum> windowEnumList = roomCardList.stream()
                .filter(item -> item != null && item.getRoomBaseInfo() != null)
                .map(item -> Null.or(item.getRoomBaseInfo().getWindowEnum(), WindowEnum.UNKNOWN))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(windowEnumList)) {
            return WindowEnum.UNKNOWN;
        }
        
        // 去重
        windowEnumList = windowEnumList.stream().distinct().collect(Collectors.toList());
        if (windowEnumList.size() == 1) {
            return windowEnumList.get(0);
        }
        
        // 去掉未知窗型
        windowEnumList = windowEnumList.stream().filter(item -> !Objects.equals(item, WindowEnum.UNKNOWN)).collect(Collectors.toList());
        
        // 有窗判断
        if (windowEnumList.stream().allMatch(item -> Objects.equals(item, WindowEnum.WINDOWED)
                || Objects.equals(item, WindowEnum.WINDOW_IN_AISLE)
                || Objects.equals(item, WindowEnum.SKYLIGHT)
                || Objects.equals(item, WindowEnum.CLOSED_WINDOW)
                || Objects.equals(item, WindowEnum.BAY_WINDOW))) {
            return WindowEnum.WINDOWED;
        }
        
        // 部分有窗判断
        if (windowEnumList.stream().anyMatch(item -> Objects.equals(item, WindowEnum.PARTIALLY_WINDOWLESS)
                || Objects.equals(item, WindowEnum.WINDOWED)
                || Objects.equals(item, WindowEnum.WINDOW_IN_AISLE)
                || Objects.equals(item, WindowEnum.SKYLIGHT)
                || Objects.equals(item, WindowEnum.CLOSED_WINDOW)
                || Objects.equals(item, WindowEnum.BAY_WINDOW))) {
            return WindowEnum.PARTIALLY_WINDOWLESS;
        }
        
        return WindowEnum.UNKNOWN;
    }
    
    @Override
    public void handleHotelDetailAggAttr(HotelDetail hotelDetail) {
        if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return;
        }

        Map<String, RoomSupplier> roomSupplierMap = new HashMap<>();

        for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
            if (basicRoomCard == null) {
                continue;
            }

            String supplierCode = basicRoomCard.getSupplierCode();
            RoomSupplier roomSupplier = roomSupplierMap.get(supplierCode);
            if (roomSupplier == null) {
                roomSupplier = RoomSupplier.builder()
                        .supplierCode(supplierCode)
                        .supplierName(basicRoomCard.getSupplierName()).build();
            }
            roomSupplier.setMinRoomPrice(getMinRoomPrice(roomSupplier.getMinRoomPrice(), basicRoomCard.getMinRoomPrice()));
            roomSupplierMap.put(supplierCode, roomSupplier);

            hotelDetail.setMinRoomPrice(getMinRoomPrice(hotelDetail.getMinRoomPrice(), basicRoomCard.getMinRoomPrice()));
        }

        hotelDetail.setSupplierList(new ArrayList<>(roomSupplierMap.values()));
    }

    @Override
    public RoomPrice getMinRoomPrice(RoomPrice roomPrice1, RoomPrice roomPrice2) {
        if (roomPrice1 == null) {
            return roomPrice2;
        }
        if (roomPrice2 == null) {
            return roomPrice1;
        }
        Price avgPriceIncludeTax1 = roomPrice1.getFinalAvgPrice();
        if (!Boolean.TRUE.equals(commonGateway.checkCustomPrice(avgPriceIncludeTax1))) {
            return roomPrice2;
        }
        Price avgPriceIncludeTax2 = roomPrice2.getFinalAvgPrice();
        if (!Boolean.TRUE.equals(commonGateway.checkCustomPrice(avgPriceIncludeTax2))) {
            return roomPrice1;
        }
        if (avgPriceIncludeTax1.getCustomPrice().compareTo(avgPriceIncludeTax2.getCustomPrice()) < 0) {
            return roomPrice1;
        }
        return roomPrice2;
    }

    @Override
    public Integer getProtocolType(Integer protocolType1, Integer protocolType2) {
        if (protocolType1 == null) {
            return protocolType2;
        }
        if (protocolType2 == null) {
            return protocolType1;
        }
        if (protocolType1 == 3 || protocolType2 == 3) {
            return 3;
        }
        if (protocolType1 == 2 || protocolType2 == 2) {
            return 2;
        }
        return null;
    }

    @Override
    public Boolean verifyTravelStandard(HotelDetail hotelDetail,
                                        String token,
                                        String travelStandardMark,
                                        BizTypeEnum bizTypeEnum,
                                        BigDecimal roomNightNum,
                                        List<String> paymentMethodList,
                                        Boolean overseasHotelControlIncludeExtraTax,
                                        PriceControlStrategyEnum priceControlStrategyEnum) {
        if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return true;
        }

        if (bizTypeEnum == null) {
            return false;
        }

        // 子房型打标签
        Map<String, RoomCard> roomCardMap = markRoomCard(hotelDetail);
        if (CollectionUtils.isEmpty(roomCardMap)) {
            return true;
        }

        // 差标校验
        List<HotelVerifyRequest> hotelVerifyRequestList = new ArrayList<>();
        roomCardMap.forEach((key, roomCard) -> {
            HotelVerifyRequest hotelVerifyRequest = new HotelVerifyRequest();
            hotelVerifyRequest.setHotelId(roomCard.getHotelId());
            hotelVerifyRequest.setRoomId(roomCard.getRoomId());
            hotelVerifyRequest.setBrandId(roomCard.getBrandId());
            SupplierStarInfo supplierStar = getSupplierStar(roomCard, hotelDetail.getHotelBaseInfo());
            if (supplierStar != null) {
                hotelVerifyRequest.setStar(supplierStar.getStar());
                hotelVerifyRequest.setStarLicence(supplierStar.getStarLicence());
            }
            hotelVerifyRequest.setResourcesId(key);
            hotelVerifyRequest.setPrice(getAvgRoomPrice(roomCard.getRoomPrice(), overseasHotelControlIncludeExtraTax));
            hotelVerifyRequest.setBreakfast(getBreakfast(roomCard.getRoomPolicyService()));
            hotelVerifyRequest.setStepLevel(StringUtils.equalsIgnoreCase(travelStandardMark, HotelCoreConstant.NONE) ? null : Optional.ofNullable(travelStandardMark).map(Integer::valueOf).orElse(null));
            hotelVerifyRequest.setPriceControlStrategy(Optional.ofNullable(priceControlStrategyEnum).map(PriceControlStrategyEnum::getCode).orElse(PriceControlStrategyEnum.AVG_PRICE.getCode()));
            hotelVerifyRequest.setDailyPriceList(buildDailyPriceList(roomCard.getRoomPrice(), overseasHotelControlIncludeExtraTax));
            hotelVerifyRequestList.add(hotelVerifyRequest);
        });
        List<ResourcesVerifyResponse> resourcesVerifyResponseList = hotelCoreOpenFeignDao.verifyTravelStandard(token, bizTypeEnum.getCode(), hotelVerifyRequestList);
        log.info("开始校验 resourcesVerifyResponseList={}", JsonUtils.toJsonString(resourcesVerifyResponseList));
        if (resourcesVerifyResponseList == null) {
            return false;
        }

        // 处理超标房间
        handleOverLimitRoomCard(roomCardMap, resourcesVerifyResponseList, roomNightNum, paymentMethodList);
        return true;
    }

    private SupplierStarInfo getSupplierStar(RoomCard subRoomInfo, HotelBaseInfo hotelBaseInfo) {
        // 兼容逻辑，发布过程中命中缓存场景走老逻辑
        if (MapUtils.isEmpty(hotelBaseInfo.getSupplierStarInfo())) {
            if (subRoomInfo.getStarLicence()) {
                SupplierStarInfo supplierStarInfo = new SupplierStarInfo();
                supplierStarInfo.setStar(hotelBaseInfo.getStar() > 5 ? 5 : hotelBaseInfo.getStar());
                supplierStarInfo.setStarLicence(hotelBaseInfo.getStarLicence());
                return supplierStarInfo;
            }
        }
        return hotelBaseInfo.getSupplierStarInfo().get(subRoomInfo.getSupplierCode());
    }

    private List<HotelVerifyRequest.DailyPrice> buildDailyPriceList(RoomPrice roomPrice, Boolean overseasHotelControlIncludeExtraTax) {
        if (roomPrice == null || CollectionUtils.isEmpty(roomPrice.getDailyRateList())) {
            return null;
        }
        
        // 到店另付税费
        BigDecimal avgExtraTax = BigDecimal.ZERO;
        if (Boolean.TRUE.equals(overseasHotelControlIncludeExtraTax) && roomPrice.getAvgExtraTax() != null && roomPrice.getAvgExtraTax().getCustomPrice() != null) {
            avgExtraTax = avgExtraTax.add(roomPrice.getAvgExtraTax().getCustomPrice());
        }
        
        List<HotelVerifyRequest.DailyPrice> resultList = new ArrayList<>();
        for (DailyRate dailyRate : roomPrice.getDailyRateList()) {
            if (dailyRate == null || StringUtils.isBlank(dailyRate.getDate()) || dailyRate.getAvgPriceIncludeTax() == null || dailyRate.getAvgPriceIncludeTax().getCustomPrice() == null) {
                continue;
            }

            resultList.add(HotelVerifyRequest.DailyPrice.builder()
                    .date(dailyRate.getDate())
                    .price(dailyRate.getAvgPriceIncludeTax().getCustomPrice().add(avgExtraTax))
                    .build());
        }
        
        return resultList;
    }
    
    @Override
    public HotelDetail verifyApplyTrip(HotelDetail hotelDetail, String travelId, String productType) {
        if (hotelDetail == null || StringUtils.isBlank(travelId) || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return hotelDetail;
        }
        
        // travelId强转
        long tmpTravelId;
        try {
            tmpTravelId = Long.parseLong(travelId);
        } catch (Exception exception) {
            return hotelDetail;
        }
        
        // 获取星级
        Integer star = getStar(hotelDetail.getHotelBaseInfo());
        
        // 获取出差申请单项列表
        List<ApplyTripItemBo> applyTripItemBoList = applyTripService.getApplyTripItemList(tmpTravelId, BizTypeEnum.getByCodeOrName(productType));
        if (CollectionUtils.isEmpty(applyTripItemBoList)) {
            return hotelDetail;
        }
        List<Item> applyTripItemList = applyTripItemBoList.stream()
                .filter(Objects::nonNull)
                .map(item -> Item.builder()
                        .key(item.getCode())
                        .label(item.getName())
                        .value(item.getDesc()).build())
                .collect(Collectors.toList());
        
        // 校验
        for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
            if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                continue;
            }
            
            for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                if (roomCard == null) {
                    continue;
                }
                
                // 校验
                List<ApplyTripItemBo> checkApplyTripItemList = applyTripService.checkApplyTripItemList(
                        applyTripItemBoList,
                        ApplyTripControlBo.builder()
                                .star(star)
                                .avgAmount(Optional.ofNullable(roomCard.getRoomPrice()).map(RoomPrice::getAvgPriceIncludeTax).map(Price::getCustomPrice).orElse(null))
                                .wantCheckItemCodeList(Arrays.asList("hotelProductType", "hotelAveragePrice")).build());
                if (checkApplyTripItemList == null) {
                    continue;
                }
                
                List<Item> overLimitItemList = checkApplyTripItemList.stream()
                        .filter(item -> item != null && Boolean.TRUE.equals(item.getOverLimit()))
                        .map(item -> Item.builder()
                                .key(item.getCode())
                                .label(item.getName())
                                .value(item.getOverLimitDesc()).build())
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(overLimitItemList)) {
                    continue;
                }
                
                // 管控
                roomCard.setApplyTripOverLimitReminder(ApplyTripOverLimitReminder.builder()
                        .applyTripItemList(applyTripItemList)
                        .overLimitItemList(overLimitItemList).build());
            }
        }
        
        return hotelDetail;
    }
    

    @Override
    @BusinessBehaviorMonitor
    public void getDirectRoomRelationMap(Map<RoomSupplier, Set<RoomSupplier>> allRoomRelationMap, List<BasicRoomCard> basicRoomCardList) {
        if (CollectionUtils.isEmpty(basicRoomCardList)) {
            return;
        }
        Map<String, List<BasicRoomCard>> basicRoomCardListMap = basicRoomCardList.stream()
                .filter(item -> item != null && item.getHotelId() != null && item.getBasicRoomId() != null)
                .collect(Collectors.groupingBy(item -> item.getHotelId() + item.getBasicRoomId()));
        if (CollectionUtils.isEmpty(basicRoomCardListMap)) {
            return;
        }

        Map<String, List<BasicRoomCard>> directBasicRoomRelationGroupMap = new HashMap<>();
        basicRoomCardListMap.forEach((key, list) -> {
            if (CollectionUtils.isEmpty(list) || list.size() == 1) {
                return;
            }

            // 直连关系组
            directBasicRoomRelationGroupMap.put(key, list);

            Set<RoomSupplier> originSet = list.stream().map(item -> RoomSupplier.builder()
                    .supplierCode(item.getSupplierCode())
                    .hotelId(item.getHotelId())
                    .basicRoomId(item.getBasicRoomId()).build()).collect(Collectors.toSet());
            // 原来关系+增量关系
            Set<RoomSupplier> allSet = new HashSet<>(originSet);
            for (RoomSupplier roomSupplier : originSet) {
                Set<RoomSupplier> tmpSet = allRoomRelationMap.get(roomSupplier);
                if (CollectionUtils.isNotEmpty(tmpSet)) {
                    allSet.addAll(tmpSet);
                }
            }
            // 全向关系导入
            for (RoomSupplier roomSupplier : allSet) {
                allRoomRelationMap.put(roomSupplier, allSet);
            }
        });
        log.info("直连关系 directBasicRoomRelationGroupMap={}", JsonUtils.toJsonString(directBasicRoomRelationGroupMap));
    }
    
    private BigDecimal getAvgRoomPrice(RoomPrice roomPrice, Boolean overseasHotelControlIncludeExtraTax) {
        if (roomPrice == null || !Boolean.TRUE.equals(commonGateway.checkCustomPrice(roomPrice.getAvgPriceIncludeTax()))) {
            return null;
        }
        
        BigDecimal avgExtraTaxCustomPrice = Optional.ofNullable(roomPrice.getAvgExtraTax()).map(Price::getCustomPrice).orElse(BigDecimal.ZERO);
        
        BigDecimal avgRoomPrice = roomPrice.getAvgPriceIncludeTax().getCustomPrice();
        if (Boolean.TRUE.equals(overseasHotelControlIncludeExtraTax)) {
            avgRoomPrice = avgRoomPrice.add(avgExtraTaxCustomPrice);
        }
        
        return avgRoomPrice;
    }

    private String getBreakfast(RoomPolicyService roomPolicyService) {
        if (roomPolicyService == null || roomPolicyService.getBreakfastCount() == null) {
            return null;
        }
        if (roomPolicyService.getBreakfastCount() <= 0) {
            return HotelCoreConstant.F;
        }
        return HotelCoreConstant.T;
    }

    private void handleOverLimitRoomCard(Map<String, RoomCard> roomCardMap,
                                         List<ResourcesVerifyResponse> resourcesVerifyResponseList,
                                         BigDecimal roomNightNum,
                                         List<String> paymentMethodList) {
        if (CollectionUtils.isEmpty(roomCardMap) || CollectionUtils.isEmpty(resourcesVerifyResponseList)) {
            return;
        }

        for (ResourcesVerifyResponse resourcesVerifyResponse : resourcesVerifyResponseList) {
            if (resourcesVerifyResponse == null || roomCardMap.get(resourcesVerifyResponse.getResourcesId()) == null
                    || !Boolean.TRUE.equals(resourcesVerifyResponse.getBookable())) {
                continue;
            }
            Integer exceed = resourcesVerifyResponse.getExceed();
            Set<String> rejectTypeSet = resourcesVerifyResponse.getRejectTypes();
            // 超标
            if (exceed != null && exceed != 0) {
                List<TravelStandardRuleVerifyResultVO> ruleVerifyResultList = resourcesVerifyResponse.getRuleVerifyResultList();
                if (CollectionUtils.isEmpty(ruleVerifyResultList)) {
                    continue;
                }

                // 超标明细
                Set<String> overLimitRuleNameSet = new HashSet<>();
                for (TravelStandardRuleVerifyResultVO travelStandardRuleVerifyResultVO : ruleVerifyResultList) {
                    Integer tmpExceed = travelStandardRuleVerifyResultVO.getExceed();
                    TravelStandardRuleVO rule = travelStandardRuleVerifyResultVO.getRule();
                    // 不超标
                    if (tmpExceed == 0 || rule == null) {
                        continue;
                    }

                    overLimitRuleNameSet.add(rule.getName());
                }

                // 不超标
                if (CollectionUtils.isEmpty(overLimitRuleNameSet)) {
                    continue;
                }
                
                // 房型卡片
                RoomCard roomCard = roomCardMap.get(resourcesVerifyResponse.getResourcesId());
                roomCard.setOverLimitInfo(OverLimitInfo.builder()
                        .overLimitRuleNameList(new ArrayList<>(overLimitRuleNameSet))
                        .overLimitModeList(getOverLimitModeList(roomCard.getDirectSupplier(), rejectTypeSet, paymentMethodList, roomCard.getBalanceTypeEnum())).build());
                
                // 价格超标
                BigDecimal totalExceedAmount = resourcesVerifyResponse.getExceedAmount();
                // 总价
                BigDecimal totalPrice = Optional.ofNullable(roomCard.getRoomPrice())
                        .map(RoomPrice::getTotalPriceIncludeTaxAndServiceCharge)
                        .map(Price::getCustomPrice)
                        .orElse(null);
                if ((overLimitRuleNameSet.contains("PriceRule")
                        || overLimitRuleNameSet.contains("CohabitRule")
                        || overLimitRuleNameSet.contains("OffPeakSeasonRule")
                        || overLimitRuleNameSet.contains("FloatPriceRule"))
                        && totalExceedAmount != null
                        && totalPrice != null
                        && roomNightNum != null
                ) {
                    RoomPrice roomPrice = Null.or(roomCard.getRoomPrice(), new RoomPrice());
                    roomPrice.setTotalOverLimitPrice(Price.builder()
                            .customCurrency(HotelCoreConstant.CNY)
                            .customPrice(totalExceedAmount).build());
                    roomPrice.setTotalCorpPayPrice(Price.builder()
                            .customCurrency(HotelCoreConstant.CNY)
                            .customPrice(totalPrice.subtract(totalExceedAmount)).build());
                    roomCard.setRoomPrice(roomPrice);
                }
            }
        }
    }
    
    private List<String> getOverLimitModeList(Boolean directSupplier, Set<String> rejectTypeSet, List<String> paymentMethodList, BalanceTypeEnum balanceTypeEnum) {
        log.info("getOverLimitModeList directSupplier={}, rejectTypeSet={}, paymentMethodList={}",
                directSupplier,
                JsonUtils.toJsonString(rejectTypeSet),
                JsonUtils.toJsonString(paymentMethodList));
        List<String> overLimitModeList = commonGateway.getOverLimitModeList(CollectionUtils.isEmpty(rejectTypeSet) ? null : new ArrayList<>(rejectTypeSet), paymentMethodList);
        
        // 到店付资源去除混付和公帐支付
        if (CollectionUtils.isNotEmpty(overLimitModeList) && Objects.equals(balanceTypeEnum, BalanceTypeEnum.FG)) {
            overLimitModeList.remove(OverLimitModeEnum.M.getCode());
        }
        
        // 直连资源去除混付
        if (Boolean.TRUE.equals(directSupplier)) {
            overLimitModeList.remove(OverLimitModeEnum.M.getCode());
        }
        
        // 无管控方式给禁止预定
        if (CollectionUtils.isEmpty(overLimitModeList)) {
            overLimitModeList.add(OverLimitModeEnum.F.getCode());
        }
        return overLimitModeList;
    }
    
    private Integer getStar(HotelBaseInfo hotelBaseInfo) {
        Integer star;
        if (hotelBaseInfo != null) {
            star = hotelBaseInfo.getStar();
            if (star != null && star > 5) {
                star = 5;
            }
        } else {
            star = null;
        }
        return star;
    }

    private Map<String, RoomCard> markRoomCard(HotelDetail hotelDetail) {
        List<BasicRoomCard> basicRoomCardList  = hotelDetail.getBasicRoomCardList();
        if (CollectionUtils.isEmpty(basicRoomCardList)) {
            return new HashMap<>(0);
        }

        Map<String, RoomCard> roomCardMap = new HashMap<>();
        for (BasicRoomCard basicRoomCard : basicRoomCardList) {
            if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                continue;
            }
            for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                if (roomCard == null
                        || StringUtils.isBlank(roomCard.getProductId())
                        || roomCard.getRoomPrice() == null
                        || !Boolean.TRUE.equals(commonGateway.checkCustomPrice(roomCard.getRoomPrice().getAvgPriceIncludeTax()))
                        || !Boolean.TRUE.equals(roomCard.getCanReserve())) {
                    continue;
                }
                roomCardMap.put(roomCard.getProductId(), roomCard);
            }
        }

        return roomCardMap;
    }

}
