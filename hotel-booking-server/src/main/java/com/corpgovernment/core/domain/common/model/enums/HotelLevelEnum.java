package com.corpgovernment.core.domain.common.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/5/9
 */
@Getter
@AllArgsConstructor
public enum HotelLevelEnum {

    ECONOMICAL("经济型", new HashSet<>(Arrays.asList(1, 2))),
    COMFORTABLE("舒适型", new HashSet<>(Collections.singletonList(3))),
    PREMIUM_TYPE("高档型", new HashSet<>(Collections.singletonList(4))),
    DELUXE("豪华型", new HashSet<>(Collections.singletonList(5)))
    ;

    private final String name;
    private final Set<Integer> starSet;

    public static String getNameByStar(Integer star) {
        if (star == null) {
            return "星级未知";
        }
        for (HotelLevelEnum value : HotelLevelEnum.values()) {
            if (value.getStarSet().contains(star)) {
                return value.getName();
            }
        }
        return "星级未知";
    }

}
