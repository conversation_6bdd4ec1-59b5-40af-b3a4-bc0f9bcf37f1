package com.corpgovernment.core.domain.common.service;

import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.domain.common.model.entity.CityInfo;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.SupplierConfig;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-15 22:34
 */
public interface ICommonDomainService {

    String getStayTime();

    Boolean setStayTime();

    String getCorpShortName();

    Integer getSupplierTimeOut();

    BigDecimal getRoomNightNum(String checkInDate, String checkOutDate, Integer roomQuantity);

    Boolean checkCustomPrice(Price price);

    void setViewedHotel(List<String> viewedHotelIdList);
    
    List<String> getOverLimitModeList(List<String> overLimitModeList, List<String> paymentMethodList);
    
    CityInfo getCityInfo(Double lon, Double lat);
    
    SupplierConfig getSupplierConfig(String supplierCode);
    
    String getSystemSupplierCode(String supplierCode);
    
    String zoomOutPicture(String supplierCode, String pictureUrl);
    
    List<String> zoomOutPictureList(String supplierCode, List<String> pictureUrlList);
    
    Double getDistance(Double lat1, Double lon1, Double lat2, Double lon2);
    
    BigDecimal getCustomPrice(Price price);
    
    Boolean openFeature(String feature);

}
