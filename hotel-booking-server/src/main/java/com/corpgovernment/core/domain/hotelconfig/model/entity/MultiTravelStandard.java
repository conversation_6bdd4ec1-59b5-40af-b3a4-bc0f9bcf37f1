package com.corpgovernment.core.domain.hotelconfig.model.entity;

import com.corpgovernment.core.domain.hotelpage.model.enums.SortDirectionEnum;
import com.corpgovernment.core.domain.hotelpage.model.enums.SortTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-22 21:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MultiTravelStandard {

    private List<LadderTravelStandard> ladderTravelStandardList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LadderTravelStandard {

        private Integer sort;

        private String hotelDistance;

        private Integer hotelQuantity;

        private SortTypeEnum sortTypeEnum;

        private SortDirectionEnum sortDirectionEnum;

        private List<Integer> starList;

        private List<String> brandIdList;

        private Boolean hasBreakfast;

        private BigDecimal minPrice;

        private BigDecimal maxPrice;

    }

}
