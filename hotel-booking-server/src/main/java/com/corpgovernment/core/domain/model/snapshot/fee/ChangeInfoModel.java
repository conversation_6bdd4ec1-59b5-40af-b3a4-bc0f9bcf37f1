package com.corpgovernment.core.domain.model.snapshot.fee;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @create 2024-09-19 13:10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChangeInfoModel {
    
    private Boolean failCheckAvail;
    
    private Boolean changePrice;
    
    private Boolean changeExtraTax;
    
    private Boolean changeCancelPolicy;
    
}
