package com.corpgovernment.core.domain.hoteldetail.gateway;

import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPackage;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15
 */
public interface IQueryRoomPackageListGateway {

    List<RoomPackage> queryRoomPackageList(SupplierProduct supplierProduct, List<String> packageIdList);

}
