package com.corpgovernment.core.domain.hoteldetail.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.annotation.Resource;

import com.corpgovernment.core.domain.hoteldetail.model.entity.HourlyRoomInfo;
import org.springframework.stereotype.Service;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.common.tuple.Tuple2;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.constant.HotelCoreConstant;
import com.corpgovernment.core.domain.common.gateway.ICommonGateway;
import com.corpgovernment.core.domain.common.gateway.IHotelIndicatorGateway;
import com.corpgovernment.core.domain.common.model.entity.Price;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.ShuntEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.core.domain.hoteldetail.gateway.IHotelDetailGateway;
import com.corpgovernment.core.domain.hoteldetail.gateway.IQueryHotelDetailGateway;
import com.corpgovernment.core.domain.hoteldetail.gateway.IQueryRoomPackageListGateway;
import com.corpgovernment.core.domain.hoteldetail.model.entity.BasicRoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.CancelPolicy;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelBaseInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelBonusPoint;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetail;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetailFilter;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelDetailRequest;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelFacility;
import com.corpgovernment.core.domain.hoteldetail.model.entity.HotelPolicyService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.OverLimitInfo;
import com.corpgovernment.core.domain.hoteldetail.model.entity.QueryHotelDetailRequest;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomCard;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomControl;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPackage;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPolicyService;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomPrice;
import com.corpgovernment.core.domain.hoteldetail.model.entity.RoomSupplier;
import com.corpgovernment.core.domain.hoteldetail.model.entity.SupplierStarInfo;
import com.corpgovernment.core.domain.hoteldetail.model.enums.BalanceTypeEnum;
import com.corpgovernment.core.domain.hoteldetail.model.enums.CancelRuleEnum;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelSupplier;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.Md5Utils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-19 15:14
 */
@Service
@Slf4j
public class HotelDetailDomainService implements IHotelDetailDomainService {

    @Resource
    private IHotelDetailGateway hotelDetailGateway;

    @Resource
    private ICommonGateway commonGateway;

    @Resource
    private IQueryHotelDetailGateway queryHotelDetailGateway;

    @Resource
    private IQueryRoomPackageListGateway queryRoomPackageListGateway;

    @Resource(name = "queryHotelDetailThreadPool")
    private ThreadPoolExecutor queryHotelDetailThreadPool;

    @Resource(name = "queryRoomPackageThreadPool")
    private ThreadPoolExecutor queryRoomPackageThreadPool;

    @Resource
    private IHotelIndicatorGateway hotelIndicatorGateway;

    @Override
    @BusinessBehaviorMonitor
    public HotelDetail getHotelDetail(HotelDetailRequest hotelDetailRequest, Boolean useCache) {
        Boolean hitCache = null;
        HotelDetail hotelDetail = null;
        try {
            // 缓存key
            String hotelDetailCacheKey = buildHotelDetailCacheKey(hotelDetailRequest);

            // 允许使用缓存
            if (Boolean.TRUE.equals(useCache)) {
                hotelDetail = hotelDetailGateway.getHotelDetail(hotelDetailCacheKey);
                if (hotelDetail != null) {
                    hitCache = true;
                    return hotelDetail;
                }
            }

            // 调用供应商获取酒店详情
            Map<String, HotelDetail> hotelDetailMap = getHotelDetailFromSupplier(hotelDetailRequest);

            // 调用供应商获取酒店套餐
            Map<String, RoomPackage> roomPackage = getRoomPackage(hotelDetailRequest, hotelDetailMap);

            // 组装酒店套餐
            assembleRoomPackage(hotelDetailMap, roomPackage);

            // 聚合酒店详情
            hotelDetail = mergeHotelDetailMap(hotelDetailMap, hotelDetailRequest);

            // 排序
            sortBasicRoomCardList(hotelDetail, hotelDetailRequest.getPrioritySupplierCodeList(), hotelDetailRequest.getHotelRoomSortRule());

            // 缓存
            hotelDetailGateway.setHotelDetail(hotelDetailCacheKey, hotelDetail);

            return hotelDetail;
        } finally {
            hotelIndicatorGateway.getHotelDetail(useCache, hitCache, hotelDetailRequest, hotelDetail);
        }

    }

    @Override
    @BusinessBehaviorMonitor
    public Boolean verifyTravelStandard(HotelDetail hotelDetail,
                                        String token,
                                        String travelStandardMark,
                                        BizTypeEnum bizTypeEnum,
                                        BigDecimal roomNightNum,
                                        List<String> paymentMethodList,
                                        Boolean overseasHotelControlIncludeExtraTax,
                                        PriceControlStrategyEnum priceControlStrategyEnum) {
        try {
            return hotelDetailGateway.verifyTravelStandard(
                    hotelDetail,
                    token,
                    travelStandardMark,
                    bizTypeEnum,
                    roomNightNum,
                    paymentMethodList,
                    overseasHotelControlIncludeExtraTax,
                    priceControlStrategyEnum);
        } finally {
            hotelIndicatorGateway.verifyTravelStandard(hotelDetail);
        }
    }

    @Override
    public void processHotelBonusPoint(HotelDetail hotelDetail) {
        if (hotelDetail == null) {
            return;
        }

        // 酒店积分map
        Map<String, HotelBonusPoint> hotelBonusPointInfoMap = Null.or(hotelDetailGateway.getHotelBonusPointInfoMap(), new HashMap<>(0));

        for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
            if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                continue;
            }
            for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                if (roomCard == null) {
                    continue;
                }
                RoomPolicyService roomPolicyService = roomCard.getRoomPolicyService();
                if (roomPolicyService != null && Boolean.TRUE.equals(roomPolicyService.getHotelBonusPoint())) {
                    HotelBonusPoint hotelBonusPoint = hotelBonusPointInfoMap.get(roomCard.getSupplierCode() + roomCard.getGroupId());
                    if (hotelBonusPoint != null) {
                        roomPolicyService.setHotelBonusPoint(true);
                        HotelBaseInfo hotelBaseInfo = hotelDetail.getHotelBaseInfo();
                        if (hotelBaseInfo != null && StringUtils.isBlank(hotelBaseInfo.getGroupName())) {
                            hotelBaseInfo.setGroupName(hotelBonusPoint.getGroupName());
                        }
                        if (CollectionUtils.isEmpty(hotelDetail.getHotelBonusPointDescList())) {
                            hotelDetail.setHotelBonusPointDescList(hotelBonusPoint.getHotelDetailPageRuleDescList());
                        }
                    } else {
                        roomPolicyService.setHotelBonusPoint(false);
                    }
                }
            }
        }
    }

    @Override
    public void filterRoom(HotelDetail hotelDetail, HotelDetailFilter hotelDetailFilter) {
        if (hotelDetail == null || hotelDetailFilter == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return;
        }
        
        // 屏蔽现付房型
        if (Boolean.TRUE.equals(commonGateway.openFeature(ShuntEnum.CASH_ROOM_SHIELD.getCode()))) {
            hotelDetailFilter.setOnlinePayFilter(true);
        }

        // 供应商标签过滤
        Map<String, RoomSupplier> roomSupplierMap = new HashMap<>();
        List<RoomSupplier> supplierList = hotelDetail.getSupplierList();
        if (CollectionUtils.isNotEmpty(supplierList)) {
            for (RoomSupplier roomSupplier : supplierList) {
                if (roomSupplier == null || StringUtils.isBlank(roomSupplier.getSupplierCode())) {
                    continue;
                }
                roomSupplier.setMinRoomPrice(null);
                roomSupplierMap.put(roomSupplier.getSupplierCode(), roomSupplier);
            }
        }

        // 房型过滤
        List<BasicRoomCard> basicRoomCardList = new ArrayList<>();
        for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
            if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                continue;
            }
            List<RoomCard> roomCardList = new ArrayList<>();
            for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                if (roomCard == null || roomCard.getRoomPrice() == null) {
                    continue;
                }
                // 筛选过滤
                OverLimitInfo overLimitInfo = roomCard.getOverLimitInfo();
                if ((Boolean.TRUE.equals(hotelDetailFilter.getTravelStandardFilter()) && (overLimitInfo != null && CollectionUtils.isNotEmpty(overLimitInfo.getOverLimitRuleNameList()))) ||
                        (Boolean.TRUE.equals(hotelDetailFilter.getProtocolRoomFilter()) && roomCard.getProtocolType() == null) ||
                        (Boolean.TRUE.equals(hotelDetailFilter.getHasBreakfastFilter()) && Optional.ofNullable(roomCard.getRoomPolicyService()).map(RoomPolicyService::getBreakfastCount).orElse(0) == 0) ||
                        (Boolean.TRUE.equals(hotelDetailFilter.getJustifyConfirmFilter()) && !Boolean.TRUE.equals(Optional.ofNullable(roomCard.getRoomPolicyService()).map(RoomPolicyService::getJustifyConfirm).orElse(null))) ||
                        (Boolean.TRUE.equals(hotelDetailFilter.getFreeCancelFilter()) && !Arrays.asList(CancelRuleEnum.FREE, CancelRuleEnum.LADDER, CancelRuleEnum.TIME_LIMIT).contains(Optional.ofNullable(roomCard.getRoomPolicyService()).map(RoomPolicyService::getCancelPolicy).map(CancelPolicy::getCancelRuleEnum).orElse(null))) ||
                        (Boolean.TRUE.equals(hotelDetailFilter.getOnlinePayFilter()) && (roomCard.getBalanceTypeEnum() != BalanceTypeEnum.PP && roomCard.getBalanceTypeEnum() != BalanceTypeEnum.USE_FG)) ||
                        (Boolean.TRUE.equals(hotelDetailFilter.getHotelBonusPointFilter()) && !Boolean.TRUE.equals(Optional.ofNullable(roomCard.getRoomPolicyService()).map(RoomPolicyService::getHotelBonusPoint).orElse(null))) ||
                        (Boolean.TRUE.equals(hotelDetailFilter.getFullAmountOnlinePayFilter()) && Boolean.FALSE.equals(judgeFullAmountOnlinePay(roomCard))) ||
                        (Boolean.TRUE.equals(hotelDetailFilter.getHourlyRoomFilter()) && !Optional.ofNullable(roomCard.getHourlyRoomInfo()).map(HourlyRoomInfo::getHourlyRoom).orElse(false))) {
                    continue;
                }
                // 供应商起价重算
                RoomSupplier roomSupplier = roomSupplierMap.get(roomCard.getSupplierCode());
                if (roomSupplier != null) {
                    RoomPrice minRoomPrice = roomSupplier.getMinRoomPrice();
                    if (minRoomPrice == null || !Price.checkCustomPrice(minRoomPrice.getFinalAvgPrice())
                            || (Price.checkCustomPrice(roomCard.getRoomPrice().getFinalAvgPrice()) && roomCard.getRoomPrice().getFinalAvgPrice().getCustomPrice().compareTo(minRoomPrice.getFinalAvgPrice().getCustomPrice()) < 0)) {
                        roomSupplier.setMinRoomPrice(roomCard.getRoomPrice());
                        roomSupplierMap.put(roomCard.getSupplierCode(), roomSupplier);
                    }
                }
                // 供应商过滤
                if (StringUtils.isNotBlank(hotelDetailFilter.getSupplierCodeFilter()) && !StringUtils.equalsIgnoreCase(hotelDetailFilter.getSupplierCodeFilter(), roomCard.getSupplierCode())) {
                    continue;
                }
                roomCardList.add(roomCard);
            }

            if (CollectionUtils.isNotEmpty(roomCardList)) {
                basicRoomCard.setRoomCardList(roomCardList);
                hotelDetailGateway.handleBasicRoomCardAggAttr(basicRoomCard);
                basicRoomCardList.add(basicRoomCard);
            }
        }
        hotelDetail.setSupplierList(new ArrayList<>(roomSupplierMap.values()));
        hotelDetail.setBasicRoomCardList(basicRoomCardList);
    }

    @Override
    public List<BasicRoomCard> unfoldBasicRoomCard(HotelDetail hotelDetail) {
        if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return null;
        }

        List<BasicRoomCard> resultList = new ArrayList<>();
        for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
            if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                continue;
            }

            for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                if (roomCard == null) {
                    continue;
                }

                BasicRoomCard tmpBasicRoomCard = BasicRoomCard.builder()
                        .hotelId(basicRoomCard.getHotelId())
                        .supplierCode(basicRoomCard.getSupplierCode())
                        .supplierName(basicRoomCard.getSupplierName())
                        .basicRoomId(basicRoomCard.getBasicRoomId())
                        .pictureList(basicRoomCard.getPictureList())
                        .name(basicRoomCard.getName())
                        .bedDesc(basicRoomCard.getBedDesc())
                        .areaDesc(basicRoomCard.getAreaDesc())
                        .floorDesc(basicRoomCard.getFloorDesc())
                        .roomCardList(Collections.singletonList(roomCard)).build();

                hotelDetailGateway.handleBasicRoomCardAggAttr(tmpBasicRoomCard);

                resultList.add(tmpBasicRoomCard);
            }
        }

        return resultList;
    }

    @Override
    public void handleBasicRoomCardAggAttr(BasicRoomCard basicRoomCard) {
        hotelDetailGateway.handleBasicRoomCardAggAttr(basicRoomCard);
    }

    @Override
    public void sortBasicRoomCardList(HotelDetail hotelDetail, List<String> prioritySupplierCodeList, Integer hotelRoomSortRule) {
        if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return;
        }
        // 房型排序
        for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
            if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                continue;
            }
            basicRoomCard.setRoomCardList(basicRoomCard.getRoomCardList().stream().filter(Objects::nonNull).sorted(
                            Comparator.comparing((RoomCard item) -> Null.or(item.getCanReserve(), false), Comparator.reverseOrder())
                                    .thenComparing(item -> getProtocolSortNum(item.getProtocolType(), hotelRoomSortRule))
                                    .thenComparing(item -> Optional.ofNullable(item.getRoomPrice()).map(RoomPrice::getFinalAvgPrice).map(Price::getCustomPrice).orElse(Price.getMaxPrice()))
                                    .thenComparing(item -> getSupplierSortNum(item.getSupplierCode(), prioritySupplierCodeList)))
                    .collect(Collectors.toList()));
        }
        // 基础房型排序
        hotelDetail.setBasicRoomCardList(hotelDetail.getBasicRoomCardList().stream().filter(Objects::nonNull).sorted(
                        Comparator.comparing((BasicRoomCard item) -> Null.or(item.getCanReserve(), false), Comparator.reverseOrder())
                                .thenComparing(item -> getProtocolSortNum(item.getProtocolType(), hotelRoomSortRule))
                                .thenComparing(item -> Optional.ofNullable(item.getMinRoomPrice()).map(RoomPrice::getFinalAvgPrice).map(Price::getCustomPrice).orElse(Price.getMaxPrice()))
                                .thenComparing(item -> getSupplierSortNum(item.getSupplierCode(), prioritySupplierCodeList)))
                .collect(Collectors.toList()));
    }
    
    @Override
    public HotelDetail handleRoomControl(HotelDetail hotelDetail, Boolean overseasHotelControlIncludeExtraTax) {
        if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
            return hotelDetail;
        }
        
        // 遍历物理房型
        for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
            if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                continue;
            }
            
            // 遍历房间卡片
            for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                if (roomCard == null) {
                    continue;
                }
                
                roomCard.setRoomControl(RoomControl.builder()
                        .reminderApplyTripOverLimit(roomCard.getApplyTripOverLimitReminder() != null)
                        .showExtraTaxTip(getShowExtraTaxTip(roomCard, overseasHotelControlIncludeExtraTax))
                        .build());
            }
        }
        
        return hotelDetail;
    }
    
    @Override
    public HotelDetail verifyApplyTrip(HotelDetail hotelDetail, String travelId, String productType) {
        return hotelDetailGateway.verifyApplyTrip(hotelDetail, travelId, productType);
    }
    
    private Boolean getShowExtraTaxTip(RoomCard roomCard, Boolean overseasHotelControlIncludeExtraTax) {
        if (overseasHotelControlIncludeExtraTax == null || Boolean.FALSE.equals(overseasHotelControlIncludeExtraTax)) {
            return Boolean.FALSE;
        }
        
        BigDecimal totalExtraTaxCustomPrice = Optional.ofNullable(roomCard)
                .map(RoomCard::getRoomPrice)
                .map(RoomPrice::getTotalExtraTax)
                .map(Price::getCustomPrice).orElse(null);
        
        return totalExtraTaxCustomPrice != null && totalExtraTaxCustomPrice.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 判断满额在线付房型
     * @param roomCard 房型卡片
     * @return true：满额在线付房型 false：不是满额在线付房型
     */
    private Boolean judgeFullAmountOnlinePay(RoomCard roomCard) {
        BigDecimal totalExtraTaxCustomPrice = Optional.ofNullable(roomCard)
                .map(RoomCard::getRoomPrice)
                .map(RoomPrice::getTotalExtraTax)
                .map(Price::getCustomPrice).orElse(null);
        BalanceTypeEnum balanceTypeEnum = Optional.ofNullable(roomCard)
                .map(RoomCard::getBalanceTypeEnum).orElse(null);
        // 在线付 且 没有税费
        return (balanceTypeEnum == BalanceTypeEnum.PP || balanceTypeEnum == BalanceTypeEnum.USE_FG)
                && (totalExtraTaxCustomPrice == null || totalExtraTaxCustomPrice.compareTo(BigDecimal.ZERO) == 0);
    }
    
    private Integer getProtocolSortNum(Integer protocolType, Integer hotelRoomSortRule) {
        if (hotelRoomSortRule == null || hotelRoomSortRule != 1) {
            return Integer.MAX_VALUE;
        }
        return protocolType == null ? Integer.MAX_VALUE : 1;
    }

    private HotelDetail mergeHotelDetailMap(Map<String, HotelDetail> hotelDetailMap, HotelDetailRequest hotelDetailRequest) {
        if (CollectionUtils.isEmpty(hotelDetailMap)) {
            return null;
        }

        List<HotelDetail> hotelDetailList = hotelDetailMap.values().stream().filter(Objects::nonNull)
                .sorted(Comparator.comparing(item -> getSupplierSortNum(item.getSupplierCode(), hotelDetailRequest.getPrioritySupplierCodeList()))).collect(Collectors.toList());

        HotelDetail hotelDetail = HotelDetail.builder()
                .hotelBaseInfo(
                        mergeHotelBaseInfo(hotelDetailList.stream().filter(Objects::nonNull).map(t->{
                            return new Tuple2<>(t.getSupplierCode(), t.getHotelBaseInfo());
                                }).collect(Collectors.toList())
                                , hotelDetailRequest.getBaseSupplierCode()))
                .hotelFacility(mergeHotelFacility(hotelDetailList.stream().filter(Objects::nonNull).map(HotelDetail::getHotelFacility).collect(Collectors.toList())))
                .hotelPolicyService(mergeHotelPolicyService(hotelDetailList.stream().filter(Objects::nonNull).map(HotelDetail::getHotelPolicyService).collect(Collectors.toList())))
                .reservationNoticeList(hotelDetailList.stream().filter(item -> item != null && CollectionUtils.isNotEmpty(item.getReservationNoticeList())).map(HotelDetail::getReservationNoticeList).findFirst().orElse(null))
                .nearByGroupList(hotelDetailList.stream().filter(item -> item != null && CollectionUtils.isNotEmpty(item.getNearByGroupList())).map(HotelDetail::getNearByGroupList).findFirst().orElse(null))
                .basicRoomCardList(hotelDetailList.stream().filter(item -> item != null && CollectionUtils.isNotEmpty(item.getBasicRoomCardList())).map(HotelDetail::getBasicRoomCardList).flatMap(List::stream).collect(Collectors.toList())).build();

        hotelDetailGateway.handleHotelDetailAggAttr(hotelDetail);

        hotelDetail.setBasicRoomCardList(mergeBasicRoomCardList(hotelDetail.getBasicRoomCardList(), getHotelIdListMap(hotelDetailMap), hotelDetailRequest.getPrioritySupplierCodeList()));
        
        // 平台虚拟ID生成
        createVirtualId(hotelDetail);

        return hotelDetail;
    }
    
    private void createVirtualId(HotelDetail hotelDetail) {
        if (hotelDetail == null) {
            return;
        }
        
        List<BasicRoomCard> basicRoomCardList = hotelDetail.getBasicRoomCardList();
        if (CollectionUtils.isEmpty(basicRoomCardList)) {
            return;
        }
        
        Set<String> antiRepeatSet = new HashSet<>();
        for (BasicRoomCard basicRoomCard : basicRoomCardList) {
            if (basicRoomCard == null) {
                continue;
            }
            
            // 生成虚拟物理房型ID
            String virtualBasicRoomId = createVirtualBasicRoomId(basicRoomCard, antiRepeatSet);
            
            // 赋值
            basicRoomCard.setVirtualBasicRoomId(virtualBasicRoomId);
            List<RoomCard> roomCardList = basicRoomCard.getRoomCardList();
            if (roomCardList != null) {
                for (RoomCard roomCard : roomCardList) {
                    if (roomCard == null) {
                        continue;
                    }
                    roomCard.setVirtualBasicRoomId(virtualBasicRoomId);
                }
            }
        }
    }
    
    private String createVirtualBasicRoomId(BasicRoomCard basicRoomCard, Set<String> antiRepeatSet) {
        if (antiRepeatSet == null) {
            return null;
        }
        
        String virtualBasicRoomIdPrefix = createVirtualBasicRoomIdPrefix(basicRoomCard);
        
        for (int i = 0; i < 100; i++) {
            String virtualBasicRoomIdPostfix = StrUtil.padPre(String.valueOf(i), 4, '0');
            String virtualBasicRoomId =  virtualBasicRoomIdPrefix + virtualBasicRoomIdPostfix;
            
            // 查重
            if (!antiRepeatSet.contains(virtualBasicRoomId)) {
                antiRepeatSet.add(virtualBasicRoomId);
                return virtualBasicRoomId;
            }
        }
        
        return null;
    }
    
    private String createVirtualBasicRoomIdPrefix(BasicRoomCard basicRoomCard) {
        if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
            return null;
        }
        
        // 供应商信息
        Set<String> prefixSet = new HashSet<>();
        for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
            if (roomCard == null || StringUtils.isBlank(roomCard.getSupplierCode())) {
                continue;
            }
            
            StringBuilder sb = new StringBuilder();
            prefixSet.add(sb
                    .append(roomCard.getSupplierCode()).append("-")
                    .append(roomCard.getHotelId()).append("-")
                    .append(roomCard.getBasicRoomId())
                    .toString());
        }
        
        // 构建ID
        StringBuilder virtualBasicRoomIdPrefix = new StringBuilder();
        prefixSet.stream()
                .sorted()
                .collect(Collectors.toList())
                .forEach(item -> {
                    virtualBasicRoomIdPrefix.append(item).append("&");
                });
        
        return virtualBasicRoomIdPrefix.toString();
    }
    
    private Map<String, List<String>> getHotelIdListMap(Map<String, HotelDetail> hotelDetailMap) {
        if (CollectionUtils.isEmpty(hotelDetailMap)) {
            return null;
        }

        Map<String, List<String>> hotelIdListMap = new HashMap<>();

        for (HotelDetail hotelDetail : hotelDetailMap.values()) {
            if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
                continue;
            }
            String supplierCode = hotelDetail.getSupplierCode();
            List<String> hotelIdList = hotelIdListMap.getOrDefault(supplierCode, new ArrayList<>());
            hotelIdList.add(hotelDetail.getHotelId());
            hotelIdListMap.put(supplierCode, hotelIdList);
        }

        return hotelIdListMap;
    }

    private List<BasicRoomCard> mergeBasicRoomCardList(List<BasicRoomCard> basicRoomCardList, Map<String, List<String>> hotelIdListMap, List<String> prioritySupplierCodeList) {
        if (CollectionUtils.isEmpty(basicRoomCardList)) {
            return basicRoomCardList;
        }

        // 全部匹配关系
        Map<RoomSupplier, Set<RoomSupplier>> allRoomRelationMap = new HashMap<>();
        // 携程匹配关系
        hotelDetailGateway.getCtripRoomRelationMap(allRoomRelationMap, hotelIdListMap);
        // 自匹配关系
        hotelDetailGateway.getSelfRoomRelationMap(allRoomRelationMap, basicRoomCardList);
        // 直连特殊逻辑：直连匹配关系
        hotelDetailGateway.getDirectRoomRelationMap(allRoomRelationMap, basicRoomCardList);

        // 无需匹配
        if (CollectionUtils.isEmpty(allRoomRelationMap)) {
            return basicRoomCardList;
        }

        // 房型卡片
        Map<RoomSupplier, BasicRoomCard> basicRoomCardMap = new HashMap<>();
        for (BasicRoomCard basicRoomCard : basicRoomCardList) {
            basicRoomCardMap.put(RoomSupplier.builder()
                    .supplierCode(basicRoomCard.getSupplierCode())
                    .hotelId(basicRoomCard.getHotelId())
                    .basicRoomId(basicRoomCard.getBasicRoomId()).build(), basicRoomCard);
        }

        // 开始合并
        List<BasicRoomCard> resultList = new ArrayList<>();
        Set<RoomSupplier> viewRoomSet = new HashSet<>();
        for (BasicRoomCard basicRoomCard : basicRoomCardList) {
            if (basicRoomCard == null) {
                continue;
            }

            RoomSupplier roomSupplier = RoomSupplier.builder()
                    .supplierCode(basicRoomCard.getSupplierCode())
                    .hotelId(basicRoomCard.getHotelId())
                    .basicRoomId(basicRoomCard.getBasicRoomId()).build();

            // 如果出现过就直接跳出
            if (viewRoomSet.contains(roomSupplier)) {
                continue;
            }

            // 获取关系
            Set<RoomSupplier> roomSupplierSet = allRoomRelationMap.getOrDefault(roomSupplier, new HashSet<>(0));

            // 记录出现
            viewRoomSet.addAll(roomSupplierSet);

            // 如果不存在匹配关系直接放入
            if (roomSupplierSet.size() < 2) {
                resultList.add(basicRoomCard);
                continue;
            }

            // 聚合
            List<BasicRoomCard> tmpList = roomSupplierSet.stream().map(basicRoomCardMap::get).filter(Objects::nonNull)
                    .sorted(Comparator.comparing(item -> getSupplierSortNum(item.getSupplierCode(), prioritySupplierCodeList))).collect(Collectors.toList());
            BasicRoomCard result = BasicRoomCard.builder()
                    .pictureList(tmpList.stream().filter(item -> item != null && CollectionUtils.isNotEmpty(item.getPictureList())).map(BasicRoomCard::getPictureList).findFirst().orElse(null))
                    .name(tmpList.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getName())).map(BasicRoomCard::getName).findFirst().orElse(null))
                    .bedDesc(mergeBedDesc(tmpList.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getBedDesc())).map(BasicRoomCard::getBedDesc).collect(Collectors.toList())))
                    .areaDesc(mergeAreaDesc(tmpList.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getAreaDesc())).map(BasicRoomCard::getAreaDesc).collect(Collectors.toList())))
                    .floorDesc(mergeFloorDesc(tmpList.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getFloorDesc())).map(BasicRoomCard::getFloorDesc).collect(Collectors.toList())))
                    .roomCardList(tmpList.stream().filter(item -> item != null && CollectionUtils.isNotEmpty(item.getRoomCardList())).map(BasicRoomCard::getRoomCardList).flatMap(List::stream).collect(Collectors.toList())).build();

            hotelDetailGateway.handleBasicRoomCardAggAttr(result);

            resultList.add(result);
        }

        return resultList;
    }

    private String mergeFloorDesc(List<String> floorDescList) {
        if (CollectionUtils.isEmpty(floorDescList)) {
            return null;
        }

        StringBuilder floorDesc = new StringBuilder();
        try {
            TreeSet<Integer> set = new TreeSet<>();
            // 解析
            for (String item : floorDescList) {
                String replace = item.replace(" ", "").replace("F", "").replace("/", ",").replace("f", "").replace("层", "").replace("，", ",");
                // 分割,
                String[] split = replace.split(",");
                for (String str : split) {
                    String[] split1 = str.split("-");
                    if (split1.length == 1) {
                        set.add(Integer.valueOf(split1[0]));
                    }
                    else if (split1.length == 2) {
                        IntStream.rangeClosed(Integer.parseInt(split1[0]), Integer.parseInt(split1[1])).forEach(set::add);
                    }
                    else {
                        throw new RuntimeException("解析失败:" + item);
                    }
                }
            }
            // 融合
            Integer prev = null;
            Integer start = null;
            for (Integer current : set) {
                if (start == null) {
                    start = current;
                    floorDesc.append(start);
                }
                else if (current - prev != 1) {
                    if (!start.equals(prev)) {
                        if (prev - start != 1) {
                            floorDesc.append("-").append(prev);
                        }
                        else {
                            floorDesc.append(",").append(prev);
                        }
                    }
                    floorDesc.append(",").append(current);
                    start = current;
                }
                prev = current;
            }
            if (start != null && !start.equals(prev)) {
                floorDesc.append("-").append(prev);
            }
            return floorDesc.append("F").toString();
        } catch (Exception e) {
            log.error("楼层融合失败:", e);
            return floorDescList.get(0);
        }

    }

    private String mergeAreaDesc(List<String> areaDescList) {
        if (CollectionUtils.isEmpty(areaDescList)) {
            return null;
        }
        try {
            TreeSet<Integer> set = new TreeSet<>();
            // 解析
            for (String item : areaDescList) {
                String[] tmp = item.replace(" ", "").replace("㎡", "").replace("m²", "").replace("平方米", "").split("-");
                if (tmp.length > 2) {
                    throw new RuntimeException("解析失败:" + item);
                }
                for (String str : tmp) {
                    set.add(Integer.parseInt(str));
                }
            }
            // 融合
            if (set.size() == 1) {
                return set.first() + "㎡";
            }
            else {
                return set.first() + "-" + set.last() + "㎡";
            }
        } catch (Exception e) {
            log.error("面积融合失败:", e);
            return areaDescList.get(0);
        }
    }

    private String mergeBedDesc(List<String> bedDescList) {
        if (CollectionUtils.isEmpty(bedDescList)) {
            return null;
        }
        return StringUtils.join(new TreeSet<>(bedDescList), "或");
    }

    private HotelPolicyService mergeHotelPolicyService(List<HotelPolicyService> hotelPolicyServiceList) {
        if (CollectionUtils.isEmpty(hotelPolicyServiceList)) {
            return null;
        }

        HotelPolicyService result = new HotelPolicyService();

        for (HotelPolicyService hotelPolicyService : hotelPolicyServiceList) {
            if (hotelPolicyService == null) {
                continue;
            }

            if (result.getArrivalAndDeparturePolicy() == null) {
                result.setArrivalAndDeparturePolicy(hotelPolicyService.getArrivalAndDeparturePolicy());
            }
            if (StringUtils.isBlank(result.getPetPolicyDesc())) {
                result.setPetPolicyDesc(hotelPolicyService.getPetPolicyDesc());
            }
            if (CollectionUtils.isEmpty(result.getPaymentToolList())) {
                result.setPaymentToolList(hotelPolicyService.getPaymentToolList());
            }
            if (result.getChildAndAddBedPolicy() == null) {
                result.setChildAndAddBedPolicy(hotelPolicyService.getChildAndAddBedPolicy());
            }
            if (result.getMealPolicy() == null) {
                result.setMealPolicy(hotelPolicyService.getMealPolicy());
            }
        }

        return result;
    }

    private HotelFacility mergeHotelFacility(List<HotelFacility> hotelFacilityList) {
        if (CollectionUtils.isEmpty(hotelFacilityList)) {
            return null;
        }

        HotelFacility result = new HotelFacility();

        for (HotelFacility hotelFacility : hotelFacilityList) {
            if (hotelFacility == null) {
                continue;
            }
            if (CollectionUtils.isEmpty(result.getParkingLotList())) {
                result.setParkingLotList(hotelFacility.getParkingLotList());
            }
            if (CollectionUtils.isEmpty(result.getChargingPileList())) {
                result.setChargingPileList(hotelFacility.getChargingPileList());
            }
            if (CollectionUtils.isEmpty(result.getFacilityGroupList())) {
                result.setFacilityGroupList(hotelFacility.getFacilityGroupList());
            }
        }

        return result;
    }


    /**
     * 除了星级的属性按基础供应商为准外，别的都按排序规则定
     */
    private HotelBaseInfo mergeHotelBaseInfo(List<Tuple2<String, HotelBaseInfo>> hotelBaseInfoTupleList, String baseSupplierCode) {
        if (CollectionUtils.isEmpty(hotelBaseInfoTupleList)) {
            return null;
        }

        HotelBaseInfo result = new HotelBaseInfo();
        Map<String, SupplierStarInfo> supplierStarInfo = Maps.newHashMapWithExpectedSize(hotelBaseInfoTupleList.size());
        for (Tuple2<String, HotelBaseInfo> tuple : hotelBaseInfoTupleList) {
            HotelBaseInfo hotelBaseInfo = tuple.getSecond();
            String supplierCode = tuple.getFirst();
            if (hotelBaseInfo == null) {
                continue;
            }
            if (supplierCode.equals(baseSupplierCode)) {
                result.setStar(hotelBaseInfo.getStar());
                result.setStarLicence(hotelBaseInfo.getStarLicence());
                result.setLevelName(hotelBaseInfo.getLevelName());
            }
            supplierStarInfo.put(supplierCode, buildSupplierStarInfo(hotelBaseInfo));

            if (StringUtils.isBlank(result.getName())) {
                result.setName(hotelBaseInfo.getName());
            }
            if (StringUtils.isBlank(result.getNameEn())) {
                result.setNameEn(hotelBaseInfo.getNameEn());
            }
            if (StringUtils.isBlank(result.getAddress())) {
                result.setAddress(hotelBaseInfo.getAddress());
                result.setLon(hotelBaseInfo.getLon());
                result.setLat(hotelBaseInfo.getLat());
            }
            if (CollectionUtils.isEmpty(result.getMapInfoList())) {
                result.setMapInfoList(hotelBaseInfo.getMapInfoList());
            }
            if (StringUtils.isBlank(result.getLogoUrl())) {
                result.setLogoUrl(hotelBaseInfo.getLogoUrl());
            }

            if (StringUtils.isBlank(result.getReviewScore())) {
                result.setReviewScore(hotelBaseInfo.getReviewScore());
            }
            if (StringUtils.isBlank(result.getGroupName())) {
                result.setGroupName(hotelBaseInfo.getGroupName());
            }
            if (StringUtils.isBlank(result.getTelephone())) {
                result.setTelephone(hotelBaseInfo.getTelephone());
            }
            if (StringUtils.isBlank(result.getDistrictName())) {
                result.setDistrictName(hotelBaseInfo.getDistrictName());
            }
            if (StringUtils.isBlank(result.getCityName())) {
                result.setCityName(hotelBaseInfo.getCityName());
            }
            if (StringUtils.isBlank(result.getOpenDateDesc())) {
                result.setOpenDateDesc(hotelBaseInfo.getOpenDateDesc());
            }
            if (StringUtils.isBlank(result.getRenovationDateDesc())) {
                result.setRenovationDateDesc(hotelBaseInfo.getRenovationDateDesc());
            }
            if (CollectionUtils.isEmpty(result.getFacilityList())) {
                result.setFacilityList(hotelBaseInfo.getFacilityList());
            }
            if (CollectionUtils.isEmpty(result.getVideoList())) {
                result.setVideoList(hotelBaseInfo.getVideoList());
            }
            if (CollectionUtils.isEmpty(result.getPictureList())) {
                result.setPictureList(hotelBaseInfo.getPictureList());
            }
        }
        result.setSupplierStarInfo(supplierStarInfo);

        return result;
    }

    private Integer getSupplierSortNum(String supplierCode, List<String> prioritySupplierCodeList) {
        if (CollectionUtils.isEmpty(prioritySupplierCodeList)) {
            return Integer.MAX_VALUE;
        }
        int index = prioritySupplierCodeList.indexOf(supplierCode);
        return index == -1 ? Integer.MAX_VALUE : index;
    }

    private void assembleRoomPackage(Map<String, HotelDetail> hotelDetailMap, Map<String, RoomPackage> roomPackage) {
        if (CollectionUtils.isEmpty(hotelDetailMap) || CollectionUtils.isEmpty(roomPackage)) {
            return;
        }

        for (HotelDetail hotelDetail : hotelDetailMap.values()) {
            if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
                continue;
            }
            for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
                if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                    continue;
                }
                for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                    if (roomCard == null || roomCard.getRoomPackage() == null || roomCard.getRoomPackage().getPackageId() == null) {
                        continue;
                    }
                    String key = buildRoomPackageMapKey(roomCard.getSupplierCode(), roomCard.getRoomPackage().getPackageId());
                    if (StringUtils.isBlank(key)) {
                        continue;
                    }
                    RoomPackage tmpRoomPackage = roomPackage.get(key);
                    if (tmpRoomPackage != null) {
                        roomCard.setRoomPackage(tmpRoomPackage);
                    }
                }
            }
        }

    }

    private Map<String, RoomPackage> getRoomPackage(HotelDetailRequest hotelDetailRequest, Map<String, HotelDetail> hotelDetailMap) {
        if (hotelDetailRequest == null || CollectionUtils.isEmpty(hotelDetailMap)) {
            return null;
        }

        Map<String, SupplierProduct> supplierProductMap = convertSupplierProductList(hotelDetailRequest.getRoomPackageDetailSupplierProductList());
        if (CollectionUtils.isEmpty(supplierProductMap)) {
            return null;
        }

        // 获取需要查询的package
        Map<String, Set<String>> roomPackageIdSetMap = getNeedQueryRoomPackageIdMap(hotelDetailMap);

        // 调用供应商获取酒店套餐
        return getRoomPackageFromSupplier(roomPackageIdSetMap, supplierProductMap);
    }

    private Map<String, Set<String>> getNeedQueryRoomPackageIdMap(Map<String, HotelDetail> hotelDetailMap) {
        if (CollectionUtils.isEmpty(hotelDetailMap)) {
            return null;
        }
        Map<String, Set<String>> roomPackageIdSetMap = new HashMap<>();
        for (HotelDetail hotelDetail : hotelDetailMap.values()) {
            if (hotelDetail == null || CollectionUtils.isEmpty(hotelDetail.getBasicRoomCardList())) {
                continue;
            }
            for (BasicRoomCard basicRoomCard : hotelDetail.getBasicRoomCardList()) {
                if (basicRoomCard == null || CollectionUtils.isEmpty(basicRoomCard.getRoomCardList())) {
                    continue;
                }
                for (RoomCard roomCard : basicRoomCard.getRoomCardList()) {
                    if (roomCard == null || roomCard.getRoomPackage() == null || roomCard.getRoomPackage().getPackageId() == null) {
                        continue;
                    }
                    String supplierCode = roomCard.getSupplierCode();
                    Set<String> tmpSet = roomPackageIdSetMap.getOrDefault(supplierCode, new HashSet<>());
                    tmpSet.add(roomCard.getRoomPackage().getPackageId());
                    roomPackageIdSetMap.put(roomCard.getSupplierCode(), tmpSet);
                }
            }
        }
        return roomPackageIdSetMap;
    }

    private Map<String, RoomPackage> getRoomPackageFromSupplier(Map<String, Set<String>> roomPackageIdSetMap, Map<String, SupplierProduct> supplierProductMap) {
        if (CollectionUtils.isEmpty(roomPackageIdSetMap) || CollectionUtils.isEmpty(supplierProductMap)) {
            return null;
        }

        ConcurrentHashMap<String, RoomPackage> roomPackageMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        roomPackageIdSetMap.forEach((supplierCode, packageIdSet) -> {
            if (CollectionUtils.isEmpty(packageIdSet)) {
                return;
            }
            SupplierProduct supplierProduct = supplierProductMap.get(supplierCode);
            if (supplierProduct == null) {
                return;
            }
            completableFutureList.add(CompletableFuture.runAsync(() -> {
                List<RoomPackage> roomPackageList = queryRoomPackageListGateway.queryRoomPackageList(supplierProduct, new ArrayList<>(packageIdSet));
                if (CollectionUtils.isEmpty(roomPackageList)) {
                    return;
                }
                for (RoomPackage roomPackage : roomPackageList) {
                    String key = buildRoomPackageMapKey(supplierCode, roomPackage.getPackageId());
                    if (StringUtils.isBlank(key)) {
                        continue;
                    }
                    roomPackageMap.put(key, roomPackage);
                }
            }, queryRoomPackageThreadPool));
        });

        try {
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(commonGateway.getSupplierTimeOut(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("请求超时", e);
        }

        return roomPackageMap;
    }

    private Map<String, HotelDetail> getHotelDetailFromSupplier(HotelDetailRequest hotelDetailRequest) {
        if (hotelDetailRequest == null) {
            return null;
        }

        Map<String, SupplierProduct> supplierProductMap = convertSupplierProductList(hotelDetailRequest.getHotelDetailSupplierProductList());
        if (CollectionUtils.isEmpty(supplierProductMap)) {
            return null;
        }

        ConcurrentHashMap<String, HotelDetail> hotelDetailMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();

        for (HotelSupplier hotelSupplier : hotelDetailRequest.getHotelSupplierList()) {
            SupplierProduct supplierProduct = supplierProductMap.get(hotelSupplier.getSupplierCode());
            if (supplierProduct == null) {
                continue;
            }
            completableFutureList.add(CompletableFuture.runAsync(() -> {
                QueryHotelDetailRequest queryHotelDetailRequest = QueryHotelDetailRequest.builder()
                        .supplierProduct(supplierProduct)
                        .hotelId(hotelSupplier.getHotelId())
                        .checkInDate(hotelDetailRequest.getCheckInDate())
                        .checkOutDate(hotelDetailRequest.getCheckOutDate())
                        .roomQuantity(hotelDetailRequest.getRoomQuantity())
                        .guestQuantity(hotelDetailRequest.getGuestQuantity())
                        .abroad(hotelDetailRequest.getAbroad())
                        .resourcePriceIncludeServiceCharge(hotelDetailRequest.getResourcePriceIncludeServiceCharge())
                        .travelMode(hotelDetailRequest.getTravelMode())
                        .hasHourlyRoom(hotelDetailRequest.getHasHourlyRoom())
                        .build();
                
                HotelDetail hotelDetail = queryHotelDetailGateway.queryHotelDetail(queryHotelDetailRequest);
                if (hotelDetail != null) {
                    hotelDetailMap.put(hotelSupplier.getSupplierCode() + hotelSupplier.getHotelId(), hotelDetail);
                }
            }, queryHotelDetailThreadPool));
        }

        try {
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(commonGateway.getSupplierTimeOut(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("请求超时", e);
        }

        return hotelDetailMap;
    }

    private Map<String, SupplierProduct> convertSupplierProductList(List<SupplierProduct> supplierProductList) {
        if (CollectionUtils.isEmpty(supplierProductList)) {
            return null;
        }
        Map<String, SupplierProduct> supplierProductMap = new HashMap<>();
        for (SupplierProduct supplierProduct : supplierProductList) {
            if (supplierProduct == null || StringUtils.isBlank(supplierProduct.getSupplierCode())) {
                continue;
            }
            supplierProductMap.put(supplierProduct.getSupplierCode(), supplierProduct);
        }
        return supplierProductMap;
    }

    private String buildRoomPackageMapKey(String supplierCode, String packageId) {
        if (StringUtils.isBlank(supplierCode) || StringUtils.isBlank(packageId)) {
            return null;
        }
        return supplierCode + packageId;
    }

    /**
     * 构建酒店详情缓存key
     * @param hotelDetailRequest 酒店详情请求
     * @return key
     */
    private String buildHotelDetailCacheKey(HotelDetailRequest hotelDetailRequest) {
        try {
            if (hotelDetailRequest == null) {
                return null;
            }
            // 去除随机信息
            HotelDetailRequest tmpHotelDetailRequest = JsonUtils.parse(JsonUtils.toJsonString(hotelDetailRequest), HotelDetailRequest.class);
            tmpHotelDetailRequest.setHotelDetailSupplierProductList(commonGateway.removeRandomInfo(tmpHotelDetailRequest.getHotelDetailSupplierProductList()));
            tmpHotelDetailRequest.setRoomPackageDetailSupplierProductList(commonGateway.removeRandomInfo(tmpHotelDetailRequest.getRoomPackageDetailSupplierProductList()));
            return HotelCoreConstant.RedisKey.HOTEL_DETAIL + HotelCoreConstant.COLON + Md5Utils.md5Hex(tmpHotelDetailRequest.toString());
        } catch (Exception e) {
            log.error("构建酒店详情缓存key失败", e);
            return null;
        }
    }

    /**
     * 转换供应商返回的星级数据
     * 
     * @param hotelBaseInfo 供应商基础信息
     * @return 星级信息
     */
    private SupplierStarInfo buildSupplierStarInfo(HotelBaseInfo hotelBaseInfo) {
        SupplierStarInfo supplierStarInfo = new SupplierStarInfo();
        supplierStarInfo.setStar(hotelBaseInfo.getStar());
        supplierStarInfo.setStarLicence(hotelBaseInfo.getStarLicence());
        return supplierStarInfo;
    }

}
