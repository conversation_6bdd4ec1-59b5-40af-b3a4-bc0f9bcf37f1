package com.corpgovernment.core.domain.hotelconfig.model.entity;

import com.corpgovernment.constants.BizTypeEnum;
import com.corpgovernment.core.domain.common.model.entity.SupplierProduct;
import com.corpgovernment.core.domain.common.model.enums.HotelForceChummageEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.PriceControlStrategyEnum;
import com.corpgovernment.core.domain.hotelconfig.model.enums.TravelModeEnum;
import com.corpgovernment.core.domain.hotelpage.model.entity.HotelAdvancedFilter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TravelConfig {

    // 支付方式 PPAY个人支付 ACCNT公账支付
    private List<String> paymentMethodList;
    // 产线供应商接口 key operatorType
    private Map<String, List<SupplierProduct>> supplierProductListMap;
    // 配置的供应商
    private List<String> prioritySupplierCodeList;
    // 产线code
    private BizTypeEnum bizTypeEnum;
    // 排序规则
    private Integer hotelListSortRule;
    private Integer hotelRoomSortRule;
    // 星级屏蔽（只有因公）
    private List<Integer> shieldStarList;
    // 因公因私
    private TravelModeEnum travelModeEnum;
    // 紧急预订支付方式 PPAY个人支付 ACCNT公账支付
    private List<String> urgentReservePaymentMethodList;
    // 海外酒店差标管控是否包含到店另付税费
    private Boolean overseasHotelControlIncludeExtraTax;
    // 酒店强制合住
    private HotelForceChummageEnum hotelForceChummageEnum;
    // 酒店强制同性合住
    private Boolean hotelForceSameSexChummage;
    // 房间入住人信息
    private List<HotelRoomCheckInInfo> hotelRoomCheckInInfoList;
    // 未合住RC列表
    private List<RcInfo> rcInfoList;
    // 酒店价格管控策略
    private PriceControlStrategyEnum priceControlStrategyEnum;
    // 是否显示价格包含服务费
    private Boolean resourcePriceIncludeServiceCharge;
    // 国内酒店是否允许预订钟点房
    private Boolean domesticHotelBookHourlyRoom;
    
    public boolean extractFilterWithServiceCharge() {
        if (!Objects.equals(travelModeEnum, TravelModeEnum.PUB)) {
            return false;
        }
        return Optional.ofNullable(resourcePriceIncludeServiceCharge)
                .orElse(false);
    }

}
