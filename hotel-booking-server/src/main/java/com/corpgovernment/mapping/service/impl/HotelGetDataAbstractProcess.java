package com.corpgovernment.mapping.service.impl;


import com.corpgovernment.common.utils.HttpUtils;
import com.corpgovernment.mapping.basic.bo.request.HotelCityRequest;
import com.corpgovernment.mapping.basic.bo.request.HotelInfoRequest;
import com.corpgovernment.mapping.basic.bo.response.HotelCityApiResponse;
import com.corpgovernment.mapping.basic.bo.response.HotelCityResponse;
import com.corpgovernment.mapping.basic.bo.response.HotelInfoDataResponse;
import com.corpgovernment.mapping.basic.bo.response.HotelInfoResponse;
import com.corpgovernment.mapping.basic.bo.subbo.ContactInfo;
import com.corpgovernment.mapping.basic.bo.subbo.CoordinateInfo;
import com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo;
import com.corpgovernment.mapping.bo.HmHotelAllInfo;
import com.corpgovernment.mapping.bo.HmHotelMappingJob;
import com.corpgovernment.mapping.bo.HotelGetDataConfigBo;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

public abstract class HotelGetDataAbstractProcess {

    public abstract void process(StringBuilder logContext, String supplierCode, String cityId);

    public HotelGetDataConfigBo getDataConfig(String supplierCode, String config){
        Map<String, String> hotelMappingMap = JsonUtils.parse(config, new TypeReference<Map<String, String>>() {});
        HotelGetDataConfigBo bo = new HotelGetDataConfigBo();
        bo.setHotelIdUrl(hotelMappingMap.get(supplierCode + "_HotelIdUrl"));
        bo.setHotelInfoUrl(hotelMappingMap.get(supplierCode + "_HotelInfoUrl"));

        if (StringUtils.isBlank(bo.getHotelIdUrl()) || StringUtils.isBlank(bo.getHotelInfoUrl())){
            return null;
        }

        bo.setSupplierCorpId(hotelMappingMap.get(supplierCode + "_SupplierCorpId"));
        bo.setUserKey(hotelMappingMap.get(supplierCode + "_UserKey"));
        bo.setSupplierUid(hotelMappingMap.get(supplierCode + "_SupplierUid"));
        return bo;
    }

    public HotelInfoResponse getHotelInfoTrip(String supplierCode, String userKey, List<String> hotelIdList, String hotelInfoUrl) throws IOException {
        try{
            HotelInfoRequest hotelInfoRequest = new HotelInfoRequest();

            //组装请求参数
            List<String> returnDataTypeList = new ArrayList<String>();
            returnDataTypeList.add("HotelSimpleEntity");
            //经纬度
            returnDataTypeList.add("CoordinateEntity");
            //地址
            returnDataTypeList.add("HotelAddress");
            //电话 邮编
            returnDataTypeList.add("ContactInformationEntity");

            hotelInfoRequest.setHotelID(hotelIdList);
            hotelInfoRequest.setReturnDataTypeList(returnDataTypeList);
            hotelInfoRequest.setGetMasterAndSubHotelMode("Normal");

            String resp = HttpUtils.doPostJSONUseSign(supplierCode, "酒店信息查询", hotelInfoUrl, JsonUtils.toJsonString(hotelInfoRequest), userKey);
            HotelInfoResponse hotelInfoResponse = JsonUtils.parse(resp, HotelInfoResponse.class);
            return hotelInfoResponse;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;

    }

    public List<String> getHotelIdTrip(String cityId, String supplierCode, String supplierCorpId, String supplierUid, String userKey, String hotelIdUrl) throws IOException {
        try{
            HotelCityRequest hotelCityRequest = new HotelCityRequest();
            hotelCityRequest.setCityId(cityId);
            hotelCityRequest.setCorpID(supplierCorpId);
            hotelCityRequest.setUID(supplierUid);
            String resp = HttpUtils.doPostJSONUseSign(supplierCode, "某个城市所有酒店ID查询", hotelIdUrl, JsonUtils.toJsonString(hotelCityRequest), userKey);
            HotelCityResponse hotelCityResponse = JsonUtils.parse(resp, HotelCityResponse.class);
            if (hotelCityResponse == null || StringUtils.isBlank(hotelCityResponse.getHotelIDList())){
                return new ArrayList<>();
            }
            return Arrays.asList(hotelCityResponse.getHotelIDList().split(","));
        }catch (Exception e){
            e.printStackTrace();
        }
        return Collections.emptyList();
    }

    public HotelInfoResponse getHotelInfoOther(String supplierCode, List<String> hotelIdList, String hotelInfoUrl) throws IOException {
        try {
            if (CollectionUtils.isEmpty(hotelIdList)){
                return new HotelInfoResponse();
            }
            HotelInfoRequest hotelInfoRequest = new HotelInfoRequest();
            hotelInfoRequest.setHotelID(hotelIdList);
            String resp = HttpUtils.doPostJSON(supplierCode, "酒店信息查询", hotelInfoUrl, JsonUtils.toJsonString(hotelInfoRequest));
            HotelInfoResponse hotelInfoResponse = JsonUtils.parse(resp, HotelInfoResponse.class);
            return hotelInfoResponse;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public List<String> getHotelIdOther(String cityId, String supplierCode, String hotelIdUrl) throws IOException{
        try{
            HotelCityRequest hotelCityRequest = new HotelCityRequest();
            hotelCityRequest.setCityId(cityId);
            String resp = HttpUtils.doPostJSON(supplierCode, "某个城市所有酒店ID查询", hotelIdUrl, JsonUtils.toJsonString(hotelCityRequest));
            HotelCityApiResponse hotelCityResponse = JsonUtils.parse(resp, HotelCityApiResponse.class);
            if (hotelCityResponse == null || CollectionUtils.isEmpty(hotelCityResponse.getHotelIDList())){
                return new ArrayList<>();
            }
            return hotelCityResponse.getHotelIDList();
        }catch (Exception e){
            e.printStackTrace();
        }
        return Collections.emptyList();
    }




}
