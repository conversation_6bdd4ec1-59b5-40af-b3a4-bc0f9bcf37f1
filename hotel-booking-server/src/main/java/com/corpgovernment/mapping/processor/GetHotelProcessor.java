package com.corpgovernment.mapping.processor;

import com.corpgovernment.basicdata.service.IHotelCityDataService;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.utils.*;
import com.corpgovernment.mapping.aop.PageAnnotation;
import com.corpgovernment.mapping.aop.PageList;
import com.corpgovernment.mapping.basic.bo.request.HotelCityRequest;
import com.corpgovernment.mapping.basic.bo.request.HotelInfoRequest;
import com.corpgovernment.mapping.basic.bo.response.*;
import com.corpgovernment.mapping.basic.bo.subbo.ContactInfo;
import com.corpgovernment.mapping.basic.bo.subbo.CoordinateInfo;
import com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo;
import com.corpgovernment.mapping.bo.*;
import com.corpgovernment.mapping.mapper.HmHotelAllInfoMapper;
import com.corpgovernment.mapping.mapper.HmHotelDetailImportMapper;
import com.corpgovernment.mapping.mapper.HpHotelHzInfoMapper;
import com.corpgovernment.mapping.mapper.HpSupplierMapper;
import com.corpgovernment.mapping.service.IGetAllHotelService;
import com.corpgovernment.mapping.supplier.request.BaseRequest;
import com.corpgovernment.mapping.supplier.request.GetHotelIDRequest;
import com.corpgovernment.mapping.supplier.request.GetHotelInfoRequest;
import com.corpgovernment.mapping.supplier.response.GeographyEntity;
import com.corpgovernment.mapping.supplier.response.GeographyResourceResponse;
import com.corpgovernment.mapping.supplier.response.HotelDetailData;
import com.corpgovernment.mapping.supplier.response.HotelIdResponse;
import com.corpgovernment.mapping.supplier.response.HotelResourceResponse;
import com.corpgovernment.mapping.supplier.response.HotelSimpleEntity;
import com.corpgovernment.mapping.util.CallWebServiceUtils;
import com.corpgovernment.mapping.vo.SupplierHotelInfoVo;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @ClassName: GetHotelProcessor
 * @description: TODO
 * @author: yssong
 * @date: Created in 15:18 2019/9/4
 * @Version: 1.0
 **/
@Service
@Slf4j
public class GetHotelProcessor implements IGetAllHotelService {

    @Autowired
    private HpHotelHzInfoMapper hpHotelHzInfoMapper;
    @Autowired
    private HmHotelAllInfoMapper hmHotelAllInfoMapper;
    @Autowired
    private HmHotelDetailImportMapper hmHotelDetailImportMapper;
	@Autowired
	private IHotelCityDataService hotelCityDataService;
    @Autowired
	private HpSupplierMapper supplierMapper;
    @Autowired
    private HotelApollo hotelApollo;

    //http://openservice.open.uat.ctripqa.com/openservice/serviceproxy.ashx?aid=1&sid=50&icode=0c7df1edd95b44c9814fabf4ec443ba8&token=a1d213daa94899a8c6e2b022a6881e947a725392403e1505554642d9011ae43f&uuid=808d82f9a0db40b496f6f7dd19c93f49&e=r6&mode=1&format=json
    //http://openservice.open.uat.ctripqa.com/openservice/serviceproxy.ashx?aid=1&sid=50&icode=3a69213f98df489fa1bc39f3cea58062&token=cc812fb5ed16e5a7717718975b96edbe0a72bd9130f8d7565b0b9b886d5a5cf0&uuid=17117208892044feadf9c9f49eb21a1b&e=r6&mode=1&format=json
    //String url = "http://apit1.shinetour.com/apis/CtripHotel/gethotelresource";
    //String url = "http://apit1.shinetour.com/apis/CtripHotel/getallhotelid";


    @Override
    public Boolean getSupplierHotelInfo(SupplierHotelInfoVo request) {
        StringBuilder logContext = new StringBuilder();
        String supplierCode = request.getSupplierCode();
        String cityId = request.getCityId();

        String hotelIdUrlkey = request.getSupplierCode() + "_HotelIdUrl";
        String hotelInfoUrlKey = request.getSupplierCode() + "_HotelInfoUrl";

        Map<String, String> hotelMappingMap = JsonUtils.parse(hotelApollo.getHotelMappingUrl(), new TypeReference<Map<String, String>>() {});
        String hotelIdUrl = hotelMappingMap.get(hotelIdUrlkey);
        String hotelInfoUrl = hotelMappingMap.get(hotelInfoUrlKey);

        LogSplicingUtils.addLogContext(logContext, "开始酒店数据处理,操作供应商：%s,操作城市编码：%s", supplierCode, cityId);
        LogSplicingUtils.addLogContext(logContext, "开始酒店数据处理,查询所有酒店ID地址：%s,根据酒店ID查询酒店信息地址：%s", hotelIdUrl, hotelInfoUrl);
        try {

            List<HpSupplier> hpSuppliers = supplierMapper.selectAllSupplier();
            Optional<Integer> first = hpSuppliers.stream().filter(item -> supplierCode.equals(item.getSupplierCode())).map(item -> item.getSupplierId()).findFirst();
            Integer supplierId = first.get();
            LogSplicingUtils.addLogContext(logContext, "获取到的SupplierId：%s", supplierId);


            List<String> hotelIdList = null;
            if ("ctrip".equals(supplierCode) || "HuaMei".equals(supplierCode)){
                hotelIdList = getHotelIdTrip(request, hotelIdUrl);
            }else{
                hotelIdList = getHotelIdOther(request, hotelIdUrl);
            }
            LogSplicingUtils.addLogContext(logContext, "获取到的酒店Id集合大小：%s", hotelIdList.size());
            List<String> subList = new ArrayList<>();

            //查酒店详细信息
            /** 酒店总数量**/
            int count = hotelIdList.size();
            /** 初始化执行数量**/
            int limit = 25,start = 0,end = 0;
            int number = count/limit;
            if (count%limit>0){
                number++;
            }
            LogSplicingUtils.addLogContext(logContext, "处理次数：%s, 一次处理条数：%s", number, limit);

            List<CompletableFuture<HotelInfoResponse>> futures = new ArrayList<>();
            for (int i=1;i<=number;i++){
                end = start+limit;
                if (end<=count){
                    subList = hotelIdList.subList(start,end).stream().collect(Collectors.toList());
                }else{
                    subList = hotelIdList.subList(start,count).stream().collect(Collectors.toList());
                }

                List<String> finalSubList = subList;

                CompletableFuture<HotelInfoResponse> future = CompletableFuture.supplyAsync(() -> {
                    HotelInfoResponse hotelInfoResponse = null;
                    try {
                        if ("ctrip".equals(supplierCode) || "HuaMei".equals(supplierCode)) {
                            hotelInfoResponse = getHotelInfoTrip(request, finalSubList, hotelInfoUrl);
                        } else {
                            hotelInfoResponse = getHotelInfoOther(request, finalSubList, hotelInfoUrl);
                        }
                    } catch (Exception e) {
                        log.error("{}", e);
                    }
                    return hotelInfoResponse;
                });

                futures.add(future);
                start =end;
            }

            LogSplicingUtils.addLogContext(logContext, "future size：%s", futures.size());
            if (futures.size() > 0){

                processHotelInfo(logContext ,futures, supplierId, cityId);
            }
        }catch (Exception e){
            log.error("酒店信息导入发生异常：{}", e);
            LogSplicingUtils.addLogContext(logContext, "发生异常: %s", e);
            return false;
        }finally {
            log.info("酒店信息导入：{}", logContext.toString());
        }
        return true;
    }

    public void processHotelInfo(StringBuilder logContext, List<CompletableFuture<HotelInfoResponse>> futures, Integer supplierId, String cityId) throws Exception {
        for (CompletableFuture<HotelInfoResponse> future: futures){
            HotelInfoResponse hotelInfoResponse = future.get();
            if (hotelInfoResponse == null){
                continue;
            }
            if (CollectionUtils.isNotEmpty(hotelInfoResponse.getHotelDataList())){
                List<HmHotelInfo> data = new ArrayList<>();
                for (HotelInfoDataResponse dataResponse:hotelInfoResponse.getHotelDataList()){
                    HmHotelInfo hmHotelInfo = dataResponse.getHotelStaticBaseInfoEntity().getHotelSimpleEntity();
                    //添加经纬度信息
                    CoordinateInfo coordinateInfo = dataResponse.getHotelStaticBaseInfoEntity().getCoordinateEntity();
                    if (coordinateInfo != null){
                        hmHotelInfo.setGlat(coordinateInfo.getGlat());
                        hmHotelInfo.setGlng(coordinateInfo.getGlng());
                    }
                    //地址
                    String hotelAddress = dataResponse.getHotelStaticBaseInfoEntity().getHotelAddress();
                    if (hotelAddress != null){
                        hmHotelInfo.setHotelAddress(hotelAddress);
                    }

                    //电话 邮编
                    ContactInfo contactInfo = dataResponse.getHotelStaticBaseInfoEntity().getContactInformationEntity();
                    if (contactInfo != null){
                        hmHotelInfo.setTelephone(contactInfo.getFax());
                        hmHotelInfo.setZipCode(contactInfo.getZipCode());
                    }
                    data.add(hmHotelInfo);
                }

                List<HmHotelAllInfo> hotelList = new ArrayList<>();
                for (HmHotelInfo hmHotelInfo:data){
                    HmHotelAllInfo hotelInfo = new HmHotelAllInfo();
                    hotelInfo.setSupplierId(supplierId);
                    if (Objects.isNull(hmHotelInfo.getHotel())){
                        hotelInfo.setHotelNo(hmHotelInfo.getHotelID());
                    }else{
                        hotelInfo.setHotelNo(hmHotelInfo.getHotel()+"");
                    }
                    hotelInfo.setHotelName(hmHotelInfo.getHotelName());
                    hotelInfo.setHotelAddress(hmHotelInfo.getHotelAddress());
                    hotelInfo.setTelephone(hmHotelInfo.getTelephone());
                    hotelInfo.setBrandCode("");
                    hotelInfo.setBrandName("");
                    hotelInfo.setCityId(cityId);
                    hotelInfo.setLatitude(hmHotelInfo.getGlat()+"");
                    hotelInfo.setLongitude(hmHotelInfo.getGlng()+"");
                    hotelList.add(hotelInfo);
                }
                LogSplicingUtils.addLogContext(logContext, "插入条数：%s", hotelList.size());
                hmHotelAllInfoMapper.batchInsert(hotelList);
            }
        }
    }



    public HotelInfoResponse getHotelInfoTrip(SupplierHotelInfoVo request, List<String> hotelIdList, String hotelInfoUrl) throws IOException {
        //查询酒店详细信息插入到数据库
        //String requestUrl =  CtripEncryptUtil.encryptCommonHotel("3a69213f98df489fa1bc39f3cea58062");

        HotelInfoRequest hotelInfoRequest = new HotelInfoRequest();

        //组装请求参数
        List<String> returnDataTypeList = new ArrayList<String>();
        returnDataTypeList.add("HotelSimpleEntity");
        //经纬度
        returnDataTypeList.add("CoordinateEntity");
        //地址
        returnDataTypeList.add("HotelAddress");
        //电话 邮编
        returnDataTypeList.add("ContactInformationEntity");

        hotelInfoRequest.setHotelID(hotelIdList);
        hotelInfoRequest.setReturnDataTypeList(returnDataTypeList);
        hotelInfoRequest.setGetMasterAndSubHotelMode("Normal");

        String resp = HttpUtils.doPostJSONUseSign(request.getSupplierCode(), "酒店信息查询", hotelInfoUrl, JsonUtils.toJsonString(hotelInfoRequest), request.getUserKey());
        HotelInfoResponse hotelInfoResponse = JsonUtils.parse(resp, HotelInfoResponse.class);
        return hotelInfoResponse;
    }


    public List<String> getHotelIdTrip(SupplierHotelInfoVo request, String hotelIdUrl) throws IOException {
        HotelCityRequest hotelCityRequest = new HotelCityRequest();
        hotelCityRequest.setCityId(request.getCityId());
        hotelCityRequest.setCorpID(request.getSupplierCorpId());
        hotelCityRequest.setUID(request.getSupplierUid());
        String resp = HttpUtils.doPostJSONUseSign(request.getSupplierCode(), "某个城市所有酒店ID查询", hotelIdUrl, JsonUtils.toJsonString(hotelCityRequest), request.getUserKey());
        HotelCityResponse hotelCityResponse = JsonUtils.parse(resp, HotelCityResponse.class);
        if (hotelCityResponse == null || StringUtils.isBlank(hotelCityResponse.getHotelIDList())){
            return new ArrayList<>();
        }
        return Arrays.asList(hotelCityResponse.getHotelIDList().split(","));
    }

    public HotelInfoResponse getHotelInfoOther(SupplierHotelInfoVo request, List<String> hotelIdList, String hotelInfoUrl) throws IOException {
        if (CollectionUtils.isEmpty(hotelIdList)){
            return new HotelInfoResponse();
        }
        HotelInfoRequest hotelInfoRequest = new HotelInfoRequest();
        hotelInfoRequest.setHotelID(hotelIdList);
        String resp = HttpUtils.doPostJSON(request.getSupplierCode(), "酒店信息查询", hotelInfoUrl, JsonUtils.toJsonString(hotelInfoRequest));
        HotelInfoResponse hotelInfoResponse = JsonUtils.parse(resp, HotelInfoResponse.class);
        return hotelInfoResponse;
    }

    public List<String> getHotelIdOther(SupplierHotelInfoVo request, String hotelIdUrl) throws IOException{
        HotelCityRequest hotelCityRequest = new HotelCityRequest();
        hotelCityRequest.setCityId(request.getCityId());
        String resp = HttpUtils.doPostJSON(request.getSupplierCode(), "某个城市所有酒店ID查询", hotelIdUrl, JsonUtils.toJsonString(hotelCityRequest));
        HotelCityApiResponse hotelCityResponse = JsonUtils.parse(resp, HotelCityApiResponse.class);
        if (hotelCityResponse == null || CollectionUtils.isEmpty(hotelCityResponse.getHotelIDList())){
            return new ArrayList<>();
        }
        return hotelCityResponse.getHotelIDList();
    }


    private final ExecutorService exec = new ThreadPoolExecutor(20, 20, 0,
            TimeUnit.SECONDS, new LinkedBlockingQueue<>(Integer.MAX_VALUE));

    @Override
    public Boolean getAllHotelByHuaZhu() {

        new Thread(()-> {
            System.out.println("异步执行线程");

            //获取全量酒店id
            String  xmls = null;
            try {
                xmls = CallWebServiceUtils.getHotelIdsWebService();
            } catch (Exception e) {
                e.printStackTrace();
            }
            String arrs  =  xmls.split("<QueryHotelIDListResult>")[1].split("</QueryHotelIDListResult>")[0];
            QueryHotelIDListResult queryHotelIDListResult =  JsonUtils.parse(arrs, QueryHotelIDListResult.class);
            if ("0".equals(queryHotelIDListResult.getResultCode())){
                List<ResultContentHotel> lists = queryHotelIDListResult.getResultContent();

                List<Map<String,String>> ids = new ArrayList<>(lists.size());
                for (ResultContentHotel resultContentHotel:lists){
                    Map<String,String> map = new HashMap<>();
                    map.put("HotelID",resultContentHotel.getHotelNO());
                    ids.add(map);
                }

                //分批获取酒店信息
                List<Map<String,String>> subList = new ArrayList<>();


                //查酒店详细信息
                /** 酒店总数量**/
                int count = ids.size();
                /** 初始化执行数量**/
                int limit = 25,start = 0,end = 0;
                int number = count/limit;
                if (count%limit>0){
                    number++;
                }

                for (int i=1;i<=number;i++) {
                    end = start + limit;
                    if (end <= count) {
                        subList = ids.subList(start, end);
                    } else {
                        subList = ids.subList(start, count);
                    }

                    //批量查询数据
//                    String hotelIds = JSONUtils.toJSONString(subList);
                    String hotelIds = JsonUtils.toJsonString(subList);

                    String  xml = null;
                    try {
                        xml = CallWebServiceUtils.WebService(hotelIds);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    String arr  =  xml.split("<QueryHotelResult>")[1].split("</QueryHotelResult>")[0];
                    QueryHotelResult hotelCityResponse = JsonUtils.parse(arr, QueryHotelResult.class);

                    //批量插入数据库
                    List<HpHotelHzInfo> data = hotelCityResponse.getResultContent();

//                    List<HmHotelAllInfo> hotelList = new ArrayList<>();
//                    for (HpHotelHzInfo hpHotelHzInfo:data){
//                        HmHotelAllInfo hotelInfo = new HmHotelAllInfo();
//                        hotelInfo.setSupplierId(2);
//                        hotelInfo.setHotelNo(hpHotelHzInfo.getHotelno());
//                        hotelInfo.setHotelName(hpHotelHzInfo.getHotelname());
//                        hotelInfo.setHotelAddress(hpHotelHzInfo.getHoteladdress());
//                        hotelInfo.setTelephone(hpHotelHzInfo.getTelephone());
//                        hotelInfo.setBrandCode(hpHotelHzInfo.getBrandcode());
//                        hotelInfo.setBrandName(hpHotelHzInfo.getBrandname());
//                        hotelInfo.setLatitude(hpHotelHzInfo.getLatitude());
//                        hotelInfo.setLongitude(hpHotelHzInfo.getLongitude());
//                        hotelList.add(hotelInfo);
//                    }
//                    hmHotelAllInfoMapper.batchInsert(hotelList);

                    start =end;
                }


                System.out.println("执行结束------------------------------------------------------------");

            }

        }).start();

        return true;
    }

    @Override
    public Boolean getHuaZhuHotel() {

        List<String> ids = hpHotelHzInfoMapper.selectHotelId();
        List<HpHotelHzInfo> data = hpHotelHzInfoMapper.selectHotelListByIds(ids);

        List<HmHotelAllInfo> hotelList = new ArrayList<>();
        for (HpHotelHzInfo hpHotelHzInfo:data){
            HmHotelAllInfo hotelInfo = new HmHotelAllInfo();
            hotelInfo.setSupplierId(2);
            hotelInfo.setHotelNo(hpHotelHzInfo.getHotelno());
            hotelInfo.setHotelName(hpHotelHzInfo.getHotelname());
            hotelInfo.setHotelAddress(hpHotelHzInfo.getHoteladdress());
            hotelInfo.setTelephone(hpHotelHzInfo.getTelephone());
            hotelInfo.setBrandCode(hpHotelHzInfo.getBrandcode());
            hotelInfo.setBrandName(hpHotelHzInfo.getBrandname());
            hotelInfo.setLatitude(hpHotelHzInfo.getLatitude());
            hotelInfo.setLongitude(hpHotelHzInfo.getLongitude());
            hotelList.add(hotelInfo);
        }
        hmHotelAllInfoMapper.batchInsert(hotelList);

        return true;
    }

    /**
     * 获取空港嘉华的酒店静态数据
     */
    public void getAllHotel(DownloadHotelRequest entity){
        //获取城市cityid
        String requestCityUrl =entity.getUrl()+"getgeographyresource";
        BaseRequest request=new BaseRequest();
        request.setId(entity.getId());
        request.setUID(entity.getUid());
        request.setToken(entity.getToken());
        request.setCorpID(entity.getCorpID());
        String resp = "";
        try {
            resp = HttpUtils.doPostJSON("ctrip", "某个城市所有酒店信息查询", requestCityUrl, JsonUtils.toJsonString(request));
            System.out.println(resp);
        } catch (IOException e) {
            log.warn("获取城市信息失败,e:{}", e);
            e.printStackTrace();
        }
        Integer totalNum=0;
        List<String> cityList=hmHotelAllInfoMapper.getExistCityList(entity.getSupplierId());
        GeographyResourceResponse response=JsonUtils.parse(resp, GeographyResourceResponse.class);
        for (GeographyResourceResponse.Country country :response.getCountryList()){
            for (GeographyResourceResponse.Country.Province province:country.getProvinceList()){
                for (GeographyResourceResponse.Country.Province.City city:province.getCityList()){
                    if (!cityList.contains(city.getCityID())){
                        log.info("拉取城市{},id{}",city.getCityName(),city.getCityID());
                        exec.execute(()->{
                            if (!Objects.equals(city.getCityID(), "1")){
                                queryHotelByCityID(city.getCityID(),city.getCityName(),entity);
                            }
                        });
                    }else {
                        log.info("拉取城市{},id{},已经拉取过",city.getCityName(),city.getCityID());
                    }
                }
            }
        }
        log.info("酒店总数{}",totalNum);
    }

    public Integer countCityNum(String cityID,String cityName,String id,String token,String uid,String corpID,String url){
        GetHotelIDRequest request=new GetHotelIDRequest();
        request.setId(id);
        request.setUID(uid);
        request.setToken(token);
        request.setCorpID(corpID);
        request.setCityID(cityID);

        String getHotelIDByCityUrl=url+ "getallhotelid";

        String resp = "";
        try {
            resp = HttpUtils.doPostJSON("ctrip", "某个城市所有酒店信息查询", getHotelIDByCityUrl, JsonUtils.toJsonString(request));
            HotelIdResponse response=JsonUtils.parse(resp, HotelIdResponse.class);
            log.info("{}数量为：{}",cityName,response.getHotelIDList().size());
            return response.getHotelIDList().size();

        } catch (IOException e) {
            log.warn("获取城市所有酒店失败,cityid:{},e:{}",cityID, e);
            e.printStackTrace();
        }
        return 0;
    }

    public void queryHotelByCityID(String cityID,String cityName,DownloadHotelRequest entity){
        GetHotelIDRequest request=new GetHotelIDRequest();
        request.setId(entity.getId());
        request.setUID(entity.getUid());
        request.setToken(entity.getToken());
        request.setCorpID(entity.getCorpID());
        request.setCityID(cityID);

        String getHotelIDByCityUrl=entity.getUrl()+ "getallhotelid";

        String resp = "";
        try {
            resp = HttpUtils.doPostJSON("ctrip", "某个城市所有酒店信息查询", getHotelIDByCityUrl, JsonUtils.toJsonString(request));
            HotelIdResponse response=JsonUtils.parse(resp, HotelIdResponse.class);
            //todo 数据分隔
            saveHotelInfo(response.getHotelIDList(),cityID,cityName,entity);

        } catch (IOException e) {
            log.warn("获取城市所有酒店失败,cityid:{},e:{}",cityID, e);
            e.printStackTrace();
        }
    }

    @PageAnnotation(pageSize = "200")
    public void saveHotelInfo(List<String> hotelIds,String cityID,String cityName,DownloadHotelRequest entity){
        /** 总数量**/
        int count = hotelIds.size();
        //int count = 100;
        /** 初始化执行数量  10000条**/
        int limit = 10;
        int number = count/limit;
        if (count%limit>0){
            number++;
        }
        List<HmHotelAllInfo> hotelList = new ArrayList<>();
        List<HmHotelDetailImport> hotelDetailList = new ArrayList<>();
        for (int i=1;i<=number;i++) {
            int fromIndex = (i - 1) * limit;
            int endIndex;
            if (i == number) {
                endIndex = count;
            } else {
                endIndex = i * limit;
            }
            List<String> subList = hotelIds.subList(fromIndex, endIndex);
            GetHotelInfoRequest request = new GetHotelInfoRequest();
            request.setId(entity.getId());
            request.setUID(entity.getUid());
            request.setToken(entity.getToken());
            request.setCorpID(entity.getCorpID());
            request.setHotelID(subList);

            String getHotelIDByCityUrl = entity.getUrl()+"gethotelresource";

            String resp = "";
            try {
                resp = HttpUtils.doPostJSON("ctrip", "某个城市所有酒店信息查询", getHotelIDByCityUrl, JsonUtils.toJsonString(request));
                HotelResourceResponse response = JsonUtils.parse(resp, HotelResourceResponse.class);

                for (HotelDetailData detailInfo : response.getHotelDataList()) {
                    HotelSimpleEntity hotelSimpleEntity = detailInfo.getHotelStaticBaseInfoEntity().getHotelSimpleEntity();
                    GeographyEntity geographyEntity = detailInfo.getHotelStaticBaseInfoEntity().getGeographyEntity();
                    HmHotelAllInfo hotelInfo = new HmHotelAllInfo();
                    hotelInfo.setSupplierId(entity.getSupplierId());
                    hotelInfo.setHotelNo(hotelSimpleEntity.getHotelID());
                    hotelInfo.setHotelName(hotelSimpleEntity.getHotelName()==null?"":hotelSimpleEntity.getHotelName());
                    hotelInfo.setHotelAddress(hotelSimpleEntity.getHotelAddress()==null?"":hotelSimpleEntity.getHotelAddress());
                    hotelInfo.setTelephone(hotelSimpleEntity.getTelephone());
                    hotelInfo.setBrandCode("");
                    hotelInfo.setBrandName("");
                    hotelInfo.setLatitude(hotelSimpleEntity.getGdLat()==null?"-1.0":hotelSimpleEntity.getGdLat());
                    hotelInfo.setLongitude(hotelSimpleEntity.getGdLon()==null?"-1.0":hotelSimpleEntity.getGdLon());
                    hotelInfo.setCityId(cityID);
                    hotelInfo.setCityName(cityName);
                    hotelList.add(hotelInfo);

                    HmHotelDetailImport hotelDetailInfo=new HmHotelDetailImport();
                    hotelDetailInfo.setSupplierId(entity.getSupplierId());
                    hotelDetailInfo.setHotelNo(hotelSimpleEntity.getHotelID());
                    hotelDetailInfo.setHotelName(hotelSimpleEntity.getHotelName()==null?"":hotelSimpleEntity.getHotelName());
                    hotelDetailInfo.setProvinceName(geographyEntity.getProvinceName());
                    hotelDetailInfo.setCityName(geographyEntity.getCityName());
                    hotelDetailInfo.setZoneName(geographyEntity.getZoneName());
                    if (StringUtils.isNotBlank(hotelSimpleEntity.getOpenYear())){
                        hotelDetailInfo.setOpenYear( DateUtil.stringToDate(hotelSimpleEntity.getOpenYear(),"yyyy-MM"));
                    }
                    if (StringUtils.isNotBlank(hotelSimpleEntity.getFitmentYear())) {
                        hotelDetailInfo.setFitmentYear(DateUtil.stringToDate(hotelSimpleEntity.getFitmentYear(), "yyyy-MM"));
                    }
                    hotelDetailInfo.setStar(Integer.valueOf(hotelSimpleEntity.getStar().replace("A","")));
                    hotelDetailInfo.setRoomQuantity(10);
                    hotelDetailInfo.setPhone(hotelSimpleEntity.getTelephone());
                    hotelDetailInfo.setFax(hotelSimpleEntity.getFax());
                    hotelDetailInfo.setBrand(detailInfo.getHotelStaticBaseInfoEntity().getBrandID());
                    hotelDetailInfo.setBranEn(detailInfo.getHotelStaticBaseInfoEntity().getBrandName());
                    hotelDetailInfo.setAddress(hotelSimpleEntity.getHotelAddress());
                    hotelDetailInfo.setHotelDesc(detailInfo.getHotelStaticBaseInfoEntity().getHotelDesc());
                    hotelDetailInfo.setDatachangeCreatetime(new Date());
                    hotelDetailInfo.setDatachangeLasttime(new Date());
                    hotelDetailInfo.setLatitude(hotelSimpleEntity.getGdLat()==null?"-1.0":hotelSimpleEntity.getGdLat());
                    hotelDetailInfo.setLongitude(hotelSimpleEntity.getGdLon()==null?"-1.0":hotelSimpleEntity.getGdLon());
                    try{
                        hotelDetailInfo.setHotelFacility(JsonUtils.toJsonString(detailInfo.getHotelStaticBaseInfoEntity().getHotelFacilityEntityList()));
                    }catch (Exception e){
                        log.info("导入异常{}",detailInfo);
                    }
                    hotelDetailList.add(hotelDetailInfo);
                    if (hotelList.size()>1000){
                        log.info("城市{},城市id{}，插入酒店数量为{}",cityName,cityID,hotelList.size());
                        InsertWithResultId(hotelList,hotelDetailList);
                        hotelDetailList.clear();
                        hotelList.clear();
                    }
                }
            } catch (Exception e) {
                log.warn("获取酒店详情失败,hotelIds:{},e:{}", subList, e);
                for (String hotelNo:subList){//将拉取失败的酒店id记录下来
                    HmHotelAllInfo hotelInfo = new HmHotelAllInfo();
                    hotelInfo.setSupplierId(entity.getSupplierId());
                    hotelInfo.setHotelNo(hotelNo);
                    hotelInfo.setHotelName("");
                    hotelInfo.setHotelAddress("");
                    hotelInfo.setTelephone("");
                    hotelInfo.setBrandCode("");
                    hotelInfo.setBrandName("");
                    hotelInfo.setLatitude("-1.0");
                    hotelInfo.setLongitude("-1.0");
                    hotelInfo.setCityId(cityID);
                    hotelInfo.setCityName(cityName);
                    hotelList.add(hotelInfo);

                    HmHotelDetailImport detailInfo=new HmHotelDetailImport();
                    detailInfo.setSupplierId(entity.getSupplierId());
                    detailInfo.setHotelNo(hotelNo);
                    detailInfo.setDatachangeCreatetime(new Date());
                    detailInfo.setDatachangeLasttime(new Date());
                    hotelDetailList.add(detailInfo);
                }
            }
        }

        if (hotelList.size()>0){
            InsertWithResultId(hotelList,hotelDetailList);
            log.info("城市{},城市id{}，插入酒店数量为{}",cityName,cityID,hotelList.size());
        }
    }

    private void InsertWithResultId(List<HmHotelAllInfo> hotelList,List<HmHotelDetailImport> detailList){
        hmHotelAllInfoMapper.batchInsert(hotelList);
        hmHotelDetailImportMapper.insertList(detailList);
        try {
            hmHotelAllInfoMapper.batchUpdateDetailId(hotelList.get(0).getSupplierId(),hotelList);
        }catch (Exception e){
            log.info("批量更新detailid失败",e);
        }
    }

    @PageAnnotation
    public void test(String gg,@PageList List<String> list,String KK){
        for (String item:list){
            System.out.println(item);
        }
    }
}

