package com.corpgovernment.mapping.processor;

import com.corpgovernment.api.basic.response.StaticMapResponse;
import com.corpgovernment.api.hotel.product.model.request.HotelMappingRequest;
import com.corpgovernment.api.hotel.product.model.request.Tree;
import com.corpgovernment.api.hotel.product.model.response.HotelMappingResponse;
import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.mapping.bo.HmSupplierResultMapping;
import com.corpgovernment.mapping.bo.HmSupplierResultVo;
import com.corpgovernment.mapping.bo.HpSupplier;
import com.corpgovernment.mapping.mapper.HmSupplierResultMappingMapper;
import com.corpgovernment.mapping.mapper.HpSupplierMapper;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jt.qin
 * @DateTime: 2020/3/19 10:48
 * @Description 判断输入的酒店信息是否匹配
 */
@Service
@Slf4j
public class CheckHotelMappingProcessor {

    @Autowired
    private HpSupplierMapper hpSupplierMapper;
    @Autowired
    private HmSupplierResultMappingMapper hmSupplierResultMappingMapper;
    @Autowired
    private HotelApollo hotelApollo;

	public List<HmSupplierResultVo> selectByHotelIds(List<String> hotelIds, String supplierCode){
		return hmSupplierResultMappingMapper.selectByHotelIds(hotelIds, supplierCode);
	}

    public HotelMappingResponse getHotelMappingResponse(List<HotelMappingRequest> checkMappingHotels) {
        log.info("判断输入的酒店信息是否匹配,参数为{}", JsonUtils.toJsonString(checkMappingHotels));
        if (CollectionUtils.isEmpty(checkMappingHotels)) {
            return null;
        }
        //判断有些酒店不在查询列表里
        List<HpSupplier> supplierList = hpSupplierMapper.selectAllSupplier();
        log.info("supplierList:{}", JsonUtils.toJsonString(supplierList));
        Map<Integer, String> supplierCodeMap = supplierList.stream().collect(Collectors.toMap(HpSupplier::getId, HpSupplier::getSupplierCode));

        //查询所有的供应商酒店
        List<HmSupplierResultVo> mappings = hmSupplierResultMappingMapper.selectHotelsForCheck(checkMappingHotels);
        List<Integer> resultIds = mappings.stream().map(HmSupplierResultVo::getResultId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultIds)) {
            log.info("没有需要mapping的酒店");
            return null;
        }
        List<HmSupplierResultVo> supplierResultList = hmSupplierResultMappingMapper.selectByResultIds(resultIds);
        Map<Integer, List<HmSupplierResultVo>> groupByCollect = supplierResultList.stream().collect(Collectors.groupingBy(HmSupplierResultVo::getResultId));
        List<Tree> mappingList = new ArrayList<>();
        for (Map.Entry<Integer, List<HmSupplierResultVo>> entry : groupByCollect.entrySet()) {
            Tree item = new Tree();
            item.setLocalHotelId(String.valueOf(entry.getKey()));
            List<Tree.Kv> kvs = new ArrayList<>();
            for (HmSupplierResultVo mapping : entry.getValue()) {
                Tree.Kv kv = new Tree.Kv();
                kv.setHotelId(mapping.getSupplierHotelId());
                kv.setSupplier(mapping.getSupplierCode());
                kvs.add(kv);
            }
            item.setKvs(kvs);
            mappingList.add(item);
        }
        log.info("getHotelMappingResponse,result{}", JsonUtils.toJsonString(mappingList));
        return HotelMappingResponse.create(mappingList);
    }

    public HotelMappingResponse matchHotel(List<HotelMappingRequest> checkMappingHotels) {
        long start = System.currentTimeMillis();
        log.info("判断输入的酒店信息是否匹配,参数为{}", JsonUtils.toJsonString(checkMappingHotels));
        if (CollectionUtils.isEmpty(checkMappingHotels)) {
            return null;
        }
        //判断有些酒店不在查询列表里
        Map<String, Set<String>> supplierMappingMap = new HashMap<>();
        for (HotelMappingRequest request : checkMappingHotels) {
            supplierMappingMap.put(request.getSupplier(), new HashSet<>(request.getHotelIds()));
        }
        //查询所有的供应商酒店
        List<HmSupplierResultVo> mappings = hmSupplierResultMappingMapper.selectHotelsForCheck(checkMappingHotels);
        log.info("查询所有的供应商酒店,结果为 {}", JsonUtils.toJsonString(mappings));
        Map<Integer, List<HmSupplierResultVo>> groupByCollect = mappings.stream().collect(Collectors.groupingBy(HmSupplierResultVo::getResultId));
        HotelMappingResponse response = new HotelMappingResponse();
        List<Tree> mappingList = new ArrayList<>();
        for (Map.Entry<Integer, List<HmSupplierResultVo>> entry : groupByCollect.entrySet()) {
            Tree item = new Tree();
            item.setLocalHotelId(String.valueOf(entry.getKey()));
            List<Tree.Kv> kvs = new ArrayList<>();
            for (HmSupplierResultVo mapping : entry.getValue()) {
                Tree.Kv kv = new Tree.Kv();
                kv.setHotelId(mapping.getSupplierHotelId());
                kv.setSupplier(mapping.getSupplierCode());
                //移除已有的酒店信息
                supplierMappingMap.get(mapping.getSupplierCode()).remove(mapping.getSupplierHotelId());
                kvs.add(kv);
            }
            item.setKvs(kvs);
            mappingList.add(item);
        }
        //添加未发现的酒店信息
        for (Map.Entry<String, Set<String>> entry : supplierMappingMap.entrySet()) {
            for (String str : entry.getValue()) {
                Tree unMatch = new Tree();
                unMatch.setLocalHotelId(entry.getKey() + "-" + str);
                List<Tree.Kv> listKv = new ArrayList<>();
                Tree.Kv kv = new Tree.Kv();
                kv.setSupplier(entry.getKey());
                kv.setHotelId(str);
                listKv.add(kv);
                unMatch.setKvs(listKv);
                mappingList.add(unMatch);
            }
        }
        response.setMappingList(mappingList);
        log.info("matchHotel 耗时(毫秒)：" + (System.currentTimeMillis() - start));
        log.info("matchHotel 返回值为：{}", JsonUtils.toJsonString(mappingList));
        return response;
    }

    public StaticMapResponse getRelatedHotel(String supplierCode, String hotelId, List<String> wantedSupplierCodes) {
        StaticMapResponse response = new StaticMapResponse();
        response.setResult(new HashMap<>());
        List<HmSupplierResultMapping> relatedHotels = hmSupplierResultMappingMapper.selectAllRelatedHotels(supplierCode, hotelId);
        log.info("获取相关的酒店为{}", JsonUtils.toJsonString(relatedHotels));
        if (CollectionUtils.isEmpty(relatedHotels)) {
            return null;
        }
        Example example = new Example(HpSupplier.class);
        example.createCriteria()
                .andIn("supplierCode", wantedSupplierCodes);
        List<HpSupplier> supplierList = hpSupplierMapper.selectByExample(example);
        log.info("获取需要相关供应商为{}", JsonUtils.toJsonString(supplierList));
        for (HpSupplier supplier : supplierList) {
            for (HmSupplierResultMapping mapping : relatedHotels) {
                if (mapping.getSupplierId().equals(supplier.getSupplierId())) {
                    response.getResult().put(supplier.getSupplierCode(), mapping.getSupplierHotelId());
                }
            }
        }
        response.setLocalId(String.valueOf(relatedHotels.get(0).getResultId()));
        return response;
    }

    public String checkSearchRequestCityId(String cityCode) {
        String searchHotelRequestScenicIdMappingCityId = hotelApollo.getSearchHotelRequestScenicIdMappingCityId();
        if (StringUtils.isEmpty(searchHotelRequestScenicIdMappingCityId)) {
            return cityCode;
        }
        Map<String, Object> map = JsonUtils.parseMap(searchHotelRequestScenicIdMappingCityId);
        if (Objects.nonNull(map.get(cityCode))) {
            log.info("酒店查询列表触发景区>>城市映射关系,景区Code:{},城市ID:{}", cityCode, map.get(cityCode));
            return map.get(cityCode).toString();
        }
        return cityCode;
    }
}
