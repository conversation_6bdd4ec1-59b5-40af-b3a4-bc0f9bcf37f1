package com.corpgovernment.mapping.processor;


import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.base.AbstractBaseService;
import com.corpgovernment.common.base.Page;
import com.corpgovernment.mapping.basic.bo.request.MergeHandlerRequest;
import com.corpgovernment.mapping.bo.HmHotelDetail;
import com.corpgovernment.mapping.bo.HmHotelMatchMapping;
import com.corpgovernment.mapping.bo.HotelMatchDisplayVo;
import com.corpgovernment.mapping.mapper.HmHotelMatchMappingMapper;
import com.corpgovernment.mapping.mapper.HpResultHotelMapper;
import com.corpgovernment.mapping.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: jt.qin
 * @DateTime: 2019/9/27 10:19
 * @Description 酒店合并展示相关的数据
 */
@Service
public class HotelMergeDisplayProcessor extends AbstractBaseService {

    @Autowired
    private HmHotelMatchMappingMapper hmHotelMatchMappingMapper;

    @Autowired
    private HpResultHotelMapper hpResultHotelMapper;
    @Autowired
    private HotelApollo hotelApollo;


    /**
     * 获取待合并酒店的列表数据
     * @param request
     * @return
     */
    public List<HotelMatchDisplayVo> getMergeList(MergeHandlerRequest request){
        List<HotelMatchDisplayVo> list=hmHotelMatchMappingMapper.getDisplayHotelMatchRelation(request);
        list.forEach(p->{
            if (p.getChildrenCount()>=1){
                List<HotelMatchDisplayVo> children=hmHotelMatchMappingMapper.getHotelMatchSupplierHotels(p.getSupplierId(),p.getHotelId());
                List<List<HotelMatchDisplayVo>> childrenGroup =new ArrayList<>();
                Map<Integer,List<HotelMatchDisplayVo>> map= children.stream().collect(Collectors.groupingBy(HotelMatchDisplayVo::getSupplierId));
                for (List<HotelMatchDisplayVo> value : map.values()) {
                    childrenGroup.add(value);
                }
                p.setChildrenGroup(childrenGroup);
            }
        });
        return list;
    }

    /**
     * 获取待合并酒店的列表数量
     * @param request
     * @return
     */
    public Integer getMergeListNum(MergeHandlerRequest request){
        return hmHotelMatchMappingMapper.getDisplayHotelMatchRelationNum(request);
    }

    /**
     * 获取操作日志表的合并结果
     * @param request
     * @return
     */
    public MergeResultResponse getMergeResult(MergeResultRequest request){
        MergeResultResponse response=new MergeResultResponse();
        List<Integer> status=new ArrayList<>();
        if(request.getMergeResult()!=null&&request.getMergeResult()>0){
            status.add(request.getMergeResult());
        }else {
            status.add(2);
            status.add(3);
        }
        request.setStatus(status);

        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<HmHotelMatchMapping> mergeList= hmHotelMatchMappingMapper.getHotelMergeResult(request);
        PageInfo pageInfo = new PageInfo<>(mergeList);
        Page<HmHotelMatchMapping> page = new Page(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), mergeList);
        //Page<HmHotelMatchMapping> page = optPageBy(mergeList, HmHotelMatchMapping.class);

        response.setTotal(Math.toIntExact(page.getCount()));
        response.setPageSize(page.getPageSize());
        response.setCurrentPage(page.getPageNum());

        List<MergeResultVo> list=new ArrayList<>();
        for (HmHotelMatchMapping item: page.getList()){
            MergeResultVo vo=new MergeResultVo();
            vo.setResult(item.getHandlerStatus());
            vo.setId(item.getId());
            List<MergeHotelVo> hotelList=new ArrayList<>();
            hotelList.add(new MergeHotelVo(item.getBaseHotel(),item.getBaseHotelName(),item.getBaseHotelAddress()));
            hotelList.add(new MergeHotelVo(item.getMatchHotel(),item.getMatchHotelName(),item.getMatchHotelAddress()));
            vo.setData(hotelList);
            list.add(vo);
        }
        response.setList(list);
        return response;
    }

    /**
     * 获取合并结果的详情
     */
    public HotelResultCompareResponse getHotelMergeDetail(Integer matchId){
        HotelResultCompareResponse response=new HotelResultCompareResponse();

        HmHotelMatchMapping matchMapping=hmHotelMatchMappingMapper.selectByPrimaryKey(matchId);
        List<String> hotelIds=new ArrayList<>();
        hotelIds.add(matchMapping.getBaseHotel());
        hotelIds.add(matchMapping.getMatchHotel());
        List<HmHotelDetail> details= hpResultHotelMapper.selectCompareHotelDetail(hotelIds);
        for (HmHotelDetail item:details){
            if (StringUtils.isNotBlank(item.getImages())){
                StringBuilder sb = new StringBuilder(item.getImages());
                if (item.getImages().contains("_R")) {
                    sb.insert(item.getImages().indexOf("_R") + 2, "_480_480");
                } else if (StringUtils.isNotBlank(item.getImages())) {
                    int index =item.getImages().toUpperCase().indexOf(".");
                    sb.insert(index, "_R_480_480");
                }
                item.setImages(hotelApollo.getCtripHotelPicUrlPrefix() + item.getImages());
            }
        }
        response.setBaseInfo(details);

        HotelCombineVo combineVo=new HotelCombineVo();
        List<String> names=new ArrayList<>();
        names.add(matchMapping.getBaseHotelName());
        names.add(matchMapping.getMatchHotelName());
        combineVo.setResult(matchMapping.getHandlerStatus());
        combineVo.setSimilarity(String.valueOf(matchMapping.getTotalPercent()));
        combineVo.setNames(names);
        response.setHotelCombine(combineVo);

        MergeHistoryVo historyVo=new MergeHistoryVo();
        historyVo.setPep(matchMapping.getOperator());
        historyVo.setOperatorStatus(matchMapping.getHandlerStatus());
        historyVo.setTime(matchMapping.getOperatorTime());
        response.setHistory(historyVo);
        return response;
    }

    /**
     * 获取酒店合并的详细信息
     * @param resultId
     * @return
     */
    public MergeDetailResponse getMergeDetail(Integer resultId){
        MergeDetailResponse response=new MergeDetailResponse();
        //todo 可优化为同一个查询
        response.setHotelMergeHistoryList(hmHotelMatchMappingMapper.getMergerHistory(resultId));
        response.setSimilarHotelList(hmHotelMatchMappingMapper.getSimilarHotel(resultId));
        return response;
    }
}
