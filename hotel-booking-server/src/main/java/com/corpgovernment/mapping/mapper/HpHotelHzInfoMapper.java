package com.corpgovernment.mapping.mapper;

import com.corpgovernment.mapping.bo.HpHotelHzInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HpHotelHzInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(HpHotelHzInfo record);

    int insertSelective(HpHotelHzInfo record);

    HpHotelHzInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(HpHotelHzInfo record);

    int updateByPrimaryKey(HpHotelHzInfo record);

    /**
     * 批量插入
     */
    int batchInsert(@Param("lists") List<HpHotelHzInfo> data);

    /**
     * 获取全量酒店数据
     * @return
     */
    List<HpHotelHzInfo> getHotelData();

    /**
     * 查询所有酒店id
     * @return
     */
    List<String> selectHotelId();

    /**
     * 根据id查询所有数据
     * @param ids
     * @return
     */
    List<HpHotelHzInfo> selectHotelListByIds(@Param("lists") List<String> ids);
}