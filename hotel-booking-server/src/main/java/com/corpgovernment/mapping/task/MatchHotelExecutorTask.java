//package com.corpgovernment.mapping.task;
//
//
//import com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo;
//import com.corpgovernment.mapping.bo.*;
//import com.corpgovernment.mapping.mapper.HmHotelConfirmMappingMapper;
//import com.corpgovernment.mapping.mapper.HmHotelInfoMapper;
//import com.corpgovernment.mapping.mapper.HmHotelMatchMappingMapper;
//import com.corpgovernment.mapping.mapper.HpHotelHzInfoMapper;
//import com.corpgovernment.mapping.util.HotelMatchedUtil;
//import com.corpgovernment.mapping.util.HotelSimilarity;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.CollectionUtils;
//
//import java.util.*;
//
///**
// * @ClassName: MatchHotelExecutorTask
// * @description: TODO
// * @author: yssong
// * @date: Created in 17:45 2019/9/20
// * @Version: 1.0
// **/
//@Slf4j
//public class MatchHotelExecutorTask implements Runnable{
//
//    /**
//     * 供应商A的分值 权重
//     */
//    private Map<String,Object> mapA;
//
//    /**
//     * 供应商A的分值 权重
//     */
//    private Map<String,Object> mapB;
//
//    private HpHotelHzInfoMapper hpHotelHzInfoMapper;
//
//    private HmHotelInfoMapper hmHotelInfoMapper;
//
//    @Autowired
//    private HmHotelConfirmMappingMapper hmHotelConfirmMappingMapper;
//
//    @Autowired
//    private HmHotelMatchMappingMapper hmHotelMatchMappingMapper;
//
//
//    public MatchHotelExecutorTask(Map<String, Object> mapA, Map<String, Object> mapB,
//                                  HpHotelHzInfoMapper hpHotelHzInfoMapper,HmHotelInfoMapper hmHotelInfoMapper,
//                                  HmHotelConfirmMappingMapper hmHotelConfirmMappingMapper,HmHotelMatchMappingMapper hmHotelMatchMappingMapper) {
//        this.mapA = mapA;
//        this.mapB = mapB;
//        this.hpHotelHzInfoMapper = hpHotelHzInfoMapper;
//        this.hmHotelInfoMapper = hmHotelInfoMapper;
//        this.hmHotelConfirmMappingMapper = hmHotelConfirmMappingMapper;
//        this.hmHotelMatchMappingMapper = hmHotelMatchMappingMapper;
//    }
//
//    /**
//     * 1. 获取供应商 AB 全量数据  取总数小的循环
//     * 2. 根据分值权重进行匹配
//     * 3， 插入 供应商酒店匹配确认映射表， 供应商酒店不确认结果集映射表
//     */
//    @Override
//    public void run() {
//
//        try {
//            //base全量酒店id
//            List<String>  baseHotelIds = hpHotelHzInfoMapper.selectHotelId();
//            //记录base匹配到的酒店
//            Set<String> baseIds = new HashSet<>();
//
//            //match全量酒店id
//            List<Integer> matchHotelIds = hmHotelInfoMapper.selectHotelId();
//            //记录匹配到的酒店id
//            Set<Integer> matchIds = new HashSet<>();
//
//            HpSupplier supplierA = (HpSupplier) mapA.get("supplier");
//            HpSupplier supplierB = (HpSupplier) mapB.get("supplier");
//
//            //分值
//            HmScoreRule baseRule = (HmScoreRule)mapA.get("sorceRule");
//            //权重
//            HmWeightRule baseWeightRule = (HmWeightRule)mapA.get("weightRule");
//
//            //取酒店数量少的作为基准数据
//            String mapper = supplierA.getHotelNum()<supplierB.getHotelNum()? supplierA.getSupplierTable():supplierB.getSupplierTable();
//            List<HpHotelHzInfo> hotelData = new ArrayList<>();
//            if (mapper.equals("HpHotelHzInfoMapper")){
//                //大于多少条 分页查询 PageHelper
//                hotelData = hpHotelHzInfoMapper.getHotelData();
//            }
//
//            for ( HpHotelHzInfo base:hotelData){
//
//                if (!base.getLatitude().equals("")|| !base.getLongitude().equals("")){
//                    //获取经纬度取方圆500米以内酒店 经纬度范围
//                    double lat = Double.parseDouble(base.getLatitude());
//                    double lng = Double.parseDouble(base.getLongitude());
//
//                    double[] around = HotelMatchedUtil.getAround(lat,lng,500);
//
//                    //查询出经纬度之间的所有酒店进行匹配
//                    List<HmHotelInfo> list = hmHotelInfoMapper.selectHotelByLatLng(around[0],around[2],around[1],around[3]);
//
//                    //记录精准匹配到的酒店id
//                    Set<Integer> hotelExactIds = new HashSet<>(list.size());
//                    //记录相似匹配到的酒店id base
//                    Set<String> hoteBaselIds = new HashSet<>(list.size());
//                    //记录相似匹配到的酒店id match
//                    Set<Integer> hoteDimlIds = new HashSet<>(list.size());
//                    //记录相似匹配的酒店信息
//                    List<HmHotelMatchMapping> hotelDimList = new ArrayList<>();
//
//                    for (HmHotelInfo match:list){
//                        double matchSum =0.00;
//                        //名称匹配度
//                        double nameMatch = HotelSimilarity.similarity(base.getHotelname(),match.getHotelName());
//                        //地址匹配度
//                        double addressMatch = HotelSimilarity.similarity(base.getHoteladdress(), match.getHotelAddress());
//                        //电话匹配度
//                        double telephoneMatch =0.00;
//                        if (!base.getTelephone().equals("") && !match.getTelephone().equals("")){
//                            telephoneMatch = HotelSimilarity.similarity(base.getTelephone(), match.getTelephone());
//                            matchSum = nameMatch*baseWeightRule.getHotelNameWeight()+addressMatch*baseWeightRule.getHotelAddressWeight()+telephoneMatch*baseWeightRule.getTelephoneWeight();
//                        }else{
//                            //总匹配度 当电话为空时候 名称地址权重各50%
//                            matchSum = nameMatch*0.5+addressMatch*0.5;
//                        }
//                        log.info("酒店A ："+base.getHotelname()+"--------酒店B ："+match.getHotelName()+"--------匹配度："+matchSum);
//                        //插入表  0.0-0.4 不匹配  0.4-0.7 模糊匹配   0.7 匹配
//                        //精准匹配 插入 hm_hotel_confirm_mapping
//                        if (matchSum>=0.7){
//                            hotelExactIds.add(match.getHotel());
//                            matchIds.add(match.getHotel());
//                            baseIds.add(base.getHotelno());
//                            //插入确认表
//                            HmHotelConfirmMapping data = getHotelExactData(supplierA,supplierB,base,match);
//                            hmHotelConfirmMappingMapper.insert(data);
//                            break;
//                        }
//
//                        //0.4-0.7 模糊匹配  hm_hotel_match_mapping
//                        if(matchSum>0.4 && matchSum<0.7){
//                            //记录模糊匹配到的酒店
//                            hoteBaselIds.add(base.getHotelno());
//                            hoteDimlIds.add(match.getHotel());
//                            //记录模糊匹配到的酒店信息
//                            HmHotelMatchMapping data = getHotelDimData(supplierA,supplierB,base,match,nameMatch,addressMatch,telephoneMatch,matchSum);
//                            hotelDimList.add(data);
//                        }
//
//                    }
//
//                    //判断模糊匹配
//                    if (CollectionUtils.isEmpty(hotelExactIds) && CollectionUtils.isNotEmpty(hoteDimlIds) && CollectionUtils.isNotEmpty(hoteBaselIds)){
//                        //插入 hm_hotel_match_mapping
//                        hmHotelMatchMappingMapper.batchInsert(hotelDimList);
//                        for (String hoteBaselId:hoteBaselIds){
//                            baseIds.add(hoteBaselId);
//                        }
//                        //记录匹配过的id  matchIds
//                        for (Integer hoteDimlId:hoteDimlIds){
//                            matchIds.add(hoteDimlId);
//                        }
//                    }
//
//                }
//
//            }
//            //完全不匹配 (剩余A,剩余B) 取差集  分页查询
//            baseHotelIds.removeAll(baseIds);
//            matchHotelIds.removeAll(matchIds);
//
//            //查询没有匹配到的数据 插入数据 hm_hotel_confirm_mapping baseHotelIds matchHotelIds
//            //base 没有匹配过的数据
//            if(CollectionUtils.isNotEmpty(baseHotelIds)){
//                //根据id查询数据
//                List<HmHotelConfirmMapping> data = new ArrayList<>(baseHotelIds.size());
//                for (String id:baseHotelIds){
//                    HmHotelConfirmMapping confirmData = new HmHotelConfirmMapping();
//                    confirmData.setBaseSupplier(supplierA.getSupplierId());
//                    confirmData.setBaseSupplierName(supplierA.getSupplierName());
//                    confirmData.setBaseHotel(id);
//                    confirmData.setRelatedSupplier(-1);
//                    confirmData.setRelatedSupplierName("");
//                    confirmData.setRelatedHotel("");
//                    confirmData.setHandlerStatus(1);
//                    data.add(confirmData);
//                }
//                hmHotelConfirmMappingMapper.batchInsert(data);
//            }
//            //match 没有匹配过的数据
//            if (CollectionUtils.isNotEmpty(matchHotelIds)){
//
//                List<Integer> subList = null;
//                /** 总数量**/
//                int count = matchHotelIds.size();
//                /** 初始化执行数量  10000条**/
//                int limit = 10000,start = 0,end = 0;
//                int number = count/limit;
//                if (count%limit>0){
//                    number++;
//                }
//                for (int i=1;i<=number;i++){
//                    end = start+limit;
//                    if (end<=count){
//                        subList = matchHotelIds.subList(start,end);
//                    }else{
//                        subList = matchHotelIds.subList(start,count);
//                    }
//
//                    List<HmHotelConfirmMapping> data = new ArrayList<>(subList.size());
//                    for (Integer id:subList){
//                        HmHotelConfirmMapping confirmData = new HmHotelConfirmMapping();
//                        confirmData.setBaseSupplier(supplierB.getSupplierId());
//                        confirmData.setBaseSupplierName(supplierB.getSupplierName());
//                        confirmData.setBaseHotel(id+"");
//
//                        confirmData.setRelatedSupplier(-1);
//                        confirmData.setRelatedSupplierName("");
//                        confirmData.setRelatedHotel("");
//                        confirmData.setHandlerStatus(1);
//                        data.add(confirmData);
//                    }
//                    hmHotelConfirmMappingMapper.batchInsert(data);
//
//                }
//
//            }
//
//
//            log.info("匹配酒店供应商A:"+mapA.get("supplier")+"----------匹配酒店供应商B:"+mapB.get("supplier")+"匹配结束");
//        }catch (Exception e){
//            //记录log
//            log.error("匹配酒店供应商A:"+mapA.get("supplier")+"----------匹配酒店供应商B:"+mapB.get("supplier")+"匹配出错", e);
//        }
//
//    }
//
//
//    private HmHotelConfirmMapping getHotelExactData(HpSupplier supplierA,HpSupplier supplierB,HpHotelHzInfo base,HmHotelInfo match){
//        HmHotelConfirmMapping data = new HmHotelConfirmMapping();
//        data.setBaseSupplier(supplierA.getSupplierId());
//        data.setBaseSupplierName(supplierA.getSupplierName());
//        data.setRelatedSupplier(supplierB.getSupplierId());
//        data.setRelatedSupplierName(supplierB.getSupplierName());
//        data.setBaseHotel(base.getHotelno());
//        data.setRelatedHotel(match.getHotel()+"");
//        data.setHandlerStatus(1);
//        return data;
//    }
//
//    private HmHotelMatchMapping getHotelDimData(HpSupplier supplierA,HpSupplier supplierB,HpHotelHzInfo base,HmHotelInfo match,
//                                                double nameMatch,double addressMatch,double telephoneMatch,double matchSum){
//        HmHotelMatchMapping data = new HmHotelMatchMapping();
//        data.setBaseSupplier(supplierA.getSupplierId());
//        data.setBaseSupplierName(supplierA.getSupplierName());
//        data.setMatchSupplier(supplierB.getSupplierId());
//        data.setMatchSupplierName(supplierB.getSupplierName());
//
//        data.setBaseHotel(base.getHotelno());
//        data.setMatchHotel(match.getHotel()+"");
//        data.setBaseHotelName(base.getHotelname());
//        data.setMatchHotelName(match.getHotelName());
//        data.setBaseHotelAddress(base.getHoteladdress());
//        data.setMatchHotelAddress(match.getHotelAddress());
//        data.setBaseHotelTelephone(base.getTelephone());
//        data.setMatchHotelTelephone(match.getTelephone());
//
//        data.setHotelNamePercent(nameMatch);
//        data.setHotelAddressPercent(addressMatch);
//        data.setHotelTelephonePercent(telephoneMatch);
//        data.setTotalPercent(matchSum);
//        return data;
//    }
//
//
//}
