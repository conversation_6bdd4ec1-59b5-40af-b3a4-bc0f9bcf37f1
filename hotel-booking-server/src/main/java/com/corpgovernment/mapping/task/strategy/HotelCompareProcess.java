package com.corpgovernment.mapping.task.strategy;

import com.corpgovernment.common.apollo.HotelApollo;
import com.corpgovernment.common.utils.LogSplicingUtils;
import com.corpgovernment.hotel.product.config.MyApplicationContext;
import com.corpgovernment.mapping.bo.HmHotelAllInfo;
import com.corpgovernment.mapping.bo.MatchHotelBo;
import com.corpgovernment.mapping.enums.HotelCompareEnum;
import com.corpgovernment.mapping.task.Bo.*;
import com.ctrip.corp.obt.generic.utils.EnvironmentHolder;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HotelCompareProcess {

    @Autowired
    private HotelApollo hotelApollo;

    /**
     * 组装算法流 -> 通过Apollo配置执行顺序 -> 通过Apollo配置执行哪几个比较算法
     //     * @param needCompareList 当前需要比较的酒店
     //     * @param matchHotelList 从该集合中寻找具有相似度的酒店
     * @return
     */
    public HotelCompareAbstractRsp process(HotelCompareAbstractReq hotelCompareAbstractReq){
        HotelCompareAbstractRsp rsp = new HotelCompareAbstractRsp();
        StringBuilder logContext = new StringBuilder();
        try{
            //匹配成功酒店
            List<MatchHotelBo> totalCompareMakeList = new ArrayList<>();
            //相似度高的酒店
            List<MatchHotelBo> totalCompareSimilarList = new ArrayList<>();

            LogSplicingUtils.addLogContext(logContext, "---------------------------->>>>>>>>>>>>>>>>");
            LogSplicingUtils.addLogContext(logContext, "当前需要处理的数量：%s", hotelCompareAbstractReq.getNeedCompareList().size());
            //获取Apollo配置需要执行任务
            String hotelCompare = hotelApollo.getHotelCompare();
            //类型转换
            List<HotelCompareConfig> hotelCompareConfigList = JsonUtils.parse(hotelCompare, new TypeReference<List<HotelCompareConfig>>() {
            });
            LogSplicingUtils.addLogContext(logContext, "Apollo配置策略：%s", JsonUtils.toJsonString(hotelCompareConfigList));
            //排序执行
            hotelCompareConfigList = hotelCompareConfigList.stream().sorted(Comparator.comparing(HotelCompareConfig::getSort)).collect(Collectors.toList());

            //循环调用
            for (HotelCompareConfig config:hotelCompareConfigList){
                //获取Bean
                HotelCompareService instance = this.createInstance(HotelCompareEnum.getEnumByCode(config.getType()).getName());
                LogSplicingUtils.addLogContext(logContext, "instance：%s", instance);
                //调用算法规则
                HotelCompareRsp hotelCompareRsp = instance.compare(
                        HotelCompareReq.create(hotelCompareAbstractReq.getNeedCompareList(), hotelCompareAbstractReq.getMatchHotelList(), hotelCompareAbstractReq.getBaseSupplier(), config.getType())
                );
                //匹配成功的酒店
                totalCompareMakeList.addAll(hotelCompareRsp.getCompareMakeList());
                //相似度高的酒店
                totalCompareSimilarList.addAll(hotelCompareRsp.getCompareSimilarList());
                LogSplicingUtils.addLogContext(logContext, "规则：%s, 匹配成功数：%s", HotelCompareEnum.getEnumByCode(config.getType()).getName(), hotelCompareRsp.getCompareMakeList().size());
                LogSplicingUtils.addLogContext(logContext, "规则：%s, 相似度较高数：%s", HotelCompareEnum.getEnumByCode(config.getType()).getName(), hotelCompareRsp.getCompareSimilarList().size());
            }
            LogSplicingUtils.addLogContext(logContext, "成功总数：%s", totalCompareMakeList.size());
            rsp.setCompareMakeList(totalCompareMakeList);
            rsp.setCompareSimilarList(totalCompareSimilarList);
            rsp.setLogContext(logContext);
        } catch (Exception e){
            log.error("酒店处理合并策略发生异常：{}", e);
        }
        return rsp;
    }

    private HotelCompareService createInstance(String rule){
        return (HotelCompareService)EnvironmentHolder.getBean(rule);
    }

}
