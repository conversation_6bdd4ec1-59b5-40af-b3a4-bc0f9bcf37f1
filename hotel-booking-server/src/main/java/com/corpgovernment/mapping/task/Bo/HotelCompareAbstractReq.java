package com.corpgovernment.mapping.task.Bo;

import com.corpgovernment.mapping.bo.HmHotelAllInfo;
import com.corpgovernment.mapping.bo.MatchHotelBo;
import com.corpgovernment.mapping.vo.SupplierBaseInfoVo;
import lombok.Data;

import java.util.List;

@Data
public class HotelCompareAbstractReq {

    private Integer seqNo;

    private List<HmHotelAllInfo> needCompareList;

    private List<HmHotelAllInfo> matchHotelList;

    private SupplierBaseInfoVo baseSupplier;

    public static HotelCompareAbstractReq create(Integer seqNo, List<HmHotelAllInfo> needCompareList, List<HmHotelAllInfo> matchHotelList, SupplierBaseInfoVo baseSupplier){
        HotelCompareAbstractReq req = new HotelCompareAbstractReq();
        req.setSeqNo(seqNo);
        req.setNeedCompareList(needCompareList);
        req.setMatchHotelList(matchHotelList);
        req.setBaseSupplier(baseSupplier);
        return req;
    }
}
