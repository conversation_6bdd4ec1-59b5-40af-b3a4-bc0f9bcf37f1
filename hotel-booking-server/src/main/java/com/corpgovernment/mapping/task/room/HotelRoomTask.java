package com.corpgovernment.mapping.task.room;

import com.corpgovernment.common.utils.HttpUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.corpgovernment.hotel.booking.util.CtripEncryptUtil;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

import java.util.List;
import java.util.concurrent.RecursiveTask;

/**
 * @ClassName: HotelRoomTask
 * @description: HotelRoomTask
 * @author: zdwang
 * @date: Created in 21:17 2019/10/15
 * @Version: 1.0
 **/
@Slf4j
public class HotelRoomTask extends RecursiveTask<List<HotelStaticInfoResponse>> {
    private static final int packet = 1000;
    private final List<String> hotelIds;
    private final int start;
    private final int end;

    public HotelRoomTask(List<String> hotelIds, int start, int end) {
        this.hotelIds = hotelIds;
        this.start = start;
        this.end = end;
    }

    @SneakyThrows
    @Override
    protected List<HotelStaticInfoResponse> compute() {
        if ((end - start) < packet) {
            List<HotelStaticInfoResponse> result = Lists.newArrayList();
            for (int i = start; i <= end; i++) {
//                Thread.sleep(600);
                String hotelId = hotelIds.get(i);
                HotelStaticInfoRequest staticInfoRequest = new HotelStaticInfoRequest();
                staticInfoRequest.setHotelID(Lists.newArrayList(Integer.valueOf(hotelId)));
                staticInfoRequest.setReturnDataTypeList(Lists.newArrayList("BasicRoomTypeSimpleEntity","BasicRoomTypeBedTypeEntity"));
                String commentResult = HttpUtils.doPostJSON(CtripEncryptUtil.encryptCommonHotel("a6cd14a2129245c2911879b3f0aa150e"), JsonUtils.toJsonString(staticInfoRequest));
                HotelStaticInfoResponse body = JsonUtils.parse(commentResult, HotelStaticInfoResponse.class);
                log.info("携程酒店id:{},该酒店对应的基础房型数据:{}",hotelId, JsonUtils.toJsonString(body));
//                if (body == null) {
//                    throw new CommonException("获取供应商携程酒店静态数据失败");
//                }
//                if (!body.getResponseStatus().getAck().equals("Success")) {
//                    throw new CommonException("获取供应商携程酒店静态数据失败" );
//                }
                result.add(body);
            }
            return result;
        } else {
            int mid = (end + start) >> 1;
            HotelRoomTask leftTask = new HotelRoomTask(hotelIds, start, mid);
            HotelRoomTask rightTask = new HotelRoomTask(hotelIds, mid + 1, end);
            invokeAll(leftTask,rightTask);
            List<HotelStaticInfoResponse> leftResult = leftTask.join();
            List<HotelStaticInfoResponse> rightResult = rightTask.join();
            leftResult = leftResult==null?Lists.newArrayList():leftResult;
	        if (CollectionUtils.isNotEmpty(rightResult)) {
		        leftResult.addAll(rightResult);
	        }
            return leftResult;
        }
    }
}