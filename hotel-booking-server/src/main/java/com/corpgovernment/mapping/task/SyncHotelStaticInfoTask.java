package com.corpgovernment.mapping.task;

import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.mapping.basic.bo.request.HotelInfoRequest;
import com.corpgovernment.mapping.basic.bo.response.HotelInfoDataResponse;
import com.corpgovernment.mapping.basic.bo.response.HotelInfoResponse;
import com.corpgovernment.mapping.basic.bo.subbo.ContactInfo;
import com.corpgovernment.mapping.basic.bo.subbo.CoordinateInfo;
import com.corpgovernment.mapping.basic.bo.subbo.HmHotelInfo;
import com.corpgovernment.mapping.bo.HmHotelAllInfo;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.RecursiveTask;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-03-17-10:45
 */
public class SyncHotelStaticInfoTask extends RecursiveTask<Boolean> {

	private static final int THRESHOLD = 100;
	private List<Integer> hotelIds;
	private int start;
	private int end;
	private HotelInfoRequest hotelInfoRequest;
	private SupplierProductBo supplierProduct;
	private BiFunction<HotelInfoRequest, SupplierProductBo, HotelInfoResponse> hotelStaticFunction;
	private Function<List<HmHotelAllInfo>, Integer> batchSaveFunction;

	public SyncHotelStaticInfoTask(List<Integer> hotelIds, int start, int end,
								   HotelInfoRequest hotelInfoRequest, SupplierProductBo supplierProduct,
								   BiFunction<HotelInfoRequest, SupplierProductBo, HotelInfoResponse> hotelStaticFunction,
								   Function<List<HmHotelAllInfo>, Integer> batchSaveFunction) {
		this.hotelIds = hotelIds;
		this.start = start;
		this.end = end;
		this.hotelInfoRequest = hotelInfoRequest;
		this.supplierProduct = supplierProduct;
		this.hotelStaticFunction = hotelStaticFunction;
		this.batchSaveFunction = batchSaveFunction;
	}

	@Override
	protected Boolean compute() {
		if (end - start <= THRESHOLD) {
			// 如果酒店数量足够小,直接同步:
			List<Integer> subHotelIds = hotelIds.subList(start, end);
			List<String> newHotelIds = subHotelIds.stream().map(String::valueOf).collect(Collectors.toList());
			hotelInfoRequest.setHotelID(newHotelIds);
			this.syncHotelStaticInfo(hotelInfoRequest, supplierProduct);
			return true;
		}
		int middle = (end + start) / 2;
		SyncHotelStaticInfoTask subtask1 = new SyncHotelStaticInfoTask(hotelIds, start, middle, hotelInfoRequest, supplierProduct, hotelStaticFunction, batchSaveFunction);
		SyncHotelStaticInfoTask subtask2 = new SyncHotelStaticInfoTask(hotelIds, middle, end, hotelInfoRequest, supplierProduct, hotelStaticFunction, batchSaveFunction);
		invokeAll(subtask1, subtask2);
		subtask1.join();
		subtask2.join();
		return true;
	}

	private void syncHotelStaticInfo(HotelInfoRequest hotelInfoRequest, SupplierProductBo supplierProduct) {
		HotelInfoResponse hotelStaticInfo = hotelStaticFunction.apply(hotelInfoRequest, supplierProduct);
		if (hotelStaticInfo == null) {
			return;
		}
		if (CollectionUtils.isEmpty(hotelStaticInfo.getHotelDataList())) {
			return;
		}
		List<HotelInfoDataResponse> hotelDataList = hotelStaticInfo.getHotelDataList();
		List<HmHotelInfo> data = new ArrayList<>(THRESHOLD);
		for (HotelInfoDataResponse dataResponse : hotelDataList) {
			HmHotelInfo hmHotelInfo = dataResponse.getHotelStaticBaseInfoEntity().getHotelSimpleEntity();
			//添加经纬度信息
			CoordinateInfo coordinateInfo = dataResponse.getHotelStaticBaseInfoEntity().getCoordinateEntity();
			hmHotelInfo.setGlat(coordinateInfo.getGlat());
			hmHotelInfo.setGlng(coordinateInfo.getGlng());
			//地址
			String hotelAddress = dataResponse.getHotelStaticBaseInfoEntity().getHotelAddress();
			hmHotelInfo.setHotelAddress(hotelAddress);
			//电话 邮编
			ContactInfo contactInfo = dataResponse.getHotelStaticBaseInfoEntity().getContactInformationEntity();
			hmHotelInfo.setTelephone(contactInfo.getFax());
			hmHotelInfo.setZipCode(contactInfo.getZipCode());
			data.add(hmHotelInfo);
		}
		List<HmHotelAllInfo> hotelList = new ArrayList<>(data.size());
		for (HmHotelInfo hmHotelInfo : data) {
			HmHotelAllInfo hotelInfo = new HmHotelAllInfo();
			hotelInfo.setSupplierCode(supplierProduct.getSupplierCode());
			hotelInfo.setHotelNo(hmHotelInfo.getHotel() + "");
			hotelInfo.setHotelName(hmHotelInfo.getHotelName());
			hotelInfo.setHotelAddress(hmHotelInfo.getHotelAddress());
			hotelInfo.setTelephone(hmHotelInfo.getTelephone());
			hotelInfo.setBrandCode("");
			hotelInfo.setBrandName("");
			hotelInfo.setCityId(String.valueOf(hmHotelInfo.getCity()));
			hotelInfo.setLatitude(hmHotelInfo.getGlat() + "");
			hotelInfo.setLongitude(hmHotelInfo.getGlng() + "");
			hotelList.add(hotelInfo);
		}
		batchSaveFunction.apply(hotelList);
	}
}
