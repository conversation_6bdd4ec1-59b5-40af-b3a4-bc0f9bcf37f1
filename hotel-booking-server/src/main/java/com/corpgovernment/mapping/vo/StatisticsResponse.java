package com.corpgovernment.mapping.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: jt.qin
 * @DateTime: 2020/5/7 15:48
 * @Description
 */
@Data
public class StatisticsResponse {
    private String cityName;
    private String cityId;
    private String baseSupplierName;
    private Integer baseSupplierNum;
    private String matchSupplierName;
    private Integer matchSupplierNum;
    private Integer relatedHotelNum;
    private Integer relatedPercent;
    private WaitConfirm WaitConfirmInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WaitConfirm {
        private Integer totalNum;
        private Integer nameSimilarityNum;
        private Integer addressSimilarityNum;
        private Integer phoneSimilarityNum;
    }
}
