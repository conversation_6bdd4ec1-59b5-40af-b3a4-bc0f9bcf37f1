<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hotel-booking</artifactId>
        <groupId>com.ctrip.corp.obt</groupId>
        <version>1.0.0-SNAPSHOT</version>

    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hotel-booking-server</artifactId>
    <properties>
        <hanlp.version>portable-1.6.5</hanlp.version>
        <jdom2.version>2.0.6</jdom2.version>
        <arch-metric-core.version>1.0.3-SNAPSHOT</arch-metric-core.version>
        <skipTests>true</skipTests>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>service-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>service-mysql</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>service-default</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>arch-discovery</artifactId>
                    <groupId>com.ctrip.corp.obt</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>service-auth</artifactId>
        </dependency>
        <!--redisson start-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>${hanlp.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom2</artifactId>
            <version>${jdom2.version}</version>
        </dependency>
        <dependency>
          <groupId>com.ctrip.corp.obt</groupId>
          <artifactId>arch-metric-core</artifactId>
          <version>${arch-metric-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-discovery</artifactId>
            <version>${discovery.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>falconcache</artifactId>
            <version>1.0.2</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>